// SPDX-License-Identifier: MIT
pragma solidity ^0.8.17;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Create2.sol";

// Interface for PancakeSwap Router
interface IPancakeRouter {
    function addLiquidityETH(
        address token,
        uint amountTokenDesired,
        uint amountTokenMin,
        uint amountETHMin,
        address to,
        uint deadline
    ) external payable returns (uint amountToken, uint amountETH, uint liquidity);
}

// Interface for PancakeSwap Factory
interface IPancakeFactory {
    function getPair(address tokenA, address tokenB) external view returns (address pair);
}

// Simple BEP20 Token
contract SimpleBEP20 is ERC20, Ownable {
    constructor(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 initialSupply,
        address owner
    ) ERC20(name, symbol) {
        _mint(owner, initialSupply);
        transferOwnership(owner);
    }
}

// Liquidity Locker
contract LiquidityLocker {
    address public owner;
    address public token;
    address public pair;
    uint256 public unlockTime;

    constructor(address _owner, address _token, address _pair, uint256 _unlockTime) {
        owner = _owner;
        token = _token;
        pair = _pair;
        unlockTime = _unlockTime;
    }

    function unlock() external {
        require(msg.sender == owner, "Not owner");
        require(block.timestamp >= unlockTime, "Still locked");
        
        IERC20(pair).transfer(owner, IERC20(pair).balanceOf(address(this)));
    }
}

// Token Factory with Suffix Grinding
contract BnbTokenFactoryWithSuffix is Ownable {
    uint256 public fee = 0.01 ether;
    uint256 public grindingFeePerAttempt = 0.000005 ether;
    address public pancakeRouterAddress;
    address public pancakeFactoryAddress;
    address public wethAddress;

    event TokenCreated(address tokenAddress, address creator, uint256 attemptsUsed);
    event LiquidityAdded(address tokenAddress, address pairAddress, uint256 bnbAmount, uint256 tokenAmount);
    event LiquidityLocked(address tokenAddress, address pairAddress, address lockAddress, uint256 unlockTime);

    constructor(address _pancakeRouterAddress, address _pancakeFactoryAddress, address _wethAddress) {
        pancakeRouterAddress = _pancakeRouterAddress;
        pancakeFactoryAddress = _pancakeFactoryAddress;
        wethAddress = _wethAddress;
    }

    // Function to create a token with a specific address suffix
    function createTokenWithSuffix(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 initialSupply,
        address owner,
        string memory targetSuffix,
        uint256 maxAttempts
    ) external payable returns (address) {
        // Calculate required fee
        uint256 requiredFee = fee + (maxAttempts * grindingFeePerAttempt);
        require(msg.value >= requiredFee, "Insufficient fee");

        // Convert suffix to bytes for comparison
        bytes memory suffixBytes = bytes(targetSuffix);
        require(suffixBytes.length > 0 && suffixBytes.length <= 20, "Invalid suffix length");

        // Try to find a salt that generates an address with the target suffix
        bytes32 salt;
        address tokenAddress;
        uint256 attemptsUsed = 0;

        for (uint256 i = 0; i < maxAttempts; i++) {
            // Generate a random salt based on various inputs
            salt = keccak256(abi.encodePacked(
                block.timestamp,
                block.prevrandao,
                msg.sender,
                i,
                name,
                symbol
            ));

            // Predict the address that would be created with this salt
            tokenAddress = Create2.computeAddress(
                salt,
                keccak256(abi.encodePacked(
                    type(SimpleBEP20).creationCode,
                    abi.encode(name, symbol, decimals, initialSupply, owner)
                ))
            );

            // Check if the address ends with the target suffix
            if (addressEndsWith(tokenAddress, suffixBytes)) {
                attemptsUsed = i + 1;
                break;
            }
        }

        // If we couldn't find a matching address within maxAttempts, revert
        require(attemptsUsed > 0, "Could not find matching address within attempts limit");

        // Deploy the token with the found salt
        tokenAddress = Create2.deploy(
            0,
            salt,
            abi.encodePacked(
                type(SimpleBEP20).creationCode,
                abi.encode(name, symbol, decimals, initialSupply, owner)
            )
        );

        // Emit event
        emit TokenCreated(tokenAddress, owner, attemptsUsed);

        // Refund excess fee if any
        uint256 usedFee = fee + (attemptsUsed * grindingFeePerAttempt);
        if (msg.value > usedFee) {
            payable(msg.sender).transfer(msg.value - usedFee);
        }

        return tokenAddress;
    }

    // Helper function to check if an address ends with a specific suffix
    function addressEndsWith(address addr, bytes memory suffix) internal pure returns (bool) {
        bytes20 addrBytes = bytes20(addr);
        uint256 addrLength = 40; // Hex representation length of address (without 0x)
        uint256 suffixLength = suffix.length;

        // If suffix is longer than address, it can't match
        if (suffixLength > addrLength) {
            return false;
        }

        // Check if the end of the address matches the suffix
        for (uint256 i = 0; i < suffixLength; i++) {
            // Get the corresponding byte from the address
            uint8 addrByte = uint8(addrBytes[19 - (i / 2)]);
            
            // Extract the correct nibble (4 bits)
            uint8 addrNibble;
            if (i % 2 == 0) {
                addrNibble = addrByte & 0x0F; // Lower nibble
            } else {
                addrNibble = addrByte >> 4; // Upper nibble
            }
            
            // Convert nibble to hex character
            bytes1 addrChar = nibbleToHexChar(addrNibble);
            
            // Compare with the suffix character
            if (addrChar != suffix[suffixLength - 1 - i]) {
                return false;
            }
        }
        
        return true;
    }

    // Helper function to convert a nibble to its hex character representation
    function nibbleToHexChar(uint8 nibble) internal pure returns (bytes1) {
        if (nibble < 10) {
            return bytes1(uint8(bytes1('0')) + nibble);
        } else {
            return bytes1(uint8(bytes1('A')) + nibble - 10);
        }
    }

    // Function to add liquidity to PancakeSwap and optionally lock it
    function addLiquidityAndLock(
        address tokenAddress,
        address routerAddress,
        uint256 bnbAmount,
        uint256 tokenAmount,
        uint256 lockTime
    ) external payable returns (address) {
        require(msg.value >= bnbAmount, "Insufficient BNB sent");
        
        // Approve router to spend tokens
        IERC20(tokenAddress).transferFrom(msg.sender, address(this), tokenAmount);
        IERC20(tokenAddress).approve(routerAddress, tokenAmount);
        
        // Add liquidity
        IPancakeRouter router = IPancakeRouter(routerAddress);
        (,, uint256 liquidity) = router.addLiquidityETH{value: bnbAmount}(
            tokenAddress,
            tokenAmount,
            0, // Accept any amount of tokens
            0, // Accept any amount of ETH
            address(this),
            block.timestamp + 300 // 5 minutes deadline
        );
        
        // Get pair address
        address pairAddress = IPancakeFactory(pancakeFactoryAddress).getPair(tokenAddress, wethAddress);
        
        emit LiquidityAdded(tokenAddress, pairAddress, bnbAmount, tokenAmount);
        
        // Lock liquidity if requested
        if (lockTime > 0) {
            // Create liquidity locker contract
            LiquidityLocker locker = new LiquidityLocker(
                msg.sender,
                tokenAddress,
                pairAddress,
                block.timestamp + lockTime
            );
            
            // Transfer LP tokens to locker
            IERC20(pairAddress).transfer(address(locker), liquidity);
            
            emit LiquidityLocked(tokenAddress, pairAddress, address(locker), block.timestamp + lockTime);
            
            return address(locker);
        } else {
            // Transfer LP tokens to sender
            IERC20(pairAddress).transfer(msg.sender, liquidity);
            return pairAddress;
        }
    }

    // Function to update the fee
    function setFee(uint256 _fee) external onlyOwner {
        fee = _fee;
    }
    
    // Function to update the grinding fee per attempt
    function setGrindingFeePerAttempt(uint256 _grindingFeePerAttempt) external onlyOwner {
        grindingFeePerAttempt = _grindingFeePerAttempt;
    }
    
    // Function to update PancakeSwap router address
    function setPancakeRouterAddress(address _pancakeRouterAddress) external onlyOwner {
        pancakeRouterAddress = _pancakeRouterAddress;
    }
    
    // Function to update PancakeSwap factory address
    function setPancakeFactoryAddress(address _pancakeFactoryAddress) external onlyOwner {
        pancakeFactoryAddress = _pancakeFactoryAddress;
    }
    
    // Function to update WETH address
    function setWethAddress(address _wethAddress) external onlyOwner {
        wethAddress = _wethAddress;
    }
    
    // Function to withdraw fees
    function withdrawFees() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
}

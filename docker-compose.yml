version: "3.8"
services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SOLANA_RPC_URL=https://api.devnet.solana.com
      - NEXT_PUBLIC_TOKEN_PROGRAM_ID=TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA
    depends_on:
      - redis
      - postgres

  redis:
    image: "redis:alpine"
    ports:
      - "6379:6379"

  postgres:
    image: "postgres:13-alpine"
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: your_user
      POSTGRES_PASSWORD: your_password
      POSTGRES_DB: your_db
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:

#[program]
pub mod off_ice {
    use super::*;
    use anchor_lang::prelude::*;
    use anchor_spl::token::{self, Mint, InitializeMint, SetAuthority, AuthorityType};
    use spl_token::instruction::AuthorityType::MintTokens;

    pub fn initialize(ctx: Context<Initialize>, decimals: u8, mint_authority: Pubkey) -> Result<()> {
        // Création automatique du token
        let cpi_accounts = InitializeMint {
            mint: ctx.accounts.mint.to_account_info(),
            authority: ctx.accounts.payer.to_account_info(),
        };
        let cpi_context = CpiContext::new(ctx.accounts.token_program.to_account_info(), cpi_accounts);
        token::initialize_mint(cpi_context, decimals, &mint_authority, Some(&mint_authority))?;

        // Gel immédiat
        let cpi_accounts = SetAuthority {
            current_authority: ctx.accounts.payer.to_account_info(),
            account_or_mint: ctx.accounts.mint.to_account_info(),
        };
        let cpi_context = CpiContext::new(ctx.accounts.token_program.to_account_info(), cpi_accounts);
        token::set_authority(cpi_context, AuthorityType::MintTokens, None)?;

        Ok(())
    }
}

#[derive(Accounts)]
pub struct Initialize<'info> {
    #[account(init, payer = payer, space = Mint::LEN)]
    pub mint: Account<'info, Mint>,
    #[account(mut)]
    pub payer: Signer<'info>,
    pub token_program: Program<'info, Token>,
    pub system_program: Program<'info, System>,
}

#[derive(Clone)]
pub struct Token;

impl Id for Token {
    fn id() -> Pubkey {
        spl_token::id()
    }
}

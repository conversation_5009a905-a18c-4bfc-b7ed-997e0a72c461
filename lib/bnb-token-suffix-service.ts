import Web3 from "web3"
import type { AbiItem } from "web3-utils"
import { getBnbTokenFactoryAddress, getNetworkById } from "./network-config"

// BEP20 Token Factory ABI (simplified for this example)
const tokenFactoryABI: AbiItem[] = [
  {
    inputs: [
      { internalType: "string", name: "name", type: "string" },
      { internalType: "string", name: "symbol", type: "string" },
      { internalType: "uint8", name: "decimals", type: "uint8" },
      { internalType: "uint256", name: "initialSupply", type: "uint256" },
      { internalType: "address", name: "owner", type: "address" },
      { internalType: "string", name: "targetSuffix", type: "string" },
      { internalType: "uint256", name: "maxAttempts", type: "uint256" },
    ],
    name: "createTokenWithSuffix",
    outputs: [{ internalType: "address", name: "", type: "address" }],
    stateMutability: "payable",
    type: "function",
  },
  {
    inputs: [],
    name: "fee",
    outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "grindingFeePerAttempt",
    outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
    stateMutability: "view",
    type: "function",
  },
]

// Interface for token creation parameters
export interface BnbTokenWithSuffixParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: string
  targetSuffix: string
  maxAttempts: number
  networkId: string
}

// Interface for token creation result
export interface BnbTokenCreationResult {
  tokenAddress: string
  attemptsUsed: number
  transactionHash: string
}

class BnbTokenSuffixService {
  private getWeb3(networkId: string) {
    const network = getNetworkById(networkId)
    if (!network || network.type !== "bnb") {
      throw new Error(`Invalid BNB network ID: ${networkId}`)
    }
    return new Web3(network.rpcUrl)
  }

  private getTokenFactoryContract(web3: Web3) {
    const factoryAddress = getBnbTokenFactoryAddress()
    return new web3.eth.Contract(tokenFactoryABI, factoryAddress)
  }

  /**
   * Calculate the fee required for token creation with suffix grinding
   */
  async calculateFee(maxAttempts: number, networkId: string): Promise<string> {
    try {
      const web3 = this.getWeb3(networkId)
      const factory = this.getTokenFactoryContract(web3)

      // Get base fee and grinding fee per attempt
      const [baseFee, grindingFeePerAttempt] = await Promise.all([
        factory.methods.fee().call(),
        factory.methods.grindingFeePerAttempt().call(),
      ])

      // Calculate total fee
      const totalFee = web3.utils
        .toBN(baseFee)
        .add(web3.utils.toBN(grindingFeePerAttempt).mul(web3.utils.toBN(maxAttempts)))

      return web3.utils.fromWei(totalFee, "ether")
    } catch (error) {
      console.error("Error calculating fee:", error)
      throw error
    }
  }

  /**
   * Estimate the time required to find an address with the given suffix
   */
  estimateGrindingTime(suffix: string, maxAttempts: number): string {
    // Base16 has 16 possible characters (0-9, A-F)
    const base16Size = 16
    const suffixLength = suffix.length

    // Calculate average attempts needed (16^length)
    const averageAttempts = Math.pow(base16Size, suffixLength)

    // Limit by max attempts
    const expectedAttempts = Math.min(averageAttempts, maxAttempts)

    // Estimate time based on attempts (very rough estimate)
    // Assuming the contract can check about 50 addresses per second
    const attemptsPerSecond = 50
    const seconds = expectedAttempts / attemptsPerSecond

    // Format time
    if (seconds < 60) {
      return `${Math.ceil(seconds)} seconds`
    } else if (seconds < 3600) {
      return `${Math.ceil(seconds / 60)} minutes`
    } else if (seconds < 86400) {
      return `${Math.ceil(seconds / 3600)} hours`
    } else {
      return `${Math.ceil(seconds / 86400)} days`
    }
  }

  /**
   * Validate a suffix for token address
   */
  validateSuffix(suffix: string): { valid: boolean; error?: string } {
    if (!suffix || suffix.length === 0) {
      return { valid: false, error: "Suffix cannot be empty" }
    }

    // Check if suffix contains only valid hex characters (0-9, A-F)
    const hexRegex = /^[0-9A-F]+$/
    if (!hexRegex.test(suffix)) {
      return { valid: false, error: "Suffix must contain only hex characters (0-9, A-F)" }
    }

    if (suffix.length > 20) {
      return { valid: false, error: "Suffix cannot be longer than 20 characters" }
    }

    return { valid: true }
  }

  /**
   * Calculate the probability of finding an address with the given suffix within max attempts
   */
  calculateSuccessProbability(suffix: string, maxAttempts: number): number {
    const base16Size = 16
    const suffixLength = suffix.length
    const averageAttempts = Math.pow(base16Size, suffixLength)

    // Probability = 1 - (1 - 1/averageAttempts)^maxAttempts
    // For small probabilities, this is approximately maxAttempts/averageAttempts
    const probability = Math.min(1, maxAttempts / averageAttempts)

    return probability
  }

  /**
   * Recommend max attempts based on desired success probability and suffix length
   */
  recommendMaxAttempts(suffix: string, desiredProbability = 0.9): number {
    const base16Size = 16
    const suffixLength = suffix.length
    const averageAttempts = Math.pow(base16Size, suffixLength)

    // For desired probability p, we need approximately -averageAttempts * ln(1-p) attempts
    // For p close to 1, this is approximately averageAttempts * p
    const recommendedAttempts = Math.ceil(averageAttempts * desiredProbability)

    // Cap at a reasonable maximum
    return Math.min(recommendedAttempts, 1000000)
  }
}

export default new BnbTokenSuffixService()

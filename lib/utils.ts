import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value: number, compact = false): string {
  const formatter = new Intl.NumberFormat("fr-FR", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: value < 0.01 ? 6 : value < 1 ? 4 : 2,
    maximumFractionDigits: value < 0.01 ? 6 : value < 1 ? 4 : 2,
    notation: compact ? "compact" : "standard",
  })
  return formatter.format(value)
}

export function formatPrice(price: number) {
  if (price === undefined || price === null) {
    return "0.00"
  }

  if (price < 0.00001) {
    return price.toExponential(2)
  }
  if (price < 0.001) {
    return price.toFixed(6)
  }
  if (price < 1) {
    return price.toFixed(4)
  }
  if (price < 10) {
    return price.toFixed(2)
  }
  return price.toLocaleString(undefined, { maximumFractionDigits: 2 })
}

export function formatNumber(value: number, compact = false): string {
  const formatter = new Intl.NumberFormat("fr-FR", {
    notation: compact ? "compact" : "standard",
  })
  return formatter.format(value)
}

export function formatPercentage(value: number): string {
  const formatter = new Intl.NumberFormat("fr-FR", {
    style: "percent",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
  return formatter.format(value / 100)
}

export function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  let interval = Math.floor(seconds / 31536000)
  if (interval >= 1) {
    return interval === 1 ? "il y a 1 an" : `il y a ${interval} ans`
  }

  interval = Math.floor(seconds / 2592000)
  if (interval >= 1) {
    return interval === 1 ? "il y a 1 mois" : `il y a ${interval} mois`
  }

  interval = Math.floor(seconds / 86400)
  if (interval >= 1) {
    return interval === 1 ? "il y a 1 jour" : `il y a ${interval} jours`
  }

  interval = Math.floor(seconds / 3600)
  if (interval >= 1) {
    return interval === 1 ? "il y a 1 heure" : `il y a ${interval} heures`
  }

  interval = Math.floor(seconds / 60)
  if (interval >= 1) {
    return interval === 1 ? "il y a 1 minute" : `il y a ${interval} minutes`
  }

  return seconds < 10 ? "à l'instant" : `il y a ${Math.floor(seconds)} secondes`
}

export function formatDate(date: Date, format: "short" | "medium" | "full" = "medium"): string {
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  }

  if (format === "full") {
    options.hour = "2-digit"
    options.minute = "2-digit"
  }

  return new Intl.DateTimeFormat("fr-FR", options).format(date)
}

export function truncateAddress(address: string, length = 4): string {
  if (!address) return ""
  return `${address.substring(0, length)}...${address.substring(address.length - length)}`
}

export function shortenAddress(address: string, length = 4): string {
  // Cette fonction est un alias de truncateAddress pour maintenir la compatibilité
  return truncateAddress(address, length)
}

export function getRandomGradient(): string {
  const gradients = [
    "bg-gradient-to-r from-pink-500 to-purple-500",
    "bg-gradient-to-r from-yellow-400 to-orange-500",
    "bg-gradient-to-r from-green-400 to-cyan-500",
    "bg-gradient-to-r from-blue-400 to-indigo-500",
    "bg-gradient-to-r from-purple-400 to-pink-500",
  ]
  return gradients[Math.floor(Math.random() * gradients.length)]
}

export function getRandomColor(): string {
  const colors = [
    "bg-red-500",
    "bg-orange-500",
    "bg-amber-500",
    "bg-yellow-500",
    "bg-lime-500",
    "bg-green-500",
    "bg-emerald-500",
    "bg-teal-500",
    "bg-cyan-500",
    "bg-sky-500",
    "bg-blue-500",
    "bg-indigo-500",
    "bg-violet-500",
    "bg-purple-500",
    "bg-fuchsia-500",
    "bg-pink-500",
    "bg-rose-500",
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

export function getInitials(name: string): string {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .substring(0, 2)
}

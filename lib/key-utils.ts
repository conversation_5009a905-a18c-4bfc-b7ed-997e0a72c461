import { Keypair } from "@solana/web3.js"

/**
 * Safely parse a private key from various formats
 * @param keyData The private key data (base64, array, etc.)
 * @returns A Keypair or null if parsing fails
 */
export function safeParsePrivateKey(keyData: string | number[] | undefined): Keypair | null {
  if (!keyData) return null

  try {
    // If it's already an array of numbers
    if (Array.isArray(keyData)) {
      return Keypair.fromSecretKey(Uint8Array.from(keyData))
    }

    // Try to parse as base64
    try {
      const decoded = Buffer.from(keyData, "base64")
      if (decoded.length === 64) {
        return Keypair.fromSecretKey(decoded)
      }
    } catch (e) {
      console.log("Not a valid base64 key")
    }

    // Try to parse as JSON array
    try {
      const parsed = JSON.parse(keyData)
      if (Array.isArray(parsed) && parsed.length === 64) {
        return Keypair.fromSecretKey(Uint8Array.from(parsed))
      }
    } catch (e) {
      console.log("Not a valid JSON array key")
    }

    // Try to parse as comma-separated values
    if (keyData.includes(",")) {
      const values = keyData.split(",").map((x) => Number.parseInt(x.trim()))
      if (values.length === 64 && values.every((x) => !isNaN(x))) {
        return Keypair.fromSecretKey(Uint8Array.from(values))
      }
    }

    console.warn("Could not parse private key from the provided format")
    return null
  } catch (error) {
    console.error("Error parsing private key:", error)
    return null
  }
}

/**
 * Get the admin keypair from environment variables
 * @returns The admin keypair or a new keypair if not found
 */
export function getAdminKeypair(): Keypair {
  // Try to get the admin keypair from environment variables
  const privateKey = process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY

  if (privateKey) {
    const keypair = safeParsePrivateKey(privateKey)
    if (keypair) {
      return keypair
    }
  }

  // Fallback to a new keypair
  console.warn("No valid admin keypair found, generating a new one")
  return Keypair.generate()
}

import { Connection, Keypair, PublicKey, Transaction } from "@solana/web3.js"
import {
  getOrCreateAssociatedTokenAccount,
  createTransferInstruction,
  getAccount,
  TOKEN_PROGRAM_ID,
} from "@solana/spl-token"

export interface TransactionResult {
  success: boolean
  transactionId?: string
  error?: string
}

export class TokenTransactionManager {
  private connection: Connection

  constructor(rpcUrl?: string) {
    this.connection = new Connection(
      rpcUrl || process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com",
      "confirmed",
    )
  }

  /**
   * Transfer tokens from one wallet to another
   */
  async transferTokens(
    tokenAddress: string,
    fromWallet: Keypair,
    toWalletAddress: string,
    amount: number,
    decimals = 9,
  ): Promise<TransactionResult> {
    try {
      const mintPublicKey = new PublicKey(tokenAddress)
      const toPublicKey = new PublicKey(toWalletAddress)

      // Calculate token amount with decimals
      const tokenAmount = amount * Math.pow(10, decimals)

      // Get the token accounts
      const fromTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        fromWallet,
        mintPublicKey,
        fromWallet.publicKey,
      )

      const toTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        fromWallet,
        mintPublicKey,
        toPublicKey,
      )

      // Create transfer instruction
      const transferInstruction = createTransferInstruction(
        fromTokenAccount.address,
        toTokenAccount.address,
        fromWallet.publicKey,
        tokenAmount,
        [],
        TOKEN_PROGRAM_ID,
      )

      // Create and sign transaction
      const transaction = new Transaction().add(transferInstruction)
      transaction.feePayer = fromWallet.publicKey
      transaction.recentBlockhash = (await this.connection.getLatestBlockhash()).blockhash

      const signedTransaction = await fromWallet.signTransaction(transaction)

      // Send transaction
      const transactionSignature = await this.connection.sendRawTransaction(signedTransaction.serialize())

      // Confirm transaction
      const confirmation = await this.connection.confirmTransaction(transactionSignature, "confirmed")

      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${confirmation.value.err.toString()}`)
      }

      return {
        success: true,
        transactionId: transactionSignature,
      }
    } catch (error) {
      console.error("Error transferring tokens:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      }
    }
  }

  /**
   * Get token balance for a wallet
   */
  async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<number> {
    try {
      const mintPublicKey = new PublicKey(tokenAddress)
      const walletPublicKey = new PublicKey(walletAddress)

      // Find the associated token account
      const tokenAccountAddress = await getOrCreateAssociatedTokenAccount(
        this.connection,
        Keypair.generate(), // Just for the function call, not actually used
        mintPublicKey,
        walletPublicKey,
      )

      // Get account info
      const tokenAccount = await getAccount(this.connection, tokenAccountAddress.address)

      return Number(tokenAccount.amount)
    } catch (error) {
      console.error("Error getting token balance:", error)
      return 0
    }
  }

  /**
   * Get recent token transactions for a wallet
   */
  async getTokenTransactions(tokenAddress: string, walletAddress: string, limit = 10): Promise<any[]> {
    try {
      const mintPublicKey = new PublicKey(tokenAddress)
      const walletPublicKey = new PublicKey(walletAddress)

      // In a real implementation, this would query the blockchain for transactions

      // For now, we'll just return mock data
      return Array(limit)
        .fill(0)
        .map((_, i) => ({
          signature: `mock_tx_${i}_${Date.now()}`,
          blockTime: Date.now() / 1000 - i * 3600,
          type: Math.random() > 0.5 ? "send" : "receive",
          amount: Math.random() * 1000,
          otherParty: `${Math.random().toString(36).substring(2, 10)}...`,
          status: "confirmed",
        }))
    } catch (error) {
      console.error("Error getting token transactions:", error)
      return []
    }
  }
}

// Export a singleton instance
export const tokenTransactionManager = new TokenTransactionManager()

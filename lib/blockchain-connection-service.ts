import { Connection, clusterApiUrl, type Cluster } from "@solana/web3.js"
import { getSecureEnvVariable, setSecureEnvVariable } from "@/lib/secure-env-storage"
import { providers } from "ethers"

// Types de réseaux supportés
export type NetworkType = "solana" | "bnb"
export type SolanaNetwork = "mainnet-beta" | "devnet" | "testnet"
export type BnbNetwork = "mainnet" | "testnet"

// Interface pour les connexions
interface ConnectionConfig {
  url: string
  enabled: boolean
  lastChecked?: string
  status: "connected" | "disconnected" | "error"
}

// Cache des connexions
const connectionCache: Record<string, { connection: any; timestamp: number }> = {}
const CACHE_EXPIRATION = 5 * 60 * 1000 // 5 minutes

/**
 * Obtenir une connexion Solana
 */
export async function getSolanaConnection(network: SolanaNetwork = "mainnet-beta"): Promise<Connection> {
  try {
    const cacheKey = `solana_${network}`
    const now = Date.now()

    // Vérifier le cache
    if (connectionCache[cacheKey] && now - connectionCache[cacheKey].timestamp < CACHE_EXPIRATION) {
      return connectionCache[cacheKey].connection
    }

    // Récupérer l'URL RPC depuis les variables d'environnement
    let rpcUrl: string | null = null

    if (network === "mainnet-beta") {
      rpcUrl = await getSecureEnvVariable("SOLANA_MAINNET_API_ENDPOINT")
    } else if (network === "devnet") {
      rpcUrl = await getSecureEnvVariable("SOLANA_DEVNET_API_ENDPOINT")
    } else if (network === "testnet") {
      rpcUrl = await getSecureEnvVariable("SOLANA_TESTNET_API_ENDPOINT")
    }

    // Utiliser l'URL RPC personnalisée ou l'URL par défaut
    const connectionUrl = rpcUrl || clusterApiUrl(network as Cluster)
    const connection = new Connection(connectionUrl, "confirmed")

    // Mettre en cache la connexion
    connectionCache[cacheKey] = {
      connection,
      timestamp: now,
    }

    return connection
  } catch (error) {
    console.error(`Erreur lors de la création de la connexion Solana ${network}:`, error)

    // Fallback sur l'URL par défaut en cas d'erreur
    const connection = new Connection(clusterApiUrl(network as Cluster), "confirmed")
    return connection
  }
}

/**
 * Obtenir un fournisseur BNB Chain
 */
export async function getBnbProvider(network: BnbNetwork = "mainnet"): Promise<providers.JsonRpcProvider> {
  try {
    const cacheKey = `bnb_${network}`
    const now = Date.now()

    // Vérifier le cache
    if (connectionCache[cacheKey] && now - connectionCache[cacheKey].timestamp < CACHE_EXPIRATION) {
      return connectionCache[cacheKey].connection
    }

    // Récupérer l'URL RPC depuis les variables d'environnement
    let rpcUrl: string | null = null

    if (network === "mainnet") {
      rpcUrl = await getSecureEnvVariable("BNB_MAINNET_API_ENDPOINT")
    } else if (network === "testnet") {
      rpcUrl = await getSecureEnvVariable("BNB_TESTNET_API_ENDPOINT")
    }

    // Utiliser l'URL RPC personnalisée ou l'URL par défaut
    const connectionUrl =
      rpcUrl ||
      (network === "mainnet" ? "https://bsc-dataseed.binance.org" : "https://data-seed-prebsc-1-s1.binance.org:8545")

    const provider = new providers.JsonRpcProvider(connectionUrl)

    // Mettre en cache le fournisseur
    connectionCache[cacheKey] = {
      connection: provider,
      timestamp: now,
    }

    return provider
  } catch (error) {
    console.error(`Erreur lors de la création du fournisseur BNB Chain ${network}:`, error)

    // Fallback sur l'URL par défaut en cas d'erreur
    const defaultUrl =
      network === "mainnet" ? "https://bsc-dataseed.binance.org" : "https://data-seed-prebsc-1-s1.binance.org:8545"

    const provider = new providers.JsonRpcProvider(defaultUrl)
    return provider
  }
}

/**
 * Tester une connexion blockchain
 */
export async function testBlockchainConnection(
  networkType: NetworkType,
  network: SolanaNetwork | BnbNetwork,
  rpcUrl: string,
): Promise<{ success: boolean; message: string }> {
  try {
    if (networkType === "solana") {
      const connection = new Connection(rpcUrl, "confirmed")
      const health = await connection.getHealth()

      return {
        success: health === "ok",
        message: health === "ok" ? "Connexion au réseau Solana réussie" : `Erreur de connexion: ${health}`,
      }
    } else if (networkType === "bnb") {
      const provider = new providers.JsonRpcProvider(rpcUrl)
      const blockNumber = await provider.getBlockNumber()

      return {
        success: blockNumber > 0,
        message:
          blockNumber > 0
            ? `Connexion au réseau BNB Chain réussie (bloc #${blockNumber})`
            : "Erreur de connexion: impossible de récupérer le numéro de bloc",
      }
    }

    return {
      success: false,
      message: "Type de réseau non pris en charge",
    }
  } catch (error) {
    console.error(`Erreur lors du test de la connexion ${networkType} ${network}:`, error)
    return {
      success: false,
      message: `Erreur de connexion: ${error.message}`,
    }
  }
}

/**
 * Obtenir la configuration de connexion pour tous les réseaux
 */
export async function getAllNetworkConnections(): Promise<Record<string, ConnectionConfig>> {
  try {
    const networks = {
      solana_mainnet: {
        url: (await getSecureEnvVariable("SOLANA_MAINNET_API_ENDPOINT")) || clusterApiUrl("mainnet-beta"),
        enabled: ((await getSecureEnvVariable("SOLANA_MAINNET_ENABLED")) || "true") === "true",
        status: "disconnected" as const,
      },
      solana_devnet: {
        url: (await getSecureEnvVariable("SOLANA_DEVNET_API_ENDPOINT")) || clusterApiUrl("devnet"),
        enabled: ((await getSecureEnvVariable("SOLANA_DEVNET_ENABLED")) || "true") === "true",
        status: "disconnected" as const,
      },
      solana_testnet: {
        url: (await getSecureEnvVariable("SOLANA_TESTNET_API_ENDPOINT")) || clusterApiUrl("testnet"),
        enabled: ((await getSecureEnvVariable("SOLANA_TESTNET_ENABLED")) || "false") === "true",
        status: "disconnected" as const,
      },
      bnb_mainnet: {
        url: (await getSecureEnvVariable("BNB_MAINNET_API_ENDPOINT")) || "https://bsc-dataseed.binance.org",
        enabled: ((await getSecureEnvVariable("BNB_MAINNET_ENABLED")) || "false") === "true",
        status: "disconnected" as const,
      },
      bnb_testnet: {
        url:
          (await getSecureEnvVariable("BNB_TESTNET_API_ENDPOINT")) || "https://data-seed-prebsc-1-s1.binance.org:8545",
        enabled: ((await getSecureEnvVariable("BNB_TESTNET_ENABLED")) || "false") === "true",
        status: "disconnected" as const,
      },
    }

    // Tester les connexions activées
    for (const [key, config] of Object.entries(networks)) {
      if (config.enabled) {
        const [networkType, networkName] = key.split("_") as [NetworkType, SolanaNetwork | BnbNetwork]

        try {
          const result = await testBlockchainConnection(networkType, networkName, config.url)
          networks[key].status = result.success ? "connected" : "error"
          networks[key].lastChecked = new Date().toISOString()
        } catch (error) {
          console.error(`Erreur lors du test de la connexion ${key}:`, error)
          networks[key].status = "error"
          networks[key].lastChecked = new Date().toISOString()
        }
      }
    }

    return networks
  } catch (error) {
    console.error("Erreur lors de la récupération des connexions réseau:", error)
    throw error
  }
}

/**
 * Mettre à jour la configuration d'une connexion réseau
 */
export async function updateNetworkConnection(networkKey: string, config: Partial<ConnectionConfig>): Promise<boolean> {
  try {
    const [networkType, networkName] = networkKey.split("_") as [NetworkType, SolanaNetwork | BnbNetwork]

    // Mettre à jour l'URL RPC
    if (config.url) {
      await setSecureEnvVariable(`${networkKey.toUpperCase()}_API_ENDPOINT`, config.url, {
        isEncrypted: false,
        category: "blockchain",
        description: `Point de terminaison RPC pour ${networkType} ${networkName}`,
        updatedBy: "admin",
      })
    }

    // Mettre à jour l'état d'activation
    if (config.enabled !== undefined) {
      await setSecureEnvVariable(`${networkKey.toUpperCase()}_ENABLED`, config.enabled.toString(), {
        isEncrypted: false,
        category: "blockchain",
        description: `État d'activation pour ${networkType} ${networkName}`,
        updatedBy: "admin",
      })
    }

    // Invalider le cache
    delete connectionCache[networkKey]

    return true
  } catch (error) {
    console.error(`Erreur lors de la mise à jour de la connexion ${networkKey}:`, error)
    return false
  }
}

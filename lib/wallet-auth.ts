import { PublicKey } from "@solana/web3.js"
import * as bs58 from "bs58"
import nacl from "tweetnacl"

export async function verifyWalletSignature(
  walletAddress: string,
  message: string,
  signature: string,
): Promise<boolean> {
  try {
    // Convertir le message en Uint8Array
    const messageBytes = new TextEncoder().encode(message)

    // Convertir la signature de base58 en Uint8Array
    const signatureBytes = bs58.decode(signature)

    // Obtenir la clé publique
    const publicKey = new PublicKey(walletAddress)

    // Vérifier la signature
    return nacl.sign.detached.verify(messageBytes, signatureBytes, publicKey.toBytes())
  } catch (error) {
    console.error("Erreur lors de la vérification de la signature:", error)
    return false
  }
}

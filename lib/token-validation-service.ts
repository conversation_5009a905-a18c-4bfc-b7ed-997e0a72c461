import { type Connection, PublicKey } from "@solana/web3.js"
import { getMint } from "@solana/spl-token"
import { getTokenByAddress } from "./token-database-service"

/**
 * Vérifie si une adresse de token est valide et existe sur la blockchain
 */
export async function validateTokenAddress(
  tokenAddress: string,
  connection: Connection,
): Promise<{ isValid: boolean; error?: string }> {
  try {
    // Vérifier si l'adresse est une adresse Solana valide
    let publicKey: PublicKey

    try {
      publicKey = new PublicKey(tokenAddress)
    } catch (error) {
      return { isValid: false, error: "Adresse de token invalide" }
    }

    // Vérifier si le token existe dans notre base de données
    try {
      const tokenFromDb = await getTokenByAddress(tokenAddress)
      if (tokenFromDb) {
        return { isValid: true }
      }
    } catch (error) {
      console.error("Erreur lors de la vérification du token dans la base de données:", error)
      // Continuer avec la vérification sur la blockchain
    }

    // Si le token n'est pas dans notre base de données, vérifier sur la blockchain
    try {
      const tokenInfo = await getMint(connection, publicKey)
      if (tokenInfo.isInitialized) {
        return { isValid: true }
      } else {
        return { isValid: false, error: "Le token existe mais n'est pas initialisé" }
      }
    } catch (error) {
      console.error("Erreur lors de la vérification du token sur la blockchain:", error)

      // Pour la démo, considérer que le token est valide même s'il n'existe pas sur la blockchain
      // Dans un environnement de production, vous devriez retourner une erreur ici
      return { isValid: true }
    }
  } catch (error) {
    console.error("Erreur lors de la validation de l'adresse du token:", error)

    // Pour la démo, considérer que le token est valide même en cas d'erreur
    // Dans un environnement de production, vous devriez retourner une erreur ici
    return { isValid: true }
  }
}

/**
 * Récupère les informations d'un token à partir de son adresse
 */
export async function getTokenInfo(tokenAddress: string, connection: Connection) {
  try {
    // Vérifier si le token existe dans notre base de données
    try {
      const tokenFromDb = await getTokenByAddress(tokenAddress)
      if (tokenFromDb) {
        return {
          success: true,
          tokenInfo: tokenFromDb,
        }
      }
    } catch (error) {
      console.error("Erreur lors de la récupération du token depuis la base de données:", error)
      // Continuer avec la récupération depuis la blockchain
    }

    // Si le token n'est pas dans notre base de données, récupérer les informations depuis la blockchain
    try {
      const publicKey = new PublicKey(tokenAddress)
      const tokenInfo = await getMint(connection, publicKey)

      return {
        success: true,
        tokenInfo: {
          address: tokenAddress,
          decimals: tokenInfo.decimals,
          supply: tokenInfo.supply.toString(),
          mintAuthority: tokenInfo.mintAuthority?.toString() || null,
          freezeAuthority: tokenInfo.freezeAuthority?.toString() || null,
          isInitialized: tokenInfo.isInitialized,
          // Ajouter des valeurs par défaut pour les autres champs
          name: `Token ${tokenAddress.slice(0, 4)}`,
          symbol: tokenAddress.slice(0, 3).toUpperCase(),
          totalSupply: Number(tokenInfo.supply) / Math.pow(10, tokenInfo.decimals),
          circulatingSupply: Number(tokenInfo.supply) / Math.pow(10, tokenInfo.decimals),
          price: 0.01,
          priceChange24h: 0,
          marketCap: 0.01 * (Number(tokenInfo.supply) / Math.pow(10, tokenInfo.decimals)),
          volume24h: 0,
          holders: 1,
          createdAt: new Date().toISOString(),
          network: "devnet",
        },
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des informations du token depuis la blockchain:", error)

      // Pour la démo, créer des informations fictives
      return {
        success: true,
        tokenInfo: {
          address: tokenAddress,
          name: `Token ${tokenAddress.slice(0, 4)}`,
          symbol: tokenAddress.slice(0, 3).toUpperCase(),
          decimals: 9,
          supply: "1000000000",
          totalSupply: 1000000000,
          circulatingSupply: 500000000,
          price: 0.01,
          priceChange24h: 0,
          marketCap: 10000,
          volume24h: 1000,
          holders: 100,
          createdAt: new Date().toISOString(),
          network: "devnet",
          mintAuthority: null,
          freezeAuthority: null,
          isInitialized: true,
        },
      }
    }
  } catch (error) {
    console.error("Erreur lors de la récupération des informations du token:", error)
    return {
      success: false,
      error: "Erreur lors de la récupération des informations du token",
    }
  }
}

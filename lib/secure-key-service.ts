import { Keypair } from "@solana/web3.js"
import bs58 from "bs58"

/**
 * Service for securely managing platform keys
 */
class SecureKeyService {
  private platformKeypair: Keypair | null = null

  constructor() {
    // Initialiser immédiatement le keypair
    this.initializePlatformKeypair()
  }

  /**
   * Initialise le keypair de la plateforme
   */
  private initializePlatformKeypair(): void {
    try {
      // ATTENTION: Ceci est une solution temporaire pour les tests uniquement
      // En production, utilisez toujours des variables d'environnement
      const hardcodedPrivateKey =
        "4mBzUCzxKnFFrW1JYNCSqUyZXPsWTVcmPruK7n2Y5jyDPsDXwwwwSGKR31qNif5xqBfuEY6sEmnfXSb2a7fYPWGm"

      // Décoder la clé privée et créer un keypair
      const privateKeyBytes = bs58.decode(hardcodedPrivateKey)
      this.platformKeypair = Keypair.fromSecretKey(privateKeyBytes)

      console.log("Keypair de plateforme initialisé avec succès (méthode temporaire)")
      console.log("Clé publique:", this.platformKeypair.publicKey.toString())
    } catch (error) {
      console.error("Erreur d'initialisation du keypair de plateforme:", error)
    }
  }

  /**
   * Obtenir le keypair de la plateforme
   */
  getPlatformKeypair(): Keypair | null {
    return this.platformKeypair
  }

  /**
   * Vérifier si le keypair de la plateforme est disponible
   */
  isPlatformKeypairAvailable(): boolean {
    return this.platformKeypair !== null
  }

  /**
   * Obtenir la clé publique de la plateforme sous forme de chaîne
   */
  getPlatformPublicKey(): string | null {
    return this.platformKeypair ? this.platformKeypair.publicKey.toString() : null
  }
}

// Exporter une instance singleton
const secureKeyService = new SecureKeyService()
export default secureKeyService

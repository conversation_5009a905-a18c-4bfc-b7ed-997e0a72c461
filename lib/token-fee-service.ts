import { Connection, PublicKey, Transaction, type Keypair } from "@solana/web3.js"
import { getOrCreateAssociatedTokenAccount, transfer } from "@solana/spl-token"
import { envConfig } from "./env-config"

export interface FeeDistribution {
  devFeePercentage: number
  burnFeePercentage: number
  priceImpactFeePercentage: number
  antiGainTaxThreshold: number
  antiGainTaxPercentage: number
}

export interface FeeWallets {
  devWallet: PublicKey
  burnWallet: PublicKey
  priceImpactWallet: PublicKey
  taxWallet: PublicKey
}

class TokenFeeService {
  private connection: Connection
  private feeDistribution: FeeDistribution
  private feeWallets: FeeWallets

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")

    // Configuration par défaut des fees
    this.feeDistribution = {
      devFeePercentage: 0.25, // 0.25% du montant total
      burnFeePercentage: 0.25, // 0.25% du montant total
      priceImpactFeePercentage: 0.5, // 0.5% du montant total
      antiGainTaxThreshold: 150, // 150% de gain
      antiGainTaxPercentage: 10, // 10% du profit
    }

    // Adresses des wallets pour les fees (à remplacer par les vraies adresses)
    this.feeWallets = {
      devWallet: new PublicKey("GFTDev11111111111111111111111111111111111111"),
      burnWallet: new PublicKey("GFTBurn11111111111111111111111111111111111111"),
      priceImpactWallet: new PublicKey("GFTImpact111111111111111111111111111111111"),
      taxWallet: new PublicKey("GFTTax111111111111111111111111111111111111111"),
    }
  }

  /**
   * Calcule et distribue les fees pour un transfert de token
   */
  async calculateAndDistributeFees(
    tokenMint: PublicKey,
    sourceWallet: PublicKey,
    destinationWallet: PublicKey,
    amount: number,
    payer: Keypair,
    previousBalance?: number,
  ): Promise<{
    success: boolean
    netAmount: number
    fees: {
      devFee: number
      burnFee: number
      priceImpactFee: number
      antiGainTax?: number
    }
    error?: string
  }> {
    try {
      // Calculer les fees de base (1% au total)
      const baseFeeRate = 0.01 // 1%
      const baseFee = Math.floor(amount * baseFeeRate)

      const devFee = Math.floor(baseFee * this.feeDistribution.devFeePercentage)
      const burnFee = Math.floor(baseFee * this.feeDistribution.burnFeePercentage)
      const priceImpactFee = Math.floor(baseFee * this.feeDistribution.priceImpactFeePercentage)

      // Montant net après les fees de base
      let netAmount = amount - (devFee + burnFee + priceImpactFee)

      // Calculer l'anti-gain tax si applicable
      let antiGainTax = 0
      if (previousBalance !== undefined) {
        const profit = amount - previousBalance
        if (profit > 0) {
          const gainPercentage = (profit / previousBalance) * 100
          if (gainPercentage > this.feeDistribution.antiGainTaxThreshold) {
            antiGainTax = Math.floor(profit * (this.feeDistribution.antiGainTaxPercentage / 100))
            netAmount -= antiGainTax
          }
        }
      }

      // Créer les comptes de token associés pour les wallets de fees
      const sourceTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        payer,
        tokenMint,
        sourceWallet,
      )

      const devTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        payer,
        tokenMint,
        this.feeWallets.devWallet,
      )

      const burnTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        payer,
        tokenMint,
        this.feeWallets.burnWallet,
      )

      const priceImpactTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        payer,
        tokenMint,
        this.feeWallets.priceImpactWallet,
      )

      // Créer une transaction pour distribuer les fees
      const transaction = new Transaction()

      // Ajouter les instructions de transfert pour les fees
      if (devFee > 0) {
        transaction.add(
          transfer(this.connection, payer, sourceTokenAccount.address, devTokenAccount.address, sourceWallet, devFee),
        )
      }

      if (burnFee > 0) {
        transaction.add(
          transfer(this.connection, payer, sourceTokenAccount.address, burnTokenAccount.address, sourceWallet, burnFee),
        )
      }

      if (priceImpactFee > 0) {
        transaction.add(
          transfer(
            this.connection,
            payer,
            sourceTokenAccount.address,
            priceImpactTokenAccount.address,
            sourceWallet,
            priceImpactFee,
          ),
        )
      }

      if (antiGainTax > 0) {
        const taxTokenAccount = await getOrCreateAssociatedTokenAccount(
          this.connection,
          payer,
          tokenMint,
          this.feeWallets.taxWallet,
        )

        transaction.add(
          transfer(
            this.connection,
            payer,
            sourceTokenAccount.address,
            taxTokenAccount.address,
            sourceWallet,
            antiGainTax,
          ),
        )
      }

      // Signer et envoyer la transaction
      transaction.feePayer = payer.publicKey
      transaction.recentBlockhash = (await this.connection.getLatestBlockhash()).blockhash

      const signature = await this.connection.sendTransaction(transaction, [payer])
      await this.connection.confirmTransaction(signature)

      return {
        success: true,
        netAmount,
        fees: {
          devFee,
          burnFee,
          priceImpactFee,
          antiGainTax: antiGainTax > 0 ? antiGainTax : undefined,
        },
      }
    } catch (error: any) {
      console.error("Erreur lors du calcul et de la distribution des fees:", error)
      return {
        success: false,
        netAmount: 0,
        fees: {
          devFee: 0,
          burnFee: 0,
          priceImpactFee: 0,
        },
        error: error.message || "Une erreur s'est produite lors du calcul et de la distribution des fees",
      }
    }
  }

  /**
   * Configure la distribution des fees
   */
  setFeeDistribution(distribution: Partial<FeeDistribution>): void {
    this.feeDistribution = { ...this.feeDistribution, ...distribution }
  }

  /**
   * Configure les adresses des wallets pour les fees
   */
  setFeeWallets(wallets: Partial<FeeWallets>): void {
    this.feeWallets = { ...this.feeWallets, ...wallets }
  }

  /**
   * Récupère la configuration actuelle des fees
   */
  getFeeConfiguration(): {
    distribution: FeeDistribution
    wallets: {
      devWallet: string
      burnWallet: string
      priceImpactWallet: string
      taxWallet: string
    }
  } {
    return {
      distribution: this.feeDistribution,
      wallets: {
        devWallet: this.feeWallets.devWallet.toString(),
        burnWallet: this.feeWallets.burnWallet.toString(),
        priceImpactWallet: this.feeWallets.priceImpactWallet.toString(),
        taxWallet: this.feeWallets.taxWallet.toString(),
      },
    }
  }
}

// Exporter une instance singleton du service
const tokenFeeService = new TokenFeeService()
export default tokenFeeService

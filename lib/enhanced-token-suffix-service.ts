import { Keypair } from "@solana/web3.js"
import { Worker } from "worker_threads"
import path from "path"
import os from "os"

export interface GrindingResult {
  success: boolean
  keypair?: {
    public: string
    secret: number[]
  }
  attempts?: number
  error?: string
  timeElapsed?: number
}

export interface SuffixConfig {
  defaultSuffix: string
  aiSuffix: string
  premiumSuffixes: string[]
  networkSuffixes: Record<string, string>
  reservedSuffixes: string[]
}

class EnhancedTokenSuffixService {
  private suffixConfig: SuffixConfig = {
    defaultSuffix: "GFMM",
    aiSuffix: "GFAI",
    premiumSuffixes: ["GF", "GFVIP", "GFPRO"],
    networkSuffixes: {
      "solana-mainnet": "GFMM",
      "solana-devnet": "GFDEV",
      "solana-testnet": "GFTEST",
    },
    reservedSuffixes: ["ADMIN", "TEAM", "RESERVE", "TREASURY"],
  }

  /**
   * Génère un keypair dont l'adresse publique se termine par le suffixe spécifié
   * Utilise des worker threads pour accélérer le processus
   */
  async generateKeypairWithSuffix(
    suffix: string,
    maxAttempts = 10000000,
    maxWorkers: number = os.cpus().length - 1,
  ): Promise<GrindingResult> {
    try {
      console.log(`Génération d'un keypair avec le suffixe: ${suffix}`)

      // Vérifier que le suffixe est valide
      if (!this.isValidSuffix(suffix)) {
        return {
          success: false,
          error: "Le suffixe contient des caractères non valides en base58",
        }
      }

      // Vérifier si le suffixe est réservé
      if (this.isReservedSuffix(suffix)) {
        return {
          success: false,
          error: "Ce suffixe est réservé et ne peut pas être utilisé",
        }
      }

      // Estimer le temps de génération
      const estimatedTime = this.estimateGrindingTime(suffix)
      console.log(`Temps estimé: ${this.formatEstimatedTime(estimatedTime)}`)

      // Si le temps estimé est trop long, refuser la demande
      if (estimatedTime > 3600 && suffix.length > 5) {
        return {
          success: false,
          error: `Le temps estimé pour générer ce suffixe est trop long (${this.formatEstimatedTime(estimatedTime)}). Veuillez choisir un suffixe plus court.`,
        }
      }

      const startTime = Date.now()

      // Utiliser des worker threads pour paralléliser la recherche
      const numWorkers = Math.min(maxWorkers, Math.ceil(suffix.length / 2))
      const attemptsPerWorker = Math.ceil(maxAttempts / numWorkers)

      console.log(`Lancement de ${numWorkers} workers pour la recherche du suffixe ${suffix}`)

      const workers: Worker[] = []
      const promises: Promise<GrindingResult>[] = []

      for (let i = 0; i < numWorkers; i++) {
        const promise = new Promise<GrindingResult>((resolve) => {
          const worker = new Worker(path.resolve(process.cwd(), "workers/suffix-grinder.js"), {
            workerData: {
              suffix,
              maxAttempts: attemptsPerWorker,
              workerId: i,
            },
          })

          workers.push(worker)

          worker.on("message", (result: GrindingResult) => {
            if (result.success) {
              // Arrêter tous les autres workers
              workers.forEach((w) => w.terminate())
              resolve(result)
            }
          })

          worker.on("error", (err) => {
            console.error(`Worker ${i} error:`, err)
            resolve({
              success: false,
              error: `Worker error: ${err.message}`,
            })
          })

          worker.on("exit", (code) => {
            if (code !== 0) {
              resolve({
                success: false,
                error: `Worker stopped with exit code ${code}`,
              })
            } else {
              resolve({
                success: false,
                error: "Worker completed without finding a match",
              })
            }
          })
        })

        promises.push(promise)
      }

      // Attendre qu'un worker trouve une correspondance ou que tous échouent
      const results = await Promise.race(promises)

      // Arrêter tous les workers restants
      workers.forEach((w) => w.terminate())

      const timeElapsed = (Date.now() - startTime) / 1000

      if (results.success) {
        console.log(`Keypair trouvé après ${results.attempts} tentatives en ${timeElapsed.toFixed(2)} secondes`)
        return {
          ...results,
          timeElapsed,
        }
      }

      return {
        success: false,
        attempts: maxAttempts,
        timeElapsed,
        error: `Échec après ${maxAttempts} tentatives (${timeElapsed.toFixed(2)} secondes)`,
      }
    } catch (error: any) {
      console.error("Erreur lors de la génération du keypair avec suffixe:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la génération du keypair avec suffixe",
      }
    }
  }

  /**
   * Méthode de secours pour générer un keypair avec suffixe en single-thread
   */
  generateKeypairWithSuffixSync(suffix: string, maxAttempts = 1000000): GrindingResult {
    try {
      console.log(`Génération synchrone d'un keypair avec le suffixe: ${suffix}`)

      // Vérifier que le suffixe est valide
      if (!this.isValidSuffix(suffix)) {
        return {
          success: false,
          error: "Le suffixe contient des caractères non valides en base58",
        }
      }

      const startTime = Date.now()
      let attempts = 0

      while (attempts < maxAttempts) {
        attempts++

        // Générer un nouveau keypair
        const keypair = Keypair.generate()
        const publicKey = keypair.publicKey.toString()

        // Vérifier si l'adresse se termine par le suffixe
        if (publicKey.endsWith(suffix)) {
          const timeElapsed = (Date.now() - startTime) / 1000
          console.log(`Keypair trouvé après ${attempts} tentatives en ${timeElapsed.toFixed(2)} secondes: ${publicKey}`)

          // Convertir la clé secrète en tableau de nombres
          const secretKey = Array.from(keypair.secretKey)

          return {
            success: true,
            keypair: {
              public: publicKey,
              secret: secretKey,
            },
            attempts,
            timeElapsed,
          }
        }

        // Afficher la progression toutes les 10000 tentatives
        if (attempts % 10000 === 0) {
          console.log(`${attempts} tentatives effectuées...`)
        }
      }

      const timeElapsed = (Date.now() - startTime) / 1000
      return {
        success: false,
        attempts,
        timeElapsed,
        error: `Échec après ${maxAttempts} tentatives (${timeElapsed.toFixed(2)} secondes)`,
      }
    } catch (error: any) {
      console.error("Erreur lors de la génération synchrone du keypair avec suffixe:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la génération synchrone du keypair avec suffixe",
      }
    }
  }

  /**
   * Vérifie si un suffixe est valide en base58
   */
  isValidSuffix(suffix: string): boolean {
    // Caractères valides en base58
    const base58Chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

    for (const char of suffix) {
      if (!base58Chars.includes(char)) {
        return false
      }
    }

    return true
  }

  /**
   * Vérifie si un suffixe est réservé
   */
  isReservedSuffix(suffix: string): boolean {
    return this.suffixConfig.reservedSuffixes.includes(suffix)
  }

  /**
   * Estime le temps de génération en secondes
   */
  estimateGrindingTime(suffix: string): number {
    // Estimation basée sur la longueur du suffixe
    // En moyenne, il faut 58^n tentatives pour trouver un suffixe de longueur n
    const averageAttempts = Math.pow(58, suffix.length)

    // Supposons que nous pouvons générer 100,000 keypairs par seconde avec multi-threading
    const attemptsPerSecond = 100000

    return averageAttempts / attemptsPerSecond
  }

  /**
   * Formate le temps estimé en une chaîne lisible
   */
  formatEstimatedTime(seconds: number): string {
    if (seconds < 60) {
      return `${Math.ceil(seconds)} secondes`
    } else if (seconds < 3600) {
      return `${Math.ceil(seconds / 60)} minutes`
    } else if (seconds < 86400) {
      return `${Math.ceil(seconds / 3600)} heures`
    } else {
      return `${Math.ceil(seconds / 86400)} jours`
    }
  }

  /**
   * Récupère le suffixe à utiliser pour un réseau donné
   */
  getSuffixForNetwork(networkId: string): string {
    return this.suffixConfig.networkSuffixes[networkId] || this.suffixConfig.defaultSuffix
  }

  /**
   * Récupère le suffixe à utiliser pour les tokens générés par IA
   */
  getAISuffixForNetwork(networkId: string): string {
    return this.suffixConfig.aiSuffix
  }

  /**
   * Vérifie si un suffixe est premium
   */
  isPremiumSuffix(suffix: string): boolean {
    return this.suffixConfig.premiumSuffixes.includes(suffix)
  }

  /**
   * Définit la configuration des suffixes
   */
  setConfig(config: Partial<SuffixConfig>): void {
    this.suffixConfig = { ...this.suffixConfig, ...config }
  }
}

// Exporter une instance singleton du service
const enhancedTokenSuffixService = new EnhancedTokenSuffixService()
export default enhancedTokenSuffixService

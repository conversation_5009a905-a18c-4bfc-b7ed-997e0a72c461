import { Connection } from "@solana/web3.js"
import { envConfig } from "./env-config"

export interface PriceData {
  time: number // timestamp en secondes
  open: number
  high: number
  low: number
  close: number
  volume?: number
}

export interface TradeData {
  time: number
  price: number
  amount: number
  type: "buy" | "sell"
  txId: string
  account: string
}

export interface TokenChartData {
  prices: PriceData[]
  trades: TradeData[]
  marketCap: number
  volume24h: number
  liquidity: number
  holders: number
}

class TokenChartService {
  private connection: Connection

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
  }

  setConnection(rpcUrl: string) {
    this.connection = new Connection(rpcUrl, "confirmed")
  }

  async getTokenChartData(
    tokenAddress: string,
    timeframe: "1h" | "24h" | "7d" | "30d" = "24h",
  ): Promise<TokenChartData> {
    try {
      // Dans une implémentation réelle, nous ferions des appels à l'API Solana
      // et à des services comme Birdeye ou Jupiter pour obtenir les données

      // Pour cette démo, nous générons des données simulées
      const now = Math.floor(Date.now() / 1000)
      const prices: PriceData[] = []
      const trades: TradeData[] = []

      let basePrice = 0.00001
      let timeStep: number
      let dataPoints: number

      switch (timeframe) {
        case "1h":
          timeStep = 60 // 1 minute
          dataPoints = 60
          break
        case "24h":
          timeStep = 900 // 15 minutes
          dataPoints = 96
          break
        case "7d":
          timeStep = 3600 // 1 heure
          dataPoints = 168
          break
        case "30d":
          timeStep = 14400 // 4 heures
          dataPoints = 180
          break
      }

      // Générer des données de prix simulées
      for (let i = 0; i < dataPoints; i++) {
        const time = now - (dataPoints - i) * timeStep
        const volatility = 0.05
        const changePercent = (Math.random() - 0.5) * volatility

        if (i > 0) {
          basePrice = prices[i - 1].close * (1 + changePercent)
        }

        const open = basePrice
        const close = basePrice * (1 + (Math.random() - 0.5) * 0.02)
        const high = Math.max(open, close) * (1 + Math.random() * 0.01)
        const low = Math.min(open, close) * (1 - Math.random() * 0.01)
        const volume = Math.random() * 10000

        prices.push({
          time,
          open,
          high,
          low,
          close,
          volume,
        })

        // Générer quelques transactions aléatoires
        if (Math.random() > 0.7) {
          const tradeType = Math.random() > 0.5 ? "buy" : "sell"
          const tradeAmount = Math.random() * 5000
          const tradePrice = close * (1 + (Math.random() - 0.5) * 0.005)

          trades.push({
            time,
            price: tradePrice,
            amount: tradeAmount,
            type: tradeType,
            txId: `tx_${Math.random().toString(36).substring(2, 10)}`,
            account: `${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 6)}`,
          })
        }
      }

      return {
        prices,
        trades,
        marketCap: basePrice * **********,
        volume24h: Math.random() * 50000,
        liquidity: Math.random() * 100000,
        holders: Math.floor(Math.random() * 1000) + 100,
      }
    } catch (error) {
      console.error("Error fetching token chart data:", error)
      throw error
    }
  }

  async subscribeToTokenUpdates(tokenAddress: string, callback: (data: PriceData) => void): Promise<() => void> {
    // Dans une implémentation réelle, nous utiliserions un WebSocket pour les mises à jour en temps réel
    // Pour cette démo, nous simulons des mises à jour périodiques

    const interval = setInterval(() => {
      const lastPrice = Math.random() * 0.0001
      const time = Math.floor(Date.now() / 1000)

      const priceData: PriceData = {
        time,
        open: lastPrice * 0.99,
        high: lastPrice * 1.02,
        low: lastPrice * 0.98,
        close: lastPrice,
        volume: Math.random() * 1000,
      }

      callback(priceData)
    }, 5000)

    // Retourner une fonction pour annuler l'abonnement
    return () => clearInterval(interval)
  }

  async getTokenHolders(tokenAddress: string): Promise<any[]> {
    try {
      // Dans une implémentation réelle, nous ferions un appel à l'API Solana
      // Pour cette démo, nous retournons des données simulées

      const holders = []
      for (let i = 0; i < 20; i++) {
        holders.push({
          address: `${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 6)}`,
          balance: Math.floor(Math.random() * 10000000),
          percentage: Math.random() * 10,
        })
      }

      return holders
    } catch (error) {
      console.error("Error fetching token holders:", error)
      throw error
    }
  }
}

// Exporter une instance singleton du service
const tokenChartService = new TokenChartService()
export default tokenChartService

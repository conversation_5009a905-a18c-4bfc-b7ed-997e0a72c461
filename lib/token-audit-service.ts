import { Connection, PublicKey } from "@solana/web3.js"
import { envConfig } from "./env-config"
import tokenMarketService from "./token-market-service"

export interface TokenAuditResult {
  tokenAddress: string
  name: string
  symbol: string
  score: number // 0-100
  issues: TokenAuditIssue[]
  recommendations: string[]
  lastUpdated: string
}

export interface TokenAuditIssue {
  severity: "critical" | "high" | "medium" | "low" | "info"
  category: "security" | "liquidity" | "ownership" | "code" | "other"
  description: string
  impact: string
  recommendation: string
}

class TokenAuditService {
  private connection: Connection

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
  }

  /**
   * Effectue un audit complet d'un token
   */
  async auditToken(tokenAddress: string): Promise<TokenAuditResult | null> {
    try {
      console.log(`Audit du token ${tokenAddress}...`)

      // Récupérer les informations de base du token
      const mintPublicKey = new PublicKey(tokenAddress)
      const tokenInfo = await this.connection.getParsedAccountInfo(mintPublicKey)

      if (!tokenInfo.value) {
        console.error(`Token ${tokenAddress} non trouvé sur la blockchain`)
        return null
      }

      // Récupérer les données de marché
      const marketData = await tokenMarketService.getTokenMarketData(tokenAddress)
      if (!marketData) {
        return null
      }

      // Récupérer les détenteurs
      const holders = await tokenMarketService.getTokenHolders(tokenAddress, 10)

      // Analyser la distribution des tokens
      const ownershipConcentration = this.analyzeOwnershipConcentration(holders)

      // Analyser la liquidité
      const liquidityIssues = this.analyzeLiquidity(marketData)

      // Analyser les transactions
      const transactions = await tokenMarketService.getTokenTransactions(tokenAddress, 100)
      const transactionIssues = this.analyzeTransactions(transactions)

      // Compiler les problèmes
      const issues = [...ownershipConcentration.issues, ...liquidityIssues.issues, ...transactionIssues.issues]

      // Calculer le score global
      const score = this.calculateAuditScore(issues)

      // Générer des recommandations
      const recommendations = this.generateRecommendations(issues)

      return {
        tokenAddress,
        name: marketData.name,
        symbol: marketData.symbol,
        score,
        issues,
        recommendations,
        lastUpdated: new Date().toISOString(),
      }
    } catch (error) {
      console.error("Erreur lors de l'audit du token:", error)
      return null
    }
  }

  /**
   * Analyse la concentration de la propriété des tokens
   */
  private analyzeOwnershipConcentration(holders: any[]): { issues: TokenAuditIssue[] } {
    const issues: TokenAuditIssue[] = []

    // Vérifier si le plus grand détenteur possède plus de 50% des tokens
    const largestHolder = holders[0]
    if (largestHolder && largestHolder.percentage > 50) {
      issues.push({
        severity: "high",
        category: "ownership",
        description: `Le plus grand détenteur possède ${largestHolder.percentage.toFixed(2)}% des tokens`,
        impact: "Risque élevé de manipulation du marché et de dump",
        recommendation: "Diversifier la distribution des tokens pour réduire la concentration",
      })
    } else if (largestHolder && largestHolder.percentage > 20) {
      issues.push({
        severity: "medium",
        category: "ownership",
        description: `Le plus grand détenteur possède ${largestHolder.percentage.toFixed(2)}% des tokens`,
        impact: "Risque modéré de manipulation du marché",
        recommendation: "Envisager de diversifier davantage la distribution des tokens",
      })
    }

    // Vérifier si les 5 plus grands détenteurs possèdent plus de 80% des tokens
    const top5Percentage = holders.slice(0, 5).reduce((sum, holder) => sum + holder.percentage, 0)
    if (top5Percentage > 80) {
      issues.push({
        severity: "high",
        category: "ownership",
        description: `Les 5 plus grands détenteurs possèdent ${top5Percentage.toFixed(2)}% des tokens`,
        impact: "Forte concentration de la propriété, risque de manipulation coordonnée",
        recommendation: "Élargir la base d'investisseurs et encourager une distribution plus équitable",
      })
    } else if (top5Percentage > 60) {
      issues.push({
        severity: "medium",
        category: "ownership",
        description: `Les 5 plus grands détenteurs possèdent ${top5Percentage.toFixed(2)}% des tokens`,
        impact: "Concentration modérée de la propriété, risque potentiel de manipulation",
        recommendation: "Continuer à élargir la base d'investisseurs",
      })
    }

    return { issues }
  }

  /**
   * Analyse la liquidité du token
   */
  private analyzeLiquidity(marketData: any): { issues: TokenAuditIssue[] } {
    const issues: TokenAuditIssue[] = []

    // Vérifier si la liquidité est suffisante par rapport à la capitalisation boursière
    const liquidityRatio = marketData.liquidity / marketData.marketCap
    if (liquidityRatio < 0.05) {
      issues.push({
        severity: "critical",
        category: "liquidity",
        description: `Ratio de liquidité très faible: ${(liquidityRatio * 100).toFixed(2)}%`,
        impact: "Risque extrême de manipulation des prix et d'impossibilité de vendre",
        recommendation: "Augmenter significativement la liquidité pour sécuriser le marché",
      })
    } else if (liquidityRatio < 0.1) {
      issues.push({
        severity: "high",
        category: "liquidity",
        description: `Ratio de liquidité faible: ${(liquidityRatio * 100).toFixed(2)}%`,
        impact: "Risque élevé de slippage important et de manipulation des prix",
        recommendation: "Augmenter la liquidité pour améliorer la stabilité des prix",
      })
    } else if (liquidityRatio < 0.2) {
      issues.push({
        severity: "medium",
        category: "liquidity",
        description: `Ratio de liquidité modéré: ${(liquidityRatio * 100).toFixed(2)}%`,
        impact: "Risque modéré de slippage lors de transactions importantes",
        recommendation: "Envisager d'augmenter la liquidité pour améliorer la profondeur du marché",
      })
    }

    // Vérifier le volume de transactions par rapport à la liquidité
    const volumeToLiquidityRatio = marketData.volume24h / marketData.liquidity
    if (volumeToLiquidityRatio > 5) {
      issues.push({
        severity: "high",
        category: "liquidity",
        description: `Volume de transactions très élevé par rapport à la liquidité: ${volumeToLiquidityRatio.toFixed(2)}x`,
        impact: "Risque élevé de volatilité excessive et d'épuisement de la liquidité",
        recommendation: "Augmenter significativement la liquidité pour soutenir le volume de transactions",
      })
    } else if (volumeToLiquidityRatio > 2) {
      issues.push({
        severity: "medium",
        category: "liquidity",
        description: `Volume de transactions élevé par rapport à la liquidité: ${volumeToLiquidityRatio.toFixed(2)}x`,
        impact: "Risque modéré de volatilité et de slippage",
        recommendation: "Envisager d'augmenter la liquidité pour mieux absorber le volume de transactions",
      })
    }

    return { issues }
  }

  /**
   * Analyse les transactions du token
   */
  private analyzeTransactions(transactions: any[]): { issues: TokenAuditIssue[] } {
    const issues: TokenAuditIssue[] = []

    // Compter les transactions par type
    const buyCount = transactions.filter((tx) => tx.type === "buy").length
    const sellCount = transactions.filter((tx) => tx.type === "sell").length
    const transferCount = transactions.filter((tx) => tx.type === "transfer").length

    // Vérifier le ratio ventes/achats
    const sellToBuyRatio = sellCount / (buyCount || 1)
    if (sellToBuyRatio > 3) {
      issues.push({
        severity: "critical",
        category: "other",
        description: `Ratio ventes/achats très élevé: ${sellToBuyRatio.toFixed(2)}`,
        impact: "Pression de vente extrême, risque imminent de chute des prix",
        recommendation:
          "Enquêter sur les raisons de cette pression de vente et prendre des mesures pour stabiliser le marché",
      })
    } else if (sellToBuyRatio > 2) {
      issues.push({
        severity: "high",
        category: "other",
        description: `Ratio ventes/achats élevé: ${sellToBuyRatio.toFixed(2)}`,
        impact: "Pression de vente significative, tendance baissière probable",
        recommendation: "Surveiller de près l'évolution des prix et envisager des mesures pour équilibrer le marché",
      })
    } else if (sellToBuyRatio > 1.5) {
      issues.push({
        severity: "medium",
        category: "other",
        description: `Ratio ventes/achats légèrement élevé: ${sellToBuyRatio.toFixed(2)}`,
        impact: "Légère pression de vente, risque de baisse modérée des prix",
        recommendation: "Surveiller la tendance et envisager des initiatives pour stimuler l'intérêt des acheteurs",
      })
    }

    // Vérifier la proportion de transferts
    const transferRatio = transferCount / transactions.length
    if (transferRatio > 0.7) {
      issues.push({
        severity: "medium",
        category: "other",
        description: `Proportion très élevée de transferts: ${(transferRatio * 100).toFixed(2)}%`,
        impact: "Activité de trading limitée, possible manipulation ou distribution en cours",
        recommendation: "Surveiller les destinataires des transferts et encourager l'activité de trading",
      })
    }

    return { issues }
  }

  /**
   * Calcule le score global de l'audit
   */
  private calculateAuditScore(issues: TokenAuditIssue[]): number {
    // Score de base
    let score = 100

    // Déduire des points en fonction de la gravité des problèmes
    for (const issue of issues) {
      switch (issue.severity) {
        case "critical":
          score -= 20
          break
        case "high":
          score -= 10
          break
        case "medium":
          score -= 5
          break
        case "low":
          score -= 2
          break
        case "info":
          score -= 0
          break
      }
    }

    // S'assurer que le score reste dans la plage 0-100
    return Math.max(0, Math.min(100, score))
  }

  /**
   * Génère des recommandations basées sur les problèmes identifiés
   */
  private generateRecommendations(issues: TokenAuditIssue[]): string[] {
    const recommendations = new Set<string>()

    // Ajouter les recommandations de chaque problème
    for (const issue of issues) {
      recommendations.add(issue.recommendation)
    }

    // Ajouter des recommandations générales si nécessaire
    if (issues.some((issue) => issue.category === "liquidity")) {
      recommendations.add(
        "Envisager d'ajouter plus de liquidité sur différents DEX pour améliorer la stabilité des prix",
      )
    }

    if (issues.some((issue) => issue.category === "ownership")) {
      recommendations.add(
        "Mettre en place un programme de distribution progressive des tokens pour réduire la concentration",
      )
    }

    return Array.from(recommendations)
  }

  /**
   * Vérifie si un token présente des risques de sécurité critiques
   */
  async checkSecurityRisks(tokenAddress: string): Promise<{
    safe: boolean
    criticalIssues: TokenAuditIssue[]
  }> {
    try {
      const auditResult = await this.auditToken(tokenAddress)

      if (!auditResult) {
        return {
          safe: false,
          criticalIssues: [
            {
              severity: "critical",
              category: "security",
              description: "Impossible d'auditer le token",
              impact: "Risque inconnu",
              recommendation: "Vérifier l'adresse du token et réessayer",
            },
          ],
        }
      }

      const criticalIssues = auditResult.issues.filter(
        (issue) => issue.severity === "critical" || issue.severity === "high",
      )

      return {
        safe: criticalIssues.length === 0,
        criticalIssues,
      }
    } catch (error) {
      console.error("Erreur lors de la vérification des risques de sécurité:", error)
      return {
        safe: false,
        criticalIssues: [
          {
            severity: "critical",
            category: "security",
            description: "Erreur lors de l'audit de sécurité",
            impact: "Risque inconnu",
            recommendation: "Réessayer ultérieurement ou contacter le support",
          },
        ],
      }
    }
  }

  /**
   * Génère un rapport d'audit complet au format HTML
   */
  async generateAuditReport(tokenAddress: string): Promise<string> {
    try {
      const auditResult = await this.auditToken(tokenAddress)

      if (!auditResult) {
        return `<div class="audit-error">Impossible de générer le rapport d'audit pour le token ${tokenAddress}</div>`
      }

      // Construire le rapport HTML
      let html = `
        <div class="audit-report">
          <h2>Rapport d'audit pour ${auditResult.name} (${auditResult.symbol})</h2>
          <p>Adresse: ${auditResult.tokenAddress}</p>
          <p>Date: ${new Date(auditResult.lastUpdated).toLocaleDateString()}</p>
          
          <div class="audit-score">
            <h3>Score de sécurité: ${auditResult.score}/100</h3>
            <div class="progress-bar">
              <div class="progress" style="width: ${auditResult.score}%; background-color: ${
                auditResult.score > 80 ? "green" : auditResult.score > 60 ? "orange" : "red"
              };"></div>
            </div>
          </div>
          
          <h3>Problèmes identifiés:</h3>
          <ul class="issues-list">
      `

      // Trier les problèmes par gravité
      const sortedIssues = [...auditResult.issues].sort((a, b) => {
        const severityOrder = { critical: 0, high: 1, medium: 2, low: 3, info: 4 }
        return severityOrder[a.severity] - severityOrder[b.severity]
      })

      // Ajouter chaque problème
      for (const issue of sortedIssues) {
        html += `
          <li class="issue ${issue.severity}">
            <span class="severity">${issue.severity.toUpperCase()}</span>
            <span class="category">${issue.category}</span>
            <p class="description">${issue.description}</p>
            <p class="impact"><strong>Impact:</strong> ${issue.impact}</p>
            <p class="recommendation"><strong>Recommandation:</strong> ${issue.recommendation}</p>
          </li>
        `
      }

      html += `
          </ul>
          
          <h3>Recommandations:</h3>
          <ul class="recommendations">
      `

      // Ajouter les recommandations
      for (const recommendation of auditResult.recommendations) {
        html += `<li>${recommendation}</li>`
      }

      html += `
          </ul>
          
          <p class="disclaimer">
            Avertissement: Ce rapport d'audit est fourni à titre informatif uniquement et ne constitue pas un conseil financier.
            Les investisseurs doivent effectuer leurs propres recherches avant de prendre des décisions d'investissement.
          </p>
        </div>
      `

      return html
    } catch (error) {
      console.error("Erreur lors de la génération du rapport d'audit:", error)
      return `<div class="audit-error">Erreur lors de la génération du rapport d'audit: ${error.message}</div>`
    }
  }
}

// Exporter une instance singleton du service
const tokenAuditService = new TokenAuditService()
export default tokenAuditService

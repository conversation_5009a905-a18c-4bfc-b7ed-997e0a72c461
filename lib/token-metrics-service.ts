import redisService from "./redis-service"
import bondingCurveService from "./bonding-curve-service"

interface TokenMetrics {
  totalSupply: number
  currentPrice: number
  marketCapUsd: number
  totalVolume: number
  holders: number
  transactions: number
  lastUpdated: string
  dexListingProgress: number
}

interface TokenTransaction {
  id: string
  type: "buy" | "sell"
  amount: number
  price: number
  total: number
  timestamp: string
  address: string
  txSignature: string
}

/**
 * Service for tracking and retrieving token metrics
 */
class TokenMetricsService {
  private readonly tokenMetricsPrefix = "token:metrics:"
  private readonly tokenTransactionsPrefix = "token:transactions:"
  private readonly platformMetricsKey = "platform:metrics"

  /**
   * Get metrics for a token
   */
  async getTokenMetrics(tokenAddress: string): Promise<TokenMetrics | null> {
    const key = `${this.tokenMetricsPrefix}${tokenAddress}`
    return redisService.get<TokenMetrics>(key)
  }

  /**
   * Update metrics for a token
   */
  async updateTokenMetrics(tokenAddress: string, metrics: Partial<TokenMetrics>): Promise<void> {
    const key = `${this.tokenMetricsPrefix}${tokenAddress}`
    const existingMetrics = await this.getTokenMetrics(tokenAddress)

    const updatedMetrics: TokenMetrics = {
      totalSupply: metrics.totalSupply ?? existingMetrics?.totalSupply ?? 0,
      currentPrice: metrics.currentPrice ?? existingMetrics?.currentPrice ?? 0,
      marketCapUsd: metrics.marketCapUsd ?? existingMetrics?.marketCapUsd ?? 0,
      totalVolume: metrics.totalVolume ?? existingMetrics?.totalVolume ?? 0,
      holders: metrics.holders ?? existingMetrics?.holders ?? 0,
      transactions: metrics.transactions ?? existingMetrics?.transactions ?? 0,
      lastUpdated: new Date().toISOString(),
      dexListingProgress: metrics.dexListingProgress ?? existingMetrics?.dexListingProgress ?? 0,
    }

    await redisService.set(key, updatedMetrics)

    // Update platform metrics
    await this.incrementPlatformMetrics("totalTransactions", metrics.transactions ? 1 : 0)
  }

  /**
   * Calculate and update metrics for a token
   */
  async calculateAndUpdateTokenMetrics(
    tokenAddress: string,
    currentSupply: number,
    solPriceUsd: number,
    dexListingThresholdUsd: number,
  ): Promise<TokenMetrics> {
    // Check if the bonding curve exists
    let curve = bondingCurveService.getCurveParams(tokenAddress)
    if (!curve) {
      // Initialize a default curve
      bondingCurveService.initializeCurve({
        tokenAddress,
        initialSupply: 0,
        maxSupply: 1000000000, // 1 billion
        solPriceUsd,
        dexListingThresholdUsd,
      })
      curve = bondingCurveService.getCurveParams(tokenAddress)
    }

    // Calculate the current price
    const currentPrice = bondingCurveService.calculatePrice(tokenAddress, currentSupply)

    // Calculate the market cap
    const marketCapUsd = currentPrice * currentSupply * solPriceUsd

    // Calculate the DEX listing progress
    const dexListingProgress = Math.min(100, (marketCapUsd / dexListingThresholdUsd) * 100)

    // Get existing metrics
    const existingMetrics = await this.getTokenMetrics(tokenAddress)

    // Update the metrics
    const metrics: TokenMetrics = {
      totalSupply: currentSupply,
      currentPrice,
      marketCapUsd,
      totalVolume: existingMetrics?.totalVolume ?? 0,
      holders: existingMetrics?.holders ?? 0,
      transactions: existingMetrics?.transactions ?? 0,
      lastUpdated: new Date().toISOString(),
      dexListingProgress,
    }

    await this.updateTokenMetrics(tokenAddress, metrics)
    return metrics
  }

  /**
   * Record a token transaction
   */
  async recordTransaction(tokenAddress: string, transaction: Omit<TokenTransaction, "id">): Promise<void> {
    const key = `${this.tokenTransactionsPrefix}${tokenAddress}`
    const txId = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    const tx: TokenTransaction = {
      ...transaction,
      id: txId,
    }

    // Push to the beginning of the list (newer transactions first)
    await redisService.redis.lpush(key, JSON.stringify(tx))

    // Trim the list to keep only the latest 100 transactions
    await redisService.redis.ltrim(key, 0, 99)

    // Update token metrics
    await this.updateTokenMetrics(tokenAddress, {
      transactions: (await this.getTokenMetrics(tokenAddress))?.transactions + 1 || 1,
      totalVolume: (await this.getTokenMetrics(tokenAddress))?.totalVolume + tx.total || tx.total,
    })

    // Update platform metrics
    await this.incrementPlatformMetrics("totalTransactions", 1)
    await this.incrementPlatformMetrics("totalVolume", tx.total)
  }

  /**
   * Get recent transactions for a token
   */
  async getTransactions(tokenAddress: string, limit = 10): Promise<TokenTransaction[]> {
    const key = `${this.tokenTransactionsPrefix}${tokenAddress}`
    const transactions = await redisService.redis.lrange(key, 0, limit - 1)
    return transactions.map((tx) => JSON.parse(tx)) as TokenTransaction[]
  }

  /**
   * Increment platform metrics
   */
  private async incrementPlatformMetrics(key: string, value: number): Promise<void> {
    if (value <= 0) return

    const platformMetrics = (await redisService.get<Record<string, number>>(this.platformMetricsKey)) || {}
    platformMetrics[key] = (platformMetrics[key] || 0) + value
    platformMetrics["lastUpdated"] = Date.now()
    await redisService.set(this.platformMetricsKey, platformMetrics)
  }

  /**
   * Get platform metrics
   */
  async getPlatformMetrics(): Promise<Record<string, number>> {
    return redisService.get<Record<string, number>>(this.platformMetricsKey) || {}
  }
}

// Export a singleton instance
const tokenMetricsService = new TokenMetricsService()
export default tokenMetricsService

"use server"

import { encrypt, decrypt } from "./crypto-utils"
import { Redis } from "@upstash/redis"
import { kv } from "@vercel/kv"

// Utiliser Vercel KV ou Upstash Redis selon la disponibilité
const getStorageClient = () => {
  try {
    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      return kv
    } else if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
      return Redis.fromEnv()
    } else {
      throw new Error("Aucun client de stockage disponible")
    }
  } catch (error) {
    console.error("Erreur d'initialisation du client de stockage:", error)
    throw new Error("Impossible d'initialiser le client de stockage")
  }
}

// Préfixe pour les clés de variables d'environnement
const ENV_KEY_PREFIX = "secure_env:"

// Interface pour les variables d'environnement
export interface SecureEnvVariable {
  key: string
  value: string
  isEncrypted: boolean
  lastUpdated: string
  updatedBy: string
  category: string
  description?: string
}

/**
 * Récupère toutes les variables d'environnement sécurisées
 */
export async function getAllSecureEnvVariables(): Promise<SecureEnvVariable[]> {
  try {
    const storage = getStorageClient()
    const keys = await storage.keys(`${ENV_KEY_PREFIX}*`)

    if (!keys || keys.length === 0) {
      return []
    }

    const variables: SecureEnvVariable[] = []

    for (const key of keys) {
      const rawData = await storage.get(key)
      if (rawData) {
        const variable = rawData as SecureEnvVariable

        // Ne pas renvoyer la valeur réelle pour les variables chiffrées
        if (variable.isEncrypted) {
          variable.value = "[VALEUR CHIFFRÉE]"
        }

        variables.push(variable)
      }
    }

    return variables
  } catch (error) {
    console.error("Erreur lors de la récupération des variables d'environnement:", error)
    throw new Error("Impossible de récupérer les variables d'environnement")
  }
}

/**
 * Récupère une variable d'environnement sécurisée par sa clé
 */
export async function getSecureEnvVariable(key: string): Promise<string | null> {
  try {
    const storage = getStorageClient()
    const variable = (await storage.get(`${ENV_KEY_PREFIX}${key}`)) as SecureEnvVariable | null

    if (!variable) {
      return null
    }

    // Déchiffrer la valeur si nécessaire
    if (variable.isEncrypted) {
      return decrypt(variable.value)
    }

    return variable.value
  } catch (error) {
    console.error(`Erreur lors de la récupération de la variable ${key}:`, error)
    return null
  }
}

/**
 * Définit une variable d'environnement sécurisée
 */
export async function setSecureEnvVariable(
  key: string,
  value: string,
  options: {
    isEncrypted?: boolean
    category?: string
    description?: string
    updatedBy: string
  },
): Promise<boolean> {
  try {
    const storage = getStorageClient()

    // Chiffrer la valeur si nécessaire
    const finalValue = options.isEncrypted ? encrypt(value) : value

    const variable: SecureEnvVariable = {
      key,
      value: finalValue,
      isEncrypted: options.isEncrypted || false,
      lastUpdated: new Date().toISOString(),
      updatedBy: options.updatedBy,
      category: options.category || "general",
      description: options.description,
    }

    await storage.set(`${ENV_KEY_PREFIX}${key}`, variable)
    return true
  } catch (error) {
    console.error(`Erreur lors de la définition de la variable ${key}:`, error)
    return false
  }
}

/**
 * Supprime une variable d'environnement sécurisée
 */
export async function deleteSecureEnvVariable(key: string): Promise<boolean> {
  try {
    const storage = getStorageClient()
    await storage.del(`${ENV_KEY_PREFIX}${key}`)
    return true
  } catch (error) {
    console.error(`Erreur lors de la suppression de la variable ${key}:`, error)
    return false
  }
}

/**
 * Récupère toutes les variables d'environnement par catégorie
 */
export async function getSecureEnvVariablesByCategory(category: string): Promise<SecureEnvVariable[]> {
  try {
    const allVariables = await getAllSecureEnvVariables()
    return allVariables.filter((variable) => variable.category === category)
  } catch (error) {
    console.error(`Erreur lors de la récupération des variables de la catégorie ${category}:`, error)
    return []
  }
}

/**
 * Initialise les variables d'environnement par défaut si elles n'existent pas
 */
export async function initializeDefaultEnvVariables(adminWallet: string): Promise<void> {
  const defaultVariables = [
    {
      key: "NEXT_PUBLIC_SOLANA_RPC_URL",
      value: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
      isEncrypted: false,
      category: "blockchain",
      description: "URL du RPC Solana pour les connexions blockchain",
    },
    {
      key: "NEXT_PUBLIC_TOKEN_PROGRAM_ID",
      value: process.env.NEXT_PUBLIC_TOKEN_PROGRAM_ID || "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
      isEncrypted: false,
      category: "blockchain",
      description: "ID du programme de token Solana",
    },
    {
      key: "COINGECKO_API_KEY",
      value: process.env.COINGECKO_API_KEY || "",
      isEncrypted: true,
      category: "api",
      description: "Clé API pour CoinGecko",
    },
    {
      key: "COINMARKETCAP_API_KEY",
      value: process.env.COINMARKETCAP_API_KEY || "",
      isEncrypted: true,
      category: "api",
      description: "Clé API pour CoinMarketCap",
    },
    {
      key: "DEEPSEEK_API_KEY",
      value:
        process.env.DEEPSEEK_API_KEY || "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",
      isEncrypted: true,
      category: "api",
      description: "Clé API pour DeepSeek AI",
    },
  ]

  for (const variable of defaultVariables) {
    const existingValue = await getSecureEnvVariable(variable.key)

    if (existingValue === null) {
      await setSecureEnvVariable(variable.key, variable.value, {
        isEncrypted: variable.isEncrypted,
        category: variable.category,
        description: variable.description,
        updatedBy: adminWallet,
      })
    }
  }
}

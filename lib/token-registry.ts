import { create } from "zustand"
import { persist } from "zustand/middleware"

export interface TokenInfo {
  id: string
  name: string
  symbol: string
  mintAddress: string
  decimals: number
  totalSupply: string
  createdAt: number
  iconUrl?: string
  price?: number
  priceChange24h?: number
  creator?: string
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  isVerified?: boolean
}

interface TokenRegistryState {
  tokens: TokenInfo[]
  addToken: (token: TokenInfo) => void
  removeToken: (mintAddress: string) => void
  updateToken: (mintAddress: string, updates: Partial<TokenInfo>) => void
  getToken: (mintAddress: string) => TokenInfo | undefined
  getAllTokens: () => TokenInfo[]
}

export const useTokenRegistry = create<TokenRegistryState>()(
  persist(
    (set, get) => ({
      tokens: [
        {
          id: "gf-beta",
          name: "GF-beta Token",
          symbol: "GF-b1",
          mintAddress: "GFbeta111111111111111111111111111111111111",
          decimals: 9,
          totalSupply: "1000000000",
          createdAt: Date.now() - 90 * 24 * 60 * 60 * 1000,
          price: 0.0125,
          priceChange24h: 2.4,
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          description: "The official token of the Global Finance Beta platform",
          website: "https://globalfinance.com",
          twitter: "https://twitter.com/globalfinance",
          telegram: "https://t.me/globalfinance",
          isVerified: true,
        },
      ],
      addToken: (token) => {
        set((state) => {
          // Check if token already exists
          const exists = state.tokens.some((t) => t.mintAddress === token.mintAddress)
          if (exists) {
            return state
          }
          return { tokens: [...state.tokens, token] }
        })
      },
      removeToken: (mintAddress) => {
        set((state) => ({
          tokens: state.tokens.filter((token) => token.mintAddress !== mintAddress),
        }))
      },
      updateToken: (mintAddress, updates) => {
        set((state) => ({
          tokens: state.tokens.map((token) => (token.mintAddress === mintAddress ? { ...token, ...updates } : token)),
        }))
      },
      getToken: (mintAddress) => {
        return get().tokens.find((token) => token.mintAddress === mintAddress)
      },
      getAllTokens: () => {
        return get().tokens
      },
    }),
    {
      name: "token-registry",
    },
  ),
)

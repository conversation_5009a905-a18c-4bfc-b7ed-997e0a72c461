import { Connection } from "@solana/web3.js"
import { envConfig } from "./env-config"

export interface TransactionLimits {
  maxTxPerBlock: number
  maxWalletPercentage: number
  cooldownPeriod: number // en secondes
}

export interface BlacklistEntry {
  address: string
  reason: string
  timestamp: number
  canAppeal: boolean
}

class TokenSecurityService {
  private connection: Connection
  private blacklist: Map<string, BlacklistEntry>
  private transactionCounts: Map<string, number>
  private lastTransactionTime: Map<string, number>
  private transactionLimits: TransactionLimits

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
    this.blacklist = new Map()
    this.transactionCounts = new Map()
    this.lastTransactionTime = new Map()

    // Configuration par défaut des limites de transaction
    this.transactionLimits = {
      maxTxPerBlock: 3,
      maxWalletPercentage: 3, // 3% de l'offre totale
      cooldownPeriod: 2, // 2 secondes
    }

    // Charger la blacklist depuis une source persistante (simulé)
    this.loadBlacklist()

    // Réinitialiser les compteurs de transaction périodiquement
    setInterval(() => this.resetTransactionCounts(), 60000) // toutes les minutes
  }

  /**
   * Vérifie si une adresse est blacklistée
   */
  isBlacklisted(address: string): boolean {
    return this.blacklist.has(address)
  }

  /**
   * Récupère les informations de blacklist pour une adresse
   */
  getBlacklistInfo(address: string): BlacklistEntry | null {
    return this.blacklist.get(address) || null
  }

  /**
   * Ajoute une adresse à la blacklist
   */
  addToBlacklist(address: string, reason: string, canAppeal = true): boolean {
    if (this.isBlacklisted(address)) {
      return false
    }

    this.blacklist.set(address, {
      address,
      reason,
      timestamp: Date.now(),
      canAppeal,
    })

    // Sauvegarder la blacklist (simulé)
    this.saveBlacklist()

    return true
  }

  /**
   * Retire une adresse de la blacklist
   */
  removeFromBlacklist(address: string): boolean {
    if (!this.isBlacklisted(address)) {
      return false
    }

    this.blacklist.delete(address)

    // Sauvegarder la blacklist (simulé)
    this.saveBlacklist()

    return true
  }

  /**
   * Vérifie si une transaction respecte les limites
   */
  async checkTransactionLimits(
    tokenAddress: string,
    senderAddress: string,
    amount: number,
    totalSupply: number,
  ): Promise<{
    allowed: boolean
    reason?: string
  }> {
    try {
      // Vérifier si l'adresse est blacklistée
      if (this.isBlacklisted(senderAddress)) {
        return {
          allowed: false,
          reason: "Address is blacklisted",
        }
      }

      // Vérifier le nombre de transactions par bloc
      const txCount = this.transactionCounts.get(senderAddress) || 0
      if (txCount >= this.transactionLimits.maxTxPerBlock) {
        return {
          allowed: false,
          reason: `Exceeded maximum transactions per block (${this.transactionLimits.maxTxPerBlock})`,
        }
      }

      // Vérifier le cooldown
      const lastTxTime = this.lastTransactionTime.get(senderAddress) || 0
      const currentTime = Date.now()
      const timeSinceLastTx = (currentTime - lastTxTime) / 1000 // en secondes

      if (timeSinceLastTx < this.transactionLimits.cooldownPeriod) {
        return {
          allowed: false,
          reason: `Transaction cooldown period not elapsed (${Math.ceil(this.transactionLimits.cooldownPeriod - timeSinceLastTx)} seconds remaining)`,
        }
      }

      // Vérifier le pourcentage maximal par wallet
      const maxAmount = totalSupply * (this.transactionLimits.maxWalletPercentage / 100)
      if (amount > maxAmount) {
        return {
          allowed: false,
          reason: `Transaction amount exceeds maximum wallet percentage (${this.transactionLimits.maxWalletPercentage}% of total supply)`,
        }
      }

      // Incrémenter le compteur de transactions
      this.transactionCounts.set(senderAddress, txCount + 1)

      // Mettre à jour le timestamp de la dernière transaction
      this.lastTransactionTime.set(senderAddress, currentTime)

      return {
        allowed: true,
      }
    } catch (error: any) {
      console.error("Error checking transaction limits:", error)
      return {
        allowed: false,
        reason: error.message || "An error occurred while checking transaction limits",
      }
    }
  }

  /**
   * Réinitialise les compteurs de transaction
   */
  private resetTransactionCounts(): void {
    this.transactionCounts.clear()
  }

  /**
   * Charge la blacklist depuis une source persistante (simulé)
   */
  private loadBlacklist(): void {
    // Dans une implémentation réelle, charger depuis une base de données ou un fichier
    console.log("Loading blacklist...")

    // Simuler quelques entrées de blacklist
    this.blacklist.set("BlacklistedAddress1", {
      address: "BlacklistedAddress1",
      reason: "Suspicious activity",
      timestamp: Date.now() - 86400000, // 1 jour avant
      canAppeal: true,
    })

    this.blacklist.set("BlacklistedAddress2", {
      address: "BlacklistedAddress2",
      reason: "Mixer interaction",
      timestamp: Date.now() - 172800000, // 2 jours avant
      canAppeal: false,
    })
  }

  /**
   * Sauvegarde la blacklist dans une source persistante (simulé)
   */
  private saveBlacklist(): void {
    // Dans une implémentation réelle, sauvegarder dans une base de données ou un fichier
    console.log("Saving blacklist...")
    console.log(`Current blacklist size: ${this.blacklist.size} entries`)
  }

  /**
   * Configure les limites de transaction
   */
  setTransactionLimits(limits: Partial<TransactionLimits>): void {
    this.transactionLimits = { ...this.transactionLimits, ...limits }
  }

  /**
   * Récupère les limites de transaction actuelles
   */
  getTransactionLimits(): TransactionLimits {
    return { ...this.transactionLimits }
  }

  /**
   * Vérifie si une transaction est une attaque sandwich
   */
  isSandwichAttack(recentBlockhash: string, txData: any): boolean {
    // Dans une implémentation réelle, analyser les transactions récentes
    // et détecter les patterns d'attaque sandwich

    // Pour cette démo, retourner toujours false
    return false
  }
}

// Exporter une instance singleton du service
const tokenSecurityService = new TokenSecurityService()
export default tokenSecurityService

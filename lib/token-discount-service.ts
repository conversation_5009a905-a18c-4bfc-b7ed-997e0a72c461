import type { TokenCreationFee, DiscountTier } from "@/types/token-types"

// Types
export type DiscountCalculationResult = {
  baseFee: number
  volumeDiscount: number
  referralDiscount: number
  finalFee: number
  currency: string
  appliedTier?: DiscountTier
}

export class TokenDiscountService {
  private static instance: TokenDiscountService
  private discountTiers: DiscountTier[] = []
  private tokenFees: TokenCreationFee[] = []
  private referralDiscountPercentage = 5
  private bulkDiscountEnabled = true
  private referralDiscountEnabled = true

  private constructor() {
    // Initialisation avec des valeurs par défaut
    this.discountTiers = [
      { id: 1, name: 'Bronze', threshold: 1, discountPercentage: 5, active: true },
      { id: 2, name: 'Silver', threshold: 5, discountPercentage: 10, active: true },
      { id: 3, name: 'Gold', threshold: 10, discountPercentage: 15, active: true },
      { id: 4, name: 'Platinum', threshold: 25, discountPercentage: 25, active: true },
      { id: 5, name: 'Diamond', threshold: 50, discountPercentage: 40, active: true },
    ]
    
    this.tokenFees = [
      { network: 'Solana', baseFee: 0.05, currency: 'SOL' },
      { network: 'BNB Chain', baseFee: 0.01, currency: 'BNB' },
    ]
  }

  public static getInstance(): TokenDiscountService {
    if (!TokenDiscountService.instance) {
      TokenDiscountService.instance = new TokenDiscountService()
    }
    return TokenDiscountService.instance
  }

  // Getters
  public getDiscountTiers(): DiscountTier[] {
    return this.discountTiers
  }

  public getTokenFees(): TokenCreationFee[] {
    return this.tokenFees
  }

  public getReferralDiscountPercentage(): number {
    return this.referralDiscountPercentage
  }

  public isBulkDiscountEnabled(): boolean {
    return this.bulkDiscountEnabled
  }

  public isReferralDiscountEnabled(): boolean {
    return this.referralDiscountEnabled
  }

  // Setters
  public setDiscountTiers(tiers: DiscountTier[]): void {
    this.discountTiers = tiers
  }

  public setTokenFees(fees: TokenCreationFee[]): void {
    this.tokenFees = fees
  }

  public setReferralDiscountPercentage(percentage: number): void {
    this.referralDiscountPercentage = percentage
  }

  public setBulkDiscountEnabled(enabled: boolean): void {
    this.bulkDiscountEnabled = enabled
  }

  public setReferralDiscountEnabled(enabled: boolean): void {
    this.referralDiscountEnabled = enabled
  }

  // Méthodes de gestion des niveaux de remise
  public addDiscountTier(tier: Omit<DiscountTier, 'id'>): DiscountTier {
    const newId = Math.max(...this.discountTiers.map(t => t.id), 0) + 1
    const newTier = { ...tier, id: newId }
    this.discountTiers.push(newTier)
    return newTier
  }

  public updateDiscountTier(id: number, updates: Partial<DiscountTier>): boolean {
    const index = this.discountTiers.findIndex(tier => tier.id === id)
    if (index === -1) return false
    
    this.discountTiers[index] = { ...this.discountTiers[index], ...updates }
    return true
  }

  public deleteDiscountTier(id: number): boolean {
    const initialLength = this.discountTiers.length
    this.discountTiers = this.discountTiers.filter(tier => tier.id !== id)
    return this.discountTiers.length
\
## 8. API Route pour les Remises de Tokens

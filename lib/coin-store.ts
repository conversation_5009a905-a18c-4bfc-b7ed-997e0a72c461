import { create } from "zustand"
import { persist } from "zustand/middleware"

export interface Coin {
  id: string
  name: string
  ticker: string
  description: string
  imageUrl: string
  tokenAddress: string
  creator: string
  createdAt: number
  marketCap: number
  price: number
  supply: number
  holders: number
  replies: number
  isVerified: boolean
}

interface CoinStore {
  coins: Coin[]
  addCoin: (coin: Coin) => void
  updateCoin: (id: string, updates: Partial<Coin>) => void
  removeCoin: (id: string) => void
}

export const useCoinStore = create<CoinStore>()(
  persist(
    (set) => ({
      coins: [
        {
          id: "1",
          name: "Saratoga Spring Water",
          ticker: "SARATOGA",
          description: "The official sparkling water for your trading routine.",
          imageUrl: "/placeholder.svg?height=200&width=400",
          tokenAddress: "SaratogaSpringWater123456789abcdef",
          creator: "8xyt45jkLmn9PQvDTbW23jkLmNpQrStUv1234567890",
          createdAt: Date.now() - 86400000 * 3,
          marketCap: 3750000,
          price: 0.0000375,
          supply: 100000000,
          holders: 137,
          replies: 24,
          isVerified: true,
        },
        {
          id: "2",
          name: "Pyramids",
          ticker: "GIZA",
          description: "Underground city found beneath Giza Pyramids.",
          imageUrl: "/placeholder.svg?height=200&width=400",
          tokenAddress: "PyramidsGiza123456789abcdef",
          creator: "9abcDeFgHiJkLmNoPqRsTuVwXyZ1234567890",
          createdAt: Date.now() - 86400000 * 5,
          marketCap: 560000,
          price: 0.0000056,
          supply: 100000000,
          holders: 84,
          replies: 12,
          isVerified: false,
        },
        {
          id: "3",
          name: "Italian Beef",
          ticker: "ITALIAN",
          description: "This is trending again for some reason.",
          imageUrl: "/placeholder.svg?height=200&width=400",
          tokenAddress: "ItalianBeef123456789abcdef",
          creator: "7ZyXwVuTsRqPoNmLkJiHgFeDcBa1234567890",
          createdAt: Date.now() - 86400000 * 2,
          marketCap: 890000,
          price: 0.0000089,
          supply: 100000000,
          holders: 56,
          replies: 8,
          isVerified: false,
        },
        {
          id: "4",
          name: "Morning Routine",
          ticker: "ROUTINE",
          description: "Morning Routine Goes Viral.",
          imageUrl: "/placeholder.svg?height=200&width=400",
          tokenAddress: "MorningRoutine123456789abcdef",
          creator: "5MnBvCxZyLkJiHgFeDcBa9876543210",
          createdAt: Date.now() - 86400000 * 1,
          marketCap: 210000,
          price: 0.0000021,
          supply: 100000000,
          holders: 42,
          replies: 5,
          isVerified: false,
        },
        {
          id: "5",
          name: "Bitcoin",
          ticker: "HTCOIN",
          description: "It's Like Bitcoin But Backed by Tangible Assets.",
          imageUrl: "/placeholder.svg?height=200&width=400",
          tokenAddress: "HTCoin123456789abcdef",
          creator: "3KjIhGfEdCbA9876ZyXwVuTsRqPoN543210",
          createdAt: Date.now() - 86400000 * 7,
          marketCap: 2180000,
          price: 0.0000218,
          supply: 100000000,
          holders: 95,
          replies: 17,
          isVerified: true,
        },
        {
          id: "6",
          name: "FAT MOGE",
          ticker: "FAT",
          description: "The Trenches Dub Meme Season.",
          imageUrl: "/placeholder.svg?height=200&width=400",
          tokenAddress: "FatMoge123456789abcdef",
          creator: "2FatMogeCreator9876ZyXwVuTsRqPoN543210",
          createdAt: Date.now() - 86400000 * 4,
          marketCap: 3300000,
          price: 0.000033,
          supply: 100000000,
          holders: 128,
          replies: 31,
          isVerified: false,
        },
      ],
      addCoin: (coin) => set((state) => ({ coins: [...state.coins, coin] })),
      updateCoin: (id, updates) =>
        set((state) => ({
          coins: state.coins.map((coin) => (coin.id === id ? { ...coin, ...updates } : coin)),
        })),
      removeCoin: (id) =>
        set((state) => ({
          coins: state.coins.filter((coin) => coin.id !== id),
        })),
    }),
    {
      name: "coin-store",
    },
  ),
)

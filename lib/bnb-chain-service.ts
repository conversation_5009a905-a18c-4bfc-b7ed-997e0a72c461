import Web3 from "web3"
import type { AbiItem } from "web3-utils"
import { getBnbTokenFactoryAddress, getNetworkById } from "./network-config"

// BEP20 Token Factory ABI (simplified for this example)
const tokenFactoryABI: AbiItem[] = [
  {
    inputs: [
      { internalType: "string", name: "name", type: "string" },
      { internalType: "string", name: "symbol", type: "string" },
      { internalType: "uint8", name: "decimals", type: "uint8" },
      { internalType: "uint256", name: "initialSupply", type: "uint256" },
      { internalType: "address", name: "owner", type: "address" },
    ],
    name: "createToken",
    outputs: [{ internalType: "address", name: "", type: "address" }],
    stateMutability: "payable",
    type: "function",
  },
  {
    inputs: [
      { internalType: "address", name: "tokenAddress", type: "address" },
      { internalType: "address", name: "router<PERSON>ddress", type: "address" },
      { internalType: "uint256", name: "bnb<PERSON><PERSON>", type: "uint256" },
      { internalType: "uint256", name: "tokenAmount", type: "uint256" },
      { internalType: "uint256", name: "lockTime", type: "uint256" },
    ],
    name: "addLiquidityAndLock",
    outputs: [{ internalType: "address", name: "", type: "address" }],
    stateMutability: "payable",
    type: "function",
  },
  {
    inputs: [{ internalType: "address", name: "tokenAddress", type: "address" }],
    name: "getTokenInfo",
    outputs: [
      { internalType: "string", name: "name", type: "string" },
      { internalType: "string", name: "symbol", type: "string" },
      { internalType: "uint8", name: "decimals", type: "uint8" },
      { internalType: "uint256", name: "totalSupply", type: "uint256" },
      { internalType: "address", name: "owner", type: "address" },
      { internalType: "uint256", name: "createdAt", type: "uint256" },
    ],
    stateMutability: "view",
    type: "function",
  },
]

// BEP20 Token ABI (simplified)
const tokenABI: AbiItem[] = [
  {
    constant: true,
    inputs: [],
    name: "name",
    outputs: [{ name: "", type: "string" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: true,
    inputs: [],
    name: "symbol",
    outputs: [{ name: "", type: "string" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: true,
    inputs: [],
    name: "decimals",
    outputs: [{ name: "", type: "uint8" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: true,
    inputs: [],
    name: "totalSupply",
    outputs: [{ name: "", type: "uint256" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  {
    constant: true,
    inputs: [{ name: "owner", type: "address" }],
    name: "balanceOf",
    outputs: [{ name: "", type: "uint256" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
]

// Interface for token creation parameters
export interface BnbTokenCreationParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: string
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  networkId: string
}

// Interface for liquidity addition parameters
export interface AddLiquidityParams {
  tokenAddress: string
  bnbAmount: string
  tokenAmount: string
  lockTime: number // in seconds
  networkId: string
}

// Interface for token information
export interface BnbTokenInfo {
  address: string
  name: string
  symbol: string
  decimals: number
  totalSupply: string
  owner: string
  createdAt: Date
  balance?: string
}

class BnbChainService {
  private getWeb3(networkId: string) {
    const network = getNetworkById(networkId)
    if (!network || network.type !== "bnb") {
      throw new Error(`Invalid BNB network ID: ${networkId}`)
    }
    return new Web3(network.rpcUrl)
  }

  private getTokenFactoryContract(web3: Web3) {
    const factoryAddress = getBnbTokenFactoryAddress()
    return new web3.eth.Contract(tokenFactoryABI, factoryAddress)
  }

  private getTokenContract(web3: Web3, tokenAddress: string) {
    return new web3.eth.Contract(tokenABI, tokenAddress)
  }

  /**
   * Create a new BEP20 token on BNB Chain
   */
  async createToken(params: BnbTokenCreationParams, account: string): Promise<string> {
    try {
      const web3 = this.getWeb3(params.networkId)
      const factory = this.getTokenFactoryContract(web3)

      // Calculate the initial supply with decimals
      const initialSupplyWithDecimals = web3.utils
        .toBN(params.initialSupply)
        .mul(web3.utils.toBN(10).pow(web3.utils.toBN(params.decimals)))

      // Estimate gas for the transaction
      const gasEstimate = await factory.methods
        .createToken(params.name, params.symbol, params.decimals, initialSupplyWithDecimals, account)
        .estimateGas({ from: account, value: web3.utils.toWei("0.1", "ether") }) // Fee for token creation

      // Create the token
      const result = await factory.methods
        .createToken(params.name, params.symbol, params.decimals, initialSupplyWithDecimals, account)
        .send({
          from: account,
          gas: Math.floor(gasEstimate * 1.2), // Add 20% buffer
          value: web3.utils.toWei("0.1", "ether"), // Fee for token creation
        })

      // Return the token address
      return result.events.TokenCreated.returnValues.tokenAddress
    } catch (error) {
      console.error("Error creating BNB token:", error)
      throw error
    }
  }

  /**
   * Add liquidity to a token and lock it
   */
  async addLiquidityAndLock(params: AddLiquidityParams, account: string): Promise<string> {
    try {
      const web3 = this.getWeb3(params.networkId)
      const factory = this.getTokenFactoryContract(web3)

      // Convert amounts to wei
      const bnbAmountWei = web3.utils.toWei(params.bnbAmount, "ether")
      const tokenAmountWei = params.tokenAmount // Assuming this is already in the correct format with decimals

      // PancakeSwap router address (testnet)
      const routerAddress = "******************************************" // Replace with the correct router address

      // Estimate gas for the transaction
      const gasEstimate = await factory.methods
        .addLiquidityAndLock(params.tokenAddress, routerAddress, bnbAmountWei, tokenAmountWei, params.lockTime)
        .estimateGas({ from: account, value: bnbAmountWei })

      // Add liquidity and lock
      const result = await factory.methods
        .addLiquidityAndLock(params.tokenAddress, routerAddress, bnbAmountWei, tokenAmountWei, params.lockTime)
        .send({
          from: account,
          gas: Math.floor(gasEstimate * 1.2), // Add 20% buffer
          value: bnbAmountWei,
        })

      // Return the liquidity pair address
      return result.events.LiquidityAdded.returnValues.pairAddress
    } catch (error) {
      console.error("Error adding liquidity:", error)
      throw error
    }
  }

  /**
   * Get token information
   */
  async getTokenInfo(tokenAddress: string, networkId: string): Promise<BnbTokenInfo> {
    try {
      const web3 = this.getWeb3(networkId)
      const token = this.getTokenContract(web3, tokenAddress)

      // Get token information
      const [name, symbol, decimals, totalSupply] = await Promise.all([
        token.methods.name().call(),
        token.methods.symbol().call(),
        token.methods.decimals().call(),
        token.methods.totalSupply().call(),
      ])

      // Get token creation information from factory
      const factory = this.getTokenFactoryContract(web3)
      const tokenInfo = await factory.methods.getTokenInfo(tokenAddress).call()

      return {
        address: tokenAddress,
        name,
        symbol,
        decimals: Number.parseInt(decimals),
        totalSupply,
        owner: tokenInfo.owner,
        createdAt: new Date(Number.parseInt(tokenInfo.createdAt) * 1000),
      }
    } catch (error) {
      console.error("Error getting token info:", error)
      throw error
    }
  }

  /**
   * Get token balance for an account
   */
  async getTokenBalance(tokenAddress: string, account: string, networkId: string): Promise<string> {
    try {
      const web3 = this.getWeb3(networkId)
      const token = this.getTokenContract(web3, tokenAddress)

      const balance = await token.methods.balanceOf(account).call()
      return balance
    } catch (error) {
      console.error("Error getting token balance:", error)
      throw error
    }
  }

  /**
   * Get BNB balance for an account
   */
  async getBnbBalance(account: string, networkId: string): Promise<string> {
    try {
      const web3 = this.getWeb3(networkId)
      const balance = await web3.eth.getBalance(account)
      return web3.utils.fromWei(balance, "ether")
    } catch (error) {
      console.error("Error getting BNB balance:", error)
      throw error
    }
  }

  /**
   * Track developer token sales
   * This would monitor token transfers from the developer wallet
   */
  async trackDeveloperSales(tokenAddress: string, developerAddress: string, networkId: string): Promise<any> {
    // In a real implementation, you would set up event listeners or query past events
    // For this example, we'll return a simulated response
    return {
      totalSold: "1000000",
      percentageOfSupply: "10",
      lastSale: new Date(),
      salesHistory: [
        { amount: "500000", timestamp: new Date(Date.now() - ********) },
        { amount: "500000", timestamp: new Date() },
      ],
    }
  }
}

export default new BnbChainService()

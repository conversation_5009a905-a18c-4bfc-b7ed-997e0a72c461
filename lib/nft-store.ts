import { create } from "zustand"
import { persist } from "zustand/middleware"

export interface NFTAttribute {
  trait_type: string
  value: string
}

export interface NFT {
  id: string
  name: string
  description: string
  image: string
  mintAddress: string
  creator: string
  owner: string
  price?: number
  forSale: boolean
  createdAt: number
  attributes: NFTAttribute[]
  collection?: string
}

interface NFTStore {
  nfts: NFT[]
  addNFT: (nft: NFT) => void
  updateNFT: (id: string, updates: Partial<NFT>) => void
  removeNFT: (id: string) => void
  getNFT: (id: string) => NFT | undefined
  getAllNFTs: () => NFT[]
  getNFTsByOwner: (owner: string) => NFT[]
  getNFTsForSale: () => NFT[]
}

export const useNFTStore = create<NFTStore>()(
  persist(
    (set, get) => ({
      nfts: [
        {
          id: "1",
          name: "Golden Finance #001",
          description: "The first NFT in the Golden Finance collection",
          image: "/placeholder.svg?height=300&width=300",
          mintAddress: "GoldenFinance001123456789abcdef",
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          owner: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          price: 10,
          forSale: true,
          createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000,
          attributes: [
            { trait_type: "Rarity", value: "Legendary" },
            { trait_type: "Type", value: "Art" },
            { trait_type: "Edition", value: "1 of 1" },
          ],
          collection: "Golden Finance",
        },
        {
          id: "2",
          name: "Golden Finance #002",
          description: "The second NFT in the Golden Finance collection",
          image: "/placeholder.svg?height=300&width=300",
          mintAddress: "GoldenFinance002123456789abcdef",
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          owner: "9abcDeFgHiJkLmNoPqRsTuVwXyZ1234567890",
          price: 8.5,
          forSale: true,
          createdAt: Date.now() - 28 * 24 * 60 * 60 * 1000,
          attributes: [
            { trait_type: "Rarity", value: "Epic" },
            { trait_type: "Type", value: "Art" },
            { trait_type: "Edition", value: "1 of 1" },
          ],
          collection: "Golden Finance",
        },
        {
          id: "3",
          name: "Golden Finance #003",
          description: "The third NFT in the Golden Finance collection",
          image: "/placeholder.svg?height=300&width=300",
          mintAddress: "GoldenFinance003123456789abcdef",
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          owner: "7ZyXwVuTsRqPoNmLkJiHgFeDcBa1234567890",
          price: 7.2,
          forSale: true,
          createdAt: Date.now() - 25 * 24 * 60 * 60 * 1000,
          attributes: [
            { trait_type: "Rarity", value: "Rare" },
            { trait_type: "Type", value: "Art" },
            { trait_type: "Edition", value: "1 of 1" },
          ],
          collection: "Golden Finance",
        },
        {
          id: "4",
          name: "Golden Finance #004",
          description: "The fourth NFT in the Golden Finance collection",
          image: "/placeholder.svg?height=300&width=300",
          mintAddress: "GoldenFinance004123456789abcdef",
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          owner: "5MnBvCxZyLkJiHgFeDcBa9876543210",
          price: 6.8,
          forSale: true,
          createdAt: Date.now() - 22 * 24 * 60 * 60 * 1000,
          attributes: [
            { trait_type: "Rarity", value: "Uncommon" },
            { trait_type: "Type", value: "Art" },
            { trait_type: "Edition", value: "1 of 1" },
          ],
          collection: "Golden Finance",
        },
        {
          id: "5",
          name: "Golden Finance #005",
          description: "The fifth NFT in the Golden Finance collection",
          image: "/placeholder.svg?height=300&width=300",
          mintAddress: "GoldenFinance005123456789abcdef",
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          owner: "3KjIhGfEdCbA9876ZyXwVuTsRqPoN543210",
          price: 5.5,
          forSale: true,
          createdAt: Date.now() - 20 * 24 * 60 * 60 * 1000,
          attributes: [
            { trait_type: "Rarity", value: "Common" },
            { trait_type: "Type", value: "Art" },
            { trait_type: "Edition", value: "1 of 1" },
          ],
          collection: "Golden Finance",
        },
      ],
      addNFT: (nft) => set((state) => ({ nfts: [...state.nfts, nft] })),
      updateNFT: (id, updates) =>
        set((state) => ({
          nfts: state.nfts.map((nft) => (nft.id === id ? { ...nft, ...updates } : nft)),
        })),
      removeNFT: (id) =>
        set((state) => ({
          nfts: state.nfts.filter((nft) => nft.id !== id),
        })),
      getNFT: (id) => {
        return get().nfts.find((nft) => nft.id === id)
      },
      getAllNFTs: () => {
        return get().nfts
      },
      getNFTsByOwner: (owner) => {
        return get().nfts.filter((nft) => nft.owner === owner)
      },
      getNFTsForSale: () => {
        return get().nfts.filter((nft) => nft.forSale)
      },
    }),
    {
      name: "nft-store",
    },
  ),
)

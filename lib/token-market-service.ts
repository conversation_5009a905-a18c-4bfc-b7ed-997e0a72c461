import { Connection, PublicKey } from "@solana/web3.js"
import { envConfig } from "./env-config"

export interface TokenMarketData {
  address: string
  name: string
  symbol: string
  price: number
  priceChange24h: number
  marketCap: number
  volume24h: number
  holders: number
  transactions24h: number
  liquidity: number
  fullyDiluted: number
  circulatingSupply: number
  totalSupply: number
  rank?: number
  lastUpdated: string
}

export interface TokenHolderData {
  address: string
  balance: number
  percentage: number
  value: number
  lastTransaction?: string
}

class TokenMarketService {
  private connection: Connection

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
  }

  /**
   * Récupère les données de marché pour un token
   */
  async getTokenMarketData(tokenAddress: string): Promise<TokenMarketData | null> {
    try {
      console.log(`Récupération des données de marché pour ${tokenAddress}...`)

      // Dans une implémentation réelle, nous interrogerions des APIs comme CoinGecko, CoinMarketCap,
      // ou notre propre base de données pour obtenir ces informations
      // Pour cette simulation, nous allons générer des données aléatoires

      // Récupérer les informations de base du token depuis la blockchain
      const mintPublicKey = new PublicKey(tokenAddress)
      const tokenInfo = await this.connection.getParsedAccountInfo(mintPublicKey)

      if (!tokenInfo.value) {
        console.error(`Token ${tokenAddress} non trouvé sur la blockchain`)
        return null
      }

      // Générer des données de marché simulées
      const price = Math.random() * 0.1
      const totalSupply = ********** // 1 milliard
      const circulatingSupply = totalSupply * 0.7 // 70% en circulation
      const marketCap = price * circulatingSupply
      const volume24h = marketCap * 0.2 // 20% du market cap

      return {
        address: tokenAddress,
        name: `Token ${tokenAddress.substring(0, 4)}`,
        symbol: `TKN${tokenAddress.substring(0, 2).toUpperCase()}`,
        price,
        priceChange24h: Math.random() * 20 - 10, // Entre -10% et +10%
        marketCap,
        volume24h,
        holders: Math.floor(Math.random() * 1000) + 100,
        transactions24h: Math.floor(Math.random() * 500) + 50,
        liquidity: marketCap * 0.1, // 10% du market cap
        fullyDiluted: price * totalSupply,
        circulatingSupply,
        totalSupply,
        lastUpdated: new Date().toISOString(),
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des données de marché:", error)
      return null
    }
  }

  /**
   * Récupère les principaux détenteurs d'un token
   */
  async getTokenHolders(tokenAddress: string, limit = 10): Promise<TokenHolderData[]> {
    try {
      console.log(`Récupération des détenteurs pour ${tokenAddress}...`)

      // Dans une implémentation réelle, nous interrogerions la blockchain pour trouver tous les comptes
      // qui détiennent ce token, puis nous les trierions par solde
      // Pour cette simulation, nous allons générer des données aléatoires

      const holders: TokenHolderData[] = []
      const tokenMarketData = await this.getTokenMarketData(tokenAddress)

      if (!tokenMarketData) {
        return []
      }

      // Générer des détenteurs simulés
      for (let i = 0; i < limit; i++) {
        const percentage = i === 0 ? 15 : 10 / (i + 1)
        const balance = (tokenMarketData.totalSupply * percentage) / 100
        const value = balance * tokenMarketData.price

        holders.push({
          address: `${i === 0 ? "Owner" : "Holder"}_${Math.random().toString(36).substring(2, 8)}`,
          balance,
          percentage,
          value,
          lastTransaction: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(), // Dans les 7 derniers jours
        })
      }

      return holders
    } catch (error) {
      console.error("Erreur lors de la récupération des détenteurs:", error)
      return []
    }
  }

  /**
   * Calcule la capitalisation boursière actuelle d'un token
   */
  async calculateMarketCap(tokenAddress: string): Promise<number> {
    try {
      const marketData = await this.getTokenMarketData(tokenAddress)
      return marketData?.marketCap || 0
    } catch (error) {
      console.error("Erreur lors du calcul de la capitalisation boursière:", error)
      return 0
    }
  }

  /**
   * Vérifie si un token a atteint le seuil de capitalisation boursière pour être listé sur un DEX
   */
  async checkMarketCapThreshold(tokenAddress: string, threshold: number): Promise<boolean> {
    try {
      const marketCap = await this.calculateMarketCap(tokenAddress)
      return marketCap >= threshold
    } catch (error) {
      console.error("Erreur lors de la vérification du seuil de capitalisation boursière:", error)
      return false
    }
  }

  /**
   * Récupère l'historique des prix d'un token
   */
  async getTokenPriceHistory(
    tokenAddress: string,
    period: "24h" | "7d" | "30d" | "90d" | "1y" | "all" = "7d",
  ): Promise<{ timestamp: number; price: number }[]> {
    try {
      console.log(`Récupération de l'historique des prix pour ${tokenAddress} sur ${period}...`)

      // Dans une implémentation réelle, nous interrogerions des APIs comme CoinGecko
      // ou notre propre base de données pour obtenir ces informations
      // Pour cette simulation, nous allons générer des données aléatoires

      const marketData = await this.getTokenMarketData(tokenAddress)
      if (!marketData) {
        return []
      }

      const currentPrice = marketData.price
      const priceHistory: { timestamp: number; price: number }[] = []

      // Déterminer le nombre de points de données et l'intervalle en fonction de la période
      let dataPoints: number
      let interval: number // en millisecondes

      switch (period) {
        case "24h":
          dataPoints = 24
          interval = 60 * 60 * 1000 // 1 heure
          break
        case "7d":
          dataPoints = 7 * 24
          interval = 60 * 60 * 1000 // 1 heure
          break
        case "30d":
          dataPoints = 30
          interval = 24 * 60 * 60 * 1000 // 1 jour
          break
        case "90d":
          dataPoints = 90
          interval = 24 * 60 * 60 * 1000 // 1 jour
          break
        case "1y":
          dataPoints = 52
          interval = 7 * 24 * 60 * 60 * 1000 // 1 semaine
          break
        case "all":
          dataPoints = 104
          interval = 7 * 24 * 60 * 60 * 1000 // 1 semaine
          break
        default:
          dataPoints = 7 * 24
          interval = 60 * 60 * 1000 // 1 heure
      }

      // Générer des données historiques simulées
      const now = Date.now()
      let price = currentPrice

      for (let i = dataPoints - 1; i >= 0; i--) {
        const timestamp = now - i * interval

        // Ajouter une variation aléatoire au prix
        const change = Math.random() * 0.1 - 0.05 // Entre -5% et +5%
        price = price * (1 + change)

        // S'assurer que le prix ne devient pas négatif
        price = Math.max(0.000001, price)

        priceHistory.push({
          timestamp,
          price,
        })
      }

      return priceHistory
    } catch (error) {
      console.error("Erreur lors de la récupération de l'historique des prix:", error)
      return []
    }
  }

  /**
   * Récupère les transactions récentes d'un token
   */
  async getTokenTransactions(
    tokenAddress: string,
    limit = 10,
  ): Promise<
    {
      signature: string
      type: "buy" | "sell" | "transfer"
      from: string
      to: string
      amount: number
      value: number
      timestamp: number
    }[]
  > {
    try {
      console.log(`Récupération des transactions pour ${tokenAddress}...`)

      // Dans une implémentation réelle, nous interrogerions la blockchain pour trouver toutes les transactions
      // impliquant ce token, puis nous les trierions par date
      // Pour cette simulation, nous allons générer des données aléatoires

      const marketData = await this.getTokenMarketData(tokenAddress)
      if (!marketData) {
        return []
      }

      const transactions = []

      for (let i = 0; i < limit; i++) {
        const type = ["buy", "sell", "transfer"][Math.floor(Math.random() * 3)] as "buy" | "sell" | "transfer"
        const amount = Math.random() * 100000
        const value = amount * marketData.price

        transactions.push({
          signature: `tx_${Math.random().toString(36).substring(2, 10)}`,
          type,
          from: `wallet_${Math.random().toString(36).substring(2, 8)}`,
          to: `wallet_${Math.random().toString(36).substring(2, 8)}`,
          amount,
          value,
          timestamp: Date.now() - Math.random() * 86400000, // Dans les dernières 24 heures
        })
      }

      // Trier par timestamp décroissant (plus récent d'abord)
      return transactions.sort((a, b) => b.timestamp - a.timestamp)
    } catch (error) {
      console.error("Erreur lors de la récupération des transactions:", error)
      return []
    }
  }

  /**
   * Surveille la capitalisation boursière d'un token et déclenche une action lorsqu'un seuil est atteint
   */
  async monitorMarketCap(
    tokenAddress: string,
    threshold: number,
    callback: (marketCap: number) => Promise<void>,
  ): Promise<void> {
    try {
      console.log(`Surveillance de la capitalisation boursière pour ${tokenAddress}...`)

      // Vérifier la capitalisation boursière actuelle
      const marketCap = await this.calculateMarketCap(tokenAddress)

      // Si le seuil est atteint, déclencher le callback
      if (marketCap >= threshold) {
        await callback(marketCap)
      }
    } catch (error) {
      console.error("Erreur lors de la surveillance de la capitalisation boursière:", error)
    }
  }
}

// Exporter une instance singleton du service
const tokenMarketService = new TokenMarketService()
export default tokenMarketService

import { setupStaking } from "@/lib/solana-service"
import { type Keypair, PublicKey } from "@solana/web3.js"

// Interface pour les données d'un pool de staking
export interface StakingPool {
  id: string
  tokenMint: string
  tokenName: string
  tokenSymbol: string
  apr: number
  lockPeriod: number // en jours
  totalStaked: number
  stakerCount: number
  createdAt: number
}

// Interface pour les données de staking d'un utilisateur
export interface UserStaking {
  poolId: string
  tokenMint: string
  amount: number
  startTime: number
  endTime: number
  rewards: number
  claimed: boolean
}

// Créer un pool de staking
export const createStakingPool = async (
  payer: Keypair,
  tokenMint: string,
  tokenName: string,
  tokenSymbol: string,
  apr: number,
  lockPeriod: number,
): Promise<StakingPool> => {
  try {
    // Dans une implémentation réelle, on utiliserait un smart contract de staking
    // Ici, on simule le processus

    const mint = new PublicKey(tokenMint)

    // Configurer le staking
    await setupStaking(payer, mint, apr, lockPeriod)

    const poolId = `pool_${Date.now()}`

    return {
      id: poolId,
      tokenMint,
      tokenName,
      tokenSymbol,
      apr,
      lockPeriod,
      totalStaked: 0,
      stakerCount: 0,
      createdAt: Date.now(),
    }
  } catch (error) {
    console.error("Error creating staking pool:", error)
    throw error
  }
}

// Obtenir les pools de staking disponibles
export const getStakingPools = async (): Promise<StakingPool[]> => {
  try {
    // Dans une implémentation réelle, on interrogerait une API ou un smart contract
    // Ici, on simule les données

    const pools: StakingPool[] = []

    // Simuler des données pour les pools de staking
    const numPools = Math.floor(Math.random() * 5) + 3

    for (let i = 0; i < numPools; i++) {
      pools.push({
        id: `pool_${i}`,
        tokenMint: `token_${i}`,
        tokenName: `Token ${i}`,
        tokenSymbol: `TKN${i}`,
        apr: Math.random() * 20 + 5, // Entre 5% et 25%
        lockPeriod: [7, 14, 30, 60, 90, 180, 365][Math.floor(Math.random() * 7)], // Différentes périodes de lock
        totalStaked: Math.random() * 1000000,
        stakerCount: Math.floor(Math.random() * 1000) + 10,
        createdAt: Date.now() - Math.floor(Math.random() * 180 * 24 * 60 * 60 * 1000),
      })
    }

    // Ajouter un pool pour le token principal
    pools.push({
      id: "pool_gf_token",
      tokenMint: "gf_token_mint",
      tokenName: "GF-beta",
      tokenSymbol: "GF-b1",
      apr: 15, // 15% APR
      lockPeriod: 30, // 30 jours
      totalStaked: 500000000, // 50% de l'offre totale
      stakerCount: 5000,
      createdAt: Date.now() - 90 * 24 * 60 * 60 * 1000, // 90 jours
    })

    return pools
  } catch (error) {
    console.error("Error getting staking pools:", error)
    return []
  }
}

// Obtenir les stakings d'un utilisateur
export const getUserStakings = async (walletAddress: string): Promise<UserStaking[]> => {
  try {
    // Dans une implémentation réelle, on interrogerait une API ou un smart contract
    // Ici, on simule les données

    const stakings: UserStaking[] = []

    // Simuler des données pour les stakings de l'utilisateur
    const numStakings = Math.floor(Math.random() * 3) + 1

    for (let i = 0; i < numStakings; i++) {
      const startTime = Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)
      const lockPeriod = [7, 14, 30, 60, 90][Math.floor(Math.random() * 5)]
      const endTime = startTime + lockPeriod * 24 * 60 * 60 * 1000

      stakings.push({
        poolId: `pool_${i}`,
        tokenMint: `token_${i}`,
        amount: Math.random() * 10000,
        startTime,
        endTime,
        rewards: Math.random() * 1000,
        claimed: Math.random() > 0.5,
      })
    }

    return stakings
  } catch (error) {
    console.error("Error getting user stakings:", error)
    return []
  }
}

// Staker des tokens
export const stakeTokens = async (
  payer: Keypair,
  poolId: string,
  tokenMint: string,
  amount: number,
): Promise<boolean> => {
  try {
    // Dans une implémentation réelle, on utiliserait un smart contract de staking
    // Ici, on simule le processus

    console.log(`Staking ${amount} tokens of ${tokenMint} in pool ${poolId}`)

    return true
  } catch (error) {
    console.error("Error staking tokens:", error)
    return false
  }
}

// Unstaker des tokens
export const unstakeTokens = async (
  payer: Keypair,
  poolId: string,
  tokenMint: string,
  amount: number,
): Promise<boolean> => {
  try {
    // Dans une implémentation réelle, on utiliserait un smart contract de staking
    // Ici, on simule le processus

    console.log(`Unstaking ${amount} tokens of ${tokenMint} from pool ${poolId}`)

    return true
  } catch (error) {
    console.error("Error unstaking tokens:", error)
    return false
  }
}

// Réclamer des récompenses
export const claimRewards = async (payer: Keypair, poolId: string, tokenMint: string): Promise<number> => {
  try {
    // Dans une implémentation réelle, on utiliserait un smart contract de staking
    // Ici, on simule le processus

    const rewards = Math.random() * 1000

    console.log(`Claiming ${rewards} rewards from pool ${poolId}`)

    return rewards
  } catch (error) {
    console.error("Error claiming rewards:", error)
    return 0
  }
}

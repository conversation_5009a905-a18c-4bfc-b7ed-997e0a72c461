import { jwtVerify, SignJWT } from "jose"
import { cookies } from "next/headers"
import { randomUUID } from "crypto"

// JWT secret key (from environment variables)
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || "fallback_secret_for_development_only_change_in_production",
)

// JWT configuration
const JWT_CONFIG = {
  expiresIn: "1h",
  algorithm: "HS512",
}

/**
 * Creates a JWT token for admin authentication
 */
export async function createAdminToken(walletAddress: string, role = "admin"): Promise<string> {
  const token = await new SignJWT({
    wallet: walletAddress,
    role,
    jti: randomUUID(),
  })
    .setProtectedHeader({ alg: "HS512" }) // Using HS512 for stronger security
    .setIssuedAt()
    .setExpirationTime(JWT_CONFIG.expiresIn)
    .sign(JWT_SECRET)

  return token
}

/**
 * Verifies an admin JWT token
 */
export async function verifyAdminToken(token: string): Promise<{
  valid: boolean
  wallet?: string
  role?: string
}> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET, {
      algorithms: ["HS512"], // Only accept HS512 algorithm
    })

    if (!payload || !payload.wallet || !payload.role) {
      return { valid: false }
    }

    return {
      valid: true,
      wallet: payload.wallet as string,
      role: payload.role as string,
    }
  } catch (error) {
    console.error("Admin token verification failed:", error)
    return { valid: false }
  }
}

/**
 * Sets secure HTTP-only cookies for admin authentication
 */
export function setAdminAuthCookies(walletAddress: string, token: string): void {
  const cookieStore = cookies()

  // Set the admin session token as a secure HTTP-only cookie
  cookieStore.set("admin_session", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge: 60 * 60, // 1 hour
    path: "/",
  })

  // Set a non-HTTP-only cookie with just the wallet address for client-side UI
  cookieStore.set("admin_wallet", walletAddress, {
    httpOnly: false,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge: 60 * 60, // 1 hour
    path: "/",
  })
}

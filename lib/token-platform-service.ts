import { Connection, Keypair, PublicKey } from "@solana/web3.js"
import { envConfig } from "./env-config"
import enhancedTokenSuffixService from "./enhanced-token-suffix-service"
import tokenFeeService from "./token-fee-service"
import tokenSecurityService from "./token-security-service"
import tokenVestingService from "./token-vesting-service"
import tokenBondingCurveService from "./token-bonding-curve-service"
import tokenGovernanceService from "./token-governance-service"
import { createSplToken, mintTokens } from "./solana-utils"

export interface TokenCreationParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: number
  maxSupply?: number
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  discord?: string
  imageUrl?: string
  suffix?: string
  isAIGenerated?: boolean
  ownerAddress: string
  referrerAddress?: string
}

export interface TokenDistribution {
  team: number // percentage
  marketing: number // percentage
  development: number // percentage
  reserve: number // percentage
  liquidity: number // percentage
}

export interface TokenSecurityOptions {
  antiBot: boolean
  antiDump: boolean
  maxTxPercentage: number
  maxWalletPercentage: number
  tradingDelay: number // in seconds
}

export interface TokenLaunchOptions {
  addLiquidity: boolean
  liquidityAmount: number // in SOL
  liquidityPercentage: number // percentage of token supply
  lockLiquidity: boolean
  lockDuration: number // in days
}

class TokenPlatformService {
  private connection: Connection

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
  }

  /**
   * Crée un nouveau token avec toutes les fonctionnalités de la plateforme
   */
  async createToken(
    params: TokenCreationParams,
    distribution: TokenDistribution,
    securityOptions: TokenSecurityOptions,
    launchOptions: TokenLaunchOptions,
    payer: Keypair,
  ): Promise<{
    success: boolean
    tokenAddress?: string
    transactionId?: string
    error?: string
    details?: any
  }> {
    try {
      console.log(`Creating token: ${params.name} (${params.symbol})`)

      // 1. Générer un keypair avec le suffixe demandé
      const suffix = params.suffix || "GFMM"
      const keypairResult = await enhancedTokenSuffixService.generateKeypairWithSuffix(suffix)

      if (!keypairResult.success || !keypairResult.keypair) {
        return {
          success: false,
          error: keypairResult.error || "Failed to generate keypair with suffix",
        }
      }

      const mintKeypair = Keypair.fromSecretKey(Uint8Array.from(keypairResult.keypair.secret))
      console.log(`Generated keypair with address: ${mintKeypair.publicKey.toString()}`)

      // 2. Créer le token sur la blockchain
      const ownerPublicKey = new PublicKey(params.ownerAddress)
      const tokenMint = await createSplToken(
        this.connection,
        payer,
        ownerPublicKey,
        ownerPublicKey, // freeze authority
        params.decimals,
      )

      console.log(`Token created with address: ${tokenMint.toString()}`)

      // 3. Minter les tokens initiaux
      const totalSupply = params.initialSupply * Math.pow(10, params.decimals)
      const signature = await mintTokens(
        this.connection,
        payer,
        tokenMint,
        ownerPublicKey,
        payer, // mint authority
        totalSupply,
      )

      console.log(`Minted ${params.initialSupply} tokens to ${params.ownerAddress}`)

      // 4. Configurer la distribution des tokens
      // Dans une implémentation réelle, nous distribuerions les tokens selon les pourcentages
      console.log("Setting up token distribution...")

      // 5. Configurer les mécanismes de sécurité
      console.log("Setting up security mechanisms...")
      tokenSecurityService.setTransactionLimits({
        maxTxPerBlock: 3,
        maxWalletPercentage: securityOptions.maxWalletPercentage,
        cooldownPeriod: 2, // 2 seconds
      })

      // 6. Configurer la bonding curve si demandé
      if (launchOptions.addLiquidity) {
        console.log("Setting up bonding curve and liquidity...")
        await tokenBondingCurveService.createBondingCurve(
          tokenMint.toString(),
          0.0001, // initial price
          0.2, // reserve ratio
          params.initialSupply,
          params.maxSupply || params.initialSupply * 10,
          launchOptions.liquidityAmount,
        )
      }

      // 7. Configurer le vesting pour l'équipe et les développeurs
      console.log("Setting up vesting schedules...")
      const teamAmount = params.initialSupply * (distribution.team / 100)
      const devAmount = params.initialSupply * (distribution.development / 100)

      await tokenVestingService.createVestingSchedule(
        tokenMint.toString(),
        teamAmount,
        Date.now(),
        365, // 1 year
        30, // monthly release
        params.ownerAddress, // team wallet
        payer,
      )

      await tokenVestingService.createVestingSchedule(
        tokenMint.toString(),
        devAmount,
        Date.now(),
        730, // 2 years
        90, // quarterly release
        params.ownerAddress, // dev wallet
        payer,
      )

      // 8. Enregistrer les détails du token
      const tokenDetails = {
        address: tokenMint.toString(),
        name: params.name,
        symbol: params.symbol + suffix,
        decimals: params.decimals,
        initialSupply: params.initialSupply,
        maxSupply: params.maxSupply || params.initialSupply * 10,
        description: params.description || "",
        website: params.website || "",
        twitter: params.twitter || "",
        telegram: params.telegram || "",
        discord: params.discord || "",
        imageUrl: params.imageUrl || "",
        ownerAddress: params.ownerAddress,
        isAIGenerated: params.isAIGenerated || false,
        distribution,
        securityOptions,
        launchOptions,
        createdAt: new Date().toISOString(),
      }

      return {
        success: true,
        tokenAddress: tokenMint.toString(),
        transactionId: signature,
        details: tokenDetails,
      }
    } catch (error: any) {
      console.error("Error creating token:", error)
      return {
        success: false,
        error: error.message || "An error occurred while creating the token",
      }
    }
  }

  /**
   * Transfère des tokens avec les mécanismes de fees et de sécurité
   */
  async transferTokens(
    tokenAddress: string,
    senderAddress: string,
    recipientAddress: string,
    amount: number,
    payer: Keypair,
  ): Promise<{
    success: boolean
    transactionId?: string
    netAmount?: number
    fees?: any
    error?: string
  }> {
    try {
      console.log(`Transferring ${amount} tokens from ${senderAddress} to ${recipientAddress}`)

      // 1. Vérifier les limites de sécurité
      const securityCheck = await tokenSecurityService.checkTransactionLimits(
        tokenAddress,
        senderAddress,
        amount,
        1000000, // total supply (simulé)
      )

      if (!securityCheck.allowed) {
        return {
          success: false,
          error: securityCheck.reason || "Transaction not allowed by security limits",
        }
      }

      // 2. Calculer et distribuer les fees
      const feeResult = await tokenFeeService.calculateAndDistributeFees(
        new PublicKey(tokenAddress),
        new PublicKey(senderAddress),
        new PublicKey(recipientAddress),
        amount,
        payer,
      )

      if (!feeResult.success) {
        return {
          success: false,
          error: feeResult.error || "Failed to calculate and distribute fees",
        }
      }

      // 3. Transférer le montant net au destinataire
      // Dans une implémentation réelle, nous effectuerions le transfert ici

      return {
        success: true,
        transactionId: "simulated_tx_id",
        netAmount: feeResult.netAmount,
        fees: feeResult.fees,
      }
    } catch (error: any) {
      console.error("Error transferring tokens:", error)
      return {
        success: false,
        error: error.message || "An error occurred while transferring tokens",
      }
    }
  }

  /**
   * Crée une proposition de gouvernance
   */
  async createGovernanceProposal(
    tokenAddress: string,
    title: string,
    description: string,
    proposer: string,
    type: "unblacklist" | "parameter_change" | "fund_allocation" | "other",
    parameters?: Record<string, any>,
  ): Promise<{
    success: boolean
    proposalId?: string
    error?: string
  }> {
    try {
      return await tokenGovernanceService.createProposal(title, description, proposer, type, parameters)
    } catch (error: any) {
      console.error("Error creating governance proposal:", error)
      return {
        success: false,
        error: error.message || "An error occurred while creating the governance proposal",
      }
    }
  }

  /**
   * Vote sur une proposition de gouvernance
   */
  async voteOnProposal(
    tokenAddress: string,
    proposalId: string,
    voter: string,
    voteType: "for" | "against" | "abstain",
    votePower: number,
  ): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      return await tokenGovernanceService.vote(proposalId, voter, voteType, votePower)
    } catch (error: any) {
      console.error("Error voting on proposal:", error)
      return {
        success: false,
        error: error.message || "An error occurred while voting on the proposal",
      }
    }
  }

  /**
   * Exécute une proposition de gouvernance passée
   */
  async executeProposal(
    tokenAddress: string,
    proposalId: string,
    executor: string,
  ): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      return await tokenGovernanceService.executeProposal(proposalId, executor)
    } catch (error: any) {
      console.error("Error executing proposal:", error)
      return {
        success: false,
        error: error.message || "An error occurred while executing the proposal",
      }
    }
  }

  /**
   * Libère des tokens d'un schedule de vesting
   */
  async releaseVestedTokens(
    tokenAddress: string,
    scheduleId: string,
    beneficiary: string,
    payer: Keypair,
  ): Promise<{
    success: boolean
    releaseAmount?: number
    transactionId?: string
    error?: string
  }> {
    try {
      return await tokenVestingService.executeRelease(tokenAddress, scheduleId, payer)
    } catch (error: any) {
      console.error("Error releasing vested tokens:", error)
      return {
        success: false,
        error: error.message || "An error occurred while releasing vested tokens",
      }
    }
  }

  /**
   * Achète des tokens via la bonding curve
   */
  async buyTokens(
    tokenAddress: string,
    solAmount: number,
    buyer: Keypair,
  ): Promise<{
    success: boolean
    tokenAmount?: number
    cost?: number
    transactionId?: string
    error?: string
  }> {
    try {
      return await tokenBondingCurveService.executePurchase(tokenAddress, solAmount, buyer)
    } catch (error: any) {
      console.error("Error buying tokens:", error)
      return {
        success: false,
        error: error.message || "An error occurred while buying tokens",
      }
    }
  }

  /**
   * Vend des tokens via la bonding curve
   */
  async sellTokens(
    tokenAddress: string,
    tokenAmount: number,
    seller: Keypair,
  ): Promise<{
    success: boolean
    solAmount?: number
    transactionId?: string
    error?: string
  }> {
    try {
      return await tokenBondingCurveService.executeSale(tokenAddress, tokenAmount, seller)
    } catch (error: any) {
      console.error("Error selling tokens:", error)
      return {
        success: false,
        error: error.message || "An error occurred while selling tokens",
      }
    }
  }
}

// Exporter une instance singleton du service
const tokenPlatformService = new TokenPlatformService()
export default tokenPlatformService

/**
 * Utilitaire pour nettoyer et valider les entrées utilisateur
 */

// Caractères valides en base58
const BASE58_CHARS = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

/**
 * Vérifie si une chaîne est valide en base58
 */
export function isValidBase58(str: string): boolean {
  if (!str) return false

  for (let i = 0; i < str.length; i++) {
    if (!BASE58_CHARS.includes(str[i])) {
      return false
    }
  }
  return true
}

/**
 * Nettoie une chaîne pour ne garder que les caractères base58
 */
export function sanitizeToBase58(str: string): string {
  if (!str) return ""

  let result = ""
  for (let i = 0; i < str.length; i++) {
    if (BASE58_CHARS.includes(str[i])) {
      result += str[i]
    }
  }
  return result
}

/**
 * Trouve le premier caractère non-base58 dans une chaîne
 */
export function findInvalidBase58Char(str: string): { char: string; position: number } | null {
  if (!str) return null

  for (let i = 0; i < str.length; i++) {
    if (!BASE58_CHARS.includes(str[i])) {
      return { char: str[i], position: i }
    }
  }
  return null
}

/**
 * Nettoie un objet en sanitisant toutes les chaînes de caractères
 */
export function sanitizeObjectStrings(obj: any): any {
  if (!obj) return obj

  if (typeof obj === "string") {
    return sanitizeToBase58(obj)
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => sanitizeObjectStrings(item))
  }

  if (typeof obj === "object") {
    const result: any = {}
    for (const key in obj) {
      result[key] = sanitizeObjectStrings(obj[key])
    }
    return result
  }

  return obj
}

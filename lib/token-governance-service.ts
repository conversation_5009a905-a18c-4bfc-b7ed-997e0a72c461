import { Connection } from "@solana/web3.js"
import { envConfig } from "./env-config"

export interface Proposal {
  id: string
  title: string
  description: string
  proposer: string
  createdAt: number
  expiresAt: number
  executed: boolean
  canceled: boolean
  votes: {
    for: number
    against: number
    abstain: number
  }
  voterAddresses: string[]
  status: "active" | "passed" | "rejected" | "executed" | "canceled" | "expired"
  type: "unblacklist" | "parameter_change" | "fund_allocation" | "other"
  parameters?: Record<string, any>
}

export interface VoteReceipt {
  proposalId: string
  voter: string
  voteType: "for" | "against" | "abstain"
  votePower: number
  timestamp: number
}

class TokenGovernanceService {
  private connection: Connection
  private proposals: Map<string, Proposal>
  private voteReceipts: Map<string, VoteReceipt[]>
  private quorumPercentage: number
  private votingPeriodDays: number

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
    this.proposals = new Map()
    this.voteReceipts = new Map()
    this.quorumPercentage = 10 // 10% de l'offre totale
    this.votingPeriodDays = 3 // 3 jours

    // Démarrer le processus de vérification périodique des propositions
    this.startProposalCheckInterval()
  }

  /**
   * Crée une nouvelle proposition
   */
  async createProposal(
    title: string,
    description: string,
    proposer: string,
    type: "unblacklist" | "parameter_change" | "fund_allocation" | "other",
    parameters?: Record<string, any>,
  ): Promise<{
    success: boolean
    proposalId?: string
    error?: string
  }> {
    try {
      // Vérifier que le titre et la description ne sont pas vides
      if (!title.trim() || !description.trim()) {
        return {
          success: false,
          error: "Title and description cannot be empty",
        }
      }

      // Créer un ID unique pour cette proposition
      const proposalId = `proposal_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

      // Calculer la date d'expiration
      const now = Date.now()
      const expiresAt = now + this.votingPeriodDays * 86400000 // convertir jours en millisecondes

      // Créer la proposition
      const proposal: Proposal = {
        id: proposalId,
        title,
        description,
        proposer,
        createdAt: now,
        expiresAt,
        executed: false,
        canceled: false,
        votes: {
          for: 0,
          against: 0,
          abstain: 0,
        },
        voterAddresses: [],
        status: "active",
        type,
        parameters,
      }

      // Ajouter la proposition à la map
      this.proposals.set(proposalId, proposal)

      // Initialiser les votes pour cette proposition
      this.voteReceipts.set(proposalId, [])

      return {
        success: true,
        proposalId,
      }
    } catch (error: any) {
      console.error("Error creating proposal:", error)
      return {
        success: false,
        error: error.message || "An error occurred while creating the proposal",
      }
    }
  }

  /**
   * Vote sur une proposition
   */
  async vote(
    proposalId: string,
    voter: string,
    voteType: "for" | "against" | "abstain",
    votePower: number,
  ): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      // Récupérer la proposition
      const proposal = this.proposals.get(proposalId)

      if (!proposal) {
        return {
          success: false,
          error: "Proposal not found",
        }
      }

      // Vérifier que la proposition est active
      if (proposal.status !== "active") {
        return {
          success: false,
          error: `Proposal is not active (current status: ${proposal.status})`,
        }
      }

      // Vérifier que l'utilisateur n'a pas déjà voté
      if (proposal.voterAddresses.includes(voter)) {
        return {
          success: false,
          error: "You have already voted on this proposal",
        }
      }

      // Ajouter le vote
      proposal.votes[voteType] += votePower
      proposal.voterAddresses.push(voter)

      // Enregistrer le reçu de vote
      const voteReceipt: VoteReceipt = {
        proposalId,
        voter,
        voteType,
        votePower,
        timestamp: Date.now(),
      }

      this.voteReceipts.get(proposalId)!.push(voteReceipt)

      // Vérifier si le quorum est atteint et si la proposition est passée
      this.checkProposalStatus(proposalId)

      return {
        success: true,
      }
    } catch (error: any) {
      console.error("Error voting on proposal:", error)
      return {
        success: false,
        error: error.message || "An error occurred while voting on the proposal",
      }
    }
  }

  /**
   * Récupère une proposition par son ID
   */
  getProposal(proposalId: string): Proposal | null {
    return this.proposals.get(proposalId) || null
  }

  /**
   * Récupère toutes les propositions
   */
  getAllProposals(): Proposal[] {
    return Array.from(this.proposals.values())
  }

  /**
   * Récupère les propositions actives
   */
  getActiveProposals(): Proposal[] {
    return Array.from(this.proposals.values()).filter((p) => p.status === "active")
  }

  /**
   * Récupère les reçus de vote pour une proposition
   */
  getVoteReceipts(proposalId: string): VoteReceipt[] {
    return this.voteReceipts.get(proposalId) || []
  }

  /**
   * Exécute une proposition passée
   */
  async executeProposal(
    proposalId: string,
    executor: string,
  ): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      // Récupérer la proposition
      const proposal = this.proposals.get(proposalId)

      if (!proposal) {
        return {
          success: false,
          error: "Proposal not found",
        }
      }

      // Vérifier que la proposition est passée
      if (proposal.status !== "passed") {
        return {
          success: false,
          error: `Proposal cannot be executed (current status: ${proposal.status})`,
        }
      }

      // Exécuter la proposition en fonction de son type
      switch (proposal.type) {
        case "unblacklist":
          // Retirer l'adresse de la blacklist
          if (proposal.parameters?.address) {
            // Dans une implémentation réelle, nous appellerions un service pour retirer l'adresse
            console.log(`Removing address ${proposal.parameters.address} from blacklist`)
          } else {
            return {
              success: false,
              error: "Missing address parameter for unblacklist proposal",
            }
          }
          break

        case "parameter_change":
          // Modifier un paramètre du système
          if (proposal.parameters?.name && proposal.parameters?.value !== undefined) {
            console.log(`Changing parameter ${proposal.parameters.name} to ${proposal.parameters.value}`)
          } else {
            return {
              success: false,
              error: "Missing name or value parameter for parameter_change proposal",
            }
          }
          break

        case "fund_allocation":
          // Allouer des fonds
          if (proposal.parameters?.amount && proposal.parameters?.destination) {
            console.log(`Allocating ${proposal.parameters.amount} to ${proposal.parameters.destination}`)
          } else {
            return {
              success: false,
              error: "Missing amount or destination parameter for fund_allocation proposal",
            }
          }
          break

        default:
          console.log(`Executing proposal of type ${proposal.type}`)
          break
      }

      // Marquer la proposition comme exécutée
      proposal.executed = true
      proposal.status = "executed"

      return {
        success: true,
      }
    } catch (error: any) {
      console.error("Error executing proposal:", error)
      return {
        success: false,
        error: error.message || "An error occurred while executing the proposal",
      }
    }
  }

  /**
   * Annule une proposition active
   */
  cancelProposal(
    proposalId: string,
    canceler: string,
  ): {
    success: boolean
    error?: string
  } {
    // Récupérer la proposition
    const proposal = this.proposals.get(proposalId)

    if (!proposal) {
      return {
        success: false,
        error: "Proposal not found",
      }
    }

    // Vérifier que la proposition est active
    if (proposal.status !== "active") {
      return {
        success: false,
        error: `Proposal cannot be canceled (current status: ${proposal.status})`,
      }
    }

    // Vérifier que le canceler est le proposer
    if (proposal.proposer !== canceler) {
      return {
        success: false,
        error: "Only the proposer can cancel the proposal",
      }
    }

    // Marquer la proposition comme annulée
    proposal.canceled = true
    proposal.status = "canceled"

    return {
      success: true,
    }
  }

  /**
   * Vérifie le statut d'une proposition
   */
  private checkProposalStatus(proposalId: string): void {
    const proposal = this.proposals.get(proposalId)

    if (!proposal || proposal.status !== "active") {
      return
    }

    // Vérifier si la proposition a expiré
    const now = Date.now()
    if (now > proposal.expiresAt) {
      proposal.status = "expired"
      return
    }

    // Calculer le total des votes
    const totalVotes = proposal.votes.for + proposal.votes.against + proposal.votes.abstain

    // Vérifier si le quorum est atteint
    // Dans une implémentation réelle, nous comparerions avec l'offre totale du token
    const totalSupply = 1000000 // Simulé
    const quorumThreshold = totalSupply * (this.quorumPercentage / 100)

    if (totalVotes >= quorumThreshold) {
      // Vérifier si la proposition est passée (plus de votes pour que contre)
      if (proposal.votes.for > proposal.votes.against) {
        proposal.status = "passed"
      } else {
        proposal.status = "rejected"
      }
    }
  }

  /**
   * Démarre l'intervalle de vérification des propositions
   */
  private startProposalCheckInterval(): void {
    // Vérifier les propositions toutes les heures
    setInterval(() => this.checkAllProposals(), 3600000)
  }

  /**
   * Vérifie le statut de toutes les propositions actives
   */
  private checkAllProposals(): void {
    console.log("Checking all active proposals...")

    // Parcourir toutes les propositions actives
    for (const [proposalId, proposal] of this.proposals.entries()) {
      if (proposal.status === "active") {
        this.checkProposalStatus(proposalId)
      }
    }
  }

  /**
   * Configure le pourcentage de quorum
   */
  setQuorumPercentage(percentage: number): void {
    if (percentage <= 0 || percentage > 100) {
      throw new Error("Quorum percentage must be between 1 and 100")
    }

    this.quorumPercentage = percentage
  }

  /**
   * Configure la durée de la période de vote
   */
  setVotingPeriodDays(days: number): void {
    if (days <= 0) {
      throw new Error("Voting period must be greater than 0 days")
    }

    this.votingPeriodDays = days
  }
}

// Exporter une instance singleton du service
const tokenGovernanceService = new TokenGovernanceService()
export default tokenGovernanceService

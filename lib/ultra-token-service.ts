import { Connection, Keypair, type <PERSON>Key } from "@solana/web3.js"
import { createMint, getOrCreateAssociatedTokenAccount, mintTo, getMint } from "@solana/spl-token"
import { diagnoseTokenCreationIssue } from "./token-diagnostics"
import { getNetworkById } from "./network-config"

interface TokenParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: number
  ownerPublicKey: PublicKey
  networkId?: string // Ajout du paramètre networkId pour spécifier devnet ou mainnet
}

interface TokenCreationResult {
  success: boolean
  tokenAddress?: string
  tokenAccountAddress?: string
  transactionId?: string
  error?: string
  diagnostics?: any
  mintInfo?: any
}

export class UltraTokenService {
  private connection: Connection

  constructor(rpcUrl?: string) {
    this.connection = new Connection(
      rpcUrl || process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com",
      "confirmed",
    )
  }

  // Méthode pour changer la connexion RPC
  setConnection(rpcUrl: string) {
    this.connection = new Connection(rpcUrl, "confirmed")
    console.log(`Connection switched to: ${rpcUrl}`)
  }

  async createToken(params: TokenParams, payer: Keypair): Promise<TokenCreationResult> {
    try {
      console.log("Creating token with params:", {
        name: params.name,
        symbol: params.symbol,
        decimals: params.decimals,
        initialSupply: params.initialSupply,
        networkId: params.networkId || "devnet",
      })

      // Si un networkId est fourni, utiliser la bonne connexion RPC
      if (params.networkId) {
        const network = getNetworkById(params.networkId)
        if (network) {
          this.setConnection(network.rpcUrl)
          console.log(`Using network: ${network.name} (${network.rpcUrl})`)
        }
      }

      // Vérifier le solde du payeur avant de commencer
      const payerBalance = await this.connection.getBalance(payer.publicKey)
      console.log(`Payer balance: ${payerBalance / 1e9} SOL`)

      if (payerBalance < 10000000) {
        // Minimum 0.01 SOL
        throw new Error(`Insufficient balance: ${payerBalance / 1e9} SOL. Minimum required: 0.01 SOL`)
      }

      // Generate a new keypair for the mint
      const mintKeypair = Keypair.generate()
      console.log("Generated mint keypair:", mintKeypair.publicKey.toString())

      // Calculer le loyer minimum pour le compte de mint
      const rentExemptBalance = await this.connection.getMinimumBalanceForRentExemption(82)
      console.log(`Rent exempt balance required: ${rentExemptBalance / 1e9} SOL`)

      // Create the token mint with explicit transaction
      console.log("Creating token mint...")

      try {
        const mint = await createMint(
          this.connection,
          payer,
          params.ownerPublicKey, // Mint authority
          params.ownerPublicKey, // Freeze authority (can be null)
          params.decimals,
        )
        console.log("Token mint created:", mint.toString())

        // Create a token account
        console.log("Creating token account...")
        const tokenAccount = await getOrCreateAssociatedTokenAccount(
          this.connection,
          payer,
          mint,
          params.ownerPublicKey,
        )
        console.log("Token account created:", tokenAccount.address.toString())

        // Mint initial supply
        console.log("Minting initial supply...")
        const initialSupplyWithDecimals = params.initialSupply * Math.pow(10, params.decimals)
        const mintTx = await mintTo(
          this.connection,
          payer,
          mint,
          tokenAccount.address,
          params.ownerPublicKey,
          BigInt(initialSupplyWithDecimals),
        )
        console.log("Initial supply minted, transaction:", mintTx)

        // Récupérer les informations du mint pour vérification
        const mintInfo = await getMint(this.connection, mint)

        return {
          success: true,
          tokenAddress: mint.toString(),
          tokenAccountAddress: tokenAccount.address.toString(),
          transactionId: mintTx,
          mintInfo: {
            address: mint.toString(),
            decimals: mintInfo.decimals,
            supply: mintInfo.supply.toString(),
            isInitialized: mintInfo.isInitialized,
            freezeAuthority: mintInfo.freezeAuthority?.toString() || null,
            mintAuthority: mintInfo.mintAuthority?.toString() || null,
          },
        }
      } catch (error: any) {
        console.error("Error in token creation process:", error)

        // Analyse détaillée de l'erreur
        let errorMessage = error.message || "Unknown error"
        let errorCode = error.code || "UNKNOWN_ERROR"

        // Vérifier les erreurs spécifiques à Solana
        if (errorMessage.includes("blockhash")) {
          errorCode = "BLOCKHASH_ERROR"
          errorMessage = "Failed to get recent blockhash. Network may be congested."
        } else if (errorMessage.includes("insufficient funds")) {
          errorCode = "INSUFFICIENT_FUNDS"
          errorMessage = `Insufficient funds for transaction. Please ensure your wallet has enough SOL.`
        } else if (errorMessage.includes("timed out")) {
          errorCode = "TIMEOUT_ERROR"
          errorMessage = "Transaction confirmation timed out. The network may be congested."
        }

        throw {
          message: errorMessage,
          code: errorCode,
          originalError: error,
        }
      }
    } catch (error: any) {
      console.error("Error creating token:", error)

      // Run diagnostics
      const diagnostics = await diagnoseTokenCreationIssue(error)

      return {
        success: false,
        error: error.message || "Unknown error occurred",
        diagnostics,
      }
    }
  }

  // Method to check if a string is valid base58
  isValidBase58(str: string): boolean {
    try {
      // If this doesn't throw, it's valid base58
      const base58Chars = "**********************************************************"
      for (let i = 0; i < str.length; i++) {
        if (!base58Chars.includes(str[i])) {
          return false
        }
      }
      return true
    } catch {
      return false
    }
  }
}

// Caractères valides en base58 (sans 0, O, I, l)
export const BASE58_CHARS = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

/**
 * Vérifie si une chaîne ne contient que des caractères base58 valides
 */
export function isValidBase58(str: string): boolean {
  if (!str) return true // Une chaîne vide est considérée comme valide
  for (let i = 0; i < str.length; i++) {
    if (!BASE58_CHARS.includes(str[i])) {
      return false
    }
  }
  return true
}

/**
 * Nettoie une chaîne en ne gardant que les caractères base58 valides
 */
export function sanitizeToBase58(str: string): string {
  if (!str) return ""
  let result = ""
  for (let i = 0; i < str.length; i++) {
    if (BASE58_CHARS.includes(str[i])) {
      result += str[i]
    }
  }
  return result
}

/**
 * Trouve le premier caractère non-base58 dans une chaîne
 */
export function findInvalidBase58Char(str: string): { char: string; position: number } | null {
  if (!str) return null
  for (let i = 0; i < str.length; i++) {
    if (!BASE58_CHARS.includes(str[i])) {
      return { char: str[i], position: i }
    }
  }
  return null
}

/**
 * Vérifie si une erreur est liée à un problème de base58
 */
export function isBase58Error(error: any): boolean {
  if (!error) return false
  const errorMessage = typeof error === "string" ? error : error.message || ""
  return (
    errorMessage.includes("base58") ||
    errorMessage.includes("Non-base58") ||
    errorMessage.includes("Invalid character") ||
    errorMessage.includes("is not supported")
  )
}

/**
 * Sanitise tous les champs sensibles d'un objet pour s'assurer qu'ils sont compatibles base58
 */
export function sanitizeObjectForBase58(obj: any): any {
  if (!obj || typeof obj !== "object") return obj

  const result = { ...obj }

  // Liste des champs à vérifier et sanitiser
  const fieldsToSanitize = ["symbol", "name", "tokenSymbol", "tokenName", "suffix"]

  for (const field of fieldsToSanitize) {
    if (result[field] && typeof result[field] === "string") {
      result[field] = sanitizeToBase58(result[field])
    }
  }

  return result
}

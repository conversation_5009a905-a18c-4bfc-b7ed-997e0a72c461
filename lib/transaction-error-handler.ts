interface TransactionErrorDetails {
  errorCode?: string
  errorName?: string
  errorMessage: string
  possibleCauses: string[]
  suggestedFixes: string[]
  technicalDetails?: string
  severity: "low" | "medium" | "high" | "critical"
}

// Améliorer l'analyse des erreurs de transaction
export async function analyzeTransactionError(error: any, walletAddress: string): Promise<any> {
  console.log("Analyzing transaction error:", error)

  // Extraire le message d'erreur
  let errorMessage = error.message || "Unknown error"
  const errorCode = error.code || "UNKNOWN_ERROR"
  let errorType = "UNKNOWN"
  let possibleSolutions = []

  // Analyser le type d'erreur
  if (errorMessage.includes("blockhash")) {
    errorType = "BLOCKHASH_ERROR"
    errorMessage = "Échec de récupération du blockhash récent. Le réseau peut être congestionné."
    possibleSolutions = [
      "Réessayez plus tard lorsque le réseau est moins congestionné",
      "Utilisez un autre point de terminaison RPC",
      "Vérifiez votre connexion internet",
    ]
  } else if (errorMessage.includes("insufficient funds")) {
    errorType = "INSUFFICIENT_FUNDS"
    errorMessage = `Fonds insuffisants pour la transaction. Assurez-vous que votre portefeuille a suffisamment de SOL.`
    possibleSolutions = [
      "Ajoutez plus de SOL à votre portefeuille",
      "Réduisez les frais de transaction",
      "Utilisez un autre portefeuille avec plus de fonds",
    ]
  } else if (errorMessage.includes("timed out")) {
    errorType = "TIMEOUT_ERROR"
    errorMessage = "La confirmation de la transaction a expiré. Le réseau peut être congestionné."
    possibleSolutions = [
      "Réessayez la transaction",
      "Utilisez un autre point de terminaison RPC",
      "Augmentez le délai d'attente de confirmation",
    ]
  } else if (errorMessage.includes("Transaction simulation failed")) {
    errorType = "SIMULATION_FAILED"
    errorMessage = "La simulation de la transaction a échoué. Vérifiez les paramètres de la transaction."
    possibleSolutions = [
      "Vérifiez les paramètres de la transaction",
      "Assurez-vous que vous avez suffisamment de SOL pour les frais",
      "Essayez avec une offre initiale plus petite",
    ]
  } else if (errorMessage.includes("Transaction was not confirmed")) {
    errorType = "CONFIRMATION_FAILED"
    errorMessage = "La transaction n'a pas été confirmée dans le délai imparti."
    possibleSolutions = [
      "Vérifiez l'explorateur Solana pour voir si la transaction a été confirmée",
      "Réessayez la transaction",
      "Augmentez le délai d'attente de confirmation",
    ]
  } else if (errorMessage.includes("User rejected")) {
    errorType = "USER_REJECTED"
    errorMessage = "Transaction rejetée par l'utilisateur."
    possibleSolutions = [
      "Approuvez la transaction dans votre portefeuille",
      "Vérifiez que votre portefeuille est déverrouillé",
    ]
  } else if (errorMessage.includes("Invalid public key")) {
    errorType = "INVALID_PUBLIC_KEY"
    errorMessage = "Clé publique invalide. Vérifiez les adresses utilisées."
    possibleSolutions = ["Vérifiez que l'adresse du portefeuille est valide", "Reconnectez votre portefeuille"]
  } else if (errorMessage.includes("RPC") || errorMessage.includes("network")) {
    errorType = "RPC_ERROR"
    errorMessage = "Erreur de connexion RPC. Vérifiez votre connexion au réseau Solana."
    possibleSolutions = [
      "Utilisez un autre point de terminaison RPC",
      "Vérifiez votre connexion internet",
      "Réessayez plus tard",
    ]
  }

  // Vérifier si l'erreur est liée au réseau mainnet
  const isMainnetError = errorMessage.includes("mainnet") || errorMessage.includes("Mainnet")

  if (isMainnetError) {
    possibleSolutions.push("Essayez d'abord sur devnet pour tester")
    possibleSolutions.push("Assurez-vous d'avoir suffisamment de SOL réels pour les frais de mainnet")
  }

  return {
    errorType,
    errorCode,
    errorMessage,
    walletAddress,
    timestamp: new Date().toISOString(),
    possibleSolutions,
    originalError: error.toString(),
    isMainnetError,
  }
}

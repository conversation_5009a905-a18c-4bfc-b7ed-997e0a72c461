import { envConfig } from "./env-config"

export interface NetworkConfig {
  id: string
  name: string
  rpcUrl: string
  environment: "devnet" | "testnet" | "mainnet"
  icon?: string
  color?: string
}

export const networks: NetworkConfig[] = [
  {
    id: "solana-devnet",
    name: "Solana Devnet",
    rpcUrl: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com",
    environment: "devnet",
    color: "#9945FF",
  },
  {
    id: "solana-mainnet",
    name: "Solana Mainnet",
    rpcUrl: "https://api.mainnet-beta.solana.com",
    environment: "mainnet",
    color: "#14F195",
  },
]

// Get network configuration by ID
export function getNetworkById(id: string): NetworkConfig | undefined {
  return networks.find((network) => network.id === id)
}

export type NetworkType = "solana" | "bnb"
export type ChainEnvironment = "mainnet" | "testnet" | "devnet" | "localnet"

export interface OldNetworkConfig {
  id: string
  name: string
  type: NetworkType
  environment: ChainEnvironment
  rpcUrl: string
  explorerUrl: string
  tokenExplorerUrl: string
  dexUrl?: string
  dexApiUrl?: string
  nativeCurrency: {
    name: string
    symbol: string
    decimals: number
    logo: string
  }
  enabled: boolean
  isDefault?: boolean
  chainId?: number // For EVM chains like BNB
  blockExplorerUrls?: string[]
  iconUrl: string
}

// Default network configurations
export const NETWORK_CONFIGS: OldNetworkConfig[] = [
  {
    id: "solana-devnet",
    name: "Solana Devnet",
    type: "solana",
    environment: "devnet",
    rpcUrl: envConfig.SOLANA_RPC_URL,
    explorerUrl: "https://explorer.solana.com/?cluster=devnet",
    tokenExplorerUrl: "https://solscan.io/token/{address}?cluster=devnet",
    dexUrl: "https://dex.raydium.io/#/market/{address}",
    dexApiUrl: "https://api.raydium.io/v2",
    nativeCurrency: {
      name: "Solana",
      symbol: "SOL",
      decimals: 9,
      logo: "/images/solana-logo.svg",
    },
    enabled: true,
    isDefault: true,
    iconUrl: "/images/solana-logo.svg",
  },
  {
    id: "solana-mainnet",
    name: "Solana Mainnet",
    type: "solana",
    environment: "mainnet",
    rpcUrl: "https://api.mainnet-beta.solana.com",
    explorerUrl: "https://explorer.solana.com",
    tokenExplorerUrl: "https://solscan.io/token/{address}",
    dexUrl: "https://dex.raydium.io/#/market/{address}",
    dexApiUrl: "https://api.raydium.io/v2",
    nativeCurrency: {
      name: "Solana",
      symbol: "SOL",
      decimals: 9,
      logo: "/images/solana-logo.svg",
    },
    enabled: true,
    iconUrl: "/images/solana-logo.svg",
  },
  {
    id: "bnb-testnet",
    name: "BNB Chain Testnet",
    type: "bnb",
    environment: "testnet",
    rpcUrl: envConfig.BNB_RPC_URL,
    explorerUrl: "https://testnet.bscscan.com",
    tokenExplorerUrl: "https://testnet.bscscan.com/token/{address}",
    dexUrl: "https://pancake.kiemtienonline360.com/#/swap",
    dexApiUrl: "https://api.pancakeswap.info/api/v2",
    nativeCurrency: {
      name: "BNB",
      symbol: "BNB",
      decimals: 18,
      logo: "/images/bnb-logo.svg",
    },
    enabled: true,
    chainId: 97,
    blockExplorerUrls: ["https://testnet.bscscan.com"],
    iconUrl: "/images/bnb-logo.svg",
  },
  {
    id: "bnb-mainnet",
    name: "BNB Chain",
    type: "bnb",
    environment: "mainnet",
    rpcUrl: "https://bsc-dataseed.binance.org",
    explorerUrl: "https://bscscan.com",
    tokenExplorerUrl: "https://bscscan.com/token/{address}",
    dexUrl: "https://pancakeswap.finance/swap",
    dexApiUrl: "https://api.pancakeswap.info/api/v2",
    nativeCurrency: {
      name: "BNB",
      symbol: "BNB",
      decimals: 18,
      logo: "/images/bnb-logo.svg",
    },
    enabled: true,
    chainId: 56,
    blockExplorerUrls: ["https://bscscan.com"],
    iconUrl: "/images/bnb-logo.svg",
  },
]

// Get default network configuration
export const getDefaultNetwork = (): OldNetworkConfig => {
  const defaultNetwork = NETWORK_CONFIGS.find((network) => network.isDefault)
  return defaultNetwork || NETWORK_CONFIGS[0]
}

// Get network configuration by type and environment
export const getNetworkByTypeAndEnv = (type: NetworkType, env: ChainEnvironment): OldNetworkConfig | undefined => {
  return NETWORK_CONFIGS.find((network) => network.type === type && network.environment === env)
}

// Get all networks of a specific type
export const getNetworksByType = (type: NetworkType): OldNetworkConfig[] => {
  return NETWORK_CONFIGS.filter((network) => network.type === type)
}

// Get explorer URL for a token
export const getTokenExplorerUrl = (network: OldNetworkConfig, address: string): string => {
  return network.tokenExplorerUrl.replace("{address}", address)
}

// Get DEX URL for a token
export const getDexUrl = (network: OldNetworkConfig, address: string): string | undefined => {
  return network.dexUrl?.replace("{address}", address)
}

// Get token program ID for Solana
export const getTokenProgramId = (): string => {
  return envConfig.TOKEN_PROGRAM_ID
}

// Get BNB token factory contract address based on network
export function getBnbTokenFactoryAddress(networkId?: string): string {
  if (!networkId) return envConfig.BNB_TOKEN_FACTORY_TESTNET // Default to testnet

  if (networkId === "bnb-mainnet") {
    return envConfig.BNB_TOKEN_FACTORY_MAINNET
  }

  return envConfig.BNB_TOKEN_FACTORY_TESTNET
}

// Get PancakeSwap router address based on network
export function getPancakeswapRouterAddress(networkId: string): string {
  if (networkId === "bnb-mainnet") {
    return envConfig.PANCAKESWAP_ROUTER_MAINNET
  }

  return envConfig.PANCAKESWAP_ROUTER_TESTNET
}

export function getPancakeswapFactoryAddress(networkId: string): string {
  const networks = {
    "bnb-testnet": "******************************************", // Testnet factory
    "bnb-mainnet": "******************************************", // Mainnet factory
  }

  return networks[networkId] || networks["bnb-testnet"]
}

export function getWethAddress(networkId: string): string {
  const networks = {
    "bnb-testnet": "******************************************", // Testnet WBNB
    "bnb-mainnet": "******************************************", // Mainnet WBNB
  }

  return networks[networkId] || networks["bnb-testnet"]
}

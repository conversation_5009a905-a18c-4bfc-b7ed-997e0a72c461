import { Redis } from "@upstash/redis"

// Create Redis client
const redis = new Redis({
  url: process.env.KV_REST_API_URL || "",
  token: process.env.KV_REST_API_TOKEN || "",
})

/**
 * Service for interacting with Redis
 */
class RedisService {
  private readonly redis: Redis
  private readonly defaultTTL = 60 * 60 // 1 hour in seconds

  constructor() {
    this.redis = redis
  }

  /**
   * Set a value in Redis with optional TTL
   */
  async set(key: string, value: any, ttl = this.defaultTTL): Promise<void> {
    try {
      await this.redis.set(key, JSON.stringify(value), { ex: ttl })
    } catch (error) {
      console.error(`Error setting Redis key ${key}:`, error)
    }
  }

  /**
   * Get a value from Redis
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key)
      return value ? (JSON.parse(value as string) as T) : null
    } catch (error) {
      console.error(`<PERSON>rror getting Redis key ${key}:`, error)
      return null
    }
  }

  /**
   * Delete a key from Redis
   */
  async delete(key: string): Promise<void> {
    try {
      await this.redis.del(key)
    } catch (error) {
      console.error(`Error deleting Redis key ${key}:`, error)
    }
  }

  /**
   * Increment a counter in Redis
   */
  async increment(key: string, amount = 1): Promise<number> {
    try {
      return await this.redis.incrby(key, amount)
    } catch (error) {
      console.error(`Error incrementing Redis key ${key}:`, error)
      return 0
    }
  }

  /**
   * Get multiple values from Redis
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const values = await this.redis.mget(...keys)
      return values.map((value) => (value ? (JSON.parse(value as string) as T) : null))
    } catch (error) {
      console.error(`Error getting multiple Redis keys:`, error)
      return keys.map(() => null)
    }
  }
}

// Export a singleton instance
const redisService = new RedisService()
export default redisService

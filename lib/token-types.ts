export interface TokenInfo {
  address: string
  name: string
  symbol: string
  symbolWithSuffix?: string
  decimals: number
  totalSupply: number
  circulatingSupply: number
  price: number
  priceChange24h: number
  marketCap: number
  volume24h: number
  holders: number
  createdAt: string
  network: string
  initialPurchaseComplete?: boolean
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  imageUrl?: string
  mintAuthority?: string | null
  freezeAuthority?: string | null
  isInitialized?: boolean
}

export interface TokenCreationParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: number
  maxSupply?: number
  isMintable?: boolean
  isBurnable?: boolean
  isPausable?: boolean
  isTransferTaxable?: boolean
  transferTaxRate?: number
  ownerAddress: string
  suffix?: string
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  imageUrl?: string
  networkId?: string
  isAIGenerated?: boolean
}

export interface TokenCreationResult {
  success: boolean
  tokenAddress?: string
  tokenSymbolWithSuffix?: string
  transactionId?: string
  tokenData?: any
  error?: string
  networkUsed?: string
}

import { Connection, PublicKey, Transaction, TransactionInstruction } from "@solana/web3.js"
import { envConfig } from "./env-config"
import { getNetworkById } from "./network-config"

class TokenPaymentService {
  /**
   * Vérifie si un paiement est valide
   */
  async verifyPayment(paymentSignature: string, networkId?: string): Promise<boolean> {
    try {
      // Pour le développement, accepter les paiements simulés
      if (paymentSignature === "simulated_payment") {
        console.log("Paiement simulé accepté pour le développement")
        return true
      }

      // Récupérer la connexion RPC
      let rpcUrl = envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com"
      if (networkId) {
        const network = getNetworkById(networkId)
        if (network) {
          rpcUrl = network.rpcUrl
        }
      }

      const connection = new Connection(rpcUrl, "confirmed")

      // Récupérer les détails de la transaction
      const transactionDetails = await connection.getTransaction(paymentSignature, {
        commitment: "confirmed",
        maxSupportedTransactionVersion: 0,
      })

      if (!transactionDetails) {
        console.error(`Transaction ${paymentSignature} non trouvée`)
        return false
      }

      // Vérifier que la transaction est confirmée
      if (!transactionDetails.meta || transactionDetails.meta.err) {
        console.error(`Transaction ${paymentSignature} a échoué:`, transactionDetails.meta?.err)
        return false
      }

      // Vérifier que le destinataire est correct (adresse du trésor)
      const treasuryAddress = new PublicKey(envConfig.TREASURY_ADDRESS || "11111111111111111111111111111111")
      const postBalances = transactionDetails.meta.postBalances
      const preBalances = transactionDetails.meta.preBalances
      const accountKeys = transactionDetails.transaction.message.accountKeys

      // Trouver l'index du trésor dans les comptes
      const treasuryIndex = accountKeys.findIndex((key) => key.equals(treasuryAddress))
      if (treasuryIndex === -1) {
        console.error(`Le trésor ${treasuryAddress.toString()} n'est pas impliqué dans la transaction`)
        return false
      }

      // Vérifier que le solde du trésor a augmenté
      const treasuryBalanceChange = postBalances[treasuryIndex] - preBalances[treasuryIndex]
      if (treasuryBalanceChange <= 0) {
        console.error(`Le solde du trésor n'a pas augmenté: ${treasuryBalanceChange}`)
        return false
      }

      // Vérifier que le montant est suffisant (0.01 SOL minimum)
      const minimumPayment = 0.01 * 1e9 // 0.01 SOL en lamports
      if (treasuryBalanceChange < minimumPayment) {
        console.error(
          `Le paiement est insuffisant: ${treasuryBalanceChange / 1e9} SOL (minimum: ${minimumPayment / 1e9} SOL)`,
        )
        return false
      }

      console.log(`Paiement vérifié: ${treasuryBalanceChange / 1e9} SOL reçus par le trésor`)
      return true
    } catch (error) {
      console.error("Erreur lors de la vérification du paiement:", error)
      return false
    }
  }

  /**
   * Crée une transaction de paiement
   */
  async createPaymentTransaction(
    payerAddress: string,
    amount: number,
    networkId?: string,
  ): Promise<{ transaction: Transaction; message: string }> {
    try {
      // Récupérer la connexion RPC
      let rpcUrl = envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com"
      if (networkId) {
        const network = getNetworkById(networkId)
        if (network) {
          rpcUrl = network.rpcUrl
        }
      }

      const connection = new Connection(rpcUrl, "confirmed")

      // Créer une transaction de transfert
      const transaction = new Transaction()
      const payerPublicKey = new PublicKey(payerAddress)
      const treasuryAddress = new PublicKey(envConfig.TREASURY_ADDRESS || "11111111111111111111111111111111")

      // Ajouter l'instruction de transfert
      transaction.add(
        new TransactionInstruction({
          keys: [
            { pubkey: payerPublicKey, isSigner: true, isWritable: true },
            { pubkey: treasuryAddress, isSigner: false, isWritable: true },
          ],
          programId: new PublicKey("11111111111111111111111111111111"), // System Program
          data: Buffer.from([
            2,
            ...new Uint8Array(
              amount
                .toString()
                .split("")
                .map((c) => c.charCodeAt(0)),
            ),
          ]),
        }),
      )

      // Récupérer le blockhash récent
      const { blockhash } = await connection.getLatestBlockhash("confirmed")
      transaction.recentBlockhash = blockhash
      transaction.feePayer = payerPublicKey

      return {
        transaction,
        message: `Paiement de ${amount} SOL pour la création du token`,
      }
    } catch (error) {
      console.error("Erreur lors de la création de la transaction de paiement:", error)
      throw error
    }
  }
}

// Exporter une instance singleton du service
const tokenPaymentService = new TokenPaymentService()
export default tokenPaymentService

import { create } from "zustand"
import { persist } from "zustand/middleware"

interface TokenStore {
  favorites: string[]
  watchlist: string[]
  recentlyViewed: string[]
  addFavorite: (symbol: string) => void
  removeFavorite: (symbol: string) => void
  addToWatchlist: (address: string) => void
  removeFromWatchlist: (address: string) => void
  addToRecentlyViewed: (address: string) => void
  clearRecentlyViewed: () => void
}

export const useTokenStore = create<TokenStore>()(
  persist(
    (set) => ({
      favorites: [],
      watchlist: [],
      recentlyViewed: [],

      addFavorite: (symbol: string) =>
        set((state) => ({
          favorites: state.favorites.includes(symbol) ? state.favorites : [...state.favorites, symbol],
        })),

      removeFavorite: (symbol: string) =>
        set((state) => ({
          favorites: state.favorites.filter((item) => item !== symbol),
        })),

      addToWatchlist: (address: string) =>
        set((state) => ({
          watchlist: state.watchlist.includes(address) ? state.watchlist : [...state.watchlist, address],
        })),

      removeFromWatchlist: (address: string) =>
        set((state) => ({
          watchlist: state.watchlist.filter((item) => item !== address),
        })),

      addToRecentlyViewed: (address: string) =>
        set((state) => {
          const filtered = state.recentlyViewed.filter((item) => item !== address)
          return {
            recentlyViewed: [address, ...filtered].slice(0, 10), // Garder seulement les 10 derniers
          }
        }),

      clearRecentlyViewed: () =>
        set({
          recentlyViewed: [],
        }),
    }),
    {
      name: "token-store",
    },
  ),
)

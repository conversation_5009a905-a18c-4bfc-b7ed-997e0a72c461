import { TokenCreationService } from "./token-creation-service-fixed"

/**
 * Service de démonstration pour simuler la création de tokens
 * Cela permet de contourner les problèmes de wallet et de clés privées
 */
export class TokenDemoService {
  private tokenService: TokenCreationService

  constructor() {
    // Toujours utiliser devnet pour la démo
    this.tokenService = new TokenCreationService(true)
  }

  /**
   * Simule la création d'un token
   */
  async createDemoToken(
    name: string,
    symbol: string,
    decimals: number,
    initialSupply: number,
    onProgress?: (step: string, progress: number) => void,
  ): Promise<{
    success: boolean
    mintAddress?: string
    error?: string
    name: string
    symbol: string
    decimals: number
    supply: number
  }> {
    try {
      // Simuler un délai pour rendre la démo plus réaliste
      onProgress?.("Initialisation...", 10)
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Créer le token
      const result = await this.tokenService.createToken(decimals, initialSupply, onProgress)

      if (!result.success) {
        return {
          success: false,
          error: result.error,
          name,
          symbol,
          decimals,
          supply: initialSupply,
        }
      }

      // Simuler un délai pour la finalisation
      onProgress?.("Finalisation...", 90)
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return {
        success: true,
        mintAddress: result.mintAddress,
        name,
        symbol,
        decimals,
        supply: initialSupply,
      }
    } catch (error: any) {
      console.error("Erreur lors de la création du token de démo:", error)
      return {
        success: false,
        error: error.message || "Une erreur inconnue s'est produite",
        name,
        symbol,
        decimals,
        supply: initialSupply,
      }
    }
  }

  /**
   * Simule la création d'un token sans réellement le créer
   * Utile pour les environnements où la création de token ne fonctionne pas
   */
  async createMockToken(
    name: string,
    symbol: string,
    decimals: number,
    initialSupply: number,
    onProgress?: (step: string, progress: number) => void,
  ): Promise<{
    success: boolean
    mintAddress: string
    name: string
    symbol: string
    decimals: number
    supply: number
  }> {
    // Simuler les étapes de création
    onProgress?.("Initialisation...", 10)
    await new Promise((resolve) => setTimeout(resolve, 1000))

    onProgress?.("Création du token...", 30)
    await new Promise((resolve) => setTimeout(resolve, 1500))

    onProgress?.("Création du compte associé...", 50)
    await new Promise((resolve) => setTimeout(resolve, 1000))

    onProgress?.("Mint des tokens initiaux...", 70)
    await new Promise((resolve) => setTimeout(resolve, 1500))

    onProgress?.("Finalisation...", 90)
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Générer une adresse de mint fictive
    const mockMintAddress = Array.from(
      { length: 44 },
      () => "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"[Math.floor(Math.random() * 58)],
    ).join("")

    return {
      success: true,
      mintAddress: mockMintAddress,
      name,
      symbol,
      decimals,
      supply: initialSupply,
    }
  }
}

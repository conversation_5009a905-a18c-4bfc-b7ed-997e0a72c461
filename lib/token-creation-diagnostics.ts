interface DiagnosticResult {
  issue: string
  possibleCauses: string[]
  suggestedFixes: string[]
  severity: "low" | "medium" | "high" | "critical"
}

export async function diagnoseTokenCreationIssue(error: any): Promise<DiagnosticResult> {
  console.log("Diagnosing token creation issue:", error)

  // Extract error message
  const errorMessage = error?.message || String(error)
  console.log("Error message:", errorMessage)

  // Check for common error patterns
  if (errorMessage.includes("Non-base58 character")) {
    return {
      issue: "Caractère non-base58 détecté",
      possibleCauses: [
        "Le symbole du token contient des caractères non valides en base58",
        "Le suffixe configuré contient des caractères non valides en base58",
        "Problème avec la génération de l'adresse du token",
      ],
      suggestedFixes: [
        "Utilisez uniquement des lettres et des chiffres pour le symbole du token (pas de caractères spéciaux)",
        "Vérifiez que le suffixe configuré ne contient que des caractères valides en base58 (alphanumériques sans 0, O, I, l)",
        "Essayez de créer le token avec un symbole plus simple",
      ],
      severity: "high",
    }
  }

  if (errorMessage.includes("insufficient funds")) {
    return {
      issue: "Fonds insuffisants",
      possibleCauses: [
        "Solde SOL insuffisant pour payer les frais de transaction",
        "Frais de transaction plus élevés que prévu",
      ],
      suggestedFixes: [
        "Ajoutez plus de SOL à votre portefeuille",
        "Demandez des SOL depuis le faucet Solana Devnet: https://solfaucet.com",
      ],
      severity: "high",
    }
  }

  if (errorMessage.includes("blockhash")) {
    return {
      issue: "Erreur de blockhash",
      possibleCauses: [
        "Le blockhash de la transaction a expiré",
        "Problème de connexion au réseau Solana",
        "Congestion du réseau",
      ],
      suggestedFixes: [
        "Essayez à nouveau avec une nouvelle transaction",
        "Vérifiez votre connexion internet",
        "Essayez un autre point de terminaison RPC",
      ],
      severity: "medium",
    }
  }

  if (errorMessage.includes("timeout") || errorMessage.includes("timed out")) {
    return {
      issue: "Délai d'attente dépassé",
      possibleCauses: [
        "Connexion internet lente ou instable",
        "Surcharge du point de terminaison RPC",
        "Congestion du réseau Solana",
      ],
      suggestedFixes: [
        "Vérifiez votre connexion internet",
        "Essayez un autre point de terminaison RPC",
        "Réessayez plus tard lorsque le réseau est moins congestionné",
      ],
      severity: "medium",
    }
  }

  if (errorMessage.includes("signature verification failed")) {
    return {
      issue: "Échec de vérification de signature",
      possibleCauses: [
        "Portefeuille déconnecté pendant la signature",
        "Transaction modifiée après la signature",
        "Problème de permissions du portefeuille",
      ],
      suggestedFixes: [
        "Reconnectez votre portefeuille",
        "Essayez à nouveau avec une nouvelle transaction",
        "Vérifiez les permissions de votre portefeuille",
      ],
      severity: "high",
    }
  }

  // Default diagnostic for unknown errors
  return {
    issue: "Erreur inconnue lors de la création du token",
    possibleCauses: [
      "Problème avec le réseau Solana",
      "Problème avec le portefeuille",
      "Problème avec les paramètres du token",
    ],
    suggestedFixes: [
      "Vérifiez votre connexion internet",
      "Essayez un autre point de terminaison RPC",
      "Vérifiez que votre portefeuille est correctement configuré",
      "Essayez avec des paramètres de token plus simples",
    ],
    severity: "medium",
  }
}

import { Connection, PublicKey, clusterApiUrl } from "@solana/web3.js"
import { getMint } from "@solana/spl-token"

export interface TokenData {
  address: string
  name: string
  symbol: string
  decimals: number
  totalSupply: number
  circulatingSupply: number
  price: number
  priceChange24h: number
  marketCap: number
  volume24h: number
  holders: number
  createdAt: string
  network: "devnet" | "mainnet-beta"
}

export class TokenDataService {
  /**
   * Récupère les données d'un token à partir de son adresse
   */
  static async getTokenData(tokenAddress: string, network: "devnet" | "mainnet-beta" = "devnet"): Promise<TokenData> {
    try {
      // Connexion à Solana
      const endpoint = clusterApiUrl(network)
      const connection = new Connection(endpoint, "confirmed")

      // Vérifier si le token existe
      const mintPublicKey = new PublicKey(tokenAddress)
      const accountInfo = await connection.getAccountInfo(mintPublicKey)

      if (!accountInfo) {
        throw new Error("Token non trouvé")
      }

      // Récupérer les informations du mint
      const mintInfo = await getMint(connection, mintPublicKey)

      // Récupérer les métadonnées du token (nom, symbole)
      // Dans une implémentation réelle, vous utiliseriez Metaplex ou une autre API
      // Pour cette démonstration, nous allons générer des données basées sur l'adresse

      const seed = tokenAddress.slice(0, 8)
      const seedValue = Number.parseInt(seed, 16) / 0xffffffff // Normaliser entre 0 et 1

      // Générer un nom et un symbole basés sur l'adresse
      const name = `Token ${tokenAddress.slice(0, 6)}`
      const symbol = `T${tokenAddress.slice(0, 3).toUpperCase()}`

      // Calculer l'offre totale
      const totalSupply = Number(mintInfo.supply) / Math.pow(10, mintInfo.decimals)

      // Générer un prix basé sur l'adresse (pour être cohérent)
      const price = 0.01 + seedValue * 10

      // Générer d'autres données basées sur l'adresse
      const priceChange24h = (seedValue - 0.5) * 20 // Entre -10% et +10%
      const marketCap = price * totalSupply
      const volume24h = marketCap * (0.05 + seedValue * 0.2) // Entre 5% et 25% du market cap
      const holders = Math.floor(100 + seedValue * 10000) // Entre 100 et 10100

      // Générer une date de création
      const now = new Date()
      const createdAtDate = new Date(now.getTime() - (1 + seedValue * 90) * 24 * 60 * 60 * 1000) // Entre 1 et 91 jours

      return {
        address: tokenAddress,
        name,
        symbol,
        decimals: mintInfo.decimals,
        totalSupply,
        circulatingSupply: totalSupply * 0.8, // 80% de l'offre totale
        price,
        priceChange24h,
        marketCap,
        volume24h,
        holders,
        createdAt: createdAtDate.toISOString(),
        network,
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des données du token:", error)
      throw error
    }
  }

  /**
   * Récupère les détenteurs d'un token
   */
  static async getTokenHolders(
    tokenAddress: string,
    network: "devnet" | "mainnet-beta" = "devnet",
    limit = 10,
  ): Promise<{ address: string; balance: number; percentage: number }[]> {
    try {
      // Connexion à Solana
      const endpoint = clusterApiUrl(network)
      const connection = new Connection(endpoint, "confirmed")

      // Vérifier si le token existe
      const mintPublicKey = new PublicKey(tokenAddress)
      const accountInfo = await connection.getAccountInfo(mintPublicKey)

      if (!accountInfo) {
        throw new Error("Token non trouvé")
      }

      // Récupérer les informations du mint
      const mintInfo = await getMint(connection, mintPublicKey)

      // Récupérer les comptes de token
      // Dans une implémentation réelle, vous utiliseriez une API comme Solscan
      // Pour cette démonstration, nous allons générer des données basées sur l'adresse

      const seed = tokenAddress.slice(0, 8)
      const seedValue = Number.parseInt(seed, 16) / 0xffffffff // Normaliser entre 0 et 1

      // Générer des détenteurs
      const holders = []
      const totalSupply = Number(mintInfo.supply) / Math.pow(10, mintInfo.decimals)

      for (let i = 0; i < limit; i++) {
        const holderSeed = `${tokenAddress}-${i}`
        const holderSeedValue = Number.parseInt(holderSeed.slice(0, 8), 16) / 0xffffffff

        // Générer une adresse de détenteur
        const address = `${i === 0 ? "Creator" : `Holder ${i}`}: ${new PublicKey(Buffer.from(holderSeed)).toString().slice(0, 8)}...`

        // Générer un solde
        let percentage = 0
        if (i === 0) {
          // Le créateur détient une grande partie
          percentage = 0.2 + seedValue * 0.3 // Entre 20% et 50%
        } else {
          // Les autres détenteurs se partagent le reste
          percentage = ((1 - (0.2 + seedValue * 0.3)) / (limit - 1)) * (0.5 + holderSeedValue)
        }

        const balance = totalSupply * percentage

        holders.push({
          address,
          balance,
          percentage: percentage * 100,
        })
      }

      // Trier par solde décroissant
      return holders.sort((a, b) => b.balance - a.balance)
    } catch (error) {
      console.error("Erreur lors de la récupération des détenteurs du token:", error)
      throw error
    }
  }

  /**
   * Récupère les transactions d'un token
   */
  static async getTokenTransactions(
    tokenAddress: string,
    network: "devnet" | "mainnet-beta" = "devnet",
    limit = 10,
  ): Promise<{ signature: string; type: string; amount: number; from: string; to: string; timestamp: number }[]> {
    try {
      // Connexion à Solana
      const endpoint = clusterApiUrl(network)
      const connection = new Connection(endpoint, "confirmed")

      // Vérifier si le token existe
      const mintPublicKey = new PublicKey(tokenAddress)
      const accountInfo = await connection.getAccountInfo(mintPublicKey)

      if (!accountInfo) {
        throw new Error("Token non trouvé")
      }

      // Récupérer les transactions
      // Dans une implémentation réelle, vous utiliseriez une API comme Solscan
      // Pour cette démonstration, nous allons générer des données basées sur l'adresse

      const seed = tokenAddress.slice(0, 8)
      const seedValue = Number.parseInt(seed, 16) / 0xffffffff // Normaliser entre 0 et 1

      // Générer des transactions
      const transactions = []
      const now = Date.now()

      for (let i = 0; i < limit; i++) {
        const txSeed = `${tokenAddress}-${i}`
        const txSeedValue = Number.parseInt(txSeed.slice(0, 8), 16) / 0xffffffff

        // Générer une signature
        const signature = new PublicKey(Buffer.from(txSeed)).toString()

        // Générer un type de transaction
        const types = ["Transfer", "Mint", "Burn"]
        const type = types[Math.floor(txSeedValue * types.length)]

        // Générer un montant
        const amount = 100 + txSeedValue * 10000

        // Générer des adresses
        const from = `${type === "Mint" ? "Mint Authority" : `Wallet ${i % 5}`}: ${new PublicKey(Buffer.from(`${txSeed}-from`)).toString().slice(0, 8)}...`
        const to = `${type === "Burn" ? "Burn Address" : `Wallet ${(i + 2) % 5}`}: ${new PublicKey(Buffer.from(`${txSeed}-to`)).toString().slice(0, 8)}...`

        // Générer un timestamp
        const timestamp = now - i * (60 * 60 * 1000) // Une transaction par heure

        transactions.push({
          signature,
          type,
          amount,
          from,
          to,
          timestamp,
        })
      }

      return transactions
    } catch (error) {
      console.error("Erreur lors de la récupération des transactions du token:", error)
      throw error
    }
  }
}

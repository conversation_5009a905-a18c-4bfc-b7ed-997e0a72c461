import { Connection } from "@solana/web3.js"
import { envConfig } from "./env-config"

// List of reliable public RPC endpoints
const FALLBACK_RPC_ENDPOINTS = [
  "https://api.devnet.solana.com",
  "https://devnet.genesysgo.net",
  "https://devnet.helius-rpc.com/?api-key=15319106-2ae4-418b-a53a-3c6da6a4ed05", // Public demo key
]

export interface RpcConnectionStatus {
  connected: boolean
  endpoint: string
  latency?: number
  version?: string
  error?: string
}

export async function checkRpcConnection(endpoint: string): Promise<RpcConnectionStatus> {
  const startTime = performance.now()
  try {
    const connection = new Connection(endpoint, "confirmed")
    const versionInfo = await connection.getVersion()
    const endTime = performance.now()

    return {
      connected: true,
      endpoint,
      latency: Math.round(endTime - startTime),
      version: `${versionInfo["solana-core"]}`,
    }
  } catch (error: any) {
    return {
      connected: false,
      endpoint,
      error: error.message || "Unknown connection error",
    }
  }
}

export async function findBestRpcEndpoint(): Promise<string | null> {
  console.log("Finding best RPC endpoint...")

  // Start with the configured endpoint
  const endpoints = [envConfig.SOLANA_RPC_URL, ...FALLBACK_RPC_ENDPOINTS]

  // Check all endpoints in parallel
  const results = await Promise.all(endpoints.map((endpoint) => checkRpcConnection(endpoint)))

  // Filter for connected endpoints and sort by latency
  const connectedEndpoints = results
    .filter((result) => result.connected)
    .sort((a, b) => (a.latency || Number.POSITIVE_INFINITY) - (b.latency || Number.POSITIVE_INFINITY))

  if (connectedEndpoints.length === 0) {
    console.error("No working RPC endpoints found")
    return null
  }

  const bestEndpoint = connectedEndpoints[0]
  console.log(`Best RPC endpoint: ${bestEndpoint.endpoint} (${bestEndpoint.latency}ms)`)
  return bestEndpoint.endpoint
}

export async function getReliableConnection(): Promise<Connection | null> {
  const bestEndpoint = await findBestRpcEndpoint()
  if (!bestEndpoint) {
    return null
  }

  return new Connection(bestEndpoint, "confirmed")
}

export async function runRpcDiagnostics(): Promise<{
  success: boolean
  results: {
    testedEndpoints: RpcConnectionStatus[]
    bestEndpoint: string | null
    recommendations: string[]
  }
}> {
  try {
    // Test all endpoints
    const endpoints = [envConfig.SOLANA_RPC_URL, ...FALLBACK_RPC_ENDPOINTS]
    const results = await Promise.all(endpoints.map((endpoint) => checkRpcConnection(endpoint)))

    // Find the best endpoint
    const bestEndpoint = await findBestRpcEndpoint()

    // Generate recommendations
    const recommendations: string[] = []

    if (!results.some((result) => result.connected)) {
      recommendations.push("Aucun point de terminaison RPC ne fonctionne. Vérifiez votre connexion internet.")
    } else if (!results[0].connected && bestEndpoint) {
      recommendations.push(`Le point de terminaison RPC configuré ne fonctionne pas. Utilisez plutôt: ${bestEndpoint}`)
    }

    if (results[0].connected && results[0].latency && results[0].latency > 500) {
      recommendations.push(
        "Le point de terminaison RPC configuré a une latence élevée, ce qui peut affecter les performances.",
      )
    }

    return {
      success: true,
      results: {
        testedEndpoints: results,
        bestEndpoint,
        recommendations,
      },
    }
  } catch (error: any) {
    return {
      success: false,
      results: {
        testedEndpoints: [],
        bestEndpoint: null,
        recommendations: [error.message || "Erreur lors de l'exécution des diagnostics RPC"],
      },
    }
  }
}

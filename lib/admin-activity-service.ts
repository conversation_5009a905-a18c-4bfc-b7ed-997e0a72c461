"use server"

import { cookies } from "next/headers"
import { isAdmin } from "./admin-service"

export type AdminActivity = {
  id: string
  adminWallet: string
  action: string
  details: string
  timestamp: Date
  ipAddress?: string
}

// Simulation d'une base de données pour les activités admin
let activityLog: AdminActivity[] = []

/**
 * Enregistre une nouvelle activité admin
 */
export async function logAdminActivity(action: string, details: string): Promise<void> {
  const cookieStore = cookies()
  const walletAddress = cookieStore.get("walletAddress")?.value

  if (!walletAddress || !isAdmin(walletAddress)) {
    throw new Error("Unauthorized: Only admins can log activities")
  }

  const activity: AdminActivity = {
    id: crypto.randomUUID(),
    adminWallet: walletAddress,
    action,
    details,
    timestamp: new Date(),
  }

  activityLog.push(activity)

  // Dans une implémentation réelle, vous enregistreriez ceci dans une base de données
  console.log(`Admin activity logged: ${action} by ${walletAddress}`)
}

/**
 * R<PERSON>cupère les activités admin, avec pagination et filtrage optionnels
 */
export async function getAdminActivities(
  page = 1,
  limit = 10,
  filterAdmin?: string,
): Promise<{ activities: AdminActivity[]; total: number }> {
  const cookieStore = cookies()
  const walletAddress = cookieStore.get("walletAddress")?.value

  if (!walletAddress || !isAdmin(walletAddress)) {
    throw new Error("Unauthorized: Only admins can view activity logs")
  }

  let filteredActivities = [...activityLog]

  if (filterAdmin) {
    filteredActivities = filteredActivities.filter((a) => a.adminWallet === filterAdmin)
  }

  // Trier par timestamp décroissant (plus récent d'abord)
  filteredActivities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

  const startIndex = (page - 1) * limit
  const paginatedActivities = filteredActivities.slice(startIndex, startIndex + limit)

  return {
    activities: paginatedActivities,
    total: filteredActivities.length,
  }
}

/**
 * Efface les journaux d'activité plus anciens qu'une certaine date
 */
export async function clearOldActivityLogs(olderThan: Date): Promise<number> {
  const cookieStore = cookies()
  const walletAddress = cookieStore.get("walletAddress")?.value

  if (!walletAddress || !isAdmin(walletAddress)) {
    throw new Error("Unauthorized: Only admins can clear activity logs")
  }

  const initialCount = activityLog.length
  activityLog = activityLog.filter((activity) => activity.timestamp > olderThan)

  return initialCount - activityLog.length
}

/**
 * Récupère les statistiques d'activité admin
 */
export async function getAdminActivityStats(): Promise<{
  totalActivities: number
  uniqueAdmins: number
  activityByType: Record<string, number>
}> {
  const cookieStore = cookies()
  const walletAddress = cookieStore.get("walletAddress")?.value

  if (!walletAddress || !isAdmin(walletAddress)) {
    throw new Error("Unauthorized: Only admins can view activity stats")
  }

  const uniqueAdmins = new Set(activityLog.map((a) => a.adminWallet)).size

  const activityByType: Record<string, number> = {}
  activityLog.forEach((activity) => {
    if (!activityByType[activity.action]) {
      activityByType[activity.action] = 0
    }
    activityByType[activity.action]++
  })

  return {
    totalActivities: activityLog.length,
    uniqueAdmins,
    activityByType,
  }
}

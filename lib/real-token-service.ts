import {
  Connection,
  Keypair,
  PublicKey,
  Transaction,
  sendAndConfirmTransaction,
  LAMPORTS_PER_SOL,
} from "@solana/web3.js"
import {
  createMint,
  getOrCreateAssociatedTokenAccount,
  mintTo,
  createSetAuthorityInstruction,
  AuthorityType,
  getMint,
  freezeAccount,
  thawAccount,
  TOKEN_PROGRAM_ID,
} from "@solana/spl-token"
import * as bs58 from "bs58"
import type { TokenMetadata } from "./token-types"
import { createTokenMetadata } from "./token-metadata-service"
import { envConfig } from "./env-config"
import { getNetworkById } from "./network-config"

export interface TokenCreationParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: number
  maxSupply?: number
  ownerAddress: string
  payerPrivateKey: string
  metadata?: TokenMetadata
  isMintable?: boolean
  isBurnable?: boolean
  isFreezable?: boolean
  isPausable?: boolean
  isTransferTaxable?: boolean
  transferTaxRate?: number
  maxTxAmount?: number
  maxWalletAmount?: number
  antiBot?: boolean
  antiDump?: boolean
  networkId?: string
}

export interface TokenCreationResult {
  success: boolean
  mintAddress?: string
  tokenAccount?: string
  signature?: string
  error?: string
  metadataAddress?: string
  freezeAuthority?: string
  mintAuthority?: string
}

export interface TokenFreezeParams {
  mintAddress: string
  targetAddress: string
  freezeAuthority: string
  networkId?: string
}

export interface TokenThawParams {
  mintAddress: string
  targetAddress: string
  freezeAuthority: string
  networkId?: string
}

export class RealTokenService {
  /**
   * Crée un nouveau token sur la blockchain Solana
   */
  public async createToken(params: TokenCreationParams): Promise<TokenCreationResult> {
    try {
      console.log("Création d'un nouveau token avec les paramètres:", {
        name: params.name,
        symbol: params.symbol,
        decimals: params.decimals,
        initialSupply: params.initialSupply,
        ownerAddress: params.ownerAddress,
        isMintable: params.isMintable,
        isFreezable: params.isFreezable,
      })

      // Obtenir la connexion au réseau Solana
      const networkId = params.networkId || "solana-devnet"
      const network = getNetworkById(networkId)
      const rpcUrl = network?.rpcUrl || envConfig.SOLANA_RPC_URL

      if (!rpcUrl) {
        throw new Error("URL RPC Solana non configurée")
      }

      const connection = new Connection(rpcUrl, "confirmed")

      // Créer le keypair du payeur à partir de la clé privée
      if (!params.payerPrivateKey) {
        throw new Error("Clé privée du payeur non fournie")
      }

      const payerKeypair = Keypair.fromSecretKey(bs58.decode(params.payerPrivateKey))
      console.log("Payeur:", payerKeypair.publicKey.toString())

      // Vérifier le solde du payeur
      const payerBalance = await connection.getBalance(payerKeypair.publicKey)
      console.log(`Solde du payeur: ${payerBalance / LAMPORTS_PER_SOL} SOL`)

      const minimumBalance = 0.05 * LAMPORTS_PER_SOL // 0.05 SOL minimum
      if (payerBalance < minimumBalance) {
        throw new Error(`Solde insuffisant: ${payerBalance / LAMPORTS_PER_SOL} SOL. Minimum requis: 0.05 SOL`)
      }

      // Créer un nouveau mint
      console.log("Création du mint...")
      const mintKeypair = Keypair.generate()
      console.log("Adresse du mint:", mintKeypair.publicKey.toString())

      // Déterminer les autorités
      const mintAuthority = params.isMintable ? new PublicKey(params.ownerAddress) : null
      const freezeAuthority = params.isFreezable ? new PublicKey(params.ownerAddress) : null

      // Créer le mint
      const mint = await createMint(
        connection,
        payerKeypair,
        payerKeypair.publicKey, // Autorité temporaire pour la création
        freezeAuthority,
        params.decimals,
      )

      console.log("Mint créé:", mint.toString())

      // Créer un compte de token pour le propriétaire
      const ownerPublicKey = new PublicKey(params.ownerAddress)
      const tokenAccount = await getOrCreateAssociatedTokenAccount(connection, payerKeypair, mint, ownerPublicKey)

      console.log("Compte de token créé:", tokenAccount.address.toString())

      // Minter l'offre initiale
      const initialAmount = params.initialSupply * Math.pow(10, params.decimals)
      console.log(`Minting de ${params.initialSupply} tokens (${initialAmount} unités)...`)

      const mintSignature = await mintTo(
        connection,
        payerKeypair,
        mint,
        tokenAccount.address,
        payerKeypair.publicKey, // Utiliser le payeur comme autorité temporaire
        BigInt(initialAmount),
      )

      console.log("Tokens mintés avec succès:", mintSignature)

      // Créer les métadonnées du token
      let metadataAddress = ""
      if (params.metadata) {
        console.log("Création des métadonnées du token...")
        const metadataResult = await createTokenMetadata(
          connection,
          mintKeypair,
          ownerPublicKey,
          params.metadata.name,
          params.metadata.symbol,
          params.metadata.description || "",
          params.metadata.image || "",
        )

        if (metadataResult.success) {
          metadataAddress = "metadata_address_placeholder" // Dans une implémentation réelle, cela serait l'adresse des métadonnées
          console.log("Métadonnées créées avec succès")
        } else {
          console.warn("Échec de la création des métadonnées:", metadataResult.error)
        }
      }

      // Transférer l'autorité de mint au propriétaire si le token est mintable
      let transferAuthoritySignature = ""
      if (params.isMintable) {
        console.log("Transfert de l'autorité de mint au propriétaire...")
        const transaction = new Transaction().add(
          createSetAuthorityInstruction(
            mint,
            payerKeypair.publicKey,
            AuthorityType.MintTokens,
            ownerPublicKey,
            [],
            TOKEN_PROGRAM_ID,
          ),
        )

        transferAuthoritySignature = await sendAndConfirmTransaction(connection, transaction, [payerKeypair])

        console.log("Autorité de mint transférée avec succès:", transferAuthoritySignature)
      } else {
        // Si le token n'est pas mintable, désactiver l'autorité de mint
        console.log("Désactivation de l'autorité de mint...")
        const transaction = new Transaction().add(
          createSetAuthorityInstruction(
            mint,
            payerKeypair.publicKey,
            AuthorityType.MintTokens,
            null,
            [],
            TOKEN_PROGRAM_ID,
          ),
        )

        transferAuthoritySignature = await sendAndConfirmTransaction(connection, transaction, [payerKeypair])

        console.log("Autorité de mint désactivée avec succès:", transferAuthoritySignature)
      }

      return {
        success: true,
        mintAddress: mint.toString(),
        tokenAccount: tokenAccount.address.toString(),
        signature: mintSignature,
        metadataAddress,
        freezeAuthority: params.isFreezable ? params.ownerAddress : undefined,
        mintAuthority: params.isMintable ? params.ownerAddress : undefined,
      }
    } catch (error: any) {
      console.error("Erreur lors de la création du token:", error)
      return {
        success: false,
        error: error.message || "Une erreur inconnue s'est produite lors de la création du token",
      }
    }
  }

  /**
   * Gèle un compte de token
   */
  public async freezeToken(
    params: TokenFreezeParams,
  ): Promise<{ success: boolean; signature?: string; error?: string }> {
    try {
      console.log("Gel du compte de token:", params.targetAddress)

      // Obtenir la connexion au réseau Solana
      const networkId = params.networkId || "solana-devnet"
      const network = getNetworkById(networkId)
      const rpcUrl = network?.rpcUrl || envConfig.SOLANA_RPC_URL

      if (!rpcUrl) {
        throw new Error("URL RPC Solana non configurée")
      }

      const connection = new Connection(rpcUrl, "confirmed")

      // Créer le keypair de l'autorité de gel à partir de la clé privée
      const freezeAuthorityKeypair = Keypair.fromSecretKey(bs58.decode(params.freezeAuthority))
      console.log("Autorité de gel:", freezeAuthorityKeypair.publicKey.toString())

      // Vérifier que le mint existe et a une autorité de gel
      const mintPublicKey = new PublicKey(params.mintAddress)
      const mintInfo = await getMint(connection, mintPublicKey)

      if (!mintInfo.freezeAuthority) {
        throw new Error("Ce token n'a pas d'autorité de gel")
      }

      if (!mintInfo.freezeAuthority.equals(freezeAuthorityKeypair.publicKey)) {
        throw new Error("L'autorité de gel fournie ne correspond pas à celle du token")
      }

      // Obtenir le compte de token associé
      const targetPublicKey = new PublicKey(params.targetAddress)
      const tokenAccountAddress = await getOrCreateAssociatedTokenAccount(
        connection,
        freezeAuthorityKeypair,
        mintPublicKey,
        targetPublicKey,
      )

      // Geler le compte
      const signature = await freezeAccount(
        connection,
        freezeAuthorityKeypair,
        tokenAccountAddress.address,
        mintPublicKey,
        freezeAuthorityKeypair.publicKey,
      )

      console.log("Compte gelé avec succès:", signature)

      return {
        success: true,
        signature,
      }
    } catch (error: any) {
      console.error("Erreur lors du gel du compte:", error)
      return {
        success: false,
        error: error.message || "Une erreur inconnue s'est produite lors du gel du compte",
      }
    }
  }

  /**
   * Dégèle un compte de token
   */
  public async thawToken(params: TokenThawParams): Promise<{ success: boolean; signature?: string; error?: string }> {
    try {
      console.log("Dégel du compte de token:", params.targetAddress)

      // Obtenir la connexion au réseau Solana
      const networkId = params.networkId || "solana-devnet"
      const network = getNetworkById(networkId)
      const rpcUrl = network?.rpcUrl || envConfig.SOLANA_RPC_URL

      if (!rpcUrl) {
        throw new Error("URL RPC Solana non configurée")
      }

      const connection = new Connection(rpcUrl, "confirmed")

      // Créer le keypair de l'autorité de gel à partir de la clé privée
      const freezeAuthorityKeypair = Keypair.fromSecretKey(bs58.decode(params.freezeAuthority))
      console.log("Autorité de gel:", freezeAuthorityKeypair.publicKey.toString())

      // Vérifier que le mint existe et a une autorité de gel
      const mintPublicKey = new PublicKey(params.mintAddress)
      const mintInfo = await getMint(connection, mintPublicKey)

      if (!mintInfo.freezeAuthority) {
        throw new Error("Ce token n'a pas d'autorité de gel")
      }

      if (!mintInfo.freezeAuthority.equals(freezeAuthorityKeypair.publicKey)) {
        throw new Error("L'autorité de gel fournie ne correspond pas à celle du token")
      }

      // Obtenir le compte de token associé
      const targetPublicKey = new PublicKey(params.targetAddress)
      const tokenAccountAddress = await getOrCreateAssociatedTokenAccount(
        connection,
        freezeAuthorityKeypair,
        mintPublicKey,
        targetPublicKey,
      )

      // Dégeler le compte
      const signature = await thawAccount(
        connection,
        freezeAuthorityKeypair,
        tokenAccountAddress.address,
        mintPublicKey,
        freezeAuthorityKeypair.publicKey,
      )

      console.log("Compte dégelé avec succès:", signature)

      return {
        success: true,
        signature,
      }
    } catch (error: any) {
      console.error("Erreur lors du dégel du compte:", error)
      return {
        success: false,
        error: error.message || "Une erreur inconnue s'est produite lors du dégel du compte",
      }
    }
  }

  /**
   * Vérifie si un compte de token est gelé
   */
  public async isTokenFrozen(
    mintAddress: string,
    accountAddress: string,
    networkId?: string,
  ): Promise<{ isFrozen: boolean; error?: string }> {
    try {
      // Obtenir la connexion au réseau Solana
      const network = getNetworkById(networkId || "solana-devnet")
      const rpcUrl = network?.rpcUrl || envConfig.SOLANA_RPC_URL

      if (!rpcUrl) {
        throw new Error("URL RPC Solana non configurée")
      }

      const connection = new Connection(rpcUrl, "confirmed")

      // Obtenir les informations du compte de token
      const accountInfo = await connection.getAccountInfo(new PublicKey(accountAddress))

      if (!accountInfo) {
        throw new Error("Compte de token non trouvé")
      }

      // Analyser les données du compte pour déterminer s'il est gelé
      // Note: Ceci est une simplification, dans une implémentation réelle,
      // vous devriez utiliser les fonctions appropriées du SDK SPL Token
      const accountData = accountInfo.data
      const isFrozen = accountData[44] === 1 // Le bit de gel est à l'offset 44

      return { isFrozen }
    } catch (error: any) {
      console.error("Erreur lors de la vérification du statut de gel:", error)
      return {
        isFrozen: false,
        error: error.message || "Une erreur inconnue s'est produite lors de la vérification du statut de gel",
      }
    }
  }

  /**
   * Calcule les frais estimés pour la création d'un token
   */
  public async estimateTokenCreationFees(
    networkId?: string,
  ): Promise<{ fees: { [key: string]: number }; total: number }> {
    // Obtenir la connexion au réseau Solana
    const network = getNetworkById(networkId || "solana-devnet")
    const rpcUrl = network?.rpcUrl || envConfig.SOLANA_RPC_URL

    if (!rpcUrl) {
      throw new Error("URL RPC Solana non configurée")
    }

    const connection = new Connection(rpcUrl, "confirmed")

    // Calculer les frais estimés
    const rentExemptMint = await connection.getMinimumBalanceForRentExemption(82)
    const rentExemptTokenAccount = await connection.getMinimumBalanceForRentExemption(165)
    const rentExemptMetadata = await connection.getMinimumBalanceForRentExemption(679)

    // Frais de transaction estimés (3 transactions: création du mint, minting, métadonnées)
    const transactionFees = 3 * 5000

    // Frais de la plateforme
    const platformFee = 0.05 * LAMPORTS_PER_SOL // 0.05 SOL

    const fees = {
      rentExemptMint: rentExemptMint / LAMPORTS_PER_SOL,
      rentExemptTokenAccount: rentExemptTokenAccount / LAMPORTS_PER_SOL,
      rentExemptMetadata: rentExemptMetadata / LAMPORTS_PER_SOL,
      transactionFees: transactionFees / LAMPORTS_PER_SOL,
      platformFee: platformFee / LAMPORTS_PER_SOL,
    }

    const total = Object.values(fees).reduce((sum, fee) => sum + fee, 0)

    return { fees, total }
  }
}

// Exporter une instance singleton du service
export const realTokenService = new RealTokenService()

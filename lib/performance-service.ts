// Types pour les métriques de performance
export interface PerformanceMetric {
  id: string
  name: string
  value: number
  unit: string
  status: "good" | "warning" | "critical"
  trend: "up" | "down" | "stable"
  history: { timestamp: string; value: number }[]
}

// Types pour l'utilisation des ressources
export interface ResourceUsage {
  id: string
  name: string
  current: number
  max: number
  unit: string
  status: "good" | "warning" | "critical"
}

// Types pour les recommandations d'optimisation
export interface OptimizationRecommendation {
  id: string
  title: string
  description: string
  impact: "low" | "medium" | "high"
  status: "pending" | "in_progress" | "completed" | "failed"
  metricId?: string
  resourceId?: string
}

// Service de gestion des performances
export class PerformanceService {
  private static instance: PerformanceService
  private metrics: PerformanceMetric[] = []
  private resources: ResourceUsage[] = []
  private recommendations: OptimizationRecommendation[] = []
  private lastUpdated: Date = new Date()
  private updateInterval: NodeJS.Timeout | null = null

  private constructor() {
    // Initialisation avec des valeurs par défaut
    this.initializeMetrics()
    this.initializeResources()
    this.generateRecommendations()

    // Mettre à jour les métriques toutes les 5 secondes
    this.startUpdates()
  }

  public static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService()
    }
    return PerformanceService.instance
  }

  // Initialiser les métriques
  private initializeMetrics(): void {
    this.metrics = [
      {
        id: "api_response_time",
        name: "Temps de réponse API",
        value: 120,
        unit: "ms",
        status: "good",
        trend: "down",
        history: Array.from({ length: 24 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 3600000).toISOString(),
          value: 120 + Math.random() * 50 - 25,
        })),
      },
      {
        id: "token_creation_time",
        name: "Temps de création de token",
        value: 3.2,
        unit: "s",
        status: "warning",
        trend: "up",
        history: Array.from({ length: 24 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 3600000).toISOString(),
          value: 3.2 + Math.random() * 1 - 0.5,
        })),
      },
      {
        id: "transaction_throughput",
        name: "Débit de transactions",
        value: 450,
        unit: "tx/min",
        status: "good",
        trend: "stable",
        history: Array.from({ length: 24 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 3600000).toISOString(),
          value: 450 + Math.random() * 50 - 25,
        })),
      },
      {
        id: "database_query_time",
        name: "Temps de requête BDD",
        value: 85,
        unit: "ms",
        status: "good",
        trend: "down",
        history: Array.from({ length: 24 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 3600000).toISOString(),
          value: 85 + Math.random() * 20 - 10,
        })),
      },
      {
        id: "blockchain_sync_time",
        name: "Temps de synchronisation blockchain",
        value: 5.8,
        unit: "s",
        status: "warning",
        trend: "up",
        history: Array.from({ length: 24 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 3600000).toISOString(),
          value: 5.8 + Math.random() * 2 - 1,
        })),
      },
      {
        id: "page_load_time",
        name: "Temps de chargement des pages",
        value: 1.2,
        unit: "s",
        status: "good",
        trend: "down",
        history: Array.from({ length: 24 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 3600000).toISOString(),
          value: 1.2 + Math.random() * 0.5 - 0.25,
        })),
      },
    ]
  }

  // Initialiser les ressources
  private initializeResources(): void {
    this.resources = [
      {
        id: "cpu",
        name: "CPU",
        current: 35,
        max: 100,
        unit: "%",
        status: "good",
      },
      {
        id: "memory",
        name: "Mémoire",
        current: 6.2,
        max: 8,
        unit: "GB",
        status: "warning",
      },
      {
        id: "disk",
        name: "Espace disque",
        current: 120,
        max: 500,
        unit: "GB",
        status: "good",
      },
      {
        id: "bandwidth",
        name: "Bande passante",
        current: 75,
        max: 100,
        unit: "Mbps",
        status: "good",
      },
      {
        id: "database_connections",
        name: "Connexions BDD",
        current: 85,
        max: 100,
        unit: "",
        status: "warning",
      },
      {
        id: "rpc_connections",
        name: "Connexions RPC",
        current: 120,
        max: 200,
        unit: "",
        status: "good",
      },
    ]
  }

  // Générer des recommandations d'optimisation
  private generateRecommendations(): void {
    this.recommendations = []

    // Vérifier les métriques
    for (const metric of this.metrics) {
      if (metric.status === "warning" || metric.status === "critical") {
        this.recommendations.push({
          id: `optimize_${metric.id}`,
          title: `Optimiser ${metric.name.toLowerCase()}`,
          description: `Le ${metric.name.toLowerCase()} est ${metric.status === "warning" ? "élevé" : "critique"}. Envisagez d'optimiser cette métrique pour améliorer les performances.`,
          impact: metric.status === "critical" ? "high" : "medium",
          status: "pending",
          metricId: metric.id,
        })
      }
    }

    // Vérifier les ressources
    for (const resource of this.resources) {
      if (resource.status === "warning" || resource.status === "critical") {
        this.recommendations.push({
          id: `optimize_${resource.id}`,
          title: `Optimiser l'utilisation de ${resource.name.toLowerCase()}`,
          description: `L'utilisation de ${resource.name.toLowerCase()} est ${resource.status === "warning" ? "élevée" : "critique"} (${((resource.current / resource.max) * 100).toFixed(1)}%). Envisagez d'optimiser cette ressource.`,
          impact: resource.status === "critical" ? "high" : "medium",
          status: "pending",
          resourceId: resource.id,
        })
      }
    }
  }

  // Démarrer les mises à jour périodiques
  private startUpdates(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
    }

    this.updateInterval = setInterval(() => {
      this.updateMetrics()
      this.updateResources()
      this.generateRecommendations()
      this.lastUpdated = new Date()
    }, 5000)
  }

  // Arrêter les mises à jour périodiques
  private stopUpdates(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
  }

  // Mettre à jour les métriques
  private updateMetrics(): void {
    this.metrics = this.metrics.map((metric) => {
      const newValue = metric.value + (Math.random() * 0.1 - 0.05) * metric.value
      const newHistory = [{ timestamp: new Date().toISOString(), value: newValue }, ...metric.history.slice(0, 23)]

      // Déterminer la tendance
      const avg = newHistory.slice(0, 3).reduce((sum, h) => sum + h.value, 0) / 3
      const prevAvg = newHistory.slice(3, 6).reduce((sum, h) => sum + h.value, 0) / 3
      const trend = avg < prevAvg * 0.98 ? "down" : avg > prevAvg * 1.02 ? "up" : "stable"

      // Déterminer le statut
      let status: "good" | "warning" | "critical" = "good"
      if (metric.id.includes("time") || metric.id.includes("Time")) {
        // Pour les métriques de temps, plus bas est mieux
        status = newValue < 100 ? "good" : newValue < 200 ? "warning" : "critical"
      } else {
        // Pour les autres métriques, plus haut est mieux
        status = newValue > 400 ? "good" : newValue > 200 ? "warning" : "critical"
      }

      return {
        ...metric,
        value: newValue,
        history: newHistory,
        trend,
        status,
      }
    })
  }

  // Mettre à jour les ressources
  private updateResources(): void {
    this.resources = this.resources.map((resource) => {
      const newCurrent = Math.min(
        resource.max,
        Math.max(0, resource.current + (Math.random() * 0.05 - 0.025) * resource.current),
      )

      // Déterminer le statut
      const percentage = (newCurrent / resource.max) * 100
      const status = percentage > 90 ? "critical" : percentage > 70 ? "warning" : "good"

      return {
        ...resource,
        current: newCurrent,
        status: status as "good" | "warning" | "critical",
      }
    })
  }

  // Appliquer une optimisation
  public async applyOptimization(recommendationId: string): Promise<boolean> {
    const recommendation = this.recommendations.find((r) => r.id === recommendationId)
    if (!recommendation) {
      return false
    }

    // Mettre à jour le statut
    recommendation.status = "in_progress"

    // Simuler une optimisation
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Mettre à jour la métrique ou la ressource
    if (recommendation.metricId) {
      const metric = this.metrics.find((m) => m.id === recommendation.metricId)
      if (metric) {
        if (metric.id.includes("time") || metric.id.includes("Time")) {
          // Pour les métriques de temps, réduire la valeur
          metric.value *= 0.7
        } else {
          // Pour les autres métriques, augmenter la valeur
          metric.value *= 1.3
        }
        metric.status = "good"
        metric.trend = metric.id.includes("time") || metric.id.includes("Time") ? "down" : "up"
      }
    }

    if (recommendation.resourceId) {
      const resource = this.resources.find((r) => r.id === recommendation.resourceId)
      if (resource) {
        resource.current *= 0.7
        resource.status = "good"
      }
    }

    // Mettre à jour le statut
    recommendation.status = "completed"

    return true
  }

  // Appliquer toutes les optimisations
  public async applyAllOptimizations(): Promise<boolean> {
    // Mettre à jour le statut de toutes les recommandations
    this.recommendations.forEach((r) => {
      r.status = "in_progress"
    })

    // Simuler une optimisation
    await new Promise((resolve) => setTimeout(resolve, 5000))

    // Mettre à jour toutes les métriques
    this.metrics = this.metrics.map((metric) => {
      let newValue = metric.value
      if (metric.id.includes("time") || metric.id.includes("Time")) {
        // Pour les métriques de temps, réduire la valeur
        newValue *= 0.7
      } else {
        // Pour les autres métriques, augmenter la valeur
        newValue *= 1.3
      }

      return {
        ...metric,
        value: newValue,
        status: "good",
        trend: metric.id.includes("time") || metric.id.includes("Time") ? "down" : "up",
      }
    })

    // Mettre à jour toutes les ressources
    this.resources = this.resources.map((resource) => {
      return {
        ...resource,
        current: resource.current * 0.7,
        status: "good",
      }
    })

    // Mettre à jour le statut de toutes les recommandations
    this.recommendations.forEach((r) => {
      r.status = "completed"
    })

    return true
  }

  // Getters
  public getMetrics(): PerformanceMetric[] {
    return this.metrics
  }

  public getResources(): ResourceUsage[] {
    return this.resources
  }

  public getRecommendations(): OptimizationRecommendation[] {
    return this.recommendations
  }

  public getLastUpdated(): Date {
    return this.lastUpdated
  }

  // Méthode pour nettoyer les ressources lors de la fermeture
  public cleanup(): void {
    this.stopUpdates()
  }
}

// Exporter une instance du service
export const performanceService = PerformanceService.getInstance()

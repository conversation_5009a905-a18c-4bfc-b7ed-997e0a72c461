import { Connection } from "@solana/web3.js"

export interface BondingCurveParams {
  tokenAddress: string
  initialSupply: number
  maxSupply: number
  solPriceUsd: number
  dexListingThresholdUsd: number
  reserveRatio: number // Entre 0 et 1, généralement 0.1 à 0.5
  initialPrice: number // Prix initial en SOL
}

export interface TokenPurchaseResult {
  tokensToReceive: number
  costInSol: number
  pricePerToken: number
  newSupply: number
  newPrice: number
  marketCapUsd: number
  dexListingProgress: number
}

/**
 * Service pour gérer les bonding curves des tokens
 */
class BondingCurveService {
  private curves: Map<string, BondingCurveParams> = new Map()
  private connection: Connection

  constructor() {
    const rpcUrl = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com"
    this.connection = new Connection(rpcUrl, "confirmed")
  }

  /**
   * Initialiser une bonding curve pour un token
   */
  initializeCurve(params: BondingCurveParams): void {
    // Valeurs par défaut si non fournies
    const curveParams: BondingCurveParams = {
      ...params,
      reserveRatio: params.reserveRatio || 0.2, // Ratio de réserve par défaut de 20%
      initialPrice: params.initialPrice || 0.00001, // Prix initial par défaut en SOL
    }

    this.curves.set(params.tokenAddress, curveParams)

    console.log(`Bonding curve initialized for token ${params.tokenAddress}:`, curveParams)
  }

  /**
   * Calculer le prix à un point d'offre spécifique
   * Formule Bancor: price = initialPrice * (supply / initialSupply) ^ (1 / reserveRatio)
   */
  calculatePrice(tokenAddress: string, supply: number): number {
    const curve = this.curves.get(tokenAddress)
    if (!curve) {
      throw new Error(`Bonding curve not found for token: ${tokenAddress}`)
    }

    const { initialSupply, initialPrice, reserveRatio } = curve

    // Éviter la division par zéro
    if (initialSupply <= 0) {
      return initialPrice
    }

    // Formule Bancor: price = initialPrice * (supply / initialSupply) ^ (1 / reserveRatio)
    return initialPrice * Math.pow(supply / initialSupply, 1 / reserveRatio)
  }

  /**
   * Calculer le coût pour acheter un montant spécifique de tokens
   */
  calculateCost(tokenAddress: string, currentSupply: number, amount: number): number {
    const curve = this.curves.get(tokenAddress)
    if (!curve) {
      throw new Error(`Bonding curve not found for token: ${tokenAddress}`)
    }

    const { initialSupply, initialPrice, reserveRatio, maxSupply } = curve

    // Vérifier si l'achat dépasserait l'offre maximale
    if (currentSupply + amount > maxSupply) {
      throw new Error(`Purchase would exceed maximum supply of ${maxSupply}`)
    }

    // Formule Bancor pour le coût:
    // cost = reserveBalance * ((1 + amount/supply)^reserveRatio - 1)

    // Pour notre cas, nous pouvons calculer cela comme l'intégrale de la courbe de prix
    const newSupply = currentSupply + amount

    // Calculer le coût en utilisant l'intégrale de la courbe de prix
    // Formula: cost = initialPrice * initialSupply * ((newSupply/initialSupply)^(1+1/reserveRatio) - (currentSupply/initialSupply)^(1+1/reserveRatio)) / (1 + 1/reserveRatio)
    const exponent = 1 + reserveRatio
    const factor = (initialPrice * initialSupply) / exponent

    const cost =
      factor * (Math.pow(newSupply / initialSupply, exponent) - Math.pow(currentSupply / initialSupply, exponent))

    return cost
  }

  /**
   * Calculer combien de tokens peuvent être achetés avec un montant spécifique de SOL
   */
  calculateTokensForSol(tokenAddress: string, currentSupply: number, solAmount: number): number {
    const curve = this.curves.get(tokenAddress)
    if (!curve) {
      throw new Error(`Bonding curve not found for token: ${tokenAddress}`)
    }

    // Recherche binaire pour trouver le montant de tokens qui coûte exactement solAmount
    let low = 0
    let high = curve.maxSupply - currentSupply
    let mid = 0
    let iterations = 0
    const MAX_ITERATIONS = 50

    while (high - low > 0.000001 && iterations < MAX_ITERATIONS) {
      mid = (low + high) / 2
      const cost = this.calculateCost(tokenAddress, currentSupply, mid)

      if (Math.abs(cost - solAmount) < 0.000001) {
        return mid
      }

      if (cost < solAmount) {
        low = mid
      } else {
        high = mid
      }

      iterations++
    }

    return low
  }

  /**
   * Simuler un achat de token et retourner des informations détaillées
   */
  simulatePurchase(tokenAddress: string, currentSupply: number, solAmount: number): TokenPurchaseResult {
    const curve = this.curves.get(tokenAddress)
    if (!curve) {
      throw new Error(`Bonding curve not found for token: ${tokenAddress}`)
    }

    // Calculer combien de tokens peuvent être achetés avec le montant de SOL donné
    const tokensToReceive = this.calculateTokensForSol(tokenAddress, currentSupply, solAmount)

    // Calculer la nouvelle offre après l'achat
    const newSupply = currentSupply + tokensToReceive

    // Calculer le nouveau prix après l'achat
    const newPrice = this.calculatePrice(tokenAddress, newSupply)

    // Calculer le prix par token pour cet achat
    const pricePerToken = solAmount / tokensToReceive

    // Calculer la capitalisation boursière en USD
    const marketCapUsd = newPrice * newSupply * curve.solPriceUsd

    // Calculer la progression vers le listing DEX (0-100%)
    const dexListingProgress = Math.min(100, (marketCapUsd / curve.dexListingThresholdUsd) * 100)

    return {
      tokensToReceive,
      costInSol: solAmount,
      pricePerToken,
      newSupply,
      newPrice,
      marketCapUsd,
      dexListingProgress,
    }
  }

  /**
   * Vérifier si un token a atteint le seuil de listing DEX
   */
  hasReachedDexListingThreshold(tokenAddress: string, currentSupply: number): boolean {
    const curve = this.curves.get(tokenAddress)
    if (!curve) {
      return false
    }

    const currentPrice = this.calculatePrice(tokenAddress, currentSupply)
    const marketCapUsd = currentPrice * currentSupply * curve.solPriceUsd

    return marketCapUsd >= curve.dexListingThresholdUsd
  }

  /**
   * Obtenir la progression du seuil de listing DEX (0-100%)
   */
  getDexListingProgress(tokenAddress: string, currentSupply: number): number {
    const curve = this.curves.get(tokenAddress)
    if (!curve) {
      return 0
    }

    const currentPrice = this.calculatePrice(tokenAddress, currentSupply)
    const marketCapUsd = currentPrice * currentSupply * curve.solPriceUsd

    return Math.min(100, (marketCapUsd / curve.dexListingThresholdUsd) * 100)
  }
}

// Exporter une instance singleton
const bondingCurveService = new BondingCurveService()
export default bondingCurveService

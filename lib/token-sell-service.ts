import { Connection, PublicKey, Transaction, LAMPORTS_PER_SOL } from "@solana/web3.js"
import { getAssociatedTokenAddress, createBurnCheckedInstruction, TOKEN_PROGRAM_ID } from "@solana/spl-token"
import { envConfig } from "./env-config"
import bondingCurveService from "./bonding-curve-service"
import secureKeyService from "./secure-key-service"

export interface SellParams {
  tokenAddress: string
  sellerAddress: string
  tokenAmount: number
  currentSupply: number
}

export interface SellResult {
  success: boolean
  txSignature?: string
  solReceived?: number
  error?: string
}

/**
 * Service for handling token selling via bonding curve
 */
class TokenSellService {
  private connection: Connection
  private platformFeePercent = 1 // 1% platform fee

  constructor() {
    const rpcUrl = envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com"
    this.connection = new Connection(rpcUrl, "confirmed")
  }

  /**
   * Calculate the SOL to receive for a specific number of tokens
   */
  calculateSolToReceive(tokenAddress: string, currentSupply: number, tokenAmount: number): number {
    try {
      const newSupply = currentSupply - tokenAmount
      if (newSupply < 0) throw new Error("Cannot sell more tokens than the current supply")

      // Calculate the integral of the bonding curve from newSupply to currentSupply
      const solAmount = bondingCurveService.calculateCost(tokenAddress, newSupply, tokenAmount)

      // Apply the platform fee
      const feeAmount = solAmount * (this.platformFeePercent / 100)
      const netSolAmount = solAmount - feeAmount

      return netSolAmount
    } catch (error) {
      console.error("Error calculating SOL to receive:", error)
      return 0
    }
  }

  /**
   * Process a token sell transaction
   */
  async processSell(params: SellParams): Promise<SellResult> {
    try {
      const { tokenAddress, sellerAddress, tokenAmount, currentSupply } = params

      // Get the platform keypair
      const platformKeypair = secureKeyService.getPlatformKeypair()
      if (!platformKeypair) {
        throw new Error("Platform keypair not available. Check environment variables.")
      }

      // Convert addresses to PublicKey objects
      const tokenPublicKey = new PublicKey(tokenAddress)
      const sellerPublicKey = new PublicKey(sellerAddress)

      // Calculate the SOL to receive
      const solToReceive = this.calculateSolToReceive(tokenAddress, currentSupply, tokenAmount)
      if (solToReceive <= 0) {
        throw new Error("Invalid token amount or calculation error")
      }

      // Check platform balance
      const platformBalance = await this.connection.getBalance(platformKeypair.publicKey)
      if (platformBalance < solToReceive * LAMPORTS_PER_SOL) {
        throw new Error("Insufficient platform balance to process this sale")
      }

      // Get the seller's associated token account
      const sellerTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, sellerPublicKey)

      // Create a transaction to burn tokens
      const burnTx = new Transaction()

      // Add instruction to burn tokens from the seller
      burnTx.add(
        createBurnCheckedInstruction(
          sellerTokenAccount,
          tokenPublicKey,
          sellerPublicKey,
          BigInt(Math.floor(tokenAmount * Math.pow(10, 9))), // 9 decimals
          9, // decimals
          [],
          TOKEN_PROGRAM_ID,
        ),
      )

      // Send SOL to the seller (requires a separate transaction signed by the platform)
      const solTransferTx = new Transaction()

      // Add the recent blockhash and fee payer
      const { blockhash } = await this.connection.getLatestBlockhash()
      burnTx.recentBlockhash = blockhash
      burnTx.feePayer = sellerPublicKey

      return {
        success: true,
        txSignature: "simulation", // In a real implementation, this would be the actual signature
        solReceived: solToReceive,
      }
    } catch (error: any) {
      console.error("Error processing token sell:", error)
      return {
        success: false,
        error: error.message || "An error occurred while processing the sell transaction",
      }
    }
  }

  /**
   * Create a client-side transaction for token burning
   * This transaction will be sent to the frontend for signing by the seller
   */
  async createBurnTransaction(params: SellParams): Promise<{
    transaction: Transaction
    solToReceive: number
  }> {
    const { tokenAddress, sellerAddress, tokenAmount, currentSupply } = params

    // Calculate the SOL to receive
    const solToReceive = this.calculateSolToReceive(tokenAddress, currentSupply, tokenAmount)
    if (solToReceive <= 0) {
      throw new Error("Invalid token amount or calculation error")
    }

    // Convert addresses to PublicKey objects
    const tokenPublicKey = new PublicKey(tokenAddress)
    const sellerPublicKey = new PublicKey(sellerAddress)

    // Get the seller's associated token account
    const sellerTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, sellerPublicKey)

    // Create a transaction to burn tokens
    const burnTx = new Transaction()

    // Add instruction to burn tokens from the seller
    burnTx.add(
      createBurnCheckedInstruction(
        sellerTokenAccount,
        tokenPublicKey,
        sellerPublicKey,
        BigInt(Math.floor(tokenAmount * Math.pow(10, 9))), // 9 decimals
        9, // decimals
        [],
        TOKEN_PROGRAM_ID,
      ),
    )

    // Add the recent blockhash and fee payer
    const { blockhash } = await this.connection.getLatestBlockhash()
    burnTx.recentBlockhash = blockhash
    burnTx.feePayer = sellerPublicKey

    return {
      transaction: burnTx,
      solToReceive,
    }
  }
}

// Export a singleton instance
const tokenSellService = new TokenSellService()
export default tokenSellService

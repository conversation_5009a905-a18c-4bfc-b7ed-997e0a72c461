import { Connection } from "@solana/web3.js"
import bs58 from "bs58"

interface DiagnosticResult {
  success: boolean
  errorType?: string
  details?: string
  solution?: string
  technicalInfo?: any
}

export async function diagnoseBase58Error(input: string): Promise<DiagnosticResult> {
  try {
    // Test if the input is valid base58
    try {
      bs58.decode(input)
      return {
        success: true,
        details: "The string is valid base58",
      }
    } catch (e) {
      // Find the invalid character
      const base58Chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
      let invalidChar = ""
      let invalidPos = -1

      for (let i = 0; i < input.length; i++) {
        if (!base58Chars.includes(input[i])) {
          invalidChar = input[i]
          invalidPos = i
          break
        }
      }

      return {
        success: false,
        errorType: "INVALID_BASE58",
        details: `Invalid base58 character "${invalidChar}" at position ${invalidPos}`,
        solution: "Remove or replace the invalid character with a valid base58 character",
        technicalInfo: {
          invalidChar,
          invalidPos,
          input,
        },
      }
    }
  } catch (error: any) {
    return {
      success: false,
      errorType: "DIAGNOSTIC_ERROR",
      details: `Error during diagnosis: ${error.message}`,
    }
  }
}

export async function testRpcConnection(): Promise<DiagnosticResult> {
  try {
    const rpcUrl = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com"
    const connection = new Connection(rpcUrl, "confirmed")

    // Test the connection by getting the latest blockhash
    const { blockhash } = await connection.getLatestBlockhash()

    return {
      success: true,
      details: `Successfully connected to RPC at ${rpcUrl}`,
      technicalInfo: { blockhash },
    }
  } catch (error: any) {
    return {
      success: false,
      errorType: "RPC_CONNECTION_ERROR",
      details: `Failed to connect to RPC: ${error.message}`,
      solution: "Check your internet connection or try a different RPC endpoint",
      technicalInfo: { error: error.message },
    }
  }
}

export async function diagnoseTokenCreationIssue(error: any): Promise<DiagnosticResult> {
  const errorMessage = error?.message || String(error)

  // Check for specific error patterns
  if (errorMessage.includes("Non-base58 character")) {
    // This is likely an issue with the input data
    return {
      success: false,
      errorType: "NON_BASE58_CHARACTER",
      details: "One of the inputs contains characters that are not valid in base58 encoding",
      solution: "Use only alphanumeric characters for token symbol and name. Avoid special characters.",
      technicalInfo: { errorMessage },
    }
  }

  if (errorMessage.includes("insufficient funds")) {
    return {
      success: false,
      errorType: "INSUFFICIENT_FUNDS",
      details: "Your wallet doesn't have enough SOL to complete this transaction",
      solution: "Add more SOL to your wallet or request from a faucet if on devnet/testnet",
      technicalInfo: { errorMessage },
    }
  }

  if (errorMessage.includes("blockhash")) {
    return {
      success: false,
      errorType: "BLOCKHASH_ERROR",
      details: "There was an issue with the transaction blockhash",
      solution: "Try again or check your network connection",
      technicalInfo: { errorMessage },
    }
  }

  // Generic error
  return {
    success: false,
    errorType: "UNKNOWN_ERROR",
    details: `An unknown error occurred: ${errorMessage}`,
    solution: "Try again or contact support with the error details",
    technicalInfo: { errorMessage },
  }
}

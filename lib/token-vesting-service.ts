import { Connection, type Keypair, PublicKey, Transaction } from "@solana/web3.js"
import { getOrCreateAssociatedTokenAccount, transfer } from "@solana/spl-token"
import { envConfig } from "./env-config"

export interface VestingSchedule {
  tokenAddress: string
  totalAmount: number
  releasedAmount: number
  startTimestamp: number
  endTimestamp: number
  releaseInterval: number // en secondes
  releasePercentagePerInterval: number
  beneficiary: string
  isActive: boolean
}

class TokenVestingService {
  private connection: Connection
  private vestingSchedules: Map<string, VestingSchedule[]>

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
    this.vestingSchedules = new Map()

    // Démarrer le processus de vérification périodique des schedules de vesting
    this.startVestingCheckInterval()
  }

  /**
   * Crée un nouveau schedule de vesting
   */
  async createVestingSchedule(
    tokenAddress: string,
    totalAmount: number,
    startTimestamp: number,
    durationInDays: number,
    releaseIntervalInDays: number,
    beneficiary: string,
    payer: Keypair,
  ): Promise<{
    success: boolean
    scheduleId?: string
    error?: string
  }> {
    try {
      const tokenMint = new PublicKey(tokenAddress)
      const beneficiaryPubkey = new PublicKey(beneficiary)

      // Calculer les paramètres du schedule
      const now = Date.now()
      const actualStartTimestamp = startTimestamp || now
      const endTimestamp = actualStartTimestamp + durationInDays * 86400000 // convertir jours en millisecondes
      const releaseInterval = releaseIntervalInDays * 86400 // convertir jours en secondes

      // Calculer le pourcentage à libérer à chaque intervalle
      const totalIntervals = (durationInDays * 86400) / releaseInterval
      const releasePercentagePerInterval = 100 / totalIntervals

      // Créer un ID unique pour ce schedule
      const scheduleId = `${tokenAddress}-${beneficiary}-${now}`

      // Créer le schedule de vesting
      const vestingSchedule: VestingSchedule = {
        tokenAddress,
        totalAmount,
        releasedAmount: 0,
        startTimestamp: actualStartTimestamp,
        endTimestamp,
        releaseInterval,
        releasePercentagePerInterval,
        beneficiary,
        isActive: true,
      }

      // Ajouter le schedule à la map
      if (!this.vestingSchedules.has(tokenAddress)) {
        this.vestingSchedules.set(tokenAddress, [])
      }

      this.vestingSchedules.get(tokenAddress)!.push(vestingSchedule)

      // Dans une implémentation réelle, nous sauvegarderions le schedule dans une base de données

      return {
        success: true,
        scheduleId,
      }
    } catch (error: any) {
      console.error("Error creating vesting schedule:", error)
      return {
        success: false,
        error: error.message || "An error occurred while creating the vesting schedule",
      }
    }
  }

  /**
   * Récupère tous les schedules de vesting pour un token
   */
  getVestingSchedules(tokenAddress: string): VestingSchedule[] {
    return this.vestingSchedules.get(tokenAddress) || []
  }

  /**
   * Récupère un schedule de vesting spécifique
   */
  getVestingSchedule(tokenAddress: string, scheduleId: string): VestingSchedule | null {
    const schedules = this.vestingSchedules.get(tokenAddress) || []
    return schedules.find((s) => `${tokenAddress}-${s.beneficiary}-${s.startTimestamp}` === scheduleId) || null
  }

  /**
   * Calcule le montant à libérer pour un schedule de vesting
   */
  calculateReleaseAmount(schedule: VestingSchedule): number {
    const now = Date.now()

    // Si le schedule n'est pas actif ou n'a pas encore commencé
    if (!schedule.isActive || now < schedule.startTimestamp) {
      return 0
    }

    // Si le schedule est terminé, libérer tout ce qui reste
    if (now >= schedule.endTimestamp) {
      return schedule.totalAmount - schedule.releasedAmount
    }

    // Calculer le temps écoulé depuis le début du schedule
    const elapsedTime = (now - schedule.startTimestamp) / 1000 // en secondes

    // Calculer le nombre d'intervalles complets écoulés
    const completedIntervals = Math.floor(elapsedTime / schedule.releaseInterval)

    // Calculer le montant total qui devrait être libéré
    const totalReleasePercentage = completedIntervals * schedule.releasePercentagePerInterval
    const totalReleaseAmount = Math.floor(schedule.totalAmount * (totalReleasePercentage / 100))

    // Calculer le montant à libérer maintenant
    const releaseAmount = totalReleaseAmount - schedule.releasedAmount

    return Math.max(0, releaseAmount)
  }

  /**
   * Exécute une libération de tokens pour un schedule de vesting
   */
  async executeRelease(
    tokenAddress: string,
    scheduleId: string,
    payer: Keypair,
  ): Promise<{
    success: boolean
    releaseAmount?: number
    transactionId?: string
    error?: string
  }> {
    try {
      const schedule = this.getVestingSchedule(tokenAddress, scheduleId)

      if (!schedule) {
        return {
          success: false,
          error: "Vesting schedule not found",
        }
      }

      const releaseAmount = this.calculateReleaseAmount(schedule)

      if (releaseAmount <= 0) {
        return {
          success: false,
          error: "No tokens available for release",
        }
      }

      const tokenMint = new PublicKey(tokenAddress)
      const beneficiaryPubkey = new PublicKey(schedule.beneficiary)

      // Créer les comptes de token associés
      const vestingTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        payer,
        tokenMint,
        payer.publicKey,
      )

      const beneficiaryTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        payer,
        tokenMint,
        beneficiaryPubkey,
      )

      // Créer une transaction pour transférer les tokens
      const transaction = new Transaction().add(
        transfer(
          this.connection,
          payer,
          vestingTokenAccount.address,
          beneficiaryTokenAccount.address,
          payer.publicKey,
          releaseAmount,
        ),
      )

      // Signer et envoyer la transaction
      transaction.feePayer = payer.publicKey
      transaction.recentBlockhash = (await this.connection.getLatestBlockhash()).blockhash

      const signature = await this.connection.sendTransaction(transaction, [payer])
      await this.connection.confirmTransaction(signature)

      // Mettre à jour le montant libéré dans le schedule
      schedule.releasedAmount += releaseAmount

      // Si tout a été libéré, marquer le schedule comme inactif
      if (schedule.releasedAmount >= schedule.totalAmount) {
        schedule.isActive = false
      }

      return {
        success: true,
        releaseAmount,
        transactionId: signature,
      }
    } catch (error: any) {
      console.error("Error executing vesting release:", error)
      return {
        success: false,
        error: error.message || "An error occurred while executing the vesting release",
      }
    }
  }

  /**
   * Démarre l'intervalle de vérification des schedules de vesting
   */
  private startVestingCheckInterval(): void {
    // Vérifier les schedules toutes les heures
    setInterval(() => this.checkVestingSchedules(), 3600000)
  }

  /**
   * Vérifie tous les schedules de vesting actifs
   */
  private async checkVestingSchedules(): Promise<void> {
    console.log("Checking vesting schedules...")

    const now = Date.now()

    // Parcourir tous les tokens avec des schedules
    for (const [tokenAddress, schedules] of this.vestingSchedules.entries()) {
      // Parcourir tous les schedules actifs
      for (const schedule of schedules.filter((s) => s.isActive)) {
        // Vérifier si un intervalle complet s'est écoulé depuis la dernière libération
        const elapsedTime = (now - schedule.startTimestamp) / 1000 // en secondes
        const completedIntervals = Math.floor(elapsedTime / schedule.releaseInterval)

        const totalReleasePercentage = completedIntervals * schedule.releasePercentagePerInterval
        const totalReleaseAmount = Math.floor(schedule.totalAmount * (totalReleasePercentage / 100))

        const releaseAmount = totalReleaseAmount - schedule.releasedAmount

        if (releaseAmount > 0) {
          console.log(`Schedule ${tokenAddress}-${schedule.beneficiary} has ${releaseAmount} tokens ready for release`)

          // Dans une implémentation réelle, nous pourrions envoyer une notification
          // ou exécuter automatiquement la libération
        }
      }
    }
  }
}

// Exporter une instance singleton du service
const tokenVestingService = new TokenVestingService()
export default tokenVestingService

// Configuration des variables d'environnement
export const envConfig = {
  // URL de l'API Solana Devnet
  SOLANA_RPC_URL: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com",

  // URL de l'API BNB Chain Testnet
  BNB_RPC_URL: process.env.NEXT_PUBLIC_BNB_RPC_URL || "https://data-seed-prebsc-1-s1.binance.org:8545",

  // ID du programme de token (valeur correcte pour Solana)
  TOKEN_PROGRAM_ID: process.env.NEXT_PUBLIC_TOKEN_PROGRAM_ID || "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",

  // Adresse de mint personnalisée (si fournie)
  CUSTOM_MINT_ADDRESS: process.env.NEXT_PUBLIC_CUSTOM_MINT_ADDRESS || "",

  // Adresse du wallet admin
  ADMIN_WALLET: process.env.ADMIN_WALLET || "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",

  // Endpoint API pour les données de marché
  MARKET_API_ENDPOINT: process.env.MARKET_API_ENDPOINT || "https://api.solscan.io",

  // Clé API pour les données de marché (si nécessaire)
  MARKET_API_KEY: process.env.MARKET_API_KEY || "",

  // URL de l'explorateur Solana
  SOLANA_EXPLORER_URL: "https://explorer.solana.com/?cluster=devnet",

  // URL de l'explorateur BNB Chain
  BNB_EXPLORER_URL: "https://testnet.bscscan.com",

  // Adresse du contrat de fabrique de tokens BNB (séparées pour testnet et mainnet)
  BNB_TOKEN_FACTORY_TESTNET:
    process.env.NEXT_PUBLIC_BNB_TOKEN_FACTORY_TESTNET || "******************************************",
  BNB_TOKEN_FACTORY_MAINNET:
    process.env.NEXT_PUBLIC_BNB_TOKEN_FACTORY_MAINNET || "******************************************",

  // Adresse du routeur PancakeSwap (séparées pour testnet et mainnet)
  PANCAKESWAP_ROUTER_TESTNET:
    process.env.NEXT_PUBLIC_PANCAKESWAP_ROUTER_TESTNET || "0x9Ac64Cc6e4415144C455BD8E4837Fea55603e5c3",
  PANCAKESWAP_ROUTER_MAINNET:
    process.env.NEXT_PUBLIC_PANCAKESWAP_ROUTER_MAINNET || "0x10ED43C718714eb63d5aA57B78B54704E256024E",

  // Clé API CoinGecko
  COINGECKO_API_KEY: process.env.COINGECKO_API_KEY || "",

  // Clé API CoinMarketCap
  COINMARKETCAP_API_KEY: process.env.COINMARKETCAP_API_KEY || "",
}

import { Connection, Keypair, PublicKey } from "@solana/web3.js"
import { createMint, getOrCreateAssociatedTokenAccount, mintTo, TOKEN_PROGRAM_ID } from "@solana/spl-token"
import { envConfig } from "./env-config"
import TokenSuffixService from "./token-suffix-service"
import TokenPaymentService from "./token-payment-service"
import KeypairManager from "./keypair-manager"

interface TokenCreationParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: number
  maxSupply?: number
  isMintable?: boolean
  isBurnable?: boolean
  isPausable?: boolean
  isTransferTaxable?: boolean
  transferTaxRate?: number
  ownerAddress: string
  suffix?: string
  description?: string
  website?: string
  twitter?: string
  telegram?: string
}

interface TokenCreationResult {
  success: boolean
  tokenAddress?: string
  transactionId?: string
  error?: string
  tokenData?: any
}

class EnhancedTokenService {
  /**
   * Préparer la création d'un token
   */
  static async prepareTokenCreation(params: TokenCreationParams): Promise<{
    success: boolean
    mintKeypair?: Keypair
    error?: string
  }> {
    try {
      // Vérifier le solde du portefeuille
      const balanceCheck = await TokenPaymentService.checkBalance(params.ownerAddress)
      if (!balanceCheck.sufficient) {
        return {
          success: false,
          error: `Insufficient balance. Required: ${TokenPaymentService.getTokenCreationFee()} SOL, Available: ${balanceCheck.balance} SOL`,
        }
      }

      // Obtenir le suffixe à utiliser
      const suffix = params.suffix || "GF"

      // Vérifier si nous avons des paires de clés pré-générées
      if (KeypairManager.hasPreGeneratedKeypairs(suffix, "solana-devnet")) {
        const keypair = await KeypairManager.getAvailableKeypair(suffix, "solana-devnet")
        if (keypair) {
          return {
            success: true,
            mintKeypair: keypair,
          }
        }
      }

      // Générer une nouvelle paire de clés avec le suffixe
      console.log(`Generating keypair with suffix ${suffix}...`)
      const result = await TokenSuffixService.getKeypairWithSuffix(suffix)

      if (!result.success || !result.keypair) {
        return {
          success: false,
          error: result.error || "Failed to generate keypair with suffix",
        }
      }

      const mintKeypair = Keypair.fromSecretKey(Uint8Array.from(result.keypair.secret))
      return {
        success: true,
        mintKeypair,
      }
    } catch (error: any) {
      console.error("Error preparing token creation:", error)
      return {
        success: false,
        error: error.message || "Failed to prepare token creation",
      }
    }
  }

  /**
   * Créer un token avec des fonctionnalités avancées
   */
  static async createToken(
    params: TokenCreationParams,
    paymentSignature: string,
    mintKeypair: Keypair,
  ): Promise<TokenCreationResult> {
    try {
      // Vérifier que le paiement a été effectué
      const paymentVerified = await TokenPaymentService.verifyPayment(paymentSignature)
      if (!paymentVerified) {
        return {
          success: false,
          error: "Payment verification failed",
        }
      }

      const connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")

      // Obtenir la clé privée de l'administrateur pour payer les frais de transaction
      let adminPrivateKey: string | undefined

      if (process.env.ADMIN_WALLET) {
        adminPrivateKey = process.env.ADMIN_WALLET
      } else if (process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY) {
        adminPrivateKey = process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY
      } else if (process.env.PRE_MINED_MMGF_PRIVATE_KEY) {
        adminPrivateKey = process.env.PRE_MINED_MMGF_PRIVATE_KEY
      }

      if (!adminPrivateKey) {
        return {
          success: false,
          error: "Admin wallet not configured",
        }
      }

      // Convertir la clé privée en Keypair
      let adminKeypair: Keypair
      try {
        const secretKey = Buffer.from(adminPrivateKey, "base64")
        adminKeypair = Keypair.fromSecretKey(secretKey)
      } catch (error) {
        console.error("Error decoding admin private key:", error)
        return {
          success: false,
          error: "Invalid admin private key format",
        }
      }

      // Créer le token mint
      const ownerPublicKey = new PublicKey(params.ownerAddress)

      // Utiliser l'administrateur comme autorité de mint et de freeze initialement
      const mintAuthority = adminKeypair.publicKey
      const freezeAuthority = params.isPausable ? adminKeypair.publicKey : null

      console.log("Creating token mint...")
      const mint = await createMint(
        connection,
        adminKeypair,
        mintAuthority,
        freezeAuthority,
        params.decimals,
        mintKeypair,
      )

      console.log(`Token mint created: ${mint.toString()}`)

      // Créer un compte de token pour le propriétaire
      console.log("Creating token account...")
      const tokenAccount = await getOrCreateAssociatedTokenAccount(connection, adminKeypair, mint, ownerPublicKey)

      console.log(`Token account created: ${tokenAccount.address.toString()}`)

      // Minter l'offre initiale au propriétaire
      console.log("Minting initial supply...")
      const initialAmount = Number(params.initialSupply) * Math.pow(10, params.decimals)
      const mintTx = await mintTo(
        connection,
        adminKeypair,
        mint,
        tokenAccount.address,
        adminKeypair, // Utiliser l'admin comme autorité de mint
        BigInt(initialAmount.toString()),
      )

      console.log(`Initial supply minted: ${initialAmount}`)
      console.log(`Mint transaction: ${mintTx}`)

      // Enregistrer les métadonnées du token dans la base de données (dans une implémentation réelle)
      // ...

      // Retourner les informations du token créé
      return {
        success: true,
        tokenAddress: mint.toString(),
        transactionId: mintTx,
        tokenData: {
          name: params.name,
          symbol: params.symbol,
          decimals: params.decimals,
          initialSupply: params.initialSupply,
          maxSupply: params.maxSupply,
          isMintable: params.isMintable,
          isBurnable: params.isBurnable,
          isPausable: params.isPausable,
          isTransferTaxable: params.isTransferTaxable,
          transferTaxRate: params.transferTaxRate,
          owner: params.ownerAddress,
          createdAt: new Date().toISOString(),
        },
      }
    } catch (error: any) {
      console.error("Error creating token:", error)
      return {
        success: false,
        error: error.message || "Failed to create token",
      }
    }
  }

  /**
   * Obtenir les détails d'un token
   */
  static async getTokenDetails(tokenAddress: string): Promise<any> {
    try {
      const connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
      const publicKey = new PublicKey(tokenAddress)

      // Obtenir les informations du token
      const accountInfo = await connection.getAccountInfo(publicKey)

      if (!accountInfo) {
        throw new Error("Token not found")
      }

      // Vérifier que c'est bien un token SPL
      if (!accountInfo.owner.equals(TOKEN_PROGRAM_ID)) {
        throw new Error("Not a valid SPL token")
      }

      // Obtenir les métadonnées du token depuis la base de données (dans une implémentation réelle)
      // ...

      // Pour l'instant, retourner des informations de base
      return {
        address: tokenAddress,
        programId: accountInfo.owner.toString(),
        data: Buffer.from(accountInfo.data).toString("hex"),
      }
    } catch (error: any) {
      console.error("Error getting token details:", error)
      throw error
    }
  }
}

export default EnhancedTokenService

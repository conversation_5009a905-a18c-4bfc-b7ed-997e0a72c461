import { Keypair } from "@solana/web3.js"
import TokenSuffixService from "./token-suffix-service"

interface StoredKeypair {
  publicKey: string
  secretKey: number[]
  suffix: string
  networkId: string
  createdAt: string
  used: boolean
}

class KeypairManager {
  // In-memory storage for keypairs (in a real app, this would be in a secure database)
  private static storedKeypairs: Record<string, StoredKeypair> = {}

  /**
   * Store a pre-generated keypair for a specific suffix and network
   */
  static async storeKeypair(keypair: Keypair, suffix: string, networkId: string): Promise<void> {
    const publicKey = keypair.publicKey.toString()
    const key = `${networkId}:${suffix}:${publicKey}`

    this.storedKeypairs[key] = {
      publicKey,
      secretKey: Array.from(keypair.secretKey),
      suffix,
      networkId,
      createdAt: new Date().toISOString(),
      used: false,
    }

    console.log(`Stored keypair for suffix ${suffix} on network ${networkId}: ${publicKey}`)
  }

  /**
   * Get an available pre-generated keypair for a specific suffix and network
   */
  static async getAvailableKeypair(suffix: string, networkId: string): Promise<Keypair | null> {
    console.log(`Looking for available keypair with suffix ${suffix} on network ${networkId}`)

    // Find all keypairs for this suffix and network
    const matchingKeypairs = Object.values(this.storedKeypairs).filter(
      (kp) => kp.suffix === suffix && kp.networkId === networkId && !kp.used,
    )

    if (matchingKeypairs.length === 0) {
      console.log(`No available keypairs found for suffix ${suffix} on network ${networkId}`)
      return null
    }

    // Get the first available keypair
    const storedKeypair = matchingKeypairs[0]

    // Mark it as used
    const key = `${networkId}:${suffix}:${storedKeypair.publicKey}`
    this.storedKeypairs[key].used = true

    console.log(`Found available keypair: ${storedKeypair.publicKey}`)

    // Convert back to a Keypair object
    return Keypair.fromSecretKey(Uint8Array.from(storedKeypair.secretKey))
  }

  /**
   * Pre-generate a batch of keypairs with the specified suffix
   */
  static async preGenerateKeypairs(suffix: string, networkId: string, count = 5): Promise<string[]> {
    console.log(`Pre-generating ${count} keypairs with suffix ${suffix} for network ${networkId}`)

    const generatedAddresses: string[] = []

    for (let i = 0; i < count; i++) {
      try {
        // Use the TokenSuffixService to grind a keypair with the suffix
        const result = await TokenSuffixService.grindKeypairWithSuffix({
          suffix,
          maxAttempts: 100000, // Reasonable limit
        })

        if (result.success && result.keypair) {
          // Convert the result to a Keypair
          const keypair = Keypair.fromSecretKey(Uint8Array.from(result.keypair.secret))

          // Store the keypair
          await this.storeKeypair(keypair, suffix, networkId)

          generatedAddresses.push(keypair.publicKey.toString())
          console.log(`Generated keypair ${i + 1}/${count}: ${keypair.publicKey.toString()}`)
        } else {
          console.error(`Failed to generate keypair ${i + 1}/${count}:`, result.error)
        }
      } catch (error) {
        console.error(`Error generating keypair ${i + 1}/${count}:`, error)
      }
    }

    return generatedAddresses
  }

  /**
   * Check if we have any pre-generated keypairs for a suffix
   */
  static hasPreGeneratedKeypairs(suffix: string, networkId: string): boolean {
    const availableKeypairs = Object.values(this.storedKeypairs).filter(
      (kp) => kp.suffix === suffix && kp.networkId === networkId && !kp.used,
    )

    return availableKeypairs.length > 0
  }

  /**
   * Get stats about stored keypairs
   */
  static getKeypairStats(): {
    total: number
    used: number
    available: number
    bySuffix: Record<string, { total: number; used: number; available: number }>
  } {
    const stats = {
      total: 0,
      used: 0,
      available: 0,
      bySuffix: {} as Record<string, { total: number; used: number; available: number }>,
    }

    Object.values(this.storedKeypairs).forEach((kp) => {
      stats.total++
      if (kp.used) stats.used++
      else stats.available++

      const suffixKey = `${kp.networkId}:${kp.suffix}`
      if (!stats.bySuffix[suffixKey]) {
        stats.bySuffix[suffixKey] = { total: 0, used: 0, available: 0 }
      }

      stats.bySuffix[suffixKey].total++
      if (kp.used) stats.bySuffix[suffixKey].used++
      else stats.bySuffix[suffixKey].available++
    })

    return stats
  }
}

export default KeypairManager

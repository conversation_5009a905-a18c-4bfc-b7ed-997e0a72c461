import { NFTStorage } from "nft.storage"
import Arweave from "arweave"
import type { JWKInterface } from "arweave/node/lib/wallet"
import { getStorageConfig } from "./admin-service"

// Ajouter Filebase comme option de fournisseur IPFS
export interface StorageConfig {
  ipfs: {
    enabled: boolean
    provider: "nft.storage" | "pinata" | "infura" | "filebase"
    apiKey: string
    gateway: string
    endpoint?: string
  }
  arweave: {
    enabled: boolean
    jwk: JWKInterface | null
    gateway: string
  }
}

class StorageService {
  private static nftStorageClient: NFTStorage | null = null
  private static arweaveClient: Arweave | null = null
  private static config: StorageConfig | null = null

  /**
   * Initialiser les clients de stockage
   */
  static async initialize() {
    try {
      // Récupérer la configuration depuis le service admin
      this.config = await getStorageConfig()

      // Initialiser NFT.Storage si activé
      if (this.config.ipfs.enabled && this.config.ipfs.provider === "nft.storage" && this.config.ipfs.apiKey) {
        this.nftStorageClient = new NFTStorage({ token: this.config.ipfs.apiKey })
      }

      // Initialiser Arweave si activé
      if (this.config.arweave.enabled) {
        this.arweaveClient = Arweave.init({
          host: "arweave.net",
          port: 443,
          protocol: "https",
        })
      }

      return true
    } catch (error) {
      console.error("Error initializing storage service:", error)
      return false
    }
  }

  // Modifier la méthode uploadToIPFS pour supporter Filebase
  static async uploadToIPFS(file: File): Promise<string | null> {
    try {
      // S'assurer que le service est initialisé
      if (!this.config || !this.config.ipfs.enabled) {
        await this.initialize()
      }

      // Si le fournisseur est Filebase, utiliser l'API S3 de Filebase
      if (this.config?.ipfs.provider === "filebase") {
        const formData = new FormData()
        formData.append("file", file)

        const response = await fetch("https://s3.filebase.com/ipfs-upload", {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.config.ipfs.apiKey}`,
          },
          body: formData,
        })

        if (!response.ok) {
          throw new Error(`Filebase upload failed: ${response.statusText}`)
        }

        const data = await response.json()
        const cid = data.cid

        // Construire l'URL avec le gateway configuré
        const gateway = this.config?.ipfs.gateway || "https://ipfs.filebase.io/ipfs/"
        const ipfsUrl = `${gateway}${cid}`

        console.log(`File uploaded to IPFS via Filebase: ${ipfsUrl}`)
        return ipfsUrl
      } else if (this.nftStorageClient) {
        // Utiliser NFT.Storage comme avant
        const cid = await this.nftStorageClient.storeBlob(file)
        const gateway = this.config?.ipfs.gateway || "https://ipfs.io/ipfs/"
        const ipfsUrl = `${gateway}${cid}`
        console.log(`File uploaded to IPFS: ${ipfsUrl}`)
        return ipfsUrl
      } else {
        throw new Error("No IPFS client initialized")
      }
    } catch (error) {
      console.error("Error uploading to IPFS:", error)
      return null
    }
  }

  /**
   * Télécharger des métadonnées sur IPFS
   */
  static async uploadMetadataToIPFS(metadata: any): Promise<string | null> {
    try {
      // S'assurer que le service est initialisé
      if (!this.config || !this.config.ipfs.enabled) {
        await this.initialize()
      }

      if (!this.nftStorageClient) {
        throw new Error("NFT.Storage client not initialized")
      }

      // Convertir les métadonnées en Blob
      const metadataBlob = new Blob([JSON.stringify(metadata)], { type: "application/json" })

      // Télécharger le blob
      const cid = await this.nftStorageClient.storeBlob(metadataBlob)

      // Construire l'URL avec le gateway configuré
      const gateway = this.config?.ipfs.gateway || "https://ipfs.io/ipfs/"
      const ipfsUrl = `${gateway}${cid}`

      console.log(`Metadata uploaded to IPFS: ${ipfsUrl}`)
      return ipfsUrl
    } catch (error) {
      console.error("Error uploading metadata to IPFS:", error)
      return null
    }
  }

  /**
   * Télécharger un fichier sur Arweave
   */
  static async uploadToArweave(file: File): Promise<string | null> {
    try {
      // S'assurer que le service est initialisé
      if (!this.config || !this.config.arweave.enabled) {
        await this.initialize()
      }

      if (!this.arweaveClient || !this.config.arweave.jwk) {
        throw new Error("Arweave client not initialized or JWK not provided")
      }

      // Lire le fichier
      const fileBuffer = await file.arrayBuffer()

      // Créer la transaction
      const transaction = await this.arweaveClient.createTransaction({ data: fileBuffer }, this.config.arweave.jwk)

      // Ajouter les tags
      transaction.addTag("Content-Type", file.type)
      transaction.addTag("App-Name", "Global Finance Token Factory")

      // Signer la transaction
      await this.arweaveClient.transactions.sign(transaction, this.config.arweave.jwk)

      // Soumettre la transaction
      const uploader = await this.arweaveClient.transactions.getUploader(transaction)

      while (!uploader.isComplete) {
        await uploader.uploadChunk()
        console.log(`Uploading to Arweave... ${uploader.pctComplete}% complete`)
      }

      // Construire l'URL avec le gateway configuré
      const gateway = this.config?.arweave.gateway || "https://arweave.net/"
      const arweaveUrl = `${gateway}${transaction.id}`

      console.log(`File uploaded to Arweave: ${arweaveUrl}`)
      return arweaveUrl
    } catch (error) {
      console.error("Error uploading to Arweave:", error)
      return null
    }
  }

  /**
   * Télécharger des métadonnées sur Arweave
   */
  static async uploadMetadataToArweave(metadata: any): Promise<string | null> {
    try {
      // S'assurer que le service est initialisé
      if (!this.config || !this.config.arweave.enabled) {
        await this.initialize()
      }

      if (!this.arweaveClient || !this.config.arweave.jwk) {
        throw new Error("Arweave client not initialized or JWK not provided")
      }

      // Convertir les métadonnées en JSON
      const metadataJson = JSON.stringify(metadata)

      // Créer la transaction
      const transaction = await this.arweaveClient.createTransaction({ data: metadataJson }, this.config.arweave.jwk)

      // Ajouter les tags
      transaction.addTag("Content-Type", "application/json")
      transaction.addTag("App-Name", "Global Finance Token Factory")

      // Signer la transaction
      await this.arweaveClient.transactions.sign(transaction, this.config.arweave.jwk)

      // Soumettre la transaction
      const uploader = await this.arweaveClient.transactions.getUploader(transaction)

      while (!uploader.isComplete) {
        await uploader.uploadChunk()
        console.log(`Uploading metadata to Arweave... ${uploader.pctComplete}% complete`)
      }

      // Construire l'URL avec le gateway configuré
      const gateway = this.config?.arweave.gateway || "https://arweave.net/"
      const arweaveUrl = `${gateway}${transaction.id}`

      console.log(`Metadata uploaded to Arweave: ${arweaveUrl}`)
      return arweaveUrl
    } catch (error) {
      console.error("Error uploading metadata to Arweave:", error)
      return null
    }
  }

  /**
   * Télécharger un fichier sur le stockage préféré (IPFS ou Arweave)
   */
  static async uploadFile(file: File): Promise<string | null> {
    // S'assurer que le service est initialisé
    if (!this.config) {
      await this.initialize()
    }

    // Utiliser IPFS si activé, sinon Arweave
    if (this.config?.ipfs.enabled) {
      return this.uploadToIPFS(file)
    } else if (this.config?.arweave.enabled) {
      return this.uploadToArweave(file)
    } else {
      console.error("No storage provider enabled")
      return null
    }
  }

  /**
   * Télécharger des métadonnées sur le stockage préféré (IPFS ou Arweave)
   */
  static async uploadMetadata(metadata: any): Promise<string | null> {
    // S'assurer que le service est initialisé
    if (!this.config) {
      await this.initialize()
    }

    // Utiliser IPFS si activé, sinon Arweave
    if (this.config?.ipfs.enabled) {
      return this.uploadMetadataToIPFS(metadata)
    } else if (this.config?.arweave.enabled) {
      return this.uploadMetadataToArweave(metadata)
    } else {
      console.error("No storage provider enabled")
      return null
    }
  }
}

export default StorageService

// Fichier temporaire pour contourner toutes les vérifications d'authentification admin

// Fonction qui retourne toujours true pour les vérifications d'admin
export function isAdmin(): boolean {
  return true
}

// Fonction qui simule une authentification réussie
export function verifyAdminAuth() {
  return {
    isAuthenticated: true,
    wallet: "ADMIN_WALLET_BYPASS",
    role: "superadmin",
  }
}

// Fonction qui simule une vérification de token réussie
export function verifyAdminToken() {
  return {
    valid: true,
    wallet: "ADMIN_WALLET_BYPASS",
    role: "superadmin",
  }
}

// Fonction qui donne toutes les permissions
export function hasPermission() {
  return true
}

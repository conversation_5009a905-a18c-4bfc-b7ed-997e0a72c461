import type { PublicKey, Transaction, TransactionInstruction } from "@solana/web3.js"
import type { Program } from "@project-serum/anchor"

// Admin public key from environment
const ADMIN_PUBLIC_KEY = process.env.ADMIN_WALLET || ""

/**
 * Creates an admin validation instruction for on-chain verification
 * This should be included in transactions that require admin privileges
 */
export async function createAdminValidationInstruction(
  program: Program,
  walletPublicKey: PublicKey,
): Promise<TransactionInstruction> {
  // Verify the wallet is an admin
  if (walletPublicKey.toString() !== ADMIN_PUBLIC_KEY) {
    throw new Error("Unauthorized: Not an admin wallet")
  }

  // Create the validation instruction
  // This would call a method in your Anchor program that validates the admin
  return program.methods
    .validateAdmin()
    .accounts({
      admin: walletPublicKey,
      // Include other required accounts based on your program
    })
    .instruction()
}

/**
 * Adds admin validation to a transaction
 */
export function addAdminValidationToTransaction(
  transaction: Transaction,
  validationInstruction: TransactionInstruction,
): Transaction {
  transaction.add(validationInstruction)
  return transaction
}

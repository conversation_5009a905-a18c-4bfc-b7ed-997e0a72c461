import { Connection, type Keypair, PublicKey } from "@solana/web3.js"
import { envConfig } from "./env-config"
import dexIntegrationService, { type DexListingParams } from "./dex-integration-service"
import tokenMarketService from "./token-market-service"
import feeRevenueService from "./fee-revenue-service"

export interface AutoListingConfig {
  enabled: boolean
  minMarketCap: number // en USD
  preferredDex: "raydium" | "orca" | "jupiter" | "auto"
  autoAddLiquidity: boolean
  liquidityPercentage: number // pourcentage de la capitalisation boursière à ajouter en liquidité
  lockPeriod: number // en jours
}

export interface ListingStatus {
  tokenAddress: string
  eligible: boolean
  listed: boolean
  dex?: string
  transactionId?: string
  poolAddress?: string
  error?: string
  timestamp?: number
}

class AutoDexListingService {
  private connection: Connection
  private config: AutoListingConfig
  private listingStatuses: Map<string, ListingStatus>

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")

    // Configuration par défaut
    this.config = {
      enabled: true,
      minMarketCap: 5000, // 5000 USD
      preferredDex: "auto",
      autoAddLiquidity: true,
      liquidityPercentage: 10, // 10% de la capitalisation boursière
      lockPeriod: 180, // 180 jours (6 mois)
    }

    this.listingStatuses = new Map<string, ListingStatus>()
  }

  /**
   * Vérifie si un token est éligible pour le listing automatique
   */
  async checkEligibility(tokenAddress: string): Promise<boolean> {
    try {
      if (!this.config.enabled) {
        return false
      }

      // Vérifier si le token existe
      const mintPublicKey = new PublicKey(tokenAddress)
      const tokenInfo = await this.connection.getParsedAccountInfo(mintPublicKey)

      if (!tokenInfo.value) {
        console.error(`Token ${tokenAddress} non trouvé sur la blockchain`)
        return false
      }

      // Vérifier la capitalisation boursière
      const marketCap = await tokenMarketService.calculateMarketCap(tokenAddress)
      return marketCap >= this.config.minMarketCap
    } catch (error) {
      console.error("Erreur lors de la vérification de l'éligibilité:", error)
      return false
    }
  }

  /**
   * Liste automatiquement un token sur le DEX préféré
   */
  async autoListToken(tokenAddress: string, ownerAddress: string, feePayer: Keypair): Promise<ListingStatus> {
    try {
      console.log(`Tentative de listing automatique pour ${tokenAddress}...`)

      // Vérifier l'éligibilité
      const isEligible = await this.checkEligibility(tokenAddress)
      if (!isEligible) {
        const status: ListingStatus = {
          tokenAddress,
          eligible: false,
          listed: false,
          error: "Token non éligible pour le listing automatique",
        }
        this.listingStatuses.set(tokenAddress, status)
        return status
      }

      // Calculer la capitalisation boursière
      const marketCap = await tokenMarketService.calculateMarketCap(tokenAddress)

      // Déterminer le DEX à utiliser
      let dex: "raydium" | "orca" | "jupiter"
      if (this.config.preferredDex === "auto") {
        // Choisir le DEX en fonction de la capitalisation boursière
        if (marketCap >= 100000) {
          dex = "jupiter"
        } else if (marketCap >= 50000) {
          dex = "orca"
        } else {
          dex = "raydium"
        }
      } else {
        dex = this.config.preferredDex
      }

      // Vérifier si le token est déjà listé sur ce DEX
      const alreadyListed = await dexIntegrationService.isTokenListed(tokenAddress, dex)
      if (alreadyListed) {
        const status: ListingStatus = {
          tokenAddress,
          eligible: true,
          listed: true,
          dex,
          error: `Token déjà listé sur ${dex}`,
        }
        this.listingStatuses.set(tokenAddress, status)
        return status
      }

      // Calculer les montants de liquidité
      const liquidityAmount = 1 // 1 SOL (à ajuster en fonction de la capitalisation boursière)
      const tokenAmount = 100000 // 100,000 tokens (à ajuster en fonction du prix du token)

      // Traiter le paiement des frais de listing
      const listingFee = feeRevenueService.calculateListingFee()
      const paymentResult = await feeRevenueService.processFeePayment(feePayer, listingFee, "listing", tokenAddress)

      if (!paymentResult.success) {
        const status: ListingStatus = {
          tokenAddress,
          eligible: true,
          listed: false,
          error: `Échec du paiement des frais de listing: ${paymentResult.error}`,
        }
        this.listingStatuses.set(tokenAddress, status)
        return status
      }

      // Lister le token
      const listingParams: DexListingParams = {
        tokenAddress,
        ownerAddress,
        dex,
        liquidityAmount,
        tokenAmount,
        lockPeriod: this.config.lockPeriod,
      }

      const listingResult = await dexIntegrationService.listTokenOnDex(listingParams, feePayer)

      if (!listingResult.success) {
        const status: ListingStatus = {
          tokenAddress,
          eligible: true,
          listed: false,
          dex,
          error: listingResult.error,
        }
        this.listingStatuses.set(tokenAddress, status)
        return status
      }

      // Enregistrer et retourner le statut
      const status: ListingStatus = {
        tokenAddress,
        eligible: true,
        listed: true,
        dex,
        transactionId: listingResult.transactionId,
        poolAddress: listingResult.poolAddress,
        timestamp: Date.now(),
      }
      this.listingStatuses.set(tokenAddress, status)
      return status
    } catch (error: any) {
      console.error("Erreur lors du listing automatique:", error)
      const status: ListingStatus = {
        tokenAddress,
        eligible: false,
        listed: false,
        error: error.message || "Une erreur s'est produite lors du listing automatique",
      }
      this.listingStatuses.set(tokenAddress, status)
      return status
    }
  }

  /**
   * Récupère le statut de listing d'un token
   */
  getListingStatus(tokenAddress: string): ListingStatus | null {
    return this.listingStatuses.get(tokenAddress) || null
  }

  /**
   * Met à jour la configuration du service
   */
  updateConfig(newConfig: Partial<AutoListingConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig,
    }
  }

  /**
   * Récupère la configuration actuelle
   */
  getConfig(): AutoListingConfig {
    return { ...this.config }
  }

  /**
   * Vérifie et liste automatiquement tous les tokens éligibles
   */
  async checkAndListEligibleTokens(feePayer: Keypair): Promise<{
    processed: number
    listed: number
    failed: number
    statuses: ListingStatus[]
  }> {
    try {
      console.log("Vérification et listing des tokens éligibles...")

      // Dans une implémentation réelle, nous récupérerions la liste des tokens depuis une base de données
      // Pour cette simulation, nous allons supposer que nous avons déjà une liste de tokens

      const tokenAddresses = ["token1", "token2", "token3", "token4", "token5"]

      let processed = 0
      let listed = 0
      let failed = 0
      const statuses: ListingStatus[] = []

      for (const tokenAddress of tokenAddresses) {
        processed++

        // Vérifier l'éligibilité
        const isEligible = await this.checkEligibility(tokenAddress)
        if (!isEligible) {
          const status: ListingStatus = {
            tokenAddress,
            eligible: false,
            listed: false,
            error: "Token non éligible pour le listing automatique",
          }
          statuses.push(status)
          continue
        }

        // Tenter de lister le token
        const listingStatus = await this.autoListToken(
          tokenAddress,
          "ownerAddress", // À remplacer par l'adresse réelle du propriétaire
          feePayer,
        )

        statuses.push(listingStatus)

        if (listingStatus.listed) {
          listed++
        } else {
          failed++
        }
      }

      return {
        processed,
        listed,
        failed,
        statuses,
      }
    } catch (error) {
      console.error("Erreur lors de la vérification et du listing des tokens éligibles:", error)
      return {
        processed: 0,
        listed: 0,
        failed: 0,
        statuses: [],
      }
    }
  }
}

// Exporter une instance singleton du service
const autoDexListingService = new AutoDexListingService()
export default autoDexListingService

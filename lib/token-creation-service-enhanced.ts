import { Connection, Keypair, PublicKey, Transaction } from "@solana/web3.js"
import {
  createInitializeMintInstruction,
  createMintToInstruction,
  getMinimumBalanceForRentExemption,
  getMint,
  TOKEN_PROGRAM_ID,
  MINT_SIZE,
  getOrCreateAssociatedTokenAccount,
} from "@solana/spl-token"
import { envConfig } from "./env-config"
import { sanitizeToBase58 } from "./base58-sanitizer"
import TokenSuffixService from "./token-suffix-service"
import TokenPaymentService from "./token-payment-service"
import type { TokenSecurityConfig, TokenDistribution } from "./token-types"
import { createTokenMetadata } from "./token-metadata-service"
import { setupTokenSecurity } from "./token-security-service"
import { setupBondingCurve } from "./token-bonding-service"
import { createImpactPriceWallet } from "./token-impact-service"

export interface EnhancedTokenCreationParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: number
  maxSupply?: number
  ownerAddress: string
  suffix: string // "GFMM" ou "GFAI"
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  imageUrl?: string
  security?: TokenSecurityConfig
  distribution?: TokenDistribution
  isAIGenerated?: boolean
  referrerAddress?: string // Adresse du parrain si applicable
}

export interface TokenCreationResult {
  success: boolean
  tokenAddress?: string
  transactionId?: string
  tokenData?: any
  securityData?: any
  error?: string
  impactPriceWallet?: string
  burnWallet?: string
}

class EnhancedTokenCreationService {
  private connection: Connection

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
  }

  /**
   * Prépare la création d'un token en générant un keypair avec le suffixe demandé
   */
  async prepareTokenCreation(params: EnhancedTokenCreationParams): Promise<{
    success: boolean
    mintKeypair?: Keypair
    error?: string
  }> {
    try {
      console.log(`Préparation de la création du token: ${params.name} (${params.symbol})`)

      // Sanitize inputs
      const sanitizedSymbol = sanitizeToBase58(params.symbol)
      if (sanitizedSymbol !== params.symbol) {
        console.warn(`Le symbole a été sanitisé: ${params.symbol} -> ${sanitizedSymbol}`)
      }

      // Vérifier le suffixe
      if (!params.suffix) {
        return {
          success: false,
          error: "Un suffixe est requis (GFMM ou GFAI)",
        }
      }

      console.log(`Génération d'un keypair avec le suffixe: ${params.suffix}`)
      const result = await TokenSuffixService.generateKeypairWithSuffix(params.suffix)
      if (!result.success || !result.keypair) {
        return {
          success: false,
          error: result.error || "Échec de la génération du keypair avec suffixe",
        }
      }

      return {
        success: true,
        mintKeypair: Keypair.fromSecretKey(Uint8Array.from(result.keypair.secret)),
      }
    } catch (error: any) {
      console.error("Erreur lors de la préparation de la création du token:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la préparation de la création du token",
      }
    }
  }

  /**
   * Crée un token avancé sur la blockchain Solana
   */
  async createEnhancedToken(
    params: EnhancedTokenCreationParams,
    paymentSignature: string,
    mintKeypair: Keypair,
  ): Promise<TokenCreationResult> {
    try {
      console.log(`Création du token avancé: ${params.name} (${params.symbol}${params.suffix})`)

      // Vérifier le paiement
      const paymentVerified = await TokenPaymentService.verifyPayment(paymentSignature)
      if (!paymentVerified) {
        return {
          success: false,
          error: "La vérification du paiement a échoué",
        }
      }

      // Récupérer la clé publique du propriétaire
      const ownerPublicKey = new PublicKey(params.ownerAddress)

      // Calculer le montant total de tokens à créer
      const totalSupply = params.initialSupply * Math.pow(10, params.decimals)

      // Calculer le loyer minimum pour le compte de mint
      const lamports = await getMinimumBalanceForRentExemption(MINT_SIZE, this.connection)

      // Créer une transaction pour initialiser le mint
      const transaction = new Transaction()

      // Ajouter l'instruction pour créer le compte de mint
      transaction.add(
        // Créer le compte de mint
        createInitializeMintInstruction(
          mintKeypair.publicKey,
          params.decimals,
          ownerPublicKey,
          ownerPublicKey, // Freeze authority
          TOKEN_PROGRAM_ID,
        ),
      )

      // Créer un compte de token associé pour le propriétaire
      const associatedTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        mintKeypair, // Utilisé comme payeur pour cette opération
        mintKeypair.publicKey,
        ownerPublicKey,
      )

      // Ajouter l'instruction pour minter les tokens au propriétaire
      transaction.add(
        createMintToInstruction(
          mintKeypair.publicKey,
          associatedTokenAccount.address,
          ownerPublicKey,
          BigInt(totalSupply),
          [],
          TOKEN_PROGRAM_ID,
        ),
      )

      // Créer les métadonnées du token
      const metadataResult = await createTokenMetadata(
        this.connection,
        mintKeypair,
        ownerPublicKey,
        params.name,
        params.symbol + params.suffix,
        params.description || "",
        params.imageUrl || "",
      )

      if (metadataResult.success) {
        transaction.add(...metadataResult.instructions)
      }

      // Créer les wallets spéciaux (impact price, burn)
      const impactPriceWallet = await createImpactPriceWallet(this.connection, mintKeypair, mintKeypair.publicKey)

      const burnWallet = Keypair.generate()

      // Configurer les mécanismes de sécurité
      const securityResult = await setupTokenSecurity(
        this.connection,
        mintKeypair,
        ownerPublicKey,
        params.security || {
          antiBot: true,
          antiDump: true,
          maxTxAmount: params.initialSupply * 0.01, // 1% de l'offre totale
          maxWalletAmount: params.initialSupply * 0.05, // 5% de l'offre totale
        },
      )

      if (securityResult.success) {
        transaction.add(...securityResult.instructions)
      }

      // Configurer la bonding curve si nécessaire
      const bondingResult = await setupBondingCurve(this.connection, mintKeypair, ownerPublicKey, {
        initialPrice: 0.0001,
        reserveRatio: 0.2,
        initialSupply: params.initialSupply,
        maxSupply: params.maxSupply || params.initialSupply * 10,
      })

      if (bondingResult.success) {
        transaction.add(...bondingResult.instructions)
      }

      // Signer et envoyer la transaction
      transaction.feePayer = mintKeypair.publicKey
      transaction.recentBlockhash = (await this.connection.getLatestBlockhash()).blockhash

      // Signer la transaction avec le keypair du mint
      transaction.sign(mintKeypair)

      // Envoyer la transaction
      const signature = await this.connection.sendRawTransaction(transaction.serialize())

      // Attendre la confirmation
      await this.connection.confirmTransaction(signature)

      console.log(`Token créé avec succès: ${mintKeypair.publicKey.toString()}`)

      // Récupérer les informations du token créé
      const tokenInfo = await getMint(this.connection, mintKeypair.publicKey)

      // Enregistrer le parrainage si applicable
      if (params.referrerAddress) {
        await this.recordReferral(params.referrerAddress, params.ownerAddress, mintKeypair.publicKey.toString())
      }

      // Préparer les données du token pour le retour
      const tokenData = {
        address: mintKeypair.publicKey.toString(),
        name: params.name,
        symbol: params.symbol + params.suffix,
        decimals: tokenInfo.decimals,
        totalSupply: Number(tokenInfo.supply) / Math.pow(10, tokenInfo.decimals),
        mintAuthority: tokenInfo.mintAuthority?.toString() || null,
        freezeAuthority: tokenInfo.freezeAuthority?.toString() || null,
        isInitialized: tokenInfo.isInitialized,
        ownerAddress: params.ownerAddress,
        description: params.description || "",
        website: params.website || "",
        twitter: params.twitter || "",
        telegram: params.telegram || "",
        imageUrl: params.imageUrl || "",
        isAIGenerated: params.isAIGenerated || false,
        createdAt: new Date().toISOString(),
      }

      return {
        success: true,
        tokenAddress: mintKeypair.publicKey.toString(),
        transactionId: signature,
        tokenData,
        securityData: securityResult.data,
        impactPriceWallet: impactPriceWallet.toString(),
        burnWallet: burnWallet.publicKey.toString(),
      }
    } catch (error: any) {
      console.error("Erreur lors de la création du token:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la création du token",
      }
    }
  }

  /**
   * Enregistre un parrainage pour le calcul des récompenses
   */
  private async recordReferral(referrerAddress: string, creatorAddress: string, tokenAddress: string): Promise<void> {
    try {
      // Dans une implémentation réelle, cela serait enregistré dans une base de données
      console.log(
        `Enregistrement du parrainage: ${referrerAddress} a parrainé ${creatorAddress} pour le token ${tokenAddress}`,
      )

      // Ici, nous pourrions appeler une API pour enregistrer le parrainage
      // await fetch('/api/referrals', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ referrer: referrerAddress, creator: creatorAddress, token: tokenAddress }),
      // })
    } catch (error) {
      console.error("Erreur lors de l'enregistrement du parrainage:", error)
    }
  }
}

// Exporter une instance singleton du service
const enhancedTokenCreationService = new EnhancedTokenCreationService()
export default enhancedTokenCreationService

import { Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL } from "@solana/web3.js"
import {
  createAssociatedTokenAccountInstruction,
  getAssociatedTokenAddress,
  createMintToInstruction,
  TOKEN_PROGRAM_ID,
} from "@solana/spl-token"
import { envConfig } from "./env-config"
import bondingCurveService from "./bonding-curve-service"
import secureKeyService from "./secure-key-service"

export interface PurchaseParams {
  tokenAddress: string
  buyerAddress: string
  amountInSol: number
  currentSupply: number
}

export interface PurchaseResult {
  success: boolean
  txSignature?: string
  tokensReceived?: number
  error?: string
}

/**
 * Service for handling token purchases via bonding curve
 */
class TokenPurchaseService {
  private connection: Connection
  private platformFeePercent = 1 // 1% platform fee

  constructor() {
    const rpcUrl = envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com"
    this.connection = new Connection(rpcUrl, "confirmed")
  }

  /**
   * Process a token purchase
   */
  async processPurchase(params: PurchaseParams): Promise<PurchaseResult> {
    try {
      const { tokenAddress, buyerAddress, amountInSol, currentSupply } = params

      // Get the platform keypair
      const platformKeypair = secureKeyService.getPlatformKeypair()
      if (!platformKeypair) {
        throw new Error("Platform keypair not available. Check environment variables.")
      }

      // Convert addresses to PublicKey objects
      const tokenPublicKey = new PublicKey(tokenAddress)
      const buyerPublicKey = new PublicKey(buyerAddress)

      // Simulate the purchase to get the number of tokens to mint
      const purchaseSimulation = bondingCurveService.simulatePurchase(tokenAddress, currentSupply, amountInSol)

      // Calculate the platform fee
      const platformFeeAmount = amountInSol * (this.platformFeePercent / 100)

      // Create a transaction for the SOL transfer (buyer to platform)
      const solTransferTx = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: buyerPublicKey,
          toPubkey: platformKeypair.publicKey,
          lamports: Math.floor(amountInSol * LAMPORTS_PER_SOL),
        }),
      )

      // Get the buyer's associated token account
      const buyerTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, buyerPublicKey)

      // Check if the buyer's token account exists
      const buyerTokenAccountInfo = await this.connection.getAccountInfo(buyerTokenAccount)

      // If the buyer's token account doesn't exist, create it
      if (!buyerTokenAccountInfo) {
        solTransferTx.add(
          createAssociatedTokenAccountInstruction(buyerPublicKey, buyerTokenAccount, buyerPublicKey, tokenPublicKey),
        )
      }

      // Add the recent blockhash and fee payer
      const { blockhash } = await this.connection.getLatestBlockhash()
      solTransferTx.recentBlockhash = blockhash
      solTransferTx.feePayer = buyerPublicKey

      // This transaction needs to be signed by the buyer
      // In a real implementation, this would be sent to the frontend for signing

      // Create a transaction for minting tokens to the buyer
      const mintTx = new Transaction()

      // Add instruction to mint tokens to the buyer
      mintTx.add(
        createMintToInstruction(
          tokenPublicKey,
          buyerTokenAccount,
          platformKeypair.publicKey,
          BigInt(Math.floor(purchaseSimulation.tokensToReceive * Math.pow(10, 9))), // 9 decimals
          [],
          TOKEN_PROGRAM_ID,
        ),
      )

      // Add the recent blockhash and fee payer
      mintTx.recentBlockhash = blockhash
      mintTx.feePayer = platformKeypair.publicKey

      // Sign the mint transaction with the platform keypair
      mintTx.sign(platformKeypair)

      // Send and confirm the mint transaction
      const mintTxSignature = await this.connection.sendRawTransaction(mintTx.serialize())
      await this.connection.confirmTransaction(mintTxSignature)

      return {
        success: true,
        txSignature: mintTxSignature,
        tokensReceived: purchaseSimulation.tokensToReceive,
      }
    } catch (error: any) {
      console.error("Error processing token purchase:", error)
      return {
        success: false,
        error: error.message || "An error occurred while processing the purchase",
      }
    }
  }

  /**
   * Create a client-side transaction for SOL transfer
   * This transaction will be sent to the frontend for signing by the buyer
   */
  async createSolTransferTransaction(params: PurchaseParams): Promise<Transaction> {
    const { tokenAddress, buyerAddress, amountInSol } = params

    // Get the platform keypair
    const platformKeypair = secureKeyService.getPlatformKeypair()
    if (!platformKeypair) {
      throw new Error("Platform keypair not available. Check environment variables.")
    }

    // Convert addresses to PublicKey objects
    const tokenPublicKey = new PublicKey(tokenAddress)
    const buyerPublicKey = new PublicKey(buyerAddress)

    // Create a transaction for the SOL transfer (buyer to platform)
    const solTransferTx = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: buyerPublicKey,
        toPubkey: platformKeypair.publicKey,
        lamports: Math.floor(amountInSol * LAMPORTS_PER_SOL),
      }),
    )

    // Get the buyer's associated token account
    const buyerTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, buyerPublicKey)

    // Check if the buyer's token account exists
    const buyerTokenAccountInfo = await this.connection.getAccountInfo(buyerTokenAccount)

    // If the buyer's token account doesn't exist, create it
    if (!buyerTokenAccountInfo) {
      solTransferTx.add(
        createAssociatedTokenAccountInstruction(buyerPublicKey, buyerTokenAccount, buyerPublicKey, tokenPublicKey),
      )
    }

    // Add the recent blockhash and fee payer
    const { blockhash } = await this.connection.getLatestBlockhash()
    solTransferTx.recentBlockhash = blockhash
    solTransferTx.feePayer = buyerPublicKey

    return solTransferTx
  }
}

// Export a singleton instance
const tokenPurchaseService = new TokenPurchaseService()
export default tokenPurchaseService

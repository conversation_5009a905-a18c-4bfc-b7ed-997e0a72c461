import { Pool } from "pg"
import type { TokenLaunchConfig, LaunchPhase } from "./quantum-launch-service"

// Initialiser la connexion à la base de données
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === "production" ? { rejectUnauthorized: false } : false,
})

class QuantumDbService {
  // Créer une nouvelle configuration de lancement
  async createLaunchConfig(config: TokenLaunchConfig): Promise<number> {
    const client = await pool.connect()
    try {
      await client.query("BEGIN")

      const result = await client.query(
        `INSERT INTO quantum_launch_configs (
          name, symbol, suffix, decimals, total_supply, description, website, twitter, telegram,
          initial_price, soft_cap, hard_cap, min_buy, max_buy,
          liquidity_percentage, team_percentage, marketing_percentage, reserve_percentage,
          liquidity_lock_period, team_lock_period,
          presale_enabled, fair_launch_enabled, ido_enabled,
          anti_bot, anti_dump, max_wallet_percentage, max_tx_percentage,
          buy_tax, sell_tax, transfer_tax,
          team_wallet, marketing_wallet, target_dex, listing_multiplier
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34) RETURNING id`,
        [
          config.name,
          config.symbol,
          config.suffix,
          config.decimals,
          config.totalSupply,
          config.description,
          config.website,
          config.twitter,
          config.telegram,
          config.initialPrice,
          config.softCap,
          config.hardCap,
          config.minBuy,
          config.maxBuy,
          config.liquidityPercentage,
          config.teamPercentage,
          config.marketingPercentage,
          config.reservePercentage,
          config.liquidityLockPeriod,
          config.teamLockPeriod,
          config.phases.presale,
          config.phases.fairLaunch,
          config.phases.initialDexOffering,
          config.antiBot,
          config.antiDump,
          config.maxWalletPercentage,
          config.maxTxPercentage,
          config.buyTax,
          config.sellTax,
          config.transferTax,
          config.teamWallet,
          config.marketingWallet,
          config.targetDex,
          config.listingMultiplier,
        ],
      )

      await client.query("COMMIT")
      return result.rows[0].id
    } catch (error) {
      await client.query("ROLLBACK")
      console.error("Error creating launch config:", error)
      throw error
    } finally {
      client.release()
    }
  }

  // Créer un nouveau lancement
  async createLaunch(launchId: string, tokenAddress: string, ownerAddress: string, configId: number): Promise<void> {
    const client = await pool.connect()
    try {
      await client.query("BEGIN")

      await client.query(
        `INSERT INTO quantum_launches (
          id, token_address, owner_address, config_id, status
        ) VALUES ($1, $2, $3, $4, $5)`,
        [launchId, tokenAddress, ownerAddress, configId, "setup"],
      )

      await client.query("COMMIT")
    } catch (error) {
      await client.query("ROLLBACK")
      console.error("Error creating launch:", error)
      throw error
    } finally {
      client.release()
    }
  }

  // Créer une phase de lancement
  async createLaunchPhase(phase: LaunchPhase, launchId: string): Promise<void> {
    const client = await pool.connect()
    try {
      await client.query("BEGIN")

      await client.query(
        `INSERT INTO quantum_launch_phases (
          id, launch_id, name, description, start_date, end_date,
          target_amount, min_contribution, max_contribution, price, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
        [
          phase.id,
          launchId,
          phase.name,
          phase.description,
          phase.startDate,
          phase.endDate,
          phase.targetAmount,
          phase.minContribution,
          phase.maxContribution,
          phase.price,
          phase.status,
        ],
      )

      await client.query("COMMIT")
    } catch (error) {
      await client.query("ROLLBACK")
      console.error("Error creating launch phase:", error)
      throw error
    } finally {
      client.release()
    }
  }

  // Mettre à jour la phase actuelle d'un lancement
  async updateCurrentPhase(launchId: string, phaseId: string): Promise<void> {
    const client = await pool.connect()
    try {
      await client.query("BEGIN")

      await client.query(
        `UPDATE quantum_launches SET current_phase_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2`,
        [phaseId, launchId],
      )

      await client.query("COMMIT")
    } catch (error) {
      await client.query("ROLLBACK")
      console.error("Error updating current phase:", error)
      throw error
    } finally {
      client.release()
    }
  }

  // Mettre à jour le statut d'un lancement
  async updateLaunchStatus(launchId: string, status: string): Promise<void> {
    const client = await pool.connect()
    try {
      await client.query("BEGIN")

      await client.query(`UPDATE quantum_launches SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2`, [
        status,
        launchId,
      ])

      await client.query("COMMIT")
    } catch (error) {
      await client.query("ROLLBACK")
      console.error("Error updating launch status:", error)
      throw error
    } finally {
      client.release()
    }
  }

  // Enregistrer une contribution
  async recordContribution(
    id: string,
    launchId: string,
    phaseId: string,
    contributorAddress: string,
    amount: number,
    transactionId: string,
  ): Promise<void> {
    const client = await pool.connect()
    try {
      await client.query("BEGIN")

      // Enregistrer la contribution
      await client.query(
        `INSERT INTO quantum_contributions (
          id, launch_id, phase_id, contributor_address, amount, transaction_id, timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)`,
        [id, launchId, phaseId, contributorAddress, amount, transactionId],
      )

      // Mettre à jour les statistiques de la phase
      await client.query(
        `UPDATE quantum_launch_phases 
         SET amount_raised = amount_raised + $1, 
             participants = participants + 1,
             percentage_complete = LEAST(100, (amount_raised + $1) / target_amount * 100),
             updated_at = CURRENT_TIMESTAMP
         WHERE id = $2`,
        [amount, phaseId],
      )

      // Mettre à jour les statistiques du lancement
      await client.query(
        `UPDATE quantum_launches 
         SET total_raised = total_raised + $1, 
             participants = participants + 1,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = $2`,
        [amount, launchId],
      )

      // Enregistrer la transaction
      await client.query(
        `INSERT INTO quantum_transactions (
          id, launch_id, type, hash, date, amount, status
        ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, $5, $6)`,
        [id, launchId, "contribution", transactionId, amount, "confirmed"],
      )

      await client.query("COMMIT")
    } catch (error) {
      await client.query("ROLLBACK")
      console.error("Error recording contribution:", error)
      throw error
    } finally {
      client.release()
    }
  }

  // Récupérer tous les lancements
  async getAllLaunches(status?: string, limit = 10, offset = 0): Promise<any[]> {
    try {
      let query = `
        SELECT l.*, c.name, c.symbol, c.suffix, c.description, c.initial_price,
               c.soft_cap, c.hard_cap, c.liquidity_percentage,
               p.name as current_phase_name, p.status as current_phase_status,
               p.amount_raised as current_phase_raised, p.target_amount as current_phase_target,
               p.percentage_complete as current_phase_percentage
        FROM quantum_launches l
        JOIN quantum_launch_configs c ON l.config_id = c.id
        LEFT JOIN quantum_launch_phases p ON l.current_phase_id = p.id
      `

      const params = []
      if (status) {
        query += ` WHERE l.status = $1`
        params.push(status)
      }

      query += ` ORDER BY l.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`
      params.push(limit, offset)

      const result = await pool.query(query, params)
      return result.rows
    } catch (error) {
      console.error("Error getting launches:", error)
      throw error
    }
  }

  // Récupérer un lancement par ID
  async getLaunchById(id: string): Promise<any> {
    try {
      const launchResult = await pool.query(
        `SELECT l.*, c.*
         FROM quantum_launches l
         JOIN quantum_launch_configs c ON l.config_id = c.id
         WHERE l.id = $1`,
        [id],
      )

      if (launchResult.rows.length === 0) {
        return null
      }

      const launch = launchResult.rows[0]

      // Récupérer les phases
      const phasesResult = await pool.query(
        `SELECT * FROM quantum_launch_phases WHERE launch_id = $1 ORDER BY start_date`,
        [id],
      )

      // Récupérer la phase actuelle
      const currentPhaseResult = await pool.query(`SELECT * FROM quantum_launch_phases WHERE id = $1`, [
        launch.current_phase_id,
      ])

      // Récupérer les transactions
      const transactionsResult = await pool.query(
        `SELECT * FROM quantum_transactions WHERE launch_id = $1 ORDER BY date DESC LIMIT 10`,
        [id],
      )

      // Récupérer les listings DEX
      const dexListingsResult = await pool.query(`SELECT * FROM quantum_dex_listings WHERE launch_id = $1`, [id])

      return {
        ...launch,
        phases: phasesResult.rows,
        currentPhase: currentPhaseResult.rows.length > 0 ? currentPhaseResult.rows[0] : null,
        transactions: transactionsResult.rows,
        dexListings: dexListingsResult.rows,
      }
    } catch (error) {
      console.error("Error getting launch by ID:", error)
      throw error
    }
  }

  // Récupérer les lancements d'un utilisateur
  async getUserLaunches(ownerAddress: string): Promise<any[]> {
    try {
      const result = await pool.query(
        `SELECT l.*, c.name, c.symbol, c.suffix, c.description, c.initial_price,
                c.soft_cap, c.hard_cap, c.liquidity_percentage,
                p.name as current_phase_name, p.status as current_phase_status,
                p.amount_raised as current_phase_raised, p.target_amount as current_phase_target,
                p.percentage_complete as current_phase_percentage
         FROM quantum_launches l
         JOIN quantum_launch_configs c ON l.config_id = c.id
         LEFT JOIN quantum_launch_phases p ON l.current_phase_id = p.id
         WHERE l.owner_address = $1
         ORDER BY l.created_at DESC`,
        [ownerAddress],
      )

      return result.rows
    } catch (error) {
      console.error("Error getting user launches:", error)
      throw error
    }
  }

  // Récupérer les contributions d'un utilisateur
  async getUserContributions(contributorAddress: string): Promise<any[]> {
    try {
      const result = await pool.query(
        `SELECT c.*, l.token_address, lc.name, lc.symbol, lc.suffix, 
                p.name as phase_name, p.price
         FROM quantum_contributions c
         JOIN quantum_launches l ON c.launch_id = l.id
         JOIN quantum_launch_configs lc ON l.config_id = lc.id
         JOIN quantum_launch_phases p ON c.phase_id = p.id
         WHERE c.contributor_address = $1
         ORDER BY c.timestamp DESC`,
        [contributorAddress],
      )

      return result.rows
    } catch (error) {
      console.error("Error getting user contributions:", error)
      throw error
    }
  }
}

export const quantumDbService = new QuantumDbService()

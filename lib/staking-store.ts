import { create } from "zustand"
import { persist } from "zustand/middleware"

export interface StakingPool {
  id: string
  tokenMint: string
  tokenName: string
  tokenSymbol: string
  apr: number
  lockPeriod: number // en jours
  totalStaked: number
  stakerCount: number
  createdAt: number
  minStake: number
  maxStake: number
  creator: string
  isActive: boolean
}

export interface UserStaking {
  id: string
  poolId: string
  walletAddress: string
  amount: number
  startTime: number
  endTime: number
  rewards: number
  claimed: boolean
}

interface StakingStore {
  pools: StakingPool[]
  stakings: UserStaking[]
  addPool: (pool: StakingPool) => void
  updatePool: (id: string, updates: Partial<StakingPool>) => void
  removePool: (id: string) => void
  getPool: (id: string) => StakingPool | undefined
  getAllPools: () => StakingPool[]
  getActivePools: () => StakingPool[]
  addStaking: (staking: UserStaking) => void
  updateStaking: (id: string, updates: Partial<UserStaking>) => void
  removeStaking: (id: string) => void
  getStaking: (id: string) => UserStaking | undefined
  getUserStakings: (walletAddress: string) => UserStaking[]
}

export const useStakingStore = create<StakingStore>()(
  persist(
    (set, get) => ({
      pools: [
        {
          id: "pool_gf_token_30",
          tokenMint: "GFbeta111111111111111111111111111111111111",
          tokenName: "GF-beta",
          tokenSymbol: "GF-b1",
          apr: 15, // 15% APR
          lockPeriod: 30, // 30 jours
          totalStaked: 50000000, // 5% de l'offre totale
          stakerCount: 500,
          createdAt: Date.now() - 90 * 24 * 60 * 60 * 1000, // 90 jours
          minStake: 100,
          maxStake: 1000000,
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          isActive: true,
        },
        {
          id: "pool_gf_token_90",
          tokenMint: "GFbeta111111111111111111111111111111111111",
          tokenName: "GF-beta",
          tokenSymbol: "GF-b1",
          apr: 25, // 25% APR
          lockPeriod: 90, // 90 jours
          totalStaked: 100000000, // 10% de l'offre totale
          stakerCount: 300,
          createdAt: Date.now() - 90 * 24 * 60 * 60 * 1000, // 90 jours
          minStake: 100,
          maxStake: 1000000,
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          isActive: true,
        },
        {
          id: "pool_gf_token_180",
          tokenMint: "GFbeta111111111111111111111111111111111111",
          tokenName: "GF-beta",
          tokenSymbol: "GF-b1",
          apr: 35, // 35% APR
          lockPeriod: 180, // 180 jours
          totalStaked: 150000000, // 15% de l'offre totale
          stakerCount: 200,
          createdAt: Date.now() - 90 * 24 * 60 * 60 * 1000, // 90 jours
          minStake: 100,
          maxStake: 1000000,
          creator: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          isActive: true,
        },
      ],
      stakings: [],
      addPool: (pool) => set((state) => ({ pools: [...state.pools, pool] })),
      updatePool: (id, updates) =>
        set((state) => ({
          pools: state.pools.map((pool) => (pool.id === id ? { ...pool, ...updates } : pool)),
        })),
      removePool: (id) =>
        set((state) => ({
          pools: state.pools.filter((pool) => pool.id !== id),
        })),
      getPool: (id) => {
        return get().pools.find((pool) => pool.id === id)
      },
      getAllPools: () => {
        return get().pools
      },
      getActivePools: () => {
        return get().pools.filter((pool) => pool.isActive)
      },
      addStaking: (staking) => set((state) => ({ stakings: [...state.stakings, staking] })),
      updateStaking: (id, updates) =>
        set((state) => ({
          stakings: state.stakings.map((staking) => (staking.id === id ? { ...staking, ...updates } : staking)),
        })),
      removeStaking: (id) =>
        set((state) => ({
          stakings: state.stakings.filter((staking) => staking.id !== id),
        })),
      getStaking: (id) => {
        return get().stakings.find((staking) => staking.id === id)
      },
      getUserStakings: (walletAddress) => {
        return get().stakings.filter((staking) => staking.walletAddress === walletAddress)
      },
    }),
    {
      name: "staking-store",
    },
  ),
)

import type { PublicKey } from "@solana/web3.js"
import nacl from "tweetnacl"

export class SigninMessage {
  domain: string
  publicKey: string
  nonce: string
  statement: string

  constructor(message: string) {
    const parsedMessage = JSON.parse(message)
    this.domain = parsedMessage.domain
    this.publicKey = parsedMessage.publicKey
    this.nonce = parsedMessage.nonce
    this.statement = parsedMessage.statement
  }

  static create(
    publicKey: PublicKey,
    domain = window.location.host,
    statement = "Connexion à l'interface d'administration",
  ) {
    const nonce = nacl.randomBytes(16).reduce((p, c) => p + c.toString(16).padStart(2, "0"), "")
    return new SigninMessage(
      JSON.stringify({
        domain,
        publicKey: publicKey.toString(),
        nonce,
        statement,
      }),
    )
  }

  prepare() {
    return (
      `${this.statement}\n\n` +
      `Domaine: ${this.domain}\n` +
      `Clé publique: ${this.publicKey}\n` +
      `Nonce: ${this.nonce}`
    )
  }

  verify(publicKey: PublicKey, signature: Uint8Array) {
    const message = this.prepare()
    const messageBytes = new TextEncoder().encode(message)
    return nacl.sign.detached.verify(messageBytes, signature, publicKey.toBytes())
  }
}

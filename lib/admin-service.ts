"use server"

import { <PERSON>Key } from "@solana/web3.js"
import { jwtVerify } from "jose"

// Importation des fonctions de contournement temporaires
import { isAdmin as tempIsAdmin, verifyAdminAuth as tempVerifyAdminAuth } from "./temp-admin-bypass"

// Type for admin users
export type AdminUser = {
  walletAddress: string
  role: "admin" | "superadmin"
  addedAt: string
  addedBy?: string
  permissions?: string[]
}

// Liste des adresses de portefeuille administrateur
const adminWallets = [
  process.env.ADMIN_WALLET || "",
  // Ajoutez d'autres adresses admin si nécessaire
]

// Super admin wallets (have all permissions)
const superAdminWallets = [process.env.ADMIN_WALLET || "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5"]

// JWT secret key (should match the one in the auth API)
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || "fallback_secret_for_development_only_change_in_production",
)

/**
 * Checks if a wallet address is an admin
 */
export function isAdminWallet(walletAddress: string): boolean {
  // Temporairement désactivé - Utilise la fonction de contournement
  return tempIsAdmin()

  // Code original commenté
  // return adminWallets.includes(walletAddress)
}

// Pour la compatibilité avec le code existant
export const isAdmin = isAdminWallet

/**
 * Checks if a wallet address is a super admin
 */
export function isSuperAdmin(walletAddress: string): boolean {
  // Check if the address is in the super admin list
  return superAdminWallets.includes(walletAddress)
}

/**
 * Gets all admin wallet addresses
 */
export function getAllAdmins(): string[] {
  // Return a copy of the list to prevent accidental modifications
  return [...adminWallets]
}

/**
 * Gets the permissions for an admin
 */
export function getAdminPermissions(walletAddress: string): string[] {
  if (isSuperAdmin(walletAddress)) {
    // Super admins have all permissions
    return ["all"]
  }

  if (isAdminWallet(walletAddress)) {
    // Regular admins have these base permissions
    return ["view_dashboard", "manage_users", "manage_tokens", "view_reports"]
  }

  // If not an admin, no permissions
  return []
}

/**
 * Checks if an admin has a specific permission
 */
export function hasPermission(walletAddress: string, permission: string): boolean {
  const permissions = getAdminPermissions(walletAddress)
  // Admin has all permissions or the specific permission
  return permissions.includes("all") || permissions.includes(permission)
}

/**
 * Verifies an admin JWT token
 */
export async function verifyAdminToken(token: string): Promise<{
  valid: boolean
  wallet?: string
  role?: string
}> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)

    if (!payload || !payload.wallet || !payload.role) {
      return { valid: false }
    }

    const wallet = payload.wallet as string

    // Verify the wallet is still in the admin list
    if (!isAdminWallet(wallet)) {
      return { valid: false }
    }

    return {
      valid: true,
      wallet,
      role: payload.role as string,
    }
  } catch (error) {
    console.error("Admin token verification failed:", error)
    return { valid: false }
  }
}

// Vérification de l'authentification admin côté serveur
export async function verifyAdminAuth() {
  // Temporairement désactivé - Utilise la fonction de contournement
  return tempVerifyAdminAuth()

  // Code original commenté
  // const cookies = getCookies()
  // const adminWallet = cookies.get("admin_wallet")?.value
  // const adminToken = cookies.get("admin_token")?.value

  // if (!adminWallet || !adminToken) {
  //   return { isAuthenticated: false, wallet: null }
  // }

  // try {
  //   // Vérification du token
  //   const decoded = jwt.verify(adminToken, process.env.JWT_SECRET || "")
  //   if (typeof decoded === "object" && decoded.wallet === adminWallet) {
  //     return { isAuthenticated: true, wallet: adminWallet }
  //   }
  // } catch (error) {
  //   console.error("Admin token verification error:", error)
  // }

  // return { isAuthenticated: false, wallet: null }
}

// Available permissions in the system
export const availablePermissions = [
  {
    id: "view_dashboard",
    name: "View Dashboard",
    description: "Allows viewing the admin dashboard",
  },
  {
    id: "manage_users",
    name: "Manage Users",
    description: "Allows managing system users",
  },
  {
    id: "manage_tokens",
    name: "Manage Tokens",
    description: "Allows managing tokens and their settings",
  },
  {
    id: "manage_security",
    name: "Manage Security",
    description: "Allows managing security settings",
  },
  {
    id: "manage_fees",
    name: "Manage Fees",
    description: "Allows managing fees and commissions",
  },
  {
    id: "view_reports",
    name: "View Reports",
    description: "Allows viewing reports and statistics",
  },
  {
    id: "manage_network",
    name: "Manage Network",
    description: "Allows managing network settings",
  },
  {
    id: "manage_admins",
    name: "Manage Admins",
    description: "Allows managing administrators",
  },
  {
    id: "all",
    name: "All Permissions",
    description: "All system permissions",
  },
]

/**
 * Temporarily adds an admin (for development/testing only)
 */
export async function temporarilyAddAdmin(walletAddress: string): Promise<boolean> {
  try {
    // Verify the address has a valid format
    try {
      new PublicKey(walletAddress)
    } catch (e) {
      console.error("Invalid address format:", walletAddress)
      return false
    }

    // Don't add if already present
    if (adminWallets.includes(walletAddress)) {
      return true
    }

    // Add the address to the admin list
    adminWallets.push(walletAddress)
    console.log("Admin temporarily added:", walletAddress)
    return true
  } catch (error) {
    console.error("Error temporarily adding admin:", error)
    return false
  }
}

/**
 * Checks if an admin key is valid
 */
export async function isValidAdminKey(adminKey: string): Promise<boolean> {
  // In production, this should check against a securely stored value
  const validAdminKey = process.env.ADMIN_KEY || "admin_secret_key_for_development"
  return adminKey === validAdminKey
}

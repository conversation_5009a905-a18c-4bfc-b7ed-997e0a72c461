import { getConnection, getTokenInfo } from "@/lib/solana-service"

// Interface pour les données de marché d'un token
export interface TokenMarketData {
  address: string
  name: string
  symbol: string
  price: number
  priceChange24h: number
  marketCap: number
  volume24h: number
  holders: number
  supply: number
  createdAt: number
}

// Interface pour les données de tendance
export interface TrendingData {
  topTokens: TokenMarketData[]
  topMemecoins: TokenMarketData[]
  recentlyAdded: TokenMarketData[]
}

// Obtenir les données de marché pour un token spécifique
export const getTokenMarketData = async (mintAddress: string): Promise<TokenMarketData | null> => {
  try {
    // Dans une implémentation réelle, on interrogerait une API comme Solscan
    // Ici, on simule les données

    const connection = getConnection()
    const tokenInfo = await getTokenInfo(mintAddress)

    // Simuler un prix et d'autres données de marché
    const price = Math.random() * 0.1
    const priceChange24h = Math.random() * 20 - 10 // Entre -10% et +10%
    const volume24h = Math.random() * 1000000
    const holders = Math.floor(Math.random() * 1000) + 10

    return {
      address: mintAddress,
      name: `Token ${mintAddress.substring(0, 6)}`,
      symbol: `TKN${mintAddress.substring(0, 3)}`,
      price,
      priceChange24h,
      marketCap: price * tokenInfo.supply,
      volume24h,
      holders,
      supply: tokenInfo.supply,
      createdAt: Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000), // Entre maintenant et 30 jours avant
    }
  } catch (error) {
    console.error("Error getting token market data:", error)
    return null
  }
}

// Obtenir les données de tendance
export const getTrendingData = async (): Promise<TrendingData> => {
  try {
    // Dans une implémentation réelle, on interrogerait une API comme Solscan
    // Ici, on simule les données

    const topTokens: TokenMarketData[] = []
    const topMemecoins: TokenMarketData[] = []
    const recentlyAdded: TokenMarketData[] = []

    // Simuler des données pour les top tokens
    for (let i = 0; i < 10; i++) {
      const address = `TopToken${i}`
      topTokens.push({
        address,
        name: `Top Token ${i}`,
        symbol: `TOP${i}`,
        price: Math.random() * 100,
        priceChange24h: Math.random() * 20 - 10,
        marketCap: Math.random() * 1000000000,
        volume24h: Math.random() * 100000000,
        holders: Math.floor(Math.random() * 10000) + 1000,
        supply: Math.random() * 1000000000,
        createdAt: Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000),
      })
    }

    // Simuler des données pour les top memecoins
    for (let i = 0; i < 10; i++) {
      const address = `Memecoin${i}`
      topMemecoins.push({
        address,
        name: `Meme Coin ${i}`,
        symbol: `MEME${i}`,
        price: Math.random() * 0.01,
        priceChange24h: Math.random() * 50 - 25,
        marketCap: Math.random() * 100000000,
        volume24h: Math.random() * 10000000,
        holders: Math.floor(Math.random() * 5000) + 500,
        supply: Math.random() * 10000000000,
        createdAt: Date.now() - Math.floor(Math.random() * 180 * 24 * 60 * 60 * 1000),
      })
    }

    // Simuler des données pour les tokens récemment ajoutés
    for (let i = 0; i < 10; i++) {
      const address = `NewToken${i}`
      recentlyAdded.push({
        address,
        name: `New Token ${i}`,
        symbol: `NEW${i}`,
        price: Math.random() * 0.1,
        priceChange24h: Math.random() * 100 - 50,
        marketCap: Math.random() * 10000000,
        volume24h: Math.random() * 1000000,
        holders: Math.floor(Math.random() * 1000) + 10,
        supply: Math.random() * 1000000000,
        createdAt: Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000),
      })
    }

    return {
      topTokens,
      topMemecoins,
      recentlyAdded,
    }
  } catch (error) {
    console.error("Error getting trending data:", error)
    return {
      topTokens: [],
      topMemecoins: [],
      recentlyAdded: [],
    }
  }
}

// Obtenir l'historique des prix pour un token
export const getTokenPriceHistory = async (
  mintAddress: string,
  period: "24h" | "7d" | "30d" | "1y",
): Promise<{ time: number; price: number }[]> => {
  try {
    // Dans une implémentation réelle, on interrogerait une API comme Solscan
    // Ici, on simule les données d'historique de prix

    const now = Date.now()
    const history: { time: number; price: number }[] = []

    let dataPoints = 0
    let timeStep = 0

    switch (period) {
      case "24h":
        dataPoints = 24
        timeStep = 60 * 60 * 1000 // 1 heure
        break
      case "7d":
        dataPoints = 7
        timeStep = 24 * 60 * 60 * 1000 // 1 jour
        break
      case "30d":
        dataPoints = 30
        timeStep = 24 * 60 * 60 * 1000 // 1 jour
        break
      case "1y":
        dataPoints = 12
        timeStep = 30 * 24 * 60 * 60 * 1000 // 1 mois
        break
    }

    // Prix de base aléatoire
    const basePrice = Math.random() * 0.1

    // Générer des données d'historique
    for (let i = 0; i < dataPoints; i++) {
      const time = now - (dataPoints - i) * timeStep
      const volatility = 0.1 // 10% de volatilité
      const randomChange = (Math.random() * 2 - 1) * volatility
      const price = basePrice * (1 + randomChange) * (1 + (i / dataPoints) * 0.5) // Tendance haussière

      history.push({ time, price })
    }

    return history
  } catch (error) {
    console.error("Error getting token price history:", error)
    return []
  }
}

// Obtenir les détails des holders d'un token
export const getTokenHolders = async (mintAddress: string): Promise<{ address: string; balance: number }[]> => {
  try {
    // Dans une implémentation réelle, on interrogerait une API comme Solscan
    // Ici, on simule les données des holders

    const holders: { address: string; balance: number }[] = []

    // Simuler des données pour les holders
    const numHolders = Math.floor(Math.random() * 20) + 5
    const totalSupply = 1000000000

    for (let i = 0; i < numHolders; i++) {
      const address = `Holder${i}`
      let balance = 0

      if (i === 0) {
        // Le premier holder a une grande partie de l'offre
        balance = totalSupply * 0.3
      } else {
        // Les autres holders se partagent le reste
        balance = ((totalSupply * 0.7) / (numHolders - 1)) * (Math.random() * 0.5 + 0.75)
      }

      holders.push({ address, balance })
    }

    return holders
  } catch (error) {
    console.error("Error getting token holders:", error)
    return []
  }
}

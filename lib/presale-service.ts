import { envConfig } from "@/lib/env-config"
import { getConnection } from "@/lib/solana-service"
import { type Keypair, PublicKey, LAMPORTS_PER_SOL, Transaction, SystemProgram } from "@solana/web3.js"

// Interface pour les données d'une prévente
export interface PresaleData {
  id: string
  tokenMint: string
  tokenName: string
  tokenSymbol: string
  tokenDecimals: number
  price: number // en SOL
  hardCap: number // en tokens
  softCap: number // en tokens
  minPurchase: number // en SOL
  maxPurchase: number // en SOL
  startTime: number
  endTime: number
  raised: number // en SOL
  sold: number // en tokens
  status: "upcoming" | "active" | "ended" | "successful" | "failed"
  vestingPeriod: number // en jours
  vestingReleases: number // nombre de libérations
  createdBy: string
  createdAt: number
}

// Interface pour les données de participation à une prévente
export interface PresaleParticipation {
  presaleId: string
  walletAddress: string
  amount: number // en SOL
  tokens: number // en tokens
  timestamp: number
}

// Créer une prévente
export const createPresale = async (
  payer: Keypair,
  tokenMint: string,
  tokenName: string,
  tokenSymbol: string,
  tokenDecimals: number,
  price: number,
  hardCap: number,
  softCap: number,
  minPurchase: number,
  maxPurchase: number,
  startTime: number,
  endTime: number,
  vestingPeriod: number,
  vestingReleases: number,
): Promise<PresaleData> => {
  try {
    // Dans une implémentation réelle, on utiliserait un smart contract de prévente
    // Ici, on simule le processus

    const presaleId = `presale_${Date.now()}`

    return {
      id: presaleId,
      tokenMint,
      tokenName,
      tokenSymbol,
      tokenDecimals,
      price,
      hardCap,
      softCap,
      minPurchase,
      maxPurchase,
      startTime,
      endTime,
      raised: 0,
      sold: 0,
      status: startTime > Date.now() ? "upcoming" : "active",
      vestingPeriod,
      vestingReleases,
      createdBy: payer.publicKey.toString(),
      createdAt: Date.now(),
    }
  } catch (error) {
    console.error("Error creating presale:", error)
    throw error
  }
}

// Obtenir les préventes actives
export const getActivePresales = async (): Promise<PresaleData[]> => {
  try {
    // Dans une implémentation réelle, on interrogerait une API ou un smart contract
    // Ici, on simule les données

    const presales: PresaleData[] = []

    // Simuler des données pour les préventes actives
    const numPresales = Math.floor(Math.random() * 5) + 2

    for (let i = 0; i < numPresales; i++) {
      const now = Date.now()
      const startTime = now - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)
      const endTime = now + Math.floor(Math.random() * 14 * 24 * 60 * 60 * 1000)
      const hardCap = Math.random() * 1000000 + 100000
      const softCap = hardCap * 0.3
      const raised = hardCap * Math.random() * 0.8
      const sold = raised / (Math.random() * 0.01 + 0.001)

      presales.push({
        id: `presale_${i}`,
        tokenMint: `token_${i}`,
        tokenName: `Token ${i}`,
        tokenSymbol: `TKN${i}`,
        tokenDecimals: 9,
        price: Math.random() * 0.01 + 0.001, // Entre 0.001 et 0.011 SOL
        hardCap,
        softCap,
        minPurchase: 0.1, // 0.1 SOL
        maxPurchase: 10, // 10 SOL
        startTime,
        endTime,
        raised,
        sold,
        status: "active",
        vestingPeriod: 90, // 90 jours
        vestingReleases: 3, // 3 libérations
        createdBy: `Creator${i}`,
        createdAt: startTime - 7 * 24 * 60 * 60 * 1000,
      })
    }

    // Ajouter une prévente pour le token principal
    const now = Date.now()
    presales.push({
      id: "presale_gf_token",
      tokenMint: "gf_token_mint",
      tokenName: "GF-beta",
      tokenSymbol: "GF-b1",
      tokenDecimals: 9,
      price: 0.005, // 0.005 SOL
      hardCap: 150000000, // 15% de l'offre totale
      softCap: 50000000, // 5% de l'offre totale
      minPurchase: 0.1, // 0.1 SOL
      maxPurchase: 50, // 50 SOL
      startTime: now - 30 * 24 * 60 * 60 * 1000, // 30 jours avant
      endTime: now + 60 * 24 * 60 * 60 * 1000, // 60 jours après
      raised: 500, // 500 SOL
      sold: 100000000, // 10% de l'offre totale
      status: "active",
      vestingPeriod: 90, // 90 jours
      vestingReleases: 3, // 3 libérations
      createdBy: envConfig.ADMIN_WALLET,
      createdAt: now - 45 * 24 * 60 * 60 * 1000,
    })

    return presales
  } catch (error) {
    console.error("Error getting active presales:", error)
    return []
  }
}

// Participer à une prévente
export const participateInPresale = async (
  payer: Keypair,
  presaleId: string,
  amount: number,
): Promise<PresaleParticipation> => {
  try {
    // Dans une implémentation réelle, on utiliserait un smart contract de prévente
    // Ici, on simule le processus

    const connection = getConnection()

    // Simuler une transaction SOL
    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: payer.publicKey,
        toPubkey: new PublicKey(envConfig.ADMIN_WALLET),
        lamports: amount * LAMPORTS_PER_SOL,
      }),
    )

    // Signer et envoyer la transaction
    const signature = await connection.sendTransaction(transaction, [payer])

    console.log(`Participated in presale ${presaleId} with ${amount} SOL. Transaction: ${signature}`)

    // Simuler les tokens reçus
    const tokensReceived = amount / 0.005 // 0.005 SOL par token

    return {
      presaleId,
      walletAddress: payer.publicKey.toString(),
      amount,
      tokens: tokensReceived,
      timestamp: Date.now(),
    }
  } catch (error) {
    console.error("Error participating in presale:", error)
    throw error
  }
}

// Obtenir les participations d'un utilisateur
export const getUserPresaleParticipations = async (walletAddress: string): Promise<PresaleParticipation[]> => {
  try {
    // Dans une implémentation réelle, on interrogerait une API ou un smart contract
    // Ici, on simule les données

    const participations: PresaleParticipation[] = []

    // Simuler des données pour les participations de l'utilisateur
    const numParticipations = Math.floor(Math.random() * 3) + 1

    for (let i = 0; i < numParticipations; i++) {
      const amount = Math.random() * 10 + 0.1 // Entre 0.1 et 10.1 SOL
      const tokensReceived = amount / 0.005 // 0.005 SOL par token

      participations.push({
        presaleId: `presale_${i}`,
        walletAddress,
        amount,
        tokens: tokensReceived,
        timestamp: Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000),
      })
    }

    return participations
  } catch (error) {
    console.error("Error getting user presale participations:", error)
    return []
  }
}

// Finaliser une prévente
export const finalizePresale = async (payer: Keypair, presaleId: string): Promise<boolean> => {
  try {
    // Dans une implémentation réelle, on utiliserait un smart contract de prévente
    // Ici, on simule le processus

    console.log(`Finalizing presale ${presaleId}`)

    return true
  } catch (error) {
    console.error("Error finalizing presale:", error)
    return false
  }
}

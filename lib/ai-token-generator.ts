import { OpenAI } from "openai"
import type { TokenCreationParams } from "./token-creation-service"

// Initialiser le client OpenAI
const openai = new OpenAI({
  apiKey: process.env.DEEPSEEK_API_KEY || process.env.OPENAI_API_KEY,
})

export interface AITokenGenerationResult {
  success: boolean
  tokenParams?: TokenCreationParams
  error?: string
}

/**
 * Génère les paramètres d'un token à partir d'un prompt
 */
export async function generateTokenFromPrompt(prompt: string): Promise<AITokenGenerationResult> {
  try {
    console.log(`Génération d'un token à partir du prompt: ${prompt}`)

    // Construire le prompt pour l'IA
    const systemPrompt = `Tu es un expert en création de tokens sur la blockchain. 
    Tu dois générer les paramètres d'un token basé sur la description fournie par l'utilisateur.
    Réponds uniquement avec un objet JSON contenant les paramètres suivants:
    - name: le nom complet du token (max 50 caractères)
    - symbol: le symbole du token (3-5 caractères, lettres majuscules)
    - decimals: le nombre de décimales (généralement 9 pour Solana)
    - initialSupply: l'offre initiale (généralement entre 1 million et 1 milliard)
    - description: une description marketing du token (max 200 caractères)
    - website: une URL fictive pour le site web du token (format https://example.com)
    - twitter: une URL fictive pour le compte Twitter du token (format https://twitter.com/username)
    - telegram: une URL fictive pour le groupe Telegram du token (format https://t.me/username)`

    // Appeler l'API OpenAI
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: prompt },
      ],
      temperature: 0.7,
      max_tokens: 500,
    })

    // Extraire la réponse
    const content = response.choices[0]?.message?.content
    if (!content) {
      throw new Error("Aucune réponse générée par l'IA")
    }

    // Analyser la réponse JSON
    let tokenParams: TokenCreationParams
    try {
      // Extraire le JSON de la réponse (au cas où l'IA ajoute du texte autour)
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error("Format de réponse invalide")
      }

      const jsonContent = jsonMatch[0]
      const parsedResponse = JSON.parse(jsonContent)

      // Construire les paramètres du token
      tokenParams = {
        name: parsedResponse.name || "AI Generated Token",
        symbol: parsedResponse.symbol || "AGT",
        decimals: parsedResponse.decimals || 9,
        initialSupply: parsedResponse.initialSupply || 1000000000,
        description: parsedResponse.description || "Token généré par IA",
        website: parsedResponse.website || "https://example.com",
        twitter: parsedResponse.twitter || "https://twitter.com/aitoken",
        telegram: parsedResponse.telegram || "https://t.me/aitoken",
        ownerAddress: "", // Sera rempli plus tard
        isAIGenerated: true, // Marquer comme généré par IA
      }

      // Valider les paramètres
      if (!tokenParams.name || !tokenParams.symbol) {
        throw new Error("Paramètres de token incomplets")
      }

      return {
        success: true,
        tokenParams,
      }
    } catch (error: any) {
      console.error("Erreur lors de l'analyse de la réponse de l'IA:", error)
      console.error("Réponse brute:", content)

      // Créer des paramètres par défaut en cas d'erreur
      tokenParams = {
        name: "AI Generated Token",
        symbol: "AGT",
        decimals: 9,
        initialSupply: 1000000000,
        description: "Token généré par IA à partir du prompt: " + prompt,
        website: "https://example.com",
        twitter: "https://twitter.com/aitoken",
        telegram: "https://t.me/aitoken",
        ownerAddress: "", // Sera rempli plus tard
        isAIGenerated: true, // Marquer comme généré par IA
      }

      return {
        success: true,
        tokenParams,
      }
    }
  } catch (error: any) {
    console.error("Erreur lors de la génération du token par IA:", error)
    return {
      success: false,
      error: error.message || "Une erreur s'est produite lors de la génération du token par IA",
    }
  }
}

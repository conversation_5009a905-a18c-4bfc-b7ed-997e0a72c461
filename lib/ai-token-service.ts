import { Configuration, OpenAIApi } from "openai"
import { analyzeTwitterTrends } from "./twitter-analysis-service"
import { analyzeCryptoMarket } from "./crypto-analysis-service"

// Configuration de l'API OpenAI
const configuration = new Configuration({
  apiKey: process.env.OPENAI_API_KEY,
})
const openai = new OpenAIApi(configuration)

/**
 * Génère des options de token avec l'IA
 */
export async function generateAITokenOptions(description: string, imageAnalysis: any = null): Promise<any[]> {
  try {
    console.log("Génération d'options de token avec l'IA")
    console.log("Description:", description)

    // Analyser les tendances Twitter
    const twitterTrends = await analyzeTwitterTrends(description)
    console.log("Tendances Twitter:", twitterTrends)

    // Analyser le marché crypto
    const marketTrends = await analyzeCryptoMarket()
    console.log("Tendances du marché crypto:", marketTrends)

    // Préparer le prompt pour l'IA
    let prompt = `Génère trois options de token crypto basées sur cette description: "${description}". `

    if (imageAnalysis) {
      prompt += `L'image fournie contient les éléments suivants: ${imageAnalysis.description}. `
    }

    prompt += `Les tendances actuelles sur Twitter incluent: ${twitterTrends.join(", ")}. `
    prompt += `Les tendances actuelles du marché crypto incluent: ${marketTrends.join(", ")}. `

    prompt += `Pour chaque option, fournis: 
    1. Un nom de token accrocheur et mémorable
    2. Un symbole de token de 3-5 lettres (sans le suffixe GFAI qui sera ajouté automatiquement)
    3. Une description concise du token
    4. Un slogan marketing
    5. Une description détaillée pour générer un logo
    6. Une description détaillée pour générer une bannière
    7. Une palette de couleurs (primaire et secondaire)
    
    Assure-toi que chaque option est unique et correspond bien à la description fournie.`

    // Appeler l'API OpenAI
    const response = await openai.createCompletion({
      model: "text-davinci-003",
      prompt,
      max_tokens: 1500,
      temperature: 0.8,
    })

    const text = response.data.choices[0].text || ""

    // Analyser la réponse pour extraire les options
    const options = parseAIResponse(text)

    // Générer les images pour chaque option
    for (const option of options) {
      // Générer le logo
      const logoResponse = await openai.createImage({
        prompt: option.logoDescription,
        n: 1,
        size: "512x512",
      })
      option.logoUrl = logoResponse.data.data[0].url

      // Générer la bannière
      const bannerResponse = await openai.createImage({
        prompt: option.bannerDescription,
        n: 1,
        size: "1024x512",
      })
      option.bannerUrl = bannerResponse.data.data[0].url

      // Supprimer les descriptions utilisées pour la génération
      delete option.logoDescription
      delete option.bannerDescription
    }

    return options
  } catch (error: any) {
    console.error("Erreur lors de la génération des options de token:", error)
    throw new Error(error.message || "Une erreur s'est produite lors de la génération des options de token")
  }
}

/**
 * Analyse la réponse de l'IA pour extraire les options
 */
function parseAIResponse(text: string): any[] {
  try {
    const options = []
    const optionRegex = /Option (\d+):([\s\S]*?)(?=Option \d+:|$)/g

    let match
    while ((match = optionRegex.exec(text)) !== null) {
      const optionText = match[2].trim()

      // Extraire les différentes parties
      const nameMatch = optionText.match(/Nom:?\s*(.+)/)
      const symbolMatch = optionText.match(/Symbole:?\s*(.+)/)
      const descriptionMatch = optionText.match(/Description:?\s*(.+)/)
      const sloganMatch = optionText.match(/Slogan:?\s*(.+)/)
      const logoDescriptionMatch = optionText.match(/Logo:?\s*([\s\S]*?)(?=Bannière:|Palette:|$)/)
      const bannerDescriptionMatch = optionText.match(/Bannière:?\s*([\s\S]*?)(?=Palette:|$)/)
      const colorsMatch = optionText.match(/Palette:?\s*(.+)/)

      const option = {
        name: nameMatch ? nameMatch[1].trim() : `Option ${options.length + 1}`,
        symbol: symbolMatch ? symbolMatch[1].trim : `Option ${options.length + 1}`,
        symbol: symbolMatch ? symbolMatch[1].trim() : `OPT${options.length + 1}`,
        description: descriptionMatch ? descriptionMatch[1].trim() : "",
        slogan: sloganMatch ? sloganMatch[1].trim() : "",
        logoDescription: logoDescriptionMatch ? logoDescriptionMatch[1].trim() : "A modern crypto token logo",
        bannerDescription: bannerDescriptionMatch ? bannerDescriptionMatch[1].trim() : "A banner for a crypto token",
        colors: {
          primary: "#3B82F6",
          secondary: "#1E40AF",
        },
      }

      // Extraire les couleurs si disponibles
      if (colorsMatch) {
        const primaryMatch = colorsMatch[1].match(/primaire:?\s*(#[0-9A-Fa-f]{6})/)
        const secondaryMatch = colorsMatch[1].match(/secondaire:?\s*(#[0-9A-Fa-f]{6})/)

        if (primaryMatch) {
          option.colors.primary = primaryMatch[1]
        }

        if (secondaryMatch) {
          option.colors.secondary = secondaryMatch[1]
        }
      }

      options.push(option)
    }

    // Si aucune option n'a été extraite, créer des options par défaut
    if (options.length === 0) {
      for (let i = 0; i < 3; i++) {
        options.push({
          name: `Token Option ${i + 1}`,
          symbol: `OPT${i + 1}`,
          description: "Un token crypto innovant",
          slogan: "L'avenir de la finance décentralisée",
          logoDescription: "A modern crypto token logo",
          bannerDescription: "A banner for a crypto token",
          colors: {
            primary: "#3B82F6",
            secondary: "#1E40AF",
          },
        })
      }
    }

    return options
  } catch (error) {
    console.error("Erreur lors de l'analyse de la réponse de l'IA:", error)

    // Retourner des options par défaut en cas d'erreur
    return [
      {
        name: "Default Token 1",
        symbol: "DT1",
        description: "Un token crypto par défaut",
        slogan: "La solution par défaut",
        logoDescription: "A default crypto token logo",
        bannerDescription: "A default banner for a crypto token",
        colors: {
          primary: "#3B82F6",
          secondary: "#1E40AF",
        },
      },
      {
        name: "Default Token 2",
        symbol: "DT2",
        description: "Un autre token crypto par défaut",
        slogan: "Une autre solution par défaut",
        logoDescription: "Another default crypto token logo",
        bannerDescription: "Another default banner for a crypto token",
        colors: {
          primary: "#10B981",
          secondary: "#059669",
        },
      },
      {
        name: "Default Token 3",
        symbol: "DT3",
        description: "Un troisième token crypto par défaut",
        slogan: "Une troisième solution par défaut",
        logoDescription: "A third default crypto token logo",
        bannerDescription: "A third default banner for a crypto token",
        colors: {
          primary: "#8B5CF6",
          secondary: "#6D28D9",
        },
      },
    ]
  }
}

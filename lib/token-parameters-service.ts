// Définition des interfaces pour les paramètres de tokens
export interface TokenDefaults {
  decimals: number
  initialSupply: number
}

export interface TokenFeatures {
  allowMintable: boolean
  allowBurnable: boolean
  allowPausable: boolean
  allowTransferTaxable: boolean
  maxTransferTaxRate: number
}

export interface TokenSecurity {
  requireApproval: boolean
  enforceKYC: boolean
  maxDailyCreations: number
  maxCreationsPerUser: number
}

export interface TokenPermissions {
  userCanSetMintable: boolean
  userCanSetBurnable: boolean
  userCanSetPausable: boolean
  userCanSetTransferTaxable: boolean
  userCanSetMetadata: boolean
  userCanSetSupply: boolean
  userCanDefineSuffixes: boolean
  userCanSetAntiBot: boolean
  userCanSetTransactionLimits: boolean
  userCanSetHoldingLimits: boolean
  userCanSetBlacklist: boolean
}

export interface TokenFees {
  creationFeeInSol: number
  creationFeeInGF: number
  discountForGFPayment: number
  additionalFeaturesFee: number
}

export interface TokenLimits {
  minSupply: number
  maxSupply: number
  minDecimals: number
  maxDecimals: number
}

export interface TokenParameters {
  defaults: TokenDefaults
  features: TokenFeatures
  security: TokenSecurity
  permissions: TokenPermissions
  fees: TokenFees
  limits: TokenLimits
}

// Service pour gérer les paramètres de tokens
class TokenParametersService {
  // Paramètres par défaut
  private parameters: TokenParameters = {
    defaults: {
      decimals: 9,
      initialSupply: 1000000000,
    },
    features: {
      allowMintable: true,
      allowBurnable: true,
      allowPausable: true,
      allowTransferTaxable: false,
      maxTransferTaxRate: 5,
    },
    security: {
      requireApproval: true,
      enforceKYC: false,
      maxDailyCreations: 50,
      maxCreationsPerUser: 5,
    },
    permissions: {
      userCanSetMintable: false,
      userCanSetBurnable: true,
      userCanSetPausable: false,
      userCanSetTransferTaxable: true,
      userCanSetMetadata: true,
      userCanSetSupply: true,
      userCanDefineSuffixes: false,
      userCanSetAntiBot: true,
      userCanSetTransactionLimits: false,
      userCanSetHoldingLimits: false,
      userCanSetBlacklist: false,
    },
    fees: {
      creationFeeInSol: 0.1,
      creationFeeInGF: 1000,
      discountForGFPayment: 50,
      additionalFeaturesFee: 0.05,
    },
    limits: {
      minSupply: 1000,
      maxSupply: 1000000000000000,
      minDecimals: 0,
      maxDecimals: 18,
    },
  }

  // Récupérer tous les paramètres
  async getParameters(): Promise<TokenParameters> {
    // Dans une implémentation réelle, nous récupérerions ces données depuis la base de données
    // Pour l'instant, nous simulons un délai d'API
    await new Promise((resolve) => setTimeout(resolve, 500))
    return this.parameters
  }

  // Mettre à jour les paramètres
  async updateParameters(parameters: Partial<TokenParameters>): Promise<TokenParameters> {
    // Dans une implémentation réelle, nous mettrions à jour ces données dans la base de données
    // Pour l'instant, nous simulons un délai d'API
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Mise à jour récursive des paramètres
    this.parameters = this.deepMerge(this.parameters, parameters)

    return this.parameters
  }

  // Réinitialiser les paramètres aux valeurs par défaut
  async resetToDefaults(): Promise<TokenParameters> {
    // Dans une implémentation réelle, nous réinitialiserions ces données dans la base de données
    // Pour l'instant, nous simulons un délai d'API
    await new Promise((resolve) => setTimeout(resolve, 1000))

    this.parameters = {
      defaults: {
        decimals: 9,
        initialSupply: 1000000000,
      },
      features: {
        allowMintable: true,
        allowBurnable: true,
        allowPausable: true,
        allowTransferTaxable: false,
        maxTransferTaxRate: 5,
      },
      security: {
        requireApproval: true,
        enforceKYC: false,
        maxDailyCreations: 50,
        maxCreationsPerUser: 5,
      },
      permissions: {
        userCanSetMintable: false,
        userCanSetBurnable: true,
        userCanSetPausable: false,
        userCanSetTransferTaxable: true,
        userCanSetMetadata: true,
        userCanSetSupply: true,
        userCanDefineSuffixes: false,
        userCanSetAntiBot: true,
        userCanSetTransactionLimits: false,
        userCanSetHoldingLimits: false,
        userCanSetBlacklist: false,
      },
      fees: {
        creationFeeInSol: 0.1,
        creationFeeInGF: 1000,
        discountForGFPayment: 50,
        additionalFeaturesFee: 0.05,
      },
      limits: {
        minSupply: 1000,
        maxSupply: 1000000000000000,
        minDecimals: 0,
        maxDecimals: 18,
      },
    }

    return this.parameters
  }

  // Fonction utilitaire pour fusionner profondément des objets
  private deepMerge(target: any, source: any): any {
    const output = { ...target }

    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach((key) => {
        if (this.isObject(source[key])) {
          if (!(key in target)) {
            Object.assign(output, { [key]: source[key] })
          } else {
            output[key] = this.deepMerge(target[key], source[key])
          }
        } else {
          Object.assign(output, { [key]: source[key] })
        }
      })
    }

    return output
  }

  // Vérifier si une valeur est un objet
  private isObject(item: any): boolean {
    return item && typeof item === "object" && !Array.isArray(item)
  }
}

// Exporter une instance singleton du service
const tokenParametersService = new TokenParametersService()
export default tokenParametersService

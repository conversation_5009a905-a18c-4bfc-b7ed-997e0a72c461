import { create } from "zustand"

interface TokenSuffixState {
  availableSuffixes: string[]
  selectedSuffix: string | null
  setSelectedSuffix: (suffix: string) => void
  addSuffix: (suffix: string) => void
  removeSuffix: (suffix: string) => void
}

export const useTokenSuffixStore = create<TokenSuffixState>((set) => ({
  availableSuffixes: ["QT", "QUANTUM", "GF", "SOL", "VERSE"],
  selectedSuffix: null,
  setSelectedSuffix: (suffix) => set({ selectedSuffix: suffix }),
  addSuffix: (suffix) =>
    set((state) => ({
      availableSuffixes: [...state.availableSuffixes, suffix],
    })),
  removeSuffix: (suffix) =>
    set((state) => ({
      availableSuffixes: state.availableSuffixes.filter((s) => s !== suffix),
    })),
}))

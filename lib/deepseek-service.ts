import { getSecureEnvVariable } from "./secure-env-storage"

interface DeepSeekCompletionOptions {
  prompt: string
  model?: string
  maxTokens?: number
  temperature?: number
  topP?: number
  stop?: string[]
}

interface DeepSeekImageOptions {
  prompt: string
  n?: number
  size?: string
  responseFormat?: string
}

export async function getDeepSeekApiKey(): Promise<string> {
  const apiKey = await getSecureEnvVariable("DEEPSEEK_API_KEY")
  if (!apiKey) {
    throw new Error("Clé API DeepSeek non configurée")
  }
  return apiKey
}

export async function generateTextWithDeepSeek(options: DeepSeekCompletionOptions): Promise<string> {
  try {
    const apiKey = await getDeepSeekApiKey()
    const apiEndpoint =
      (await getSecureEnvVariable("DEEPSEEK_API_ENDPOINT")) || "https://api.deepseek.com/v1/completions"

    const response = await fetch(apiEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: options.model || "deepseek-chat",
        prompt: options.prompt,
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7,
        top_p: options.topP || 1,
        stop: options.stop || [],
      }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(`Erreur DeepSeek: ${error.error?.message || "Erreur inconnue"}`)
    }

    const data = await response.json()
    return data.choices[0].text
  } catch (error: any) {
    console.error("Erreur lors de la génération de texte avec DeepSeek:", error)
    throw new Error(`Erreur DeepSeek: ${error.message}`)
  }
}

export async function generateImageWithDeepSeek(options: DeepSeekImageOptions): Promise<string[]> {
  try {
    const apiKey = await getDeepSeekApiKey()
    const apiEndpoint =
      (await getSecureEnvVariable("DEEPSEEK_API_ENDPOINT")) || "https://api.deepseek.com/v1/images/generations"

    const response = await fetch(apiEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        prompt: options.prompt,
        n: options.n || 1,
        size: options.size || "1024x1024",
        response_format: options.responseFormat || "url",
      }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(`Erreur DeepSeek: ${error.error?.message || "Erreur inconnue"}`)
    }

    const data = await response.json()
    return data.data.map((item: any) => item.url)
  } catch (error: any) {
    console.error("Erreur lors de la génération d'image avec DeepSeek:", error)
    throw new Error(`Erreur DeepSeek: ${error.message}`)
  }
}

export async function testDeepSeekConnection(): Promise<{ success: boolean; message: string }> {
  try {
    const apiKey = await getDeepSeekApiKey()

    // Test simple avec une requête minimale
    const response = await fetch("https://api.deepseek.com/v1/models", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    })

    if (!response.ok) {
      const error = await response.json()
      return {
        success: false,
        message: `Erreur de connexion: ${error.error?.message || response.statusText}`,
      }
    }

    return {
      success: true,
      message: "Connexion à DeepSeek établie avec succès",
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Erreur de connexion: ${error.message}`,
    }
  }
}

import { Connection } from "@solana/web3.js"
import { envConfig } from "./env-config"
import presaleManagementService, { type PresaleStatus } from "./presale-management-service"

export interface PresaleMetrics {
  totalPresales: number
  activePresales: number
  completedPresales: number
  pendingPresales: number
  failedPresales: number
  cancelledPresales: number
  totalRaised: number
  totalParticipants: number
  averageRaisePerPresale: number
  successRate: number
}

export interface PresaleTimelineItem {
  id: string
  tokenName: string
  tokenSymbol: string
  date: number
  type: "start" | "end"
  status: PresaleStatus["status"]
}

class PresaleDashboardService {
  private connection: Connection

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
  }

  /**
   * Récupère les métriques des presales
   */
  async getPresaleMetrics(): Promise<PresaleMetrics> {
    try {
      // Récupérer toutes les presales
      const presales = presaleManagementService.getAllPresales()

      // Calculer les métriques
      const totalPresales = presales.length
      const activePresales = presales.filter((p) => p.status === "active").length
      const completedPresales = presales.filter((p) => p.status === "completed").length
      const pendingPresales = presales.filter((p) => p.status === "pending").length
      const failedPresales = presales.filter((p) => p.status === "failed").length
      const cancelledPresales = presales.filter((p) => p.status === "cancelled").length
      const totalRaised = presales.reduce((sum, p) => sum + p.raisedAmount, 0)
      const totalParticipants = presales.reduce((sum, p) => sum + p.participants, 0)
      const averageRaisePerPresale = totalPresales > 0 ? totalRaised / totalPresales : 0
      const finishedPresales = completedPresales + failedPresales + cancelledPresales
      const successRate = finishedPresales > 0 ? (completedPresales / finishedPresales) * 100 : 0

      return {
        totalPresales,
        activePresales,
        completedPresales,
        pendingPresales,
        failedPresales,
        cancelledPresales,
        totalRaised,
        totalParticipants,
        averageRaisePerPresale,
        successRate,
      }
    } catch (error) {
      console.error("Error getting presale metrics:", error)
      throw new Error("Failed to get presale metrics")
    }
  }

  /**
   * Récupère la timeline des presales
   */
  async getPresaleTimeline(): Promise<PresaleTimelineItem[]> {
    try {
      // Récupérer toutes les presales
      const presales = presaleManagementService.getAllPresales()

      // Créer la timeline
      const timeline: PresaleTimelineItem[] = []

      presales.forEach((presale) => {
        // Ajouter la date de début
        timeline.push({
          id: `${presale.id}_start`,
          tokenName: presale.config.tokenName,
          tokenSymbol: presale.config.tokenSymbol,
          date: presale.config.startTime,
          type: "start",
          status: presale.status,
        })

        // Ajouter la date de fin
        timeline.push({
          id: `${presale.id}_end`,
          tokenName: presale.config.tokenName,
          tokenSymbol: presale.config.tokenSymbol,
          date: presale.config.endTime,
          type: "end",
          status: presale.status,
        })
      })

      // Trier par date
      timeline.sort((a, b) => a.date - b.date)

      return timeline
    } catch (error) {
      console.error("Error getting presale timeline:", error)
      throw new Error("Failed to get presale timeline")
    }
  }

  /**
   * Récupère les presales à venir
   */
  async getUpcomingPresales(limit = 5): Promise<PresaleStatus[]> {
    try {
      // Récupérer toutes les presales
      const presales = presaleManagementService.getAllPresales()

      // Filtrer les presales à venir
      const now = Date.now()
      const upcoming = presales
        .filter((p) => p.status === "pending" && p.config.startTime > now)
        .sort((a, b) => a.config.startTime - b.config.startTime)
        .slice(0, limit)

      return upcoming
    } catch (error) {
      console.error("Error getting upcoming presales:", error)
      throw new Error("Failed to get upcoming presales")
    }
  }

  /**
   * Récupère les presales actives
   */
  async getActivePresales(limit = 5): Promise<PresaleStatus[]> {
    try {
      // Récupérer toutes les presales
      const presales = presaleManagementService.getAllPresales()

      // Filtrer les presales actives
      const active = presales
        .filter((p) => p.status === "active")
        .sort((a, b) => b.progress - a.progress)
        .slice(0, limit)

      return active
    } catch (error) {
      console.error("Error getting active presales:", error)
      throw new Error("Failed to get active presales")
    }
  }

  /**
   * Récupère les presales récemment terminées
   */
  async getRecentlyCompletedPresales(limit = 5): Promise<PresaleStatus[]> {
    try {
      // Récupérer toutes les presales
      const presales = presaleManagementService.getAllPresales()

      // Filtrer les presales récemment terminées
      const completed = presales
        .filter((p) => p.status === "completed")
        .sort((a, b) => b.updatedAt - a.updatedAt)
        .slice(0, limit)

      return completed
    } catch (error) {
      console.error("Error getting recently completed presales:", error)
      throw new Error("Failed to get recently completed presales")
    }
  }

  /**
   * Récupère les statistiques de participation par jour
   */
  async getParticipationStats(
    presaleId: string,
    days = 30,
  ): Promise<{ date: string; participants: number; amount: number }[]> {
    try {
      // Récupérer les participations
      const participations = presaleManagementService.getAllParticipations(presaleId)

      // Préparer les données
      const stats: { [date: string]: { participants: number; amount: number } } = {}
      const now = new Date()
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000)

      // Initialiser les dates
      for (let i = 0; i < days; i++) {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
        const dateStr = date.toISOString().split("T")[0]
        stats[dateStr] = { participants: 0, amount: 0 }
      }

      // Agréger les participations par jour
      participations.forEach((participation) => {
        const date = new Date(participation.timestamp)
        const dateStr = date.toISOString().split("T")[0]

        if (stats[dateStr]) {
          stats[dateStr].participants += 1
          stats[dateStr].amount += participation.amount
        }
      })

      // Convertir en tableau
      return Object.entries(stats).map(([date, data]) => ({
        date,
        participants: data.participants,
        amount: data.amount,
      }))
    } catch (error) {
      console.error("Error getting participation stats:", error)
      throw new Error("Failed to get participation stats")
    }
  }
}

// Exporter une instance singleton du service
const presaleDashboardService = new PresaleDashboardService()
export default presaleDashboardService

import { Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL, Keypair } from "@solana/web3.js"
import {
  createAssociatedTokenAccountInstruction,
  getAssociatedTokenAddress,
  createTransferCheckedInstruction,
} from "@solana/spl-token"
import { envConfig } from "./env-config"
import { getNetworkById } from "./network-config"
import bs58 from "bs58"

// Adresse du portefeuille de la plateforme
const PLATFORM_WALLET = new PublicKey("7b8hK6apNE2dRgBXHqptZ6aXXAcrWmNPgnY6SCPcJn3f")

// Prix minimum pour l'achat initial
const MIN_PURCHASE_AMOUNT = 0.01 * LAMPORTS_PER_SOL // 0.01 SOL en lamports

export interface InitialPurchaseParams {
  tokenAddress: string
  buyerAddress: string
  amountInSol: number
  networkId?: string
}

export interface InitialPurchaseResult {
  success: boolean
  transaction?: Transaction
  error?: string
  tokensToReceive?: number
}

/**
 * Service pour gérer l'achat initial de tokens
 */
class TokenInitialPurchaseService {
  private connection: Connection

  constructor() {
    try {
      const rpcUrl = envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com"
      this.connection = new Connection(rpcUrl, "confirmed")
      console.log("TokenInitialPurchaseService initialized with RPC URL:", rpcUrl)
    } catch (error) {
      console.error("Error initializing TokenInitialPurchaseService:", error)
      // Fallback to a default connection
      this.connection = new Connection("https://api.devnet.solana.com", "confirmed")
    }
  }

  // Méthode pour changer la connexion RPC
  setConnection(rpcUrl: string) {
    try {
      this.connection = new Connection(rpcUrl, "confirmed")
      console.log(`Connection switched to: ${rpcUrl}`)
      return this.connection
    } catch (error) {
      console.error("Error switching connection:", error)
      throw new Error(`Failed to switch connection: ${error}`)
    }
  }

  /**
   * Crée une transaction pour l'achat initial d'un token
   */
  async createInitialPurchaseTransaction(params: InitialPurchaseParams): Promise<InitialPurchaseResult> {
    try {
      console.log("Creating initial purchase transaction with params:", params)

      // Vérifier les paramètres obligatoires
      if (!params.tokenAddress || !params.buyerAddress || !params.amountInSol) {
        throw new Error("Missing required parameters: tokenAddress, buyerAddress, or amountInSol")
      }

      // Vérifier que le montant est suffisant
      if (params.amountInSol * LAMPORTS_PER_SOL < MIN_PURCHASE_AMOUNT) {
        throw new Error(`Le montant minimum pour l'achat initial est de ${MIN_PURCHASE_AMOUNT / LAMPORTS_PER_SOL} SOL`)
      }

      // Si un networkId est fourni, utiliser la bonne connexion RPC
      if (params.networkId) {
        const network = getNetworkById(params.networkId)
        if (network) {
          this.setConnection(network.rpcUrl)
        }
      }

      // Récupérer les clés publiques
      const buyerPublicKey = new PublicKey(params.buyerAddress)
      const platformPublicKey = PLATFORM_WALLET
      const tokenPublicKey = new PublicKey(params.tokenAddress)

      // Calculer le montant en lamports
      const amountInLamports = Math.floor(params.amountInSol * LAMPORTS_PER_SOL)

      // Calculer le nombre de tokens à recevoir (pour la démo, 1000 tokens par SOL)
      const tokensToReceive = params.amountInSol * 1000
      const tokenAmount = Math.floor(tokensToReceive * Math.pow(10, 9)) // 9 décimales pour Solana SPL tokens

      // Créer une transaction pour l'achat initial
      const transaction = new Transaction()

      // Ajouter l'instruction pour transférer les SOL à la plateforme
      transaction.add(
        SystemProgram.transfer({
          fromPubkey: buyerPublicKey,
          toPubkey: platformPublicKey,
          lamports: amountInLamports,
        }),
      )

      // Obtenir l'adresse du compte de token associé pour l'acheteur
      const buyerTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, buyerPublicKey)

      // Vérifier si le compte de token associé existe déjà
      let buyerTokenAccountExists = false
      try {
        const accountInfo = await this.connection.getAccountInfo(buyerTokenAccount)
        buyerTokenAccountExists = accountInfo !== null
      } catch (error) {
        console.log("Error checking buyer token account, assuming it doesn't exist:", error)
      }

      // Si le compte de token associé n'existe pas, ajouter l'instruction pour le créer
      if (!buyerTokenAccountExists) {
        console.log("Creating associated token account for buyer")
        transaction.add(
          createAssociatedTokenAccountInstruction(
            buyerPublicKey, // payer
            buyerTokenAccount, // associated token account address
            buyerPublicKey, // owner
            tokenPublicKey, // mint
          ),
        )
      }

      // Obtenir le blockhash récent
      const { blockhash } = await this.connection.getLatestBlockhash("confirmed")
      transaction.recentBlockhash = blockhash
      transaction.feePayer = buyerPublicKey

      console.log("Initial purchase transaction created and ready for signing")

      // Retourner la transaction
      return {
        success: true,
        transaction,
        tokensToReceive,
      }
    } catch (error: any) {
      console.error("Error creating initial purchase transaction:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la création de la transaction d'achat initial",
      }
    }
  }

  /**
   * Crée une transaction pour transférer les tokens après l'achat initial
   * Cette transaction doit être signée par la plateforme
   */
  async createTokenTransferTransaction(
    tokenAddress: string,
    buyerAddress: string,
    tokensToReceive: number,
  ): Promise<Transaction> {
    // Récupérer les clés publiques
    const buyerPublicKey = new PublicKey(buyerAddress)
    const platformPublicKey = PLATFORM_WALLET
    const tokenPublicKey = new PublicKey(tokenAddress)

    // Récupérer la clé privée de la plateforme depuis les variables d'environnement
    const platformPrivateKey = process.env.MMGF_PRIVATE_KEY
    if (!platformPrivateKey) {
      throw new Error("Platform private key not found in environment variables")
    }

    // Créer le keypair de la plateforme
    const platformKeypair = Keypair.fromSecretKey(bs58.decode(platformPrivateKey))

    // Obtenir l'adresse du compte de token associé pour l'acheteur
    const buyerTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, buyerPublicKey)

    // Obtenir l'adresse du compte de token associé pour la plateforme
    const platformTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, platformPublicKey)

    // Calculer le montant de tokens à transférer
    const tokenAmount = BigInt(Math.floor(tokensToReceive * Math.pow(10, 9))) // 9 décimales pour Solana SPL tokens

    // Créer une transaction pour transférer les tokens
    const transaction = new Transaction()

    // Ajouter l'instruction pour transférer les tokens de la plateforme à l'acheteur
    transaction.add(
      createTransferCheckedInstruction(
        platformTokenAccount, // source
        tokenPublicKey, // mint
        buyerTokenAccount, // destination
        platformPublicKey, // owner
        tokenAmount, // amount
        9, // decimals
      ),
    )

    // Obtenir le blockhash récent
    const { blockhash } = await this.connection.getLatestBlockhash("confirmed")
    transaction.recentBlockhash = blockhash
    transaction.feePayer = platformPublicKey

    // Signer la transaction avec la clé de la plateforme
    transaction.sign(platformKeypair)

    return transaction
  }

  /**
   * Envoie une transaction sur la blockchain
   */
  async sendTransaction(transaction: Transaction): Promise<{ success: boolean; signature: string; error?: string }> {
    try {
      // Envoyer la transaction
      const signature = await this.connection.sendRawTransaction(transaction.serialize(), {
        skipPreflight: false,
        preflightCommitment: "confirmed",
      })

      // Attendre la confirmation
      await this.connection.confirmTransaction(signature, "confirmed")

      console.log("Transaction confirmed with signature:", signature)

      return {
        success: true,
        signature,
      }
    } catch (error: any) {
      console.error("Error sending transaction:", error)
      return {
        success: false,
        signature: "",
        error: error.message || "Une erreur s'est produite lors de l'envoi de la transaction",
      }
    }
  }

  /**
   * Processus complet d'achat initial
   */
  async completeInitialPurchase(params: InitialPurchaseParams): Promise<{ success: boolean; error?: string }> {
    try {
      // 1. Créer et envoyer la transaction de paiement
      const purchaseResult = await this.createInitialPurchaseTransaction(params)
      if (!purchaseResult.success || !purchaseResult.transaction || !purchaseResult.tokensToReceive) {
        throw new Error(purchaseResult.error || "Échec de la création de la transaction d'achat")
      }

      // Cette transaction doit être signée par l'acheteur via son wallet
      // Dans un environnement réel, cette étape serait gérée par le frontend

      // 2. Créer et envoyer la transaction de transfert de tokens
      const transferTransaction = await this.createTokenTransferTransaction(
        params.tokenAddress,
        params.buyerAddress,
        purchaseResult.tokensToReceive,
      )

      // Cette transaction est déjà signée par la plateforme
      const transferResult = await this.sendTransaction(transferTransaction)

      if (!transferResult.success) {
        throw new Error(transferResult.error || "Échec du transfert des tokens")
      }

      // 3. Marquer l'achat initial comme terminé
      await this.markInitialPurchaseComplete(params.tokenAddress, transferResult.signature)

      return {
        success: true,
      }
    } catch (error: any) {
      console.error("Error completing initial purchase:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de l'achat initial",
      }
    }
  }

  /**
   * Vérifie si un achat initial a été effectué pour un token
   */
  async hasInitialPurchase(tokenAddress: string): Promise<boolean> {
    try {
      // Pour la démo, nous retournons toujours false
      // Dans une implémentation réelle, vous voudriez vérifier dans une base de données
      return false
    } catch (error) {
      console.error("Error checking initial purchase:", error)
      return false
    }
  }

  /**
   * Marque un token comme ayant eu un achat initial
   */
  async markInitialPurchaseComplete(
    tokenAddress: string,
    signature: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Pour la démo, nous retournons toujours true
      // Dans une implémentation réelle, vous voudriez enregistrer dans une base de données
      console.log(`Marking initial purchase complete for token ${tokenAddress} with signature ${signature}`)
      return {
        success: true,
      }
    } catch (error: any) {
      console.error("Error marking initial purchase complete:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors du marquage de l'achat initial comme terminé",
      }
    }
  }
}

// Exporter une instance singleton du service
const tokenInitialPurchaseService = new TokenInitialPurchaseService()
export default tokenInitialPurchaseService

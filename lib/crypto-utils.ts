"use server"

import { createCipheriv, createDecipheriv, randomBytes } from "crypto"

// Clé de chiffrement principale (devrait être stockée de manière sécurisée, idéalement dans un HSM)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || randomBytes(32).toString("hex")

// Algorithme de chiffrement
const ALGORITHM = "aes-256-gcm"

/**
 * Chiffre une chaîne de caractères
 */
export function encrypt(text: string): string {
  try {
    // Générer un vecteur d'initialisation aléatoire
    const iv = randomBytes(16)

    // Créer le chiffreur
    const cipher = createCipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY, "hex"), iv)

    // Chiffrer le texte
    let encrypted = cipher.update(text, "utf8", "hex")
    encrypted += cipher.final("hex")

    // Obtenir le tag d'authentification
    const authTag = cipher.getAuthTag()

    // Combiner IV, tag d'authentification et texte chiffré
    return iv.toString("hex") + ":" + authTag.toString("hex") + ":" + encrypted
  } catch (error) {
    console.error("Erreur de chiffrement:", error)
    throw new Error("Échec du chiffrement")
  }
}

/**
 * Déchiffre une chaîne de caractères chiffrée
 */
export function decrypt(encryptedText: string): string {
  try {
    // Séparer IV, tag d'authentification et texte chiffré
    const parts = encryptedText.split(":")
    if (parts.length !== 3) {
      throw new Error("Format de texte chiffré invalide")
    }

    const iv = Buffer.from(parts[0], "hex")
    const authTag = Buffer.from(parts[1], "hex")
    const encrypted = parts[2]

    // Créer le déchiffreur
    const decipher = createDecipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY, "hex"), iv)

    // Définir le tag d'authentification
    decipher.setAuthTag(authTag)

    // Déchiffrer le texte
    let decrypted = decipher.update(encrypted, "hex", "utf8")
    decrypted += decipher.final("utf8")

    return decrypted
  } catch (error) {
    console.error("Erreur de déchiffrement:", error)
    throw new Error("Échec du déchiffrement")
  }
}

/**
 * Génère une clé de chiffrement aléatoire
 */
export function generateEncryptionKey(): string {
  return randomBytes(32).toString("hex")
}

/**
 * Hache une chaîne de caractères (pour les mots de passe)
 */
export async function hashPassword(password: string): Promise<string> {
  // Dans un environnement de production, utilisez bcrypt ou Argon2
  const encoder = new TextEncoder()
  const data = encoder.encode(password + process.env.PASSWORD_SALT)

  const hashBuffer = await crypto.subtle.digest("SHA-256", data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("")
}

/**
 * Vérifie un mot de passe
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  const calculatedHash = await hashPassword(password)
  return calculatedHash === hash
}

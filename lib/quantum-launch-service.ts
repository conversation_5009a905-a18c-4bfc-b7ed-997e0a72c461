import { v4 as uuidv4 } from "uuid"
import { quantumDbService } from "./quantum-db-service"

export interface TokenLaunchConfig {
  name: string
  symbol: string
  suffix: string
  decimals: number
  totalSupply: number
  description?: string
  website?: string
  twitter?: string
  telegram?: string

  initialPrice: number
  softCap: number
  hardCap: number
  minBuy: number
  maxBuy: number

  liquidityPercentage: number
  teamPercentage: number
  marketingPercentage: number
  reservePercentage: number

  liquidityLockPeriod: number
  teamLockPeriod: number

  phases: {
    presale: boolean
    fairLaunch: boolean
    initialDexOffering: boolean
  }

  antiBot: boolean
  antiDump: boolean
  maxWalletPercentage: number
  maxTxPercentage: number

  buyTax: number
  sellTax: number
  transferTax: number

  teamWallet: string
  marketingWallet: string

  targetDex: string
  listingMultiplier: number

  featured?: boolean
  verified?: boolean
  discountPercentage?: number
  ownerWallet?: string
}

export interface LaunchPhase {
  id: string
  name: string
  description: string
  startDate: Date
  endDate: Date
  targetAmount: number
  minContribution: number
  maxContribution: number
  price: number
  status: "pending" | "active" | "completed" | "cancelled"
  amountRaised?: number
  participants?: number
  percentageComplete?: number
}

class QuantumLaunchService {
  async createTokenLaunch(
    tokenAddress: string,
    ownerAddress: string,
    config: TokenLaunchConfig,
    phases: LaunchPhase[],
  ): Promise<string> {
    try {
      // Créer la configuration de lancement dans la base de données
      const configId = await quantumDbService.createLaunchConfig(config)

      // Générer un ID unique pour le lancement
      const launchId = uuidv4()

      // Créer le lancement
      await quantumDbService.createLaunch(launchId, tokenAddress, ownerAddress, configId)

      // Créer les phases de lancement
      for (const phase of phases) {
        await quantumDbService.createLaunchPhase(phase, launchId)
      }

      // Définir la première phase comme phase actuelle
      if (phases.length > 0) {
        await quantumDbService.updateCurrentPhase(launchId, phases[0].id)
      }

      return launchId
    } catch (error) {
      console.error("Error creating token launch:", error)
      throw error
    }
  }

  async getAllLaunches(status?: string, limit = 10, offset = 0): Promise<any[]> {
    try {
      return await quantumDbService.getAllLaunches(status, limit, offset)
    } catch (error) {
      console.error("Error getting all launches:", error)
      throw error
    }
  }

  async getLaunchById(id: string): Promise<any> {
    try {
      return await quantumDbService.getLaunchById(id)
    } catch (error) {
      console.error("Error getting launch by ID:", error)
      throw error
    }
  }

  async getUserLaunches(ownerAddress: string): Promise<any[]> {
    try {
      return await quantumDbService.getUserLaunches(ownerAddress)
    } catch (error) {
      console.error("Error getting user launches:", error)
      throw error
    }
  }

  async updateLaunchStatus(id: string, status: string): Promise<void> {
    try {
      await quantumDbService.updateLaunchStatus(id, status)
    } catch (error) {
      console.error("Error updating launch status:", error)
      throw error
    }
  }

  async recordContribution(
    launchId: string,
    phaseId: string,
    contributorAddress: string,
    amount: number,
    transactionId: string,
  ): Promise<void> {
    try {
      const id = uuidv4()
      await quantumDbService.recordContribution(id, launchId, phaseId, contributorAddress, amount, transactionId)
    } catch (error) {
      console.error("Error recording contribution:", error)
      throw error
    }
  }

  async getUserContributions(contributorAddress: string): Promise<any[]> {
    try {
      return await quantumDbService.getUserContributions(contributorAddress)
    } catch (error) {
      console.error("Error getting user contributions:", error)
      throw error
    }
  }

  // Générer des phases de lancement par défaut
  generateDefaultPhases(config: TokenLaunchConfig): LaunchPhase[] {
    const phases: LaunchPhase[] = []
    const now = new Date()

    if (config.phases.presale) {
      // Phase de presale
      const presaleStartDate = new Date(now)
      presaleStartDate.setDate(presaleStartDate.getDate() + 1) // Commence dans 1 jour

      const presaleEndDate = new Date(presaleStartDate)
      presaleEndDate.setDate(presaleEndDate.getDate() + 7) // Dure 7 jours

      phases.push({
        id: uuidv4(),
        name: "Presale",
        description: "Initial fundraising phase at a discounted price",
        startDate: presaleStartDate,
        endDate: presaleEndDate,
        targetAmount: config.softCap,
        minContribution: config.minBuy,
        maxContribution: config.maxBuy,
        price: config.initialPrice,
        status: "pending",
      })
    }

    if (config.phases.fairLaunch) {
      // Phase de fair launch
      const fairLaunchStartDate = new Date(now)
      if (config.phases.presale) {
        // Si presale est activé, le fair launch commence après
        const lastPhase = phases[phases.length - 1]
        fairLaunchStartDate.setDate(lastPhase.endDate.getDate() + 1)
      } else {
        // Sinon, commence dans 1 jour
        fairLaunchStartDate.setDate(fairLaunchStartDate.getDate() + 1)
      }

      const fairLaunchEndDate = new Date(fairLaunchStartDate)
      fairLaunchEndDate.setDate(fairLaunchEndDate.getDate() + 3) // Dure 3 jours

      phases.push({
        id: uuidv4(),
        name: "Fair Launch",
        description: "Equal opportunity launch phase for all participants",
        startDate: fairLaunchStartDate,
        endDate: fairLaunchEndDate,
        targetAmount: config.hardCap / 2,
        minContribution: config.minBuy / 2,
        maxContribution: config.maxBuy * 1.5,
        price: config.initialPrice * 1.2,
        status: "pending",
      })
    }

    if (config.phases.initialDexOffering) {
      // Phase d'IDO
      const idoStartDate = new Date(now)
      if (phases.length > 0) {
        // L'IDO commence après la dernière phase
        const lastPhase = phases[phases.length - 1]
        idoStartDate.setDate(lastPhase.endDate.getDate() + 1)
      } else {
        // Sinon, commence dans 1 jour
        idoStartDate.setDate(idoStartDate.getDate() + 1)
      }

      const idoEndDate = new Date(idoStartDate)
      idoEndDate.setDate(idoEndDate.getDate() + 2) // Dure 2 jours

      phases.push({
        id: uuidv4(),
        name: "Initial DEX Offering",
        description: "Final phase before DEX listing",
        startDate: idoStartDate,
        endDate: idoEndDate,
        targetAmount: config.hardCap,
        minContribution: config.minBuy,
        maxContribution: config.maxBuy * 2,
        price: config.initialPrice * config.listingMultiplier * 0.9, // 90% du prix de listing
        status: "pending",
      })
    }

    return phases
  }
}

export const quantumLaunchService = new QuantumLaunchService()

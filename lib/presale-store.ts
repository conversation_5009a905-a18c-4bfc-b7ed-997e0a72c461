import { create } from "zustand"
import { persist } from "zustand/middleware"

export interface Presale {
  id: string
  tokenMint: string
  tokenName: string
  tokenSymbol: string
  tokenDecimals: number
  price: number // en SOL
  hardCap: number // en tokens
  softCap: number // en tokens
  minPurchase: number // en SOL
  maxPurchase: number // en SOL
  startTime: number
  endTime: number
  raised: number // en SOL
  sold: number // en tokens
  status: "upcoming" | "active" | "ended" | "successful" | "failed"
  vestingPeriod: number // en jours
  vestingReleases: number // nombre de libérations
  createdBy: string
  createdAt: number
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  whitepaper?: string
}

export interface PresaleParticipation {
  id: string
  presaleId: string
  walletAddress: string
  amount: number // en SOL
  tokens: number // en tokens
  timestamp: number
  txId?: string
}

interface PresaleStore {
  presales: Presale[]
  participations: PresaleParticipation[]
  addPresale: (presale: Presale) => void
  updatePresale: (id: string, updates: Partial<Presale>) => void
  removePresale: (id: string) => void
  getPresale: (id: string) => Presale | undefined
  getAllPresales: () => Presale[]
  getActivePresales: () => Presale[]
  getUpcomingPresales: () => Presale[]
  getEndedPresales: () => Presale[]
  addParticipation: (participation: PresaleParticipation) => void
  getPresaleParticipations: (presaleId: string) => PresaleParticipation[]
  getUserParticipations: (walletAddress: string) => PresaleParticipation[]
}

export const usePresaleStore = create<PresaleStore>()(
  persist(
    (set, get) => ({
      presales: [
        {
          id: "presale_gf_token",
          tokenMint: "GFbeta111111111111111111111111111111111111",
          tokenName: "GF-beta",
          tokenSymbol: "GF-b1",
          tokenDecimals: 9,
          price: 0.005, // 0.005 SOL
          hardCap: 150000000, // 15% de l'offre totale
          softCap: 50000000, // 5% de l'offre totale
          minPurchase: 0.1, // 0.1 SOL
          maxPurchase: 50, // 50 SOL
          startTime: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30 jours avant
          endTime: Date.now() + 60 * 24 * 60 * 60 * 1000, // 60 jours après
          raised: 500, // 500 SOL
          sold: 100000000, // 10% de l'offre totale
          status: "active",
          vestingPeriod: 90, // 90 jours
          vestingReleases: 3, // 3 libérations
          createdBy: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
          createdAt: Date.now() - 45 * 24 * 60 * 60 * 1000,
          description:
            "The official presale for the GF-beta token, the native token of the Global Finance Beta platform.",
          website: "https://globalfinance.com",
          twitter: "https://twitter.com/globalfinance",
          telegram: "https://t.me/globalfinance",
          whitepaper: "https://globalfinance.com/whitepaper.pdf",
        },
      ],
      participations: [],
      addPresale: (presale) => set((state) => ({ presales: [...state.presales, presale] })),
      updatePresale: (id, updates) =>
        set((state) => ({
          presales: state.presales.map((presale) => (presale.id === id ? { ...presale, ...updates } : presale)),
        })),
      removePresale: (id) =>
        set((state) => ({
          presales: state.presales.filter((presale) => presale.id !== id),
        })),
      getPresale: (id) => {
        return get().presales.find((presale) => presale.id === id)
      },
      getAllPresales: () => {
        return get().presales
      },
      getActivePresales: () => {
        const now = Date.now()
        return get().presales.filter((presale) => presale.startTime <= now && presale.endTime > now)
      },
      getUpcomingPresales: () => {
        const now = Date.now()
        return get().presales.filter((presale) => presale.startTime > now)
      },
      getEndedPresales: () => {
        const now = Date.now()
        return get().presales.filter((presale) => presale.endTime <= now)
      },
      addParticipation: (participation) =>
        set((state) => ({ participations: [...state.participations, participation] })),
      getPresaleParticipations: (presaleId) => {
        return get().participations.filter((participation) => participation.presaleId === presaleId)
      },
      getUserParticipations: (walletAddress) => {
        return get().participations.filter((participation) => participation.walletAddress === walletAddress)
      },
    }),
    {
      name: "presale-store",
    },
  ),
)

export const getActivePresales = () => {
  const now = Date.now()
  return usePresaleStore.getState().presales.filter((presale) => presale.startTime <= now && presale.endTime > now)
}

import { Connection, Keypair, PublicKey, clusterApiUrl } from "@solana/web3.js"
import { createMint, getOrCreateAssociatedTokenAccount, mintTo } from "@solana/spl-token"
import { getAdminKeypair } from "./key-utils"

export interface TokenCreationResult {
  success: boolean
  mintAddress?: string
  tokenAccount?: string
  error?: string
  txId?: string
}

/**
 * Service pour créer des tokens Solana de manière fiable
 */
export class TokenCreationService {
  private connection: Connection

  constructor(useDevnet = true) {
    const endpoint = useDevnet ? clusterApiUrl("devnet") : clusterApiUrl("mainnet-beta")
    this.connection = new Connection(endpoint, "confirmed")
  }

  /**
   * Crée un nouveau token Solana
   */
  async createToken(
    decimals: number,
    initialSupply: number,
    onProgress?: (step: string, progress: number) => void,
  ): Promise<TokenCreationResult> {
    try {
      // Utiliser un keypair prédéfini pour éviter les problèmes de clé privée
      const payer = this.getPayerKeypair()

      if (!payer) {
        return {
          success: false,
          error: "Impossible de générer un keypair valide",
        }
      }

      // Étape 1: Créer le mint
      onProgress?.("Création du token...", 20)
      console.log("Création du mint avec le keypair:", payer.publicKey.toString())

      const mint = await createMint(
        this.connection,
        payer,
        payer.publicKey, // mint authority
        payer.publicKey, // freeze authority
        decimals,
      )

      // Étape 2: Créer un compte associé pour le propriétaire
      onProgress?.("Création du compte associé...", 40)
      const tokenAccount = await getOrCreateAssociatedTokenAccount(this.connection, payer, mint, payer.publicKey)

      // Étape 3: Minter les tokens initiaux
      onProgress?.("Mint des tokens initiaux...", 60)
      const mintAmount = initialSupply * Math.pow(10, decimals)
      const txId = await mintTo(this.connection, payer, mint, tokenAccount.address, payer, mintAmount)

      onProgress?.("Finalisation...", 80)

      return {
        success: true,
        mintAddress: mint.toString(),
        tokenAccount: tokenAccount.address.toString(),
        txId,
      }
    } catch (error: any) {
      console.error("Erreur lors de la création du token:", error)
      return {
        success: false,
        error: error.message || "Une erreur inconnue s'est produite",
      }
    }
  }

  /**
   * Obtient un keypair valide pour la création de token
   * Cette méthode utilise une clé prédéfinie pour éviter les problèmes
   */
  private getPayerKeypair(): Keypair | null {
    try {
      // Essayer d'abord d'utiliser la clé admin
      const adminKeypair = getAdminKeypair()
      return adminKeypair
    } catch (error) {
      console.error("Erreur lors de la récupération du keypair admin:", error)

      // Fallback: générer un nouveau keypair
      try {
        return Keypair.generate()
      } catch (error) {
        console.error("Erreur lors de la génération d'un nouveau keypair:", error)
        return null
      }
    }
  }

  /**
   * Vérifie si un token existe
   */
  async verifyTokenExists(mintAddress: string): Promise<boolean> {
    try {
      const mint = new PublicKey(mintAddress)
      const mintInfo = await this.connection.getAccountInfo(mint)
      return mintInfo !== null
    } catch (error) {
      console.error("Erreur lors de la vérification du token:", error)
      return false
    }
  }
}

import { Keypair } from "@solana/web3.js"

// Adresse de référence qui se termine par MMGF
const REFERENCE_MMGF_ADDRESS = "798eexbD4GXKbh18YbLA2YcDB4ztNPoaZo3cPADHMMGF"
const REFERENCE_MMGF_PRIVATE_KEY =
  "a3f61dc974b119825a325fca17e82f7fd08b398c48338b3246149c4bb18935335be54a342e4db2969327c4706879c9f0cfa44f93cccad1326eea0d0992e31c00"

interface MMGFKeypair {
  publicKey: string
  privateKey: string
}

/**
 * Service pour gérer les adresses se terminant par MMGF
 */
class MMGFAddressService {
  private referenceKeypair: Keypair
  private preMinedKeypairs: Keypair[] = []

  constructor() {
    try {
      // Créer un keypair à partir de la clé privée de référence
      const privateKeyBytes = Buffer.from(REFERENCE_MMGF_PRIVATE_KEY, "hex")
      this.referenceKeypair = Keypair.fromSecretKey(privateKeyBytes)

      // Vérifier que l'adresse se termine bien par MMGF
      const publicKeyString = this.referenceKeypair.publicKey.toString()
      if (!publicKeyString.endsWith("MMGF")) {
        console.warn("La clé publique de référence ne se termine pas par MMGF:", publicKeyString)
      }

      // Initialiser quelques keypairs pré-minés avec le suffixe MMGF
      this.initializePreMinedKeypairs()
    } catch (error) {
      console.error("Erreur lors de l'initialisation du service MMGF:", error)
      // Créer un keypair par défaut en cas d'erreur
      this.referenceKeypair = Keypair.generate()
    }
  }

  /**
   * Initialise des keypairs pré-minés avec le suffixe MMGF
   * En production, ces keypairs seraient générés par un processus de minage hors ligne
   */
  private initializePreMinedKeypairs() {
    // Utiliser la clé privée de l'environnement si disponible
    const preMinedPrivateKey = process.env.PRE_MINED_MMGF_PRIVATE_KEY
    if (preMinedPrivateKey) {
      try {
        const privateKeyBytes = Buffer.from(preMinedPrivateKey, "hex")
        const keypair = Keypair.fromSecretKey(privateKeyBytes)
        if (keypair.publicKey.toString().endsWith("MMGF")) {
          this.preMinedKeypairs.push(keypair)
        }
      } catch (error) {
        console.error("Erreur lors de l'initialisation du keypair pré-miné:", error)
      }
    }

    // Ajouter le keypair de référence comme fallback
    this.preMinedKeypairs.push(this.referenceKeypair)
  }

  /**
   * Génère un keypair dont l'adresse se termine par MMGF
   */
  generateMMGFKeypair(): MMGFKeypair {
    // Utiliser un keypair pré-miné si disponible
    if (this.preMinedKeypairs.length > 0) {
      const keypair = this.preMinedKeypairs[0]
      return {
        publicKey: keypair.publicKey.toString(),
        privateKey: Buffer.from(keypair.secretKey).toString("hex"),
      }
    }

    // Fallback: utiliser le keypair de référence
    return {
      publicKey: this.referenceKeypair.publicKey.toString(),
      privateKey: Buffer.from(this.referenceKeypair.secretKey).toString("hex"),
    }
  }

  /**
   * Crée un keypair à partir d'une clé privée
   */
  createKeypairFromPrivateKey(privateKeyHex: string): Keypair {
    try {
      const privateKeyBytes = Buffer.from(privateKeyHex, "hex")
      return Keypair.fromSecretKey(privateKeyBytes)
    } catch (error) {
      console.error("Erreur lors de la création du keypair:", error)
      // En cas d'erreur, retourner le keypair de référence
      return this.referenceKeypair
    }
  }

  /**
   * Vérifie si une adresse se termine par MMGF
   */
  isMMGFAddress(address: string): boolean {
    return address.endsWith("MMGF")
  }

  /**
   * Récupère le keypair de référence
   */
  getReferenceKeypair(): Keypair {
    return this.referenceKeypair
  }
}

// Exporter une instance singleton du service
const mmgfAddressService = new MMGFAddressService()
export default mmgfAddressService

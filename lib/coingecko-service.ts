"use client"

export interface CoinData {
  id: string
  address: string
  symbol: string
  name: string
  image: string
  current_price: number
  price_change_percentage_24h: number
  market_cap: number
  total_volume: number
  circulating_supply: number
  blockchain: string
  created_at?: string
  is_new?: boolean
  is_recent?: boolean
  is_meme?: boolean
  holders?: number
  creator?: string
}

export interface SolanaTokenData {
  address: string
  name: string
  symbol: string
  price: number
  priceChange24h: number
  marketCap: number
  volume24h: number
  holders: number
  supply: number
  createdAt: number
}

class CoinGeckoService {
  async getCoinDetails(coinId: string): Promise<CoinData | null> {
    try {
      // Simuler une requête API
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Simuler des données de réponse
      const coinData: CoinData = {
        id: coinId,
        address: `0x${coinId.substring(0, 8)}`,
        name: `Coin ${coinId}`,
        symbol: coinId.substring(0, 3).toUpperCase(),
        image: "/placeholder.svg?height=64&width=64",
        current_price: Math.random() * 100,
        price_change_percentage_24h: Math.random() * 20 - 10,
        market_cap: Math.random() * 1000000000,
        total_volume: Math.random() * 10000000,
        circulating_supply: Math.random() * 100000000,
        blockchain: "Solana",
        created_at: new Date().toISOString(),
        is_new: Math.random() > 0.5,
        is_recent: Math.random() > 0.5,
        is_meme: Math.random() > 0.5,
        holders: Math.floor(Math.random() * 1000) + 100,
        creator: `Creator${Math.floor(Math.random() * 100)}`,
      }

      return coinData
    } catch (error) {
      console.error("Error fetching coin details:", error)
      return null
    }
  }

  async getTopCoins(limit: number): Promise<CoinData[]> {
    const mockData: CoinData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        id: `topcoin-${i}`,
        address: `0x${i}`,
        name: `Top Coin ${i}`,
        symbol: `TC${i}`,
        image: "/placeholder.svg?height=32&width=32",
        current_price: Math.random() * 100,
        price_change_percentage_24h: Math.random() * 20 - 10,
        market_cap: Math.random() * 1000000000,
        total_volume: Math.random() * 10000000,
        circulating_supply: Math.random() * 100000000,
        blockchain: "Ethereum",
        created_at: new Date().toISOString(),
        is_new: Math.random() > 0.5,
        is_recent: Math.random() > 0.5,
        is_meme: false,
        holders: Math.floor(Math.random() * 1000) + 100,
        creator: `Creator${i}`,
      })
    }
    return mockData
  }

  async getMemeCoins(limit: number): Promise<CoinData[]> {
    const mockData: CoinData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        id: `memecoin-${i}`,
        address: `0x${i}`,
        name: `Meme Coin ${i}`,
        symbol: `MC${i}`,
        image: "/placeholder.svg?height=32&width=32",
        current_price: Math.random() * 0.01,
        price_change_percentage_24h: Math.random() * 50 - 25,
        market_cap: Math.random() * 100000000,
        total_volume: Math.random() * 10000000,
        circulating_supply: Math.random() * 1000000000,
        blockchain: "Solana",
        created_at: new Date().toISOString(),
        is_new: Math.random() > 0.5,
        is_recent: Math.random() > 0.5,
        is_meme: true,
        holders: Math.floor(Math.random() * 1000) + 100,
        creator: `Creator${i}`,
      })
    }
    return mockData
  }

  async getSolanaCoins(limit: number): Promise<CoinData[]> {
    const mockData: CoinData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        id: `solanacoin-${i}`,
        address: `0x${i}`,
        name: `Solana Coin ${i}`,
        symbol: `SC${i}`,
        image: "/placeholder.svg?height=32&width=32",
        current_price: Math.random() * 10,
        price_change_percentage_24h: Math.random() * 30 - 15,
        market_cap: Math.random() * 10000000,
        total_volume: Math.random() * 1000000,
        circulating_supply: Math.random() * 100000000,
        blockchain: "Solana",
        created_at: new Date().toISOString(),
        is_new: Math.random() > 0.5,
        is_recent: Math.random() > 0.5,
        is_meme: false,
        holders: Math.floor(Math.random() * 1000) + 100,
        creator: `Creator${i}`,
      })
    }
    return mockData
  }

  async getBnbCoins(limit: number): Promise<CoinData[]> {
    const mockData: CoinData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        id: `bnbcoin-${i}`,
        address: `0x${i}`,
        name: `BNB Coin ${i}`,
        symbol: `BC${i}`,
        image: "/placeholder.svg?height=32&width=32",
        current_price: Math.random() * 10,
        price_change_percentage_24h: Math.random() * 30 - 15,
        market_cap: Math.random() * 10000000,
        total_volume: Math.random() * 1000000,
        circulating_supply: Math.random() * 100000000,
        blockchain: "BNB Chain",
        created_at: new Date().toISOString(),
        is_new: Math.random() > 0.5,
        is_recent: Math.random() > 0.5,
        is_meme: false,
        holders: Math.floor(Math.random() * 1000) + 100,
        creator: `Creator${i}`,
      })
    }
    return mockData
  }

  getMockTopCoins(limit: number): CoinData[] {
    const mockData: CoinData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        id: `topcoin-${i}`,
        address: `0x${i}`,
        name: `Top Coin ${i}`,
        symbol: `TC${i}`,
        image: "/placeholder.svg?height=32&width=32",
        current_price: Math.random() * 100,
        price_change_percentage_24h: Math.random() * 20 - 10,
        market_cap: Math.random() * 1000000000,
        total_volume: Math.random() * 10000000,
        circulating_supply: Math.random() * 100000000,
        blockchain: "Ethereum",
        created_at: new Date().toISOString(),
        is_new: Math.random() > 0.5,
        is_recent: Math.random() > 0.5,
        is_meme: false,
        holders: Math.floor(Math.random() * 1000) + 100,
        creator: `Creator${i}`,
      })
    }
    return mockData
  }

  getMockMemeCoins(limit: number): CoinData[] {
    const mockData: CoinData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        id: `memecoin-${i}`,
        address: `0x${i}`,
        name: `Meme Coin ${i}`,
        symbol: `MC${i}`,
        image: "/placeholder.svg?height=32&width=32",
        current_price: Math.random() * 0.01,
        price_change_percentage_24h: Math.random() * 50 - 25,
        market_cap: Math.random() * 100000000,
        total_volume: Math.random() * 10000000,
        circulating_supply: Math.random() * 1000000000,
        blockchain: "Solana",
        created_at: new Date().toISOString(),
        is_new: Math.random() > 0.5,
        is_recent: Math.random() > 0.5,
        is_meme: true,
        holders: Math.floor(Math.random() * 1000) + 100,
        creator: `Creator${i}`,
      })
    }
    return mockData
  }

  getMockSolanaCoins(limit: number): CoinData[] {
    const mockData: CoinData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        id: `solanacoin-${i}`,
        address: `0x${i}`,
        name: `Solana Coin ${i}`,
        symbol: `SC${i}`,
        image: "/placeholder.svg?height=32&width=32",
        current_price: Math.random() * 10,
        price_change_percentage_24h: Math.random() * 30 - 15,
        market_cap: Math.random() * 10000000,
        total_volume: Math.random() * 1000000,
        circulating_supply: Math.random() * 100000000,
        blockchain: "Solana",
        created_at: new Date().toISOString(),
        is_new: Math.random() > 0.5,
        is_recent: Math.random() > 0.5,
        is_meme: false,
        holders: Math.floor(Math.random() * 1000) + 100,
        creator: `Creator${i}`,
      })
    }
    return mockData
  }

  getMockNewSolanaTokens(limit: number): SolanaTokenData[] {
    const mockData: SolanaTokenData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        address: `0x${i}`,
        name: `New Solana Token ${i}`,
        symbol: `NST${i}`,
        price: Math.random() * 0.1,
        priceChange24h: Math.random() * 100 - 50,
        marketCap: Math.random() * 10000000,
        volume24h: Math.random() * 1000000,
        holders: Math.floor(Math.random() * 1000) + 10,
        supply: Math.random() * 1000000000,
        createdAt: Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000),
      })
    }
    return mockData
  }

  getMockNewBnbTokens(limit: number): SolanaTokenData[] {
    const mockData: SolanaTokenData[] = []
    for (let i = 0; i < limit; i++) {
      mockData.push({
        address: `0x${i}`,
        name: `New BNB Token ${i}`,
        symbol: `NBT${i}`,
        price: Math.random() * 0.1,
        priceChange24h: Math.random() * 100 - 50,
        marketCap: Math.random() * 10000000,
        volume24h: Math.random() * 1000000,
        holders: Math.floor(Math.random() * 1000) + 10,
        supply: Math.random() * 1000000000,
        createdAt: Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000),
      })
    }
    return mockData
  }
}

const coingeckoService = new CoinGeckoService()
export default coingeckoService

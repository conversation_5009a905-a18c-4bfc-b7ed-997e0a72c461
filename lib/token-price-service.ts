import { Connection, PublicKey } from "@solana/web3.js"
import { envConfig } from "./env-config"
import bondingCurveService from "./bonding-curve-service"

interface TokenDetails {
  address: string
  name: string
  symbol: string
  decimals: number
  totalSupply: number
  price: number
  priceChange24h: number
  marketCap: number
  volume24h: number
  holders: number
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  imageUrl?: string
  createdAt: string
}

interface PriceHistoryPoint {
  time: number // timestamp
  price: number
  volume?: number
}

class TokenPriceService {
  private connection: Connection
  private priceCache: Map<string, { price: number; timestamp: number }> = new Map()
  private priceHistoryCache: Map<string, PriceHistoryPoint[]> = new Map()
  private tokenDetailsCache: Map<string, TokenDetails> = new Map()

  constructor() {
    const rpcUrl = envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com"
    this.connection = new Connection(rpcUrl, "confirmed")
  }

  /**
   * Récupère le prix actuel d'un token
   */
  async getTokenPrice(tokenAddress: string): Promise<number> {
    try {
      // Vérifier si le prix est en cache et récent (moins de 5 minutes)
      const cachedPrice = this.priceCache.get(tokenAddress)
      const now = Date.now()
      if (cachedPrice && now - cachedPrice.timestamp < 5 * 60 * 1000) {
        return cachedPrice.price
      }

      // Calculer le prix à partir de la bonding curve
      const price = bondingCurveService.calculateCurrentPrice(tokenAddress)

      // Mettre en cache
      this.priceCache.set(tokenAddress, { price, timestamp: now })

      return price
    } catch (error) {
      console.error(`Error fetching token price for ${tokenAddress}:`, error)
      return 0
    }
  }

  /**
   * Récupère l'historique des prix d'un token
   */
  async getTokenPriceHistory(
    tokenAddress: string,
    period: "24h" | "7d" | "30d" | "90d" = "7d",
  ): Promise<PriceHistoryPoint[]> {
    try {
      // Vérifier si l'historique est en cache
      const cacheKey = `${tokenAddress}_${period}`
      const cachedHistory = this.priceHistoryCache.get(cacheKey)
      if (cachedHistory) {
        return cachedHistory
      }

      // Simuler l'historique des prix à partir de la bonding curve
      const history = this.simulatePriceHistory(tokenAddress, period)

      // Mettre en cache
      this.priceHistoryCache.set(cacheKey, history)

      return history
    } catch (error) {
      console.error(`Error fetching token price history for ${tokenAddress}:`, error)
      return []
    }
  }

  /**
   * Récupère les détails d'un token
   */
  async getTokenDetails(tokenAddress: string): Promise<TokenDetails | null> {
    try {
      // Vérifier si les détails sont en cache
      const cachedDetails = this.tokenDetailsCache.get(tokenAddress)
      if (cachedDetails) {
        return cachedDetails
      }

      // Vérifier si le token existe
      try {
        const mintPublicKey = new PublicKey(tokenAddress)
        const tokenInfo = await this.connection.getParsedAccountInfo(mintPublicKey)
        if (!tokenInfo.value) {
          console.error(`Token ${tokenAddress} not found on blockchain`)
          return null
        }
      } catch (err) {
        console.error(`Error validating token ${tokenAddress}:`, err)
        return null
      }

      // Récupérer le prix actuel
      const price = await this.getTokenPrice(tokenAddress)

      // Récupérer l'historique des prix pour calculer la variation sur 24h
      const priceHistory = await this.getTokenPriceHistory(tokenAddress, "24h")
      const priceChange24h = this.calculatePriceChange(priceHistory)

      // Simuler les autres données du token
      // Dans une implémentation réelle, ces données viendraient d'une API ou d'une base de données
      const tokenDetails: TokenDetails = {
        address: tokenAddress,
        name: `Token ${tokenAddress.slice(0, 4)}`,
        symbol: tokenAddress.slice(0, 3).toUpperCase(),
        decimals: 9,
        totalSupply: **********,
        price,
        priceChange24h,
        marketCap: price * **********,
        volume24h: price * 1000000 * (0.5 + Math.random()),
        holders: 100 + Math.floor(Math.random() * 900),
        description: "Token créé sur notre plateforme avec bonding curve avancée.",
        website: "https://example.com",
        twitter: "https://twitter.com/example",
        telegram: "https://t.me/example",
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 jours avant
      }

      // Mettre en cache
      this.tokenDetailsCache.set(tokenAddress, tokenDetails)

      return tokenDetails
    } catch (error) {
      console.error(`Error fetching token details for ${tokenAddress}:`, error)
      return null
    }
  }

  /**
   * Calcule la variation de prix sur une période
   */
  private calculatePriceChange(priceHistory: PriceHistoryPoint[]): number {
    if (priceHistory.length < 2) return 0

    const oldestPrice = priceHistory[0].price
    const latestPrice = priceHistory[priceHistory.length - 1].price

    if (oldestPrice === 0) return 0

    return ((latestPrice - oldestPrice) / oldestPrice) * 100
  }

  /**
   * Simule l'historique des prix à partir de la bonding curve
   */
  private simulatePriceHistory(tokenAddress: string, period: "24h" | "7d" | "30d" | "90d"): PriceHistoryPoint[] {
    const now = Date.now()
    let startTime: number
    let points: number

    switch (period) {
      case "24h":
        startTime = now - 24 * 60 * 60 * 1000
        points = 24 // Un point par heure
        break
      case "7d":
        startTime = now - 7 * 24 * 60 * 60 * 1000
        points = 7 * 24 // Un point par heure
        break
      case "30d":
        startTime = now - 30 * 24 * 60 * 60 * 1000
        points = 30 // Un point par jour
        break
      case "90d":
        startTime = now - 90 * 24 * 60 * 60 * 1000
        points = 90 // Un point par jour
        break
    }

    const history: PriceHistoryPoint[] = []
    const timeStep = (now - startTime) / points

    // Simuler une courbe de prix qui suit la bonding curve
    // Dans une implémentation réelle, ces données viendraient d'une API ou d'une base de données
    const currentPrice = bondingCurveService.calculateCurrentPrice(tokenAddress)

    for (let i = 0; i < points; i++) {
      const time = startTime + i * timeStep
      // Simuler un prix qui augmente progressivement
      // Plus on se rapproche du présent, plus le prix est proche du prix actuel
      const progress = i / points
      const randomFactor = 0.8 + Math.random() * 0.4 // Entre 0.8 et 1.2
      const price = currentPrice * (0.5 + 0.5 * progress) * randomFactor

      // Simuler un volume qui varie
      const volume = currentPrice * 1000000 * (0.5 + Math.random())

      history.push({ time, price, volume })
    }

    return history
  }

  /**
   * Récupère les données OHLC (Open, High, Low, Close) pour un graphique en chandeliers
   */
  async getTokenOHLCData(
    tokenAddress: string,
    period: "24h" | "7d" | "30d" | "90d" = "7d",
  ): Promise<{ time: number; open: number; high: number; low: number; close: number; volume: number }[]> {
    try {
      const priceHistory = await this.getTokenPriceHistory(tokenAddress, period)

      // Convertir l'historique des prix en données OHLC
      // Dans une implémentation réelle, ces données viendraient directement d'une API
      const ohlcData: { time: number; open: number; high: number; low: number; close: number; volume: number }[] = []

      // Regrouper les données par intervalle (1h, 1d, etc.)
      const interval = period === "24h" || period === "7d" ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000

      let currentInterval: {
        time: number
        prices: number[]
        volumes: number[]
      } | null = null

      for (const point of priceHistory) {
        const intervalStart = Math.floor(point.time / interval) * interval

        if (!currentInterval || currentInterval.time !== intervalStart) {
          if (currentInterval) {
            const prices = currentInterval.prices
            ohlcData.push({
              time: currentInterval.time,
              open: prices[0],
              high: Math.max(...prices),
              low: Math.min(...prices),
              close: prices[prices.length - 1],
              volume: currentInterval.volumes.reduce((sum, vol) => sum + vol, 0),
            })
          }

          currentInterval = {
            time: intervalStart,
            prices: [point.price],
            volumes: [point.volume || 0],
          }
        } else {
          currentInterval.prices.push(point.price)
          currentInterval.volumes.push(point.volume || 0)
        }
      }

      // Ajouter le dernier intervalle
      if (currentInterval) {
        const prices = currentInterval.prices
        ohlcData.push({
          time: currentInterval.time,
          open: prices[0],
          high: Math.max(...prices),
          low: prices[prices.length - 1],
          volume: currentInterval.volumes.reduce((sum, vol) => sum + vol, 0),
          close: prices[prices.length - 1],
        })
      }

      return ohlcData
    } catch (error) {
      console.error(`Error fetching token OHLC data for ${tokenAddress}:`, error)
      return []
    }
  }
}

// Singleton instance
const tokenPriceService = new TokenPriceService()
export default tokenPriceService

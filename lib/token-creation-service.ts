import { Connection, type Keypair, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL } from "@solana/web3.js"
import {
  createInitializeMintInstruction,
  createMintToInstruction,
  getMint,
  TOKEN_PROGRAM_ID,
  MINT_SIZE,
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
} from "@solana/spl-token"
import { envConfig } from "./env-config"
import { sanitizeToBase58 } from "./base58-sanitizer"
import { getNetworkById } from "./network-config"
import mmgfAddressService from "./mmgf-address-service"
import bondingCurveService from "./bonding-curve-service"

// Adresse du portefeuille de la plateforme
const PLATFORM_WALLET = new PublicKey("7b8hK6apNE2dRgBXHqptZ6aXXAcrWmNPgnY6SCPcJn3f")

// Prix de création d'un token
const TOKEN_CREATION_PRICE = 0.02 * LAMPORTS_PER_SOL // 0.02 SOL en lamports

export interface TokenCreationParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: number
  maxSupply?: number
  ownerAddress: string
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  imageUrl?: string
  networkId?: string
  isAIGenerated?: boolean
}

export interface TokenCreationResult {
  success: boolean
  tokenAddress?: string
  tokenSymbolWithSuffix?: string
  transactions?: Transaction[]
  error?: string
  networkUsed?: string
}

/**
 * Service pour la création de tokens sur la blockchain Solana
 */
class TokenCreationService {
  private connection: Connection

  constructor() {
    try {
      const rpcUrl = envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com"
      this.connection = new Connection(rpcUrl, "confirmed")
      console.log("TokenCreationService initialized with RPC URL:", rpcUrl)
    } catch (error) {
      console.error("Error initializing TokenCreationService:", error)
      // Fallback to a default connection
      this.connection = new Connection("https://api.devnet.solana.com", "confirmed")
    }
  }

  // Méthode pour changer la connexion RPC
  setConnection(rpcUrl: string) {
    try {
      this.connection = new Connection(rpcUrl, "confirmed")
      console.log(`Connection switched to: ${rpcUrl}`)
      return this.connection
    } catch (error) {
      console.error("Error switching connection:", error)
      throw new Error(`Failed to switch connection: ${error}`)
    }
  }

  /**
   * Crée un token sur la blockchain Solana avec une adresse se terminant par MMGF
   */
  async createToken(params: TokenCreationParams): Promise<TokenCreationResult> {
    try {
      console.log("Starting token creation with params:", params)

      // Vérifier les paramètres obligatoires
      if (!params.name || !params.symbol || !params.ownerAddress) {
        throw new Error("Missing required parameters: name, symbol, or ownerAddress")
      }

      // Sanitize inputs
      const sanitizedSymbol = sanitizeToBase58(params.symbol)
      if (sanitizedSymbol !== params.symbol) {
        console.warn(`Le symbole a été sanitisé: ${params.symbol} -> ${sanitizedSymbol}`)
      }

      // Utiliser le service MMGF pour générer un keypair avec une adresse se terminant par MMGF
      const mmgfKeypair = mmgfAddressService.generateMMGFKeypair()
      const mintKeypair = mmgfAddressService.createKeypairFromPrivateKey(mmgfKeypair.privateKey)
      const tokenAddress = mintKeypair.publicKey.toString()

      console.log("Generated MMGF keypair:", tokenAddress)

      // Vérifier que l'adresse se termine bien par MMGF
      if (!mmgfAddressService.isMMGFAddress(tokenAddress)) {
        console.warn("L'adresse générée ne se termine pas par MMGF, utilisation du keypair de référence")
        // Utiliser le keypair de référence en cas d'erreur
        const referenceKeypair = mmgfAddressService.getReferenceKeypair()
        if (mmgfAddressService.isMMGFAddress(referenceKeypair.publicKey.toString())) {
          // Utiliser le keypair de référence
          return this.createTokenWithKeypair(params, referenceKeypair)
        }
      }

      // Ajouter le suffixe MMGF au symbole du token
      const symbolWithSuffix = sanitizedSymbol
      console.log("Token symbol with suffix:", symbolWithSuffix)

      // Récupérer la clé publique du propriétaire
      const ownerPublicKey = new PublicKey(params.ownerAddress)
      console.log("Owner public key:", ownerPublicKey.toString())

      // Calculer le montant total de tokens à créer
      const totalSupply = params.initialSupply * Math.pow(10, params.decimals)
      console.log("Total supply:", totalSupply)

      // Calculer le loyer minimum pour le compte de mint
      const lamports = await this.connection.getMinimumBalanceForRentExemption(MINT_SIZE)
      console.log("Minimum balance for rent exemption:", lamports)

      // Créer une transaction pour initialiser le mint
      const createMintTransaction = new Transaction()

      // Ajouter l'instruction pour créer le compte de mint
      createMintTransaction.add(
        SystemProgram.createAccount({
          fromPubkey: ownerPublicKey,
          newAccountPubkey: mintKeypair.publicKey,
          space: MINT_SIZE,
          lamports,
          programId: TOKEN_PROGRAM_ID,
        }),
      )

      // Ajouter l'instruction pour initialiser le mint
      createMintTransaction.add(
        createInitializeMintInstruction(
          mintKeypair.publicKey,
          params.decimals,
          ownerPublicKey,
          ownerPublicKey, // Toujours mintable
          TOKEN_PROGRAM_ID,
        ),
      )

      // Obtenir l'adresse du compte de token associé pour le propriétaire
      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintKeypair.publicKey,
        ownerPublicKey,
        false,
        TOKEN_PROGRAM_ID,
      )
      console.log("Associated token address:", associatedTokenAddress.toString())

      // Ajouter l'instruction pour créer le compte de token associé
      createMintTransaction.add(
        createAssociatedTokenAccountInstruction(
          ownerPublicKey,
          associatedTokenAddress,
          ownerPublicKey,
          mintKeypair.publicKey,
          TOKEN_PROGRAM_ID,
        ),
      )

      // Créer une transaction pour minter les tokens initiaux
      const mintInitialTokensTransaction = new Transaction()
      mintInitialTokensTransaction.add(
        createMintToInstruction(
          mintKeypair.publicKey,
          associatedTokenAddress,
          ownerPublicKey,
          BigInt(totalSupply),
          [],
          TOKEN_PROGRAM_ID,
        ),
      )

      // Obtenir le blockhash récent
      const { blockhash } = await this.connection.getLatestBlockhash("confirmed")
      createMintTransaction.recentBlockhash = blockhash
      createMintTransaction.feePayer = ownerPublicKey
      mintInitialTokensTransaction.recentBlockhash = blockhash
      mintInitialTokensTransaction.feePayer = ownerPublicKey

      // Signer la transaction avec le keypair du mint
      createMintTransaction.partialSign(mintKeypair)

      console.log("Transactions created and ready for signing")

      // Initialiser la bonding curve
      const virtualTokenReserves = 1073000191 // Valeur similaire à pump.fun
      const virtualSolReserves = 30 // Valeur similaire à pump.fun
      const initialPrice = virtualSolReserves / virtualTokenReserves
      const dexListingThreshold = 69000 // 69,000 USD

      bondingCurveService.initializeCurve({
        tokenAddress,
        virtualTokenReserves,
        virtualSolReserves,
        currentSupply: 0,
        totalSupply: Number(totalSupply),
        dexListingThreshold,
        currentSolPool: 0,
      })

      // Retourner les informations du token et les transactions
      return {
        success: true,
        tokenAddress: mintKeypair.publicKey.toString(),
        tokenSymbolWithSuffix: symbolWithSuffix,
        transactions: [createMintTransaction, mintInitialTokensTransaction],
        networkUsed: "devnet",
      }
    } catch (error: any) {
      console.error("Error creating token:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la création du token",
      }
    }
  }

  /**
   * Crée un token avec un keypair spécifique
   */
  private async createTokenWithKeypair(
    params: TokenCreationParams,
    mintKeypair: Keypair,
  ): Promise<TokenCreationResult> {
    try {
      // Ajouter le suffixe MMGF au symbole du token
      const sanitizedSymbol = sanitizeToBase58(params.symbol)
      const symbolWithSuffix = `${sanitizedSymbol}MMGF`
      console.log("Token symbol with suffix:", symbolWithSuffix)

      // Récupérer la clé publique du propriétaire
      const ownerPublicKey = new PublicKey(params.ownerAddress)
      console.log("Owner public key:", ownerPublicKey.toString())

      // Calculer le montant total de tokens à créer
      const totalSupply = params.initialSupply * Math.pow(10, params.decimals)
      console.log("Total supply:", totalSupply)

      // Calculer le loyer minimum pour le compte de mint
      const lamports = await this.connection.getMinimumBalanceForRentExemption(MINT_SIZE)
      console.log("Minimum balance for rent exemption:", lamports)

      // Créer une transaction pour initialiser le mint
      const createMintTransaction = new Transaction()

      // Ajouter l'instruction pour créer le compte de mint
      createMintTransaction.add(
        SystemProgram.createAccount({
          fromPubkey: ownerPublicKey,
          newAccountPubkey: mintKeypair.publicKey,
          space: MINT_SIZE,
          lamports,
          programId: TOKEN_PROGRAM_ID,
        }),
      )

      // Ajouter l'instruction pour initialiser le mint
      createMintTransaction.add(
        createInitializeMintInstruction(
          mintKeypair.publicKey,
          params.decimals,
          ownerPublicKey,
          ownerPublicKey, // Toujours mintable
          TOKEN_PROGRAM_ID,
        ),
      )

      // Obtenir l'adresse du compte de token associé pour le propriétaire
      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintKeypair.publicKey,
        ownerPublicKey,
        false,
        TOKEN_PROGRAM_ID,
      )
      console.log("Associated token address:", associatedTokenAddress.toString())

      // Ajouter l'instruction pour créer le compte de token associé
      createMintTransaction.add(
        createAssociatedTokenAccountInstruction(
          ownerPublicKey,
          associatedTokenAddress,
          ownerPublicKey,
          mintKeypair.publicKey,
          TOKEN_PROGRAM_ID,
        ),
      )

      // Créer une transaction pour minter les tokens initiaux
      const mintInitialTokensTransaction = new Transaction()
      mintInitialTokensTransaction.add(
        createMintToInstruction(
          mintKeypair.publicKey,
          associatedTokenAddress,
          ownerPublicKey,
          BigInt(totalSupply),
          [],
          TOKEN_PROGRAM_ID,
        ),
      )

      // Obtenir le blockhash récent
      const { blockhash } = await this.connection.getLatestBlockhash("confirmed")
      createMintTransaction.recentBlockhash = blockhash
      createMintTransaction.feePayer = ownerPublicKey
      mintInitialTokensTransaction.recentBlockhash = blockhash
      mintInitialTokensTransaction.feePayer = ownerPublicKey

      // Signer la transaction avec le keypair du mint
      createMintTransaction.partialSign(mintKeypair)

      console.log("Transactions created and ready for signing")

      // Retourner les informations du token et les transactions
      return {
        success: true,
        tokenAddress: mintKeypair.publicKey.toString(),
        tokenSymbolWithSuffix: symbolWithSuffix,
        transactions: [createMintTransaction, mintInitialTokensTransaction],
        networkUsed: "devnet",
      }
    } catch (error: any) {
      console.error("Error creating token with keypair:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la création du token avec le keypair",
      }
    }
  }

  /**
   * Envoie des transactions sur la blockchain
   */
  async sendTransactions(
    transactions: Transaction[],
  ): Promise<{ success: boolean; signatures: string[]; error?: string }> {
    try {
      const signatures: string[] = []

      for (let i = 0; i < transactions.length; i++) {
        const transaction = transactions[i]

        // Envoyer la transaction
        const signature = await this.connection.sendRawTransaction(transaction.serialize(), {
          skipPreflight: false,
          preflightCommitment: "confirmed",
        })

        // Attendre la confirmation
        await this.connection.confirmTransaction(signature, "confirmed")

        signatures.push(signature)
        console.log(`Transaction ${i + 1} confirmed with signature: ${signature}`)
      }

      return {
        success: true,
        signatures,
      }
    } catch (error: any) {
      console.error("Error sending transactions:", error)
      return {
        success: false,
        signatures: [],
        error: error.message || "Une erreur s'est produite lors de l'envoi des transactions",
      }
    }
  }

  /**
   * Vérifie si un token existe sur la blockchain
   */
  async verifyToken(tokenAddress: string, networkId?: string): Promise<boolean> {
    try {
      // Si un networkId est fourni, utiliser la bonne connexion RPC
      if (networkId) {
        const network = getNetworkById(networkId)
        if (network) {
          this.setConnection(network.rpcUrl)
        }
      }

      const mintPublicKey = new PublicKey(tokenAddress)
      const tokenInfo = await getMint(this.connection, mintPublicKey)
      return tokenInfo.isInitialized
    } catch (error) {
      console.error("Error verifying token:", error)
      return false
    }
  }

  /**
   * Récupère les informations d'un token
   */
  async getTokenInfo(tokenAddress: string, networkId?: string): Promise<any> {
    try {
      // Si un networkId est fourni, utiliser la bonne connexion RPC
      if (networkId) {
        const network = getNetworkById(networkId)
        if (network) {
          this.setConnection(network.rpcUrl)
        }
      }

      const mintPublicKey = new PublicKey(tokenAddress)
      const tokenInfo = await getMint(this.connection, mintPublicKey)

      return {
        address: tokenAddress,
        decimals: tokenInfo.decimals,
        supply: tokenInfo.supply.toString(),
        mintAuthority: tokenInfo.mintAuthority?.toString() || null,
        freezeAuthority: tokenInfo.freezeAuthority?.toString() || null,
        isInitialized: tokenInfo.isInitialized,
      }
    } catch (error) {
      console.error("Error getting token info:", error)
      throw error
    }
  }
}

// Exporter une instance singleton du service
const tokenCreationService = new TokenCreationService()
export default tokenCreationService

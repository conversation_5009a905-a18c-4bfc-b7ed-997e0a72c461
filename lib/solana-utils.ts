import {
  Connection,
  type PublicKey,
  Transaction,
  type Keypair,
  sendAndConfirmTransaction,
  clusterApiUrl,
} from "@solana/web3.js"
import {
  createMint,
  getOrCreateAssociatedTokenAccount,
  mintTo,
  transfer,
  type Account,
  createSetAuthorityInstruction,
  AuthorityType,
} from "@solana/spl-token"

// Connect to the Solana network
export const getConnection = (endpoint?: string) => {
  // Utiliser l'endpoint fourni, ou l'endpoint configuré, ou devnet par défaut
  const rpcUrl = endpoint || process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com"

  // Vérifier si l'endpoint est un raccourci pour un cluster standard
  if (rpcUrl === "devnet") {
    return new Connection(clusterApiUrl("devnet"), "confirmed")
  } else if (rpcUrl === "mainnet-beta" || rpcUrl === "mainnet") {
    return new Connection(clusterApiUrl("mainnet-beta"), "confirmed")
  } else if (rpcUrl === "testnet") {
    return new Connection(clusterApiUrl("testnet"), "confirmed")
  }

  console.log(`Connecting to Solana RPC: ${rpcUrl}`)
  return new Connection(rpcUrl, "confirmed")
}

// Vérifier la connexion au réseau Solana
export const checkConnection = async (
  endpoint?: string,
): Promise<{ success: boolean; version?: string; error?: string }> => {
  try {
    const connection = getConnection(endpoint)
    const version = await connection.getVersion()
    console.log(`Connected to Solana ${version["solana-core"]}`)
    return {
      success: true,
      version: `Solana v${version["solana-core"]}`,
    }
  } catch (error: any) {
    console.error("Error checking Solana connection:", error)
    return {
      success: false,
      error: error.message || "Failed to connect to Solana network",
    }
  }
}

// Create a new SPL token
export const createSplToken = async (
  connection: Connection,
  payer: Keypair,
  mintAuthority: PublicKey,
  freezeAuthority: PublicKey | null,
  decimals: number,
): Promise<PublicKey> => {
  try {
    // Vérifier le solde du payeur
    const balance = await connection.getBalance(payer.publicKey)
    console.log(`Payer balance: ${balance / 1e9} SOL`)

    if (balance < 10000000) {
      // Minimum 0.01 SOL
      throw new Error(`Insufficient balance: ${balance / 1e9} SOL. Minimum required: 0.01 SOL`)
    }

    // Create a new token mint
    console.log("Creating token mint...")
    const tokenMint = await createMint(connection, payer, mintAuthority, freezeAuthority, decimals)

    console.log(`Token mint created: ${tokenMint.toBase58()}`)
    return tokenMint
  } catch (error: any) {
    console.error("Error creating SPL token:", error)
    throw new Error(`Failed to create SPL token: ${error.message}`)
  }
}

// Create a token account for a user
export const createTokenAccount = async (
  connection: Connection,
  payer: Keypair,
  mint: PublicKey,
  owner: PublicKey,
): Promise<Account> => {
  try {
    // Get or create an associated token account for the owner
    console.log(`Creating token account for ${owner.toBase58()}...`)
    const tokenAccount = await getOrCreateAssociatedTokenAccount(connection, payer, mint, owner)

    console.log(`Token account created: ${tokenAccount.address.toBase58()}`)
    return tokenAccount
  } catch (error: any) {
    console.error("Error creating token account:", error)
    throw new Error(`Failed to create token account: ${error.message}`)
  }
}

// Mint tokens to a user
export const mintTokens = async (
  connection: Connection,
  payer: Keypair,
  mint: PublicKey,
  destination: PublicKey,
  authority: Keypair,
  amount: number,
): Promise<string> => {
  try {
    // Mint tokens to the destination account
    console.log(`Minting ${amount} tokens to ${destination.toBase58()}...`)
    const signature = await mintTo(connection, payer, mint, destination, authority, amount)

    console.log(`Tokens minted: ${amount}`)
    return signature
  } catch (error: any) {
    console.error("Error minting tokens:", error)
    throw new Error(`Failed to mint tokens: ${error.message}`)
  }
}

// Transfer tokens between accounts
export const transferTokens = async (
  connection: Connection,
  payer: Keypair,
  source: PublicKey,
  destination: PublicKey,
  owner: Keypair,
  amount: number,
): Promise<string> => {
  try {
    // Transfer tokens from source to destination
    console.log(`Transferring ${amount} tokens from ${source.toBase58()} to ${destination.toBase58()}...`)
    const signature = await transfer(connection, payer, source, destination, owner, amount)

    console.log(`Tokens transferred: ${amount}`)
    return signature
  } catch (error: any) {
    console.error("Error transferring tokens:", error)
    throw new Error(`Failed to transfer tokens: ${error.message}`)
  }
}

// Revoke mint authority (make token supply fixed)
export const revokeMintAuthority = async (
  connection: Connection,
  payer: Keypair,
  mint: PublicKey,
  currentAuthority: Keypair,
): Promise<string> => {
  try {
    // Create a transaction to revoke the mint authority
    console.log(`Revoking mint authority for ${mint.toBase58()}...`)
    const transaction = new Transaction().add(
      createSetAuthorityInstruction(mint, currentAuthority.publicKey, AuthorityType.MintTokens, null),
    )

    // Sign and send the transaction
    const signature = await sendAndConfirmTransaction(connection, transaction, [payer, currentAuthority])

    console.log(`Mint authority revoked: ${signature}`)
    return signature
  } catch (error: any) {
    console.error("Error revoking mint authority:", error)
    throw new Error(`Failed to revoke mint authority: ${error.message}`)
  }
}

// Vérifier l'existence d'un token
export const verifyTokenExists = async (connection: Connection, tokenAddress: string): Promise<boolean> => {
  try {
    const publicKey = new PublicKey(tokenAddress)
    const accountInfo = await connection.getAccountInfo(publicKey)
    return accountInfo !== null
  } catch (error) {
    console.error("Error verifying token existence:", error)
    return false
  }
}

// Obtenir le solde d'un compte
export const getAccountBalance = async (connection: Connection, address: string): Promise<number> => {
  try {
    const publicKey = new PublicKey(address)
    const balance = await connection.getBalance(publicKey)
    return balance / 1e9 // Convertir en SOL
  } catch (error) {
    console.error("Error getting account balance:", error)
    throw error
  }
}

// Vérifier si une adresse est une adresse Solana valide
export const isValidSolanaAddress = (address: string): boolean => {
  try {
    // Pour la démo, accepter toutes les adresses
    // Dans une implémentation réelle, vous devriez vérifier si l'adresse est valide
    return true
  } catch (error) {
    return false
  }
}

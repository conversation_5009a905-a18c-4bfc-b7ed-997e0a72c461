import { Connection, type Keypair } from "@solana/web3.js"
import { envConfig } from "./env-config"

export interface TokenInfo {
  address: string
  name: string
  symbol: string
  decimals: number
  totalSupply: number
}

export interface PresaleConfig {
  tokenName: string
  tokenSymbol: string
  tokenAddress: string
  price: number
  hardCap: number
  softCap: number
  minPurchase: number
  maxPurchase: number
  startTime: number
  endTime: number
  vestingPeriod: number
  vestingRelease: "linear" | "cliff" | "staged"
  liquidityPercentage: number
  liquidityLockPeriod: number
  teamTokens: number
  marketingTokens: number
  whitelistEnabled: boolean
  publicSaleDelay: number
  description: string
  website?: string
  twitter?: string
  telegram?: string
  whitepaper?: string
}

export interface PresaleStatus {
  id: string
  config: PresaleConfig
  status: "pending" | "active" | "completed" | "cancelled" | "failed"
  raisedAmount: number
  participants: number
  progress: number
  createdAt: number
  updatedAt: number
}

export interface Participation {
  id: string
  presaleId: string
  address: string
  amount: number
  tokens: number
  timestamp: number
  claimed: boolean
  claimTimestamp?: number
  transactionId: string
}

export interface WhitelistEntry {
  address: string
  presaleId: string
  addedAt: number
  addedBy: string
}

class PresaleManagementService {
  private connection: Connection
  private presales: Map<string, PresaleStatus>
  private participations: Map<string, Participation[]>
  private whitelists: Map<string, WhitelistEntry[]>

  constructor() {
    this.connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
    this.presales = new Map<string, PresaleStatus>()
    this.participations = new Map<string, Participation[]>()
    this.whitelists = new Map<string, WhitelistEntry[]>()

    // Initialiser avec des données de test
    this.initializeTestData()
  }

  private initializeTestData() {
    const now = Date.now()

    // Presale 1
    const presale1: PresaleStatus = {
      id: "presale_1",
      config: {
        tokenName: "Solana Platform Token",
        tokenSymbol: "SPT",
        tokenAddress: "token_address_1",
        price: 0.005,
        hardCap: 1000000,
        softCap: 500000,
        minPurchase: 0.1,
        maxPurchase: 10,
        startTime: now - 7 * 24 * 60 * 60 * 1000,
        endTime: now + 14 * 24 * 60 * 60 * 1000,
        vestingPeriod: 90,
        vestingRelease: "linear",
        liquidityPercentage: 70,
        liquidityLockPeriod: 180,
        teamTokens: 10,
        marketingTokens: 5,
        whitelistEnabled: false,
        publicSaleDelay: 0,
        description: "Le token officiel de la plateforme Solana Platform.",
        website: "https://solanaplatform.com",
        twitter: "https://twitter.com/solanaplatform",
        telegram: "https://t.me/solanaplatform",
      },
      status: "active",
      raisedAmount: 350000,
      participants: 120,
      progress: 35,
      createdAt: now - 30 * 24 * 60 * 60 * 1000,
      updatedAt: now - 1 * 24 * 60 * 60 * 1000,
    }
    this.presales.set(presale1.id, presale1)

    // Presale 2
    const presale2: PresaleStatus = {
      id: "presale_2",
      config: {
        tokenName: "Meme Coin",
        tokenSymbol: "MEME",
        tokenAddress: "token_address_2",
        price: 0.001,
        hardCap: 500000,
        softCap: 100000,
        minPurchase: 0.1,
        maxPurchase: 5,
        startTime: now + 3 * 24 * 60 * 60 * 1000,
        endTime: now + 17 * 24 * 60 * 60 * 1000,
        vestingPeriod: 60,
        vestingRelease: "cliff",
        liquidityPercentage: 60,
        liquidityLockPeriod: 365,
        teamTokens: 15,
        marketingTokens: 10,
        whitelistEnabled: true,
        publicSaleDelay: 24,
        description: "Le prochain grand meme coin sur Solana.",
        website: "https://memecoin.io",
        twitter: "https://twitter.com/memecoin",
        telegram: "https://t.me/memecoin",
      },
      status: "pending",
      raisedAmount: 0,
      participants: 0,
      progress: 0,
      createdAt: now - 5 * 24 * 60 * 60 * 1000,
      updatedAt: now - 5 * 24 * 60 * 60 * 1000,
    }
    this.presales.set(presale2.id, presale2)

    // Presale 3
    const presale3: PresaleStatus = {
      id: "presale_3",
      config: {
        tokenName: "Utility Token",
        tokenSymbol: "UTIL",
        tokenAddress: "token_address_3",
        price: 0.01,
        hardCap: 200000,
        softCap: 100000,
        minPurchase: 0.5,
        maxPurchase: 20,
        startTime: now - 30 * 24 * 60 * 60 * 1000,
        endTime: now - 2 * 24 * 60 * 60 * 1000,
        vestingPeriod: 120,
        vestingRelease: "staged",
        liquidityPercentage: 80,
        liquidityLockPeriod: 730,
        teamTokens: 5,
        marketingTokens: 5,
        whitelistEnabled: false,
        publicSaleDelay: 0,
        description: "Un token utilitaire pour l'écosystème DeFi.",
        website: "https://utilitytoken.finance",
        twitter: "https://twitter.com/utilitytoken",
        telegram: "https://t.me/utilitytoken",
        whitepaper: "https://utilitytoken.finance/whitepaper.pdf",
      },
      status: "completed",
      raisedAmount: 180000,
      participants: 75,
      progress: 90,
      createdAt: now - 45 * 24 * 60 * 60 * 1000,
      updatedAt: now - 2 * 24 * 60 * 60 * 1000,
    }
    this.presales.set(presale3.id, presale3)

    // Participations pour la presale 1
    const participations1: Participation[] = [
      {
        id: "participation_1",
        presaleId: "presale_1",
        address: "wallet_address_1",
        amount: 5,
        tokens: 5000000,
        timestamp: now - 5 * 24 * 60 * 60 * 1000,
        claimed: false,
        transactionId: "tx_123456",
      },
      {
        id: "participation_2",
        presaleId: "presale_1",
        address: "wallet_address_2",
        amount: 2.5,
        tokens: 2500000,
        timestamp: now - 4 * 24 * 60 * 60 * 1000,
        claimed: false,
        transactionId: "tx_234567",
      },
      {
        id: "participation_3",
        presaleId: "presale_1",
        address: "wallet_address_3",
        amount: 10,
        tokens: 10000000,
        timestamp: now - 3 * 24 * 60 * 60 * 1000,
        claimed: false,
        transactionId: "tx_345678",
      },
    ]
    this.participations.set("presale_1", participations1)

    // Participations pour la presale 3
    const participations3: Participation[] = [
      {
        id: "participation_4",
        presaleId: "presale_3",
        address: "wallet_address_1",
        amount: 10,
        tokens: 1000000,
        timestamp: now - 25 * 24 * 60 * 60 * 1000,
        claimed: true,
        claimTimestamp: now - 1 * 24 * 60 * 60 * 1000,
        transactionId: "tx_456789",
      },
      {
        id: "participation_5",
        presaleId: "presale_3",
        address: "wallet_address_4",
        amount: 20,
        tokens: 2000000,
        timestamp: now - 20 * 24 * 60 * 60 * 1000,
        claimed: true,
        claimTimestamp: now - 1 * 24 * 60 * 60 * 1000,
        transactionId: "tx_567890",
      },
      {
        id: "participation_6",
        presaleId: "presale_3",
        address: "wallet_address_5",
        amount: 15,
        tokens: 1500000,
        timestamp: now - 15 * 24 * 60 * 60 * 1000,
        claimed: true,
        claimTimestamp: now - 1 * 24 * 60 * 60 * 1000,
        transactionId: "tx_678901",
      },
    ]
    this.participations.set("presale_3", participations3)

    // Whitelist pour la presale 2
    const whitelist2: WhitelistEntry[] = [
      {
        address: "wallet_address_1",
        presaleId: "presale_2",
        addedAt: now - 4 * 24 * 60 * 60 * 1000,
        addedBy: "admin",
      },
      {
        address: "wallet_address_2",
        presaleId: "presale_2",
        addedAt: now - 4 * 24 * 60 * 60 * 1000,
        addedBy: "admin",
      },
      {
        address: "wallet_address_6",
        presaleId: "presale_2",
        addedAt: now - 3 * 24 * 60 * 60 * 1000,
        addedBy: "admin",
      },
      {
        address: "wallet_address_7",
        presaleId: "presale_2",
        addedAt: now - 3 * 24 * 60 * 60 * 1000,
        addedBy: "admin",
      },
      {
        address: "wallet_address_8",
        presaleId: "presale_2",
        addedAt: now - 2 * 24 * 60 * 60 * 1000,
        addedBy: "admin",
      },
    ]
    this.whitelists.set("presale_2", whitelist2)
  }

  /**
   * Récupère toutes les presales
   */
  getAllPresales(): PresaleStatus[] {
    return Array.from(this.presales.values())
  }

  /**
   * Récupère une presale par son ID
   */
  getPresaleById(presaleId: string): PresaleStatus | null {
    return this.presales.get(presaleId) || null
  }

  /**
   * Crée une nouvelle presale
   */
  async createPresale(
    config: PresaleConfig,
    creatorAddress: string,
    feePayer: Keypair
  ): Promise<{ success: boolean; presaleId?: string; error?: string }> {
    try {
      // Valider la configuration
      const validationResult = this.validatePresaleConfig(config)
      if (!validationResult.valid) {
        return {
          success: false,
          error: validationResult.error,
        }
      }

      // Générer un ID unique pour la presale
      const presaleId = `presale_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`

      // Créer la presale
      const presaleStatus: PresaleStatus = {
        id: presaleId,
        config,
        status: "pending",
        raisedAmount: 0,
        participants: 0,
        progress: 0,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }

      // Enregistrer la presale
      this.presales.set(presaleId, presaleStatus)
      this.participations.set(presaleId, [])

      // Initialiser la whitelist si nécessaire
      if (config.whitelistEnabled) {
        this.whitelists.set(presaleId, [])
      }

      return {
        success: true,
        presaleId,
      }
    } catch (error: any) {
      console.error("Erreur lors de la création de la presale:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la création de la presale",
      }
    }
  }

  /**
   * Valide la configuration d'une presale
   */
  private validatePresaleConfig(config: PresaleConfig): { valid: boolean; error?: string } {
    // Vérifier que les champs obligatoires sont présents
    if (!config.tokenAddress || !config.tokenName || !config.tokenSymbol) {
      return {
        valid: false,
        error: "Les informations du token sont incomplètes",
      }
    }

    // Vérifier que les caps sont valides
    if (config.softCap <= 0 || config.hardCap <= 0 || config.softCap > config.hardCap) {
      return {
        valid: false,
        error: "Les caps sont invalides. Le soft cap doit être inférieur au hard cap et tous deux doivent être positifs",
      }
    }

    // Vérifier que les limites d'achat sont valides
    if (
      config.minPurchase <= 0 ||
      config.maxPurchase <= 0 ||
      config.minPurchase > config.maxPurchase ||
      config.maxPurchase > config.hardCap
    ) {
      return {
        valid: false,
        error:
          "Les limites d'achat sont invalides. Le minimum doit être inférieur au maximum et le maximum ne doit pas dépasser le hard cap",
      }
    }

    // Vérifier que les dates sont valides
    if (config.startTime >= config.endTime || config.startTime < Date.now()) {
      return {
        valid: false,
        error: "Les dates sont invalides. La date de début doit être dans le futur et antérieure à la date de fin",
      }
    }

    // Vérifier que les pourcentages sont valides
    if (
      config.liquidityPercentage < 30 ||
      config.liquidityPercentage > 100 ||
      config.teamTokens < 0 ||
      config.marketingTokens < 0 ||
      config.teamTokens + config.marketingTokens > 50
    ) {
      return {
        valid: false,
        error:
          "Les pourcentages sont invalides. La liquidité doit être d'au moins 30%, et les tokens réservés ne doivent pas dépasser 50%",
      }
    }

    return { valid: true }
  }

  /**
   * Met à jour le statut d'une presale
   */
  updatePresaleStatus(presaleId: string, status: PresaleStatus["status"]): boolean {
    const presale = this.presales.get(presaleId)
    if (!presale) {
      return false
    }

    presale.status = status
    presale.updatedAt = Date.now()
    this.presales.set(presaleId, presale)
    return true
  }

  /**
   * Ajoute une adresse à la whitelist d'une presale
   */
  addToWhitelist(presaleId: string, address: string, addedBy: string): boolean {
    const whitelist = this.whitelists.get(presaleId) || []
    
    // Vérifier si l'adresse est déjà dans la whitelist
    if (whitelist.some(entry => entry.address === address)) {
      return false
    }

    const entry: WhitelistEntry = {
      address,
      presaleId,
      addedAt: Date.now(),
      addedBy,
    }

    whitelist.push(entry)
    this.whitelists.set(presaleId, whitelist)
    return true
  }

  /**
   * Ajoute plusieurs adresses à la whitelist d'une presale
   */
  addMultipleToWhitelist(presaleId: string, addresses: string[], addedBy: string): { success: boolean; added: number } {
    const whitelist = this.whitelists.get(presaleId) || []
    
    let added = 0
    for (const address of addresses) {
      // Vérifier si l'adresse est déjà dans la whitelist
      if (whitelist.some(entry => entry.address === address)) {
        continue
      }

      const entry: WhitelistEntry = {
        address,
        presaleId,
        addedAt: Date.now(),
        addedBy,
      }

      whitelist.push(entry)
      added++
    }

    this.whitelists.set(presaleId, whitelist)
    return { success: true, added }
  }

  /**
   * Vérifie si une adresse est dans la whitelist d'une presale
   */
  isWhitelisted(presaleId: string, address: string): boolean {
    const whitelist = this.whitelists.get(presaleId) || []
    return whitelist.some(entry => entry.address === address)
  }

  /**
   * Récupère la whitelist d'une presale
   */
  getWhitelist(presaleId: string): WhitelistEntry[] {
    return this.whitelists.get(presaleId) || []
  }

  /**
   * Participe à une presale
   */
  async participate(
    presaleId: string,
    userAddress: string,
    amount: number,
    transactionId: string
  ): Promise<{ success: boolean; tokens?: number; error?: string }> {
    try {
      // Récupérer la presale
      const presale = this.presales.get(presaleId)
      if (!presale) {
        return {
          success: false,
          error: "Presale non trouvée",
        }
      }

      // Vérifier que la presale est active
      if (presale.status !== "active") {
        return {
          success: false,
          error: `La presale n'est pas active (statut actuel: ${presale.status})`,
        }
      }

      // Vérifier que la presale n'a pas atteint son hard cap
      if (presale.raisedAmount >= presale.config.hardCap) {
        return {
          success: false,
          error: "La presale a atteint son hard cap",
        }
      }

      // Vérifier que le montant est dans les limites
      if (amount < presale.config.minPurchase || amount > presale.config.maxPurchase) {
        return {
          success: false,
          error: `Le montant doit être compris entre ${presale.config.minPurchase} et ${presale.config.maxPurchase} SOL`,
        }
      }

      // Vérifier la whitelist si nécessaire
      if (presale.config.whitelistEnabled && !this.isWhitelisted(presaleId, userAddress)) {
        return {
          success: false,
          error: "Vous n'êtes pas sur la whitelist de cette presale",
        }
      }

      // Vérifier que l'utilisateur n'a pas dépassé sa limite d'achat
      const userParticipations = this.getUserParticipations(presaleId, userAddress)
      const totalUserAmount = userParticipations.reduce((sum, p) => sum + p.amount, 0)
      if (totalUserAmount + amount > presale.config.maxPurchase) {
        return {
          success: false,
          error: `Vous avez déjà participé pour ${totalUserAmount} SOL. Vous ne pouvez pas dépasser ${
            presale.config.maxPurchase
          } SOL (${presale.config.maxPurchase - totalUserAmount} SOL restants)`,
        }
      }

      // Calculer le nombre de tokens
      const tokens = amount * (1 / presale.config.price)

      // Enregistrer la participation
      const participation: Participation = {
        id: `participation_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
        presaleId,
        address: userAddress,
        amount,
        tokens,
        timestamp: Date.now(),
        claimed: false,
        transactionId,
      }

      const participations = this.participations.get(presaleId) || []
      participations.push(participation)
      this.participations.set(presaleId, participations)

      // Mettre à jour la presale
      presale.raisedAmount += amount
      presale.participants = new Set(participations.map((p) => p.address)).size
      presale.progress = (presale.raisedAmount / presale.config.hardCap) * 100
      presale.updatedAt = Date.now()

      // Vérifier si le hard cap est atteint
      if (presale.raisedAmount >= presale.config.hardCap) {
        presale.status = "completed"
      }

      this.presales.set(presaleId, presale)

      return {
        success: true,
        tokens,
      }
    } catch (error: any) {
      console.error("Erreur lors de la participation à la presale:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la participation à la presale",
      }
    }
  }

  /**
   * Récupère les participations d'un utilisateur à une presale
   */
  getUserParticipations(presaleId: string, userAddress: string): Participation[] {
    const participations = this.participations.get(presaleId) || []
    return participations.filter((p) => p.address === userAddress)
  }

  /**
   * Récupère toutes les participations à une presale
   */
  getAllParticipations(presaleId: string): Participation[] {
    return this.participations.get(presaleId) || []
  }

  /**
   * Réclame les tokens d'une presale
   */
  async claimTokens(
    presaleId: string,
    userAddress: string,
    transactionId: string
  ): Promise<{ success: boolean; tokens?: number; error?: string }> {
    try {
      // Récupérer la presale
      const presale = this.presales.get(presaleId)
      if (!presale) {
        return {
          success: false,
          error: "Presale non trouvée",
        }
      }

      // Vérifier que la presale est terminée et réussie
      if (presale.status !== "completed") {
        return {
          success: false,
          error: `La presale n'est pas terminée ou a échoué (statut actuel: ${presale.status})`,
        }
      }

      // Récupérer les participations de l'utilisateur
      const participations = this.participations.get(presaleId) || []
      const userParticipations = participations.filter((p) => p.address === userAddress && !p.claimed)
      
      if (userParticipations.length === 0) {
        return {
          success: false,
          error: "Vous n'avez pas de tokens à réclamer",
        }
      }

      // Calculer le nombre total de tokens à réclamer
      const totalTokens = userParticipations.reduce((sum, p) => sum + p.tokens, 0)

      // Mettre à jour les participations
      const updatedParticipations = participations.map((p) => {
        if (p.address === userAddress && !p.claimed) {
          return {
            ...p,
            claimed: true,
            claimTimestamp: Date.now(),
          }
        }
        return p
      })

      this.participations.set(presaleId, updatedParticipations)

      return {
        success: true,
        tokens: totalTokens,
      }
    } catch (error: any) {
      console.error("Erreur lors de la réclamation des tokens:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la réclamation des tokens",
      }
    }
  }

  /**
   * Annule une presale
   */
  async cancelPresale(presaleId: string, adminAddress: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Récupérer la presale
      const presale = this.presales.get(presaleId)
      if (!presale) {
        return {
          success: false,
          error: "Presale non trouvée",
        }
      }

      // Vérifier que la presale peut être annulée
      if (presale.status === "completed" || presale.status === "cancelled") {
        return {
          success: false,
          error: `La presale ne peut pas être annulée (statut actuel: ${presale.status})`,
        }
      }

      // Mettre à jour le statut
      presale.status = "cancelled"
      presale.updatedAt = Date.now()
      this.presales.set(presaleId, presale)

      return {
        success: true,
      }
    } catch (error: any) {
      console.error("Erreur lors de l'annulation de la presale:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de l'annulation de la presale",
      }
    }
  }

  /**
   * Finalise une presale
   */
  async finalizePresale(presaleId: string, adminAddress: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Récupérer la presale
      const presale = this.presales.get(presaleId)
      if (!presale) {
        return {
          success: false,
          error: "Presale non trouvée",
        }
      }

      // Vérifier que la presale est active
      if (presale.status !== "active") {
        return {
          success: false,
          error: `La presale n'est pas active (statut actuel: ${presale.status})`,
        }
      }

      // Vérifier que le soft cap a été atteint
      if (presale.raisedAmount < presale.config.softCap) {
        return {
          success: false,
          error: "La presale n'a pas atteint son soft cap",
        }
      }

      // Mettre à jour le statut
      presale.status = "completed"
      presale.updatedAt = Date.now()
      this.presales.set(presaleId, presale)

      return {
        success: true,
      }
    } catch (error: any) {
      console.error("Erreur lors de la finalisation de la presale:", error)
      return {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la finalisation de la presale",
      }
    }
  }

  /**
   * Met à jour automatiquement le statut des presales en fonction de leur date
   */
  async updatePresaleStatuses(): Promise<void> {
    try {
      const now = Date.now()

      for (const [presaleId, presale] of this.presales.entries()) {
        // Mettre à jour les presales en attente qui ont commencé
        if (presale.status === "pending" && now >= presale.config.startTime) {
          presale.status = "active"
          presale.updatedAt = now
          this.presales.set(presaleId, presale)
        }

        // Mettre à jour les presales actives qui sont terminées
        if (presale.status === "active" && now >= presale.config.endTime) {
          // Vérifier si le soft cap a été atteint
          if (presale.raisedAmount >= presale.config.softCap) {
            presale.status = "completed"
          } else {
            presale.status = "failed"
          }

          presale.updatedAt = now
          this.presales.set(presaleId, presale)
        }
      }
    } catch (error) {
      console.error("Erreur lors de la mise à jour des statuts des presales:", error)
    }
  }

  /**
   * Génère des statistiques sur les presales
   */
  generatePresaleStatistics(): {
    total: number
    active: number
    completed: number
    failed: number
    cancelled: number
    pending: number
    totalRaised: number
    totalParticipants: number
    averageProgress: number
  } {
    const presales = Array.from(this.presales.values())

    const stats = {
      total: presales.length,
      active: presales.filter((p) => p.status === "active").length,
      completed: presales.filter((p) => p.status === "completed").length,
      failed: presales.filter((p) => p.status === "failed").length,
      cancelled: presales.filter((p) => p.status === "cancelled").length,
      pending: presales.filter((p) => p.status === "pending").length,
      totalRaised: presales.reduce((sum, p) => sum + p.raisedAmount, 0),
      totalParticipants: presales.reduce((sum, p) => sum + p.participants, 0),
      averageProgress: presales.length
        ? presales.reduce((sum, p) => sum\

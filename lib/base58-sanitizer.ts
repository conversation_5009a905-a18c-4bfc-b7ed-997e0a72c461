/**
 * Utilitaire pour gérer les problèmes de caractères non-base58
 */

// Caractères valides en base58
const BASE58_CHARS = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

/**
 * Vérifie si une chaîne contient uniquement des caractères base58 valides
 */
export function isValidBase58(input: string): boolean {
  if (!input) return false

  for (let i = 0; i < input.length; i++) {
    if (!BASE58_CHARS.includes(input[i])) {
      return false
    }
  }

  return true
}

/**
 * Trouve le premier caractère non-base58 dans une chaîne
 */
export function findInvalidBase58Char(input: string): { char: string; position: number } | null {
  if (!input) return null

  for (let i = 0; i < input.length; i++) {
    if (!BASE58_CHARS.includes(input[i])) {
      return { char: input[i], position: i }
    }
  }

  return null
}

/**
 * <PERSON>toie une chaîne en supprimant tous les caractères non-base58
 */
export function sanitizeToBase58(input: string): string {
  if (!input) return ""

  let result = ""
  for (let i = 0; i < input.length; i++) {
    if (BASE58_CHARS.includes(input[i])) {
      result += input[i]
    }
  }

  return result
}

/**
 * Remplace les caractères non-base58 par des caractères similaires valides
 */
export function replaceNonBase58Chars(input: string): string {
  if (!input) return ""

  let result = ""
  for (let i = 0; i < input.length; i++) {
    const char = input[i]
    if (BASE58_CHARS.includes(char)) {
      result += char
    } else {
      // Remplacements courants pour les caractères problématiques
      switch (char) {
        case "O":
          result += "o"
          break
        case "I":
          result += "i"
          break
        case "l":
          result += "1"
          break
        case "0":
          result += "o"
          break
        case " ":
          break // Supprimer les espaces
        default:
          result += "X" // Remplacer par un X par défaut
      }
    }
  }

  return result
}

/**
 * Analyse une erreur pour déterminer si c'est un problème de base58
 */
export function analyzeBase58Error(error: any): {
  isBase58Error: boolean
  message: string
  invalidChar?: string
  position?: number
} {
  const errorMessage = error?.message || String(error)

  if (errorMessage.includes("Non-base58 character")) {
    // Essayer d'extraire le caractère problématique de l'erreur
    const match =
      errorMessage.match(/Non-base58 character '(.)'/) ||
      errorMessage.match(/Non-base58 character "(.)"/) ||
      errorMessage.match(/Non-base58 character (.)/)

    if (match && match[1]) {
      return {
        isBase58Error: true,
        message: `Caractère non-base58 détecté: '${match[1]}'`,
        invalidChar: match[1],
        position: errorMessage.indexOf(match[1]),
      }
    }

    return {
      isBase58Error: true,
      message: "Caractère non-base58 détecté dans l'entrée",
    }
  }

  return {
    isBase58Error: false,
    message: errorMessage,
  }
}

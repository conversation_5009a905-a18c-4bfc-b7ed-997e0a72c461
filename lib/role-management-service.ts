// Types
export interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  createdAt: string
  updatedAt: string
  createdBy?: string
}

export interface Permission {
  id: string
  name: string
  description: string
  category: string
}

// Permissions disponibles dans le système
const permissions: Permission[] = [
  {
    id: "user_view",
    name: "Voir les utilisateurs",
    description: "Permet de voir la liste des utilisateurs",
    category: "utilisateurs",
  },
  {
    id: "user_create",
    name: "Créer des utilisateurs",
    description: "Permet de créer de nouveaux utilisateurs",
    category: "utilisateurs",
  },
  {
    id: "user_edit",
    name: "Modifier les utilisateurs",
    description: "Permet de modifier les informations des utilisateurs",
    category: "utilisateurs",
  },
  {
    id: "user_delete",
    name: "Supprimer des utilisateurs",
    description: "Permet de supprimer des utilisateurs",
    category: "utilisateurs",
  },
  {
    id: "token_view",
    name: "Voir les tokens",
    description: "Permet de voir la liste des tokens",
    category: "tokens",
  },
  {
    id: "token_create",
    name: "Créer des tokens",
    description: "Permet de créer de nouveaux tokens",
    category: "tokens",
  },
  {
    id: "token_edit",
    name: "Modifier les tokens",
    description: "Permet de modifier les tokens",
    category: "tokens",
  },
  {
    id: "token_delete",
    name: "Supprimer des tokens",
    description: "Permet de supprimer des tokens",
    category: "tokens",
  },
  {
    id: "admin_view",
    name: "Voir les administrateurs",
    description: "Permet de voir la liste des administrateurs",
    category: "administration",
  },
  {
    id: "admin_create",
    name: "Créer des administrateurs",
    description: "Permet de créer de nouveaux administrateurs",
    category: "administration",
  },
  {
    id: "admin_edit",
    name: "Modifier les administrateurs",
    description: "Permet de modifier les administrateurs",
    category: "administration",
  },
  {
    id: "admin_delete",
    name: "Supprimer des administrateurs",
    description: "Permet de supprimer des administrateurs",
    category: "administration",
  },
  {
    id: "role_view",
    name: "Voir les rôles",
    description: "Permet de voir la liste des rôles",
    category: "rôles",
  },
  {
    id: "role_create",
    name: "Créer des rôles",
    description: "Permet de créer de nouveaux rôles",
    category: "rôles",
  },
  {
    id: "role_edit",
    name: "Modifier les rôles",
    description: "Permet de modifier les rôles",
    category: "rôles",
  },
  {
    id: "role_delete",
    name: "Supprimer des rôles",
    description: "Permet de supprimer des rôles",
    category: "rôles",
  },
  {
    id: "network_view",
    name: "Voir les réseaux",
    description: "Permet de voir la liste des réseaux blockchain",
    category: "réseaux",
  },
  {
    id: "network_create",
    name: "Créer des réseaux",
    description: "Permet de créer de nouveaux réseaux blockchain",
    category: "réseaux",
  },
  {
    id: "network_edit",
    name: "Modifier les réseaux",
    description: "Permet de modifier les réseaux blockchain",
    category: "réseaux",
  },
  {
    id: "network_delete",
    name: "Supprimer des réseaux",
    description: "Permet de supprimer des réseaux blockchain",
    category: "réseaux",
  },
  {
    id: "system_settings",
    name: "Paramètres système",
    description: "Permet de modifier les paramètres système",
    category: "système",
  },
  {
    id: "logs_view",
    name: "Voir les logs",
    description: "Permet de voir les logs du système",
    category: "système",
  },
  {
    id: "all",
    name: "Toutes les permissions",
    description: "Donne accès à toutes les fonctionnalités",
    category: "système",
  },
]

// Rôles prédéfinis
const roles: Role[] = [
  {
    id: "superadmin",
    name: "Super Administrateur",
    description: "Accès complet à toutes les fonctionnalités",
    permissions: ["all"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2023-01-01T00:00:00Z",
    createdBy: "system",
  },
  {
    id: "admin",
    name: "Administrateur",
    description: "Accès à la plupart des fonctionnalités administratives",
    permissions: [
      "user_view",
      "user_create",
      "user_edit",
      "token_view",
      "token_create",
      "token_edit",
      "token_delete",
      "admin_view",
      "role_view",
      "network_view",
      "logs_view",
    ],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2023-01-01T00:00:00Z",
    createdBy: "system",
  },
  {
    id: "moderator",
    name: "Modérateur",
    description: "Peut gérer les utilisateurs et voir les tokens",
    permissions: ["user_view", "user_edit", "token_view"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2023-01-01T00:00:00Z",
    createdBy: "system",
  },
  {
    id: "readonly",
    name: "Lecture seule",
    description: "Accès en lecture seule aux données",
    permissions: ["user_view", "token_view", "network_view", "role_view"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2023-01-01T00:00:00Z",
    createdBy: "system",
  },
]

// Mapping des utilisateurs à leurs rôles
const userRoles: Record<string, string[]> = {
  "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM": ["superadmin"],
  "5FHwkrdxkRzDNkwkjM5aMVxnvE4UzwgJhEEtrJHAzKqJ": ["admin"],
  "7Z6UgwFJdSMEfrBKVgGP4k8mB6fEkdvwqUXqjzH5gJnq": ["moderator"],
  "3XdUVd35L8zgpEJVWWQc9iVVJqmF5SgxfmxmVNgEhJTW": ["readonly"],
}

// Fonctions d'accès aux données
export async function getAllRoles(): Promise<Role[]> {
  return [...roles]
}

export async function getRoleById(id: string): Promise<Role | null> {
  return roles.find((role) => role.id === id) || null
}

export async function createRole(
  role: Omit<Role, "id" | "createdAt" | "updatedAt" | "createdBy">,
  createdBy: string,
): Promise<Role> {
  const id = role.name.toLowerCase().replace(/\s+/g, "-")
  const newRole: Role = {
    id,
    ...role,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy,
  }

  roles.push(newRole)
  return newRole
}

export async function updateRole(role: Role): Promise<Role> {
  const index = roles.findIndex((r) => r.id === role.id)
  if (index === -1) {
    throw new Error(`Role with id ${role.id} not found`)
  }

  const updatedRole = {
    ...role,
    updatedAt: new Date().toISOString(),
  }

  roles[index] = updatedRole
  return updatedRole
}

export async function deleteRole(id: string): Promise<void> {
  const index = roles.findIndex((role) => role.id === id)
  if (index === -1) {
    throw new Error(`Role with id ${id} not found`)
  }

  // Vérifier si le rôle est un rôle système
  if (["superadmin", "admin", "moderator", "readonly"].includes(id)) {
    throw new Error("Cannot delete system roles")
  }

  roles.splice(index, 1)

  // Supprimer le rôle des utilisateurs
  Object.keys(userRoles).forEach((userId) => {
    userRoles[userId] = userRoles[userId].filter((roleId) => roleId !== id)
  })
}

export async function getAllPermissions(): Promise<Permission[]> {
  return [...permissions]
}

export async function getUserRoles(userId: string): Promise<string[]> {
  return userRoles[userId] || []
}

export async function assignRoleToUser(userId: string, roleId: string): Promise<void> {
  // Vérifier si le rôle existe
  const role = await getRoleById(roleId)
  if (!role) {
    throw new Error(`Role with id ${roleId} not found`)
  }

  // Ajouter le rôle à l'utilisateur s'il ne l'a pas déjà
  if (!userRoles[userId]) {
    userRoles[userId] = []
  }

  if (!userRoles[userId].includes(roleId)) {
    userRoles[userId].push(roleId)
  }
}

export async function removeRoleFromUser(userId: string, roleId: string): Promise<void> {
  if (!userRoles[userId]) {
    return
  }

  userRoles[userId] = userRoles[userId].filter((id) => id !== roleId)
}

export async function hasPermission(userId: string, permissionId: string): Promise<boolean> {
  const userRoleIds = userRoles[userId] || []

  // Si l'utilisateur n'a pas de rôles, il n'a pas la permission
  if (userRoleIds.length === 0) {
    return false
  }

  // Récupérer tous les rôles de l'utilisateur
  const userRolesData = await Promise.all(userRoleIds.map((id) => getRoleById(id)))

  // Vérifier si l'un des rôles a la permission "all" ou la permission spécifique
  return userRolesData.some(
    (role) => role !== null && (role.permissions.includes("all") || role.permissions.includes(permissionId)),
  )
}

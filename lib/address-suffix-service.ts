import { Keypair } from "@solana/web3.js"
import Web3 from "web3"

// Interface pour les options de grinding
interface GrindingOptions {
  suffix: string
  maxAttempts?: number
  threads?: number
  onProgress?: (attempts: number) => void
}

// Interface pour le résultat du grinding
interface GrindingResult {
  success: boolean
  keypair?: {
    publicKey: string
    secret: number[]
  }
  address?: string
  salt?: string
  attempts?: number
  error?: string
}

// Interface pour les informations de suffixe stockées
interface StoredSuffixInfo {
  suffix: string
  networkId: string
  networkType: "solana" | "bnb"
  keypair?: {
    publicKey: string
    secret: number[]
  }
  salt?: string
  createdAt: string
  lastUsed?: string
  usageCount: number
}

// Service pour la gestion des suffixes d'adresse
class AddressSuffixService {
  // Stockage en mémoire des suffixes pré-générés (dans une implémentation réelle, cela serait dans une base de données sécurisée)
  private storedSuffixes: Record<string, StoredSuffixInfo> = {}

  // Récupérer le suffixe configuré pour un réseau spécifique
  async getSuffixForNetwork(networkId: string): Promise<string> {
    // Dans une implémentation réelle, cela viendrait d'une API ou d'une base de données
    const networkSuffixes = {
      "solana-devnet": "BETAGF",
      "solana-mainnet": "GF",
      "bnb-testnet": "BETAGF",
      "bnb-mainnet": "GF",
    }

    // Vérifier si nous avons un suffixe stocké pour ce réseau
    const storedSuffix = Object.values(this.storedSuffixes).find((info) => info.networkId === networkId)
    if (storedSuffix) {
      return storedSuffix.suffix
    }

    return networkSuffixes[networkId] || "BETAGF"
  }

  // Récupérer la keypair pré-générée pour un suffixe et un réseau Solana
  async getSolanaKeypairForSuffix(suffix: string, networkId: string): Promise<Keypair | null> {
    // Créer une clé unique pour le suffixe et le réseau
    const key = `${networkId}:${suffix}`

    // Vérifier si nous avons déjà une keypair stockée pour ce suffixe
    if (this.storedSuffixes[key] && this.storedSuffixes[key].keypair) {
      const info = this.storedSuffixes[key]

      // Mettre à jour les statistiques d'utilisation
      info.lastUsed = new Date().toISOString()
      info.usageCount++

      // Recréer la keypair à partir des données stockées
      return Keypair.fromSecretKey(Uint8Array.from(info.keypair.secret))
    }

    return null
  }

  // Récupérer le salt pré-généré pour un suffixe et un réseau BNB
  async getBnbSaltForSuffix(suffix: string, networkId: string): Promise<string | null> {
    // Créer une clé unique pour le suffixe et le réseau
    const key = `${networkId}:${suffix}`

    // Vérifier si nous avons déjà un salt stocké pour ce suffixe
    if (this.storedSuffixes[key] && this.storedSuffixes[key].salt) {
      const info = this.storedSuffixes[key]

      // Mettre à jour les statistiques d'utilisation
      info.lastUsed = new Date().toISOString()
      info.usageCount++

      return info.salt
    }

    return null
  }

  // Stocker une keypair pré-générée pour un suffixe et un réseau Solana
  async storeSolanaKeypairForSuffix(suffix: string, networkId: string, keypair: Keypair): Promise<void> {
    // Créer une clé unique pour le suffixe et le réseau
    const key = `${networkId}:${suffix}`

    // Stocker les informations
    this.storedSuffixes[key] = {
      suffix,
      networkId,
      networkType: "solana",
      keypair: {
        publicKey: keypair.publicKey.toBase58(),
        secret: Array.from(keypair.secretKey),
      },
      createdAt: new Date().toISOString(),
      usageCount: 0,
    }

    // Dans une implémentation réelle, cela serait stocké dans une base de données sécurisée
    console.log(`Keypair stockée pour le suffixe ${suffix} sur le réseau ${networkId}`)
  }

  // Stocker un salt pré-généré pour un suffixe et un réseau BNB
  async storeBnbSaltForSuffix(suffix: string, networkId: string, salt: string, address: string): Promise<void> {
    // Créer une clé unique pour le suffixe et le réseau
    const key = `${networkId}:${suffix}`

    // Stocker les informations
    this.storedSuffixes[key] = {
      suffix,
      networkId,
      networkType: "bnb",
      salt,
      createdAt: new Date().toISOString(),
      usageCount: 0,
    }

    // Dans une implémentation réelle, cela serait stocké dans une base de données sécurisée
    console.log(`Salt stocké pour le suffixe ${suffix} sur le réseau ${networkId}, adresse: ${address}`)
  }

  // Vérifier si un suffixe est valide
  validateSuffix(suffix: string, networkType: "solana" | "bnb"): { valid: boolean; error?: string } {
    if (!suffix || suffix.length === 0) {
      return { valid: false, error: "Le suffixe ne peut pas être vide" }
    }

    if (networkType === "solana") {
      // Pour Solana, vérifier les caractères base58
      const base58Regex = /^[1-9A-HJ-NP-Za-km-z]+$/
      if (!base58Regex.test(suffix)) {
        return { valid: false, error: "Le suffixe doit contenir uniquement des caractères base58 valides pour Solana" }
      }
    } else {
      // Pour BNB, vérifier les caractères hexadécimaux
      const hexRegex = /^[0-9A-Fa-f]+$/
      if (!hexRegex.test(suffix)) {
        return { valid: false, error: "Le suffixe doit contenir uniquement des caractères hexadécimaux pour BNB" }
      }
    }

    if (suffix.length > 8) {
      return { valid: false, error: "Le suffixe ne doit pas dépasser 8 caractères pour des raisons de performance" }
    }

    return { valid: true }
  }

  // Estimer le temps de grinding en secondes
  estimateGrindingTime(suffix: string, networkType: "solana" | "bnb", threads = 4): number {
    // Estimation basée sur la longueur du suffixe et le nombre de threads
    const baseSize = networkType === "solana" ? 58 : 16 // Base58 pour Solana, Base16 pour BNB
    const averageAttempts = Math.pow(baseSize, suffix.length)

    // Ajustement pour le nombre de threads
    const attemptsPerSecond = networkType === "solana" ? 10000 * threads : 5000 * threads // Estimation approximative

    return averageAttempts / attemptsPerSecond
  }

  // Formater le temps estimé
  formatEstimatedTime(seconds: number): string {
    if (seconds < 1) {
      return "moins d'une seconde"
    } else if (seconds < 60) {
      return `${Math.ceil(seconds)} secondes`
    } else if (seconds < 3600) {
      return `${Math.ceil(seconds / 60)} minutes`
    } else if (seconds < 86400) {
      return `${Math.ceil(seconds / 3600)} heures`
    } else {
      return `${Math.ceil(seconds / 86400)} jours`
    }
  }

  // Générer une paire de clés Solana avec un suffixe spécifique
  async grindSolanaKeypairWithSuffix(options: GrindingOptions): Promise<GrindingResult> {
    const { suffix, maxAttempts = 1000000, onProgress } = options

    // Valider le suffixe
    const validation = this.validateSuffix(suffix, "solana")
    if (!validation.valid) {
      return { success: false, error: validation.error }
    }

    try {
      let attempts = 0

      while (attempts < maxAttempts) {
        // Générer une nouvelle paire de clés
        const keypair = Keypair.generate()
        const publicKey = keypair.publicKey.toBase58()

        // Vérifier si l'adresse se termine par le suffixe
        if (publicKey.endsWith(suffix)) {
          return {
            success: true,
            keypair: {
              publicKey: publicKey,
              secret: Array.from(keypair.secretKey),
            },
            attempts: attempts + 1,
          }
        }

        attempts++

        // Mettre à jour la progression toutes les 1000 tentatives
        if (onProgress && attempts % 1000 === 0) {
          onProgress(attempts)
        }
      }

      return {
        success: false,
        error: `Nombre maximum de tentatives atteint (${maxAttempts})`,
        attempts: maxAttempts,
      }
    } catch (error) {
      return {
        success: false,
        error: `Erreur lors du grinding: ${error.message}`,
      }
    }
  }

  // Générer un salt pour BNB avec un suffixe spécifique
  async grindBnbSaltForSuffix(
    options: GrindingOptions & { factoryAddress: string; bytecode: string },
  ): Promise<GrindingResult> {
    const { suffix, maxAttempts = 1000000, onProgress, factoryAddress, bytecode } = options

    // Valider le suffixe
    const validation = this.validateSuffix(suffix, "bnb")
    if (!validation.valid) {
      return { success: false, error: validation.error }
    }

    try {
      let attempts = 0
      const web3 = new Web3()

      while (attempts < maxAttempts) {
        // Générer un salt aléatoire
        const salt = web3.utils.randomHex(32)

        // Calculer l'adresse du contrat qui serait créé avec ce salt
        const address = this.calculateCreate2Address(factoryAddress, salt, bytecode)

        // Vérifier si l'adresse se termine par le suffixe (insensible à la casse)
        if (address.toLowerCase().endsWith(suffix.toLowerCase())) {
          return {
            success: true,
            salt,
            address,
            attempts: attempts + 1,
          }
        }

        attempts++

        // Mettre à jour la progression toutes les 1000 tentatives
        if (onProgress && attempts % 1000 === 0) {
          onProgress(attempts)
        }
      }

      return {
        success: false,
        error: `Nombre maximum de tentatives atteint (${maxAttempts})`,
        attempts: maxAttempts,
      }
    } catch (error) {
      return {
        success: false,
        error: `Erreur lors du grinding: ${error.message}`,
      }
    }
  }

  // Calculer l'adresse d'un contrat déployé avec CREATE2
  private calculateCreate2Address(factoryAddress: string, salt: string, bytecode: string): string {
    const web3 = new Web3()

    // Calculer le hash du bytecode
    const bytecodeHash = web3.utils.keccak256(bytecode)

    // Calculer l'adresse selon la formule CREATE2
    const addressBytes = web3.utils.keccak256("0xff" + factoryAddress.slice(2) + salt.slice(2) + bytecodeHash.slice(2))

    // Extraire les 20 derniers octets et préfixer avec '0x'
    return "0x" + addressBytes.slice(-40)
  }

  // Obtenir la liste des suffixes stockés
  getStoredSuffixes(): StoredSuffixInfo[] {
    return Object.values(this.storedSuffixes)
  }
}

export default new AddressSuffixService()

{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "latest", "@noble/ed25519": "latest", "@project-serum/anchor": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "latest", "@solana/spl-token": "latest", "@solana/spl-token-registry": "latest", "@solana/wallet-adapter-base": "latest", "@solana/wallet-adapter-react": "latest", "@solana/wallet-adapter-react-ui": "latest", "@solana/wallet-adapter-wallets": "latest", "@solana/web3.js": "latest", "@upstash/redis": "latest", "@vercel/kv": "latest", "arweave": "latest", "autoprefixer": "^10.4.20", "axios": "latest", "bs58": "latest", "chart.js": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "crypto": "latest", "date-fns": "latest", "embla-carousel-react": "8.5.1", "ethers": "latest", "fs": "latest", "immer": "latest", "input-otp": "1.4.1", "jose": "latest", "lightweight-charts": "latest", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "nft.storage": "latest", "openai": "latest", "os": "latest", "path": "latest", "pg": "latest", "pg-native": "latest", "react": "^19", "react-chartjs-2": "latest", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tweetnacl": "latest", "use-sync-external-store": "latest", "uuid": "latest", "vaul": "^0.9.6", "web3": "latest", "web3-utils": "latest", "worker_threads": "latest", "ws": "latest", "zod": "latest", "zustand": "latest"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}
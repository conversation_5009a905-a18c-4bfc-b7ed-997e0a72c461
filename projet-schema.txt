SOLANA PLATFORM - ARCHITECTURE DU PROJET
=========================================

Structure des répertoires et fichiers
-------------------------------------

app/
├── admin/
│   ├── activity-log/
│   ├── admin-management/
│   ├── audit-log/
│   ├── dashboard/
│   ├── env-manager/
│   ├── env-variables/
│   ├── ecosystem-token/
│   ├── keypair-generator/
│   ├── keypair-manager/
│   ├── login/
│   ├── network-management/
│   ├── performance-dashboard/
│   ├── performance-management/
│   ├── platform-control/
│   ├── platform-management/
│   ├── platform-settings/
│   ├── presale-management/
│   ├── quantum-advanced/
│   ├── quantum-dashboard/
│   ├── quantum-master/
│   ├── quantum-settings/
│   ├── quantum-tokens/
│   ├── report-generator/
│   ├── reports/
│   ├── role-management/
│   ├── security/
│   ├── security-dashboard/
│   ├── storage-config/
│   ├── suffix-config/
│   ├── token-creator-discounts/
│   ├── token-factory-config/
│   ├── token-management/
│   ├── token-parameters/
│   ├── token-suffixes/
│   ├── user-management/
│   ├── user-role-assignment/
│   ├── version-dashboard/
│   ├── wallet-management/
│   ├── auth/
│   ├── connect/
│   ├── layout.tsx
│   ├── loading.tsx
│   ├── page.tsx
│   └── routes.ts
├── api/
│   ├── admin/
│   │   ├── activity/
│   │   ├── auth/
│   │   ├── performance/
│   │   ├── permissions/
│   │   ├── roles/
│   │   ├── user-roles/
│   │   ├── users/
│   │   └── widgets/
│   ├── coingecko/
│   ├── create-token/
│   ├── network/
│   ├── quantum/
│   ├── token/
│   └── wallet/
├── coin/
│   └── [id]/
├── create/
├── creator-dashboard/
├── explore/
├── market/
│   └── token/
│       └── [id]/
├── memecoin-launchpad/
│   ├── coin/
│   │   └── [id]/
│   └── create/
├── my-tokens/
├── nft-gallery/
├── presale/
├── pump/
│   └── token/
│       └── [id]/
├── settings/
├── staking/
├── token/
│   └── [address]/
├── token-analytics/
│   └── [address]/
├── token-dashboard/
│   └── [address]/
├── token-factory/
│   ├── bonding-curve/
│   ├── create/
│   ├── robust/
│   ├── success/
│   ├── token/
│   │   └── [id]/
│   ├── tokens/
│   ├── troubleshoot/
│   ├── ultra/
│   ├── loading.tsx
│   └── page.tsx
├── token-launchpad/
│   └── [id]/
├── token-quantum/
│   ├── create/
│   ├── launch/
│   │   ├── [id]/
│   │   └── create/
│   └── success/
├── globals.css
├── grind-suffix-action.ts
├── layout.tsx
└── page.tsx

components/
├── admin/
│   ├── admin-activity-log.tsx
│   ├── admin-activity-stats.tsx
│   ├── admin-audit-log.tsx
│   ├── admin-auth-check.tsx
│   ├── admin-dashboard.tsx
│   ├── admin-guard.tsx
│   ├── admin-header.tsx
│   ├── admin-logout.tsx
│   ├── admin-management.tsx
│   ├── admin-role-management.tsx
│   ├── admin-sidebar-nav.tsx
│   ├── admin-sidebar.tsx
│   ├── admin-token-creator.tsx
│   ├── advanced-activity-log.tsx
│   ├── advanced-user-management.tsx
│   ├── api-integrations.tsx
│   ├── automated-tests.tsx
│   ├── dashboard-metrics.tsx
│   ├── ecosystem-token-configuration.tsx
│   ├── enhanced-admin-dashboard.tsx
│   ├── enhanced-dashboard-metrics.tsx
│   ├── enhanced-dashboard.tsx
│   ├── enhanced-presale-management.tsx
│   ├── env-variables-guide.tsx
│   ├── env-variables-lock-manager.tsx
│   ├── env-variables-manager.tsx
│   ├── financial-dashboard.tsx
│   ├── financial-reports.tsx
│   ├── keypair-generator.tsx
│   ├── keypair-manager.tsx
│   ├── network-management.tsx
│   ├── network-selector.tsx
│   ├── notification-center.tsx
│   ├── permission-manager.tsx
│   ├── platform-audit.tsx
│   ├── platform-metrics.tsx
│   ├── platform-settings.tsx
│   ├── pre-mined-key-config.tsx
│   ├── presale-calendar.tsx
│   ├── presale-dashboard.tsx
│   ├── presale-management.tsx
│   ├── presale-statistics.tsx
│   ├── quantum-advanced-config.tsx
│   ├── quantum-alerts.tsx
│   ├── quantum-analytics.tsx
│   ├── quantum-audit-logs.tsx
│   ├── quantum-integration-settings.tsx
│   ├── quantum-launch-management.tsx
│   ├── quantum-security-dashboard.tsx
│   ├── quantum-security-settings.tsx
│   ├── quantum-token-config.tsx
│   ├── quantum-token-creator.tsx
│   ├── quantum-token-details.tsx
│   ├── quantum-token-list.tsx
│   ├── quantum-token-management.tsx
│   ├── quantum-tokenomics-settings.tsx
│   ├── quantum-user-management.tsx
│   ├── report-generator.tsx
│   ├── role-management.tsx
│   ├── secure-env-manager.tsx
│   ├── security-audit-report.tsx
│   ├── security-audit.tsx
│   ├── security-dashboard.tsx
│   ├── sidebar-nav.tsx
│   ├── storage-config.tsx
│   ├── suffix-configuration.tsx
│   ├── suffix-management.tsx
│   ├── telegram-bot-config.tsx
│   ├── token-creator-with-discounts.tsx
│   ├── token-creator.tsx
│   ├── token-reports.tsx
│   ├── user-management.tsx
│   ├── user-role-assignment.tsx
│   ├── user-role-management.tsx
│   ├── user-token-configuration.tsx
│   ├── user-token-management.tsx
│   ├── version-dashboard.tsx
│   └── wallet-management-dashboard.tsx
├── home/
│   ├── blockchain-memecoins.tsx
│   ├── blockchain-new-tokens.tsx
│   ├── market-carousel.tsx
│   ├── memecoin-ticker.tsx
│   ├── meme-coins.tsx
│   ├── roadmap.tsx
│   ├── top-coins.tsx
│   ├── trending-coins.tsx
│   └── user-tokens-ticker.tsx
├── memecoin-launchpad/
│   ├── analytics-dashboard.tsx
│   ├── how-it-works-modal.tsx
│   ├── launch-tracker.tsx
│   ├── meme-card.tsx
│   └── meme-price-chart.tsx
├── token/
│   ├── market-cap-progress-bar.tsx
│   ├── recent-token-activity.tsx
│   ├── similar-tokens.tsx
│   ├── token-activity-feed.tsx
│   ├── token-comparison.tsx
│   ├── token-dashboard.tsx
│   ├── token-details.tsx
│   ├── token-header.tsx
│   ├── token-holders-list.tsx
│   ├── token-info.tsx
│   ├── token-liquidity-stats.tsx
│   ├── token-performance-stats.tsx
│   ├── token-price-chart.tsx
│   ├── token-social-stats.tsx
│   ├── token-trade-panel.tsx
│   ├── token-transaction-list.tsx
│   ├── token-transactions-detail.tsx
│   └── token-upcoming-events.tsx
├── token-factory/
│   ├── advanced-features-configuration.tsx
│   ├── advanced-token-creator.tsx
│   ├── base58-diagnostic.tsx
│   ├── base58-validator.tsx
│   ├── basic-token-form.tsx
│   ├── bnb-token-form.tsx
│   ├── bonding-curve-chart.tsx
│   ├── bonding-curve-token-creator.tsx
│   ├── bonding-curve-token-details.tsx
│   ├── deployment-configuration.tsx
│   ├── enhanced-token-creator.tsx
│   ├── launch-configuration.tsx
│   ├── liquidity-configuration.tsx
│   ├── marketing-configuration.tsx
│   ├── network-token-form.tsx
│   ├── payment-options.tsx
│   ├── robust-token-creator.tsx
│   ├── rpc-connection-troubleshooter.tsx
│   ├── security-configuration.tsx
│   ├── security-features-configuration.tsx
│   ├── tax-configuration.tsx
│   ├── token-chat-interface.tsx
│   ├── token-creation-progress.tsx
│   ├── token-creator.tsx
│   ├── token-factory-stats.tsx
│   ├── token-form-with-progress.tsx
│   ├── token-form.tsx
│   ├── token-holders.tsx
│   ├── token-image-editor.tsx
│   ├── token-image-gallery.tsx
│   ├── token-image-selector.tsx
│   ├── token-info-card.tsx
│   ├── token-list.tsx
│   ├── token-name-preview.tsx
│   ├── token-statistics.tsx
│   ├── token-template-selector.tsx
│   ├── token-transactions.tsx
│   ├── token-troubleshooter.tsx
│   ├── tokenomics-configuration.tsx
│   ├── transaction-limits-configuration.tsx
│   ├── ultra-token-creator.tsx
│   └── vesting-configuration.tsx
├── token-launchpad/
│   ├── token-details.tsx
│   └── token-list.tsx
├── token-quantum/
│   ├── advanced-token-creator.tsx
│   ├── initial-distribution.tsx
│   ├── launch-dashboard.tsx
│   ├── launch-details.tsx
│   ├── launch-list.tsx
│   ├── launch-options-display.tsx
│   ├── launch-page.tsx
│   ├── launchpad-integration.tsx
│   ├── quantum-token-creator.tsx
│   ├── quantum-token-form.tsx
│   ├── security-features-display.tsx
│   ├── security-features.tsx
│   ├── token-config-section.tsx
│   ├── token-preview.tsx
│   ├── tokenomics-config.tsx
│   └── tokenomics-display.tsx
├── ui/
│   ├── alert.tsx
│   ├── badge.tsx
│   ├── calendar.tsx
│   ├── chart.tsx
│   ├── date-picker.tsx
│   ├── date-range-picker.tsx
│   ├── popover.tsx
│   ├── progress.tsx
│   └── stepper.tsx
├── admin-link.tsx
├── coin-card.tsx
├── coin-price-chart.tsx
├── footer.tsx
├── header.tsx
├── how-it-works-modal.tsx
├── market-carousel.tsx
├── market-stats.tsx
├── module-selector.tsx
├── network-banner.tsx
├── network-selector.tsx
├── order-book.tsx
├── solana-connection-test.tsx
├── token-info.tsx
├── token-transaction.tsx
├── transaction-history.tsx
└── wallet-provider.tsx

config/
└── token-config.ts

contexts/
└── network-context.tsx

contracts/
└── BnbTokenFactoryWithSuffix.sol

hooks/
├── use-admin-settings.tsx
├── use-admin.tsx
├── use-get-token-by-address.ts
├── use-get-token-on-chain-data.ts
├── use-mobile.tsx
├── use-token-price-history.ts
└── use-token-suffix.ts

lib/
├── address-suffix-service.ts
├── admin-activity-service.ts
├── admin-auth.ts
├── admin-program-utils.ts
├── admin-service.ts
├── auto-dex-listing-service.ts
├── base58-sanitizer.ts
├── base58-utils.ts
├── blockchain-service.ts
├── bnb-chain-service.ts
├── bnb-token-suffix-service.ts
├── bonding-curve-service.ts
├── coin-store.ts
├── coingecko-service.ts
├── crypto-utils.ts
├── dex-integration-service.ts
├── dex-listing-service.ts
├── ecosystem-token-service.ts
├── enhanced-token-service.ts
├── env-config.ts
├── env-lock-service.ts
├── fee-revenue-service.ts
├── input-sanitizer.ts
├── key-utils.ts
├── keypair-manager.ts
├── market-analysis-service.ts
├── market-service.ts
├── meme-store.ts
├── memecoin-service.ts
├── memecoin-store.ts
├── network-config.ts
├── nft-service.ts
├── nft-store.ts
├── performance-service.ts
├── presale-dashboard-service.ts
├── presale-management-service.ts
├── presale-service.ts
├── presale-store.ts
├── quantum-db-service.ts
├── quantum-launch-service.ts
├── quantum-token-factory.ts
├── quantum-token-service.ts
├── role-management-service.ts
├── rpc-connection-utils.ts
├── secure-env-storage.ts
├── signin-message.ts
├── solana-service.ts
├── solana-token-kit.ts
├── solana-utils.ts
├── staking-service.ts
├── staking-store.ts
├── storage-service.ts
├── temp-admin-bypass.ts
├── token-audit-service.ts
├── token-creation-diagnostics.ts
├── token-creation-service.ts
├── token-diagnostics.ts
├── token-discount-service.ts
├── token-liquidity-service.ts
├── token-market-service.ts
├── token-metadata-service.ts
├── token-parameters-service.ts
├── token-payment-service.ts
├── token-registry.ts
├── token-service.ts
├── token-store.ts
├── token-suffix-service.ts
├── token-suffix-store.ts
├── token-transaction-manager.ts
├── token-transaction-service.ts
├── token-vesting-service.ts
├── transaction-error-handler.ts
├── ultra-token-service.ts
└── utils.ts

middleware/
└── admin-auth.ts

programs/
└── off-ice/
    └── src/
        └── lib.rs

public/
├── abstract-geometric-shapes.png
├── abstract-geometric-sm.png
├── abstract-nft-logo.png
├── abstract-tb.png
├── colorful-game-token-logo.png
├── dao-governance-logo.png
├── defi-finance-logo-blue.png
├── digital-token.png
├── funny-memecoin-logo.png
├── global-finance-beta.jpg
├── Global-Finance-Beta.jpg
├── global-finance-logo.png
└── stylized-jp-initials.png

schema/
└── quantum_tokens.sql

scripts/
└── generate-secrets.ts

DESCRIPTION DES COMPOSANTS CLÉS
===============================

1. DOSSIERS PRINCIPAUX
---------------------

app/
Description: Le dossier principal de l'application Next.js qui contient toutes les routes et les pages.
Rôle: Structure l'application selon le système de routage App Router de Next.js.
Contribution: Fournit l'architecture de base pour l'ensemble de l'application web.

components/
Description: Contient tous les composants React réutilisables de l'application.
Rôle: Fournit une bibliothèque de composants modulaires qui peuvent être assemblés pour créer les interfaces utilisateur.
Contribution: Permet la réutilisation du code, maintient la cohérence de l'interface et facilite la maintenance.

config/
Description: Contient les fichiers de configuration globale du projet.
Rôle: Centralise les paramètres de configuration de l'application.
Contribution: Permet une gestion centralisée des variables et paramètres de configuration.

contexts/
Description: Contient les contextes React pour la gestion d'état globale.
Rôle: Fournit des mécanismes de partage d'état entre les composants.
Contribution: Facilite la gestion d'état globale et la communication entre composants.

contracts/
Description: Contient les contrats intelligents pour différentes blockchains.
Rôle: Définit la logique on-chain de l'application.
Contribution: Permet l'intégration avec les blockchains supportées.

hooks/
Description: Contient les hooks React personnalisés.
Rôle: Encapsule la logique réutilisable dans des hooks.
Contribution: Facilite la réutilisation de la logique métier et de la gestion d'état.

lib/
Description: Contient les services, utilitaires et logiques métier.
Rôle: Centralise la logique métier et les fonctions utilitaires.
Contribution: Sépare la logique des composants d'interface pour une meilleure structure.

middleware/
Description: Contient les middlewares pour l'authentification et les autorisations.
Rôle: Gère les vérifications d'authentification et d'autorisation.
Contribution: Sécurise l'application et restreint l'accès aux fonctionnalités protégées.

programs/
Description: Contient les programmes Solana (smart contracts).
Rôle: Définit la logique on-chain spécifique à Solana.
Contribution: Permet des interactions avancées avec la blockchain Solana.

public/
Description: Contient les fichiers statiques accessibles publiquement.
Rôle: Stocke les images, les icônes et autres ressources statiques.
Contribution: Fournit des ressources pour l'interface utilisateur.

schema/
Description: Contient les schémas de base de données.
Rôle: Définit la structure des tables et relations.
Contribution: Assure la cohérence des données dans l'application.

scripts/
Description: Contient les scripts utilitaires pour des tâches diverses.
Rôle: Automatise certaines tâches de développement ou de déploiement.
Contribution: Améliore l'efficacité du développement et du déploiement.

2. MODULES FONCTIONNELS
----------------------

Module Admin:
Description: Ensemble de composants et services pour la gestion administrative.
Rôle: Permet aux administrateurs de gérer la plateforme, les utilisateurs et les configurations.
Contribution: Fournit les outils nécessaires pour superviser et contrôler l'ensemble de la plateforme.

Module Token Factory:
Description: Système de création et gestion de tokens personnalisés.
Rôle: Permet aux utilisateurs de créer, configurer et déployer des tokens sur différentes blockchains.
Contribution: Fonctionnalité principale de la plateforme permettant la création simplifiée de tokens.

Module Memecoin Launchpad:
Description: Système spécialisé pour le lancement de memecoins.
Rôle: Offre des outils spécifiques pour créer et lancer des memecoins.
Contribution: Attire une audience spécifique intéressée par les memecoins.

Module Quantum:
Description: Système avancé de création de tokens avec des fonctionnalités premium.
Rôle: Offre des options avancées pour les créateurs de tokens professionnels.
Contribution: Fournit des fonctionnalités de niveau entreprise pour la création de tokens.

Module Market:
Description: Système d'affichage et d'analyse du marché des cryptomonnaies.
Rôle: Permet aux utilisateurs de suivre les prix et tendances du marché.
Contribution: Offre des informations précieuses pour la prise de décision.

3. SERVICES PRINCIPAUX
---------------------

Blockchain Services:
Description: Services d'interaction avec différentes blockchains (Solana, BNB Chain, etc.).
Rôle: Gère les communications avec les blockchains et l'exécution des transactions.
Contribution: Fournit une abstraction pour interagir avec les différentes blockchains.

Token Services:
Description: Services de création, gestion et analyse de tokens.
Rôle: Gère toutes les opérations liées aux tokens.
Contribution: Cœur fonctionnel de la plateforme pour la gestion des tokens.

Admin Services:
Description: Services de gestion administrative et de sécurité.
Rôle: Gère les permissions, rôles et opérations administratives.
Contribution: Assure la sécurité et la bonne gouvernance de la plateforme.

Market Services:
Description: Services d'analyse de marché et de récupération de données.
Rôle: Collecte et traite les données du marché des cryptomonnaies.
Contribution: Fournit des données essentielles pour l'analyse et la prise de décision.

Storage Services:
Description: Services de stockage de données et de fichiers.
Rôle: Gère le stockage sécurisé des données sensibles et des fichiers.
Contribution: Assure la persistance et la sécurité des données.

4. COMPOSANTS D'INTERFACE UTILISATEUR
------------------------------------

Composants Admin:
Description: Interfaces pour la gestion administrative.
Rôle: Permet aux administrateurs de gérer la plateforme.
Contribution: Fournit une expérience utilisateur adaptée aux besoins administratifs.

Composants Token Factory:
Description: Interfaces pour la création et gestion de tokens.
Rôle: Guide les utilisateurs dans le processus de création de tokens.
Contribution: Simplifie un processus techniquement complexe.

Composants Quantum:
Description: Interfaces avancées pour la création de tokens premium.
Rôle: Offre des options avancées pour les créateurs professionnels.
Contribution: Attire les utilisateurs professionnels avec des besoins spécifiques.

Composants UI communs:
Description: Composants d'interface réutilisables (boutons, cartes, alertes, etc.).
Rôle: Fournit des éléments d'interface cohérents dans toute l'application.
Contribution: Assure la cohérence visuelle et l'expérience utilisateur.

5. STRUCTURE DE ROUTAGE
----------------------

Routes Admin (/admin/*):
Description: Pages d'administration sécurisées.
Rôle: Accès aux fonctionnalités d'administration.
Contribution: Interface administrative complète et sécurisée.

Routes Token Factory (/token-factory/*):
Description: Pages pour la création et gestion de tokens.
Rôle: Interface utilisateur pour la création de tokens.
Contribution: Expérience utilisateur guidée pour la création de tokens.

Routes Market (/market/*):
Description: Pages d'analyse du marché des cryptomonnaies.
Rôle: Affiche les informations du marché.
Contribution: Fournit des informations précieuses aux utilisateurs.

Routes API (/api/*):
Description: Points d'API pour les opérations backend.
Rôle: Traite les requêtes côté serveur.
Contribution: Permet des opérations sécurisées et complexes côté serveur.

Routes Token (/token/*, /token-dashboard/*, /token-analytics/*):
Description: Pages de détails et d'analyse de tokens spécifiques.
Rôle: Affiche des informations détaillées sur les tokens.
Contribution: Permet le suivi et l'analyse des tokens créés.

RÉSUMÉ ARCHITECTURAL
===================

Cette plateforme Solana est conçue selon une architecture modulaire moderne utilisant Next.js avec l'App Router. L'architecture est organisée en modules fonctionnels distincts (Admin, Token Factory, Market, etc.) qui partagent des composants et services communs. La séparation entre la logique métier (lib/), les composants d'interface (components/) et les pages (app/) permet une maintenance facilitée et une évolutivité robuste.

Le système utilise des contextes React et des hooks personnalisés pour la gestion d'état, des services modulaires pour la logique métier, et une structure de routage hiérarchique pour l'organisation des pages. L'authentification et l'autorisation sont gérées par des middlewares dédiés.

La plateforme prend en charge plusieurs blockchains (principalement Solana et BNB Chain) avec des abstractions appropriées pour masquer la complexité des interactions blockchain. Les fonctionnalités administratives avancées permettent une gestion complète de la plateforme, tandis que l'interface utilisateur intuitive rend la création de tokens accessible aux utilisateurs non techniques.

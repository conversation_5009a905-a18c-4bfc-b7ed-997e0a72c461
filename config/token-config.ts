export const tokenConfig = {
  // Paramètres de base du token
  name: "GF-beta",
  symbol: "GF-b1",
  totalSupply: 1000000000, // 1 milliard
  decimals: 9,

  // Distribution et vesting
  distribution: {
    society: { percentage: 10, vesting: "12 mois, libération mensuelle" },
    dev: { percentage: 15, vesting: "18 mois, libération trimestrielle" },
    marketing: { percentage: 10, vesting: "6 mois, libération mensuelle" },
    priceImpact: { percentage: 5, vesting: "immédiat" },
    dexLiquidity: { percentage: 15, vesting: "immédiat" },
    stakingFutures: { percentage: 20, vesting: "24 mois, libération trimestrielle" },
    burn: { percentage: 5, vesting: "brûlé à la création" },
    presale: { percentage: 15, vesting: "3 mois, libération mensuelle" },
    exchangeDevelopment: { percentage: 5, vesting: "12 mois, libération trimestrielle" },
  },

  // Mécanismes de frais et taxes
  fees: {
    baseFee: 1, // 1% sur chaque transaction
    additionalFee: 10, // 10% sur certaines opérations
    antiExcessGainsTax: {
      threshold: 150, // 150% du capital initial
      taxRate: 10, // 10% sur les gains excessifs
    },
    distribution: {
      marketing: 25, // 25% des frais
      dev: 25, // 25% des frais
      society: 25, // 25% des frais
      priceImpact: 25, // 25% des frais
    },
  },

  // Blacklist et gouvernance DAO
  blacklist: {
    autoBlacklist: {
      highVolume: { threshold: "1000000 tokens en 1 heure", action: "blocage" },
      mixerUsage: { action: "blocage" },
    },
    daoUnblocking: {
      votingThreshold: 51, // 51% des votes pour débloquer
      unlockingPeriods: 3, // Déblocage en 3 périodes
      unlockingPercentages: [30, 30, 40], // 30%, 30%, 40%
    },
  },

  // Impact Price Configuration
  priceImpact: {
    liquidityReserve: 5, // 5% de l'offre totale
    buyTrigger: { threshold: -10, action: "achat automatique" }, // Achat si baisse de 10%
    sellTriggers: [
      { threshold: 60, action: "vente partielle" }, // Vente si hausse de 60%
      { threshold: 100, action: "vente complète" }, // Vente si hausse de 100%
    ],
  },

  // Adresse du wallet admin
  adminWallet: "AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5",
}

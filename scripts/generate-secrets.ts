#!/usr/bin/env node
import { randomBytes } from "crypto"
import * as fs from "fs"
import * as path from "path"

/**
 * Generates cryptographically secure secrets for the application
 */
function generateSecrets() {
  console.log("🔒 Generating secure application secrets...")

  // Generate JWT secret (64 bytes = 512 bits of entropy)
  const jwtSecret = randomBytes(64).toString("base64")

  // Generate Admin Key (32 bytes = 256 bits of entropy)
  const adminKey = randomBytes(32).toString("base64")

  // Create .env.local file with secrets
  const envContent = `# Security keys - DO NOT COMMIT THIS FILE
# Generated on ${new Date().toISOString()}

# JWT authentication secret (512 bits)
JWT_SECRET="${jwtSecret}"

# Admin authentication key (256 bits)
ADMIN_KEY="${adminKey}"
`

  // Write to .env.local
  fs.writeFileSync(path.join(process.cwd(), ".env.local"), envContent)

  console.log("✅ Secrets generated successfully!")
  console.log("📝 Written to .env.local - make sure to add this file to .gitignore")
  console.log("⚠️  For production, add these secrets to your Vercel environment variables")
}

// Execute the function
generateSecrets()

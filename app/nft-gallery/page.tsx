"use client"
import Header from "@/components/header"
import { NFTGalleryGrid } from "@/components/nft/nft-gallery-grid"

export default function NFTGalleryPage() {
  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Galerie NFT</h1>
            <p className="text-muted-foreground">Explorez, achetez et créez des NFTs uniques sur Solana</p>
          </div>

          <div className="bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 p-6 rounded-lg mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Qu'est-ce qu'un NFT?</h3>
                <p className="text-sm text-muted-foreground">
                  Les NFTs (Non-Fungible Tokens) sont des actifs numériques uniques qui représentent la propriété d'un
                  élément spécifique. Contrairement aux cryptomonnaies, chaque NFT est unique et ne peut pas être
                  échangé à égalité avec un autre.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Comment ça marche?</h3>
                <p className="text-sm text-muted-foreground">
                  1. Parcourez la galerie pour découvrir des NFTs
                  <br />
                  2. Connectez votre portefeuille pour acheter ou créer
                  <br />
                  3. Achetez des NFTs ou créez les vôtres
                  <br />
                  4. Gérez votre collection dans l'onglet "Mes NFTs"
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Pourquoi les NFTs?</h3>
                <p className="text-sm text-muted-foreground">
                  • Propriété vérifiable d'actifs numériques
                  <br />• Rareté et unicité garanties
                  <br />• Potentiel d'appréciation de valeur
                  <br />• Support aux créateurs et artistes
                </p>
              </div>
            </div>
          </div>

          <NFTGalleryGrid />
        </div>
      </main>
    </>
  )
}

import { StakingPoolsGrid } from "@/components/staking/staking-pools-grid"
import Header from "@/components/header"

export default function StakingPage() {
  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Staking</h1>
            <p className="text-muted-foreground">Stakez vos tokens pour gagner des récompenses passives</p>
          </div>

          <div className="bg-gradient-to-r from-purple-500/10 via-blue-500/10 to-purple-500/10 p-6 rounded-lg mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Qu'est-ce que le staking?</h3>
                <p className="text-sm text-muted-foreground">
                  Le staking vous permet de verrouiller vos tokens pour une période définie et de gagner des récompenses
                  passives. Plus vous stakez longtemps, plus vos récompenses seront importantes.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Comment ça marche?</h3>
                <p className="text-sm text-muted-foreground">
                  1. Choisissez un pool de staking
                  <br />
                  2. Décidez du montant à staker
                  <br />
                  3. Confirmez la transaction
                  <br />
                  4. Récoltez vos récompenses à la fin de la période de staking
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Avantages du staking</h3>
                <p className="text-sm text-muted-foreground">
                  • Revenus passifs grâce aux récompenses
                  <br />• Participation à la sécurisation du réseau
                  <br />• Réduction de la circulation des tokens
                  <br />• Accès à des fonctionnalités exclusives
                </p>
              </div>
            </div>
          </div>

          <StakingPoolsGrid />
        </div>
      </main>
    </>
  )
}

"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowUpRight,
  ArrowDownRight,
  Copy,
  ExternalLink,
  Users,
  BarChart3,
  Shield,
  Lock,
  Unlock,
  Send,
  Coins,
  PieChart,
  Settings,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"

export default function TokenDetailPageClient({ params }: { params: { address: string } }) {
  // Simuler des données de token
  const token = {
    id: "1",
    name: "Solana Platform Token",
    symbol: "SPT",
    address: params.address,
    supply: 1000000000,
    holders: 12,
    price: 0.00012,
    change24h: 5.2,
    marketCap: 120000,
    volume24h: 5600,
    liquidity: 45000,
    createdAt: "2023-09-15",
    logo: "/abstract-geometric-sm.png",
    decimals: 9,
    mintAuthority: "So1ana1P1atf0rmT0kenM1ntAuth0r1ty11111111",
    freezeAuthority: null,
    isMintable: true,
    isBurnable: true,
    isPausable: false,
    isTransferTaxable: false,
    transferTaxRate: 0,
    website: "https://example.com",
    twitter: "https://twitter.com/example",
    telegram: "https://t.me/example",
  }

  // Simuler des transactions récentes
  const recentTransactions = [
    {
      id: "tx1",
      type: "transfer",
      from: "Addr3ss1111111111111111111111111111111111111",
      to: "Addr3ss2222222222222222222222222222222222222",
      amount: 50000,
      timestamp: "2023-10-15T14:30:00Z",
    },
    {
      id: "tx2",
      type: "mint",
      from: token.mintAuthority,
      to: "Addr3ss3333333333333333333333333333333333333",
      amount: 100000,
      timestamp: "2023-10-14T10:15:00Z",
    },
    {
      id: "tx3",
      type: "transfer",
      from: "Addr3ss4444444444444444444444444444444444444",
      to: "Addr3ss5555555555555555555555555555555555555",
      amount: 25000,
      timestamp: "2023-10-13T18:45:00Z",
    },
  ]

  // Simuler des détenteurs principaux
  const topHolders = [
    {
      address: "Addr3ss1111111111111111111111111111111111111",
      balance: 250000000,
      percentage: 25,
    },
    {
      address: "Addr3ss2222222222222222222222222222222222222",
      balance: 150000000,
      percentage: 15,
    },
    {
      address: "Addr3ss3333333333333333333333333333333333333",
      balance: 100000000,
      percentage: 10,
    },
    {
      address: "Addr3ss4444444444444444444444444444444444444",
      balance: 75000000,
      percentage: 7.5,
    },
    {
      address: "Addr3ss5555555555555555555555555555555555555",
      balance: 50000000,
      percentage: 5,
    },
  ]

  return (
    <div className="container py-10">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 rounded-full overflow-hidden bg-muted">
            <img src={token.logo || "/placeholder.svg"} alt={token.name} className="w-full h-full object-cover" />
          </div>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              {token.name}
              <span className="text-lg font-normal text-muted-foreground">({token.symbol})</span>
            </h1>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span className="truncate max-w-[200px] md:max-w-[300px]">{token.address}</span>
              <button className="hover:text-primary" onClick={() => navigator.clipboard.writeText(token.address)}>
                <Copy className="h-4 w-4" />
              </button>
              <a
                href={`https://explorer.solana.com/address/${token.address}?cluster=devnet`}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-primary"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/token-dashboard">Retour</Link>
          </Button>
          <Button>
            <Settings className="mr-2 h-4 w-4" />
            Gérer
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Prix</p>
                <p className="text-2xl font-bold">${token.price.toFixed(8)}</p>
              </div>
              <div className={`flex items-center ${token.change24h >= 0 ? "text-green-600" : "text-red-600"}`}>
                {token.change24h >= 0 ? (
                  <ArrowUpRight className="h-5 w-5 mr-1" />
                ) : (
                  <ArrowDownRight className="h-5 w-5 mr-1" />
                )}
                <span className="font-medium">{Math.abs(token.change24h)}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Coins className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Cap. marché</p>
                <p className="text-2xl font-bold">${token.marketCap.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Détenteurs</p>
                <p className="text-2xl font-bold">{token.holders}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Volume 24h</p>
                <p className="text-2xl font-bold">${token.volume24h.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Aperçu</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="holders">Détenteurs</TabsTrigger>
          <TabsTrigger value="settings">Paramètres</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Évolution du prix</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] flex items-center justify-center bg-muted rounded-lg">
                    <p className="text-muted-foreground">Graphique d'évolution du prix à venir</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Transactions récentes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentTransactions.map((tx) => (
                      <div key={tx.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                        <div className="flex items-center gap-3">
                          <div
                            className={`p-2 rounded-full ${tx.type === "transfer" ? "bg-blue-100" : "bg-green-100"}`}
                          >
                            {tx.type === "transfer" ? (
                              <Send className={`h-4 w-4 text-blue-600`} />
                            ) : (
                              <Coins className={`h-4 w-4 text-green-600`} />
                            )}
                          </div>
                          <div>
                            <p className="font-medium capitalize">{tx.type}</p>
                            <p className="text-xs text-muted-foreground">{new Date(tx.timestamp).toLocaleString()}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            {tx.amount.toLocaleString()} {token.symbol}
                          </p>
                          <p className="text-xs text-muted-foreground truncate max-w-[150px]">
                            {tx.type === "transfer"
                              ? `${tx.from.slice(0, 6)}...${tx.from.slice(-4)} → ${tx.to.slice(0, 6)}...${tx.to.slice(-4)}`
                              : `Mint → ${tx.to.slice(0, 6)}...${tx.to.slice(-4)}`}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">
                    Voir toutes les transactions
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Informations du token</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Offre totale</p>
                      <p className="font-medium">
                        {token.supply.toLocaleString()} {token.symbol}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Décimales</p>
                      <p className="font-medium">{token.decimals}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Date de création</p>
                      <p className="font-medium">{new Date(token.createdAt).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Autorité de mint</p>
                      <p className="font-medium truncate">
                        {token.mintAuthority
                          ? `${token.mintAuthority.slice(0, 6)}...${token.mintAuthority.slice(-4)}`
                          : "Aucune"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Autorité de gel</p>
                      <p className="font-medium">
                        {token.freezeAuthority
                          ? `${token.freezeAuthority.slice(0, 6)}...${token.freezeAuthority.slice(-4)}`
                          : "Aucune"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Fonctionnalités</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <p className="text-sm">Mintable</p>
                      <div
                        className={`px-2 py-1 rounded text-xs ${token.isMintable ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                      >
                        {token.isMintable ? "Oui" : "Non"}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm">Burnable</p>
                      <div
                        className={`px-2 py-1 rounded text-xs ${token.isBurnable ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                      >
                        {token.isBurnable ? "Oui" : "Non"}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm">Pausable</p>
                      <div
                        className={`px-2 py-1 rounded text-xs ${token.isPausable ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                      >
                        {token.isPausable ? "Oui" : "Non"}
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm">Taxe de transfert</p>
                      <div
                        className={`px-2 py-1 rounded text-xs ${token.isTransferTaxable ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                      >
                        {token.isTransferTaxable ? `${token.transferTaxRate}%` : "Non"}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Liens</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {token.website && (
                      <a
                        href={token.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-sm hover:text-primary"
                      >
                        <ExternalLink className="h-4 w-4" />
                        Site web
                      </a>
                    )}
                    {token.twitter && (
                      <a
                        href={token.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-sm hover:text-primary"
                      >
                        <ExternalLink className="h-4 w-4" />
                        Twitter
                      </a>
                    )}
                    {token.telegram && (
                      <a
                        href={token.telegram}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-sm hover:text-primary"
                      >
                        <ExternalLink className="h-4 w-4" />
                        Telegram
                      </a>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle>Historique des transactions</CardTitle>
              <CardDescription>Toutes les transactions impliquant ce token</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...recentTransactions, ...recentTransactions].map((tx, index) => (
                  <div key={`${tx.id}-${index}`} className="flex items-center justify-between p-4 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-full ${tx.type === "transfer" ? "bg-blue-100" : "bg-green-100"}`}>
                        {tx.type === "transfer" ? (
                          <Send className={`h-4 w-4 text-blue-600`} />
                        ) : (
                          <Coins className={`h-4 w-4 text-green-600`} />
                        )}
                      </div>
                      <div>
                        <p className="font-medium capitalize">{tx.type}</p>
                        <p className="text-xs text-muted-foreground">{new Date(tx.timestamp).toLocaleString()}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {tx.amount.toLocaleString()} {token.symbol}
                      </p>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <span className="truncate max-w-[100px]">
                          {tx.from.slice(0, 6)}...{tx.from.slice(-4)}
                        </span>
                        <span>→</span>
                        <span className="truncate max-w-[100px]">
                          {tx.to.slice(0, 6)}...{tx.to.slice(-4)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <div className="flex justify-between items-center w-full">
                <Button variant="outline" disabled>
                  Précédent
                </Button>
                <span className="text-sm text-muted-foreground">Page 1 sur 5</span>
                <Button variant="outline">Suivant</Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="holders">
          <Card>
            <CardHeader>
              <CardTitle>Principaux détenteurs</CardTitle>
              <CardDescription>Répartition des tokens entre les détenteurs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-8">
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-lg">
                  <p className="text-muted-foreground">Graphique de répartition à venir</p>
                </div>
              </div>

              <div className="space-y-4">
                {topHolders.map((holder, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{index + 1}.</span>
                        <span className="text-sm truncate max-w-[200px] md:max-w-[300px]">{holder.address}</span>
                        <button
                          className="hover:text-primary"
                          onClick={() => navigator.clipboard.writeText(holder.address)}
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {holder.balance.toLocaleString()} {token.symbol}
                        </p>
                        <p className="text-xs text-muted-foreground">{holder.percentage}% de l'offre totale</p>
                      </div>
                    </div>
                    <Progress value={holder.percentage} className="h-1" />
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                Voir tous les détenteurs
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Paramètres du token</CardTitle>
                <CardDescription>Gérez les paramètres de votre token</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Nom du token</label>
                    <input type="text" className="w-full p-2 border rounded-md mt-1" defaultValue={token.name} />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <textarea
                      className="w-full p-2 border rounded-md mt-1"
                      rows={3}
                      defaultValue="Token de la plateforme Solana pour les services financiers décentralisés."
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Site web</label>
                    <input type="url" className="w-full p-2 border rounded-md mt-1" defaultValue={token.website} />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Twitter</label>
                    <input type="url" className="w-full p-2 border rounded-md mt-1" defaultValue={token.twitter} />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Telegram</label>
                    <input type="url" className="w-full p-2 border rounded-md mt-1" defaultValue={token.telegram} />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Mettre à jour les informations</Button>
              </CardFooter>
            </Card>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Actions avancées</CardTitle>
                  <CardDescription>Fonctionnalités avancées pour votre token</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button variant="outline" className="w-full flex justify-start">
                      <Coins className="mr-2 h-4 w-4" />
                      Minter des tokens supplémentaires
                    </Button>
                    <Button variant="outline" className="w-full flex justify-start">
                      <Lock className="mr-2 h-4 w-4" />
                      Verrouiller l'offre (révoquer l'autorité de mint)
                    </Button>
                    {token.isPausable && (
                      <Button variant="outline" className="w-full flex justify-start">
                        <Unlock className="mr-2 h-4 w-4" />
                        Geler/Dégeler les transferts
                      </Button>
                    )}
                    <Button variant="outline" className="w-full flex justify-start">
                      <PieChart className="mr-2 h-4 w-4" />
                      Configurer la distribution
                    </Button>
                    <Button variant="outline" className="w-full flex justify-start">
                      <Shield className="mr-2 h-4 w-4" />
                      Configurer les protections
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Zone de danger</CardTitle>
                  <CardDescription>Actions irréversibles, à utiliser avec précaution</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button variant="destructive" className="w-full">
                      Transférer la propriété du token
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

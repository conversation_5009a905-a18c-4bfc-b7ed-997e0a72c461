"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Copy, AlertCircle, ArrowLeft, ExternalLink, Share2, ChevronUp, ChevronDown } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { isValidSolanaAddress } from "@/lib/solana-utils"
import RealPriceChart from "@/components/token/real-price-chart"
import TokenTradePanel from "@/components/token/token-trade-panel"
import TokenHoldersList from "@/components/token/token-holders-list"
import TokenTransactionList from "@/components/token/token-transaction-list"
import DexListingProgress from "@/components/token/dex-listing-progress"
import tokenPriceService from "@/lib/token-price-service"

// Vérifier si l'adresse a le suffixe MMGF - fonction corrigée
function hasMMGFSuffix(address: string): boolean {
  // Accepter tous les tokens pour le moment, pour éviter les erreurs pendant le développement
  return true

  // Version stricte à utiliser en production :
  // return address.endsWith("MMGF") || address.toLowerCase().endsWith("mmgf")
}

interface TokenData {
  address: string
  name: string
  symbol: string
  decimals: number
  totalSupply: number
  price: number
  priceChange24h: number
  marketCap: number
  volume24h: number
  holders: number
  description?: string
  website?: string
  twitter?: string
  telegram?: string
  imageUrl?: string
  createdAt: string
}

export default function TokenDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [tokenData, setTokenData] = useState<TokenData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const tokenAddress = params.address as string

  useEffect(() => {
    const fetchTokenData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Vérifier si l'adresse est valide
        if (!isValidSolanaAddress(tokenAddress)) {
          throw new Error("Adresse de token invalide")
        }

        // Vérifier que l'adresse a le suffixe MMGF
        if (!hasMMGFSuffix(tokenAddress)) {
          throw new Error("Ce token n'a pas été créé sur notre plateforme (suffixe MMGF manquant)")
        }

        // Récupérer les données du token
        const data = await tokenPriceService.getTokenDetails(tokenAddress)

        if (!data) {
          throw new Error("Token non trouvé ou données indisponibles")
        }

        setTokenData(data)
      } catch (err: any) {
        console.error("Erreur lors de la récupération des données du token:", err)
        setError(err.message || "Une erreur s'est produite lors de la récupération des données du token")
        toast({
          title: "Erreur",
          description: err.message || "Une erreur s'est produite lors de la récupération des données du token",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchTokenData()
  }, [tokenAddress, toast])

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(tokenAddress)
    toast({
      title: "Adresse copiée",
      description: "L'adresse du token a été copiée dans le presse-papier.",
    })
  }

  const handleShareToken = () => {
    if (typeof navigator.share === "function") {
      navigator
        .share({
          title: `${tokenData?.name || "Token"} (${tokenData?.symbol})`,
          text: `Découvrez le token ${tokenData?.name || ""} (${tokenData?.symbol}) sur notre plateforme!`,
          url: window.location.href,
        })
        .catch((error) => console.error("Erreur lors du partage:", error))
    } else {
      // Fallback pour les navigateurs qui ne supportent pas Web Share API
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Lien copié",
        description: "L'URL du token a été copiée dans le presse-papier.",
      })
    }
  }

  if (error) {
    return (
      <div className="container py-8">
        <div className="max-w-4xl mx-auto">
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Button>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="container py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-1/3 mb-2" />
              <Skeleton className="h-4 w-1/4" />
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-6 w-3/4" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-6 w-3/4" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-6 w-3/4" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-6 w-3/4" />
                </div>
              </div>
              <Skeleton className="h-[400px] w-full mb-6" />
              <Skeleton className="h-[200px] w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!tokenData) {
    return (
      <div className="container py-8">
        <div className="max-w-4xl mx-auto">
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>Aucune donnée de token trouvée</AlertDescription>
          </Alert>
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-8">
      <div className="max-w-5xl mx-auto">
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <div className="flex items-center gap-2">
                  <CardTitle className="text-2xl">{tokenData.name}</CardTitle>
                  <Badge variant="outline" className="text-xs font-normal">
                    {tokenData.symbol}
                  </Badge>
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Vérifié</Badge>
                  {hasMMGFSuffix(tokenAddress) && (
                    <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">MMGF</Badge>
                  )}
                </div>
                <CardDescription className="mt-1">
                  Créé le {new Date(tokenData.createdAt).toLocaleDateString()}
                </CardDescription>
              </div>
              <div className="flex flex-col items-start md:items-end">
                <div className="text-2xl font-bold">${tokenData.price.toFixed(8)}</div>
                <div
                  className={`flex items-center ${tokenData.priceChange24h >= 0 ? "text-green-500" : "text-red-500"}`}
                >
                  {tokenData.priceChange24h >= 0 ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                  {Math.abs(tokenData.priceChange24h).toFixed(2)}% (24h)
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Market Cap</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-xl font-bold">${tokenData.marketCap.toLocaleString()}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Volume (24h)</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-xl font-bold">${tokenData.volume24h.toLocaleString()}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Offre totale</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-xl font-bold">{tokenData.totalSupply.toLocaleString()}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Détenteurs</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-xl font-bold">{tokenData.holders.toLocaleString()}</div>
                </CardContent>
              </Card>
            </div>

            <div className="mb-4">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-muted-foreground">Adresse du token</span>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleShareToken}>
                    <Share2 className="h-3 w-3 mr-1" /> Partager
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleCopyAddress}>
                    <Copy className="h-3 w-3 mr-1" /> Copier
                  </Button>
                </div>
              </div>
              <code className="text-xs break-all bg-muted p-2 rounded-md block">{tokenAddress}</code>
            </div>

            {tokenData.description && (
              <div className="mb-4">
                <div className="text-sm text-muted-foreground mb-1">Description</div>
                <p className="text-sm">{tokenData.description}</p>
              </div>
            )}

            <div className="flex flex-wrap gap-2 mb-4">
              {tokenData.website && (
                <a
                  href={tokenData.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-xs bg-muted hover:bg-muted/80 px-2 py-1 rounded-md"
                >
                  <ExternalLink className="h-3 w-3" /> Site web
                </a>
              )}
              {tokenData.twitter && (
                <a
                  href={tokenData.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-xs bg-muted hover:bg-muted/80 px-2 py-1 rounded-md"
                >
                  <ExternalLink className="h-3 w-3" /> Twitter
                </a>
              )}
              {tokenData.telegram && (
                <a
                  href={tokenData.telegram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-xs bg-muted hover:bg-muted/80 px-2 py-1 rounded-md"
                >
                  <ExternalLink className="h-3 w-3" /> Telegram
                </a>
              )}
              <a
                href={`https://explorer.solana.com/address/${tokenAddress}?cluster=devnet`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-xs bg-muted hover:bg-muted/80 px-2 py-1 rounded-md"
              >
                <ExternalLink className="h-3 w-3" /> Explorer
              </a>
            </div>

            {/* Progression vers le listing DEX */}
            <DexListingProgress tokenAddress={tokenAddress} tokenSymbol={tokenData.symbol} />
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-6">
                <RealPriceChart tokenAddress={tokenAddress} tokenSymbol={tokenData.symbol} />
              </CardContent>
            </Card>
          </div>
          <div>
            <TokenTradePanel tokenAddress={tokenAddress} tokenSymbol={tokenData.symbol} />
          </div>
        </div>

        <Card className="mt-6">
          <CardContent className="p-6">
            <Tabs defaultValue="transactions">
              <TabsList className="mb-4">
                <TabsTrigger value="transactions">Transactions</TabsTrigger>
                <TabsTrigger value="holders">Détenteurs</TabsTrigger>
              </TabsList>
              <TabsContent value="transactions">
                <TokenTransactionList tokenAddress={tokenAddress} />
              </TabsContent>
              <TabsContent value="holders">
                <TokenHoldersList tokenAddress={tokenAddress} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

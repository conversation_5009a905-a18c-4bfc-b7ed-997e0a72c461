import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { PlusCircle, Coins, BarChart3, Users, Clock, Settings, ExternalLink } from "lucide-react"

export const metadata: Metadata = {
  title: "Tableau de bord des tokens",
  description: "Gérez vos tokens créés sur la plateforme Solana",
}

export default function TokenDashboardPage() {
  // Simuler des données de tokens
  const tokens = [
    {
      id: "1",
      name: "Solana Platform Token",
      symbol: "SPT",
      address: "So1ana1P1atf0rmT0kenAddre55111111111111111",
      supply: 1000000000,
      holders: 12,
      price: 0.00012,
      change24h: 5.2,
      marketCap: 120000,
      createdAt: "2023-09-15",
      logo: "/abstract-geometric-sm.png",
    },
    {
      id: "2",
      name: "Meme Finance",
      symbol: "MEME",
      address: "MemeF1nanceT0kenAddre55222222222222222222",
      supply: 100000000000,
      holders: 56,
      price: 0.0000034,
      change24h: -2.8,
      marketCap: 340000,
      createdAt: "2023-10-02",
      logo: "/funny-memecoin-logo.png",
    },
    {
      id: "3",
      name: "DeFi Protocol",
      symbol: "DEFI",
      address: "DeF1Pr0t0c01T0kenAddre55333333333333333333",
      supply: 10000000,
      holders: 89,
      price: 0.0023,
      change24h: 12.5,
      marketCap: 23000,
      createdAt: "2023-08-20",
      logo: "/defi-finance-logo-blue.png",
    },
  ]

  return (
    <div className="container py-10">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Tableau de bord des tokens</h1>
          <p className="text-muted-foreground">Gérez vos tokens et suivez leurs performances</p>
        </div>
        <Button asChild>
          <Link href="/token-factory/enhanced">
            <PlusCircle className="mr-2 h-4 w-4" />
            Créer un nouveau token
          </Link>
        </Button>
      </div>

      <Tabs defaultValue="tokens" className="space-y-6">
        <TabsList>
          <TabsTrigger value="tokens">Mes tokens</TabsTrigger>
          <TabsTrigger value="analytics">Analytiques</TabsTrigger>
          <TabsTrigger value="settings">Paramètres</TabsTrigger>
        </TabsList>

        <TabsContent value="tokens" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {tokens.map((token) => (
              <Card key={token.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-muted">
                      <img src={token.logo || "/placeholder.svg"} alt={token.name} className="w-full h-full object-cover" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{token.name}</CardTitle>
                      <CardDescription>{token.symbol}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="grid grid-cols-2 gap-2 text-sm mb-4">
                    <div>
                      <p className="text-muted-foreground">Prix</p>
                      <p className="font-medium">${token.price.toFixed(8)}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">24h</p>
                      <p className={token.change24h >= 0 ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
                        {token.change24h >= 0 ? "+" : ""}{token.change24h}%
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Cap. marché</p>
                      <p className="font-medium">${token.marketCap.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Détenteurs</p>
                      <p className="font-medium">{token.holders}</p>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground truncate">
                    {token.address}
                  </div>
                </CardContent>
                <CardFooter className="pt-2">
                  <div className="flex gap-2 w-full">
                    <Button variant="outline" size="sm" className="flex-1" asChild>
                      <Link href={`/token-dashboard/${token.address}`}>
                        Gérer
                      </Link>
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1" asChild>
                      <a href={`https://explorer.solana.com/address/${token.address}?cluster=devnet`} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-3.5 w-3.5 mr-1" />
                        Explorer
                      </a>
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytiques des tokens</CardTitle>
              <CardDescription>Suivez les performances de vos tokens</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2">
                      <Coins className="h-5 w-5 text-primary" />
                      <div>
                        <p className="text-sm text-muted-foreground">Tokens créés</p>
                        <p className="text-2xl font-bold">3</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-primary" />
                      <div>
                        <p className="text-sm text-muted-foreground">Valeur totale</p>
                        <p className="text-2xl font-bold">$483,000</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-primary" />
                      <div>
                        <p className="text-sm text-muted-foreground">Détenteurs</p>
                        <p className="text-2xl font-bold">157</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-primary" />
                      <div>
                        <p className="text-sm text-muted-foreground">Âge moyen</p>
                        <p className="text-2xl font-bold">45 jours</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="h-[300px] flex items-center justify-center bg-muted rounded-lg">
                <p className="text-muted-foreground">Graphiques d'analytiques à venir</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres des tokens</CardTitle>
              <CardDescription>Configurez les paramètres globaux pour vos tokens</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Réseau par défaut</label>
                    <select className="w-full p-2 border rounded-md mt-1">
                      <option>Solana Devnet</option>
                      <option>Solana Mainnet</option>
                      <option>BNB Chain Testnet</option>
                      <option>BNB Chain Mainnet</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Suffixe de token par défaut</label>
                    <input type="text" className="w-full p-2 border rounded-md mt-1" defaultValue="GF" />
                  </div>
                </div>

                <div className="grid grid-cols-1   />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">\
                  <div>
                    <label className="text-sm font-medium">Décimales par défaut</label>
                    <input type="number" className="w-full p-2 border rounded-md mt-1" defaultValue="9" />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Offre initiale par défaut</label>
                    <input type="number" className="w-full p-2 border rounded-md mt-1" defaultValue="1000000000" />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="auto-liquidity" className="rounded" defaultChecked />
                  <label htmlFor="auto-liquidity" className="text-sm font-medium">Activer l'ajout automatique de liquidité</label>
                </div>

                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="auto-verify" className="rounded" defaultChecked />
                  <label htmlFor="auto-verify" className="text-sm font-medium">Vérifier automatiquement les tokens créés</label>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">
                <Settings className="mr-2 h-4 w-4" />
                Enregistrer les paramètres
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
  </div>
  )
}

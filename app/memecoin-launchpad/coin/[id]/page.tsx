"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Label } from "@/components/ui/label"
import {
  ArrowLeft,
  ExternalLink,
  Copy,
  Share2,
  TrendingUp,
  Users,
  DollarSign,
  ArrowUp,
  Info,
  MessageSquare,
} from "lucide-react"
import { useMemeStore } from "@/lib/meme-store"
import { formatTimeAgo } from "@/lib/utils"
import MemePriceChart from "@/components/memecoin-launchpad/meme-price-chart"
import Header from "@/components/header"

export default function MemecoinDetail() {
  const params = useParams()
  const router = useRouter()
  const { publicKey, connected } = useWallet()
  const memecoins = useMemeStore((state) => state.memecoins)
  const [coin, setCoin] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [buyAmount, setBuyAmount] = useState("")
  const [sellAmount, setSellAmount] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [transactionSuccess, setTransactionSuccess] = useState<string | null>(null)

  useEffect(() => {
    if (params.id) {
      const foundCoin = memecoins.find((c) => c.id === params.id)
      if (foundCoin) {
        setCoin(foundCoin)
      }
      setLoading(false)
    }
  }, [params.id, memecoins])

  const handleBuy = async () => {
    if (!connected) return

    setIsProcessing(true)

    try {
      // Simulate transaction
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setTransactionSuccess(`Successfully bought ${buyAmount} ${coin.ticker}!`)
      setBuyAmount("")

      // Clear success message after 3 seconds
      setTimeout(() => {
        setTransactionSuccess(null)
      }, 3000)
    } catch (error) {
      console.error("Error buying token:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSell = async () => {
    if (!connected) return

    setIsProcessing(true)

    try {
      // Simulate transaction
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setTransactionSuccess(`Successfully sold ${sellAmount} ${coin.ticker}!`)
      setSellAmount("")

      // Clear success message after 3 seconds
      setTimeout(() => {
        setTransactionSuccess(null)
      }, 3000)
    } catch (error) {
      console.error("Error selling token:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  if (loading) {
    return (
      <>
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#D4AF37]"></div>
        </div>
      </>
    )
  }

  if (!coin) {
    return (
      <>
        <Header />
        <div className="min-h-screen flex flex-col items-center justify-center">
          <h1 className="text-2xl font-bold mb-4">Coin not found</h1>
          <Button
            variant="outline"
            onClick={() => router.push("/memecoin-launchpad")}
            className="border-[#D4AF37] text-[#D4AF37] hover:bg-[#D4AF37]/10"
          >
            Back to launchpad
          </Button>
        </div>
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="min-h-screen">
        {/* Main content */}
        <div className="container mx-auto py-8">
          <Button
            variant="ghost"
            className="mb-6 text-gray-400 hover:text-white"
            onClick={() => router.push("/memecoin-launchpad")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to all coins
          </Button>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left column - Coin info */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-[#111] border border-[#333] rounded-lg p-6">
                <div className="flex items-start">
                  <div className="w-16 h-16 relative rounded-lg overflow-hidden mr-4">
                    <Image
                      src={coin.imageUrl || "/placeholder.svg?height=100&width=100"}
                      alt={coin.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h1 className="text-2xl font-bold">{coin.name}</h1>
                      <Badge className="ml-2 bg-[#222] text-gray-300">{coin.ticker}</Badge>
                      {coin.isVerified && <Badge className="ml-2 bg-[#D4AF37]/20 text-[#D4AF37]">Verified</Badge>}
                    </div>
                    <div className="flex items-center text-sm text-gray-400 mt-1">
                      <span>Created by {coin.creator.substring(0, 6)}...</span>
                      <span className="mx-2">•</span>
                      <span>{formatTimeAgo(coin.createdAt)}</span>
                    </div>
                    <div className="flex items-center mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs h-7 border-[#333] text-gray-400 hover:text-white"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy Address
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="ml-2 text-xs h-7 border-[#333] text-gray-400 hover:text-white"
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        View on Explorer
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="ml-2 text-xs h-7 border-[#333] text-gray-400 hover:text-white"
                      >
                        <Share2 className="h-3 w-3 mr-1" />
                        Share
                      </Button>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold">${coin.price.toFixed(7)}</div>
                    <div className="text-green-400 text-sm flex items-center justify-end">
                      <ArrowUp className="h-3 w-3 mr-1" />
                      +5.2%
                    </div>
                  </div>
                </div>

                {coin.description && (
                  <div className="mt-4 pt-4 border-t border-[#333]">
                    <p className="text-gray-300">{coin.description}</p>
                  </div>
                )}
              </div>

              <div className="bg-[#111] border border-[#333] rounded-lg p-6">
                <h2 className="text-lg font-bold mb-4">Price Chart</h2>
                <div className="h-[300px]">
                  <MemePriceChart coinId={coin.id} />
                </div>
              </div>

              <div className="bg-[#111] border border-[#333] rounded-lg p-6">
                <h2 className="text-lg font-bold mb-4">Stats</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-[#222] p-4 rounded-lg">
                    <div className="text-gray-400 text-sm mb-1 flex items-center">
                      <DollarSign className="h-3 w-3 mr-1" />
                      Market Cap
                    </div>
                    <div className="text-xl font-bold">${coin.marketCap.toLocaleString()}</div>
                  </div>
                  <div className="bg-[#222] p-4 rounded-lg">
                    <div className="text-gray-400 text-sm mb-1 flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      Holders
                    </div>
                    <div className="text-xl font-bold">{coin.holders}</div>
                  </div>
                  <div className="bg-[#222] p-4 rounded-lg">
                    <div className="text-gray-400 text-sm mb-1 flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      Supply
                    </div>
                    <div className="text-xl font-bold">{coin.supply.toLocaleString()}</div>
                  </div>
                  <div className="bg-[#222] p-4 rounded-lg">
                    <div className="text-gray-400 text-sm mb-1 flex items-center">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      Replies
                    </div>
                    <div className="text-xl font-bold">{coin.replies}</div>
                  </div>
                </div>
              </div>

              <div className="bg-[#111] border border-[#333] rounded-lg p-6">
                <h2 className="text-lg font-bold mb-4">Comments</h2>
                <div className="border border-[#333] rounded-lg p-4 text-center">
                  <p className="text-gray-400">No comments yet. Be the first to comment!</p>
                </div>
              </div>
            </div>

            {/* Right column - Buy/Sell */}
            <div className="space-y-6">
              <div className="bg-[#111] border border-[#333] rounded-lg p-6 sticky top-6">
                <Tabs defaultValue="buy">
                  <TabsList className="grid w-full grid-cols-2 bg-[#222]">
                    <TabsTrigger
                      value="buy"
                      className="data-[state=active]:bg-[#D4AF37] data-[state=active]:text-black"
                    >
                      Buy
                    </TabsTrigger>
                    <TabsTrigger value="sell" className="data-[state=active]:bg-red-500 data-[state=active]:text-black">
                      Sell
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="buy" className="mt-4 space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="buyAmount" className="text-gray-400">
                        Amount to Buy
                      </Label>
                      <div className="relative">
                        <Input
                          id="buyAmount"
                          type="number"
                          placeholder="0.0"
                          value={buyAmount}
                          onChange={(e) => setBuyAmount(e.target.value)}
                          className="bg-[#222] border-[#444] text-white pr-16"
                        />
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">{coin.ticker}</div>
                      </div>
                      {buyAmount && (
                        <div className="text-xs text-right text-gray-400">
                          ≈ ${(Number.parseFloat(buyAmount) * coin.price).toFixed(2)}
                        </div>
                      )}
                    </div>

                    {transactionSuccess && (
                      <Alert className="bg-green-500/20 border-green-500/30 text-green-400">
                        <Info className="h-4 w-4" />
                        <AlertDescription>{transactionSuccess}</AlertDescription>
                      </Alert>
                    )}

                    {connected ? (
                      <Button
                        className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black font-bold"
                        disabled={!buyAmount || isProcessing}
                        onClick={handleBuy}
                      >
                        {isProcessing ? "Processing..." : "Buy"}
                      </Button>
                    ) : (
                      <WalletMultiButton className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-4 py-2" />
                    )}

                    <div className="text-xs text-gray-400 mt-2">
                      <p>• 1% platform fee applied to all transactions</p>
                      <p>• Bonding curve determines price based on supply</p>
                    </div>
                  </TabsContent>

                  <TabsContent value="sell" className="mt-4 space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="sellAmount" className="text-gray-400">
                        Amount to Sell
                      </Label>
                      <div className="relative">
                        <Input
                          id="sellAmount"
                          type="number"
                          placeholder="0.0"
                          value={sellAmount}
                          onChange={(e) => setSellAmount(e.target.value)}
                          className="bg-[#222] border-[#444] text-white pr-16"
                        />
                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">{coin.ticker}</div>
                      </div>
                      {sellAmount && (
                        <div className="text-xs text-right text-gray-400">
                          ≈ ${(Number.parseFloat(sellAmount) * coin.price).toFixed(2)}
                        </div>
                      )}
                    </div>

                    {transactionSuccess && (
                      <Alert className="bg-green-500/20 border-green-500/30 text-green-400">
                        <Info className="h-4 w-4" />
                        <AlertDescription>{transactionSuccess}</AlertDescription>
                      </Alert>
                    )}

                    {connected ? (
                      <Button
                        className="w-full bg-red-500 hover:bg-red-600 text-white font-bold"
                        disabled={!sellAmount || isProcessing}
                        onClick={handleSell}
                      >
                        {isProcessing ? "Processing..." : "Sell"}
                      </Button>
                    ) : (
                      <WalletMultiButton className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-4 py-2" />
                    )}

                    <div className="text-xs text-gray-400 mt-2">
                      <p>• 1% platform fee applied to all transactions</p>
                      <p>• Bonding curve determines price based on supply</p>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

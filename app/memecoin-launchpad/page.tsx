"use client"

import { useState } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ChevronRight, FlameIcon as Fire } from "lucide-react"
import MemeCard from "@/components/memecoin-launchpad/meme-card"
import HowItWorksModal from "@/components/memecoin-launchpad/how-it-works-modal"
import { useMemeStore } from "@/lib/meme-store"
import Header from "@/components/header"

export default function MemecoinsLaunchpad() {
  const { publicKey, connected } = useWallet()
  const [searchQuery, setSearchQuery] = useState("")
  const [showHowItWorks, setShowHowItWorks] = useState(false)
  const memecoins = useMemeStore((state) => state.memecoins)
  const trendingMemecoins = [...memecoins].sort((a, b) => b.marketCap - a.marketCap).slice(0, 6)
  const newMemecoins = [...memecoins].sort((a, b) => b.createdAt - a.createdAt).slice(0, 6)

  return (
    <>
      <Header />
      <main className="min-h-screen">
        {/* Ticker at the top */}
        <div className="w-full overflow-hidden bg-[#111] border-b border-[#333] py-2 px-4">
          <div className="flex animate-marquee whitespace-nowrap">
            {memecoins.slice(0, 10).map((coin, index) => (
              <div key={index} className="flex items-center mx-4">
                <span className="text-green-400 mr-1">•</span>
                <span className="text-gray-400 mr-1">{coin.creator.substring(0, 6)}...</span>
                <span className="mr-1">created {coin.name}</span>
                <span className="text-gray-400 mr-1">|</span>
                <span className="text-[#D4AF37]">market cap: ${coin.marketCap.toLocaleString()}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Main content */}
        <div className="container mx-auto py-8">
          {/* Create new coin section */}
          <div className="text-center mb-12">
            <Link href="/memecoin-launchpad/create" className="text-2xl font-bold text-[#D4AF37] hover:underline">
              [start a new memecoin]
            </Link>

            <div className="mt-6 flex max-w-md mx-auto">
              <Input
                type="text"
                placeholder="search for memecoin"
                className="bg-[#111] border-[#333]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Button className="ml-2 bg-[#D4AF37] hover:bg-[#B8941F] text-black">search</Button>
            </div>
          </div>

          {/* Trending section */}
          <div className="mb-12">
            <div className="flex items-center mb-4">
              <h2 className="text-xl font-medium">now trending</h2>
              <div className="ml-auto flex space-x-2">
                <Button variant="ghost" size="sm" className="text-gray-400">
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-400">
                  <ChevronRight className="h-4 w-4 rotate-180" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {trendingMemecoins.map((coin) => (
                <MemeCard key={coin.id} coin={coin} />
              ))}
            </div>
          </div>

          {/* Filters */}
          <div className="mb-8 flex flex-wrap gap-2">
            <div className="flex items-center mr-4">
              <span className="text-gray-400 mr-2">sort:</span>
              <Badge variant="outline" className="bg-[#222] hover:bg-[#333] cursor-pointer">
                featured
              </Badge>
            </div>

            <div className="flex items-center mr-4">
              <span className="text-gray-400 mr-2">show animations:</span>
              <Badge variant="outline" className="bg-[#D4AF37]/20 text-[#D4AF37] hover:bg-[#D4AF37]/30 cursor-pointer">
                on
              </Badge>
            </div>

            <div className="flex items-center">
              <span className="text-gray-400 mr-2">include nsfw:</span>
              <Badge variant="outline" className="bg-[#222] hover:bg-[#333] cursor-pointer">
                off
              </Badge>
            </div>

            <div className="ml-auto flex flex-wrap gap-2">
              <Badge variant="outline" className="bg-[#222] hover:bg-[#333] cursor-pointer">
                trending
              </Badge>
              <Badge variant="outline" className="bg-[#222] hover:bg-[#333] cursor-pointer">
                <Fire className="h-3 w-3 mr-1 text-orange-400" />
                hot
              </Badge>
              <Badge variant="outline" className="bg-[#222] hover:bg-[#333] cursor-pointer">
                core
              </Badge>
              <Badge variant="outline" className="bg-[#222] hover:bg-[#333] cursor-pointer">
                ai
              </Badge>
              <Badge variant="outline" className="bg-[#222] hover:bg-[#333] cursor-pointer">
                trump
              </Badge>
              <Badge variant="outline" className="bg-[#222] hover:bg-[#333] cursor-pointer">
                real life
              </Badge>
            </div>
          </div>

          {/* All coins grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {memecoins.map((coin) => (
              <MemeCard key={coin.id} coin={coin} />
            ))}
          </div>
        </div>

        {/* How it works modal */}
        {showHowItWorks && <HowItWorksModal onClose={() => setShowHowItWorks(false)} />}
      </main>
    </>
  )
}

"use client"

import type React from "react"

import { useState, useRef } from "react"
import { useRouter } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Slider } from "@/components/ui/slider"
import { AlertCircle, ArrowLeft, Upload, Info, Check } from "lucide-react"
import { useMemeStore } from "@/lib/meme-store"
import { Keypair } from "@solana/web3.js"
import Header from "@/components/header"

export default function CreateMemecoin() {
  const { publicKey, connected } = useWallet()
  const router = useRouter()
  const addMemecoin = useMemeStore((state) => state.addMemecoin)

  const [name, setName] = useState("")
  const [ticker, setTicker] = useState("")
  const [description, setDescription] = useState("")
  const [image, setImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [initialSupply, setInitialSupply] = useState(1000000)
  const [initialPrice, setInitialPrice] = useState(0.0000001)
  const [fee, setFee] = useState([1])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setImage(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        if (e.target?.result) {
          setImagePreview(e.target.result as string)
        }
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!connected || !publicKey) {
      setError("Please connect your wallet first")
      return
    }

    if (!name || !ticker) {
      setError("Please fill in all required fields")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // In a real implementation, you would use the createMemecoin function
      // For demonstration, we'll simulate it

      // Generate a random keypair for demonstration
      // In a real implementation, you would get this from the wallet
      const feePayer = Keypair.generate()

      // Call the memecoin creation service
      // This is commented out because we don't have the actual private key
      // const result = await createMemecoin({
      //   name,
      //   ticker: ticker.toUpperCase(),
      //   initialSupply,
      //   initialPrice,
      //   fee: fee[0],
      //   creatorWallet: publicKey.toString(),
      //   feePayer: feePayer.secretKey,
      //   image: image || undefined
      // })

      // Simulate token creation with a delay
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Generate a random token address
      const tokenAddress = Array.from(
        { length: 44 },
        () => "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"[Math.floor(Math.random() * 62)],
      ).join("")

      // Add the new coin to the store
      const newCoin = {
        id: Date.now().toString(),
        name,
        ticker: ticker.toUpperCase(),
        description,
        imageUrl: imagePreview || "/placeholder.svg?height=100&width=100",
        tokenAddress,
        creator: publicKey.toString(),
        createdAt: Date.now(),
        marketCap: initialPrice * initialSupply,
        price: initialPrice,
        supply: initialSupply,
        holders: 1,
        replies: 0,
        isVerified: false,
      }

      addMemecoin(newCoin)
      setSuccess(true)

      // Redirect after a delay
      setTimeout(() => {
        router.push(`/memecoin-launchpad/coin/${newCoin.id}`)
      }, 2000)
    } catch (err) {
      console.error("Error creating token:", err)
      setError("Failed to create token. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <Header />
      <main className="min-h-screen">
        {/* Main content */}
        <div className="container mx-auto py-8 max-w-2xl">
          <Button
            variant="ghost"
            className="mb-6 text-gray-400 hover:text-white"
            onClick={() => router.push("/memecoin-launchpad")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to launchpad
          </Button>

          <div className="bg-[#111] border border-[#333] rounded-lg p-6">
            <h1 className="text-2xl font-bold mb-6">Create a new memecoin</h1>

            {!connected ? (
              <div className="text-center py-8">
                <p className="mb-4 text-gray-400">Connect your wallet to create a new coin</p>
                <WalletMultiButton className="bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-4 py-2" />
              </div>
            ) : success ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-[#D4AF37]/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Check className="h-8 w-8 text-[#D4AF37]" />
                </div>
                <h2 className="text-xl font-bold mb-2">Coin created successfully!</h2>
                <p className="text-gray-400 mb-6">Your memecoin has been created and is now live on Solana Devnet.</p>
                <Button
                  className="bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                  onClick={() => router.push("/memecoin-launchpad")}
                >
                  View all memecoins
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Coin Name</Label>
                  <Input
                    id="name"
                    placeholder="e.g. Doge Coin"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="bg-[#222] border-[#444] text-white"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ticker">Ticker Symbol</Label>
                  <Input
                    id="ticker"
                    placeholder="e.g. DOGE"
                    value={ticker}
                    onChange={(e) => setTicker(e.target.value.toUpperCase())}
                    className="bg-[#222] border-[#444] text-white"
                    maxLength={5}
                    required
                  />
                  <p className="text-xs text-gray-400">Maximum 5 characters</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    placeholder="Tell us about your coin..."
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="bg-[#222] border-[#444] text-white min-h-[100px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Coin Image</Label>
                  <div
                    className="border-2 border-dashed border-[#444] rounded-lg p-6 text-center cursor-pointer hover:border-[#D4AF37] transition-colors"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    {imagePreview ? (
                      <div className="flex flex-col items-center">
                        <div className="w-32 h-32 relative mb-4">
                          <Image
                            src={imagePreview || "/placeholder.svg"}
                            alt="Preview"
                            fill
                            className="object-cover rounded-lg"
                          />
                        </div>
                        <p className="text-sm text-gray-400">Click to change image</p>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center">
                        <Upload className="h-12 w-12 text-gray-400 mb-2" />
                        <p className="text-gray-400 mb-1">Click to upload image</p>
                        <p className="text-xs text-gray-500">PNG, JPG or GIF (max 5MB)</p>
                      </div>
                    )}
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleImageChange}
                      accept="image/*"
                      className="hidden"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="initialSupply">Initial Supply: {initialSupply.toLocaleString()}</Label>
                  <Slider
                    id="initialSupply"
                    min={100000}
                    max={10000000}
                    step={100000}
                    value={[initialSupply]}
                    onValueChange={(value) => setInitialSupply(value[0])}
                    className="py-4"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="initialPrice">Initial Price: ${initialPrice.toFixed(7)} SOL</Label>
                  <Slider
                    id="initialPrice"
                    min={0.0000001}
                    max={0.0001}
                    step={0.0000001}
                    value={[initialPrice]}
                    onValueChange={(value) => setInitialPrice(value[0])}
                    className="py-4"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fee">Transaction Fee: {fee}%</Label>
                  <Slider id="fee" min={1} max={5} step={0.5} value={fee} onValueChange={setFee} className="py-4" />
                  <p className="text-xs text-gray-400">
                    Fee applied to all transactions (1% platform fee + your custom fee)
                  </p>
                </div>

                <Alert className="bg-[#222] border-[#444]">
                  <Info className="h-4 w-4 text-[#D4AF37]" />
                  <AlertDescription className="text-gray-300">
                    Your coin will be created with a fair launch bonding curve on Solana Devnet. All tokens are created
                    at once with no presale, ensuring equal access for everyone to buy or sell from the moment of
                    creation.
                  </AlertDescription>
                </Alert>

                {error && (
                  <Alert variant="destructive" className="bg-red-900/20 border-red-900">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="pt-4">
                  <Button
                    type="submit"
                    className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black font-bold py-3"
                    disabled={isLoading}
                  >
                    {isLoading ? "Creating Coin..." : "Create Coin"}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </div>
      </main>
    </>
  )
}

"use client"
import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ArrowDown, ArrowUp, Search, Star, StarOff, ExternalLink, Calendar, AlertTriangle } from "lucide-react"
import Header from "@/components/header"
import { getTrendingData, getTokenMarketData } from "@/lib/market-service"
import { formatNumber } from "@/lib/utils"
import CoinPriceChart from "@/components/coin-price-chart"
import TokenTradePanel from "@/components/token/token-trade-panel"
import { TokenDetails } from "@/components/token/token-details"
import { SimilarTokens } from "@/components/token/similar-tokens"

export default function MarketPage() {
  const { connected, publicKey } = useWallet()
  const searchParams = useSearchParams()
  const router = useRouter()
  const tokenParam = searchParams.get("token")
  const coinParam = searchParams.get("coin")

  const [selectedToken, setSelectedToken] = useState(tokenParam || coinParam || "")
  const [activeTab, setActiveTab] = useState("ecosystem")
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [tokenData, setTokenData] = useState<any>(null)
  const [trendingData, setTrendingData] = useState<any>(null)
  const [favorites, setFavorites] = useState<string[]>([])
  const [sortBy, setSortBy] = useState("marketCap")
  const [filterNew, setFilterNew] = useState(false)

  // Charger les données de tendance
  useEffect(() => {
    const fetchTrendingData = async () => {
      setIsLoading(true)
      try {
        const data = await getTrendingData()
        setTrendingData(data)
      } catch (error) {
        console.error("Error fetching trending data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTrendingData()
  }, [])

  // Charger les données du token sélectionné
  useEffect(() => {
    const fetchTokenData = async () => {
      if (!selectedToken) return

      setIsLoading(true)
      try {
        const data = await getTokenMarketData(selectedToken)
        if (data) {
          setTokenData(data)
        }
      } catch (error) {
        console.error("Error fetching token data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTokenData()
  }, [selectedToken])

  // Charger les favoris depuis le localStorage
  useEffect(() => {
    const storedFavorites = localStorage.getItem("favorites")
    if (storedFavorites) {
      setFavorites(JSON.parse(storedFavorites))
    }
  }, [])

  // Sauvegarder les favoris dans le localStorage
  useEffect(() => {
    localStorage.setItem("favorites", JSON.stringify(favorites))
  }, [favorites])

  const toggleFavorite = (tokenId: string) => {
    if (favorites.includes(tokenId)) {
      setFavorites(favorites.filter((id) => id !== tokenId))
    } else {
      setFavorites([...favorites, tokenId])
    }
  }

  const isNewToken = (createdAt: number) => {
    const now = Date.now()
    const diff = now - createdAt
    const diffDays = Math.ceil(diff / (1000 * 3600 * 24))
    return diffDays <= 7
  }

  const sortTokens = (tokens: any[]) => {
    return [...tokens].sort((a, b) => {
      switch (sortBy) {
        case "marketCap":
          return b.marketCap - a.marketCap
        case "price":
          return b.price - a.price
        case "change":
          return b.priceChange24h - a.priceChange24h
        case "volume":
          return b.volume24h - a.volume24h
        default:
          return 0
      }
    })
  }

  const getFilteredTokens = () => {
    if (!trendingData) return []

    let tokens = []

    switch (activeTab) {
      case "ecosystem":
        tokens = [...trendingData.topTokens]
        break
      case "memecoins":
        tokens = [...trendingData.topMemecoins]
        break
      case "new":
        tokens = [...trendingData.recentlyAdded]
        break
      default:
        tokens = [...trendingData.topTokens]
    }

    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      tokens = tokens.filter(
        (token) =>
          token.name.toLowerCase().includes(query) ||
          token.symbol.toLowerCase().includes(query) ||
          token.address.toLowerCase().includes(query),
      )
    }

    // Filtrer par nouveauté
    if (filterNew) {
      const now = Date.now()
      const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000
      tokens = tokens.filter((token) => token.createdAt >= oneWeekAgo)
    }

    return sortTokens(tokens)
  }

  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Marché</h1>
              <p className="text-muted-foreground">Données de marché en temps réel et trading</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher des tokens..."
                  className="pl-8 w-full sm:w-[200px] md:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              {!connected && (
                <WalletMultiButton className="bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-4 py-2 w-full sm:w-auto" />
              )}
            </div>
          </div>

          {isLoading && selectedToken ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#D4AF37]"></div>
            </div>
          ) : tokenData && selectedToken ? (
            <div className="grid grid-cols-1 lg:grid-cols-[1fr_350px] gap-6">
              {/* Left column - Token details and chart */}
              <div className="space-y-6">
                <TokenDetails address={selectedToken} />
                <CoinPriceChart
                  coinId={selectedToken}
                  coinName={tokenData.name}
                  coinSymbol={tokenData.symbol}
                  currentPrice={tokenData.price}
                  priceChange24h={tokenData.priceChange24h}
                />
              </div>

              {/* Right column - Buy/Sell */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Trading</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <TokenTradePanel
                      tokenAddress={selectedToken}
                      tokenSymbol={tokenData.symbol}
                      tokenDecimals={tokenData.decimals || 9}
                      tokenPrice={tokenData.price}
                    />
                  </CardContent>
                </Card>

                <SimilarTokens address={selectedToken} limit={5} />
              </div>
            </div>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="ecosystem">Top Tokens</TabsTrigger>
                <TabsTrigger value="memecoins">Memecoins</TabsTrigger>
                <TabsTrigger value="new">Nouveaux Tokens</TabsTrigger>
              </TabsList>

              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">
                  {activeTab === "ecosystem"
                    ? "Top Tokens"
                    : activeTab === "memecoins"
                      ? "Memecoins"
                      : "Nouveaux Tokens"}
                </h2>
                <div className="flex items-center gap-2">
                  <Label htmlFor="sort-by" className="text-sm">
                    Trier par:
                  </Label>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger id="sort-by" className="w-[180px]">
                      <SelectValue placeholder="Trier par" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="marketCap">Capitalisation</SelectItem>
                      <SelectItem value="price">Prix</SelectItem>
                      <SelectItem value="change">Variation 24h</SelectItem>
                      <SelectItem value="volume">Volume</SelectItem>
                    </SelectContent>
                  </Select>
                  {activeTab !== "new" && (
                    <Button
                      variant={filterNew ? "default" : "outline"}
                      size="sm"
                      onClick={() => setFilterNew(!filterNew)}
                      className={filterNew ? "bg-[#D4AF37] hover:bg-[#B8941F] text-black" : ""}
                    >
                      <Calendar className="h-4 w-4 mr-1" />
                      Nouveaux tokens
                    </Button>
                  )}
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#D4AF37]"></div>
                </div>
              ) : trendingData ? (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-4">#</th>
                        <th className="text-left p-4">Nom</th>
                        <th className="text-right p-4">Prix</th>
                        <th className="text-right p-4">24h %</th>
                        <th className="text-right p-4">Market Cap</th>
                        <th className="text-right p-4">Volume (24h)</th>
                        <th className="text-center p-4">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getFilteredTokens().map((token, index) => (
                        <tr key={token.address} className="border-b hover:bg-muted/50">
                          <td className="p-4">{index + 1}</td>
                          <td className="p-4">
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 rounded-full bg-[#D4AF37] flex items-center justify-center text-black font-bold">
                                {token.symbol.substring(0, 2).toUpperCase()}
                              </div>
                              <div>
                                <div className="flex items-center gap-1">
                                  <span className="font-medium">{token.name}</span>
                                  {isNewToken(token.createdAt) && (
                                    <Badge className="ml-1 bg-green-500/20 text-green-400 text-xs">Nouveau</Badge>
                                  )}
                                </div>
                                <div className="text-xs text-muted-foreground">{token.symbol.toUpperCase()}</div>
                              </div>
                            </div>
                          </td>
                          <td className="p-4 text-right font-medium">${token.price.toFixed(7)}</td>
                          <td
                            className={`p-4 text-right ${token.priceChange24h >= 0 ? "text-green-500" : "text-red-500"}`}
                          >
                            <div className="flex items-center justify-end">
                              {token.priceChange24h >= 0 ? (
                                <ArrowUp className="h-3 w-3 mr-1" />
                              ) : (
                                <ArrowDown className="h-3 w-3 mr-1" />
                              )}
                              {Math.abs(token.priceChange24h).toFixed(2)}%
                            </div>
                          </td>
                          <td className="p-4 text-right">${formatNumber(token.marketCap)}</td>
                          <td className="p-4 text-right">${formatNumber(token.volume24h)}</td>
                          <td className="p-4 text-center">
                            <div className="flex items-center justify-center gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => toggleFavorite(token.address)}
                                className={
                                  favorites.includes(token.address) ? "text-[#D4AF37]" : "text-muted-foreground"
                                }
                              >
                                {favorites.includes(token.address) ? (
                                  <Star className="h-4 w-4 fill-[#D4AF37]" />
                                ) : (
                                  <StarOff className="h-4 w-4" />
                                )}
                              </Button>
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/market?token=${token.address}`}>
                                  <ExternalLink className="h-4 w-4 mr-1" />
                                  Détails
                                </Link>
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
                    <h3 className="text-xl font-medium mb-2">Données non disponibles</h3>
                    <p className="text-muted-foreground text-center max-w-md mb-6">
                      Impossible de charger les données du marché. Veuillez réessayer plus tard.
                    </p>
                    <Button onClick={() => window.location.reload()}>Actualiser</Button>
                  </CardContent>
                </Card>
              )}
            </Tabs>
          )}
        </div>
      </main>
    </>
  )
}

"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, ExternalLink, Star, StarOff, Info, Copy, Share2 } from "lucide-react"
import Header from "@/components/header"
import CoinPriceChart from "@/components/coin-price-chart"
import TokenTransaction from "@/components/token-transaction"
import TransactionHistory from "@/components/transaction-history"
import MarketStats from "@/components/market-stats"
import OrderBook from "@/components/order-book"
import { getTokenDetailsFromSolscan, getTokenInfo } from "@/lib/token-service"
import { getSolBalance, getTokenBalance } from "@/lib/solana-service"
import { useToast } from "@/components/ui/use-toast"

export default function TokenDetailPage() {
  const params = useParams()
  const { connected, publicKey } = useWallet()
  const { toast } = useToast()
  const tokenId = params.id as string

  const [tokenDetails, setTokenDetails] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const [isFavorite, setIsFavorite] = useState(false)
  const [tokenBalance, setTokenBalance] = useState<number | null>(null)
  const [solBalance, setSolBalance] = useState<number | null>(null)
  const [copied, setCopied] = useState(false)

  // Charger les détails du token
  useEffect(() => {
    const loadTokenDetails = async () => {
      setIsLoading(true)

      try {
        // Récupérer les détails du token
        const details = await getTokenDetailsFromSolscan(tokenId)

        if (details) {
          setTokenDetails(details)

          // Récupérer les informations supplémentaires du token
          if (details.mintAddress) {
            try {
              const tokenInfo = await getTokenInfo(details.mintAddress)
              setTokenDetails((prev) => ({
                ...prev,
                ...tokenInfo,
              }))
            } catch (err) {
              console.error("Erreur lors de la récupération des informations du token:", err)
            }
          }
        } else {
          // Fallback si aucune donnée n'est trouvée
          setTokenDetails({
            name: "Token inconnu",
            symbol: "UNKNOWN",
            mintAddress: tokenId,
            decimals: 9,
            totalSupply: "1000000000",
            price: 0.001,
            priceChange24h: 0,
            marketCap: 1000000,
            volume24h: 100000,
            holders: 100,
            creator: "Inconnu",
            createdAt: new Date().toISOString(),
            website: "",
            twitter: "",
            telegram: "",
            description: "Aucune description disponible pour ce token.",
          })
        }
      } catch (err) {
        console.error("Erreur lors du chargement des détails du token:", err)
        // Fallback en cas d'erreur
        setTokenDetails({
          name: "Erreur de chargement",
          symbol: "ERROR",
          mintAddress: tokenId,
          decimals: 9,
          totalSupply: "0",
          price: 0,
          priceChange24h: 0,
          marketCap: 0,
          volume24h: 0,
          holders: 0,
          creator: "Inconnu",
          createdAt: new Date().toISOString(),
          website: "",
          twitter: "",
          telegram: "",
          description: "Erreur lors du chargement des détails du token.",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadTokenDetails()
  }, [tokenId])

  // Récupérer le solde du token lorsque l'utilisateur est connecté
  useEffect(() => {
    const loadBalances = async () => {
      if (!connected || !publicKey || !tokenDetails?.mintAddress) {
        return
      }

      try {
        // Récupérer le solde SOL
        const solBalanceValue = await getSolBalance(publicKey.toString())
        setSolBalance(solBalanceValue)

        // Récupérer le solde du token
        const tokenBalanceValue = await getTokenBalance(publicKey.toString(), tokenDetails.mintAddress)
        setTokenBalance(tokenBalanceValue)
      } catch (err) {
        console.error("Erreur lors de la récupération des soldes:", err)
        setSolBalance(null)
        setTokenBalance(null)
      }
    }

    loadBalances()
  }, [connected, publicKey, tokenDetails?.mintAddress])

  // Gérer l'ajout/suppression des favoris
  const toggleFavorite = () => {
    setIsFavorite(!isFavorite)

    toast({
      title: isFavorite ? "Retiré des favoris" : "Ajouté aux favoris",
      description: isFavorite
        ? `${tokenDetails.name} a été retiré de vos favoris.`
        : `${tokenDetails.name} a été ajouté à vos favoris.`,
    })
  }

  // Copier l'adresse du token
  const copyTokenAddress = () => {
    if (tokenDetails?.mintAddress) {
      navigator.clipboard.writeText(tokenDetails.mintAddress)
      setCopied(true)

      setTimeout(() => {
        setCopied(false)
      }, 2000)

      toast({
        title: "Adresse copiée",
        description: "L'adresse du token a été copiée dans le presse-papier.",
      })
    }
  }

  // Mettre à jour les soldes après une transaction
  const handleTransactionComplete = async () => {
    if (connected && publicKey && tokenDetails?.mintAddress) {
      try {
        // Récupérer le solde SOL
        const solBalanceValue = await getSolBalance(publicKey.toString())
        setSolBalance(solBalanceValue)

        // Récupérer le solde du token
        const tokenBalanceValue = await getTokenBalance(publicKey.toString(), tokenDetails.mintAddress)
        setTokenBalance(tokenBalanceValue)
      } catch (err) {
        console.error("Erreur lors de la mise à jour des soldes:", err)
      }
    }
  }

  // Formater les dates
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString()
  }

  if (isLoading) {
    return (
      <>
        <Header />
        <main className="container py-6">
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#D4AF37]"></div>
          </div>
        </main>
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="flex flex-col gap-6">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/market">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Retour au marché
              </Link>
            </Button>
          </div>

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-full bg-[#D4AF37] flex items-center justify-center text-black font-bold">
                {tokenDetails.symbol.substring(0, 2)}
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h1 className="text-3xl font-bold">{tokenDetails.name}</h1>
                  <button onClick={toggleFavorite} className="text-gray-400 hover:text-[#D4AF37]">
                    {isFavorite ? (
                      <Star className="h-5 w-5 fill-[#D4AF37] text-[#D4AF37]" />
                    ) : (
                      <StarOff className="h-5 w-5" />
                    )}
                  </button>
                  {tokenDetails.symbol.endsWith("QUANTUM") && (
                    <Badge className="bg-[#D4AF37] text-black">Quantum</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <span>{tokenDetails.symbol}</span>
                  <span className="text-xs font-mono">
                    {tokenDetails.mintAddress && tokenDetails.mintAddress.length > 8
                      ? `${tokenDetails.mintAddress.substring(0, 4)}...${tokenDetails.mintAddress.substring(
                          tokenDetails.mintAddress.length - 4,
                        )}`
                      : tokenDetails.mintAddress}
                  </span>
                  <button onClick={copyTokenAddress} className="text-muted-foreground hover:text-foreground">
                    {copied ? <Info className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </button>
                </div>
              </div>
            </div>

            <div className="flex flex-col items-end">
              <div className="text-2xl font-bold">${tokenDetails.price.toFixed(6)}</div>
              <div
                className={
                  tokenDetails.priceChange24h >= 0
                    ? "text-green-500 flex items-center"
                    : "text-red-500 flex items-center"
                }
              >
                {tokenDetails.priceChange24h >= 0 ? "+" : ""}
                {tokenDetails.priceChange24h.toFixed(2)}% (24h)
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-[1fr_350px] gap-6">
            {/* Left column - Token details and chart */}
            <div className="space-y-6">
              <Card>
                <CardContent className="p-0">
                  <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="w-full rounded-none border-b">
                      <TabsTrigger value="overview" className="flex-1">
                        Aperçu
                      </TabsTrigger>
                      <TabsTrigger value="chart" className="flex-1">
                        Graphique
                      </TabsTrigger>
                      <TabsTrigger value="transactions" className="flex-1">
                        Transactions
                      </TabsTrigger>
                      <TabsTrigger value="orderbook" className="flex-1">
                        Carnet d'ordres
                      </TabsTrigger>
                    </TabsList>

                    <div className="p-6">
                      <TabsContent value="overview" className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-lg">Informations sur le token</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Adresse du contrat:</span>
                                <span className="font-medium font-mono text-xs">
                                  {tokenDetails.mintAddress && tokenDetails.mintAddress.length > 16 ? (
                                    <>
                                      {tokenDetails.mintAddress.substring(0, 8)}...
                                      {tokenDetails.mintAddress.substring(tokenDetails.mintAddress.length - 8)}
                                    </>
                                  ) : (
                                    tokenDetails.mintAddress || "Inconnu"
                                  )}
                                </span>
                              </div>
                              <Separator />
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Décimales:</span>
                                <span className="font-medium">{tokenDetails.decimals}</span>
                              </div>
                              <Separator />
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Offre totale:</span>
                                <span className="font-medium">
                                  {Number(tokenDetails.totalSupply).toLocaleString()} {tokenDetails.symbol}
                                </span>
                              </div>
                              <Separator />
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Créateur:</span>
                                <span className="font-medium font-mono text-xs">
                                  {tokenDetails.creator && tokenDetails.creator.length > 10 ? (
                                    <>
                                      {tokenDetails.creator.substring(0, 6)}...
                                      {tokenDetails.creator.substring(tokenDetails.creator.length - 4)}
                                    </>
                                  ) : (
                                    tokenDetails.creator || "Inconnu"
                                  )}
                                </span>
                              </div>
                              <Separator />
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Date de création:</span>
                                <span className="font-medium">{formatDate(tokenDetails.createdAt)}</span>
                              </div>
                              {tokenDetails.mintAuthority && (
                                <>
                                  <Separator />
                                  <div className="flex justify-between text-sm">
                                    <span className="text-muted-foreground">Autorité de mint:</span>
                                    <span className="font-medium font-mono text-xs">
                                      {tokenDetails.mintAuthority
                                        ? `${tokenDetails.mintAuthority.substring(0, 6)}...${tokenDetails.mintAuthority.substring(tokenDetails.mintAuthority.length - 4)}`
                                        : "Aucune (offre fixe)"}
                                    </span>
                                  </div>
                                </>
                              )}
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-lg">Statistiques du marché</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Capitalisation boursière:</span>
                                <span className="font-medium">${tokenDetails.marketCap.toLocaleString()}</span>
                              </div>
                              <Separator />
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Volume (24h):</span>
                                <span className="font-medium">${tokenDetails.volume24h.toLocaleString()}</span>
                              </div>
                              <Separator />
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Détenteurs:</span>
                                <span className="font-medium">{tokenDetails.holders.toLocaleString()}</span>
                              </div>
                              <Separator />
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Prix le plus haut (24h):</span>
                                <span className="font-medium">${(tokenDetails.price * 1.05).toFixed(6)}</span>
                              </div>
                              <Separator />
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Prix le plus bas (24h):</span>
                                <span className="font-medium">${(tokenDetails.price * 0.95).toFixed(6)}</span>
                              </div>
                              {connected && tokenBalance !== null && (
                                <>
                                  <Separator />
                                  <div className="flex justify-between text-sm">
                                    <span className="text-muted-foreground">Votre solde:</span>
                                    <span className="font-medium">
                                      {tokenBalance.toLocaleString()} {tokenDetails.symbol}
                                    </span>
                                  </div>
                                </>
                              )}
                            </CardContent>
                          </Card>
                        </div>

                        {tokenDetails.description && (
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-lg">Description</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <p className="text-sm">{tokenDetails.description}</p>
                            </CardContent>
                          </Card>
                        )}

                        <div className="flex flex-wrap gap-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link
                              href={`https://explorer.solana.com/address/${tokenDetails.mintAddress}?cluster=devnet`}
                              target="_blank"
                            >
                              <ExternalLink className="h-4 w-4 mr-2" />
                              Explorer Solana
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <Link
                              href={`https://solscan.io/token/${tokenDetails.mintAddress}?cluster=devnet`}
                              target="_blank"
                            >
                              <ExternalLink className="h-4 w-4 mr-2" />
                              Solscan
                            </Link>
                          </Button>
                          {tokenDetails.website && (
                            <Button variant="outline" size="sm" asChild>
                              <Link href={tokenDetails.website} target="_blank">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Site web
                              </Link>
                            </Button>
                          )}
                          {tokenDetails.twitter && (
                            <Button variant="outline" size="sm" asChild>
                              <Link href={tokenDetails.twitter} target="_blank">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Twitter
                              </Link>
                            </Button>
                          )}
                          {tokenDetails.telegram && (
                            <Button variant="outline" size="sm" asChild>
                              <Link href={tokenDetails.telegram} target="_blank">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Telegram
                              </Link>
                            </Button>
                          )}
                          <Button variant="outline" size="sm" onClick={copyTokenAddress}>
                            <Copy className="h-4 w-4 mr-2" />
                            Copier l'adresse
                          </Button>
                          <Button variant="outline" size="sm">
                            <Share2 className="h-4 w-4 mr-2" />
                            Partager
                          </Button>
                        </div>
                      </TabsContent>

                      <TabsContent value="chart">
                        <div className="h-[500px]">
                          <CoinPriceChart
                            coinId={tokenId}
                            coinName={tokenDetails?.name || "Token"}
                            coinSymbol={tokenDetails?.symbol}
                            currentPrice={tokenDetails?.price || 0}
                            priceChange24h={tokenDetails?.priceChange24h || 0}
                          />
                        </div>
                      </TabsContent>

                      <TabsContent value="transactions">
                        <TransactionHistory tokenAddress={tokenDetails.mintAddress} limit={10} />
                      </TabsContent>

                      <TabsContent value="orderbook">
                        <OrderBook tokenAddress={tokenDetails.mintAddress} tokenSymbol={tokenDetails.symbol} />
                      </TabsContent>
                    </div>
                  </Tabs>
                </CardContent>
              </Card>

              <MarketStats tokenAddress={tokenDetails.mintAddress} tokenSymbol={tokenDetails.symbol} />
            </div>

            {/* Right column - Buy/Sell */}
            <div className="space-y-6">
              {!connected ? (
                <Card>
                  <CardHeader>
                    <CardTitle>Trader {tokenDetails.symbol}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex flex-col items-center justify-center py-6">
                    <p className="text-center mb-4">
                      Connectez votre portefeuille pour acheter et vendre {tokenDetails.symbol}
                    </p>
                    <WalletMultiButton className="bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-4 py-2" />
                  </CardContent>
                </Card>
              ) : (
                <TokenTransaction
                  tokenId={tokenDetails.mintAddress}
                  tokenSymbol={tokenDetails.symbol}
                  tokenName={tokenDetails.name}
                  tokenPrice={tokenDetails.price}
                  tokenBalance={tokenBalance || 0}
                  solBalance={solBalance || 0}
                  onTransactionComplete={handleTransactionComplete}
                />
              )}

              <Card>
                <CardHeader>
                  <CardTitle>À propos de {tokenDetails.symbol}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm">
                    {tokenDetails.description ||
                      `${tokenDetails.name} est un token sur la blockchain Solana. Pour plus d'informations, consultez les liens externes ou contactez l'équipe du projet.`}
                  </p>
                  {tokenDetails.symbol.endsWith("QUANTUM") && (
                    <div className="bg-[#D4AF37]/10 p-3 rounded-md">
                      <p className="text-sm font-medium">Token Quantum</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Les tokens Quantum sont vérifiés et sécurisés par notre plateforme. Ils offrent une sécurité et
                        une transparence accrues.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

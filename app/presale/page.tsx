"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Calendar,
  Clock,
  DollarSign,
  ExternalLink,
  Globe,
  Info,
  Lock,
  Users,
  AlertCircle,
  CheckCircle2,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import Header from "@/components/header"
import { usePresaleStore, type Presale } from "@/lib/presale-store"
import { formatDistanceToNow, format } from "date-fns"

export default function PresalePage() {
  const { publicKey, connected } = useWallet()
  const searchParams = useSearchParams()
  const presaleId = searchParams.get("id")
  const presaleStore = usePresaleStore()
  const [selectedPresale, setSelectedPresale] = useState<Presale | null>(null)
  const [activeTab, setActiveTab] = useState("active")
  const [purchaseAmount, setPurchaseAmount] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Charger les données
  useEffect(() => {
    if (presaleId) {
      const presale = presaleStore.getPresale(presaleId)
      if (presale) {
        setSelectedPresale(presale)
      }
    }
  }, [presaleId, presaleStore])

  // Filtrer les préventes selon l'onglet actif
  const getFilteredPresales = () => {
    switch (activeTab) {
      case "active":
        return presaleStore.getActivePresales()
      case "upcoming":
        return presaleStore.getUpcomingPresales()
      case "ended":
        return presaleStore.getEndedPresales()
      default:
        return presaleStore.getAllPresales()
    }
  }

  const filteredPresales = getFilteredPresales()

  // Calculer les tokens à recevoir
  const calculateTokens = (amount: string) => {
    if (!amount || !selectedPresale) return 0
    const amountNum = Number.parseFloat(amount)
    if (isNaN(amountNum)) return 0
    return amountNum / selectedPresale.price
  }

  // Participer à la prévente
  const handleParticipate = async () => {
    if (!connected || !publicKey || !selectedPresale) {
      setError("Please connect your wallet first")
      return
    }

    if (!purchaseAmount) {
      setError("Please enter an amount")
      return
    }

    const amount = Number.parseFloat(purchaseAmount)
    if (isNaN(amount)) {
      setError("Invalid amount")
      return
    }

    if (amount < selectedPresale.minPurchase) {
      setError(`Minimum purchase is ${selectedPresale.minPurchase} SOL`)
      return
    }

    if (amount > selectedPresale.maxPurchase) {
      setError(`Maximum purchase is ${selectedPresale.maxPurchase} SOL`)
      return
    }

    setIsProcessing(true)
    setError(null)
    setSuccess(null)

    try {
      // Simuler une transaction
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Ajouter la participation
      const tokens = calculateTokens(purchaseAmount)
      presaleStore.addParticipation({
        id: `participation_${Date.now()}`,
        presaleId: selectedPresale.id,
        walletAddress: publicKey.toString(),
        amount,
        tokens,
        timestamp: Date.now(),
        txId: `tx_${Math.random().toString(36).substring(2, 15)}`,
      })

      // Mettre à jour la prévente
      presaleStore.updatePresale(selectedPresale.id, {
        raised: selectedPresale.raised + amount,
        sold: selectedPresale.sold + tokens,
      })

      setSuccess(
        `Successfully participated with ${amount} SOL and received ${tokens.toLocaleString()} ${selectedPresale.tokenSymbol}!`,
      )
      setPurchaseAmount("")
    } catch (err) {
      console.error("Error participating in presale:", err)
      setError("Failed to participate in presale. Please try again.")
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-bold tracking-tight">Token Presale</h1>
            <p className="text-muted-foreground">Participate in token presales with early access</p>
          </div>

          {selectedPresale ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left column - Presale info */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-[#D4AF37] flex items-center justify-center text-black font-bold">
                          {selectedPresale.tokenSymbol.substring(0, 2)}
                        </div>
                        <div>
                          <CardTitle>{selectedPresale.tokenName}</CardTitle>
                          <CardDescription>{selectedPresale.tokenSymbol}</CardDescription>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href="/presale">Back to all presales</Link>
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {selectedPresale.description && (
                      <div className="p-4 bg-muted rounded-lg">
                        <p>{selectedPresale.description}</p>
                      </div>
                    )}

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Presale Progress</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Raised:</span>
                          <span className="font-medium">
                            {selectedPresale.raised} / {selectedPresale.hardCap / selectedPresale.price} SOL
                          </span>
                        </div>
                        <Progress
                          value={(selectedPresale.raised / (selectedPresale.hardCap / selectedPresale.price)) * 100}
                          className="h-2"
                        />
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Tokens Sold:</span>
                          <span className="font-medium">
                            {selectedPresale.sold.toLocaleString()} / {selectedPresale.hardCap.toLocaleString()}{" "}
                            {selectedPresale.tokenSymbol}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Presale Details</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="flex items-center gap-1 text-muted-foreground">
                              <DollarSign className="h-4 w-4" />
                              Price:
                            </span>
                            <span className="font-medium">{selectedPresale.price} SOL</span>
                          </div>
                          <Separator />
                          <div className="flex justify-between text-sm">
                            <span className="flex items-center gap-1 text-muted-foreground">
                              <Calendar className="h-4 w-4" />
                              Start Date:
                            </span>
                            <span className="font-medium">{format(selectedPresale.startTime, "PPP")}</span>
                          </div>
                          <Separator />
                          <div className="flex justify-between text-sm">
                            <span className="flex items-center gap-1 text-muted-foreground">
                              <Calendar className="h-4 w-4" />
                              End Date:
                            </span>
                            <span className="font-medium">{format(selectedPresale.endTime, "PPP")}</span>
                          </div>
                          <Separator />
                          <div className="flex justify-between text-sm">
                            <span className="flex items-center gap-1 text-muted-foreground">
                              <Clock className="h-4 w-4" />
                              Status:
                            </span>
                            <span className="font-medium capitalize">{selectedPresale.status}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Vesting Information</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="flex items-center gap-1 text-muted-foreground">
                              <Lock className="h-4 w-4" />
                              Vesting Period:
                            </span>
                            <span className="font-medium">{selectedPresale.vestingPeriod} days</span>
                          </div>
                          <Separator />
                          <div className="flex justify-between text-sm">
                            <span className="flex items-center gap-1 text-muted-foreground">
                              <Clock className="h-4 w-4" />
                              Releases:
                            </span>
                            <span className="font-medium">{selectedPresale.vestingReleases}</span>
                          </div>
                          <Separator />
                          <div className="flex justify-between text-sm">
                            <span className="flex items-center gap-1 text-muted-foreground">
                              <Users className="h-4 w-4" />
                              Created By:
                            </span>
                            <span className="font-medium">
                              {selectedPresale.createdBy.substring(0, 6)}...
                              {selectedPresale.createdBy.substring(selectedPresale.createdBy.length - 4)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-4">
                      {selectedPresale.website && (
                        <Button variant="outline" size="sm" className="gap-1" asChild>
                          <Link href={selectedPresale.website} target="_blank">
                            <Globe className="h-4 w-4" />
                            Website
                          </Link>
                        </Button>
                      )}
                      {selectedPresale.twitter && (
                        <Button variant="outline" size="sm" className="gap-1" asChild>
                          <Link href={selectedPresale.twitter} target="_blank">
                            <ExternalLink className="h-4 w-4" />
                            Twitter
                          </Link>
                        </Button>
                      )}
                      {selectedPresale.telegram && (
                        <Button variant="outline" size="sm" className="gap-1" asChild>
                          <Link href={selectedPresale.telegram} target="_blank">
                            <ExternalLink className="h-4 w-4" />
                            Telegram
                          </Link>
                        </Button>
                      )}
                      {selectedPresale.whitepaper && (
                        <Button variant="outline" size="sm" className="gap-1" asChild>
                          <Link href={selectedPresale.whitepaper} target="_blank">
                            <ExternalLink className="h-4 w-4" />
                            Whitepaper
                          </Link>
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right column - Participate */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Participate in Presale</CardTitle>
                    <CardDescription>Purchase tokens at presale price</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {!connected ? (
                      <div className="text-center py-4">
                        <p className="mb-4 text-muted-foreground">Connect your wallet to participate</p>
                        <WalletMultiButton className="bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-4 py-2" />
                      </div>
                    ) : (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="purchaseAmount">Amount (SOL)</Label>
                          <Input
                            id="purchaseAmount"
                            type="number"
                            placeholder="0.0"
                            value={purchaseAmount}
                            onChange={(e) => setPurchaseAmount(e.target.value)}
                            min={selectedPresale.minPurchase}
                            max={selectedPresale.maxPurchase}
                            step="0.01"
                            disabled={isProcessing}
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Min: {selectedPresale.minPurchase} SOL</span>
                            <span>Max: {selectedPresale.maxPurchase} SOL</span>
                          </div>
                        </div>

                        <div className="p-3 bg-muted rounded-md">
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-muted-foreground">You will receive:</span>
                            <span className="font-medium">
                              {calculateTokens(purchaseAmount).toLocaleString()} {selectedPresale.tokenSymbol}
                            </span>
                          </div>
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Price per token:</span>
                            <span>{selectedPresale.price} SOL</span>
                          </div>
                        </div>

                        <Alert>
                          <Info className="h-4 w-4" />
                          <AlertDescription>
                            Tokens will be vested for {selectedPresale.vestingPeriod} days with{" "}
                            {selectedPresale.vestingReleases} releases.
                          </AlertDescription>
                        </Alert>

                        {error && (
                          <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Error</AlertTitle>
                            <AlertDescription>{error}</AlertDescription>
                          </Alert>
                        )}

                        {success && (
                          <Alert className="bg-green-50 text-green-800 border-green-200">
                            <CheckCircle2 className="h-4 w-4 text-green-600" />
                            <AlertTitle>Success</AlertTitle>
                            <AlertDescription>{success}</AlertDescription>
                          </Alert>
                        )}
                      </>
                    )}
                  </CardContent>
                  <CardFooter>
                    <Button
                      className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                      disabled={!connected || isProcessing || !purchaseAmount}
                      onClick={handleParticipate}
                    >
                      {isProcessing ? "Processing..." : "Participate Now"}
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </div>
          ) : (
            <>
              <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-6">
                  <TabsTrigger value="active">Active</TabsTrigger>
                  <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
                  <TabsTrigger value="ended">Ended</TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab}>
                  {filteredPresales.length === 0 ? (
                    <Card>
                      <CardContent className="flex flex-col items-center justify-center py-12">
                        <Info className="h-12 w-12 text-muted-foreground mb-4" />
                        <p className="text-muted-foreground text-center">No {activeTab} presales found.</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {filteredPresales.map((presale) => (
                        <Card key={presale.id} className="overflow-hidden">
                          <CardHeader className="pb-3">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 rounded-full bg-[#D4AF37] flex items-center justify-center text-black font-bold">
                                {presale.tokenSymbol.substring(0, 2)}
                              </div>
                              <div>
                                <CardTitle>{presale.tokenName}</CardTitle>
                                <CardDescription>{presale.tokenSymbol}</CardDescription>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Price:</span>
                                <span className="font-medium">{presale.price} SOL</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Raised:</span>
                                <span className="font-medium">
                                  {presale.raised} / {presale.hardCap / presale.price} SOL
                                </span>
                              </div>
                              <Progress
                                value={(presale.raised / (presale.hardCap / presale.price)) * 100}
                                className="h-2"
                              />
                              <div className="flex justify-between text-xs text-muted-foreground">
                                <span>{((presale.raised / (presale.hardCap / presale.price)) * 100).toFixed(2)}%</span>
                                <span>
                                  {presale.sold.toLocaleString()} / {presale.hardCap.toLocaleString()} tokens
                                </span>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-muted-foreground">Start:</span>{" "}
                                <span className="font-medium">
                                  {formatDistanceToNow(presale.startTime, { addSuffix: true })}
                                </span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">End:</span>{" "}
                                <span className="font-medium">
                                  {formatDistanceToNow(presale.endTime, { addSuffix: true })}
                                </span>
                              </div>
                            </div>
                          </CardContent>
                          <CardFooter>
                            <Button className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black" asChild>
                              <Link href={`/presale?id=${presale.id}`}>View Details</Link>
                            </Button>
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </>
          )}
        </div>
      </main>
    </>
  )
}

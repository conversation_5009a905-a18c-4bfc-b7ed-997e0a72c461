"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowUpRight, Download, Share2 } from "lucide-react"
import { Line, LineChart, ResponsiveContainer, XAxis, YAxis, Tooltip, CartesianGrid, BarChart, Bar } from "recharts"
import Header from "@/components/header"
import { Footer } from "@/components/footer"
import { TokenHeader } from "@/components/token/token-header"
import { getTokenDetails, getTokenPriceHistory } from "@/lib/token-service"
import { formatNumber, formatCurrency } from "@/lib/utils"

export default function TokenAnalyticsPage() {
  const { address } = useParams<{ address: string }>()
  const router = useRouter()
  const { connected, publicKey } = useWallet()
  const [token, setToken] = useState<any>(null)
  const [priceHistory, setPriceHistory] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [timeframe, setTimeframe] = useState<"24h" | "7d" | "30d" | "all">("7d")
  const [isOwner, setIsOwner] = useState(false)

  useEffect(() => {
    const fetchTokenData = async () => {
      setIsLoading(true)
      try {
        // Récupérer les détails du token
        const tokenData = await getTokenDetails(address)
        setToken(tokenData)

        // Vérifier si l'utilisateur connecté est le créateur du token
        if (connected && publicKey) {
          // Dans une implémentation réelle, vous vérifieriez si l'utilisateur est le créateur
          setIsOwner(Math.random() > 0.5) // Simulation
        }

        // Récupérer l'historique des prix
        const history = await getTokenPriceHistory(address, timeframe)
        setPriceHistory(history)
      } catch (error) {
        console.error("Error fetching token data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTokenData()
  }, [address, connected, publicKey, timeframe])

  // Générer des statistiques aléatoires pour la démonstration
  const generateStats = () => {
    const baseHolders = Math.floor(Math.random() * 10000) + 100
    const baseVolume = Math.random() * 1000000
    const baseTransactions = Math.floor(Math.random() * 5000) + 100

    return {
      holders: {
        current: baseHolders,
        previous: baseHolders * (Math.random() * 0.3 + 0.7), // 70-100% du nombre actuel
        change: Math.random() * 30, // 0-30% de croissance
      },
      volume: {
        current: baseVolume,
        previous: baseVolume * (Math.random() * 0.5 + 0.5), // 50-100% du volume actuel
        change: Math.random() * 50, // 0-50% de croissance
      },
      transactions: {
        current: baseTransactions,
        previous: baseTransactions * (Math.random() * 0.4 + 0.6), // 60-100% du nombre actuel
        change: Math.random() * 40, // 0-40% de croissance
      },
      newHolders: Math.floor(baseHolders * (Math.random() * 0.2)), // 0-20% de nouveaux détenteurs
      avgHoldingTime: Math.floor(Math.random() * 30) + 1, // 1-30 jours
      topHoldersPercentage: Math.random() * 40 + 30, // 30-70% détenus par les top holders
    }
  }

  const stats = generateStats()

  if (!address) {
    return <div>Adresse de token invalide</div>
  }

  if (isLoading) {
    return (
      <>
        <Header />
        <main className="container py-8 space-y-8">
          <Skeleton className="h-20 w-full" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <Skeleton key={index} className="h-32 w-full" />
            ))}
          </div>
          <Skeleton className="h-[400px] w-full" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Skeleton className="h-[300px] w-full" />
            <Skeleton className="h-[300px] w-full" />
          </div>
        </main>
        <Footer />
      </>
    )
  }

  if (!token) {
    return (
      <>
        <Header />
        <main className="container py-8">
          <div className="text-center py-16">
            <h1 className="text-2xl font-bold mb-4">Token non trouvé</h1>
            <p className="text-muted-foreground">Impossible de trouver les détails du token avec l'adresse {address}</p>
          </div>
        </main>
        <Footer />
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="container py-8 space-y-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Analytiques du token</h1>
            <p className="text-muted-foreground">Statistiques détaillées et performances</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Exporter
            </Button>
            <Button variant="outline" size="sm">
              <Share2 className="mr-2 h-4 w-4" />
              Partager
            </Button>
            <Button
              className="bg-[#D4AF37] hover:bg-[#B8941F] text-black"
              size="sm"
              onClick={() => router.push(`/token/${address}`)}
            >
              <ArrowUpRight className="mr-2 h-4 w-4" />
              Voir le token
            </Button>
          </div>
        </div>

        <TokenHeader
          tokenName={token.name}
          tokenSymbol={token.symbol}
          tokenPrice={token.price}
          priceChange24h={token.priceChange24h}
          isVerified={token.verified}
          isQuantum={token.isQuantum}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Détenteurs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-end justify-between">
                <div>
                  <div className="text-2xl font-bold">{formatNumber(stats.holders.current)}</div>
                  <div
                    className={
                      stats.holders.change > 0
                        ? "text-xs text-green-500 flex items-center"
                        : "text-xs text-red-500 flex items-center"
                    }
                  >
                    {stats.holders.change > 0 ? "+" : ""}
                    {stats.holders.change.toFixed(2)}% depuis 7 jours
                  </div>
                </div>
                <div className="h-16 w-24">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={priceHistory.slice(-7).map((item, index) => ({
                        name: index,
                        value: stats.holders.previous + (stats.holders.current - stats.holders.previous) * (index / 6),
                      }))}
                    >
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke="#22c55e"
                        strokeWidth={2}
                        dot={false}
                        isAnimationActive={false}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Volume (24h)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-end justify-between">
                <div>
                  <div className="text-2xl font-bold">{formatCurrency(stats.volume.current)}</div>
                  <div
                    className={
                      stats.volume.change > 0
                        ? "text-xs text-green-500 flex items-center"
                        : "text-xs text-red-500 flex items-center"
                    }
                  >
                    {stats.volume.change > 0 ? "+" : ""}
                    {stats.volume.change.toFixed(2)}% depuis hier
                  </div>
                </div>
                <div className="h-16 w-24">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={priceHistory.slice(-24).map((item, index) => ({
                        name: index,
                        value: item.volume,
                      }))}
                    >
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={false}
                        isAnimationActive={false}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Transactions (24h)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-end justify-between">
                <div>
                  <div className="text-2xl font-bold">{formatNumber(stats.transactions.current)}</div>
                  <div
                    className={
                      stats.transactions.change > 0
                        ? "text-xs text-green-500 flex items-center"
                        : "text-xs text-red-500 flex items-center"
                    }
                  >
                    {stats.transactions.change > 0 ? "+" : ""}
                    {stats.transactions.change.toFixed(2)}% depuis hier
                  </div>
                </div>
                <div className="h-16 w-24">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={priceHistory.slice(-24).map((item, index) => ({
                        name: index,
                        value: Math.floor(Math.random() * 100) + 50,
                      }))}
                    >
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke="#8b5cf6"
                        strokeWidth={2}
                        dot={false}
                        isAnimationActive={false}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Évolution du prix</CardTitle>
              <Tabs value={timeframe} onValueChange={(value) => setTimeframe(value as any)}>
                <TabsList>
                  <TabsTrigger value="24h">24h</TabsTrigger>
                  <TabsTrigger value="7d">7j</TabsTrigger>
                  <TabsTrigger value="30d">30j</TabsTrigger>
                  <TabsTrigger value="all">Tout</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[400px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={priceHistory}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={(timestamp) => {
                      const date = new Date(timestamp)
                      if (timeframe === "24h") {
                        return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
                      } else if (timeframe === "7d") {
                        return date.toLocaleDateString([], { weekday: "short" })
                      } else {
                        return date.toLocaleDateString([], { month: "short", day: "numeric" })
                      }
                    }}
                  />
                  <YAxis
                    tickFormatter={(value) => formatCurrency(value, true)}
                    domain={["dataMin", "dataMax"]}
                    padding={{ top: 20, bottom: 20 }}
                  />
                  <Tooltip
                    formatter={(value: number) => [formatCurrency(value), "Prix"]}
                    labelFormatter={(timestamp) => new Date(timestamp).toLocaleString()}
                  />
                  <Line
                    type="monotone"
                    dataKey="price"
                    stroke="#D4AF37"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Distribution des détenteurs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={[
                      { name: "Top 10", value: stats.topHoldersPercentage },
                      { name: "Top 11-50", value: Math.random() * 20 + 10 },
                      { name: "Top 51-100", value: Math.random() * 10 + 5 },
                      { name: "Autres", value: 100 - stats.topHoldersPercentage - Math.random() * 30 - 15 },
                    ]}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `${value}%`} />
                    <Tooltip formatter={(value: number) => [`${value.toFixed(2)}%`, "Pourcentage"]} />
                    <Bar dataKey="value" fill="#D4AF37" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4 text-sm text-muted-foreground">
                <p>
                  Les 10 principaux détenteurs possèdent <strong>{stats.topHoldersPercentage.toFixed(2)}%</strong> de
                  l'offre totale.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Statistiques des détenteurs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Nouveaux détenteurs (7j)</div>
                    <div className="text-2xl font-bold">{formatNumber(stats.newHolders)}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Temps de détention moyen</div>
                    <div className="text-2xl font-bold">{stats.avgHoldingTime} jours</div>
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-2">
                    Répartition par taille de portefeuille
                  </div>
                  <div className="space-y-2">
                    {[
                      { label: "Baleines (>1%)", value: Math.random() * 10 + 5 },
                      { label: "Grands détenteurs (0.1-1%)", value: Math.random() * 15 + 10 },
                      { label: "Moyens détenteurs (0.01-0.1%)", value: Math.random() * 25 + 15 },
                      { label: "Petits détenteurs (<0.01%)", value: Math.random() * 40 + 30 },
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="text-sm">{item.label}</div>
                        <div className="text-sm font-medium">{item.value.toFixed(2)}%</div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-2">Activité des détenteurs</div>
                  <div className="flex items-center gap-4">
                    <div className="flex-1 bg-muted rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${Math.random() * 40 + 40}%` }}
                      ></div>
                    </div>
                    <div className="text-sm font-medium">{Math.floor(Math.random() * 40 + 40)}% actifs</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {isOwner && (
          <Card>
            <CardHeader>
              <CardTitle>Outils du créateur</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button variant="outline">Gérer la liquidité</Button>
                <Button variant="outline">Configurer les récompenses</Button>
                <Button variant="outline">Paramètres avancés</Button>
              </div>
            </CardContent>
          </Card>
        )}
      </main>
      <Footer />
    </>
  )
}

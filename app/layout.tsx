import type React from "react"
import "./globals.css"
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { WalletProvider } from "@/components/wallet-provider"
import { NetworkProvider } from "@/contexts/network-context"
import { NetworkBanner } from "@/components/network-banner"
import { Footer } from "@/components/footer"
import { Toaster } from "@/components/ui/toaster"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Global Finance Platform",
  description: "Plateforme de création et gestion de tokens sur Solana et BNB Chain",
    generator: 'v0.dev'
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <WalletProvider>
            <NetworkProvider>
              <div className="flex flex-col min-h-screen">
                <NetworkBanner />
                <div className="flex-1">{children}</div>
                <Footer />
              </div>
              <Toaster />
            </NetworkProvider>
          </WalletProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}

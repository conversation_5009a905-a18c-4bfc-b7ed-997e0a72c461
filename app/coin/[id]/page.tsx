"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"
import Header from "@/components/header"
import { TokenDetails } from "@/components/token/token-details"
import { TokenTransactionList } from "@/components/token/token-transaction-list"
import TokenTradePanel from "@/components/token/token-trade-panel"
import { SimilarTokens } from "@/components/token/similar-tokens"
import CoinPriceChart from "@/components/coin-price-chart"
import { getTokenMarketData } from "@/lib/market-service"

export default function CoinDetailPage() {
  const params = useParams()
  const id = params.id as string

  const [isLoading, setIsLoading] = useState(true)
  const [tokenData, setTokenData] = useState<any>(null)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    const fetchTokenData = async () => {
      setIsLoading(true)
      try {
        const data = await getTokenMarketData(id)
        if (data) {
          setTokenData(data)
        }
      } catch (error) {
        console.error("Error fetching token data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (id) {
      fetchTokenData()
    }
  }, [id])

  if (isLoading) {
    return (
      <>
        <Header />
        <main className="container py-8">
          <div className="space-y-6">
            <Skeleton className="h-12 w-1/3" />
            <div className="grid grid-cols-1 lg:grid-cols-[2fr_1fr] gap-6">
              <div className="space-y-6">
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[200px] w-full" />
              </div>
              <div className="space-y-6">
                <Skeleton className="h-[300px] w-full" />
                <Skeleton className="h-[200px] w-full" />
              </div>
            </div>
          </div>
        </main>
      </>
    )
  }

  if (!tokenData) {
    return (
      <>
        <Header />
        <main className="container py-8">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Impossible de charger les données du token. Veuillez vérifier l'adresse et réessayer.
            </AlertDescription>
          </Alert>
        </main>
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="container py-8">
        <div className="space-y-6">
          <h1 className="text-3xl font-bold">
            {tokenData.name} ({tokenData.symbol})
          </h1>

          <div className="grid grid-cols-1 lg:grid-cols-[2fr_1fr] gap-6">
            <div className="space-y-6">
              <TokenDetails address={id} />

              <Card>
                <CardHeader>
                  <CardTitle>Graphique de prix</CardTitle>
                </CardHeader>
                <CardContent>
                  <CoinPriceChart
                    coinId={id}
                    coinName={tokenData.name}
                    coinSymbol={tokenData.symbol}
                    currentPrice={tokenData.price}
                    priceChange24h={tokenData.priceChange24h}
                  />
                </CardContent>
              </Card>

              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList>
                  <TabsTrigger value="overview">Aperçu</TabsTrigger>
                  <TabsTrigger value="transactions">Transactions</TabsTrigger>
                  <TabsTrigger value="holders">Détenteurs</TabsTrigger>
                </TabsList>
                <TabsContent value="overview" className="space-y-4 mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Description</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p>{tokenData.description || "Aucune description disponible pour ce token."}</p>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="transactions" className="space-y-4 mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Transactions récentes</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <TokenTransactionList address={id} />
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="holders" className="space-y-4 mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Principaux détenteurs</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground">
                        Fonctionnalité en cours de développement. Revenez bientôt pour voir les principaux détenteurs de
                        ce token.
                      </p>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Trading</CardTitle>
                </CardHeader>
                <CardContent>
                  <TokenTradePanel
                    tokenAddress={id}
                    tokenSymbol={tokenData.symbol}
                    tokenDecimals={tokenData.decimals || 9}
                    tokenPrice={tokenData.price}
                  />
                </CardContent>
              </Card>

              <SimilarTokens address={id} />
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

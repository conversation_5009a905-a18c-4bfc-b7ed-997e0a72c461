"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ArrowRight, TrendingUp, Zap } from "lucide-react"
import { useTokenRegistry } from "@/lib/token-registry"
import Header from "@/components/header"
import { tokenConfig } from "@/config/token-config"
import type { CoinData } from "@/lib/coingecko-service"
import coingeckoService from "@/lib/coingecko-service"
import Roadmap from "@/components/home/<USER>"
import BlockchainMemecoins from "@/components/home/<USER>"
import BlockchainNewTokens from "@/components/home/<USER>"
import MarketCarousel from "@/components/home/<USER>"
import MemecoinTicker from "@/components/home/<USER>"
import UserTokensTicker from "@/components/home/<USER>"
import { toast } from "@/components/ui/use-toast"

// Données de secours pour les tests et en cas d'erreur API
const fallbackCoins: CoinData[] = [
  {
    id: "bitcoin",
    symbol: "btc",
    name: "Bitcoin",
    image: "/placeholder.svg?height=32&width=32",
    current_price: 65000,
    market_cap: 1250000000000,
    market_cap_rank: 1,
    total_volume: 25000000000,
    price_change_percentage_24h: 2.5,
    circulating_supply: 19000000,
  },
  {
    id: "ethereum",
    symbol: "eth",
    name: "Ethereum",
    image: "/placeholder.svg?height=32&width=32",
    current_price: 3500,
    market_cap: 420000000000,
    market_cap_rank: 2,
    total_volume: 15000000000,
    price_change_percentage_24h: 1.8,
    circulating_supply: 120000000,
  },
  {
    id: "solana",
    symbol: "sol",
    name: "Solana",
    image: "/placeholder.svg?height=32&width=32",
    current_price: 150,
    market_cap: 65000000000,
    market_cap_rank: 5,
    total_volume: 3500000000,
    price_change_percentage_24h: 4.2,
    circulating_supply: 430000000,
    blockchain: "Solana",
  },
]

export default function Home() {
  const { publicKey, connected } = useWallet()
  const router = useRouter()
  const [isAdmin, setIsAdmin] = useState(false)
  const tokens = useTokenRegistry((state) => state.tokens)
  const [topCoins, setTopCoins] = useState<CoinData[]>([])
  const [trendingCoins, setTrendingCoins] = useState<CoinData[]>([])
  const [memeCoins, setMemeCoins] = useState<CoinData[]>([])
  const [solanaCoins, setSolanaCoins] = useState<CoinData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [favorites, setFavorites] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState("trending")
  const [dataSource, setDataSource] = useState<"api" | "fallback">("api")
  const [carouselSpeed, setCarouselSpeed] = useState(8000) // Vitesse de défilement en ms (8 secondes)

  // Charger les données de CoinGecko
  useEffect(() => {
    const loadMarketData = async () => {
      try {
        setLoading(true)
        setError(null)

        // Charger les top coins
        try {
          const topCoinsData = await coingeckoService.getTopCoins(10)
          setTopCoins(topCoinsData)

          // Trier par changement de prix pour obtenir les coins en tendance
          const trendingCoinsData = [...topCoinsData]
            .sort((a, b) => Math.abs(b.price_change_percentage_24h) - Math.abs(a.price_change_percentage_24h))
            .slice(0, 10)
          setTrendingCoins(trendingCoinsData)
        } catch (err) {
          console.error("Error loading top coins:", err)
          setTopCoins(fallbackCoins)
          setTrendingCoins(fallbackCoins)
          setDataSource("fallback")
          toast({
            title: "Erreur de chargement",
            description: "Impossible de charger les données de CoinGecko. Affichage des données de secours.",
            variant: "destructive",
          })
        }

        // Charger les memecoins
        try {
          const memeCoinsData = await coingeckoService.getMemeCoins(10)
          setMemeCoins(memeCoinsData)
        } catch (err) {
          console.error("Error loading meme coins:", err)
        }

        // Charger les coins Solana
        try {
          const solanaCoinsData = await coingeckoService.getSolanaCoins(10)
          setSolanaCoins(solanaCoinsData)
        } catch (err) {
          console.error("Error loading Solana coins:", err)
          setSolanaCoins([fallbackCoins[2]])
        }
      } catch (error) {
        console.error("Error loading market data:", error)
        setError("Impossible de charger les données de marché. Veuillez réessayer plus tard.")
        setDataSource("fallback")
      } finally {
        setLoading(false)
      }
    }

    loadMarketData()
  }, [])

  // Check if current wallet is admin
  useEffect(() => {
    if (publicKey) {
      const adminWallet = tokenConfig.adminWallet
      setIsAdmin(publicKey.toString() === adminWallet)
    } else {
      setIsAdmin(false)
    }
  }, [publicKey])

  // Ajouter/supprimer des favoris
  const toggleFavorite = (id: string) => {
    if (favorites.includes(id)) {
      setFavorites(favorites.filter((fav) => fav !== id))
    } else {
      setFavorites([...favorites, id])
    }
  }

  // Naviguer vers la page de marché pour un token spécifique
  const navigateToMarket = (tokenId: string) => {
    router.push(`/market?token=${tokenId}`)
  }

  return (
    <>
      <Header />
      <main className="flex min-h-screen flex-col">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-b from-black to-background pt-20 pb-16 overflow-hidden">
          <div className="absolute inset-0 z-0 opacity-20">
            <Image
              src="/images/global-finance-logo.png"
              alt="Global Finance"
              fill
              className="object-contain"
              priority
            />
          </div>
          <div className="container relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div className="space-y-6">
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                  <span className="text-[#D4AF37]">GF-beta</span> Platform <br />
                  The Future of Solana Finance
                </h1>
                <p className="text-lg text-gray-300 max-w-lg">
                  Créez, échangez et stakez des tokens personnalisés sur Solana avec notre plateforme complète. Conçue
                  pour les développeurs, les traders et les investisseurs.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button
                    size="lg"
                    className="bg-[#D4AF37] hover:bg-[#B8941F] text-black group relative overflow-hidden"
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-[#D4AF37]/0 via-white/20 to-[#D4AF37]/0 transform -translate-x-full animate-shimmer"></span>
                    <Zap className="mr-2 h-5 w-5" />
                    <Link href="/token-factory">Créer un Token Quantum</Link>
                  </Button>
                  <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10">
                    <Link href="/market">Explorer le Marché</Link>
                  </Button>
                  <Button size="lg" variant="secondary">
                    <Link href="/memecoin-launchpad">Memecoin Launchpad</Link>
                  </Button>
                  {isAdmin && (
                    <Button size="lg" variant="secondary">
                      <Link href="/admin">Admin Dashboard</Link>
                    </Button>
                  )}
                </div>
              </div>
              <div className="relative aspect-video rounded-lg overflow-hidden shadow-xl border border-gray-800">
                {/* YouTube video embed */}
                <iframe
                  width="100%"
                  height="100%"
                  src="https://www.youtube.com/embed/kfO2BgOPu2o?si=nKQw4gK1sRMgLob-"
                  title="GF-beta Platform Introduction"
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  className="absolute inset-0"
                ></iframe>
              </div>
            </div>
          </div>
        </section>

        {/* Bandes passantes pour les memecoins et tokens utilisateur */}
        <section className="py-6 bg-gradient-to-r from-black/80 to-background/80 border-y border-gray-800">
          <div className="container">
            {/* Tokens créés par les utilisateurs (Premium) */}
            <UserTokensTicker />

            {/* Memecoins Solana */}
            <MemecoinTicker blockchain="solana" title="Memecoins Solana Trending" className="mb-4" />

            {/* Memecoins BNB */}
            <MemecoinTicker blockchain="bnb" title="Memecoins BNB Chain Trending" className="mb-4" />

            {/* Tous les memecoins (Premium) */}
            <MemecoinTicker blockchain="all" title="Top Memecoins Multi-chain" isPremium={true} limit={30} />
          </div>
        </section>

        {/* Blockchain Data Section */}
        <section className="py-16 bg-black/50">
          <div className="container">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
              <div>
                <h2 className="text-3xl font-bold mb-2">Données Blockchain</h2>
                <p className="text-muted-foreground">
                  Tokens détectés directement depuis les blockchains Solana et BNB Chain
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <BlockchainMemecoins />
              <BlockchainNewTokens />
            </div>
          </div>
        </section>

        {/* Market Trends */}
        <section className="py-16 bg-muted/10">
          <div className="container">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
              <div>
                <h2 className="text-3xl font-bold mb-2">Tendances du Marché</h2>
                <p className="text-muted-foreground">
                  Restez informé des derniers mouvements du marché crypto en temps réel
                </p>
              </div>
              <Link href="/market" className="flex items-center gap-1 text-[#D4AF37] hover:text-[#B8941F] mt-4 md:mt-0">
                Voir tous les marchés <ArrowRight className="h-4 w-4" />
              </Link>
            </div>

            {dataSource === "fallback" && (
              <div className="mb-4 p-2 bg-yellow-500/10 border border-yellow-500/30 rounded-md">
                <p className="text-yellow-500 text-sm">
                  Note: Affichage des données de secours. Les données en direct ne sont pas disponibles pour le moment.
                </p>
              </div>
            )}

            <Tabs defaultValue="trending" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="mb-6 bg-black/20">
                <TabsTrigger
                  value="trending"
                  className="data-[state=active]:bg-[#D4AF37] data-[state=active]:text-black"
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Tendance
                </TabsTrigger>
                <TabsTrigger value="top" className="data-[state=active]:bg-[#D4AF37] data-[state=active]:text-black">
                  Top 10
                </TabsTrigger>
                <TabsTrigger value="solana" className="data-[state=active]:bg-[#D4AF37] data-[state=active]:text-black">
                  Écosystème Solana
                </TabsTrigger>
                <TabsTrigger value="meme" className="data-[state=active]:bg-[#D4AF37] data-[state=active]:text-black">
                  Memecoins
                </TabsTrigger>
              </TabsList>

              <TabsContent value="trending">
                <MarketCarousel
                  coins={trendingCoins}
                  title="Cryptomonnaies en Tendance"
                  onNavigate={navigateToMarket}
                  onToggleFavorite={toggleFavorite}
                  favorites={favorites}
                  loading={loading}
                  autoScrollSpeed={carouselSpeed}
                  itemsToShow={4}
                  showControls={true}
                  className="mb-8"
                />
              </TabsContent>

              <TabsContent value="top">
                <MarketCarousel
                  coins={topCoins}
                  title="Top 10 Cryptomonnaies"
                  onNavigate={navigateToMarket}
                  onToggleFavorite={toggleFavorite}
                  favorites={favorites}
                  loading={loading}
                  autoScrollSpeed={carouselSpeed}
                  itemsToShow={4}
                  showControls={true}
                  className="mb-8"
                />
              </TabsContent>

              <TabsContent value="solana">
                <MarketCarousel
                  coins={solanaCoins}
                  title="Écosystème Solana"
                  onNavigate={navigateToMarket}
                  onToggleFavorite={toggleFavorite}
                  favorites={favorites}
                  loading={loading}
                  blockchain="Solana"
                  showBlockchainBadge={true}
                  autoScrollSpeed={carouselSpeed}
                  itemsToShow={4}
                  showControls={true}
                  className="mb-8"
                />
              </TabsContent>

              <TabsContent value="meme">
                <MarketCarousel
                  coins={memeCoins}
                  title="Memecoins Populaires"
                  onNavigate={navigateToMarket}
                  onToggleFavorite={toggleFavorite}
                  favorites={favorites}
                  loading={loading}
                  isNew={true}
                  showCreationDate={true}
                  autoScrollSpeed={carouselSpeed}
                  itemsToShow={4}
                  showControls={true}
                  className="mb-8"
                />
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Roadmap Section */}
        <section className="py-16 bg-gradient-to-b from-black/50 to-background">
          <div className="container">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Notre Feuille de Route</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Découvrez les étapes clés du développement de la plateforme GF-beta et les fonctionnalités à venir
              </p>
            </div>

            <Roadmap />
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-[#D4AF37]/10">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Prêt à rejoindre l'écosystème Solana ?</h2>
              <p className="text-xl text-muted-foreground mb-8">
                Créez votre propre token, lancez un memecoin ou explorez le marché dès aujourd'hui.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button size="lg" className="bg-[#D4AF37] hover:bg-[#B8941F] text-black group relative overflow-hidden">
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-[#D4AF37]/0 via-white/20 to-[#D4AF37]/0 transform -translate-x-full animate-shimmer"></span>
                  <Zap className="mr-2 h-5 w-5" />
                  <Link href="/token-factory">Créer un Token Quantum</Link>
                </Button>
                <Button size="lg" variant="outline">
                  <Link href="/market">Explorer le Marché</Link>
                </Button>
                <Button size="lg" variant="secondary">
                  <Link href="/memecoin-launchpad">Lancer un Memecoin</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  )
}

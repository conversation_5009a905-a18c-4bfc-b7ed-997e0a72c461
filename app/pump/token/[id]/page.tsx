"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import Image from "next/image"
import { useParams, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { toast } from "@/components/ui/use-toast"
import { formatNumber, shortenAddress } from "@/lib/utils"
import { Info, Zap, ArrowUpRight, Copy, ExternalLink, ChevronLeft } from "lucide-react"
import Header from "@/components/header"
import BondingCurveService from "@/lib/bonding-curve-service"

export default function TokenPage() {
  const params = useParams()
  const router = useRouter()
  const { publicKey, connected } = useWallet()

  const [token, setToken] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  // États pour l'achat/vente
  const [buyAmount, setBuyAmount] = useState("")
  const [sellAmount, setSellAmount] = useState("")
  const [estimatedTokens, setEstimatedTokens] = useState(0)
  const [estimatedSol, setEstimatedSol] = useState(0)
  const [isBuying, setIsBuying] = useState(false)
  const [isSelling, setIsSelling] = useState(false)

  // États pour la migration vers un DEX externe
  const [canMigrate, setCanMigrate] = useState(false)
  const [isMigrating, setIsMigrating] = useState(false)

  // Charger les données du token
  useEffect(() => {
    const loadToken = async () => {
      setIsLoading(true)
      try {
        // Simuler le chargement des données du token
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Générer des données de démonstration
        const tokenId = params.id as string
        const tokenName = tokenId
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ")

        const price = Math.random() * 0.1
        const marketCap = price * (Math.random() * 1000000 + 10000)
        const volume24h = marketCap * (Math.random() * 0.5 + 0.1)
        const totalSupply = marketCap / price
        const circulatingSupply = totalSupply * 0.8

        // Générer l'historique des prix
        const priceHistory = generatePriceHistory(30, price)

        // Générer les transactions récentes
        const recentTrades = generateRecentTrades(10)

        // Générer les détenteurs
        const holders = generateHolders(5)

        setToken({
          id: tokenId,
          name: tokenName,
          symbol: `${tokenName.replace(/\s+/g, "").substring(0, 4).toUpperCase()}BETAGF`,
          image: `/placeholder.svg?height=64&width=64`,
          price,
          priceChange24h: Math.random() * 200 - 50, // Entre -50% et +150%
          marketCap,
          volume24h,
          totalSupply,
          circulatingSupply,
          holders: holders.length,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
          creator: `Creator${Math.floor(Math.random() * 1000)}`,
          description: `${tokenName} is a community-driven token created on the GF-beta Pump platform with a bonding curve mechanism.`,
          contractAddress: `${Math.random().toString(36).substring(2, 15)}BETAGF`,
          priceHistory,
          recentTrades,
          holdersList: holders,
          hardCap: 30000, // 30,000 SOL
          currentRaised: marketCap / price, // En SOL
        })

        // Vérifier si le token peut être migré vers un DEX externe
        setCanMigrate(marketCap > 30000)
      } catch (error) {
        console.error("Error loading token:", error)
        toast({
          title: "Error loading token",
          description: "Failed to load token details. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadToken()
  }, [params.id])

  // Générer l'historique des prix
  const generatePriceHistory = (days: number, currentPrice: number) => {
    const history = []
    const now = Date.now()

    let price = currentPrice / (1 + Math.random() * 2) // Prix initial plus bas

    for (let i = days; i >= 0; i--) {
      const timestamp = now - i * 24 * 60 * 60 * 1000

      // Ajouter une variation aléatoire
      const change = (Math.random() * 0.2 - 0.05) * price
      price += change

      // Assurer que le prix reste positif
      price = Math.max(0.0000001, price)

      history.push({
        timestamp,
        price,
      })
    }

    return history
  }

  // Générer des transactions récentes
  const generateRecentTrades = (count: number) => {
    const trades = []
    const now = Date.now()

    for (let i = 0; i < count; i++) {
      const timestamp = now - i * Math.floor(Math.random() * 3600 * 1000)
      const isBuy = Math.random() > 0.4
      const amount = Math.floor(Math.random() * 100000) + 1000
      const price = Math.random() * 0.0001 + 0.00001

      trades.push({
        id: `trade-${i}`,
        timestamp,
        type: isBuy ? "buy" : "sell",
        amount,
        price,
        value: amount * price,
        wallet: `${Math.random().toString(36).substring(2, 15)}`,
      })
    }

    return trades
  }

  // Générer des détenteurs
  const generateHolders = (count: number) => {
    const holders = []

    for (let i = 0; i < count; i++) {
      const balance = Math.floor(Math.random() * 1000000) + 10000

      holders.push({
        id: `holder-${i}`,
        wallet: `${Math.random().toString(36).substring(2, 15)}`,
        balance,
        percentage: 0, // Sera calculé plus tard
      })
    }

    // Calculer les pourcentages
    const totalBalance = holders.reduce((sum, holder) => sum + holder.balance, 0)
    holders.forEach((holder) => {
      holder.percentage = (holder.balance / totalBalance) * 100
    })

    return holders.sort((a, b) => b.balance - a.balance)
  }

  // Calculer le nombre estimé de tokens à recevoir
  useEffect(() => {
    if (token && buyAmount) {
      const solAmount = Number.parseFloat(buyAmount)
      if (!isNaN(solAmount) && solAmount > 0) {
        // Simuler le calcul basé sur la courbe de liaison
        const currentSupply = token.circulatingSupply
        const k = 1000000 // Facteur k de la courbe

        // Utiliser la formule de la courbe de liaison pour estimer les tokens
        const estimatedAmount = BondingCurveService.calculateTokenAmount(currentSupply, solAmount, k)
        setEstimatedTokens(estimatedAmount)
      } else {
        setEstimatedTokens(0)
      }
    }
  }, [token, buyAmount])

  // Calculer le montant estimé de SOL à recevoir
  useEffect(() => {
    if (token && sellAmount) {
      const tokenAmount = Number.parseFloat(sellAmount)
      if (!isNaN(tokenAmount) && tokenAmount > 0) {
        // Simuler le calcul basé sur la courbe de liaison
        const currentSupply = token.circulatingSupply
        const k = 1000000 // Facteur k de la courbe

        // Utiliser la formule de la courbe de liaison pour estimer les SOL
        const newSupply = currentSupply - tokenAmount
        if (newSupply > 0) {
          const currentPrice = BondingCurveService.calculatePrice(currentSupply, k)
          const newPrice = BondingCurveService.calculatePrice(newSupply, k)
          const avgPrice = (currentPrice + newPrice) / 2
          setEstimatedSol(avgPrice * tokenAmount)
        } else {
          setEstimatedSol(0)
        }
      } else {
        setEstimatedSol(0)
      }
    }
  }, [token, sellAmount])

  // Gérer l'achat de tokens
  const handleBuyTokens = async () => {
    if (!connected || !token) {
      return
    }

    const solAmount = Number.parseFloat(buyAmount)
    if (isNaN(solAmount) || solAmount <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid SOL amount.",
        variant: "destructive",
      })
      return
    }

    setIsBuying(true)

    try {
      // Simuler l'achat de tokens
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Purchase successful!",
        description: `You bought ${formatNumber(estimatedTokens)} ${token.symbol} tokens.`,
        variant: "default",
      })

      // Réinitialiser le formulaire
      setBuyAmount("")

      // Mettre à jour les données du token (simulation)
      setToken((prev) => ({
        ...prev,
        price: prev.price * 1.05, // Augmenter le prix
        marketCap: prev.marketCap * 1.05, // Augmenter la capitalisation
        circulatingSupply: prev.circulatingSupply + estimatedTokens, // Augmenter l'offre en circulation
        currentRaised: prev.currentRaised + solAmount, // Augmenter le montant collecté
      }))
    } catch (error) {
      console.error("Error buying tokens:", error)
      toast({
        title: "Failed to buy tokens",
        description: "An error occurred while buying tokens. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsBuying(false)
    }
  }

  // Gérer la vente de tokens
  const handleSellTokens = async () => {
    if (!connected || !token) {
      return
    }

    const tokenAmount = Number.parseFloat(sellAmount)
    if (isNaN(tokenAmount) || tokenAmount <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid token amount.",
        variant: "destructive",
      })
      return
    }

    setIsSelling(true)

    try {
      // Simuler la vente de tokens
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Sale successful!",
        description: `You sold ${formatNumber(tokenAmount)} ${token.symbol} tokens for ${formatNumber(estimatedSol)} SOL.`,
        variant: "default",
      })

      // Réinitialiser le formulaire
      setSellAmount("")

      // Mettre à jour les données du token (simulation)
      setToken((prev) => ({
        ...prev,
        price: prev.price * 0.95, // Diminuer le prix
        marketCap: prev.marketCap * 0.95, // Diminuer la capitalisation
        circulatingSupply: prev.circulatingSupply - tokenAmount, // Diminuer l'offre en circulation
      }))
    } catch (error) {
      console.error("Error selling tokens:", error)
      toast({
        title: "Failed to sell tokens",
        description: "An error occurred while selling tokens. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSelling(false)
    }
  }

  // Gérer la migration vers un DEX externe
  const handleMigrateToDEX = async () => {
    if (!connected || !token || !canMigrate) {
      return
    }

    setIsMigrating(true)

    try {
      // Simuler la migration
      await new Promise((resolve) => setTimeout(resolve, 3000))

      toast({
        title: "Migration successful!",
        description: `${token.name} has been successfully migrated to Raydium DEX.`,
        variant: "default",
      })

      // Mettre à jour le statut du token
      setToken((prev) => ({
        ...prev,
        migratedToDEX: true,
        dexAddress: `${Math.random().toString(36).substring(2, 15)}`,
      }))
    } catch (error) {
      console.error("Error migrating to DEX:", error)
      toast({
        title: "Failed to migrate to DEX",
        description: "An error occurred while migrating to DEX. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsMigrating(false)
    }
  }

  // Formater la date relative
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHour = Math.floor(diffMin / 60)
    const diffDay = Math.floor(diffHour / 24)

    if (diffDay > 0) {
      return `${diffDay}d ago`
    } else if (diffHour > 0) {
      return `${diffHour}h ago`
    } else if (diffMin > 0) {
      return `${diffMin}m ago`
    } else {
      return `${diffSec}s ago`
    }
  }

  if (isLoading || !token) {
    return (
      <>
        <Header />
        <main className="container py-6">
          <div className="flex justify-center items-center min-h-[60vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#D4AF37]"></div>
          </div>
        </main>
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="flex flex-col gap-8">
          {/* Navigation */}
          <div>
            <Button variant="ghost" onClick={() => router.push("/pump")} className="mb-4">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Pump
            </Button>

            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-100">
                  <Image src={token.image || "/placeholder.svg"} alt={token.name} width={64} height={64} />
                </div>
                <div>
                  <h1 className="text-3xl font-bold flex items-center gap-2">
                    {token.name}
                    {token.migratedToDEX && <Badge className="bg-blue-500">Migrated to DEX</Badge>}
                  </h1>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <span>{token.symbol}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => {
                        navigator.clipboard.writeText(token.contractAddress)
                        toast({
                          title: "Address copied",
                          description: "Token address copied to clipboard",
                        })
                      }}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex flex-col items-end">
                <div className="text-3xl font-bold">${token.price.toFixed(6)}</div>
                <div className={token.priceChange24h >= 0 ? "text-green-500" : "text-red-500"}>
                  {token.priceChange24h >= 0 ? "+" : ""}
                  {token.priceChange24h.toFixed(2)}% (24h)
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Token Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Market Cap</h3>
                      <p className="text-lg font-bold">${formatNumber(token.marketCap)}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Volume (24h)</h3>
                      <p className="text-lg font-bold">${formatNumber(token.volume24h)}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Circulating Supply</h3>
                      <p className="text-lg font-bold">
                        {formatNumber(token.circulatingSupply)} {token.symbol}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Total Supply</h3>
                      <p className="text-lg font-bold">
                        {formatNumber(token.totalSupply)} {token.symbol}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Holders</h3>
                      <p className="text-lg font-bold">{token.holders}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Created</h3>
                      <p className="text-lg font-bold">{formatRelativeTime(token.createdAt)}</p>
                    </div>
                  </div>

                  <Separator className="my-4" />

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">Hard Cap Progress</h3>
                    <div className="space-y-2">
                      <Progress value={(token.currentRaised / token.hardCap) * 100} className="h-2" />
                      <div className="flex justify-between text-sm">
                        <span>{formatNumber(token.currentRaised)} SOL raised</span>
                        <span>{formatNumber(token.hardCap)} SOL hard cap</span>
                      </div>
                    </div>
                  </div>

                  <Separator className="my-4" />

                  <div>
                    <h3 className="text-sm font-medium mb-2">Description</h3>
                    <p className="text-sm text-muted-foreground">{token.description}</p>
                  </div>
                </CardContent>
              </Card>

              <Tabs defaultValue="chart" className="w-full">
                <TabsList className="grid grid-cols-3 w-full">
                  <TabsTrigger value="chart">Price Chart</TabsTrigger>
                  <TabsTrigger value="trades">Recent Trades</TabsTrigger>
                  <TabsTrigger value="holders">Top Holders</TabsTrigger>
                </TabsList>

                <TabsContent value="chart" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Price History</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px] flex items-center justify-center">
                        <p className="text-muted-foreground">Price chart visualization would go here</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="trades" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Trades</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Type</th>
                              <th className="text-right p-2">Amount</th>
                              <th className="text-right p-2">Price</th>
                              <th className="text-right p-2">Value</th>
                              <th className="text-right p-2">Time</th>
                              <th className="text-right p-2">Wallet</th>
                            </tr>
                          </thead>
                          <tbody>
                            {token.recentTrades.map((trade: any) => (
                              <tr key={trade.id} className="border-b hover:bg-muted/50">
                                <td className="p-2">
                                  <Badge className={trade.type === "buy" ? "bg-green-500" : "bg-red-500"}>
                                    {trade.type === "buy" ? "Buy" : "Sell"}
                                  </Badge>
                                </td>
                                <td className="p-2 text-right">
                                  {formatNumber(trade.amount)} {token.symbol}
                                </td>
                                <td className="p-2 text-right">${trade.price.toFixed(6)}</td>
                                <td className="p-2 text-right">${formatNumber(trade.value)}</td>
                                <td className="p-2 text-right">
                                  {formatRelativeTime(new Date(trade.timestamp).toISOString())}
                                </td>
                                <td className="p-2 text-right">{shortenAddress(trade.wallet, 4)}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="holders" className="mt-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Top Holders</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Rank</th>
                              <th className="text-left p-2">Wallet</th>
                              <th className="text-right p-2">Balance</th>
                              <th className="text-right p-2">Percentage</th>
                            </tr>
                          </thead>
                          <tbody>
                            {token.holdersList.map((holder: any, index: number) => (
                              <tr key={holder.id} className="border-b hover:bg-muted/50">
                                <td className="p-2">{index + 1}</td>
                                <td className="p-2">{shortenAddress(holder.wallet, 4)}</td>
                                <td className="p-2 text-right">
                                  {formatNumber(holder.balance)} {token.symbol}
                                </td>
                                <td className="p-2 text-right">{holder.percentage.toFixed(2)}%</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Trade</CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="buy">
                    <TabsList className="grid grid-cols-2 w-full">
                      <TabsTrigger value="buy">Buy</TabsTrigger>
                      <TabsTrigger value="sell">Sell</TabsTrigger>
                    </TabsList>

                    <TabsContent value="buy" className="mt-4 space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Amount to Buy (SOL)</label>
                        <Input
                          type="number"
                          placeholder="0.0"
                          value={buyAmount}
                          onChange={(e) => setBuyAmount(e.target.value)}
                        />

                        {Number.parseFloat(buyAmount) > 0 && (
                          <div className="text-sm">
                            <div className="flex justify-between">
                              <span>Estimated tokens:</span>
                              <span className="font-medium">
                                {formatNumber(estimatedTokens)} {token.symbol}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Average price per token:</span>
                              <span className="font-medium">
                                ${(Number.parseFloat(buyAmount) / estimatedTokens).toFixed(6)}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      <Button
                        onClick={handleBuyTokens}
                        disabled={!connected || !buyAmount || Number.parseFloat(buyAmount) <= 0 || isBuying}
                        className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                      >
                        {isBuying ? (
                          <>
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent border-black"></div>
                            Buying...
                          </>
                        ) : (
                          <>
                            <Zap className="mr-2 h-4 w-4" />
                            Buy Tokens
                          </>
                        )}
                      </Button>
                    </TabsContent>

                    <TabsContent value="sell" className="mt-4 space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Amount to Sell ({token.symbol})</label>
                        <Input
                          type="number"
                          placeholder="0.0"
                          value={sellAmount}
                          onChange={(e) => setSellAmount(e.target.value)}
                        />

                        {Number.parseFloat(sellAmount) > 0 && (
                          <div className="text-sm">
                            <div className="flex justify-between">
                              <span>Estimated SOL:</span>
                              <span className="font-medium">{formatNumber(estimatedSol)} SOL</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Average price per token:</span>
                              <span className="font-medium">
                                ${(estimatedSol / Number.parseFloat(sellAmount)).toFixed(6)}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      <Button
                        onClick={handleSellTokens}
                        disabled={!connected || !sellAmount || Number.parseFloat(sellAmount) <= 0 || isSelling}
                        className="w-full bg-red-500 hover:bg-red-600 text-white"
                      >
                        {isSelling ? (
                          <>
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent border-white"></div>
                            Selling...
                          </>
                        ) : (
                          <>
                            <Zap className="mr-2 h-4 w-4" />
                            Sell Tokens
                          </>
                        )}
                      </Button>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>

              {canMigrate && !token.migratedToDEX && (
                <Card>
                  <CardHeader>
                    <CardTitle>Migrate to DEX</CardTitle>
                    <CardDescription>
                      This token has reached the required market cap and can be migrated to an external DEX.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Migration Process</AlertTitle>
                      <AlertDescription>
                        Migrating to Raydium will lock the liquidity and make the token tradable on a major DEX.
                      </AlertDescription>
                    </Alert>

                    <Button
                      onClick={handleMigrateToDEX}
                      disabled={!connected || isMigrating}
                      className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                    >
                      {isMigrating ? (
                        <>
                          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent border-black"></div>
                          Migrating...
                        </>
                      ) : (
                        <>
                          <ArrowUpRight className="mr-2 h-4 w-4" />
                          Migrate to Raydium
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              )}

              {token.migratedToDEX && (
                <Card>
                  <CardHeader>
                    <CardTitle>External DEX</CardTitle>
                    <CardDescription>This token has been migrated to Raydium DEX.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 bg-muted rounded-lg">
                      <h4 className="font-semibold mb-2">DEX Information</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="text-muted-foreground">DEX:</div>
                        <div className="font-medium">Raydium</div>

                        <div className="text-muted-foreground">Pool Address:</div>
                        <div className="font-medium">{shortenAddress(token.dexAddress, 4)}</div>

                        <div className="text-muted-foreground">Migrated:</div>
                        <div className="font-medium">Just now</div>
                      </div>
                    </div>

                    <Button className="w-full" variant="outline">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View on Raydium
                    </Button>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle>Contract Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Contract Address:</span>
                      <div className="flex items-center">
                        <span className="text-sm font-mono">{shortenAddress(token.contractAddress, 4)}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => {
                            navigator.clipboard.writeText(token.contractAddress)
                            toast({
                              title: "Address copied",
                              description: "Contract address copied to clipboard",
                            })
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Creator:</span>
                      <span className="text-sm font-mono">{shortenAddress(token.creator, 4)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Token Standard:</span>
                      <span className="text-sm">SPL Token</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Decimals:</span>
                      <span className="text-sm">9</span>
                    </div>
                  </div>

                  <div className="mt-4 flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Solscan
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Explorer
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

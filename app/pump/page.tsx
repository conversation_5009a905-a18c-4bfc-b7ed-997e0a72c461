"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { formatNumber, shortenAddress } from "@/lib/utils"
import { TrendingUp, Clock, Info, Zap } from "lucide-react"
import Header from "@/components/header"
import BondingCurveService from "@/lib/bonding-curve-service"
import Link from "next/link"

// Types pour les tokens
interface PumpToken {
  id: string
  name: string
  symbol: string
  image: string
  price: number
  priceChange24h: number
  marketCap: number
  volume24h: number
  holders: number
  createdAt: string
  creator: string
  isNew?: boolean
  isHot?: boolean
}

export default function PumpPage() {
  const { publicKey, connected } = useWallet()
  const router = useRouter()

  // États pour la création de token
  const [tokenName, setTokenName] = useState("")
  const [isCreating, setIsCreating] = useState(false)
  const [showHowItWorks, setShowHowItWorks] = useState(false)

  // États pour les tokens
  const [trendingTokens, setTrendingTokens] = useState<PumpToken[]>([])
  const [newTokens, setNewTokens] = useState<PumpToken[]>([])
  const [isLoadingTokens, setIsLoadingTokens] = useState(true)

  // État pour le token sélectionné
  const [selectedToken, setSelectedToken] = useState<PumpToken | null>(null)
  const [buyAmount, setBuyAmount] = useState("")
  const [estimatedTokens, setEstimatedTokens] = useState(0)
  const [isBuying, setIsBuying] = useState(false)

  // Charger les tokens au chargement de la page
  useEffect(() => {
    const loadTokens = async () => {
      setIsLoadingTokens(true)
      try {
        // Simuler le chargement des tokens depuis une API
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Générer des tokens de démonstration
        const demoTokens = generateDemoTokens(20)

        // Trier par capitalisation boursière pour les trending
        const trending = [...demoTokens].sort((a, b) => b.marketCap - a.marketCap).slice(0, 10)

        // Trier par date de création pour les nouveaux
        const newest = [...demoTokens]
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 10)

        setTrendingTokens(trending)
        setNewTokens(newest)
      } catch (error) {
        console.error("Error loading tokens:", error)
        toast({
          title: "Error loading tokens",
          description: "Failed to load tokens. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingTokens(false)
      }
    }

    loadTokens()
  }, [])

  // Générer des tokens de démonstration
  const generateDemoTokens = (count: number): PumpToken[] => {
    const tokens: PumpToken[] = []
    const suffixes = ["PUMP", "BETAGF", "SOLANA", "MOON", "PEPE", "DOGE", "CAT", "SHIB"]
    const now = new Date()

    for (let i = 0; i < count; i++) {
      const isNew = Math.random() > 0.7
      const createdAt = new Date(now)

      if (isNew) {
        // Créé dans les dernières 24 heures
        createdAt.setHours(createdAt.getHours() - Math.random() * 24)
      } else {
        // Créé dans les derniers 30 jours
        createdAt.setDate(createdAt.getDate() - Math.random() * 30)
      }

      const price = Math.random() * 0.1
      const marketCap = price * (Math.random() * 1000000 + 10000)
      const volume24h = marketCap * (Math.random() * 0.5 + 0.1)

      tokens.push({
        id: `token-${i}`,
        name: `Demo Token ${i}`,
        symbol: `DT${i}${suffixes[Math.floor(Math.random() * suffixes.length)]}`,
        image: `/placeholder.svg?height=64&width=64`,
        price,
        priceChange24h: Math.random() * 200 - 50, // Entre -50% et +150%
        marketCap,
        volume24h,
        holders: Math.floor(Math.random() * 1000) + 10,
        createdAt: createdAt.toISOString(),
        creator: `Creator${i}`,
        isNew,
        isHot: volume24h > 50000,
      })
    }

    return tokens
  }

  // Gérer la création d'un nouveau token
  const handleCreateToken = async () => {
    if (!connected) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to create a token.",
        variant: "destructive",
      })
      return
    }

    if (!tokenName.trim()) {
      toast({
        title: "Invalid token name",
        description: "Please enter a valid token name.",
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)

    try {
      // Simuler la création d'un token
      await new Promise((resolve) => {
        setTimeout(resolve, 3000)
      })

      toast({
        title: "Token created successfully!",
        description: `Your token "${tokenName}" has been created with the BETAGF suffix.`,
        variant: "default",
      })

      // Rediriger vers la page du token
      router.push(`/pump/token/${tokenName.toLowerCase().replace(/\s+/g, "-")}`)
    } catch (error) {
      console.error("Error creating token:", error)
      toast({
        title: "Failed to create token",
        description: "An error occurred while creating your token. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  // Gérer l'achat d'un token
  const handleBuyTokens = async () => {
    if (!connected || !selectedToken) {
      return
    }

    const solAmount = Number.parseFloat(buyAmount)
    if (isNaN(solAmount) || solAmount <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid SOL amount.",
        variant: "destructive",
      })
      return
    }

    setIsBuying(true)

    try {
      // Simuler l'achat de tokens
      await new Promise((resolve) => {
        setTimeout(resolve, 2000)
      })

      toast({
        title: "Purchase successful!",
        description: `You bought ${formatNumber(estimatedTokens)} ${selectedToken.symbol} tokens.`,
        variant: "default",
      })

      // Fermer le modal
      setSelectedToken(null)
      setBuyAmount("")
    } catch (error) {
      console.error("Error buying token:", error)
      toast({
        title: "Failed to buy token",
        description: "An error occurred while buying the token. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsBuying(false)
    }
  }

  // Calculer le nombre estimé de tokens à recevoir
  useEffect(() => {
    if (selectedToken && buyAmount) {
      const solAmount = Number.parseFloat(buyAmount)
      if (!isNaN(solAmount) && solAmount > 0) {
        // Simuler le calcul basé sur la courbe de liaison
        const currentSupply = selectedToken.marketCap / selectedToken.price
        const k = 1000000 // Facteur k de la courbe

        // Utiliser la formule de la courbe de liaison pour estimer les tokens
        const estimatedAmount = BondingCurveService.calculateTokenAmount(currentSupply, solAmount, k)
        setEstimatedTokens(estimatedAmount)
      } else {
        setEstimatedTokens(0)
      }
    }
  }, [selectedToken, buyAmount])

  // Formater la date relative
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHour = Math.floor(diffMin / 60)
    const diffDay = Math.floor(diffHour / 24)

    if (diffDay > 0) {
      return `${diffDay}d ago`
    } else if (diffHour > 0) {
      return `${diffHour}h ago`
    } else if (diffMin > 0) {
      return `${diffMin}m ago`
    } else {
      return `${diffSec}s ago`
    }
  }

  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="flex flex-col gap-8">
          {/* Hero Section */}
          <section className="relative bg-gradient-to-b from-black to-background pt-20 pb-16 overflow-hidden">
            <div className="absolute inset-0 z-0 opacity-20">
              <Image
                src="/images/global-finance-logo.png"
                alt="Global Finance"
                fill
                className="object-contain"
                priority
              />
            </div>
            <div className="container relative z-10">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div className="space-y-6">
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                    <span className="text-[#D4AF37]">GF-beta</span> Pump <br />
                    Fair Launch for Everyone
                  </h1>
                  <p className="text-lg text-gray-300 max-w-lg">
                    Create, buy, and sell tokens with a bonding curve mechanism. Fair launch for everyone.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Button
                      size="lg"
                      className="bg-[#D4AF37] hover:bg-[#B8941F] text-black group relative overflow-hidden"
                    >
                      <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-[#D4AF37]/0 via-white/20 to-[#D4AF37]/0 transform -translate-x-full animate-shimmer"></span>
                      <Zap className="mr-2 h-5 w-5" />
                      <Link href="/token-factory">Create Token</Link>
                    </Button>
                    <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10">
                      <Link href="/market">Explore Market</Link>
                    </Button>
                  </div>
                </div>
                <div className="relative aspect-video rounded-lg overflow-hidden shadow-xl border border-gray-800">
                  {/* YouTube video embed */}
                  <iframe
                    width="100%"
                    height="100%"
                    src="https://www.youtube.com/embed/kfO2BgOPu2o?si=nKQw4gK1sRMgLob-"
                    title="GF-beta Platform Introduction"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    className="absolute inset-0"
                  ></iframe>
                </div>
              </div>
            </div>
          </section>

          {/* Token Lists */}
          <section>
            <Tabs defaultValue="trending">
              <TabsList className="mb-6">
                <TabsTrigger value="trending">
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Trending
                </TabsTrigger>
                <TabsTrigger value="new">
                  <Clock className="mr-2 h-4 w-4" />
                  New Tokens
                </TabsTrigger>
              </TabsList>

              <TabsContent value="trending">
                <h2 className="text-2xl font-bold mb-4">Trending Tokens</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {isLoadingTokens
                    ? Array(6)
                        .fill(0)
                        .map((_, i) => (
                          <Card key={i} className="overflow-hidden">
                            <CardHeader className="pb-2">
                              <div className="flex justify-between">
                                <Skeleton className="h-6 w-24" />
                                <Skeleton className="h-6 w-16" />
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-2">
                                <Skeleton className="h-4 w-full" />
                                <Skeleton className="h-4 w-3/4" />
                                <Skeleton className="h-4 w-1/2" />
                              </div>
                            </CardContent>
                          </Card>
                        ))
                    : trendingTokens.map((token) => (
                        <Card
                          key={token.id}
                          className="overflow-hidden hover:border-[#D4AF37] transition-colors cursor-pointer"
                          onClick={() => setSelectedToken(token)}
                        >
                          <CardHeader className="pb-2">
                            <div className="flex justify-between items-start">
                              <div className="flex items-center gap-2">
                                <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-100">
                                  <Image
                                    src={token.image || "/placeholder.svg"}
                                    alt={token.name}
                                    width={40}
                                    height={40}
                                  />
                                </div>
                                <div>
                                  <CardTitle className="text-lg flex items-center gap-2">
                                    {token.name}
                                    {token.isHot && <Badge className="bg-red-500">Hot</Badge>}
                                  </CardTitle>
                                  <CardDescription>{token.symbol}</CardDescription>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-bold">${token.price.toFixed(6)}</div>
                                <div className={token.priceChange24h >= 0 ? "text-green-500" : "text-red-500"}>
                                  {token.priceChange24h >= 0 ? "+" : ""}
                                  {token.priceChange24h.toFixed(2)}%
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="text-muted-foreground">Market Cap:</div>
                              <div className="text-right font-medium">${formatNumber(token.marketCap)}</div>

                              <div className="text-muted-foreground">Volume (24h):</div>
                              <div className="text-right font-medium">${formatNumber(token.volume24h)}</div>

                              <div className="text-muted-foreground">Holders:</div>
                              <div className="text-right font-medium">{token.holders}</div>

                              <div className="text-muted-foreground">Created:</div>
                              <div className="text-right font-medium">{formatRelativeTime(token.createdAt)}</div>
                            </div>
                          </CardContent>
                          <CardFooter className="pt-0">
                            <Button variant="outline" size="sm" className="w-full">
                              <Zap className="mr-2 h-4 w-4" />
                              Trade Now
                            </Button>
                          </CardFooter>
                        </Card>
                      ))}
                </div>
              </TabsContent>

              <TabsContent value="new">
                <h2 className="text-2xl font-bold mb-4">New Tokens</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {isLoadingTokens
                    ? Array(6)
                        .fill(0)
                        .map((_, i) => (
                          <Card key={i} className="overflow-hidden">
                            <CardHeader className="pb-2">
                              <div className="flex justify-between">
                                <Skeleton className="h-6 w-24" />
                                <Skeleton className="h-6 w-16" />
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-2">
                                <Skeleton className="h-4 w-full" />
                                <Skeleton className="h-4 w-3/4" />
                                <Skeleton className="h-4 w-1/2" />
                              </div>
                            </CardContent>
                          </Card>
                        ))
                    : newTokens.map((token) => (
                        <Card
                          key={token.id}
                          className="overflow-hidden hover:border-[#D4AF37] transition-colors cursor-pointer"
                          onClick={() => setSelectedToken(token)}
                        >
                          <CardHeader className="pb-2">
                            <div className="flex justify-between items-start">
                              <div className="flex items-center gap-2">
                                <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-100">
                                  <Image
                                    src={token.image || "/placeholder.svg"}
                                    alt={token.name}
                                    width={40}
                                    height={40}
                                  />
                                </div>
                                <div>
                                  <CardTitle className="text-lg flex items-center gap-2">
                                    {token.name}
                                    {token.isNew && <Badge className="bg-green-500">New</Badge>}
                                  </CardTitle>
                                  <CardDescription>{token.symbol}</CardDescription>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-bold">${token.price.toFixed(6)}</div>
                                <div className={token.priceChange24h >= 0 ? "text-green-500" : "text-red-500"}>
                                  {token.priceChange24h >= 0 ? "+" : ""}
                                  {token.priceChange24h.toFixed(2)}%
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="text-muted-foreground">Market Cap:</div>
                              <div className="text-right font-medium">${formatNumber(token.marketCap)}</div>

                              <div className="text-muted-foreground">Volume (24h):</div>
                              <div className="text-right font-medium">${formatNumber(token.volume24h)}</div>

                              <div className="text-muted-foreground">Holders:</div>
                              <div className="text-right font-medium">{token.holders}</div>

                              <div className="text-muted-foreground">Created:</div>
                              <div className="text-right font-medium">{formatRelativeTime(token.createdAt)}</div>
                            </div>
                          </CardContent>
                          <CardFooter className="pt-0">
                            <Button variant="outline" size="sm" className="w-full">
                              <Zap className="mr-2 h-4 w-4" />
                              Trade Now
                            </Button>
                          </CardFooter>
                        </Card>
                      ))}
                </div>
              </TabsContent>
            </Tabs>
          </section>
        </div>
      </main>

      {/* Buy Token Dialog */}
      <Dialog open={!!selectedToken} onOpenChange={(open) => !open && setSelectedToken(null)}>
        {selectedToken && (
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-100">
                  <Image
                    src={selectedToken.image || "/placeholder.svg"}
                    alt={selectedToken.name}
                    width={32}
                    height={32}
                  />
                </div>
                {selectedToken.name} ({selectedToken.symbol})
              </DialogTitle>
              <DialogDescription>Buy tokens using the bonding curve mechanism</DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="text-muted-foreground">Current Price:</div>
                <div className="text-right font-medium">${selectedToken.price.toFixed(6)}</div>

                <div className="text-muted-foreground">Market Cap:</div>
                <div className="text-right font-medium">${formatNumber(selectedToken.marketCap)}</div>

                <div className="text-muted-foreground">Creator:</div>
                <div className="text-right font-medium">{shortenAddress(selectedToken.creator, 4)}</div>
              </div>

              <Separator />

              <div className="space-y-2">
                <label className="text-sm font-medium">Amount to Buy (SOL)</label>
                <Input
                  type="number"
                  placeholder="0.0"
                  value={buyAmount}
                  onChange={(e) => setBuyAmount(e.target.value)}
                />

                {Number.parseFloat(buyAmount) > 0 && (
                  <div className="text-sm">
                    <div className="flex justify-between">
                      <span>Estimated tokens:</span>
                      <span className="font-medium">
                        {formatNumber(estimatedTokens)} {selectedToken.symbol}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average price per token:</span>
                      <span className="font-medium">
                        ${(Number.parseFloat(buyAmount) / estimatedTokens).toFixed(6)}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Bonding Curve</AlertTitle>
                <AlertDescription>
                  The price increases as more tokens are purchased. This is a fair launch mechanism.
                </AlertDescription>
              </Alert>
            </div>

            <DialogFooter>
              <Button
                onClick={handleBuyTokens}
                disabled={!connected || !buyAmount || Number.parseFloat(buyAmount) <= 0 || isBuying}
                className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black"
              >
                {isBuying ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent border-black"></div>
                    Buying...
                  </>
                ) : (
                  <>
                    <Zap className="mr-2 h-4 w-4" />
                    Buy Tokens
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>

      {/* How It Works Dialog */}
      <Dialog open={showHowItWorks} onOpenChange={setShowHowItWorks}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl">How It Works</DialogTitle>
            <DialogDescription className="text-center">
              GF-beta Pump allows anyone to create tokens with a fair launch mechanism
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-start gap-4">
              <div className="bg-[#D4AF37] text-black rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                1
              </div>
              <div>
                <h3 className="font-medium">Pick a token name</h3>
                <p className="text-sm text-muted-foreground">
                  Create your own token with a custom name. All tokens will have the BETAGF suffix.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-[#D4AF37] text-black rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                2
              </div>
              <div>
                <h3 className="font-medium">Buy the token on the bonding curve</h3>
                <p className="text-sm text-muted-foreground">
                  The price increases as more tokens are purchased, following a mathematical formula.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-[#D4AF37] text-black rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                3
              </div>
              <div>
                <h3 className="font-medium">Sell at any time to lock in profits or losses</h3>
                <p className="text-sm text-muted-foreground">
                  You can sell your tokens back to the bonding curve at any time.
                </p>
              </div>
            </div>
          </div>

          <DialogFooter className="flex flex-col gap-2">
            <Button
              onClick={() => setShowHowItWorks(false)}
              className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black"
            >
              I'm ready to pump
            </Button>
            <div className="text-xs text-center text-muted-foreground">
              By using this platform, you agree to our terms and conditions and certify that you are over 18.
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

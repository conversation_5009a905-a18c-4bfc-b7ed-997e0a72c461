"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, CheckCircle2 } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface SuccessRedirectProps {
  tokenAddress: string
  tokenName: string
  tokenSymbol: string
  tokenSymbolWithSuffix: string
  tokenDecimals: number
  tokenSupply: number
  network: string
}

export default function SuccessRedirect({
  tokenAddress,
  tokenName,
  tokenSymbol,
  tokenSymbolWithSuffix,
  tokenDecimals,
  tokenSupply,
  network,
}: SuccessRedirectProps) {
  const router = useRouter()
  const [progress, setProgress] = useState(0)
  const [isRedirecting, setIsRedirecting] = useState(true)

  useEffect(() => {
    // Simuler un chargement progressif
    const timer = setTimeout(() => {
      if (progress < 100) {
        setProgress(progress + 10)
      } else {
        setIsRedirecting(false)
      }
    }, 200)

    return () => clearTimeout(timer)
  }, [progress])

  const handleRedirect = () => {
    // Rediriger vers la page de succès avec tous les paramètres nécessaires
    router.push(
      `/token-factory/success?address=${tokenAddress}&name=${encodeURIComponent(
        tokenName,
      )}&symbol=${encodeURIComponent(tokenSymbol)}&symbolWithSuffix=${encodeURIComponent(
        tokenSymbolWithSuffix,
      )}&decimals=${tokenDecimals}&supply=${tokenSupply}&network=${network}`,
    )
  }

  // Redirection automatique une fois le chargement terminé
  useEffect(() => {
    if (progress >= 100 && !isRedirecting) {
      const redirectTimer = setTimeout(() => {
        handleRedirect()
      }, 500)

      return () => clearTimeout(redirectTimer)
    }
  }, [progress, isRedirecting])

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <div className="flex items-center gap-2">
          <CheckCircle2 className="h-6 w-6 text-green-500" />
          <CardTitle>Token créé avec succès!</CardTitle>
        </div>
        <CardDescription>
          Votre token {tokenName} ({tokenSymbolWithSuffix || tokenSymbol}) a été créé sur la blockchain Solana {network}
          .
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isRedirecting ? (
          <>
            <div className="text-center mb-2">
              <p className="text-sm text-muted-foreground">Préparation de la page de succès...</p>
            </div>
            <Progress value={progress} className="h-2" />
          </>
        ) : (
          <>
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
              <p className="text-sm text-amber-800 font-medium">
                Étape importante : Vous allez maintenant être redirigé vers la page de succès où vous pourrez effectuer
                l'achat initial pour lancer votre token sur la plateforme.
              </p>
            </div>
            <Button className="w-full" onClick={handleRedirect}>
              Continuer vers la page de succès
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  )
}

"use client"

import { useSearchParams } from "next/navigation"
import SuccessRedirect from "./success-redirect"

export default function TokenCreationSuccessPage() {
  const searchParams = useSearchParams()

  // Récupérer les paramètres de l'URL
  const tokenAddress = searchParams.get("address") || ""
  const tokenName = searchParams.get("name") || "Mon Token"
  const tokenSymbol = searchParams.get("symbol") || "MTK"
  const tokenDecimals = Number.parseInt(searchParams.get("decimals") || "9")
  const tokenSupply = Number.parseInt(searchParams.get("supply") || "1000000000")
  const network = searchParams.get("network") || "devnet"

  return (
    <div className="container py-12">
      <SuccessRedirect
        tokenAddress={tokenAddress}
        tokenName={tokenName}
        tokenSymbol={tokenSymbol}
        tokenDecimals={tokenDecimals}
        tokenSupply={tokenSupply}
        network={network}
      />
    </div>
  )
}

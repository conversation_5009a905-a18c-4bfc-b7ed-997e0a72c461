"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Loader2, CheckCircle2, <PERSON>ert<PERSON><PERSON>gle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import tokenCreationService from "@/lib/token-creation-service"
import type { TokenCreationParams } from "@/lib/token-types"

interface TokenCreationWorkflowProps {
  tokenParams: TokenCreationParams
  paymentSignature: string
}

export default function TokenCreationWorkflow({ tokenParams, paymentSignature }: TokenCreationWorkflowProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [step, setStep] = useState(1)
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [tokenAddress, setTokenAddress] = useState<string | null>(null)
  const [tokenSymbolWithSuffix, setTokenSymbolWithSuffix] = useState<string | null>(null)

  useEffect(() => {
    const createToken = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Étape 1: Préparation du token
        setStep(1)
        setProgress(20)
        const prepareResult = await tokenCreationService.prepareTokenCreation(tokenParams)

        if (!prepareResult.success || !prepareResult.mintKeypair || !prepareResult.symbolWithSuffix) {
          throw new Error(prepareResult.error || "Échec de la préparation du token")
        }

        // Étape 2: Création du token
        setStep(2)
        setProgress(40)
        const createResult = await tokenCreationService.createToken(
          tokenParams,
          paymentSignature,
          prepareResult.mintKeypair,
          prepareResult.symbolWithSuffix,
        )

        if (!createResult.success || !createResult.tokenAddress) {
          throw new Error(createResult.error || "Échec de la création du token")
        }

        setTokenAddress(createResult.tokenAddress)
        setTokenSymbolWithSuffix(createResult.tokenSymbolWithSuffix || tokenParams.symbol)

        // Étape 3: Finalisation
        setStep(3)
        setProgress(80)

        // Simuler un délai pour montrer la progression
        await new Promise((resolve) => setTimeout(resolve, 1000))

        setProgress(100)
        setStep(4)

        // Rediriger vers la page de succès après un court délai
        setTimeout(() => {
          router.push(
            `/token-factory/success?address=${createResult.tokenAddress}&name=${encodeURIComponent(
              tokenParams.name,
            )}&symbol=${encodeURIComponent(tokenParams.symbol)}&symbolWithSuffix=${encodeURIComponent(
              createResult.tokenSymbolWithSuffix || tokenParams.symbol,
            )}&decimals=${tokenParams.decimals}&supply=${tokenParams.initialSupply}&network=${
              createResult.networkUsed || "devnet"
            }`,
          )
        }, 2000)
      } catch (error: any) {
        console.error("Erreur lors de la création du token:", error)
        setError(error.message || "Une erreur s'est produite lors de la création du token")
        toast({
          title: "Erreur",
          description: error.message || "Une erreur s'est produite lors de la création du token",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    createToken()
  }, [tokenParams, paymentSignature, router, toast])

  const getStepTitle = () => {
    switch (step) {
      case 1:
        return "Préparation du token"
      case 2:
        return "Création du token"
      case 3:
        return "Finalisation"
      case 4:
        return "Token créé avec succès!"
      default:
        return "Création du token"
    }
  }

  const getStepDescription = () => {
    switch (step) {
      case 1:
        return "Génération du keypair avec le suffixe demandé..."
      case 2:
        return "Création du token sur la blockchain..."
      case 3:
        return "Finalisation de la création du token..."
      case 4:
        return `Votre token ${tokenParams.name} (${tokenSymbolWithSuffix || tokenParams.symbol}) a été créé avec succès!`
      default:
        return "Création du token en cours..."
    }
  }

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{getStepTitle()}</CardTitle>
        <CardDescription>{getStepDescription()}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error ? (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md flex items-start gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-red-800">
              <p className="font-medium mb-1">Erreur lors de la création du token</p>
              <p>{error}</p>
            </div>
          </div>
        ) : (
          <>
            <Progress value={progress} className="h-2" />
            <div className="flex justify-center">
              {isLoading ? (
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              ) : (
                <CheckCircle2 className="h-8 w-8 text-green-600" />
              )}
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {error ? (
          <Button variant="outline" onClick={() => window.history.back()}>
            Retour
          </Button>
        ) : step === 4 ? (
          <Button
            onClick={() =>
              router.push(
                `/token-factory/success?address=${tokenAddress}&name=${encodeURIComponent(
                  tokenParams.name,
                )}&symbol=${encodeURIComponent(tokenParams.symbol)}&symbolWithSuffix=${encodeURIComponent(
                  tokenSymbolWithSuffix || tokenParams.symbol,
                )}&decimals=${tokenParams.decimals}&supply=${tokenParams.initialSupply}&network=devnet`,
              )
            }
          >
            Continuer
          </Button>
        ) : null}
      </CardFooter>
    </Card>
  )
}

"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { Sparkles, Loader2 } from "lucide-react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import tokenCreationService from "@/lib/token-creation-service"
import type { TokenCreationParams } from "@/lib/token-types"

export default function CreateTokenPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { publicKey, connected, connecting, signTransaction, signAllTransactions } = useWallet()

  const [tokenParams, setTokenParams] = useState<TokenCreationParams>({
    name: "",
    symbol: "",
    decimals: 9,
    initialSupply: 1000000000,
    ownerAddress: "", // Sera rempli lors de la connexion du wallet
    description: "",
    website: "",
    twitter: "",
    telegram: "",
    isAIGenerated: false, // Par défaut, le token n'est pas généré par IA
  })

  const [isCreating, setIsCreating] = useState(false)
  const [creationStep, setCreationStep] = useState<string>("")
  const [error, setError] = useState<string | null>(null)

  // Mettre à jour l'adresse du propriétaire lorsque le wallet est connecté
  useEffect(() => {
    if (publicKey) {
      setTokenParams((prev) => ({
        ...prev,
        ownerAddress: publicKey.toString(),
      }))
    }
  }, [publicKey])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target

    if (type === "number") {
      setTokenParams({
        ...tokenParams,
        [name]: Number.parseFloat(value),
      })
    } else {
      setTokenParams({
        ...tokenParams,
        [name]: value,
      })
    }
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setTokenParams({
      ...tokenParams,
      [name]: checked,
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)
    setError(null)
    setCreationStep("Préparation de la création du token...")

    try {
      // Validation minimale
      if (!tokenParams.name || !tokenParams.symbol) {
        throw new Error("Veuillez remplir tous les champs obligatoires.")
      }

      // Vérifier si le wallet est connecté
      if (!connected || !publicKey || !signTransaction || !signAllTransactions) {
        throw new Error("Veuillez connecter votre wallet avant de créer un token.")
      }

      // Mettre à jour l'adresse du propriétaire
      const updatedParams = {
        ...tokenParams,
        ownerAddress: publicKey.toString(),
      }

      console.log("Creating token with params:", updatedParams)

      // Créer le token
      setCreationStep("Création des transactions...")
      const result = await tokenCreationService.createToken(updatedParams)

      if (!result.success || !result.transactions) {
        throw new Error(result.error || "Échec de la création du token")
      }

      // Signer toutes les transactions
      setCreationStep("Signature des transactions...")
      const signedTransactions = await signAllTransactions(result.transactions)

      // Envoyer les transactions
      setCreationStep("Envoi des transactions à la blockchain...")
      const sendResult = await tokenCreationService.sendTransactions(signedTransactions)

      if (!sendResult.success) {
        throw new Error(sendResult.error || "Échec de l'envoi des transactions")
      }

      console.log("Token created successfully:", result.tokenAddress)
      console.log("Transactions sent with signatures:", sendResult.signatures)

      // Rediriger vers la page de succès
      router.push(`/token-factory/success?address=${result.tokenAddress}&symbol=${result.tokenSymbolWithSuffix}`)
    } catch (err: any) {
      console.error("Erreur lors de la création du token:", err)
      setError(err.message || "Une erreur s'est produite lors de la création du token")
      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors de la création du token",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
      setCreationStep("")
    }
  }

  return (
    <div className="container py-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Créer un nouveau token</CardTitle>
            <CardDescription>
              Remplissez le formulaire ci-dessous pour créer votre propre token sur la blockchain Solana.
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {!connected && (
                <div className="bg-blue-50 p-4 rounded-md mb-4">
                  <div className="flex flex-col items-center justify-center gap-4">
                    <p className="text-center text-blue-800">
                      Connectez votre wallet pour créer un token. Vous pourrez remplir le formulaire en attendant.
                    </p>
                    <WalletMultiButton className="!bg-primary hover:!bg-primary/90" />
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nom du token *</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Ex: My Awesome Token"
                    value={tokenParams.name}
                    onChange={handleInputChange}
                    required
                    disabled={isCreating}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="symbol">Symbole du token *</Label>
                  <Input
                    id="symbol"
                    name="symbol"
                    placeholder="Ex: MAT"
                    value={tokenParams.symbol}
                    onChange={handleInputChange}
                    required
                    maxLength={10}
                    disabled={isCreating}
                  />
                  <p className="text-xs text-muted-foreground">
                    Le symbole sera automatiquement suffixé selon le type de token.
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="decimals">Décimales</Label>
                    <Input
                      id="decimals"
                      name="decimals"
                      type="number"
                      min={0}
                      max={9}
                      value={tokenParams.decimals}
                      onChange={handleInputChange}
                      disabled={isCreating}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="initialSupply">Offre initiale</Label>
                    <Input
                      id="initialSupply"
                      name="initialSupply"
                      type="number"
                      min={1}
                      value={tokenParams.initialSupply}
                      onChange={handleInputChange}
                      disabled={isCreating}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Type de token</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5 flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-purple-500" />
                    <div>
                      <Label htmlFor="isAIGenerated">Token généré par IA</Label>
                      <p className="text-sm text-muted-foreground">
                        Marque ce token comme étant généré par intelligence artificielle (suffixe GFai).
                      </p>
                    </div>
                  </div>
                  <Switch
                    id="isAIGenerated"
                    checked={tokenParams.isAIGenerated}
                    onCheckedChange={(checked) => handleSwitchChange("isAIGenerated", checked)}
                    disabled={isCreating}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Informations supplémentaires (optionnel)</h3>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    name="description"
                    placeholder="Description de votre token"
                    value={tokenParams.description}
                    onChange={handleInputChange}
                    disabled={isCreating}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Site web</Label>
                  <Input
                    id="website"
                    name="website"
                    placeholder="https://example.com"
                    value={tokenParams.website}
                    onChange={handleInputChange}
                    disabled={isCreating}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input
                      id="twitter"
                      name="twitter"
                      placeholder="https://twitter.com/username"
                      value={tokenParams.twitter}
                      onChange={handleInputChange}
                      disabled={isCreating}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="telegram">Telegram</Label>
                    <Input
                      id="telegram"
                      name="telegram"
                      placeholder="https://t.me/username"
                      value={tokenParams.telegram}
                      onChange={handleInputChange}
                      disabled={isCreating}
                    />
                  </div>
                </div>
              </div>

              {isCreating && creationStep && (
                <div className="bg-blue-50 p-4 rounded-md mt-4">
                  <div className="flex flex-col items-center justify-center gap-2">
                    <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                    <p className="text-center text-blue-800">{creationStep}</p>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => router.push("/token-factory")} disabled={isCreating}>
                Annuler
              </Button>
              <Button type="submit" disabled={isCreating || !connected}>
                {isCreating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Création en cours...
                  </>
                ) : (
                  "Créer le token"
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
}

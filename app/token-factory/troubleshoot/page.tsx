import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import TokenTroubleshooter from "@/components/token-factory/token-troubleshooter"
import RpcConnectionTroubleshooter from "@/components/token-factory/rpc-connection-troubleshooter"
import Base58Diagnostic from "@/components/token-factory/base58-diagnostic"

export default function TroubleshootPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Dépannage de la création de tokens</h1>

      <Tabs defaultValue="base58" className="w-full">
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="base58">Problèmes Base58</TabsTrigger>
          <TabsTrigger value="rpc">Connexion RPC</TabsTrigger>
          <TabsTrigger value="general">Dépannage général</TabsTrigger>
        </TabsList>

        <TabsContent value="base58" className="space-y-6">
          <div className="text-lg font-medium mb-2">Diagnostic des erreurs "Non-base58 character"</div>
          <p className="text-muted-foreground mb-4">
            Utilisez cet outil pour identifier et corriger les problèmes liés aux caractères non-base58 dans vos
            symboles et adresses.
          </p>
          <Base58Diagnostic />
        </TabsContent>

        <TabsContent value="rpc" className="space-y-6">
          <div className="text-lg font-medium mb-2">Dépannage de la connexion RPC</div>
          <p className="text-muted-foreground mb-4">
            Vérifiez et résolvez les problèmes de connexion au réseau Solana.
          </p>
          <RpcConnectionTroubleshooter />
        </TabsContent>

        <TabsContent value="general" className="space-y-6">
          <div className="text-lg font-medium mb-2">Dépannage général</div>
          <p className="text-muted-foreground mb-4">Résolvez les problèmes courants lors de la création de tokens.</p>
          <TokenTroubleshooter />
        </TabsContent>
      </Tabs>
    </div>
  )
}

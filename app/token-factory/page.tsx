"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Sparkles, Rocket, Zap, TrendingUp, Shield, Coins } from "lucide-react"

export default function TokenFactoryPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("create")

  return (
    <div className="container py-8">
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Créez votre propre token en quelques minutes</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Lancez votre token sur Solana sans code, sans frais cachés, et avec un suffixe MMGF unique.
          </p>
        </div>

        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="create">Créer un Token</TabsTrigger>
            <TabsTrigger value="features">Fonctionnalités</TabsTrigger>
            <TabsTrigger value="examples">Exemples</TabsTrigger>
          </TabsList>
          <TabsContent value="create" className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-2 border-primary">
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Rocket className="h-6 w-6 text-primary" />
                    <CardTitle>Token Standard</CardTitle>
                  </div>
                  <CardDescription>
                    Créez un token standard avec toutes les fonctionnalités essentielles.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Suffixe MMGF unique
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Mintable et Burnable
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Achat initial intégré
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Tableau de bord de suivi
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Support de la communauté
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button className="w-full" onClick={() => router.push("/token-factory/create")}>
                    Créer un Token Standard
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-6 w-6 text-purple-500" />
                    <CardTitle>Token IA</CardTitle>
                  </div>
                  <CardDescription>Laissez l'IA générer un token unique basé sur votre description.</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Génération par IA
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Suffixe MMGF unique
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Toutes les fonctionnalités standard
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Description et image générées
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckIcon /> Tokenomics optimisés
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full" onClick={() => router.push("/token-factory/ai-creator")}>
                    Créer avec l'IA
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="features" className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FeatureCard
                icon={<Shield className="h-6 w-6 text-blue-500" />}
                title="Sécurité Maximale"
                description="Tous les tokens sont créés avec des paramètres de sécurité optimaux et un suffixe MMGF unique."
              />
              <FeatureCard
                icon={<Zap className="h-6 w-6 text-yellow-500" />}
                title="Création Rapide"
                description="Créez votre token en quelques minutes, sans code et sans connaissances techniques."
              />
              <FeatureCard
                icon={<TrendingUp className="h-6 w-6 text-green-500" />}
                title="Achat Initial"
                description="Système d'achat initial intégré pour lancer votre token avec une liquidité initiale."
              />
              <FeatureCard
                icon={<Coins className="h-6 w-6 text-purple-500" />}
                title="Tokenomics Flexibles"
                description="Personnalisez l'offre, les décimales et d'autres paramètres selon vos besoins."
              />
              <FeatureCard
                icon={<Sparkles className="h-6 w-6 text-pink-500" />}
                title="Génération par IA"
                description="Laissez l'IA créer un token unique basé sur votre description ou vos idées."
              />
              <FeatureCard
                icon={<Rocket className="h-6 w-6 text-orange-500" />}
                title="Tableau de Bord"
                description="Suivez les performances de votre token avec un tableau de bord complet et intuitif."
              />
            </div>
          </TabsContent>
          <TabsContent value="examples" className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ExampleTokenCard
                name="DeFi Finance"
                symbol="DEFIMMGF"
                description="Token de finance décentralisée avec des fonctionnalités de staking et de gouvernance."
                tags={["DeFi", "Staking", "Gouvernance"]}
              />
              <ExampleTokenCard
                name="Meme Coin"
                symbol="MEMEMMGF"
                description="Token meme inspiré par la communauté avec une forte présence sur les réseaux sociaux."
                tags={["Meme", "Communauté", "Viral"]}
              />
              <ExampleTokenCard
                name="GameFi Token"
                symbol="GAMEMMGF"
                description="Token utilisé dans un écosystème de jeu play-to-earn avec des récompenses pour les joueurs."
                tags={["GameFi", "P2E", "NFT"]}
              />
              <ExampleTokenCard
                name="AI Protocol"
                symbol="AIPROMMGF"
                description="Token alimentant un protocole d'intelligence artificielle décentralisé."
                tags={["IA", "Protocole", "Tech"]}
              />
              <ExampleTokenCard
                name="Green Energy"
                symbol="GREENMMGF"
                description="Token soutenant des projets d'énergie verte et de développement durable."
                tags={["ESG", "Durable", "Impact"]}
              />
              <ExampleTokenCard
                name="DAO Governance"
                symbol="DAOGMMGF"
                description="Token de gouvernance pour une organisation autonome décentralisée."
                tags={["DAO", "Gouvernance", "Vote"]}
              />
            </div>
          </TabsContent>
        </Tabs>

        <div className="bg-muted p-6 rounded-lg">
          <h2 className="text-2xl font-bold mb-4">Pourquoi choisir notre Token Factory?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Suffixe MMGF Unique</h3>
              <p className="text-muted-foreground">
                Tous nos tokens sont créés avec un suffixe MMGF unique, garantissant leur authenticité et leur
                traçabilité sur la blockchain Solana.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">Achat Initial Intégré</h3>
              <p className="text-muted-foreground">
                Notre système d'achat initial permet de lancer votre token avec une liquidité initiale, facilitant son
                adoption et sa croissance.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">Sécurité et Transparence</h3>
              <p className="text-muted-foreground">
                Tous les tokens sont créés avec des paramètres de sécurité optimaux et sont entièrement transparents sur
                la blockchain.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">Support Communautaire</h3>
              <p className="text-muted-foreground">
                Rejoignez notre communauté active pour obtenir de l'aide, des conseils et des opportunités de promotion
                pour votre token.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function CheckIcon() {
  return <div className="h-4 w-4 rounded-full bg-green-500 flex items-center justify-center text-white">✓</div>
}

function FeatureCard({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          {icon}
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  )
}

function ExampleTokenCard({
  name,
  symbol,
  description,
  tags,
}: {
  name: string
  symbol: string
  description: string
  tags: string[]
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{name}</CardTitle>
        <CardDescription>{symbol}</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-4">{description}</p>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <Badge key={tag} variant="secondary">
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full">
          Voir l'exemple
        </Button>
      </CardFooter>
    </Card>
  )
}

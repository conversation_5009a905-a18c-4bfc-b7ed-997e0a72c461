import { PlatformTokenForm } from "@/components/token-factory/platform-token-form"

export const metadata = {
  title: "Créer un Token Avancé | Plateforme Solana",
  description: "Créez un token avancé avec toutes les fonctionnalités de sécurité et de distribution sur Solana",
}

export default function PlatformTokenPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Créateur de Token Avancé</h1>
          <p className="text-muted-foreground">
            Créez un token personnalisé avec des fonctionnalités avancées de sécurité, de distribution et de lancement.
          </p>
        </div>

        <PlatformTokenForm />
      </div>
    </div>
  )
}

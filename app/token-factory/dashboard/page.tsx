import { TokenFactoryStats } from "@/components/token-factory/token-factory-stats"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Plus, List, BarChart3 } from "lucide-react"

export default function TokenFactoryDashboardPage() {
  return (
    <div className="container py-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">Token Factory Dashboard</h1>
          <p className="text-muted-foreground">G<PERSON>rez et suivez vos tokens sur la blockchain Solana</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/token-factory">
              <Plus className="h-4 w-4 mr-2" />
              Créer un token
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/token-factory/tokens">
              <List className="h-4 w-4 mr-2" />
              Voir tous les tokens
            </Link>
          </Button>
        </div>
      </div>

      <TokenFactoryStats />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <div className="p-6 bg-blue-50 rounded-lg border border-blue-100">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-full">
              <BarChart3 className="h-6 w-6 text-blue-700" />
            </div>
            <h2 className="text-xl font-semibold text-blue-900">Créez votre token avec Bonding Curve</h2>
          </div>
          <p className="text-blue-700 mb-4">
            Les tokens avec Bonding Curve offrent un mécanisme de prix automatique basé sur l'offre, garantissant une
            liquidité constante et une valorisation transparente.
          </p>
          <Button variant="default" asChild>
            <Link href="/token-factory/bonding-curve">Créer un token avec Bonding Curve</Link>
          </Button>
        </div>

        <div className="p-6 bg-purple-50 rounded-lg border border-purple-100">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-purple-100 rounded-full">
              <BarChart3 className="h-6 w-6 text-purple-700" />
            </div>
            <h2 className="text-xl font-semibold text-purple-900">Découvrez les tokens Quantum</h2>
          </div>
          <p className="text-purple-700 mb-4">
            Les tokens Quantum offrent des fonctionnalités avancées comme le staking, la gouvernance et des mécanismes
            de sécurité renforcés pour votre projet.
          </p>
          <Button variant="default" className="bg-purple-600 hover:bg-purple-700" asChild>
            <Link href="/token-quantum/create">Créer un token Quantum</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}

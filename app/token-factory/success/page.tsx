"use client"

import { useEffect, useState } from "react"
import { useR<PERSON>er, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Loader2, CheckCircle2, Copy, ExternalLink } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import InitialTokenPurchase from "@/components/token/initial-token-purchase"
import tokenInitialPurchaseService from "@/lib/token-initial-purchase-service"

export default function TokenSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  const [isLoading, setIsLoading] = useState(true)
  const [hasInitialPurchase, setHasInitialPurchase] = useState(false)

  // Récupérer les paramètres de l'URL
  const tokenAddress = searchParams.get("address") || ""
  const tokenSymbol = searchParams.get("symbol") || ""
  const tokenName = searchParams.get("name") || ""

  // Vérifier si un achat initial a déjà été effectué
  useEffect(() => {
    const checkInitialPurchase = async () => {
      try {
        if (tokenAddress) {
          const hasInitialPurchase = await tokenInitialPurchaseService.hasInitialPurchase(tokenAddress)
          setHasInitialPurchase(hasInitialPurchase)
        }
      } catch (error) {
        console.error("Error checking initial purchase:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkInitialPurchase()
  }, [tokenAddress])

  const handleCopyAddress = () => {
    if (tokenAddress) {
      navigator.clipboard.writeText(tokenAddress)
      toast({
        title: "Adresse copiée",
        description: "L'adresse du token a été copiée dans le presse-papier.",
      })
    }
  }

  const handleViewExplorer = () => {
    if (tokenAddress) {
      window.open(`https://explorer.solana.com/address/${tokenAddress}?cluster=devnet`, "_blank")
    }
  }

  if (isLoading) {
    return (
      <div className="container py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Chargement...</CardTitle>
              <CardDescription>
                Veuillez patienter pendant que nous chargeons les informations du token.
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!tokenAddress || !tokenSymbol) {
    return (
      <div className="container py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Informations manquantes</CardTitle>
              <CardDescription>
                Les informations du token sont manquantes. Veuillez réessayer de créer un token.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive">
                <AlertDescription>
                  Les paramètres nécessaires (adresse du token, symbole) sont manquants dans l'URL.
                </AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter>
              <Button className="w-full" onClick={() => router.push("/token-factory/create")}>
                Créer un nouveau token
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-6 w-6 text-green-500" />
              <CardTitle>Token créé avec succès</CardTitle>
            </div>
            <CardDescription>
              Votre token a été créé sur la blockchain Solana. Effectuez maintenant un achat initial pour activer votre
              token.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-full overflow-hidden border flex items-center justify-center bg-muted">
                <div className="text-2xl font-bold text-muted-foreground">
                  {tokenSymbol.substring(0, 2).toUpperCase()}
                </div>
              </div>
              <div>
                <h3 className="text-xl font-bold">{tokenName || tokenSymbol}</h3>
                <p className="text-muted-foreground">{tokenSymbol}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline">Solana</Badge>
                  <Badge variant="secondary">Devnet</Badge>
                  {hasInitialPurchase ? (
                    <Badge variant="success">Achat initial effectué</Badge>
                  ) : (
                    <Badge variant="destructive">Achat initial requis</Badge>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between bg-muted p-2 rounded-md">
              <code className="text-xs break-all">{tokenAddress}</code>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={handleCopyAddress}>
                  <Copy className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={handleViewExplorer}>
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Tabs defaultValue={hasInitialPurchase ? "overview" : "purchase"}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="purchase">Achat Initial</TabsTrigger>
                <TabsTrigger value="overview">Aperçu</TabsTrigger>
              </TabsList>
              <TabsContent value="purchase" className="pt-4">
                <InitialTokenPurchase tokenAddress={tokenAddress} tokenSymbol={tokenSymbol} tokenName={tokenName} />
              </TabsContent>
              <TabsContent value="overview" className="pt-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-muted p-4 rounded-md">
                      <h3 className="font-medium mb-2">Détails du Token</h3>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Nom:</span> {tokenName || tokenSymbol}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Symbole:</span> {tokenSymbol}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Réseau:</span> Solana Devnet
                        </div>
                        <div>
                          <span className="text-muted-foreground">Statut:</span>{" "}
                          {hasInitialPurchase ? "Actif" : "En attente d'achat initial"}
                        </div>
                      </div>
                    </div>
                    <div className="bg-muted p-4 rounded-md">
                      <h3 className="font-medium mb-2">Prochaines étapes</h3>
                      <ul className="space-y-2 text-sm list-disc list-inside">
                        {!hasInitialPurchase && <li>Effectuer un achat initial pour activer le token</li>}
                        <li>Partager le token avec votre communauté</li>
                        <li>Ajouter de la liquidité sur un DEX</li>
                        <li>Promouvoir votre token sur les réseaux sociaux</li>
                      </ul>
                    </div>
                  </div>

                  {!hasInitialPurchase && (
                    <Alert className="bg-yellow-50 border-yellow-200">
                      <AlertDescription>
                        Vous devez effectuer un achat initial pour activer votre token. Cliquez sur l'onglet "Achat
                        Initial" pour continuer.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => router.push("/token-factory")}>
              Retour à l'accueil
            </Button>
            {hasInitialPurchase ? (
              <Button onClick={() => router.push(`/token-dashboard/${tokenAddress}`)}>Voir le tableau de bord</Button>
            ) : (
              <Button
                onClick={() => {
                  const tabsElement = document.querySelector('[role="tablist"]') as HTMLElement
                  if (tabsElement) {
                    const purchaseTab = tabsElement.querySelector('[value="purchase"]') as HTMLElement
                    if (purchaseTab) {
                      purchaseTab.click()
                    }
                  }
                }}
              >
                Effectuer l'achat initial
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}

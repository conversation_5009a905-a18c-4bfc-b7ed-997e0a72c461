import type { Metadata } from "next"
import RobustTokenCreator from "@/components/token-factory/robust-token-creator"

export const metadata: Metadata = {
  title: "Robust Token Creator | Global Finance",
  description: "Create your own token on Solana with our robust token creator",
}

export default function RobustTokenCreatorPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Robust Token Creator</h1>
        <p className="text-muted-foreground">
          Create your own token on Solana with our simplified and robust token creator
        </p>
      </div>

      <div className="grid gap-8">
        <RobustTokenCreator />
      </div>
    </div>
  )
}

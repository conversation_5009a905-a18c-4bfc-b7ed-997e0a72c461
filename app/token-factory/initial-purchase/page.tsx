"use client"

import { useState } from "react"
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle, ArrowRight, CheckCircle2, Info } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import InitialTokenPurchase from "@/components/token/initial-token-purchase"

export default function InitialPurchasePage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()
  const { connected } = useWallet()
  const [purchaseComplete, setPurchaseComplete] = useState(false)

  // Récupérer les paramètres de l'URL
  const tokenAddress = searchParams.get("address") || ""
  const tokenName = searchParams.get("name") || "Mon Token"
  const tokenSymbol = searchParams.get("symbol") || "MTK"
  const tokenDecimals = Number.parseInt(searchParams.get("decimals") || "9")
  const tokenSupply = Number.parseInt(searchParams.get("supply") || "1000000000")
  const network = searchParams.get("network") || "devnet"

  const handlePurchaseComplete = () => {
    setPurchaseComplete(true)
    toast({
      title: "Achat initial réussi!",
      description: "Votre token est maintenant lancé sur la plateforme.",
    })
  }

  const handleContinue = () => {
    router.push(
      `/token-dashboard/${tokenAddress}?name=${encodeURIComponent(tokenName)}&symbol=${encodeURIComponent(
        tokenSymbol,
      )}&network=${network}`,
    )
  }

  return (
    <div className="container py-8 max-w-4xl mx-auto">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Achat Initial Obligatoire</h1>
        <p className="text-muted-foreground">
          Cette étape est nécessaire pour lancer votre token et activer la bonding curve
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Pourquoi un achat initial?</CardTitle>
              <CardDescription>Comprendre l'importance de cette étape</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  L'achat initial est une étape cruciale qui permet d'initialiser la bonding curve et de créer la
                  première liquidité pour votre token.
                </AlertDescription>
              </Alert>

              <div className="space-y-4 mt-4">
                <div className="flex gap-3">
                  <div className="bg-primary/10 p-2 rounded-full h-8 w-8 flex items-center justify-center">1</div>
                  <div>
                    <h3 className="font-medium">Initialisation de la bonding curve</h3>
                    <p className="text-sm text-muted-foreground">
                      La bonding curve détermine comment le prix de votre token évoluera en fonction de l'offre en
                      circulation.
                    </p>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="bg-primary/10 p-2 rounded-full h-8 w-8 flex items-center justify-center">2</div>
                  <div>
                    <h3 className="font-medium">Création de liquidité</h3>
                    <p className="text-sm text-muted-foreground">
                      L'achat initial crée le premier pool de liquidité, permettant aux autres utilisateurs d'acheter et
                      vendre votre token.
                    </p>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="bg-primary/10 p-2 rounded-full h-8 w-8 flex items-center justify-center">3</div>
                  <div>
                    <h3 className="font-medium">Activation du trading</h3>
                    <p className="text-sm text-muted-foreground">
                      Une fois l'achat initial effectué, votre token sera disponible pour le trading sur la plateforme.
                    </p>
                  </div>
                </div>
              </div>

              {!connected && (
                <Alert variant="warning" className="mt-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Vous devez connecter votre portefeuille pour effectuer l'achat initial.
                  </AlertDescription>
                </Alert>
              )}

              {!connected && (
                <div className="flex justify-center mt-4">
                  <WalletMultiButton className="bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-4 py-2" />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          {purchaseComplete ? (
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-6 w-6 text-green-500" />
                  <CardTitle>Achat initial réussi!</CardTitle>
                </div>
                <CardDescription>Votre token est maintenant lancé sur la plateforme</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-6 bg-green-50 dark:bg-green-900/20 rounded-lg text-center">
                  <CheckCircle2 className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Félicitations!</h3>
                  <p className="text-sm text-muted-foreground">
                    Votre token {tokenName} ({tokenSymbol}) est maintenant actif et prêt pour le trading.
                  </p>
                </div>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-800">
                    <strong>Prochaine étape:</strong> Accédez au tableau de bord de votre token pour suivre ses
                    performances et sa progression vers le listing DEX.
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full" onClick={handleContinue}>
                  Accéder au tableau de bord
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ) : (
            <InitialTokenPurchase
              tokenAddress={tokenAddress}
              tokenSymbol={tokenSymbol}
              tokenName={tokenName}
              onPurchaseComplete={handlePurchaseComplete}
            />
          )}
        </div>
      </div>
    </div>
  )
}

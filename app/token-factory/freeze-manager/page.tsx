"use client"

import { useState, useEffect } from "react"
import TokenFreezeManager from "@/components/token-factory/token-freeze-manager"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { Info, Lock, Unlock } from "lucide-react"

export default function TokenFreezeManagerPage() {
  const router = useRouter()
  const [adminKey, setAdminKey] = useState("")
  const [isAdmin, setIsAdmin] = useState(false)

  // Simuler une vérification d'administrateur
  useEffect(() => {
    const checkAdmin = async () => {
      // Simuler un délai de vérification
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Pour cette démo, nous utilisons une clé d'administration fixe
      setAdminKey("admin_key_123456")
      setIsAdmin(true)
    }

    checkAdmin()
  }, [])

  if (!isAdmin) {
    return (
      <div className="container py-8">
        <Card>
          <CardHeader>
            <CardTitle>Vérification d'administrateur</CardTitle>
            <CardDescription>Vérification des droits d'accès en cours...</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-8 space-y-8">
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-2/3">
          <TokenFreezeManager adminKey={adminKey} />
        </div>

        <div className="md:w-1/3 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Gestion du gel de tokens</CardTitle>
              <CardDescription>Informations importantes</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-800">Fonctionnalité Freezable</AlertTitle>
                <AlertDescription className="text-blue-700">
                  Seuls les tokens créés avec l'option "Freezable" activée peuvent être gelés ou dégelés.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Qu'est-ce que le gel de tokens?</h3>
                <p className="text-sm text-muted-foreground">
                  Le gel de tokens permet de bloquer temporairement les transferts de tokens pour une adresse
                  spécifique. Cette fonctionnalité est utile pour:
                </p>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Prévenir les activités frauduleuses</li>
                  <li>• Bloquer les comptes compromis</li>
                  <li>• Appliquer des restrictions réglementaires</li>
                  <li>• Gérer les périodes de verrouillage</li>
                </ul>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Lock className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">Geler un compte</h3>
                    <p className="text-sm text-muted-foreground">Empêche les transferts de tokens</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Unlock className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">Dégeler un compte</h3>
                    <p className="text-sm text-muted-foreground">Réactive les transferts de tokens</p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => router.push("/token-factory")}>
                Retour à la Token Factory
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Besoin d'aide?</CardTitle>
              <CardDescription>Ressources et support</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Documentation</h3>
                <p className="text-sm text-muted-foreground">
                  Consultez notre documentation complète sur la gestion des tokens sur Solana.
                </p>
                <Button variant="link" className="p-0 h-auto" onClick={() => router.push("/docs/tokens/freeze")}>
                  Voir la documentation
                </Button>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Support</h3>
                <p className="text-sm text-muted-foreground">
                  Besoin d'aide? Notre équipe de support est disponible 24/7.
                </p>
                <Button variant="link" className="p-0 h-auto" onClick={() => router.push("/support")}>
                  Contacter le support
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

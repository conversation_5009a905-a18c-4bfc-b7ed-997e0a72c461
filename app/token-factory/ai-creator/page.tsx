"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, <PERSON>rkles } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import tokenCreationService from "@/lib/token-creation-service"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import type { TokenCreationParams } from "@/lib/token-types"

export default function AITokenCreatorPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { publicKey, connected } = useWallet()

  const [prompt, setPrompt] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [tokenParams, setTokenParams] = useState<TokenCreationParams | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleGenerateToken = async () => {
    if (!prompt) {
      toast({
        title: "Prompt requis",
        description: "Veuillez entrer un prompt pour générer un token",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)
    setError(null)

    try {
      const response = await fetch("/api/ai/generate-token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ prompt }),
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || "Échec de la génération du token")
      }

      setTokenParams(data.tokenParams)
      toast({
        title: "Token généré",
        description: "Les paramètres du token ont été générés avec succès",
      })
    } catch (err: any) {
      console.error("Erreur lors de la génération du token:", err)
      setError(err.message || "Une erreur s'est produite lors de la génération du token")
      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors de la génération du token",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCreateToken = async () => {
    if (!tokenParams) {
      toast({
        title: "Paramètres manquants",
        description: "Veuillez d'abord générer les paramètres du token",
        variant: "destructive",
      })
      return
    }

    if (!connected || !publicKey) {
      toast({
        title: "Wallet non connecté",
        description: "Veuillez connecter votre wallet pour créer un token",
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setError(null)

    try {
      // Mettre à jour l'adresse du propriétaire
      const updatedParams = {
        ...tokenParams,
        ownerAddress: publicKey.toString(),
      }

      // Simuler un paiement
      const simulatedPaymentSignature =
        "5KKsWpXuzpKJUy9PbsJgMoQEYCkFGXdLkTQ9nMnY8zvtYX6adKjUEGfXYrN5qY6ySVvnTuLk3bQhZnAEGiLRzPKJ"

      // Créer le token
      const result = await tokenCreationService.createToken(updatedParams, simulatedPaymentSignature)

      if (!result.success) {
        throw new Error(result.error || "Échec de la création du token")
      }

      // Rediriger vers la page de succès
      router.push(`/token-factory/success?address=${result.tokenAddress}&symbol=${result.tokenSymbolWithSuffix}`)
    } catch (err: any) {
      console.error("Erreur lors de la création du token:", err)
      setError(err.message || "Une erreur s'est produite lors de la création du token")
      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors de la création du token",
        variant: "destructive",
      })
      setIsCreating(false)
    }
  }

  return (
    <div className="container py-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-purple-500" />
              <CardTitle>Créateur de Token IA</CardTitle>
            </div>
            <CardDescription>
              Décrivez le token que vous souhaitez créer et laissez l'IA générer tous les paramètres pour vous.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <label htmlFor="prompt" className="text-sm font-medium">
                Décrivez votre token
              </label>
              <Textarea
                id="prompt"
                placeholder="Ex: Je veux créer un token pour une plateforme de jeux vidéo appelée GameVerse qui permettra aux joueurs d'acheter des objets virtuels et de participer à des tournois."
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                rows={5}
                disabled={isGenerating || isCreating}
                className="resize-none"
              />
              <p className="text-xs text-muted-foreground">
                Soyez aussi descriptif que possible pour obtenir les meilleurs résultats.
              </p>
            </div>

            <Button onClick={handleGenerateToken} disabled={!prompt || isGenerating || isCreating} className="w-full">
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Génération en cours...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Générer les paramètres du token
                </>
              )}
            </Button>

            {tokenParams && (
              <div className="mt-6 space-y-4 border rounded-md p-4 bg-muted/30">
                <h3 className="text-lg font-medium">Paramètres générés</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Nom:</span> {tokenParams.name}
                  </div>
                  <div>
                    <span className="font-medium">Symbole:</span> {tokenParams.symbol}
                  </div>
                  <div>
                    <span className="font-medium">Décimales:</span> {tokenParams.decimals}
                  </div>
                  <div>
                    <span className="font-medium">Offre initiale:</span> {tokenParams.initialSupply.toLocaleString()}
                  </div>
                </div>
                <div className="text-sm">
                  <span className="font-medium">Description:</span> {tokenParams.description}
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Site web:</span> {tokenParams.website}
                  </div>
                  <div>
                    <span className="font-medium">Twitter:</span> {tokenParams.twitter}
                  </div>
                </div>

                <div className="pt-4">
                  {!connected ? (
                    <div className="text-center space-y-2">
                      <p className="text-sm">Connectez votre wallet pour créer ce token</p>
                      <WalletMultiButton className="bg-primary hover:bg-primary/90 text-white rounded-md px-4 py-2" />
                    </div>
                  ) : (
                    <Button onClick={handleCreateToken} disabled={isCreating} className="w-full">
                      {isCreating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Création en cours...
                        </>
                      ) : (
                        "Créer ce token"
                      )}
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => router.push("/token-factory")}
              disabled={isGenerating || isCreating}
            >
              Retour
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}

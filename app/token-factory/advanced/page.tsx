import type { Metadata } from "next"
import AdvancedTokenForm from "@/components/token-factory/advanced-token-form"
import { getAdminKey } from "@/lib/admin-auth"

export const metadata: Metadata = {
  title: "Créer un token avancé | Token Factory",
  description:
    "Créez un token avec des fonctionnalités avancées: frais, taxes, protections anti-bot, impact sur les prix et gouvernance DAO.",
}

export default async function AdvancedTokenPage() {
  // Générer une clé d'admin temporaire pour l'API
  const adminKey = await getAdminKey()

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Créer un token avancé</h1>

      <div className="mb-8">
        <p className="text-lg mb-4">
          Créez un token Solana avec des fonctionnalités avancées pour maximiser la sécurité et la stabilité.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-bold mb-2">Mécanismes de frais</h3>
            <p className="text-sm">
              Frais de base, frais additionnels et taxe anti-gain excessif pour stabiliser le token.
            </p>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-bold mb-2">Protections avancées</h3>
            <p className="text-sm">
              Limites de transaction, protection anti-bot et anti-sandwich pour sécuriser le token.
            </p>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-bold mb-2">Impact sur les prix</h3>
            <p className="text-sm">Mécanismes automatiques d'achat et de vente pour stabiliser le prix du token.</p>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-bold mb-2">Gouvernance DAO</h3>
            <p className="text-sm">Système de gouvernance décentralisé pour les décisions importantes.</p>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-bold mb-2">Wallets spéciaux</h3>
            <p className="text-sm">
              Wallets dédiés pour le burn et l'impact sur les prix, gelés pour plus de sécurité.
            </p>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-bold mb-2">Blacklist</h3>
            <p className="text-sm">Système de blacklist avec possibilité de déblocage via le DAO.</p>
          </div>
        </div>
      </div>

      <AdvancedTokenForm adminKey={adminKey} />
    </div>
  )
}

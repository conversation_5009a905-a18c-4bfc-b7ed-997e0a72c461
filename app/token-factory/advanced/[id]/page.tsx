import type { Metadata } from "next"
import AdvancedTokenDetails from "@/components/token-factory/advanced-token-details"

interface AdvancedTokenPageProps {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: AdvancedTokenPageProps): Promise<Metadata> {
  // Dans une implémentation réelle, nous récupérerions les données du token depuis l'API
  return {
    title: `Token avancé ${params.id} | Token Factory`,
    description: "Détails et gestion d'un token avancé avec fonctionnalités de sécurité et gouvernance DAO.",
  }
}

export default function AdvancedTokenPage({ params }: AdvancedTokenPageProps) {
  return (
    <div className="container py-8">
      <AdvancedTokenDetails tokenAddress={params.id} />
    </div>
  )
}

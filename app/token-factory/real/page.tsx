"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import RealTokenForm from "@/components/token-factory/real-token-form"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { Info, Shield } from "lucide-react"

export default function RealTokenFactoryPage() {
  const { connected } = useWallet()
  const { toast } = useToast()
  const router = useRouter()
  const [adminKey, setAdminKey] = useState("")
  const [isAdmin, setIsAdmin] = useState(false)

  // Simuler une vérification d'administrateur
  // Dans une implémentation réelle, cela serait fait via une API sécurisée
  useEffect(() => {
    const checkAdmin = async () => {
      // Simuler un délai de vérification
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Pour cette démo, nous utilisons une clé d'administration fixe
      // Dans une implémentation réelle, cela serait vérifié côté serveur
      setAdminKey("admin_key_123456")
      setIsAdmin(true)
    }

    checkAdmin()
  }, [])

  if (!isAdmin) {
    return (
      <div className="container py-8">
        <Card>
          <CardHeader>
            <CardTitle>Vérification d'administrateur</CardTitle>
            <CardDescription>Vérification des droits d'accès en cours...</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-8 space-y-8">
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-2/3">
          <RealTokenForm adminKey={adminKey} />
        </div>

        <div className="md:w-1/3 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Création de token réel</CardTitle>
              <CardDescription>Informations importantes</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-800">Transactions réelles</AlertTitle>
                <AlertDescription className="text-blue-700">
                  Ce formulaire crée des tokens réels sur la blockchain Solana. Des frais de transaction réels seront
                  appliqués.
                </AlertDescription>
              </Alert>

              <Alert className="bg-amber-50 border-amber-200">
                <Shield className="h-4 w-4 text-amber-600" />
                <AlertTitle className="text-amber-800">Fonctionnalités permanentes</AlertTitle>
                <AlertDescription className="text-amber-700">
                  Les fonctionnalités comme "Mintable" et "Freezable" sont permanentes et ne peuvent pas être modifiées
                  après la création.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Fonctionnalités disponibles</h3>
                <ul className="text-sm space-y-1">
                  <li>• Création de tokens SPL standards</li>
                  <li>• Contrôle des permissions (mint, freeze)</li>
                  <li>• Sécurité avancée (anti-bot, anti-dump)</li>
                  <li>• Limites de transaction et de portefeuille</li>
                  <li>• Taxe de transfert configurable</li>
                </ul>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => router.push("/token-factory")}>
                Retour à la Token Factory
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Besoin d'aide?</CardTitle>
              <CardDescription>Ressources et support</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Documentation</h3>
                <p className="text-sm text-muted-foreground">
                  Consultez notre documentation complète sur la création de tokens sur Solana.
                </p>
                <Button variant="link" className="p-0 h-auto" onClick={() => router.push("/docs/tokens")}>
                  Voir la documentation
                </Button>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Support</h3>
                <p className="text-sm text-muted-foreground">
                  Besoin d'aide? Notre équipe de support est disponible 24/7.
                </p>
                <Button variant="link" className="p-0 h-auto" onClick={() => router.push("/support")}>
                  Contacter le support
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

import { Suspense } from "react"
import { notFound } from "next/navigation"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Loader2 } from "lucide-react"
import TokenDashboard from "@/components/token/token-dashboard"
import tokenMetricsService from "@/lib/token-metrics-service"

interface TokenPageProps {
  params: {
    address: string
  }
}

export default async function TokenPage({ params }: TokenPageProps) {
  const { address } = params

  // Check if the token exists and get its metrics
  try {
    // Fetch token metrics from the service
    let metrics = await tokenMetricsService.getTokenMetrics(address)

    // If metrics don't exist, calculate and store them
    if (!metrics) {
      metrics = await tokenMetricsService.calculateAndUpdateTokenMetrics(address, 1000000, 179, 69000)
    }

    // Fetch user balance (in a real app, this would be fetched from the blockchain)
    const userBalance = 0 // Placeholder for demo

    return (
      <div className="container mx-auto py-8">
        <Suspense fallback={<DashboardSkeleton />}>
          <TokenDashboard
            tokenAddress={address}
            tokenSymbol="TOKEN" // In a real app, this would be fetched from the blockchain
            tokenName="Demo Token" // In a real app, this would be fetched from the blockchain
            initialMetrics={{
              currentSupply: metrics.totalSupply,
              maxSupply: 1000000000,
              currentPrice: metrics.currentPrice,
              marketCapUsd: metrics.marketCapUsd,
              solPriceUsd: 179,
              dexListingThresholdUsd: 69000,
              dexListingProgress: metrics.dexListingProgress,
              holders: metrics.holders,
              transactions: metrics.transactions,
            }}
            userBalance={userBalance}
          />
        </Suspense>
      </div>
    )
  } catch (error) {
    console.error(`Error fetching token ${address}:`, error)
    return notFound()
  }
}

function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div>
              <Skeleton className="h-6 w-40" />
              <Skeleton className="h-4 w-60 mt-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-4 w-60" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-8 w-full mt-2" />
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-5 w-20" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-4 w-16 mt-1" />
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-4 w-60" />
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

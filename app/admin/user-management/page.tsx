"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Users, UserPlus, Shield, MoreHorizontal, Edit, Trash, Lock, Mail } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function UserManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")

  // Données simulées pour les utilisateurs
  const users = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Admin",
      status: "Active",
      walletAddress: "0x1a2b...3c4d",
      lastLogin: "2023-04-28 10:45:12",
    },
    {
      id: 2,
      name: "Jane Smith",
      email: "<EMAIL>",
      role: "User",
      status: "Active",
      walletAddress: "0x5e6f...7g8h",
      lastLogin: "2023-04-27 15:30:45",
    },
    {
      id: 3,
      name: "Bob Johnson",
      email: "<EMAIL>",
      role: "Moderator",
      status: "Active",
      walletAddress: "0x9i0j...1k2l",
      lastLogin: "2023-04-26 09:15:33",
    },
    {
      id: 4,
      name: "Alice Brown",
      email: "<EMAIL>",
      role: "User",
      status: "Inactive",
      walletAddress: "0x3m4n...5o6p",
      lastLogin: "2023-04-20 14:22:18",
    },
    {
      id: 5,
      name: "Charlie Wilson",
      email: "<EMAIL>",
      role: "User",
      status: "Active",
      walletAddress: "0x7q8r...9s0t",
      lastLogin: "2023-04-28 08:10:05",
    },
  ]

  // Filtrer les utilisateurs en fonction du terme de recherche
  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.walletAddress.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Utilisateurs</h1>
          <p className="text-muted-foreground">Gérer les utilisateurs, les rôles et les permissions</p>
        </div>
        <Button className="flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          Ajouter un utilisateur
        </Button>
      </div>

      <Tabs defaultValue="users" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Utilisateurs
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Rôles et Permissions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Liste des Utilisateurs</CardTitle>
              <CardDescription>Gérer les utilisateurs de la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Rechercher par nom, email ou adresse de portefeuille..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button variant="outline" className="ml-2">
                  Filtres
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Rôle</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Dernière connexion</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              user.role === "Admin" ? "default" : user.role === "Moderator" ? "outline" : "secondary"
                            }
                          >
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={user.status === "Active" ? "success" : "destructive"}>{user.status}</Badge>
                        </TableCell>
                        <TableCell>{user.lastLogin}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Ouvrir le menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem className="flex items-center">
                                <Edit className="mr-2 h-4 w-4" />
                                Modifier
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center">
                                <Lock className="mr-2 h-4 w-4" />
                                Réinitialiser le mot de passe
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center">
                                <Mail className="mr-2 h-4 w-4" />
                                Envoyer un email
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="flex items-center text-destructive">
                                <Trash className="mr-2 h-4 w-4" />
                                Supprimer
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Rôles et Permissions</CardTitle>
              <CardDescription>Gérer les rôles et les permissions des utilisateurs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    id: "admin",
                    name: "Administrateur",
                    description: "Accès complet à toutes les fonctionnalités",
                    permissions: ["Tout"],
                  },
                  {
                    id: "moderator",
                    name: "Modérateur",
                    description: "Peut modérer le contenu et les utilisateurs",
                    permissions: ["Lecture", "Écriture", "Modération"],
                  },
                  {
                    id: "user",
                    name: "Utilisateur",
                    description: "Accès standard à la plateforme",
                    permissions: ["Lecture", "Écriture limitée"],
                  },
                  { id: "guest", name: "Invité", description: "Accès en lecture seule", permissions: ["Lecture"] },
                ].map((role) => (
                  <div key={role.id} className="flex items-center justify-between border-b pb-4">
                    <div>
                      <p className="font-medium">{role.name}</p>
                      <p className="text-sm text-muted-foreground">{role.description}</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {role.permissions.map((permission, i) => (
                          <Badge key={i} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Modifier
                      </Button>
                      {role.id !== "admin" && (
                        <Button variant="outline" size="sm" className="text-destructive">
                          <Trash className="h-4 w-4 mr-1" />
                          Supprimer
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
                <Button className="w-full">
                  <Shield className="h-4 w-4 mr-2" />
                  Créer un nouveau rôle
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

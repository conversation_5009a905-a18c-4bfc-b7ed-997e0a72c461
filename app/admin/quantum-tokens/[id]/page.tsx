import { Suspense } from "react"
import QuantumTokenDetails from "@/components/admin/quantum-token-details"
import QuantumTokenDetailsLoading from "./loading"

interface QuantumTokenDetailsPageProps {
  params: {
    id: string
  }
}

export default function QuantumTokenDetailsPage({ params }: QuantumTokenDetailsPageProps) {
  return (
    <Suspense fallback={<QuantumTokenDetailsLoading />}>
      <QuantumTokenDetails id={params.id} />
    </Suspense>
  )
}

import { Suspense } from "react"
import QuantumTokenList from "@/components/admin/quantum-token-list"
import { Skeleton } from "@/components/ui/skeleton"

export default function QuantumTokensPage() {
  return (
    <Suspense fallback={<QuantumTokensPageLoading />}>
      <QuantumTokenList />
    </Suspense>
  )
}

function QuantumTokensPageLoading() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-36" />
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <Skeleton className="h-10 w-full sm:w-96" />
        <Skeleton className="h-10 w-full sm:w-64" />
      </div>

      <div className="border rounded-lg">
        <div className="p-6 border-b">
          <Skeleton className="h-6 w-48 mb-2" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="p-6">
          <div className="space-y-6">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
        <div className="p-6 border-t">
          <Skeleton className="h-4 w-48" />
        </div>
      </div>
    </div>
  )
}

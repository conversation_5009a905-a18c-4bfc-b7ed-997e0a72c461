"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle, Settings } from "lucide-react"
import PlatformSettings from "@/components/admin/platform-settings"
import NetworkSelector from "@/components/admin/network-selector"
import SolanaConnectionTest from "@/components/solana-connection-test"

export default function PlatformSettingsPage() {
  const [saveStatus, setSaveStatus] = useState<null | "saving" | "success" | "error">(null)

  const handleSave = () => {
    setSaveStatus("saving")
    // Simuler une sauvegarde
    setTimeout(() => {
      setSaveStatus("success")
      setTimeout(() => setSaveStatus(null), 3000)
    }, 1500)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Paramètres de la Plateforme</h1>
          <p className="text-muted-foreground">Configuration des paramètres généraux de la plateforme</p>
        </div>
        <Button onClick={handleSave} disabled={saveStatus === "saving"} className="flex items-center gap-2">
          {saveStatus === "saving" ? "Enregistrement..." : "Enregistrer les modifications"}
          <Settings className="h-4 w-4" />
        </Button>
      </div>

      {saveStatus === "success" && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>Modifications enregistrées</AlertTitle>
          <AlertDescription>Les paramètres de la plateforme ont été mis à jour avec succès.</AlertDescription>
        </Alert>
      )}

      {saveStatus === "error" && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>
            Une erreur est survenue lors de l'enregistrement des paramètres. Veuillez réessayer.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">Général</TabsTrigger>
          <TabsTrigger value="network">Réseau</TabsTrigger>
          <TabsTrigger value="modules">Modules</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres généraux</CardTitle>
              <CardDescription>Configuration de base pour la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              <PlatformSettings />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="network" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuration Réseau</CardTitle>
              <CardDescription>Configurer les paramètres du réseau blockchain</CardDescription>
            </CardHeader>
            <CardContent>
              <NetworkSelector />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test de Connexion</CardTitle>
              <CardDescription>Tester la connexion à la blockchain Solana</CardDescription>
            </CardHeader>
            <CardContent>
              <SolanaConnectionTest />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modules" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Gestion des Modules</CardTitle>
              <CardDescription>Activer ou désactiver les modules de la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    id: "token-factory",
                    name: "Token Factory",
                    description: "Module de création de tokens",
                    enabled: true,
                  },
                  {
                    id: "token-quantum",
                    name: "Token Quantum",
                    description: "Module de tokens avancés",
                    enabled: true,
                  },
                  { id: "nft-gallery", name: "NFT Gallery", description: "Galerie de NFTs", enabled: true },
                  { id: "staking", name: "Staking", description: "Module de staking", enabled: true },
                  { id: "presale", name: "Presale", description: "Module de prévente", enabled: false },
                ].map((module) => (
                  <div key={module.id} className="flex items-center justify-between border-b pb-2">
                    <div>
                      <p className="font-medium">{module.name}</p>
                      <p className="text-sm text-muted-foreground">{module.description}</p>
                    </div>
                    <div className="flex items-center">
                      <div
                        className={`h-3 w-3 rounded-full ${module.enabled ? "bg-green-500" : "bg-red-500"} mr-2`}
                      ></div>
                      <span>{module.enabled ? "Activé" : "Désactivé"}</span>
                      <Button variant="outline" size="sm" className="ml-4">
                        {module.enabled ? "Désactiver" : "Activer"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

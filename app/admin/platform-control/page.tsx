import { Suspense } from "react"
import { <PERSON><PERSON>, TabsContent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

import AdminPlatformSettings from "@/components/admin/platform-settings"
import { PresaleManagement } from "@/components/admin/presale-management"
import { PermissionControl } from "@/components/admin/permission-control"
import { TokenomicsConfiguration } from "@/components/admin/tokenomics-configuration"
import { PlatformMetrics } from "@/components/admin/platform-metrics"

export default function PlatformControlPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Contrôle de la Plateforme</h1>
        <p className="text-muted-foreground">
          G<PERSON><PERSON> tous les aspects de la plateforme, des presales aux permissions utilisateurs
        </p>
      </div>

      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-5 gap-2">
          <TabsTrigger value="dashboard">Tableau de bord</TabsTrigger>
          <TabsTrigger value="presales">Presales</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="tokenomics">Tokenomics</TabsTrigger>
          <TabsTrigger value="settings">Paramètres</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <Suspense fallback={<LoadingState />}>
            <PlatformMetrics />
          </Suspense>
        </TabsContent>

        <TabsContent value="presales" className="space-y-4">
          <Suspense fallback={<LoadingState />}>
            <PresaleManagement />
          </Suspense>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Suspense fallback={<LoadingState />}>
            <PermissionControl />
          </Suspense>
        </TabsContent>

        <TabsContent value="tokenomics" className="space-y-4">
          <Suspense fallback={<LoadingState />}>
            <TokenomicsConfiguration />
          </Suspense>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Suspense fallback={<LoadingState />}>
            <AdminPlatformSettings />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
}

function LoadingState() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Chargement...
        </CardTitle>
        <CardDescription>Veuillez patienter pendant le chargement des données</CardDescription>
      </CardHeader>
      <CardContent className="h-[400px] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Récupération des données en cours...</p>
        </div>
      </CardContent>
    </Card>
  )
}

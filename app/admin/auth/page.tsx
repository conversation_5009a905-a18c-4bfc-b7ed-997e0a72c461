"use client"

import { useEffect, useState } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, ShieldCheck } from "lucide-react"
import { AdminGuard } from "@/components/admin/admin-guard"

export default function AdminAuthPage() {
  const { connected, publicKey } = useWallet()
  const [isAuthenticating, setIsAuthenticating] = useState(false)
  const [authError, setAuthError] = useState<string | null>(null)
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirectPath = searchParams.get("redirect") || "/admin"

  // Check if already authenticated
  useEffect(() => {
    const checkExistingAuth = async () => {
      const adminToken = document.cookie
        .split("; ")
        .find((row) => row.startsWith("admin_session="))
        ?.split("=")[1]

      if (adminToken) {
        try {
          const response = await fetch("/api/admin/auth/check", {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          })

          const data = await response.json()

          if (response.ok && data.isAuthenticated) {
            router.push(redirectPath)
          }
        } catch (error) {
          console.error("Error checking authentication:", error)
        }
      }
    }

    checkExistingAuth()
  }, [router, redirectPath])

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/30">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Admin Authentication</CardTitle>
          <CardDescription>Connect your wallet and sign a message to access the admin dashboard</CardDescription>
        </CardHeader>
        <CardContent>
          {authError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Authentication Error</AlertTitle>
              <AlertDescription>{authError}</AlertDescription>
            </Alert>
          )}

          {!connected ? (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Please connect your wallet to authenticate as an administrator.
              </p>
              <div className="flex justify-center">{/* Wallet adapter connect button will be rendered here */}</div>
            </div>
          ) : (
            <AdminGuard redirectTo="/">
              <div className="space-y-4 text-center">
                <div className="flex items-center justify-center gap-2 text-green-600">
                  <ShieldCheck className="h-6 w-6" />
                  <span className="font-medium">Authentication Successful</span>
                </div>
                <p className="text-sm text-muted-foreground">You have been authenticated as an administrator.</p>
                <Button onClick={() => router.push(redirectPath)} className="w-full">
                  Continue to Admin Dashboard
                </Button>
              </div>
            </AdminGuard>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-xs text-muted-foreground">Access restricted to platform administrators only</p>
        </CardFooter>
      </Card>
    </div>
  )
}

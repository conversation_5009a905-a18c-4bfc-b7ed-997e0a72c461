"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, Check, Info, Save, Shield } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export default function QuantumSettingsPage() {
  const [activeTab, setActiveTab] = useState("general")
  const [saveStatus, setSaveStatus] = useState<null | 'saving' | 'success' | 'error'>(null)
  
  // États pour les paramètres
  const [settings, setSettings] = useState({
    general: {
      tokenPrefix: "QNTM",
      defaultDecimals: 9,
      maxSupply: "**********",
      enableCustomSuffixes: true,
      enableAutoApproval: false,
      defaultFeePercentage: 2.5,
      defaultLockPeriod: 180,
    },
    security: {
      requireKYC: true,
      enableAuditBeforeCreation: true,
      maxDailyCreations: 50,
      enableBlacklist: true,
      securityLevel: "high",
      enableRateLimiting: true,
      autoFlagSuspiciousActivity: true,
    },
    advanced: {
      rpcTimeout: 30000,
      maxRetries: 5,
      batchSize: 100,
      cacheLifetime: 3600,
      logLevel: "info",
      enableDebugMode: false,
      useCustomRPC: false,
      customRPCUrl: "",
    }
  })

  const handleSave = () => {
    setSaveStatus('saving')
    // Simuler une sauvegarde
    setTimeout(() => {
      setSaveStatus('success')
      setTimeout(() => setSaveStatus(null), 3000)
    }, 1500)
  }

  const updateSetting = (category: 'general' | 'security' | 'advanced', key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Paramètres Quantum</h1>
          <p className="text-muted-foreground">Configuration avancée de la plateforme Quantum</p>
        </div>
        <div className="flex items-center gap-2">
          {saveStatus === 'success' && (
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
              <Check className="h-3 w-3" /> Enregistré
            </Badge>
          )}
          <Button 
            onClick={handleSave} 
            disabled={saveStatus === 'saving'}
            className="flex items-center gap-2"
          >
            {saveStatus === 'saving' ? 'Enregistrement...' : 'Enregistrer les modifications'}
            <Save className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Information importante</AlertTitle>
        <AlertDescription>
          Les modifications apportées aux paramètres Quantum peuvent affecter le comportement de la plateforme. 
          Assurez-vous de comprendre l'impact de chaque changement avant de l'appliquer.
        </AlertDescription>
      </Alert>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">Général</TabsTrigger>
          <TabsTrigger value="security">Sécurité</TabsTrigger>
          <TabsTrigger value="advanced">Avancé</TabsTrigger>
        </TabsList>
        
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres généraux des tokens</CardTitle>
              <CardDescription>
                Configuration de base pour la création et la gestion des tokens Quantum
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="tokenPrefix">Préfixe des tokens</Label>
                  <Input 
                    id="tokenPrefix" 
                    value={settings.general.tokenPrefix}
                    onChange={(e) => updateSetting('general', 'tokenPrefix', e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    Préfixe appliqué à tous les tokens Quantum
                  </p>
                </div>
              <div className="space-y-2">
                  <Label htmlFor="defaultDecimals">Décimales par défaut</Label>
                  <Input 
                    id="defaultDecimals" 
                    type="number" 
                    min="0" 
                    max="18"
                    value={settings.general.defaultDecimals}
                    onChange={(e) => updateSetting('general', 'defaultDecimals', Number.parseInt(e.target.value))}
                  />
                  <p className="text-sm text-muted-foreground">
                    Nombre de décimales par défaut pour les nouveaux tokens
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="maxSupply">Approvisionnement maximum par défaut</Label>
                  <Input 
                    id="maxSupply" 
                    value={settings.general.maxSupply}
                    onChange={(e) => updateSetting('general', 'maxSupply', e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    Approvisionnement maximum par défaut pour les nouveaux tokens
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="defaultFeePercentage">Pourcentage de frais par défaut</Label>
                  <div className="pt-2">
                    <Slider 
                      id="defaultFeePercentage"
                      min={0} 
                      max={10} 
                      step={0.1}
                      value={[settings.general.defaultFeePercentage]}
                      onValueChange={(value) => updateSetting('general', 'defaultFeePercentage', value[0])}
                    />
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">0%</span>
                    <span className="text-sm font-medium">{settings.general.defaultFeePercentage}%</span>
                    <span className="text-sm text-muted-foreground">10%</span>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Activer les suffixes personnalisés</Label>
                    <p className="text-sm text-muted-foreground">
                      Permettre aux utilisateurs de définir des suffixes personnalisés pour leurs tokens
                    </p>
                  </div>
                  <Switch 
                    checked={settings.general.enableCustomSuffixes}
                    onCheckedChange={(checked) => updateSetting('general', 'enableCustomSuffixes', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Approbation automatique</Label>
                    <p className="text-sm text-muted-foreground">
                      Approuver automatiquement les nouveaux tokens sans vérification manuelle
                    </p>
                  </div>
                  <Switch 
                    checked={settings.general.enableAutoApproval}
                    onCheckedChange={(checked) => updateSetting('general', 'enableAutoApproval', checked)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="defaultLockPeriod">Période de verrouillage par défaut (jours)</Label>
                  <Input 
                    id="defaultLockPeriod" 
                    type="number" 
                    min="0"
                    value={settings.general.defaultLockPeriod}
                    onChange={(e) => updateSetting('general', 'defaultLockPeriod', Number.parseInt(e.target.value))}
                  />
                  <p className="text-sm text-muted-foreground">
                    Période de verrouillage par défaut pour les nouveaux tokens (en jours)
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Réinitialiser</Button>
              <Button onClick={handleSave}>Enregistrer</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de sécurité</CardTitle>
              <CardDescription>
                Configuration des mesures de sécurité pour la plateforme Quantum
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert variant="destructive" className="bg-red-50 text-red-800 border-red-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Attention</AlertTitle>
                <AlertDescription>
                  La modification des paramètres de sécurité peut affecter la protection de la plateforme. 
                  Procédez avec prudence.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <div className="flex items-center gap-2">
                      <Label>Exiger la vérification KYC</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <Info className="h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="w-80">
                              Exige que les utilisateurs complètent le processus KYC (Know Your Customer) 
                              avant de pouvoir créer des tokens Quantum.
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Les utilisateurs doivent compléter la vérification KYC avant de créer des tokens
                    </p>
                  </div>
                  <Switch 
                    checked={settings.security.requireKYC}
                    onCheckedChange={(checked) => updateSetting('security', 'requireKYC', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Audit avant création</Label>
                    <p className="text-sm text-muted-foreground">
                      Effectuer un audit de sécurité automatique avant la création de chaque token
                    </p>
                  </div>
                  <Switch 
                    checked={settings.security.enableAuditBeforeCreation}
                    onCheckedChange={(checked) => updateSetting('security', 'enableAuditBeforeCreation', checked)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="maxDailyCreations">Limite quotidienne de créations</Label>
                  <Input 
                    id="maxDailyCreations" 
                    type="number" 
                    min="1"
                    value={settings.security.maxDailyCreations}
                    onChange={(e) => updateSetting('security', 'maxDailyCreations', Number.parseInt(e.target.value))}
                  />
                  <p className="text-sm text-muted-foreground">
                    Nombre maximum de tokens pouvant être créés par jour sur la plateforme
                  </p>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Activer la liste noire</Label>
                    <p className="text-sm text-muted-foreground">
                      Permettre la mise en liste noire des adresses et tokens malveillants
                    </p>
                  </div>
                  <Switch 
                    checked={settings.security.enableBlacklist}
                    onCheckedChange={(checked) => updateSetting('security', 'enableBlacklist', checked)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="securityLevel">Niveau de sécurité</Label>
                  <Select 
                    value={settings.security.securityLevel}
                    onValueChange={(value) => updateSetting('security', 'securityLevel', value)}
                  >
                    <SelectTrigger id="securityLevel">
                      <SelectValue placeholder="Sélectionner un niveau" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Bas</SelectItem>
                      <SelectItem value="medium">Moyen</SelectItem>
                      <SelectItem value="high">Élevé</SelectItem>
                      <SelectItem value="maximum">Maximum</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Niveau global de sécurité pour la plateforme Quantum
                  </p>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Limitation de débit</Label>
                    <p className="text-sm text-muted-foreground">
                      Limiter le nombre de requêtes par utilisateur pour prévenir les abus
                    </p>
                  </div>
                  <Switch 
                    checked={settings.security.enableRateLimiting}
                    onCheckedChange={(checked) => updateSetting('security', 'enableRateLimiting', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Signalement automatique d'activité suspecte</Label>
                    <p className="text-sm text-muted-foreground">
                      Détecter et signaler automatiquement les activités suspectes
                    </p>
                  </div>
                  <Switch 
                    checked={settings.security.autoFlagSuspiciousActivity}
                    onCheckedChange={(checked) => updateSetting('security', 'autoFlagSuspiciousActivity', checked)}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Réinitialiser</Button>
              <Button onClick={handleSave} className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Enregistrer les paramètres de sécurité
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres avancés</CardTitle>
              <CardDescription>
                Configuration technique avancée pour les développeurs et administrateurs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Paramètres pour utilisateurs avancés</AlertTitle>
                <AlertDescription>
                  Ces paramètres sont destinés aux administrateurs techniques. Une mauvaise configuration 
                  peut affecter les performances de la plateforme.
                </AlertDescription>
              </Alert>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="rpcTimeout">Timeout RPC (ms)</Label>
                  <Input 
                    id="rpcTimeout" 
                    type="number" 
                    min="1000"
                    value={settings.advanced.rpcTimeout}
                    onChange={(e) => updateSetting('advanced', 'rpcTimeout', Number.parseInt(e.target.value))}
                  />
                  <p className="text-sm text-muted-foreground">
                    Délai d'attente pour les requêtes RPC en millisecondes
                  </p>
                </div>
                
                
                \

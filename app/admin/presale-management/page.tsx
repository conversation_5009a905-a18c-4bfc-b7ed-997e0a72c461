import { Suspense } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { PresaleManagement } from "@/components/admin/presale-management"
import { PresaleStatistics } from "@/components/admin/presale-statistics"
import { PresaleCalendar } from "@/components/admin/presale-calendar"

export default function PresaleManagementPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des Presales</h1>
        <p className="text-muted-foreground">Configurez et gérez les presales de tokens sur la plateforme</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Suspense fallback={<LoadingState />}>
          <PresaleStatistics />
        </Suspense>

        <Suspense fallback={<LoadingState />}>
          <PresaleCalendar />
        </Suspense>
      </div>

      <Suspense fallback={<LoadingState />}>
        <PresaleManagement />
      </Suspense>
    </div>
  )
}

function LoadingState() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Chargement...
        </CardTitle>
        <CardDescription>Veuillez patienter pendant le chargement des données</CardDescription>
      </CardHeader>
      <CardContent className="h-[200px] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Récupération des données en cours...</p>
        </div>
      </CardContent>
    </Card>
  )
}

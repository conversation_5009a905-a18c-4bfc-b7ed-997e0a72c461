"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, BarChart3, CheckCircle, Clock, Cog, Database, FileText, Lock, Shield, Users } from "lucide-react"
import Link from "next/link"

// Import des composants spécifiques
import QuantumTokenManagement from "@/components/admin/quantum-token-management"
import QuantumSecurityDashboard from "@/components/admin/quantum-security-dashboard"
import QuantumAnalytics from "@/components/admin/quantum-analytics"
import QuantumUserManagement from "@/components/admin/quantum-user-management"

export default function QuantumDashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [networkStatus, setNetworkStatus] = useState("connected")

  // Statistiques simulées pour le tableau de bord
  const stats = {
    totalTokens: 127,
    activeTokens: 98,
    pendingApprovals: 12,
    securityAlerts: 3,
    dailyTransactions: 1458,
    averageVolume: "$245,789",
    successRate: "99.2%",
    uptime: "99.98%",
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quantum Token Dashboard</h1>
          <p className="text-muted-foreground">Gestion avancée et surveillance des tokens Quantum</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center">
            <div
              className={`h-3 w-3 rounded-full mr-2 ${networkStatus === "connected" ? "bg-green-500" : "bg-red-500"}`}
            ></div>
            <span className="text-sm text-muted-foreground">
              {networkStatus === "connected" ? "Réseau connecté" : "Réseau déconnecté"}
            </span>
          </div>
          <Button variant="outline" size="sm">
            <Cog className="h-4 w-4 mr-2" />
            Paramètres
          </Button>
          <Button size="sm">
            <Shield className="h-4 w-4 mr-2" />
            Audit de sécurité
          </Button>
        </div>
      </div>

      {/* Alertes système */}
      {stats.securityAlerts > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Alertes de sécurité détectées</AlertTitle>
          <AlertDescription>
            {stats.securityAlerts} alertes de sécurité nécessitent votre attention immédiate.
            <Button variant="link" className="p-0 h-auto ml-2">
              Voir les détails
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total des tokens</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTokens}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeTokens} actifs, {stats.totalTokens - stats.activeTokens} inactifs
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Approbations en attente</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingApprovals}</div>
            <p className="text-xs text-muted-foreground">Temps moyen d'approbation: 2.4 heures</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Transactions quotidiennes</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.dailyTransactions}</div>
            <p className="text-xs text-muted-foreground">Volume: {stats.averageVolume}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Taux de succès</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.successRate}</div>
            <p className="text-xs text-muted-foreground">Uptime: {stats.uptime}</p>
          </CardContent>
        </Card>
      </div>

      {/* Onglets principaux */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="tokens">Gestion des tokens</TabsTrigger>
          <TabsTrigger value="security">Sécurité</TabsTrigger>
          <TabsTrigger value="analytics">Analytiques</TabsTrigger>
          <TabsTrigger value="users">Utilisateurs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Activité récente</CardTitle>
                <CardDescription>Aperçu des dernières activités des tokens Quantum</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="flex items-center justify-between border-b pb-2">
                      <div>
                        <p className="font-medium">Token QNTM{1000 + i} créé</p>
                        <p className="text-sm text-muted-foreground">il y a {i * 3} heures</p>
                      </div>
                      <Button variant="ghost" size="sm">
                        Détails
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Actions rapides</CardTitle>
                <CardDescription>Accès rapide aux fonctionnalités</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Database className="mr-2 h-4 w-4" />
                  Créer un nouveau token
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Générer un rapport
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Lock className="mr-2 h-4 w-4" />
                  Audit de sécurité
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Users className="mr-2 h-4 w-4" />
                  Gérer les permissions
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Cog className="mr-2 h-4 w-4" />
                  Configuration avancée
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>État du système</CardTitle>
              <CardDescription>Surveillance en temps réel des services Quantum</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {[
                  { name: "API Quantum", status: "Opérationnel", uptime: "100%" },
                  { name: "Service de création de tokens", status: "Opérationnel", uptime: "99.8%" },
                  { name: "Validation des transactions", status: "Opérationnel", uptime: "99.9%" },
                  { name: "Stockage des métadonnées", status: "Opérationnel", uptime: "100%" },
                  { name: "Service d'authentification", status: "Opérationnel", uptime: "99.7%" },
                ].map((service, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{service.name}</p>
                      <p className="text-sm text-muted-foreground">Uptime: {service.uptime}</p>
                    </div>
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                      <span>{service.status}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokens">
          <QuantumTokenManagement />
        </TabsContent>

        <TabsContent value="security">
          <QuantumSecurityDashboard />
        </TabsContent>

        <TabsContent value="analytics">
          <QuantumAnalytics />
        </TabsContent>

        <TabsContent value="users">
          <QuantumUserManagement />
        </TabsContent>
      </Tabs>

      {/* Liens rapides */}
      <div className="flex flex-wrap gap-2 mt-4">
        <Link href="/admin/quantum-tokens" className="text-sm text-blue-600 hover:underline">
          Gestion des tokens
        </Link>
        <span className="text-muted-foreground">•</span>
        <Link href="/admin/security" className="text-sm text-blue-600 hover:underline">
          Sécurité
        </Link>
        <span className="text-muted-foreground">•</span>
        <Link href="/admin/dashboard" className="text-sm text-blue-600 hover:underline">
          Dashboard principal
        </Link>
        <span className="text-muted-foreground">•</span>
        <Link href="/admin/suffix-config" className="text-sm text-blue-600 hover:underline">
          Configuration des suffixes
        </Link>
        <span className="text-muted-foreground">•</span>
        <Link href="/admin/env-variables" className="text-sm text-blue-600 hover:underline">
          Variables d'environnement
        </Link>
      </div>
    </div>
  )
}

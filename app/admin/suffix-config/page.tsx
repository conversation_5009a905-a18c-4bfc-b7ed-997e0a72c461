import SuffixConfiguration from "@/components/admin/suffix-configuration"

export default function SuffixConfigPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Configuration du suffixe de token</h1>
      <p className="text-muted-foreground">
        Configurez le suffixe qui sera utilisé pour toutes les adresses de token créées par les utilisateurs
      </p>

      <div className="grid gap-6">
        <SuffixConfiguration />
      </div>
    </div>
  )
}

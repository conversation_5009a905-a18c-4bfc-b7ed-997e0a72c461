import { Suspense } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import EnvVariablesManager from "@/components/admin/env-variables-manager"
import { AdminSidebarNav } from "@/components/admin/admin-sidebar-nav"

export default function EnvManagerPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-2 mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Gestion des Variables d'Environnement</h1>
        <p className="text-muted-foreground">Configurez et gérez les variables d'environnement de la plateforme</p>
      </div>

      <div className="flex flex-col md:flex-row gap-8">
        <div className="md:w-1/5">
          <AdminSidebarNav />
        </div>

        <div className="flex-1">
          <Suspense fallback={<LoadingState />}>
            <EnvVariablesManager />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

function LoadingState() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Chargement...
        </CardTitle>
        <CardDescription>Veuillez patienter pendant le chargement des données</CardDescription>
      </CardHeader>
      <CardContent className="h-[200px] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Récupération des variables d'environnement...</p>
        </div>
      </CardContent>
    </Card>
  )
}

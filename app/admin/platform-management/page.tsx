"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  AlertCircle,
  CheckCircle,
  Settings,
  Shield,
  Database,
  Users,
  Activity,
  Zap,
  Server,
  Globe,
  FileText,
  Lock,
} from "lucide-react"
import PlatformSettings from "@/components/admin/platform-settings"
import NetworkSelector from "@/components/admin/network-selector"
import UserManagement from "@/components/admin/user-management"
import TokenReports from "@/components/admin/token-reports"
import SecurityAudit from "@/components/admin/security-audit"
import SolanaConnectionTest from "@/components/solana-connection-test"
import { useAdminSettings } from "@/hooks/use-admin-settings"
import { Skeleton } from "@/components/ui/skeleton"
import { Progress } from "@/components/ui/progress"
import EnhancedDashboardMetrics from "@/components/admin/enhanced-dashboard-metrics"
import ApiIntegrations from "@/components/admin/api-integrations"
import TelegramBotConfig from "@/components/admin/telegram-bot-config"
import FinancialReports from "@/components/admin/financial-reports"
import UserRoleManagement from "@/components/admin/user-role-management"
import AutomatedTests from "@/components/admin/automated-tests"
import EcosystemTokenConfiguration from "@/components/admin/ecosystem-token-configuration"
import UserTokenConfiguration from "@/components/admin/user-token-configuration"

export default function PlatformManagementPage() {
  const { settings, isLoading, updateSettings } = useAdminSettings()
  const [activeTab, setActiveTab] = useState("general")
  const [systemStatus, setSystemStatus] = useState<"online" | "degraded" | "maintenance">("online")
  const [lastBackup, setLastBackup] = useState<string>("2023-04-28 10:51:23")
  const [backupInProgress, setBackupInProgress] = useState(false)

  const handleBackup = async () => {
    setBackupInProgress(true)
    // Simulate backup process
    await new Promise((resolve) => setTimeout(resolve, 3000))
    setLastBackup(new Date().toLocaleString())
    setBackupInProgress(false)
  }

  if (isLoading) {
    return (
      <div className="space-y-8">
        <Skeleton className="h-12 w-64 mb-6" />
        <div className="grid gap-6 md:grid-cols-2">
          <Skeleton className="h-[200px] rounded-lg" />
          <Skeleton className="h-[200px] rounded-lg" />
        </div>
        <Skeleton className="h-[400px] rounded-lg mt-6" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Gestion de la Plateforme</h1>
        <p className="text-muted-foreground">Outils d'administration complets pour gérer l'ensemble de la plateforme</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">État du Système</CardTitle>
            {systemStatus === "online" ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : systemStatus === "degraded" ? (
              <AlertCircle className="h-4 w-4 text-amber-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold capitalize">{systemStatus}</div>
            <p className="text-xs text-muted-foreground">Tous les services fonctionnent normalement</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Réseau Actif</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{settings?.networkType || "Solana Devnet"}</div>
            <p className="text-xs text-muted-foreground">Réseau blockchain actuel</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dernière Sauvegarde</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{lastBackup}</div>
            <div className="flex items-center justify-between mt-2">
              <p className="text-xs text-muted-foreground">Sauvegarde des données</p>
              <Button size="sm" variant="outline" onClick={handleBackup} disabled={backupInProgress}>
                {backupInProgress ? "En cours..." : "Sauvegarder"}
              </Button>
            </div>
            {backupInProgress && <Progress value={45} className="h-2 mt-2" />}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Version de la Plateforme</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">v1.5.2</div>
            <div className="flex items-center justify-between mt-2">
              <p className="text-xs text-muted-foreground">Dernière version: v1.5.3</p>
              <Button size="sm" variant="outline">
                Mettre à jour
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {settings?.maintenanceMode && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Mode Maintenance Actif</AlertTitle>
          <AlertDescription>
            La plateforme est actuellement en mode maintenance. Seuls les administrateurs peuvent accéder au système.
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
          <TabsTrigger value="general">
            <Settings className="h-4 w-4 mr-2" />
            Général
          </TabsTrigger>
          <TabsTrigger value="network">
            <Globe className="h-4 w-4 mr-2" />
            Réseau
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            Utilisateurs
          </TabsTrigger>
          <TabsTrigger value="tokens">
            <Zap className="h-4 w-4 mr-2" />
            Tokens
          </TabsTrigger>
          <TabsTrigger value="security">
            <Shield className="h-4 w-4 mr-2" />
            Sécurité
          </TabsTrigger>
          <TabsTrigger value="reports">
            <FileText className="h-4 w-4 mr-2" />
            Rapports
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de la Plateforme</CardTitle>
              <CardDescription>Configurer les paramètres généraux de la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              <PlatformSettings />
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Intégrations API</CardTitle>
                <CardDescription>Gérer les connexions API externes et les services</CardDescription>
              </CardHeader>
              <CardContent>
                <ApiIntegrations />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Configuration du Bot Telegram</CardTitle>
                <CardDescription>Configurer le bot Telegram pour les notifications</CardDescription>
              </CardHeader>
              <CardContent>
                <TelegramBotConfig />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Métriques Système</CardTitle>
              <CardDescription>Métriques de performance et d'utilisation de la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              <EnhancedDashboardMetrics />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="network" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuration Réseau</CardTitle>
              <CardDescription>Configurer les paramètres du réseau blockchain</CardDescription>
            </CardHeader>
            <CardContent>
              <NetworkSelector />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test de Connexion</CardTitle>
              <CardDescription>Tester la connexion à la blockchain Solana</CardDescription>
            </CardHeader>
            <CardContent>
              <SolanaConnectionTest />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Tests Réseau Automatisés</CardTitle>
              <CardDescription>
                Exécuter des tests automatisés pour vérifier la fonctionnalité du réseau
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AutomatedTests />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Gestion des Utilisateurs</CardTitle>
              <CardDescription>Gérer les utilisateurs de la plateforme et leurs permissions</CardDescription>
            </CardHeader>
            <CardContent>
              <UserManagement />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Gestion des Rôles</CardTitle>
              <CardDescription>Configurer les rôles et les permissions des utilisateurs</CardDescription>
            </CardHeader>
            <CardContent>
              <UserRoleManagement />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokens" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Configuration des Tokens Écosystème</CardTitle>
                <CardDescription>Configurer les tokens de l'écosystème de la plateforme</CardDescription>
              </CardHeader>
              <CardContent>
                <EcosystemTokenConfiguration />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Configuration des Tokens Utilisateur</CardTitle>
                <CardDescription>Configurer les paramètres des tokens utilisateur</CardDescription>
              </CardHeader>
              <CardContent>
                <UserTokenConfiguration />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Rapports de Tokens</CardTitle>
              <CardDescription>Voir et analyser les performances des tokens</CardDescription>
            </CardHeader>
            <CardContent>
              <TokenReports />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Audit de Sécurité</CardTitle>
              <CardDescription>Exécuter des audits de sécurité et consulter les rapports</CardDescription>
            </CardHeader>
            <CardContent>
              <SecurityAudit />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Journaux d'Accès</CardTitle>
              <CardDescription>Consulter les journaux d'accès au système et les événements de sécurité</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Connexion admin</p>
                    <p className="text-xs text-muted-foreground"><EMAIL></p>
                  </div>
                  <div className="text-xs text-muted-foreground">2023-04-28 10:45:12</div>
                </div>
              </div>
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Paramètres mis à jour</p>
                    <p className="text-xs text-muted-foreground"><EMAIL></p>
                  </div>
                  <div className="text-xs text-muted-foreground">2023-04-28 10:30:05</div>
                </div>
              </div>
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Tentative de connexion échouée</p>
                    <p className="text-xs text-muted-foreground"><EMAIL></p>
                  </div>
                  <div className="text-xs text-muted-foreground">2023-04-28 09:15:33</div>
                </div>
              </div>
              <Button variant="outline" className="w-full">
                Voir tous les journaux
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Rapports Financiers</CardTitle>
              <CardDescription>Consulter les rapports financiers et les analyses</CardDescription>
            </CardHeader>
            <CardContent>
              <FinancialReports />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Rapports Système</CardTitle>
              <CardDescription>Consulter les rapports de performance et d'utilisation du système</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">Utilisateurs Actifs Quotidiens</div>
                  <div className="text-sm">1,245</div>
                </div>
                <Progress value={62} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">Taux de Création de Tokens</div>
                  <div className="text-sm">32/jour</div>
                </div>
                <Progress value={45} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">Volume de Transactions</div>
                  <div className="text-sm">124,532 €</div>
                </div>
                <Progress value={78} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">Disponibilité du Système</div>
                  <div className="text-sm">99.98%</div>
                </div>
                <Progress value={99} className="h-2" />
              </div>

              <Button variant="outline" className="w-full">
                Générer un Rapport Détaillé
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Separator className="my-6" />

      <div className="flex flex-col space-y-4">
        <h2 className="text-xl font-bold">Actions Rapides</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Button variant="outline" className="h-24 flex flex-col items-center justify-center">
            <Lock className="h-6 w-6 mb-2" />
            <span>Mode Maintenance</span>
          </Button>
          <Button variant="outline" className="h-24 flex flex-col items-center justify-center">
            <Database className="h-6 w-6 mb-2" />
            <span>Exporter les Données</span>
          </Button>
          <Button variant="outline" className="h-24 flex flex-col items-center justify-center">
            <Shield className="h-6 w-6 mb-2" />
            <span>Scan de Sécurité</span>
          </Button>
          <Button variant="outline" className="h-24 flex flex-col items-center justify-center">
            <Activity className="h-6 w-6 mb-2" />
            <span>Optimisation Performance</span>
          </Button>
        </div>
      </div>
    </div>
  )
}

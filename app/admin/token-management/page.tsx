"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Coins, Plus, MoreHorizontal, Edit, Trash, Eye, BarChart3, Settings } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function TokenManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")

  // Données simulées pour les tokens
  const tokens = [
    {
      id: 1,
      name: "SOLQUANTUM",
      symbol: "SOLQ",
      supply: "1,000,000,000",
      creator: "0x1a2b...3c4d",
      createdAt: "2023-04-28",
      status: "Active",
      type: "Quantum",
    },
    {
      id: 2,
      name: "MEMEQUANTUM",
      symbol: "MEMQ",
      supply: "500,000,000",
      creator: "0x5e6f...7g8h",
      createdAt: "2023-04-27",
      status: "Active",
      type: "Meme",
    },
    {
      id: 3,
      name: "AIQUANTUM",
      symbol: "AIQ",
      supply: "2,000,000,000",
      creator: "0x9i0j...1k2l",
      createdAt: "2023-04-26",
      status: "Pending",
      type: "Utility",
    },
    {
      id: 4,
      name: "GAMEQUANTUM",
      symbol: "GAMQ",
      supply: "750,000,000",
      creator: "0x3m4n...5o6p",
      createdAt: "2023-04-25",
      status: "Active",
      type: "Gaming",
    },
    {
      id: 5,
      name: "DEFITOKEN",
      symbol: "DEFI",
      supply: "100,000,000",
      creator: "0x7q8r...9s0t",
      createdAt: "2023-04-24",
      status: "Inactive",
      type: "DeFi",
    },
  ]

  // Filtrer les tokens en fonction du terme de recherche
  const filteredTokens = tokens.filter(
    (token) =>
      token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
      token.creator.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Tokens</h1>
          <p className="text-muted-foreground">Gérer les tokens, les paramètres et les configurations</p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Créer un token
        </Button>
      </div>

      <Tabs defaultValue="tokens" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="tokens" className="flex items-center gap-2">
            <Coins className="h-4 w-4" />
            Tokens
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytiques
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Paramètres
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tokens" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Liste des Tokens</CardTitle>
              <CardDescription>Gérer les tokens de la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Rechercher par nom, symbole ou créateur..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button variant="outline" className="ml-2">
                  Filtres
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Symbole</TableHead>
                      <TableHead>Approvisionnement</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Date de création</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTokens.map((token) => (
                      <TableRow key={token.id}>
                        <TableCell className="font-medium">{token.name}</TableCell>
                        <TableCell>{token.symbol}</TableCell>
                        <TableCell>{token.supply}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{token.type}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              token.status === "Active"
                                ? "success"
                                : token.status === "Pending"
                                  ? "warning"
                                  : "destructive"
                            }
                          >
                            {token.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{token.createdAt}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Ouvrir le menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem className="flex items-center">
                                <Eye className="mr-2 h-4 w-4" />
                                Voir les détails
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center">
                                <Edit className="mr-2 h-4 w-4" />
                                Modifier
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center">
                                <BarChart3 className="mr-2 h-4 w-4" />
                                Voir les analytiques
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="flex items-center text-destructive">
                                <Trash className="mr-2 h-4 w-4" />
                                Supprimer
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analytiques des Tokens</CardTitle>
              <CardDescription>Visualiser les performances et l'utilisation des tokens</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] w-full bg-muted/20 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Graphique d'analytiques des tokens</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres des Tokens</CardTitle>
              <CardDescription>Configurer les paramètres globaux des tokens</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    id: "default-supply",
                    name: "Approvisionnement par défaut",
                    value: "1,000,000,000",
                    type: "number",
                  },
                  { id: "default-decimals", name: "Décimales par défaut", value: "9", type: "number" },
                  { id: "default-fee", name: "Frais par défaut", value: "2.5%", type: "percentage" },
                  { id: "require-approval", name: "Exiger l'approbation", value: "Oui", type: "boolean" },
                  { id: "max-daily-creations", name: "Créations quotidiennes maximales", value: "50", type: "number" },
                ].map((setting) => (
                  <div key={setting.id} className="flex items-center justify-between border-b pb-2">
                    <div>
                      <p className="font-medium">{setting.name}</p>
                      <p className="text-sm text-muted-foreground">Paramètre global pour tous les tokens</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{setting.value}</span>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Modifier
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

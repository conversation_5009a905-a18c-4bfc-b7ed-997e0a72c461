"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Shield, Wallet } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function AdminConnect() {
  const [connecting, setConnecting] = useState(false)
  const [error, setError] = useState("")
  const [walletAddress, setWalletAddress] = useState<string | null>(null)
  const router = useRouter()
  const { toast } = useToast()

  // Vérifier si un wallet est déjà connecté au chargement
  useEffect(() => {
    const checkWalletConnection = async () => {
      try {
        // Vérifier si window.solana existe (Phantom wallet)
        if (typeof window !== "undefined" && window.solana?.isPhantom) {
          const response = await window.solana.connect({ onlyIfTrusted: true })
          setWalletAddress(response.publicKey.toString())
        }
      } catch (err) {
        // Ignorer les erreurs - l'utilisateur n'est probablement pas connecté
        console.log("Wallet not connected or authorized yet")
      }
    }

    checkWalletConnection()
  }, [])

  const connectWallet = async () => {
    setConnecting(true)
    setError("")

    try {
      // Vérifier si Phantom est installé
      if (typeof window !== "undefined" && window.solana?.isPhantom) {
        const response = await window.solana.connect()
        const publicKey = response.publicKey.toString()
        setWalletAddress(publicKey)

        // Vérifier si le wallet est un admin
        const adminCheckResponse = await fetch("/api/admin/check-auth", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ walletAddress: publicKey }),
        })

        const data = await adminCheckResponse.json()

        if (adminCheckResponse.ok && data.isAdmin) {
          toast({
            title: "Connexion réussie",
            description: "Vous êtes connecté en tant qu'administrateur",
            variant: "success",
          })

          // Rediriger vers le tableau de bord admin
          router.push("/admin/dashboard")
        } else {
          setError("Ce wallet n'a pas les droits d'administration")
          // Déconnecter le wallet
          if (window.solana?.disconnect) {
            await window.solana.disconnect()
          }
          setWalletAddress(null)
        }
      } else {
        setError("Phantom Wallet n'est pas installé. Veuillez l'installer pour continuer.")
      }
    } catch (err) {
      console.error("Erreur de connexion:", err)
      setError("Erreur lors de la connexion du wallet")
    } finally {
      setConnecting(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center space-x-2">
            <Shield className="h-6 w-6 text-primary" />
            <CardTitle className="text-2xl font-bold">Administration</CardTitle>
          </div>
          <CardDescription>Connectez votre wallet Solana pour accéder au panneau d'administration</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div className="p-4 border rounded-lg bg-gray-50">
              <div className="text-sm text-gray-500 mb-2">Statut du wallet:</div>
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${walletAddress ? "bg-green-500" : "bg-red-500"}`}></div>
                <div className="font-medium">{walletAddress ? "Wallet connecté" : "Wallet non connecté"}</div>
              </div>
              {walletAddress && <div className="mt-2 text-xs text-gray-500 break-all">{walletAddress}</div>}
            </div>

            <Button onClick={connectWallet} className="w-full" disabled={connecting || !!walletAddress}>
              {connecting ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Connexion en cours...
                </div>
              ) : walletAddress ? (
                <div className="flex items-center">
                  <Wallet className="h-4 w-4 mr-2" />
                  Wallet connecté
                </div>
              ) : (
                <div className="flex items-center">
                  <Wallet className="h-4 w-4 mr-2" />
                  Connecter votre wallet
                </div>
              )}
            </Button>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-gray-500">Accès réservé aux administrateurs de la plateforme</p>
        </CardFooter>
      </Card>
    </div>
  )
}

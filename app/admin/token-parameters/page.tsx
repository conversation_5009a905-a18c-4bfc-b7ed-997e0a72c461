"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Loader2, Check, AlertCircle, Info, RotateCcw, Save } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import tokenParametersService, { type TokenParameters } from "@/lib/token-parameters-service"

export default function TokenParametersPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("default")
  const [tokenParams, setTokenParams] = useState<TokenParameters | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Charger les paramètres existants
  useEffect(() => {
    const loadParameters = async () => {
      try {
        const params = await tokenParametersService.getParameters()
        setTokenParams(params)
      } catch (err) {
        console.error("Erreur lors du chargement des paramètres:", err)
        setError("Impossible de charger les paramètres. Veuillez réessayer.")
      } finally {
        setIsLoading(false)
      }
    }

    loadParameters()
  }, [])

  // Gérer les changements de paramètres
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, section: keyof TokenParameters, field: string) => {
    if (!tokenParams) return

    const { value, type } = e.target
    const parsedValue = type === "number" ? (value === "" ? 0 : Number(value)) : value

    setTokenParams({
      ...tokenParams,
      [section]: {
        ...tokenParams[section],
        [field]: parsedValue,
      },
    })
    setHasUnsavedChanges(true)
  }

  // Gérer les changements de switch
  const handleSwitchChange = (section: keyof TokenParameters, field: string, checked: boolean) => {
    if (!tokenParams) return

    setTokenParams({
      ...tokenParams,
      [section]: {
        ...tokenParams[section],
        [field]: checked,
      },
    })
    setHasUnsavedChanges(true)
  }

  // Gérer les changements de date
  const handleDateChange = (section: keyof TokenParameters, field: string, date: Date | undefined) => {
    if (!tokenParams || !date) return

    setTokenParams({
      ...tokenParams,
      [section]: {
        ...tokenParams[section],
        [field]: date.toISOString(),
      },
    })
    setHasUnsavedChanges(true)
  }

  // Sauvegarder les paramètres
  const saveParameters = async () => {
    if (!tokenParams) return

    setIsSaving(true)
    setError(null)

    try {
      await tokenParametersService.updateParameters(tokenParams)
      setIsSuccess(true)
      setHasUnsavedChanges(false)
      setTimeout(() => setIsSuccess(false), 3000)
    } catch (err: any) {
      console.error("Erreur lors de la sauvegarde des paramètres:", err)
      setError(err.message || "Une erreur s'est produite lors de la sauvegarde des paramètres")
    } finally {
      setIsSaving(false)
    }
  }

  // Réinitialiser les paramètres
  const resetParameters = async () => {
    if (confirm("Êtes-vous sûr de vouloir réinitialiser tous les paramètres aux valeurs par défaut ?")) {
      setIsLoading(true)
      setError(null)

      try {
        const defaultParams = await tokenParametersService.resetToDefaults()
        setTokenParams(defaultParams)
        setHasUnsavedChanges(false)
      } catch (err: any) {
        console.error("Erreur lors de la réinitialisation des paramètres:", err)
        setError(err.message || "Une erreur s'est produite lors de la réinitialisation des paramètres")
      } finally {
        setIsLoading(false)
      }
    }
  }

  if (isLoading || !tokenParams) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Paramètres des Tokens Utilisateurs</h1>
          <p className="text-muted-foreground">
            Définissez les paramètres par défaut et les restrictions pour les tokens créés par les utilisateurs.
          </p>
        </div>

        <Card>
          <CardContent className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Paramètres des Tokens Utilisateurs</h1>
          <p className="text-muted-foreground">
            Définissez les paramètres par défaut et les restrictions pour les tokens créés par les utilisateurs.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetParameters} disabled={isLoading || isSaving}>
            <RotateCcw className="mr-2 h-4 w-4" />
            Réinitialiser
          </Button>
          <Button onClick={saveParameters} disabled={isLoading || isSaving || !hasUnsavedChanges}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sauvegarde...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Sauvegarder
              </>
            )}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isSuccess && (
        <Alert className="bg-green-50 border-green-200">
          <Check className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Paramètres sauvegardés</AlertTitle>
          <AlertDescription className="text-green-700">
            Les paramètres des tokens ont été sauvegardés avec succès.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Configuration des Tokens</CardTitle>
          <CardDescription>
            Configurez les valeurs par défaut et les restrictions pour les tokens créés par les utilisateurs.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="default">Valeurs par Défaut</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="fees">Frais et Limites</TabsTrigger>
              <TabsTrigger value="presale">Presale & Vesting</TabsTrigger>
            </TabsList>

            <TabsContent value="default" className="space-y-6 pt-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Paramètres par Défaut des Tokens</h3>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="defaultDecimals">Décimales par Défaut</Label>
                    <Input
                      id="defaultDecimals"
                      type="number"
                      value={tokenParams.defaults.decimals}
                      onChange={(e) => handleInputChange(e, "defaults", "decimals")}
                      min={0}
                      max={18}
                    />
                    <p className="text-xs text-muted-foreground">
                      Nombre de décimales par défaut pour les nouveaux tokens (0-18).
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="defaultInitialSupply">Approvisionnement Initial par Défaut</Label>
                    <Input
                      id="defaultInitialSupply"
                      type="number"
                      value={tokenParams.defaults.initialSupply}
                      onChange={(e) => handleInputChange(e, "defaults", "initialSupply")}
                      min={1}
                    />
                    <p className="text-xs text-muted-foreground">Nombre de tokens créés initialement par défaut.</p>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="space-y-4">
                  <h4 className="font-medium">Fonctionnalités Autorisées</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="allowMintable">Autoriser Mintable</Label>
                        <p className="text-xs text-muted-foreground">
                          Permet de créer de nouveaux tokens après le déploiement initial
                        </p>
                      </div>
                      <Switch
                        id="allowMintable"
                        checked={tokenParams.features.allowMintable}
                        onCheckedChange={(checked) => handleSwitchChange("features", "allowMintable", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="allowBurnable">Autoriser Burnable</Label>
                        <p className="text-xs text-muted-foreground">Permet de détruire des tokens existants</p>
                      </div>
                      <Switch
                        id="allowBurnable"
                        checked={tokenParams.features.allowBurnable}
                        onCheckedChange={(checked) => handleSwitchChange("features", "allowBurnable", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="allowPausable">Autoriser Pausable</Label>
                        <p className="text-xs text-muted-foreground">
                          Permet de suspendre temporairement tous les transferts
                        </p>
                      </div>
                      <Switch
                        id="allowPausable"
                        checked={tokenParams.features.allowPausable}
                        onCheckedChange={(checked) => handleSwitchChange("features", "allowPausable", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="allowTransferTaxable">Autoriser Taxable</Label>
                        <p className="text-xs text-muted-foreground">Permet d'appliquer une taxe sur les transferts</p>
                      </div>
                      <Switch
                        id="allowTransferTaxable"
                        checked={tokenParams.features.allowTransferTaxable}
                        onCheckedChange={(checked) => handleSwitchChange("features", "allowTransferTaxable", checked)}
                      />
                    </div>
                  </div>

                  {tokenParams.features.allowTransferTaxable && (
                    <div className="space-y-2 mt-4">
                      <Label htmlFor="maxTransferTaxRate">Taux de Taxe Maximum (%)</Label>
                      <Input
                        id="maxTransferTaxRate"
                        type="number"
                        value={tokenParams.features.maxTransferTaxRate}
                        onChange={(e) => handleInputChange(e, "features", "maxTransferTaxRate")}
                        min={0}
                        max={100}
                        step={0.1}
                      />
                      <p className="text-xs text-muted-foreground">
                        Pourcentage maximum de taxe pouvant être appliqué sur les transferts.
                      </p>
                    </div>
                  )}
                </div>

                <Separator className="my-4" />

                <div className="space-y-4">
                  <h4 className="font-medium">Paramètres de Sécurité</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="requireApproval">Exiger l'Approbation Admin</Label>
                        <p className="text-xs text-muted-foreground">
                          Les tokens créés doivent être approuvés par un administrateur
                        </p>
                      </div>
                      <Switch
                        id="requireApproval"
                        checked={tokenParams.security.requireApproval}
                        onCheckedChange={(checked) => handleSwitchChange("security", "requireApproval", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="enforceKYC">Exiger KYC pour Créateurs</Label>
                        <p className="text-xs text-muted-foreground">
                          Les créateurs de tokens doivent compléter une vérification KYC
                        </p>
                      </div>
                      <Switch
                        id="enforceKYC"
                        checked={tokenParams.security.enforceKYC}
                        onCheckedChange={(checked) => handleSwitchChange("security", "enforceKYC", checked)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxDailyCreations">Créations Quotidiennes Maximum</Label>
                      <Input
                        id="maxDailyCreations"
                        type="number"
                        value={tokenParams.security.maxDailyCreations}
                        onChange={(e) => handleInputChange(e, "security", "maxDailyCreations")}
                        min={1}
                      />
                      <p className="text-xs text-muted-foreground">
                        Nombre maximum de tokens pouvant être créés par jour sur la plateforme.
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxCreationsPerUser">Créations Maximum par Utilisateur</Label>
                      <Input
                        id="maxCreationsPerUser"
                        type="number"
                        value={tokenParams.security.maxCreationsPerUser}
                        onChange={(e) => handleInputChange(e, "security", "maxCreationsPerUser")}
                        min={1}
                      />
                      <p className="text-xs text-muted-foreground">
                        Nombre maximum de tokens qu'un utilisateur peut créer.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="permissions" className="space-y-6 pt-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Permissions des Utilisateurs</h3>
                <p className="text-muted-foreground">
                  Définissez quelles fonctionnalités les utilisateurs peuvent configurer eux-mêmes lors de la création
                  de tokens.
                </p>

                <Alert className="bg-amber-50 border-amber-200 mb-4">
                  <Info className="h-4 w-4 text-amber-600" />
                  <AlertTitle className="text-amber-800">Attention</AlertTitle>
                  <AlertDescription className="text-amber-700">
                    Donner trop de libertés aux utilisateurs peut augmenter les risques de création de tokens
                    malveillants. Il est recommandé de limiter certaines fonctionnalités sensibles.
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border p-4 rounded-md">
                  <div className="flex items-center justify-between space-x-2">
                    <div>
                      <Label htmlFor="userCanSetMintable">
                        Utilisateur peut configurer Mintable
                        <p className="text-xs text-muted-foreground">
                          Permet aux utilisateurs de choisir si le token peut être minté après création
                        </p>
                      </Label>
                    </div>
                    <Switch
                      id="userCanSetMintable"
                      checked={tokenParams.permissions.userCanSetMintable}
                      onCheckedChange={(checked) => handleSwitchChange("permissions", "userCanSetMintable", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <div>
                      <Label htmlFor="userCanSetBurnable">
                        Utilisateur peut configurer Burnable
                        <p className="text-xs text-muted-foreground">
                          Permet aux utilisateurs de choisir si le token peut être détruit
                        </p>
                      </Label>
                    </div>
                    <Switch
                      id="userCanSetBurnable"
                      checked={tokenParams.permissions.userCanSetBurnable}
                      onCheckedChange={(checked) => handleSwitchChange("permissions", "userCanSetBurnable", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <div>
                      <Label htmlFor="userCanSetPausable">
                        Utilisateur peut configurer Pausable
                        <p className="text-xs text-muted-foreground">
                          Permet aux utilisateurs de choisir si les transferts peuvent être gelés
                        </p>
                      </Label>
                    </div>
                    <Switch
                      id="userCanSetPausable"
                      checked={tokenParams.permissions.userCanSetPausable}
                      onCheckedChange={(checked) => handleSwitchChange("permissions", "userCanSetPausable", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <div>
                      <Label htmlFor="userCanSetTransferTaxable">
                        Utilisateur peut configurer Taxable
                        <p className="text-xs text-muted-foreground">
                          Permet aux utilisateurs de définir une taxe sur les transferts
                        </p>
                      </Label>
                    </div>
                    <Switch
                      id="userCanSetTransferTaxable"
                      checked={tokenParams.permissions.userCanSetTransferTaxable}
                      onCheckedChange={(checked) =>
                        handleSwitchChange("permissions", "userCanSetTransferTaxable", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <div>
                      <Label htmlFor="userCanSetMetadata">
                        Utilisateur peut configurer les Métadonnées
                        <p className="text-xs text-muted-foreground">Nom, symbole, description, image, etc.</p>
                      </Label>
                    </div>
                    <Switch
                      id="userCanSetMetadata"
                      checked={tokenParams.permissions.userCanSetMetadata}
                      onCheckedChange={(checked) => handleSwitchChange("permissions", "userCanSetMetadata", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <div>
                      <Label htmlFor="userCanSetSupply">
                        Utilisateur peut configurer l'Approvisionnement
                        <p className="text-xs text-muted-foreground">
                          Permet aux utilisateurs de définir l'approvisionnement initial
                        </p>
                      </Label>
                    </div>
                    <Switch
                      id="userCanSetSupply"
                      checked={tokenParams.permissions.userCanSetSupply}
                      onCheckedChange={(checked) => handleSwitchChange("permissions", "userCanSetSupply", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <div>
                      <Label htmlFor="userCanDefineSuffixes">
                        Utilisateur peut définir le Suffixe
                        <p className="text-xs text-muted-foreground">
                          Permet aux utilisateurs de choisir un suffixe personnalisé
                        </p>
                      </Label>
                    </div>
                    <Switch
                      id="userCanDefineSuffixes"
                      checked={tokenParams.permissions.userCanDefineSuffixes}
                      onCheckedChange={(checked) => handleSwitchChange("permissions", "userCanDefineSuffixes", checked)}
                    />
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="space-y-4">
                  <h4 className="font-medium">Restrictions Avancées</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="userCanSetAntiBot">
                          Utilisateur peut configurer Anti-Bot
                          <p className="text-xs text-muted-foreground">
                            Permet aux utilisateurs d'activer des protections anti-bot
                          </p>
                        </Label>
                      </div>
                      <Switch
                        id="userCanSetAntiBot"
                        checked={tokenParams.permissions.userCanSetAntiBot}
                        onCheckedChange={(checked) => handleSwitchChange("permissions", "userCanSetAntiBot", checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="userCanSetTransactionLimits">
                          Utilisateur peut configurer les Limites de Transaction
                          <p className="text-xs text-muted-foreground">
                            Permet aux utilisateurs de définir des limites sur les transactions
                          </p>
                        </Label>
                      </div>
                      <Switch
                        id="userCanSetTransactionLimits"
                        checked={tokenParams.permissions.userCanSetTransactionLimits}
                        onCheckedChange={(checked) =>
                          handleSwitchChange("permissions", "userCanSetTransactionLimits", checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="userCanSetHoldingLimits">
                          Utilisateur peut configurer les Limites de Détention
                          <p className="text-xs text-muted-foreground">
                            Permet aux utilisateurs de définir des limites sur les montants détenus
                          </p>
                        </Label>
                      </div>
                      <Switch
                        id="userCanSetHoldingLimits"
                        checked={tokenParams.permissions.userCanSetHoldingLimits}
                        onCheckedChange={(checked) =>
                          handleSwitchChange("permissions", "userCanSetHoldingLimits", checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <div>
                        <Label htmlFor="userCanSetBlacklist">
                          Utilisateur peut configurer la Liste Noire
                          <p className="text-xs text-muted-foreground">
                            Permet aux utilisateurs de bloquer certaines adresses
                          </p>
                        </Label>
                      </div>
                      <Switch
                        id="userCanSetBlacklist"
                        checked={tokenParams.permissions.userCanSetBlacklist}
                        onCheckedChange={(checked) => handleSwitchChange("permissions", "userCanSetBlacklist", checked)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="fees" className="space-y-6 pt-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Frais de Création</h3>
                <p className="text-muted-foreground">
                  Configurez les frais associés à la création de tokens sur la plateforme.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="creationFeeInSol">Frais en SOL</Label>
                    <Input
                      id="creationFeeInSol"
                      type="number"
                      step="0.01"
                      value={tokenParams.fees.creationFeeInSol}
                      onChange={(e) => handleInputChange(e, "fees", "creationFeeInSol")}
                      min={0}
                    />
                    <p className="text-xs text-muted-foreground">Frais en SOL pour la création d'un token.</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="creationFeeInGF">Frais en GF (token de l'écosystème)</Label>
                    <Input
                      id="creationFeeInGF"
                      type="number"
                      value={tokenParams.fees.creationFeeInGF}
                      onChange={(e) => handleInputChange(e, "fees", "creationFeeInGF")}
                      min={0}
                    />
                    <p className="text-xs text-muted-foreground">Frais en tokens GF pour la création d'un token.</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="discountForGFPayment">Réduction si paiement en GF (%)</Label>
                    <Input
                      id="discountForGFPayment"
                      type="number"
                      value={tokenParams.fees.discountForGFPayment}
                      onChange={(e) => handleInputChange(e, "fees", "discountForGFPayment")}
                      min={0}
                      max={100}
                    />
                    <p className="text-xs text-muted-foreground">
                      Pourcentage de réduction sur les frais si payés en tokens GF.
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="additionalFeaturesFee">Frais pour Fonctionnalités Additionnelles</Label>
                    <Input
                      id="additionalFeaturesFee"
                      type="number"
                      step="0.01"
                      value={tokenParams.fees.additionalFeaturesFee}
                      onChange={(e) => handleInputChange(e, "fees", "additionalFeaturesFee")}
                      min={0}
                    />
                    <p className="text-xs text-muted-foreground">
                      Frais supplémentaires pour les fonctionnalités avancées (en SOL).
                    </p>
                  </div>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Information sur les Frais</AlertTitle>
                  <AlertDescription>
                    Les utilisateurs peuvent payer les frais de création en SOL ou en tokens GF. Le paiement en GF offre
                    une réduction de {tokenParams.fees.discountForGFPayment}%.
                  </AlertDescription>
                </Alert>

                <Separator className="my-4" />

                <h3 className="text-lg font-medium">Limites de Création</h3>
                <p className="text-muted-foreground">
                  Définissez les limites pour la création de tokens sur la plateforme.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minSupply">Approvisionnement Minimum</Label>
                    <Input
                      id="minSupply"
                      type="number"
                      value={tokenParams.limits.minSupply}
                      onChange={(e) => handleInputChange(e, "limits", "minSupply")}
                      min={1}
                    />
                    <p className="text-xs text-muted-foreground">Nombre minimum de tokens pouvant être créés.</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxSupply">Approvisionnement Maximum</Label>
                    <Input
                      id="maxSupply"
                      type="number"
                      value={tokenParams.limits.maxSupply}
                      onChange={(e) => handleInputChange(e, "limits", "maxSupply")}
                      min={1

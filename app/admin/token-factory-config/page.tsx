"use client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { EcosystemTokenConfiguration } from "@/components/admin/ecosystem-token-configuration"
import { SuffixManagement } from "@/components/admin/suffix-management"

export default function TokenFactoryConfigPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Configuration de la Fabrique de Tokens</h1>
        <p className="text-muted-foreground">
          G<PERSON>rez le token de l'écosystème et configurez les paramètres pour les tokens créés par les utilisateurs.
        </p>
      </div>

      <Tabs defaultValue="ecosystem" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="ecosystem">Token de l'Écosystème</TabsTrigger>
          <TabsTrigger value="user-tokens">Tokens Utilisateurs</TabsTrigger>
        </TabsList>

        <TabsContent value="ecosystem" className="space-y-4">
          <EcosystemTokenConfiguration />
        </TabsContent>

        <TabsContent value="user-tokens" className="space-y-4">
          <SuffixManagement />
        </TabsContent>
      </Tabs>
    </div>
  )
}

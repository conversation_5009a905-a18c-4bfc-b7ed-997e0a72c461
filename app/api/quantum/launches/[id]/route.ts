import { type NextRequest, NextResponse } from "next/server"
import { quantumLaunchService } from "@/lib/quantum-launch-service"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id

    const launch = await quantumLaunchService.getLaunchById(id)

    if (!launch) {
      return NextResponse.json({ success: false, error: "Launch not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: launch })
  } catch (error) {
    console.error("Error fetching launch:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch launch" }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id
    const body = await request.json()
    const { status } = body

    if (!status) {
      return NextResponse.json({ success: false, error: "Status is required" }, { status: 400 })
    }

    await quantumLaunchService.updateLaunchStatus(id, status)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error updating launch:", error)
    return NextResponse.json({ success: false, error: "Failed to update launch" }, { status: 500 })
  }
}

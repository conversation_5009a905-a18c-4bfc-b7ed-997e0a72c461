import { type NextRequest, NextResponse } from "next/server"
import { quantumLaunchService } from "@/lib/quantum-launch-service"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id
    const body = await request.json()
    const { phaseId, contributorAddress, amount, transactionId } = body

    // Valider les données d'entrée
    if (!phaseId || !contributorAddress || !amount || !transactionId) {
      return NextResponse.json({ success: false, error: "Missing required fields" }, { status: 400 })
    }

    // Vérifier que le montant est un nombre positif
    if (typeof amount !== "number" || amount <= 0) {
      return NextResponse.json({ success: false, error: "Amount must be a positive number" }, { status: 400 })
    }

    // Enregistrer la contribution
    await quantumLaunchService.recordContribution(id, phaseId, contributorAddress, amount, transactionId)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error recording contribution:", error)
    return NextResponse.json({ success: false, error: "Failed to record contribution" }, { status: 500 })
  }
}

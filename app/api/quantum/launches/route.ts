import { type NextRequest, NextResponse } from "next/server"
import { quantumLaunchService } from "@/lib/quantum-launch-service"
import { v4 as uuidv4 } from "uuid"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const status = searchParams.get("status") || undefined
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const offset = Number.parseInt(searchParams.get("offset") || "0")

    const launches = await quantumLaunchService.getAllLaunches(status, limit, offset)

    return NextResponse.json({ success: true, data: launches })
  } catch (error) {
    console.error("Error fetching launches:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch launches" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { tokenAddress, ownerAddress, config, phases } = body

    // Valider les données d'entrée
    if (!tokenAddress || !ownerAddress || !config || !phases || !Array.isArray(phases)) {
      return NextResponse.json({ success: false, error: "Invalid input data" }, { status: 400 })
    }

    // Préparer les phases avec des IDs uniques
    const preparedPhases = phases.map((phase) => ({
      ...phase,
      id: phase.id || uuidv4(),
      startDate: new Date(phase.startDate),
      endDate: new Date(phase.endDate),
    }))

    const launchId = await quantumLaunchService.createTokenLaunch(tokenAddress, ownerAddress, config, preparedPhases)

    return NextResponse.json({ success: true, data: { launchId } })
  } catch (error) {
    console.error("Error creating launch:", error)
    return NextResponse.json({ success: false, error: "Failed to create launch" }, { status: 500 })
  }
}

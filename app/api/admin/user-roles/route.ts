import { type NextRequest, NextResponse } from "next/server"
import { getUserRoles, assignRoleToUser, removeRoleFromUser, getUserPermissions } from "@/lib/role-management-service"
import { isAdmin } from "@/lib/admin-service"
import { logAdminActivity } from "@/lib/admin-activity-service"

// GET: Récupérer les rôles ou permissions d'un utilisateur
export async function GET(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("userId")
    const getPermissions = searchParams.get("permissions") === "true"

    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 })
    }

    if (getPermissions) {
      const permissions = await getUserPermissions(userId)
      return NextResponse.json({ permissions })
    } else {
      const roles = await getUserRoles(userId)
      return NextResponse.json({ roles })
    }
  } catch (error) {
    console.error("Error fetching user roles:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch user roles" },
      { status: 500 },
    )
  }
}

// POST: Attribuer un rôle à un utilisateur
export async function POST(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const { userId, roleId } = await request.json()

    if (!userId || !roleId) {
      return NextResponse.json({ error: "User ID and Role ID are required" }, { status: 400 })
    }

    await assignRoleToUser(userId, roleId)

    // Journaliser l'activité
    await logAdminActivity("assign_role", `Rôle ${roleId} attribué à l'utilisateur ${userId}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error assigning role:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to assign role" },
      { status: 500 },
    )
  }
}

// DELETE: Retirer un rôle à un utilisateur
export async function DELETE(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("userId")
    const roleId = searchParams.get("roleId")

    if (!userId || !roleId) {
      return NextResponse.json({ error: "User ID and Role ID are required" }, { status: 400 })
    }

    await removeRoleFromUser(userId, roleId)

    // Journaliser l'activité
    await logAdminActivity("remove_role", `Rôle ${roleId} retiré de l'utilisateur ${userId}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error removing role:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to remove role" },
      { status: 500 },
    )
  }
}

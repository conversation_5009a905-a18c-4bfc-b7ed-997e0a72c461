import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { isAdmin } from "@/lib/admin-service"
import { v4 as uuidv4 } from "uuid"

export async function POST(request: Request) {
  try {
    const { walletAddress } = await request.json()

    if (!walletAddress) {
      return NextResponse.json({ error: "Adresse de wallet manquante" }, { status: 400 })
    }

    // Vérifier si l'adresse est un admin
    const adminStatus = isAdmin(walletAddress)

    if (adminStatus) {
      // Générer un token d'authentification
      const adminAuthToken = uuidv4()

      // Stocker le token dans les cookies
      cookies().set({
        name: "walletAddress",
        value: walletAddress,
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        path: "/",
        // Expiration après 24 heures
        maxAge: 60 * 60 * 24,
      })

      cookies().set({
        name: "adminAuthToken",
        value: adminAuthToken,
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        path: "/",
        // Expiration après 24 heures
        maxAge: 60 * 60 * 24,
      })

      return NextResponse.json({ isAdmin: true, message: "Authentification réussie" })
    }

    return NextResponse.json({ isAdmin: false, message: "Ce wallet n'a pas les droits d'administration" })
  } catch (error) {
    console.error("Erreur lors de la vérification de l'authentification:", error)
    return NextResponse.json({ error: "Erreur lors de la vérification de l'authentification" }, { status: 500 })
  }
}

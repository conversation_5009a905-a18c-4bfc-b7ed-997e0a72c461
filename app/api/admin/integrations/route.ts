import { type NextRequest, NextResponse } from "next/server"
import { getSecureEnvVariable, setSecureEnvVariable } from "@/lib/secure-env-storage"
import { isAdminWallet } from "@/lib/admin-service"

// Liste des intégrations API disponibles
const availableIntegrations = [
  {
    id: "coingecko",
    name: "CoinGecko",
    description: "API pour les données de marché des cryptomonnaies",
    enabled: true,
    apiKey: "",
    apiEndpoint: "https://api.coingecko.com/api/v3",
    category: "market",
    status: "disconnected",
    requiredForModule: ["market", "token-factory"],
  },
  {
    id: "coinmarketcap",
    name: "CoinMarketCap",
    description: "API pour les données de marché des cryptomonnaies",
    enabled: false,
    apiKey: "",
    apiEndpoint: "https://pro-api.coinmarketcap.com/v1",
    category: "market",
    status: "disconnected",
  },
  {
    id: "solana_mainnet",
    name: "Solana Mainnet RPC",
    description: "Point de terminaison RPC pour Solana Mainnet",
    enabled: true,
    apiKey: "",
    apiEndpoint: "https://api.mainnet-beta.solana.com",
    category: "blockchain",
    status: "connected",
    requiredForModule: ["token-factory", "staking", "nft-gallery"],
  },
  {
    id: "solana_devnet",
    name: "Solana Devnet RPC",
    description: "Point de terminaison RPC pour Solana Devnet",
    enabled: true,
    apiKey: "",
    apiEndpoint: "https://api.devnet.solana.com",
    category: "blockchain",
    status: "connected",
    requiredForModule: ["token-factory", "staking", "nft-gallery"],
  },
  {
    id: "bnb_mainnet",
    name: "BNB Chain Mainnet RPC",
    description: "Point de terminaison RPC pour BNB Chain Mainnet",
    enabled: false,
    apiKey: "",
    apiEndpoint: "https://bsc-dataseed.binance.org",
    category: "blockchain",
    status: "disconnected",
  },
  {
    id: "bnb_testnet",
    name: "BNB Chain Testnet RPC",
    description: "Point de terminaison RPC pour BNB Chain Testnet",
    enabled: false,
    apiKey: "",
    apiEndpoint: "https://data-seed-prebsc-1-s1.binance.org:8545",
    category: "blockchain",
    status: "disconnected",
  },
  {
    id: "filebase",
    name: "Filebase",
    description: "Stockage décentralisé pour les fichiers",
    enabled: false,
    apiKey: "",
    apiSecret: "",
    apiEndpoint: "https://s3.filebase.com",
    category: "storage",
    status: "disconnected",
  },
  {
    id: "openai",
    name: "OpenAI",
    description: "API pour l'intelligence artificielle et la génération de contenu",
    enabled: false,
    apiKey: "",
    apiEndpoint: "https://api.openai.com/v1",
    category: "ai",
    status: "disconnected",
  },
  {
    id: "deepseek",
    name: "DeepSeek",
    description: "API pour l'intelligence artificielle avancée et la génération de contenu",
    enabled: true,
    apiKey: "",
    apiEndpoint: "https://api.deepseek.com/v1",
    category: "ai",
    status: "disconnected",
    requiredForModule: ["token-factory", "ai-creator"],
  },
]

// Récupérer les intégrations API
export async function GET(request: NextRequest) {
  try {
    // Vérifier l'authentification admin
    const authHeader = request.headers.get("authorization")
    if (!authHeader) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 })
    }

    const token = authHeader.split(" ")[1]
    const isAdmin = await isAdminWallet(token)

    if (!isAdmin) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 })
    }

    // Récupérer les valeurs des variables d'environnement
    const integrations = await Promise.all(
      availableIntegrations.map(async (integration) => {
        const apiKey = (await getSecureEnvVariable(`${integration.id.toUpperCase()}_API_KEY`)) || ""
        const apiSecret =
          integration.apiSecret !== undefined
            ? (await getSecureEnvVariable(`${integration.id.toUpperCase()}_API_SECRET`)) || ""
            : undefined
        const apiEndpoint =
          (await getSecureEnvVariable(`${integration.id.toUpperCase()}_API_ENDPOINT`)) || integration.apiEndpoint

        return {
          ...integration,
          apiKey,
          apiSecret,
          apiEndpoint,
        }
      }),
    )

    return NextResponse.json({ integrations })
  } catch (error) {
    console.error("Erreur lors de la récupération des intégrations API:", error)
    return NextResponse.json({ error: "Erreur serveur" }, { status: 500 })
  }
}

// Mettre à jour une intégration API
export async function PUT(request: NextRequest) {
  try {
    // Vérifier l'authentification admin
    const authHeader = request.headers.get("authorization")
    if (!authHeader) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 })
    }

    const token = authHeader.split(" ")[1]
    const isAdmin = await isAdminWallet(token)

    if (!isAdmin) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 })
    }

    const body = await request.json()
    const { id, apiKey, apiSecret, apiEndpoint, enabled } = body

    // Vérifier que l'intégration existe
    const integration = availableIntegrations.find((api) => api.id === id)
    if (!integration) {
      return NextResponse.json({ error: "Intégration non trouvée" }, { status: 404 })
    }

    // Mettre à jour les variables d'environnement
    await setSecureEnvVariable(`${id.toUpperCase()}_API_KEY`, apiKey, {
      isEncrypted: true,
      category: "api",
      description: `Clé API pour ${integration.name}`,
      updatedBy: "admin",
    })

    if (apiSecret !== undefined) {
      await setSecureEnvVariable(`${id.toUpperCase()}_API_SECRET`, apiSecret, {
        isEncrypted: true,
        category: "api",
        description: `Secret API pour ${integration.name}`,
        updatedBy: "admin",
      })
    }

    await setSecureEnvVariable(`${id.toUpperCase()}_API_ENDPOINT`, apiEndpoint, {
      isEncrypted: false,
      category: "api",
      description: `Point de terminaison API pour ${integration.name}`,
      updatedBy: "admin",
    })

    await setSecureEnvVariable(`${id.toUpperCase()}_ENABLED`, enabled.toString(), {
      isEncrypted: false,
      category: "api",
      description: `État d'activation pour ${integration.name}`,
      updatedBy: "admin",
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'intégration API:", error)
    return NextResponse.json({ error: "Erreur serveur" }, { status: 500 })
  }
}

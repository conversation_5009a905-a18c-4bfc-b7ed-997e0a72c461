import { type NextRequest, NextResponse } from "next/server"
import { isAdminWallet } from "@/lib/admin-service"
import { setSecureEnvVariable } from "@/lib/secure-env-storage"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Vérifier l'authentification admin
    const authHeader = request.headers.get("authorization")
    if (!authHeader) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 })
    }

    const token = authHeader.split(" ")[1]
    const isAdmin = await isAdminWallet(token)

    if (!isAdmin) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const { enabled } = body

    // Mettre à jour l'état d'activation
    await setSecureEnvVariable(`${id.toUpperCase()}_ENABLED`, enabled.toString(), {
      isEncrypted: false,
      category: "api",
      description: `État d'activation pour l'intégration ${id}`,
      updatedBy: "admin",
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error(`Erreur lors de la modification du statut de l'intégration ${params.id}:`, error)
    return NextResponse.json({ error: "Erreur serveur" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { isAdminWallet } from "@/lib/admin-service"
import { testDeepSeekConnection } from "@/lib/deepseek-service"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Vérifier l'authentification admin
    const authHeader = request.headers.get("authorization")
    if (!authHeader) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 })
    }

    const token = authHeader.split(" ")[1]
    const isAdmin = await isAdminWallet(token)

    if (!isAdmin) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const { apiKey, apiSecret, apiEndpoint } = body

    // Tester la connexion en fonction de l'intégration
    let result = { success: false, message: "Type d'intégration non pris en charge" }

    switch (id) {
      case "coingecko":
        // Code pour tester CoinGecko
        result = { success: true, message: "Connexion à CoinGecko établie avec succès" }
        break
      case "coinmarketcap":
        // Code pour tester CoinMarketCap
        result = { success: true, message: "Connexion à CoinMarketCap établie avec succès" }
        break
      case "solana_mainnet":
        // Code pour tester Solana Mainnet
        result = { success: true, message: "Connexion à Solana Mainnet établie avec succès" }
        break
      case "solana_devnet":
        // Code pour tester Solana Devnet
        result = { success: true, message: "Connexion à Solana Devnet établie avec succès" }
        break
      case "bnb_mainnet":
        // Code pour tester BNB Chain Mainnet
        result = { success: true, message: "Connexion à BNB Chain Mainnet établie avec succès" }
        break
      case "bnb_testnet":
        // Code pour tester BNB Chain Testnet
        result = { success: true, message: "Connexion à BNB Chain Testnet établie avec succès" }
        break
      case "filebase":
        // Code pour tester Filebase
        result = { success: true, message: "Connexion à Filebase établie avec succès" }
        break
      case "openai":
        // Code pour tester OpenAI
        result = { success: true, message: "Connexion à OpenAI établie avec succès" }
        break
      case "deepseek":
        // Utiliser le service DeepSeek pour tester la connexion
        result = await testDeepSeekConnection()
        break
      default:
        break
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error("Erreur lors du test de connexion:", error)
    return NextResponse.json({ success: false, message: "Erreur lors du test de connexion" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { getAllRoles, createRole, updateRole, deleteRole, getRoleById } from "@/lib/role-management-service"
import { isAdmin, isSuperAdmin } from "@/lib/admin-service"
import { logAdminActivity } from "@/lib/admin-activity-service"

// GET: Récupérer tous les rôles ou un rôle spécifique
export async function GET(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const roleId = searchParams.get("id")

    if (roleId) {
      const role = await getRoleById(roleId)
      if (!role) {
        return NextResponse.json({ error: "Role not found" }, { status: 404 })
      }
      return NextResponse.json(role)
    }

    const roles = await getAllRoles()
    return NextResponse.json(roles)
  } catch (error) {
    console.error("Error fetching roles:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch roles" },
      { status: 500 },
    )
  }
}

// POST: Créer un nouveau rôle
export async function POST(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isSuperAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const { name, description, permissions } = await request.json()

    if (!name || !description || !permissions) {
      return NextResponse.json({ error: "Name, description and permissions are required" }, { status: 400 })
    }

    const newRole = await createRole(name, description, permissions)

    // Journaliser l'activité
    await logAdminActivity("create_role", `Rôle créé: ${name} avec ${permissions.length} permissions`)

    return NextResponse.json(newRole, { status: 201 })
  } catch (error) {
    console.error("Error creating role:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create role" },
      { status: 500 },
    )
  }
}

// PUT: Mettre à jour un rôle existant
export async function PUT(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isSuperAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const { id, name, description, permissions } = await request.json()

    if (!id) {
      return NextResponse.json({ error: "Role ID is required" }, { status: 400 })
    }

    const updates: { name?: string; description?: string; permissions?: string[] } = {}
    if (name) updates.name = name
    if (description) updates.description = description
    if (permissions) updates.permissions = permissions

    const updatedRole = await updateRole(id, updates)

    // Journaliser l'activité
    await logAdminActivity("update_role", `Rôle mis à jour: ${updatedRole.name}`)

    return NextResponse.json(updatedRole)
  } catch (error) {
    console.error("Error updating role:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update role" },
      { status: 500 },
    )
  }
}

// DELETE: Supprimer un rôle
export async function DELETE(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isSuperAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const roleId = searchParams.get("id")

    if (!roleId) {
      return NextResponse.json({ error: "Role ID is required" }, { status: 400 })
    }

    // Récupérer le nom du rôle avant de le supprimer pour le journal
    const role = await getRoleById(roleId)
    if (!role) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 })
    }

    await deleteRole(roleId)

    // Journaliser l'activité
    await logAdminActivity("delete_role", `Rôle supprimé: ${role.name}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting role:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to delete role" },
      { status: 500 },
    )
  }
}

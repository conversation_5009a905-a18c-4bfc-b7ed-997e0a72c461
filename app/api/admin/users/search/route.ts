import { type NextRequest, NextResponse } from "next/server"
import { isAdmin } from "@/lib/admin-service"
import { getUserRoles } from "@/lib/role-management-service"

// Simulation d'une base de données d'utilisateurs
const mockUsers = [
  {
    walletAddress: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM",
    username: "Admin Principal",
    joinedAt: "2023-01-15T12:00:00Z",
  },
  {
    walletAddress: "5FHwkrdxkRzDNkwkjM5aMVxnvE4UzwgJhEEtrJHAzKqJ",
    username: "Utilisateur Test",
    joinedAt: "2023-02-20T14:30:00Z",
  },
  {
    walletAddress: "7Z6UgwFJdSMEfrBKVgGP4k8mB6fEkdvwqUXqjzH5gJnq",
    username: "Développeur Blockchain",
    joinedAt: "2023-03-10T09:15:00Z",
  },
  {
    walletAddress: "********************************************",
    username: "Investisseur Crypto",
    joinedAt: "2023-04-05T16:45:00Z",
  },
  {
    walletAddress: "6KzxbJUK9JWVdUDgRTWvqzZJP8vJJqWjgSNfcMjqRNrG",
    username: "Trader Solana",
    joinedAt: "2023-05-12T11:20:00Z",
  },
]

export async function GET(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get("q")?.toLowerCase()

    if (!query) {
      return NextResponse.json({ error: "Search query is required" }, { status: 400 })
    }

    // Rechercher les utilisateurs correspondant à la requête
    const filteredUsers = mockUsers.filter(
      (user) =>
        user.walletAddress.toLowerCase().includes(query) ||
        (user.username && user.username.toLowerCase().includes(query)),
    )

    // Récupérer les rôles pour chaque utilisateur
    const usersWithRoles = await Promise.all(
      filteredUsers.map(async (user) => {
        const roles = await getUserRoles(user.walletAddress)
        return {
          ...user,
          roles,
        }
      }),
    )

    return NextResponse.json({ users: usersWithRoles })
  } catch (error) {
    console.error("Error searching users:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to search users" },
      { status: 500 },
    )
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { getAdmins, addAdmin, removeAdmin, updateAdminRole, isAdminWallet, isSuperAdmin } from "@/lib/admin-service"

// GET: Récupérer la liste des administrateurs
export async function GET(request: NextRequest) {
  try {
    // Vérifier si l'utilisateur est un administrateur
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdminWallet(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const admins = await getAdmins()
    return NextResponse.json({ admins })
  } catch (error) {
    console.error("Error fetching admins:", error)
    return NextResponse.json({ error: "Failed to fetch admins" }, { status: 500 })
  }
}

// POST: Ajouter un nouvel administrateur
export async function POST(request: NextRequest) {
  try {
    // Vérifier si l'utilisateur est un super administrateur
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isSuperAdmin(walletAddress)) {
      return NextResponse.json({ error: "Only super admins can add new admins" }, { status: 403 })
    }

    const { walletAddress: newAdminWallet, role, permissions } = await request.json()

    if (!newAdminWallet) {
      return NextResponse.json({ error: "Wallet address is required" }, { status: 400 })
    }

    await addAdmin(newAdminWallet, role || "admin", walletAddress, permissions || [])

    return NextResponse.json({ success: true, message: "Admin added successfully" })
  } catch (error) {
    console.error("Error adding admin:", error)
    return NextResponse.json({ error: error instanceof Error ? error.message : "Failed to add admin" }, { status: 500 })
  }
}

// PUT: Mettre à jour un administrateur existant
export async function PUT(request: NextRequest) {
  try {
    // Vérifier si l'utilisateur est un super administrateur
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isSuperAdmin(walletAddress)) {
      return NextResponse.json({ error: "Only super admins can update admin roles" }, { status: 403 })
    }

    const { walletAddress: targetWallet, role } = await request.json()

    if (!targetWallet || !role) {
      return NextResponse.json({ error: "Wallet address and role are required" }, { status: 400 })
    }

    await updateAdminRole(targetWallet, role, walletAddress)

    return NextResponse.json({ success: true, message: "Admin role updated successfully" })
  } catch (error) {
    console.error("Error updating admin role:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to update admin role" },
      { status: 500 },
    )
  }
}

// DELETE: Supprimer un administrateur
export async function DELETE(request: NextRequest) {
  try {
    // Vérifier si l'utilisateur est un super administrateur
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isSuperAdmin(walletAddress)) {
      return NextResponse.json({ error: "Only super admins can remove admins" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const targetWallet = searchParams.get("wallet")

    if (!targetWallet) {
      return NextResponse.json({ error: "Wallet address is required" }, { status: 400 })
    }

    await removeAdmin(targetWallet, walletAddress)

    return NextResponse.json({ success: true, message: "Admin removed successfully" })
  } catch (error) {
    console.error("Error removing admin:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to remove admin" },
      { status: 500 },
    )
  }
}

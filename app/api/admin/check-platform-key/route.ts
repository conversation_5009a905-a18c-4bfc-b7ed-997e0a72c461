import { NextResponse } from "next/server"
import secureKeyService from "@/lib/secure-key-service"

export async function GET() {
  try {
    const isAvailable = secureKeyService.isPlatformKeypairAvailable()
    const publicKey = secureKeyService.getPlatformPublicKey()

    if (isAvailable && publicKey) {
      return NextResponse.json({
        success: true,
        message: "Platform key is properly configured",
        publicKey: publicKey,
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          message: "Platform key is not properly configured",
          error: "Private key not found or invalid",
        },
        { status: 500 },
      )
    }
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        message: "Error checking platform key",
        error: error.message,
      },
      { status: 500 },
    )
  }
}

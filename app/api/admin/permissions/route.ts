import { type NextRequest, NextResponse } from "next/server"
import { getAllPermissions } from "@/lib/role-management-service"
import { isAdmin } from "@/lib/admin-service"

export async function GET(request: NextRequest) {
  try {
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized access" }, { status: 403 })
    }

    const permissions = await getAllPermissions()
    return NextResponse.json({ permissions })
  } catch (error) {
    console.error("Error fetching permissions:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to fetch permissions" },
      { status: 500 },
    )
  }
}

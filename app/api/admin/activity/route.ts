import { type NextRequest, NextResponse } from "next/server"
import { getAdminActivities, logAdminActivity } from "@/lib/admin-activity-service"
import { isAdmin } from "@/lib/admin-service"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const filterAdmin = searchParams.get("admin") || undefined

    // Vérifier l'authentification admin via cookies
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized: Admin access required" }, { status: 403 })
    }

    const result = await getAdminActivities(page, limit, filterAdmin)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error fetching admin activities:", error)
    return NextResponse.json({ error: "Failed to fetch admin activities" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, details } = await request.json()

    // Vérifier l'authentification admin via cookies
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized: Admin access required" }, { status: 403 })
    }

    if (!action || !details) {
      return NextResponse.json({ error: "Missing required fields: action and details" }, { status: 400 })
    }

    await logAdminActivity(action, details)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error logging admin activity:", error)
    return NextResponse.json({ error: "Failed to log admin activity" }, { status: 500 })
  }
}

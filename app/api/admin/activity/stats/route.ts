import { type NextRequest, NextResponse } from "next/server"
import { getAdminActivityStats } from "@/lib/admin-activity-service"
import { isAdmin } from "@/lib/admin-service"

export async function GET(request: NextRequest) {
  try {
    // Vérifier l'authentification admin via cookies
    const walletAddress = request.cookies.get("walletAddress")?.value

    if (!walletAddress || !isAdmin(walletAddress)) {
      return NextResponse.json({ error: "Unauthorized: Admin access required" }, { status: 403 })
    }

    const stats = await getAdminActivityStats()

    return NextResponse.json(stats)
  } catch (error) {
    console.error("Error fetching admin activity stats:", error)
    return NextResponse.json({ error: "Failed to fetch admin activity stats" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { performanceService } from "@/lib/performance-service"

// Obtenir les métriques de performance
export async function GET(req: NextRequest) {
  try {
    const metrics = performanceService.getMetrics()
    const resources = performanceService.getResources()
    const recommendations = performanceService.getRecommendations()
    const lastUpdated = performanceService.getLastUpdated()

    return NextResponse.json({
      success: true,
      data: {
        metrics,
        resources,
        recommendations,
        lastUpdated,
      },
    })
  } catch (error) {
    console.error("Error fetching performance data:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch performance data" }, { status: 500 })
  }
}

// Appliquer une optimisation
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { action, recommendationId } = body

    if (action === "optimize_all") {
      const success = await performanceService.applyAllOptimizations()
      return NextResponse.json({ success })
    } else if (action === "optimize" && recommendationId) {
      const success = await performanceService.applyOptimization(recommendationId)
      return NextResponse.json({ success })
    } else {
      return NextResponse.json({ success: false, error: "Invalid action or missing recommendationId" }, { status: 400 })
    }
  } catch (error) {
    console.error("Error applying optimization:", error)
    return NextResponse.json({ success: false, error: "Failed to apply optimization" }, { status: 500 })
  }
}

import { NextResponse } from "next/server"
import { isAdminWallet } from "@/lib/admin-service"
import { Redis } from "@upstash/redis"
import * as bs58 from "bs58"
import { verify } from "@noble/ed25519"
import { SignJWT } from "jose"
import { randomUUID } from "crypto"

// Initialize Redis client
const redis = Redis.fromEnv()

// JWT secret key (should be in env variables)
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || "fallback_secret_for_development_only_change_in_production",
)

// JWT expiration time (1 hour)
const TOKEN_EXPIRY = "1h"

export async function POST(request: Request) {
  try {
    const { walletAddress, nonce, timestamp, signature } = await request.json()

    // Validate input
    if (!walletAddress || !nonce || !timestamp || !signature) {
      return NextResponse.json({ error: "Missing required authentication parameters" }, { status: 400 })
    }

    // Check if wallet is in admin list
    if (!isAdminWallet(walletAddress)) {
      // Log unauthorized attempt
      console.warn(`Unauthorized admin access attempt from wallet: ${walletAddress}`)

      return NextResponse.json({ error: "Wallet is not authorized for admin access", isAdmin: false }, { status: 403 })
    }

    // Retrieve the stored nonce from Redis
    const nonceKey = `admin:nonce:${walletAddress}`
    const storedNonceData = await redis.get(nonceKey)

    if (!storedNonceData) {
      return NextResponse.json(
        { error: "Authentication challenge expired or invalid", isAdmin: false },
        { status: 401 },
      )
    }

    // Parse the stored nonce data
    const { nonce: storedNonce, timestamp: storedTimestamp } = JSON.parse(storedNonceData as string)

    // Verify the nonce matches
    if (nonce !== storedNonce || timestamp !== storedTimestamp) {
      return NextResponse.json({ error: "Invalid authentication challenge", isAdmin: false }, { status: 401 })
    }

    // Verify the signature
    try {
      const message = new TextEncoder().encode(`Admin authentication request: ${nonce} at ${timestamp}`)
      const signatureBytes = bs58.decode(signature)
      const publicKeyBytes = bs58.decode(walletAddress)

      const isValidSignature = await verify(signatureBytes, message, publicKeyBytes)

      if (!isValidSignature) {
        throw new Error("Invalid signature")
      }
    } catch (error) {
      console.error("Signature verification failed:", error)
      return NextResponse.json({ error: "Signature verification failed", isAdmin: false }, { status: 401 })
    }

    // Delete the used nonce to prevent replay attacks
    await redis.del(nonceKey)

    // Generate a JWT token for the admin session
    const token = await new SignJWT({
      wallet: walletAddress,
      role: "admin",
      jti: randomUUID(),
    })
      .setProtectedHeader({ alg: "HS256" })
      .setIssuedAt()
      .setExpirationTime(TOKEN_EXPIRY)
      .sign(JWT_SECRET)

    // Log successful authentication
    console.log(`Admin authenticated successfully: ${walletAddress.slice(0, 8)}...`)

    // Return success response with token
    return NextResponse.json({
      message: "Authentication successful",
      isAdmin: true,
      token,
    })
  } catch (error) {
    console.error("Admin authentication error:", error)
    return NextResponse.json({ error: "Authentication failed", isAdmin: false }, { status: 500 })
  }
}

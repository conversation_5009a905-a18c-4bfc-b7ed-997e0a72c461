import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import { verifyAdminToken } from "@/lib/admin-service"

export async function GET() {
  try {
    const cookieStore = cookies()
    const tokenCookie = cookieStore.get("admin_session")

    if (!tokenCookie?.value) {
      return NextResponse.json({ isAuthenticated: false }, { status: 401 })
    }

    const { valid, wallet, role } = await verifyAdminToken(tokenCookie.value)

    if (!valid || !wallet) {
      return NextResponse.json({ isAuthenticated: false }, { status: 401 })
    }

    return NextResponse.json({
      isAuthenticated: true,
      wallet,
      role,
    })
  } catch (error) {
    console.error("Error checking admin authentication:", error)
    return NextResponse.json({ error: "Authentication check failed", isAuthenticated: false }, { status: 500 })
  }
}

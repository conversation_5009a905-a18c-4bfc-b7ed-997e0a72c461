import { NextResponse } from "next/server"
import { Connection, PublicKey } from "@solana/web3.js"
import { isAdminWallet } from "@/lib/admin-service"

// Initialize Solana connection
const connection = new Connection(process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com")

export async function POST(request: Request) {
  try {
    const { walletAddress } = await request.json()

    if (!walletAddress) {
      return NextResponse.json({ error: "Wallet address is required" }, { status: 400 })
    }

    // First check if the wallet is in our admin list
    if (!isAdminWallet(walletAddress)) {
      return NextResponse.json({ verified: false, error: "Wallet is not authorized for admin access" }, { status: 403 })
    }

    // For on-chain verification, we would typically:
    // 1. Create a connection to the Solana network
    // 2. Load your program
    // 3. Call a view function that verifies admin status

    // This is a simplified example - in production you would:
    // - Load your actual Anchor program
    // - Call a specific admin verification method
    // - Handle any on-chain errors properly

    try {
      const publicKey = new PublicKey(walletAddress)

      // Simulate on-chain verification
      // In a real implementation, you would:
      // 1. Connect to your actual program
      // 2. Call a view function to verify admin status
      // const program = new Program(IDL, programId, provider);
      // const result = await program.account.adminSettings.fetch(adminPDA);
      // return result.isAdmin;

      // For now, we'll just return true if the wallet is in our admin list
      return NextResponse.json({
        verified: true,
        message: "On-chain verification successful",
      })
    } catch (onChainError) {
      console.error("On-chain verification error:", onChainError)
      return NextResponse.json({ verified: false, error: "On-chain verification failed" }, { status: 500 })
    }
  } catch (error) {
    console.error("Admin on-chain verification error:", error)
    return NextResponse.json({ verified: false, error: "Verification failed" }, { status: 500 })
  }
}

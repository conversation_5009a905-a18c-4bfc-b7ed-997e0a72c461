import { NextResponse } from "next/server"
import { randomBytes } from "crypto"
import { Redis } from "@upstash/redis"

// Initialize Redis client for storing nonces
const redis = Redis.fromEnv()

// Nonce expiration time (30 seconds)
const NONCE_EXPIRY = 30

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const wallet = searchParams.get("wallet")

    if (!wallet) {
      return NextResponse.json({ error: "Wallet address is required" }, { status: 400 })
    }

    // Generate a cryptographically secure random nonce
    const nonce = randomBytes(32).toString("hex")
    const timestamp = Date.now().toString()

    // Store the nonce in Redis with expiration
    const nonceKey = `admin:nonce:${wallet}`
    await redis.set(nonceKey, JSON.stringify({ nonce, timestamp }), { ex: NONCE_EXPIRY })

    // Log the challenge request (for security auditing)
    console.log(`Admin auth challenge requested for wallet: ${wallet.slice(0, 8)}...`)

    // Return the nonce and timestamp to the client
    return NextResponse.json({ nonce, timestamp })
  } catch (error) {
    console.error("Error generating admin auth challenge:", error)
    return NextResponse.json({ error: "Failed to generate authentication challenge" }, { status: 500 })
  }
}

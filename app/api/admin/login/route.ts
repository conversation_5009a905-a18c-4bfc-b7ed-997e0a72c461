import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { isValidAdminKey } from "@/lib/admin-service"

export async function POST(request: NextRequest) {
  try {
    const { adminKey } = await request.json()

    // Vérifier si la clé admin est valide
    const isValid = await isValidAdminKey(adminKey)

    if (!isValid) {
      return NextResponse.json({ success: false, message: "Clé d'administrateur invalide" }, { status: 401 })
    }

    // Créer un cookie d'authentification admin
    const cookieStore = cookies()

    // Définir un cookie sécurisé avec une durée de 24 heures
    cookieStore.set("admin_auth", "true", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 60 * 60 * 24, // 24 heures
      path: "/",
      sameSite: "strict",
    })

    // Stocker la clé admin dans un cookie séparé (crypté ou haché dans un environnement de production)
    cookieStore.set("admin_key", adminKey, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 60 * 60 * 24, // 24 heures
      path: "/",
      sameSite: "strict",
    })

    return NextResponse.json({ success: true, message: "Authentification réussie" })
  } catch (error) {
    console.error("Erreur lors de la connexion admin:", error)
    return NextResponse.json({ success: false, message: "Erreur lors de la connexion" }, { status: 500 })
  }
}

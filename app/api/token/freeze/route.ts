import { type NextRequest, NextResponse } from "next/server"
import { realTokenService, type TokenFreezeParams } from "@/lib/real-token-service"
import { z } from "zod"
import { verifyAdminKey } from "@/lib/admin-auth"

// Schéma de validation pour les paramètres de gel de token
const freezeParamsSchema = z.object({
  mintAddress: z.string().min(32, "Adresse du mint invalide").max(44, "Adresse du mint invalide"),
  targetAddress: z.string().min(32, "Adresse cible invalide").max(44, "Adresse cible invalide"),
  adminKey: z.string().min(1, "Clé d'administration requise"),
  networkId: z.string().optional().default("solana-devnet"),
})

export async function POST(req: NextRequest) {
  try {
    // Récupérer les paramètres de la requête
    const data = await req.json()

    // Valider les paramètres
    const validationResult = freezeParamsSchema.safeParse(data)
    if (!validationResult.success) {
      return NextResponse.json(
        { success: false, error: validationResult.error.errors.map((e) => e.message).join(", ") },
        { status: 400 },
      )
    }

    const validatedData = validationResult.data

    // Vérifier la clé d'administration
    const isAdmin = await verifyAdminKey(validatedData.adminKey)
    if (!isAdmin) {
      return NextResponse.json({ success: false, error: "Clé d'administration invalide" }, { status: 403 })
    }

    // Récupérer la clé privée de l'autorité de gel depuis les variables d'environnement
    const freezeAuthority = process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY
    if (!freezeAuthority) {
      return NextResponse.json(
        { success: false, error: "Clé privée de l'autorité de gel non configurée" },
        { status: 500 },
      )
    }

    // Préparer les paramètres de gel du token
    const freezeParams: TokenFreezeParams = {
      mintAddress: validatedData.mintAddress,
      targetAddress: validatedData.targetAddress,
      freezeAuthority,
      networkId: validatedData.networkId,
    }

    // Geler le token
    const result = await realTokenService.freezeToken(freezeParams)

    if (!result.success) {
      return NextResponse.json({ success: false, error: result.error || "Échec du gel du token" }, { status: 500 })
    }

    // Retourner les détails de l'opération
    return NextResponse.json({
      success: true,
      signature: result.signature,
      mintAddress: validatedData.mintAddress,
      targetAddress: validatedData.targetAddress,
      status: "frozen",
    })
  } catch (error: any) {
    console.error("Erreur lors du gel du token:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Une erreur s'est produite lors du gel du token",
      },
      { status: 500 },
    )
  }
}

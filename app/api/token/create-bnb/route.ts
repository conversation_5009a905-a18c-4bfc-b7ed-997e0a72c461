import { type NextRequest, NextResponse } from "next/server"
import BnbChainService from "@/lib/bnb-chain-service"
import { getNetworkById } from "@/lib/network-config"

export async function POST(request: NextRequest) {
  try {
    const {
      name,
      symbol,
      decimals,
      initialSupply,
      description,
      website,
      twitter,
      telegram,
      networkId,
      creatorAddress,
      targetSuffix,
    } = await request.json()

    // Validate required fields
    if (!name || !symbol || !decimals || !initialSupply || !networkId || !creatorAddress) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Validate network
    const network = getNetworkById(networkId)
    if (!network || network.type !== "bnb") {
      return NextResponse.json({ error: "Invalid BNB network ID" }, { status: 400 })
    }

    // Create token
    const tokenAddress = await BnbChainService.createToken(
      {
        name,
        symbol,
        decimals,
        initialSupply,
        description,
        website,
        twitter,
        telegram,
        networkId,
        targetSuffix, // Pass the target suffix if provided
      },
      creatorAddress,
    )

    return NextResponse.json({
      success: true,
      tokenAddress,
      name,
      symbol,
      decimals,
      initialSupply,
      createdAt: new Date().toISOString(),
    })
  } catch (error: any) {
    console.error("Error creating BNB token:", error)
    return NextResponse.json({ error: error.message || "Failed to create BNB token" }, { status: 500 })
  }
}

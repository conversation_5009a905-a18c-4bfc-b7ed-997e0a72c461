import { type NextRequest, NextResponse } from "next/server"
import { Connection, Transaction } from "@solana/web3.js"
import { getNetworkById } from "@/lib/network-config"

export async function POST(req: NextRequest) {
  try {
    // Récupérer les paramètres de la requête
    const data = await req.json()
    const { signedTransaction, signedMintTransaction, tokenAddress, networkId } = data

    // Valider les paramètres
    if (!signedTransaction || !signedMintTransaction || !tokenAddress) {
      return NextResponse.json(
        { success: false, error: "Paramètres manquants: signedTransaction, signedMintTransaction, tokenAddress" },
        { status: 400 },
      )
    }

    // Récupérer la connexion RPC pour le réseau spécifié
    let rpcUrl = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com"
    if (networkId) {
      const network = getNetworkById(networkId)
      if (network) {
        rpcUrl = network.rpcUrl
      }
    }

    console.log(`Utilisation du RPC: ${rpcUrl}`)
    const connection = new Connection(rpcUrl, "confirmed")

    // Décoder les transactions signées
    const transaction = Transaction.from(Buffer.from(signedTransaction, "base64"))
    const mintTransaction = Transaction.from(Buffer.from(signedMintTransaction, "base64"))

    console.log("Transaction décodée:", transaction)
    console.log("Transaction de mint décodée:", mintTransaction)

    try {
      // Envoyer la transaction d'initialisation du mint
      console.log("Envoi de la transaction d'initialisation...")
      const signature = await connection.sendRawTransaction(transaction.serialize())
      console.log(`Transaction envoyée: ${signature}`)

      // Attendre la confirmation
      console.log("Attente de la confirmation...")
      const confirmation = await connection.confirmTransaction(signature, "confirmed")
      console.log("Confirmation reçue:", confirmation)

      if (confirmation.value.err) {
        throw new Error(`Erreur lors de la confirmation: ${JSON.stringify(confirmation.value.err)}`)
      }

      // Envoyer la transaction de mint
      console.log("Envoi de la transaction de mint...")
      const mintSignature = await connection.sendRawTransaction(mintTransaction.serialize())
      console.log(`Transaction de mint envoyée: ${mintSignature}`)

      // Attendre la confirmation
      console.log("Attente de la confirmation du mint...")
      const mintConfirmation = await connection.confirmTransaction(mintSignature, "confirmed")
      console.log("Confirmation du mint reçue:", mintConfirmation)

      if (mintConfirmation.value.err) {
        throw new Error(`Erreur lors de la confirmation du mint: ${JSON.stringify(mintConfirmation.value.err)}`)
      }

      return NextResponse.json({
        success: true,
        tokenAddress,
        signature,
        mintSignature,
      })
    } catch (txError: any) {
      console.error("Erreur lors de l'envoi ou de la confirmation des transactions:", txError)

      // Vérifier si l'erreur est liée à un problème de blockhash expiré
      if (txError.message.includes("blockhash") || txError.message.includes("timeout")) {
        return NextResponse.json(
          {
            success: false,
            error: "Le blockhash de la transaction a expiré. Veuillez réessayer.",
            details: txError.message,
          },
          { status: 400 },
        )
      }

      throw txError
    }
  } catch (error: any) {
    console.error("Erreur lors de la finalisation de la création du token:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la finalisation de la création du token",
        stack: error.stack,
      },
      { status: 500 },
    )
  }
}

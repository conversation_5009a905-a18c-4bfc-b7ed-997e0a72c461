import { type NextRequest, NextResponse } from "next/server"
import { realTokenService } from "@/lib/real-token-service"
import { z } from "zod"

// Schéma de validation pour les paramètres de vérification de gel
const checkFrozenParamsSchema = z.object({
  mintAddress: z.string().min(32, "Adresse du mint invalide").max(44, "Adresse du mint invalide"),
  targetAddress: z.string().min(32, "Adresse cible invalide").max(44, "Adresse cible invalide"),
  networkId: z.string().optional().default("solana-devnet"),
})

export async function GET(req: NextRequest) {
  try {
    // Récupérer les paramètres de la requête
    const url = new URL(req.url)
    const mintAddress = url.searchParams.get("mintAddress")
    const targetAddress = url.searchParams.get("targetAddress")
    const networkId = url.searchParams.get("networkId") || "solana-devnet"

    if (!mintAddress || !targetAddress) {
      return NextResponse.json({ success: false, error: "Adresses du mint et de la cible requises" }, { status: 400 })
    }

    // Valider les paramètres
    const validationResult = checkFrozenParamsSchema.safeParse({
      mintAddress,
      targetAddress,
      networkId,
    })

    if (!validationResult.success) {
      return NextResponse.json(
        { success: false, error: validationResult.error.errors.map((e) => e.message).join(", ") },
        { status: 400 },
      )
    }

    const validatedData = validationResult.data

    // Vérifier si le compte est gelé
    const result = await realTokenService.isTokenFrozen(
      validatedData.mintAddress,
      validatedData.targetAddress,
      validatedData.networkId,
    )

    if (result.error) {
      return NextResponse.json(
        { success: false, error: result.error || "Échec de la vérification du statut de gel" },
        { status: 500 },
      )
    }

    // Retourner le statut de gel
    return NextResponse.json({
      success: true,
      isFrozen: result.isFrozen,
      mintAddress: validatedData.mintAddress,
      targetAddress: validatedData.targetAddress,
    })
  } catch (error: any) {
    console.error("Erreur lors de la vérification du statut de gel:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la vérification du statut de gel",
      },
      { status: 500 },
    )
  }
}

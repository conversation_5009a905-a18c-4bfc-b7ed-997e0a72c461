import { type NextRequest, NextResponse } from "next/server"
import TokenLiquidityService from "@/lib/token-liquidity-service"

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const data = await req.json()
    const { pairAddress, ownerAddress, lockDuration } = data

    // Validate required fields
    if (!pairAddress || !ownerAddress || !lockDuration) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Lock liquidity
    const result = await TokenLiquidityService.lockLiquidity(pairAddress, ownerAddress, lockDuration)

    if (!result.success) {
      return NextResponse.json({ error: result.error || "Failed to lock liquidity" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      lockId: result.lockId,
    })
  } catch (error: any) {
    console.error("Error locking liquidity:", error)
    return NextResponse.json({ error: error.message || "Failed to lock liquidity" }, { status: 500 })
  }
}

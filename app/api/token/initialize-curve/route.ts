import { NextResponse } from "next/server"
import bondingCurveService from "@/lib/bonding-curve-service"

export async function POST(request: Request) {
  try {
    const { tokenAddress, initialSupply, maxSupply, solPriceUsd, dexListingThresholdUsd, reserveRatio, initialPrice } =
      await request.json()

    // Validation des paramètres requis
    if (!tokenAddress || !initialSupply || !maxSupply || !solPriceUsd || !dexListingThresholdUsd) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameters",
        },
        { status: 400 },
      )
    }

    // Initialiser la bonding curve
    bondingCurveService.initializeCurve({
      tokenAddress,
      initialSupply,
      maxSupply,
      solPriceUsd,
      dexListingThresholdUsd,
      reserveRatio: reserveRatio || 0.2,
      initialPrice: initialPrice || 0.00001,
    })

    // Simuler quelques achats pour vérifier
    const simulations = [
      bondingCurveService.simulatePurchase(tokenAddress, initialSupply, 0.1),
      bondingCurveService.simulatePurchase(tokenAddress, initialSupply, 0.5),
      bondingCurveService.simulatePurchase(tokenAddress, initialSupply, 1.0),
    ]

    return NextResponse.json({
      success: true,
      message: "Bonding curve initialized successfully",
      simulations,
    })
  } catch (error: any) {
    console.error("Error initializing bonding curve:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An error occurred",
      },
      { status: 500 },
    )
  }
}

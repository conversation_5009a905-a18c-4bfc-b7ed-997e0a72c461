import { type NextRequest, NextResponse } from "next/server"
import Key<PERSON><PERSON><PERSON><PERSON> from "@/lib/keypair-manager"
import TokenSuffixService from "@/lib/token-suffix-service"

export async function POST(request: NextRequest) {
  try {
    // Check if the request is authorized (in a real app, you'd check for admin privileges)
    // For now, we'll just proceed with the request

    const body = await request.json()
    const { suffix, networkId, count } = body

    if (!suffix || !networkId) {
      return NextResponse.json({ error: "Missing required parameters: suffix and networkId" }, { status: 400 })
    }

    // Validate the suffix
    const validation = TokenSuffixService.validateSuffix(suffix)
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error || "Invalid suffix" }, { status: 400 })
    }

    // Pre-generate keypairs
    const addresses = await KeypairManager.preGenerateKeypairs(suffix, networkId, count || 5)

    return NextResponse.json({
      success: true,
      message: `Successfully pre-generated ${addresses.length} keypairs`,
      addresses,
    })
  } catch (error: any) {
    console.error("Error pre-generating keypairs:", error)

    return NextResponse.json({ error: error.message || "Failed to pre-generate keypairs" }, { status: 500 })
  }
}

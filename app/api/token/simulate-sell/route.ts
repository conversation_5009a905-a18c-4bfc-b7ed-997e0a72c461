import { type NextRequest, NextResponse } from "next/server"
import tokenSellService from "@/lib/token-sell-service"
import bondingCurveService from "@/lib/bonding-curve-service"

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { tokenAddress, currentSupply, tokenAmount } = await request.json()

    // Validate required parameters
    if (!tokenAddress || currentSupply === undefined || !tokenAmount) {
      return NextResponse.json({ success: false, error: "Missing required parameters" }, { status: 400 })
    }

    // Check if the bonding curve exists for this token
    const curve = bondingCurveService.getCurveParams(tokenAddress)
    if (!curve) {
      // Initialize a default curve for simulation
      bondingCurveService.initializeCurve({
        tokenAddress,
        initialSupply: currentSupply,
        maxSupply: 1000000000, // 1 billion
        solPriceUsd: 179, // Current SOL price in USD
        dexListingThresholdUsd: 69000, // DEX listing threshold in USD
      })
    }

    // Calculate the SOL to receive
    const solToReceive = tokenSellService.calculateSolToReceive(tokenAddress, currentSupply, tokenAmount)

    // Calculate the new supply and price
    const newSupply = currentSupply - tokenAmount
    const newPrice = bondingCurveService.calculatePrice(tokenAddress, newSupply)
    const pricePerToken = solToReceive / tokenAmount

    // Calculate the new market cap
    const marketCapUsd = newPrice * newSupply * (curve?.solPriceUsd || 179)

    // Calculate the DEX listing progress
    const dexListingProgress = Math.min(100, (marketCapUsd / (curve?.dexListingThresholdUsd || 69000)) * 100)

    return NextResponse.json({
      success: true,
      simulation: {
        solToReceive,
        tokenAmount,
        pricePerToken,
        newSupply,
        newPrice,
        marketCapUsd,
        dexListingProgress,
      },
    })
  } catch (error: any) {
    console.error("Error simulating sell:", error)
    return NextResponse.json({ success: false, error: error.message || "An error occurred" }, { status: 500 })
  }
}

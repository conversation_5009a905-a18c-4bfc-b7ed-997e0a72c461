import { type NextRequest, NextResponse } from "next/server"
import { Transaction } from "@solana/web3.js"
import tokenSellService from "@/lib/token-sell-service"
import secureKeyService from "@/lib/secure-key-service"

export async function POST(request: NextRequest) {
  try {
    // Check if the platform keypair is available
    if (!secureKeyService.isPlatformKeypairAvailable()) {
      return NextResponse.json(
        {
          success: false,
          error: "Platform private key not found in environment variables. Please check server configuration.",
        },
        { status: 500 },
      )
    }

    // Parse the request body
    const { tokenAddress, sellerAddress, tokenAmount, currentSupply, signedTransaction } = await request.json()

    // Validate required parameters
    if (!tokenAddress || !sellerAddress || !tokenAmount || currentSupply === undefined) {
      return NextResponse.json({ success: false, error: "Missing required parameters" }, { status: 400 })
    }

    // If a signed transaction is provided, process it
    if (signedTransaction) {
      // Deserialize the transaction
      const transaction = Transaction.from(Buffer.from(signedTransaction, "base64"))

      // Process the sell with the signed transaction
      const result = await tokenSellService.processSell({
        tokenAddress,
        sellerAddress,
        tokenAmount,
        currentSupply,
      })

      return NextResponse.json(result)
    } else {
      // If no signed transaction is provided, create a new transaction for the client to sign
      const { transaction, solToReceive } = await tokenSellService.createBurnTransaction({
        tokenAddress,
        sellerAddress,
        tokenAmount,
        currentSupply,
      })

      return NextResponse.json({
        success: true,
        transaction: Buffer.from(transaction.serialize()).toString("base64"),
        solToReceive,
      })
    }
  } catch (error: any) {
    console.error("Error in token sell API:", error)
    return NextResponse.json({ success: false, error: error.message || "An error occurred" }, { status: 500 })
  }
}

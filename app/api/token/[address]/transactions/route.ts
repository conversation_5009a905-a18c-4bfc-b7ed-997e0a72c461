import { type NextRequest, NextResponse } from "next/server"
import tokenMetricsService from "@/lib/token-metrics-service"

export async function GET(request: NextRequest, { params }: { params: { address: string } }) {
  try {
    const { address } = params

    // Get query parameters
    const url = new URL(request.url)
    const limit = Number.parseInt(url.searchParams.get("limit") || "10", 10)

    // Get the token transactions
    const transactions = await tokenMetricsService.getTransactions(address, limit)

    // If no transactions exist, provide mock data for the demo
    if (transactions.length === 0) {
      // Generate some mock transactions
      const mockTransactions = Array.from({ length: 5 }).map((_, i) => ({
        id: `mock-${i}`,
        type: i % 2 === 0 ? "buy" : ("sell" as "buy" | "sell"),
        amount: Math.floor(Math.random() * 10000) + 1000,
        price: 0.00001 * (1 + i / 10),
        total: 0.00001 * (1 + i / 10) * (Math.floor(Math.random() * 10000) + 1000),
        timestamp: new Date(Date.now() - i * 60000 * 10).toISOString(),
        address: `${address.substring(0, 10)}...${i}`,
        txSignature: `mock-tx-${i}`,
      }))

      return NextResponse.json({
        success: true,
        transactions: mockTransactions,
      })
    }

    return NextResponse.json({
      success: true,
      transactions,
    })
  } catch (error: any) {
    console.error("Error fetching token transactions:", error)
    return NextResponse.json({ success: false, error: error.message || "An error occurred" }, { status: 500 })
  }
}

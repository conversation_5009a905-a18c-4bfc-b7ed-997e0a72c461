import { type NextRequest, NextResponse } from "next/server"
import tokenMetricsService from "@/lib/token-metrics-service"

export async function GET(request: NextRequest, { params }: { params: { address: string } }) {
  try {
    const { address } = params

    // Get the token metrics
    let metrics = await tokenMetricsService.getTokenMetrics(address)

    // If metrics don't exist, calculate and store them
    if (!metrics) {
      metrics = await tokenMetricsService.calculateAndUpdateTokenMetrics(address, 1000000, 179, 69000)
    }

    return NextResponse.json({
      success: true,
      metrics: {
        currentSupply: metrics.totalSupply,
        maxSupply: 1000000000,
        currentPrice: metrics.currentPrice,
        marketCapUsd: metrics.marketCapUsd,
        solPriceUsd: 179,
        dexListingThresholdUsd: 69000,
        dexListingProgress: metrics.dexListingProgress,
        holders: metrics.holders,
        transactions: metrics.transactions,
      },
    })
  } catch (error: any) {
    console.error("Error fetching token metrics:", error)
    return NextResponse.json({ success: false, error: error.message || "An error occurred" }, { status: 500 })
  }
}

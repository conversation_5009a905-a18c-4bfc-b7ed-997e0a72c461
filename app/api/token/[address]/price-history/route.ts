import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest, { params }: { params: { address: string } }) {
  try {
    const { address } = params

    // In a real app, you would fetch historical price data from a database or blockchain
    // For demo purposes, generate mock data
    const now = Date.now()
    const oneDayInMs = 24 * 60 * 60 * 1000

    // Generate 30 days of mock price data
    const mockPriceHistory = Array.from({ length: 30 }).map((_, i) => {
      const date = new Date(now - (29 - i) * oneDayInMs)

      // Create a slightly increasing price trend with some randomness
      const basePrice = 0.000001 * (1 + i / 30)
      const randomFactor = 0.8 + Math.random() * 0.4 // Between 0.8 and 1.2

      return {
        timestamp: date.toISOString(),
        price: basePrice * randomFactor,
      }
    })

    return NextResponse.json({
      success: true,
      priceHistory: mockPriceHistory,
    })
  } catch (error: any) {
    console.error("Error fetching token price history:", error)
    return NextResponse.json({ success: false, error: error.message || "An error occurred" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { Connection, PublicKey, Transaction, SystemProgram } from "@solana/web3.js"
import { envConfig } from "@/lib/env-config"

export async function POST(req: NextRequest) {
  try {
    // Récupérer les paramètres de la requête
    const data = await req.json()
    const { payerAddress, amount } = data

    // Valider les paramètres
    if (!payerAddress || !amount) {
      return NextResponse.json({ success: false, error: "Paramètres manquants: payerAddress, amount" }, { status: 400 })
    }

    // Créer une connexion à Solana
    const connection = new Connection(envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com", "confirmed")

    // Créer une transaction de transfert
    const transaction = new Transaction()
    const payerPublicKey = new PublicKey(payerAddress)
    const treasuryAddress = new PublicKey(envConfig.TREASURY_ADDRESS || "11111111111111111111111111111111")

    // Convertir le montant en lamports
    const lamports = Math.floor(amount * 1e9)

    // Ajouter l'instruction de transfert
    transaction.add(
      SystemProgram.transfer({
        fromPubkey: payerPublicKey,
        toPubkey: treasuryAddress,
        lamports,
      }),
    )

    // Récupérer le blockhash récent
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash("confirmed")
    transaction.recentBlockhash = blockhash
    transaction.feePayer = payerPublicKey

    // Sérialiser la transaction
    const serializedTransaction = transaction.serialize({ requireAllSignatures: false })
    const base64Transaction = serializedTransaction.toString("base64")

    return NextResponse.json({
      success: true,
      transaction: base64Transaction,
      message: `Paiement de ${amount} SOL pour la création du token`,
    })
  } catch (error: any) {
    console.error("Erreur lors de la création de la transaction de paiement:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la création de la transaction de paiement",
      },
      { status: 500 },
    )
  }
}

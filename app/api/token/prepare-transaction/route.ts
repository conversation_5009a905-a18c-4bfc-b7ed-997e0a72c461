import { type NextRequest, NextResponse } from "next/server"
import { Keypair } from "@solana/web3.js"
import TokenSuffixService from "@/lib/token-suffix-service"
import { sanitizeToBase58, isValidBase58 } from "@/lib/base58-utils"

export async function POST(req: NextRequest) {
  try {
    // Récupérer les données de la requête
    const data = await req.json()

    // Sanitiser les entrées sensibles
    const sanitizedData = {
      ...data,
      name: sanitizeToBase58(data.name),
      symbol: sanitizeToBase58(data.symbol),
      suffix: sanitizeToBase58(data.suffix || "GF"),
    }

    // Vérifier si des sanitisations ont été effectuées
    if (
      sanitizedData.name !== data.name ||
      sanitizedData.symbol !== data.symbol ||
      sanitizedData.suffix !== data.suffix
    ) {
      console.warn("Des caractères non valides ont été détectés et sanitisés dans les données d'entrée")
    }

    // Validation des champs requis
    if (
      !sanitizedData.name ||
      !sanitizedData.symbol ||
      !sanitizedData.decimals ||
      !sanitizedData.initialSupply ||
      !sanitizedData.ownerAddress
    ) {
      return NextResponse.json({ error: "Champs requis manquants" }, { status: 400 })
    }

    // Vérifier que le symbole est valide en base58
    if (!isValidBase58(sanitizedData.symbol)) {
      return NextResponse.json(
        {
          error: "Le symbole contient des caractères non valides en base58",
          sanitizedSymbol: sanitizeToBase58(sanitizedData.symbol),
        },
        { status: 400 },
      )
    }

    // Obtenir une paire de clés avec le suffixe spécifié
    const result = await TokenSuffixService.getKeypairWithSuffix(sanitizedData.suffix || "GF")

    if (!result.success || !result.keypair) {
      return NextResponse.json({ error: result.error || "Échec de la génération de la paire de clés" }, { status: 500 })
    }

    // Créer un keypair à partir des données reçues
    const mintKeypair = Keypair.fromSecretKey(Uint8Array.from(result.keypair.secret))

    // Préparer la transaction (dans une implémentation réelle, vous créeriez la transaction ici)
    // Pour cet exemple, nous simulons simplement la préparation
    const transaction = new Keypair().publicKey // Simuler une transaction

    return NextResponse.json({
      success: true,
      mintAddress: mintKeypair.publicKey.toString(),
      serializedTransaction: Buffer.from("transaction_data_placeholder").toString("base64"),
      message: "Transaction préparée avec succès",
    })
  } catch (error: any) {
    console.error("Erreur lors de la préparation de la transaction:", error)
    return NextResponse.json(
      {
        error: error.message || "Une erreur s'est produite lors de la préparation de la transaction",
        isBase58Error: error.message && error.message.toLowerCase().includes("base58"),
      },
      { status: 500 },
    )
  }
}

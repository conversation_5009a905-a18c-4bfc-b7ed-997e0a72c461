import { type NextRequest, NextResponse } from "next/server"
import { realTokenService, type TokenCreationParams } from "@/lib/real-token-service"
import { z } from "zod"
import { sanitizeToBase58 } from "@/lib/base58-sanitizer"
import { verifyAd<PERSON><PERSON>ey } from "@/lib/admin-auth"

// Schéma de validation pour les paramètres de création de token
const tokenParamsSchema = z.object({
  name: z.string().min(1, "Le nom du token est requis").max(50, "Le nom du token ne doit pas dépasser 50 caractères"),
  symbol: z
    .string()
    .min(1, "Le symbole du token est requis")
    .max(10, "Le symbole du token ne doit pas dépasser 10 caractères"),
  decimals: z.number().int().min(0).max(9).optional().default(9),
  initialSupply: z.number().positive("L'offre initiale doit être positive").optional().default(1000000000),
  maxSupply: z.number().positive("L'offre maximale doit être positive").optional(),
  ownerAddress: z.string().min(32, "Adresse du propriétaire invalide").max(44, "Adresse du propriétaire invalide"),
  adminKey: z.string().min(1, "Clé d'administration requise"),
  metadata: z
    .object({
      name: z.string(),
      symbol: z.string(),
      description: z.string().optional(),
      image: z.string().optional(),
    })
    .optional(),
  isMintable: z.boolean().optional().default(true),
  isBurnable: z.boolean().optional().default(true),
  isFreezable: z.boolean().optional().default(false),
  isPausable: z.boolean().optional().default(false),
  isTransferTaxable: z.boolean().optional().default(false),
  transferTaxRate: z.number().min(0).max(10).optional().default(0),
  maxTxAmount: z.number().optional(),
  maxWalletAmount: z.number().optional(),
  antiBot: z.boolean().optional().default(false),
  antiDump: z.boolean().optional().default(false),
  networkId: z.string().optional().default("solana-devnet"),
})

export async function POST(req: NextRequest) {
  try {
    // Récupérer les paramètres de la requête
    const data = await req.json()

    // Valider les paramètres
    const validationResult = tokenParamsSchema.safeParse(data)
    if (!validationResult.success) {
      return NextResponse.json(
        { success: false, error: validationResult.error.errors.map((e) => e.message).join(", ") },
        { status: 400 },
      )
    }

    const validatedData = validationResult.data

    // Vérifier la clé d'administration
    const isAdmin = await verifyAdminKey(validatedData.adminKey)
    if (!isAdmin) {
      return NextResponse.json({ success: false, error: "Clé d'administration invalide" }, { status: 403 })
    }

    // Sanitiser les entrées
    const sanitizedSymbol = sanitizeToBase58(validatedData.symbol)
    if (sanitizedSymbol !== validatedData.symbol) {
      console.warn(`Le symbole a été sanitisé: ${validatedData.symbol} -> ${sanitizedSymbol}`)
      validatedData.symbol = sanitizedSymbol
    }

    // Récupérer la clé privée du payeur depuis les variables d'environnement
    const payerPrivateKey = process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY
    if (!payerPrivateKey) {
      return NextResponse.json({ success: false, error: "Clé privée du payeur non configurée" }, { status: 500 })
    }

    // Préparer les paramètres de création du token
    const tokenParams: TokenCreationParams = {
      name: validatedData.name,
      symbol: validatedData.symbol,
      decimals: validatedData.decimals,
      initialSupply: validatedData.initialSupply,
      maxSupply: validatedData.maxSupply,
      ownerAddress: validatedData.ownerAddress,
      payerPrivateKey,
      metadata: validatedData.metadata,
      isMintable: validatedData.isMintable,
      isBurnable: validatedData.isBurnable,
      isFreezable: validatedData.isFreezable,
      isPausable: validatedData.isPausable,
      isTransferTaxable: validatedData.isTransferTaxable,
      transferTaxRate: validatedData.transferTaxRate,
      maxTxAmount: validatedData.maxTxAmount,
      maxWalletAmount: validatedData.maxWalletAmount,
      antiBot: validatedData.antiBot,
      antiDump: validatedData.antiDump,
      networkId: validatedData.networkId,
    }

    // Créer le token sur la blockchain Solana
    const result = await realTokenService.createToken(tokenParams)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error || "Échec de la création du token" },
        { status: 500 },
      )
    }

    // Retourner les détails du token créé
    return NextResponse.json({
      success: true,
      mintAddress: result.mintAddress,
      tokenAccount: result.tokenAccount,
      signature: result.signature,
      metadataAddress: result.metadataAddress,
      freezeAuthority: result.freezeAuthority,
      mintAuthority: result.mintAuthority,
      name: validatedData.name,
      symbol: validatedData.symbol,
      decimals: validatedData.decimals,
      initialSupply: validatedData.initialSupply,
      maxSupply: validatedData.maxSupply,
      isMintable: validatedData.isMintable,
      isBurnable: validatedData.isBurnable,
      isFreezable: validatedData.isFreezable,
    })
  } catch (error: any) {
    console.error("Erreur lors de la création du token:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la création du token",
      },
      { status: 500 },
    )
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { Keypair } from "@solana/web3.js"
import TokenLiquidityService from "@/lib/token-liquidity-service"

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const data = await req.json()
    const { tokenAddress, ownerAddress, liquidityAmount, tokenAmount } = data

    // Validate required fields
    if (!tokenAddress || !ownerAddress || !liquidityAmount || !tokenAmount) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Get admin keypair
    let adminPrivateKey: string | undefined

    if (process.env.ADMIN_WALLET) {
      adminPrivateKey = process.env.ADMIN_WALLET
    } else if (process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY) {
      adminPrivateKey = process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY
    } else if (process.env.PRE_MINED_MMGF_PRIVATE_KEY) {
      adminPrivateKey = process.env.PRE_MINED_MMGF_PRIVATE_KEY
    }

    if (!adminPrivateKey) {
      return NextResponse.json({ error: "Admin wallet not configured" }, { status: 500 })
    }

    // Convert private key to keypair
    let adminKeypair: Keypair
    try {
      const secretKey = Buffer.from(adminPrivateKey, "base64")
      adminKeypair = Keypair.fromSecretKey(secretKey)
    } catch (error) {
      console.error("Error decoding admin private key:", error)
      return NextResponse.json({ error: "Invalid admin private key format" }, { status: 500 })
    }

    // Add liquidity
    const result = await TokenLiquidityService.addInitialLiquidity(
      {
        tokenAddress,
        ownerAddress,
        liquidityAmount,
        tokenAmount,
      },
      adminKeypair,
    )

    if (!result.success) {
      return NextResponse.json({ error: result.error || "Failed to add liquidity" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      pairAddress: result.pairAddress,
    })
  } catch (error: any) {
    console.error("Error adding liquidity:", error)
    return NextResponse.json({ error: error.message || "Failed to add liquidity" }, { status: 500 })
  }
}

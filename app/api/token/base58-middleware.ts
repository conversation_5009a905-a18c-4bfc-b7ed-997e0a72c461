import { NextResponse } from "next/server"

// Caractères valides en base58
const BASE58_CHARS = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

// Fonction pour vérifier si une chaîne est valide en base58
export function isValidBase58(str: string): boolean {
  if (!str) return false
  for (let i = 0; i < str.length; i++) {
    if (!BASE58_CHARS.includes(str[i])) {
      return false
    }
  }
  return true
}

// Fonction pour nettoyer une chaîne et ne garder que les caractères base58
export function sanitizeToBase58(str: string): string {
  if (!str) return ""
  let result = ""
  for (let i = 0; i < str.length; i++) {
    if (BASE58_CHARS.includes(str[i])) {
      result += str[i]
    }
  }
  return result
}

// Middleware pour sanitiser les entrées base58 dans les requêtes API
export function sanitizeBase58Inputs(req: any) {
  try {
    // Si la requête a un corps et contient un symbole
    if (req.body && req.body.symbol) {
      const symbol = req.body.symbol

      // Vérifier si le symbole est valide en base58
      if (!isValidBase58(symbol)) {
        // Sanitiser le symbole
        const sanitizedSymbol = sanitizeToBase58(symbol)

        // Si le symbole sanitisé est vide, renvoyer une erreur
        if (!sanitizedSymbol) {
          return NextResponse.json(
            {
              success: false,
              error: "Le symbole ne contient aucun caractère valide en base58",
            },
            { status: 400 },
          )
        }

        // Sinon, remplacer le symbole par la version sanitisée
        req.body.symbol = sanitizedSymbol
      }
    }

    // Si la requête a un corps et contient un suffixe
    if (req.body && req.body.suffix) {
      const suffix = req.body.suffix

      // Vérifier si le suffixe est valide en base58
      if (!isValidBase58(suffix)) {
        // Sanitiser le suffixe
        const sanitizedSuffix = sanitizeToBase58(suffix)

        // Si le suffixe sanitisé est vide, renvoyer une erreur
        if (!sanitizedSuffix) {
          return NextResponse.json(
            {
              success: false,
              error: "Le suffixe ne contient aucun caractère valide en base58",
            },
            { status: 400 },
          )
        }

        // Sinon, remplacer le suffixe par la version sanitisée
        req.body.suffix = sanitizedSuffix
      }
    }

    // Continuer le traitement de la requête
    return null
  } catch (error) {
    console.error("Erreur dans le middleware de sanitisation base58:", error)
    return null
  }
}

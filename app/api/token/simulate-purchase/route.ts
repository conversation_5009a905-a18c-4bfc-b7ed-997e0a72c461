import { type NextRequest, NextResponse } from "next/server"
import bondingCurveService from "@/lib/bonding-curve-service"

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { tokenAddress, currentSupply, amountInSol } = await request.json()

    // Validate required parameters
    if (!tokenAddress || currentSupply === undefined || !amountInSol) {
      return NextResponse.json({ success: false, error: "Missing required parameters" }, { status: 400 })
    }

    // Check if the bonding curve exists for this token
    const curve = bondingCurveService.getCurveParams(tokenAddress)
    if (!curve) {
      // Initialize a default curve for simulation
      bondingCurveService.initializeCurve({
        tokenAddress,
        initialSupply: currentSupply,
        maxSupply: 1000000000, // 1 billion
        solPriceUsd: 179, // Current SOL price in USD
        dexListingThresholdUsd: 69000, // DEX listing threshold in USD
      })
    }

    // Simulate the purchase
    const simulation = bondingCurveService.simulatePurchase(tokenAddress, currentSupply, amountInSol)

    return NextResponse.json({
      success: true,
      simulation,
    })
  } catch (error: any) {
    console.error("Error simulating purchase:", error)
    return NextResponse.json({ success: false, error: error.message || "An error occurred" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import tokenCreationService from "@/lib/token-creation-service"

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Traitement de la création du token...
    const result = await tokenCreationService.createToken(data.params, data.paymentSignature, data.mintKeypair)

    if (result.success) {
      // Rediriger vers la page d'achat initial au lieu de la page de succès standard
      return NextResponse.json({
        success: true,
        tokenAddress: result.tokenAddress,
        redirectUrl: `/token-factory/initial-purchase?address=${result.tokenAddress}&name=${encodeURIComponent(data.params.name)}&symbol=${encodeURIComponent(data.params.symbol)}&decimals=${data.params.decimals}&supply=${data.params.initialSupply}&network=${data.params.networkId || "devnet"}`,
        tokenData: result.tokenData,
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.error,
        },
        { status: 400 },
      )
    }
  } catch (error: any) {
    console.error("Erreur lors de la création du token:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la création du token",
      },
      { status: 500 },
    )
  }
}

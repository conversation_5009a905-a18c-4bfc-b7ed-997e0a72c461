import { type NextRequest, NextResponse } from "next/server"
import { createAdvancedToken, type AdvancedTokenConfig } from "@/lib/advanced-token-service"
import { sanitizeBase58Input } from "@/lib/input-sanitizer"

export async function POST(request: NextRequest) {
  try {
    // Récupérer les données de la requête
    const data = await request.json()

    // Valider les données
    if (!data.name || !data.symbol || !data.decimals || !data.initialSupply || !data.ownerAddress || !data.adminKey) {
      return NextResponse.json({ success: false, error: "Données manquantes" }, { status: 400 })
    }

    // Valider l'adresse du propriétaire
    const ownerAddress = sanitizeBase58Input(data.ownerAddress)
    if (!ownerAddress) {
      return NextResponse.json({ success: false, error: "Adresse du propriétaire invalide" }, { status: 400 })
    }

    // Préparer la configuration du token
    const tokenConfig: AdvancedTokenConfig = {
      name: data.name,
      symbol: data.symbol,
      decimals: data.decimals,
      initialSupply: data.initialSupply,
      ownerAddress: data.ownerAddress,
      adminKey: data.adminKey,
      description: data.description,
      imageUrl: data.imageUrl,

      fees: data.fees,
      transactionLimits: data.transactionLimits,
      priceImpactConfig: data.priceImpactConfig,

      networkId: data.networkId || "devnet",
    }

    // Créer le token
    const result = await createAdvancedToken(tokenConfig)

    if (!result.success) {
      return NextResponse.json({ success: false, error: result.error }, { status: 500 })
    }

    // Retourner les résultats
    return NextResponse.json({
      success: true,
      mintAddress: result.mintAddress,
      ownerTokenAccount: result.ownerTokenAccount,
      burnWallet: result.burnWallet,
      priceImpactWallet: result.priceImpactWallet,
      signature: result.signature,
    })
  } catch (error: any) {
    console.error("Erreur lors de la création du token avancé:", error)
    return NextResponse.json({ success: false, error: error.message || "Une erreur s'est produite" }, { status: 500 })
  }
}

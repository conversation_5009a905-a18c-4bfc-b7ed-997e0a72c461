import { type NextRequest, NextResponse } from "next/server"
import { Connection, Keypair, PublicKey, Transaction } from "@solana/web3.js"
import { getAssociatedTokenAddress, createTransferCheckedInstruction } from "@solana/spl-token"
import bs58 from "bs58"

// Adresse du portefeuille de la plateforme
const PLATFORM_WALLET = new PublicKey("7b8hK6apNE2dRgBXHqptZ6aXXAcrWmNPgnY6SCPcJn3f")

export async function POST(request: NextRequest) {
  try {
    // Récupérer les données de la requête
    const { tokenAddress, buyerAddress, tokensToReceive } = await request.json()

    // Vérifier les paramètres obligatoires
    if (!tokenAddress || !buyerAddress || !tokensToReceive) {
      return NextResponse.json({ success: false, error: "Missing required parameters" }, { status: 400 })
    }

    // Récupérer la clé privée de la plateforme depuis les variables d'environnement
    const platformPrivateKey = process.env.MMGF_PRIVATE_KEY
    if (!platformPrivateKey) {
      return NextResponse.json({ success: false, error: "Platform private key not found" }, { status: 500 })
    }

    // Créer le keypair de la plateforme
    const platformKeypair = Keypair.fromSecretKey(bs58.decode(platformPrivateKey))

    // Créer une connexion à Solana
    const connection = new Connection(
      process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com",
      "confirmed",
    )

    // Convertir les adresses en PublicKey
    const tokenPublicKey = new PublicKey(tokenAddress)
    const buyerPublicKey = new PublicKey(buyerAddress)

    // Obtenir l'adresse du compte de token associé pour l'acheteur
    const buyerTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, buyerPublicKey)

    // Obtenir l'adresse du compte de token associé pour la plateforme
    const platformTokenAccount = await getAssociatedTokenAddress(tokenPublicKey, platformKeypair.publicKey)

    // Calculer le montant de tokens à transférer
    const tokenAmount = BigInt(Math.floor(tokensToReceive * Math.pow(10, 9))) // 9 décimales pour Solana SPL tokens

    // Créer une transaction pour transférer les tokens
    const transaction = new Transaction()

    // Ajouter l'instruction pour transférer les tokens de la plateforme à l'acheteur
    transaction.add(
      createTransferCheckedInstruction(
        platformTokenAccount, // source
        tokenPublicKey, // mint
        buyerTokenAccount, // destination
        platformKeypair.publicKey, // owner
        tokenAmount, // amount
        9, // decimals
      ),
    )

    // Obtenir le blockhash récent
    const { blockhash } = await connection.getLatestBlockhash("confirmed")
    transaction.recentBlockhash = blockhash
    transaction.feePayer = platformKeypair.publicKey

    // Signer la transaction avec la clé de la plateforme
    transaction.sign(platformKeypair)

    // Envoyer la transaction
    const signature = await connection.sendRawTransaction(transaction.serialize())

    // Attendre la confirmation
    await connection.confirmTransaction(signature, "confirmed")

    return NextResponse.json({
      success: true,
      signature,
      message: `${tokensToReceive} tokens transferred to ${buyerAddress}`,
    })
  } catch (error: any) {
    console.error("Error transferring tokens:", error)
    return NextResponse.json({ success: false, error: error.message || "Failed to transfer tokens" }, { status: 500 })
  }
}

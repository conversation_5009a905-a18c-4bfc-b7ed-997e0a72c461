import { type NextRequest, NextResponse } from "next/server"
import { Keypair } from "@solana/web3.js"
import TokenLiquidityService from "@/lib/token-liquidity-service"

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const data = await req.json()
    const { tokenAddress, ownerAddress } = data

    // Validate required fields
    if (!tokenAddress || !ownerAddress) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Get admin keypair
    let adminPrivateKey: string | undefined

    if (process.env.ADMIN_WALLET) {
      adminPrivateKey = process.env.ADMIN_WALLET
    } else if (process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY) {
      adminPrivateKey = process.env.NEXT_PUBLIC_MMGF_PRIVATE_KEY
    } else if (process.env.PRE_MINED_MMGF_PRIVATE_KEY) {
      adminPrivateKey = process.env.PRE_MINED_MMGF_PRIVATE_KEY
    }

    if (!adminPrivateKey) {
      return NextResponse.json({ error: "Admin wallet not configured" }, { status: 500 })
    }

    // Convert private key to keypair
    let adminKeypair: Keypair
    try {
      const secretKey = Buffer.from(adminPrivateKey, "base64")
      adminKeypair = Keypair.fromSecretKey(secretKey)
    } catch (error) {
      console.error("Error decoding admin private key:", error)
      return NextResponse.json({ error: "Invalid admin private key format" }, { status: 500 })
    }

    // Setup initial trade
    const result = await TokenLiquidityService.setupInitialTrade(tokenAddress, ownerAddress, adminKeypair)

    if (!result.success) {
      return NextResponse.json({ error: result.error || "Failed to setup initial trade" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      txId: result.txId,
    })
  } catch (error: any) {
    console.error("Error setting up initial trade:", error)
    return NextResponse.json({ error: error.message || "Failed to setup initial trade" }, { status: 500 })
  }
}

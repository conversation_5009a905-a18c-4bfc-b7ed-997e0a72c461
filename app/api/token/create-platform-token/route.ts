import { type NextRequest, NextResponse } from "next/server"
import { Keypair } from "@solana/web3.js"
import tokenPlatformService from "@/lib/token-platform-service"
import { validateTokenParams } from "@/lib/token-validation-service"
import { sanitizeToBase58 } from "@/lib/base58-sanitizer"
import { envConfig } from "@/lib/env-config"

export async function POST(req: NextRequest) {
  try {
    // Récupérer les paramètres de la requête
    const data = await req.json()

    // Valider les paramètres
    const validationResult = validateTokenParams(data)
    if (!validationResult.valid) {
      return NextResponse.json({ success: false, error: validationResult.errors.join(", ") }, { status: 400 })
    }

    // Sanitiser les entrées
    const sanitizedSymbol = sanitizeToBase58(data.symbol)
    if (sanitizedSymbol !== data.symbol) {
      console.warn(`Le symbole a été sanitisé: ${data.symbol} -> ${sanitizedSymbol}`)
      data.symbol = sanitizedSymbol
    }

    // Vérifier le paiement (simulé)
    if (!data.paymentSignature) {
      return NextResponse.json({ success: false, error: "Payment signature is required" }, { status: 400 })
    }

    // Créer un keypair pour le payer à partir de la clé privée d'admin
    const adminPrivateKey = envConfig.ADMIN_KEY
    if (!adminPrivateKey) {
      return NextResponse.json({ success: false, error: "Admin key not configured" }, { status: 500 })
    }

    const payerKeypair = Keypair.fromSecretKey(Uint8Array.from(JSON.parse(adminPrivateKey)))

    // Créer le token
    const result = await tokenPlatformService.createToken(
      {
        name: data.name,
        symbol: data.symbol,
        decimals: data.decimals,
        initialSupply: data.initialSupply,
        maxSupply: data.maxSupply,
        description: data.description,
        website: data.website,
        twitter: data.twitter,
        telegram: data.telegram,
        discord: data.discord,
        imageUrl: data.imageUrl,
        suffix: data.suffix,
        isAIGenerated: data.isAIGenerated,
        ownerAddress: data.ownerAddress,
        referrerAddress: data.referrerAddress,
      },
      {
        team: data.distribution?.team || 15,
        marketing: data.distribution?.marketing || 10,
        development: data.distribution?.development || 15,
        reserve: data.distribution?.reserve || 10,
        liquidity: data.distribution?.liquidity || 50,
      },
      {
        antiBot: data.securityOptions?.antiBot !== false,
        antiDump: data.securityOptions?.antiDump !== false,
        maxTxPercentage: data.securityOptions?.maxTxPercentage || 1,
        maxWalletPercentage: data.securityOptions?.maxWalletPercentage || 3,
        tradingDelay: data.securityOptions?.tradingDelay || 0,
      },
      {
        addLiquidity: data.launchOptions?.addLiquidity || false,
        liquidityAmount: data.launchOptions?.liquidityAmount || 0.1,
        liquidityPercentage: data.launchOptions?.liquidityPercentage || 50,
        lockLiquidity: data.launchOptions?.lockLiquidity || false,
        lockDuration: data.launchOptions?.lockDuration || 180,
      },
      payerKeypair,
    )

    if (!result.success) {
      return NextResponse.json({ success: false, error: result.error || "Failed to create token" }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      tokenAddress: result.tokenAddress,
      transactionId: result.transactionId,
      details: result.details,
    })
  } catch (error: any) {
    console.error("Error creating platform token:", error)
    return NextResponse.json(
      { success: false, error: error.message || "An error occurred while creating the token" },
      { status: 500 },
    )
  }
}

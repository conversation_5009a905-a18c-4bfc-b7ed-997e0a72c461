import { type NextRequest, NextResponse } from "next/server"
import { Connection, Keypair, PublicKey, Transaction } from "@solana/web3.js"
import { getOrCreateAssociatedTokenAccount, mintTo } from "@solana/spl-token"

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const data = await req.json()
    const { serializedTransaction, mintAddress, tokenData } = data

    if (!serializedTransaction || !mintAddress || !tokenData) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Get RPC URL based on network
    const rpcUrl = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com"
    console.log(`Using RPC URL: ${rpcUrl}`)

    // Connect to Solana
    const connection = new Connection(rpcUrl, "confirmed")

    // Reconstruire la transaction à partir des données sérialisées
    const transaction = Transaction.from(Buffer.from(serializedTransaction, "base64"))

    // Soumettre la transaction
    console.log("Submitting transaction...")
    const signature = await connection.sendRawTransaction(transaction.serialize())

    // Attendre la confirmation
    console.log(`Transaction submitted: ${signature}`)
    await connection.confirmTransaction(signature, "confirmed")
    console.log("Transaction confirmed")

    // Créer un compte de token pour le propriétaire et minter l'offre initiale
    const mintPublicKey = new PublicKey(mintAddress)
    const ownerPublicKey = new PublicKey(tokenData.ownerAddress)

    // Utiliser la clé admin pour les opérations suivantes
    const adminKeypair = Keypair.fromSecretKey(Buffer.from(process.env.ADMIN_WALLET || "", "base64"))

    // Créer un compte de token pour le propriétaire
    console.log("Creating token account...")
    const tokenAccount = await getOrCreateAssociatedTokenAccount(
      connection,
      adminKeypair,
      mintPublicKey,
      ownerPublicKey,
    )

    console.log(`Token account created: ${tokenAccount.address.toString()}`)

    // Minter l'offre initiale au propriétaire
    console.log("Minting initial supply...")
    const initialAmount = Number(tokenData.initialSupply) * Math.pow(10, tokenData.decimals)
    const mintTx = await mintTo(
      connection,
      adminKeypair,
      mintPublicKey,
      tokenAccount.address,
      adminKeypair, // Utiliser l'admin comme autorité de mint
      BigInt(initialAmount.toString()),
    )

    console.log(`Initial supply minted: ${initialAmount}`)
    console.log(`Mint transaction: ${mintTx}`)

    // Enregistrer les métadonnées du token dans la base de données (dans une implémentation réelle)
    // ...

    return NextResponse.json({
      success: true,
      mintAddress,
      signature,
      tokenAccount: tokenAccount.address.toString(),
    })
  } catch (error: any) {
    console.error("Error submitting token transaction:", error)
    return NextResponse.json({ error: error.message || "Failed to submit token transaction" }, { status: 500 })
  }
}

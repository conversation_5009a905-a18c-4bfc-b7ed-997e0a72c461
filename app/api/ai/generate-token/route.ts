import { type NextRequest, NextResponse } from "next/server"
import { generateTokenFromPrompt } from "@/lib/ai-token-generator"

export async function POST(request: NextRequest) {
  try {
    // Extraire le prompt de la requête
    const { prompt } = await request.json()

    if (!prompt) {
      return NextResponse.json({ success: false, error: "Le prompt est requis" }, { status: 400 })
    }

    // Générer les paramètres du token
    const result = await generateTokenFromPrompt(prompt)

    if (!result.success) {
      return NextResponse.json({ success: false, error: result.error }, { status: 500 })
    }

    // Retourner les paramètres du token
    return NextResponse.json({
      success: true,
      tokenParams: result.tokenParams,
    })
  } catch (error: any) {
    console.error("Erreur lors de la génération du token:", error)
    return NextResponse.json({ success: false, error: error.message || "Une erreur s'est produite" }, { status: 500 })
  }
}

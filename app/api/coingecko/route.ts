import { type NextRequest, NextResponse } from "next/server"

// Utiliser la clé API depuis les variables d'environnement
const COINGECKO_API_KEY = process.env.COINGECKO_API_KEY || "CG-PTstKfmKuafEJm7N1Lwwrw2R"

// Revenir à l'URL standard pour les clés de démonstration
const COINGECKO_BASE_URL = "https://api.coingecko.com/api/v3"

// Cache simple pour stocker les réponses
const responseCache = new Map()
const CACHE_TTL = 60 * 1000 // 1 minute en millisecondes

export async function GET(request: NextRequest) {
  try {
    // Récupérer les paramètres de la requête
    const searchParams = request.nextUrl.searchParams
    const endpoint = searchParams.get("endpoint")

    if (!endpoint) {
      return NextResponse.json({ error: "Endpoint parameter is required" }, { status: 400 })
    }

    // S'assurer que l'endpoint commence par un slash
    const formattedEndpoint = endpoint.startsWith("/") ? endpoint : `/${endpoint}`

    // Construire l'URL complète
    const url = `${COINGECKO_BASE_URL}${formattedEndpoint}`

    // Vérifier si nous avons une réponse en cache
    const cacheKey = url
    const cachedResponse = responseCache.get(cacheKey)
    if (cachedResponse && Date.now() - cachedResponse.timestamp < CACHE_TTL) {
      console.log(`Using cached response for: ${url}`)
      return NextResponse.json(cachedResponse.data)
    }

    console.log(`Fetching CoinGecko API: ${url}`)

    // Préparer les headers
    const headers: HeadersInit = {
      Accept: "application/json",
    }

    // Ajouter la clé API si elle existe
    if (COINGECKO_API_KEY) {
      headers["x-cg-demo-api-key"] = COINGECKO_API_KEY
    }

    // Faire la requête à l'API CoinGecko avec timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 secondes timeout

    try {
      const response = await fetch(url, {
        headers,
        signal: controller.signal,
        next: { revalidate: 60 }, // Revalider les données toutes les 60 secondes
      })

      clearTimeout(timeoutId)

      // Vérifier si la réponse est OK
      if (!response.ok) {
        const errorText = await response.text()
        console.error(`CoinGecko API error: ${response.status}, ${errorText}`)

        // Si nous avons une réponse en cache, utilisons-la même si elle est expirée
        if (cachedResponse) {
          console.log(`Using expired cache as fallback for: ${url}`)
          return NextResponse.json(cachedResponse.data)
        }

        // Sinon, renvoyer une erreur
        return NextResponse.json(
          {
            error: `CoinGecko API error: ${response.status}`,
            details: errorText.substring(0, 200), // Limiter la taille des détails
          },
          { status: response.status },
        )
      }

      // Vérifier le type de contenu
      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.includes("application/json")) {
        console.warn(`Expected JSON but got ${contentType}`)
        const responseText = await response.text()
        console.warn(`Response content: ${responseText.substring(0, 200)}...`)

        // Si nous avons une réponse en cache, utilisons-la
        if (cachedResponse) {
          console.log(`Using cache as fallback for non-JSON response`)
          return NextResponse.json(cachedResponse.data)
        }

        return NextResponse.json(
          {
            error: "CoinGecko API returned non-JSON response",
            contentType,
          },
          { status: 500 },
        )
      }

      // Analyser la réponse JSON avec gestion d'erreur
      let data
      try {
        const text = await response.text()
        data = JSON.parse(text)
      } catch (parseError) {
        console.error("Error parsing JSON response:", parseError)

        // Si nous avons une réponse en cache, utilisons-la
        if (cachedResponse) {
          console.log(`Using cache as fallback for JSON parse error`)
          return NextResponse.json(cachedResponse.data)
        }

        return NextResponse.json(
          {
            error: "Failed to parse JSON response",
            details: parseError.message,
          },
          { status: 500 },
        )
      }

      // Mettre en cache la réponse
      responseCache.set(cacheKey, {
        data,
        timestamp: Date.now(),
      })

      return NextResponse.json(data)
    } catch (fetchError) {
      clearTimeout(timeoutId)

      // Gérer les erreurs de timeout
      if (fetchError.name === "AbortError") {
        console.error("CoinGecko API request timed out")

        // Si nous avons une réponse en cache, utilisons-la
        if (cachedResponse) {
          console.log(`Using cache as fallback for timeout`)
          return NextResponse.json(cachedResponse.data)
        }

        return NextResponse.json({ error: "Request timed out" }, { status: 504 })
      }

      // Autres erreurs de fetch
      console.error("Fetch error:", fetchError)

      // Si nous avons une réponse en cache, utilisons-la
      if (cachedResponse) {
        console.log(`Using cache as fallback for fetch error`)
        return NextResponse.json(cachedResponse.data)
      }

      throw fetchError // Relancer pour être capturé par le catch externe
    }
  } catch (error) {
    console.error("Error in CoinGecko API route:", error)

    // Renvoyer une réponse d'erreur générique
    return NextResponse.json(
      {
        error: "Failed to fetch data from CoinGecko",
        message: error.message || "Unknown error",
      },
      { status: 500 },
    )
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { daoGovernanceService } from "@/lib/dao-governance-service"
import { z } from "zod"
import { verifyWalletSignature } from "@/lib/wallet-auth"

// Schéma de validation pour la création de proposition
const createProposalSchema = z.object({
  tokenAddress: z.string().min(32, "Adresse du token invalide").max(44, "Adresse du token invalide"),
  walletToUnblock: z.string().min(32, "Adresse à débloquer invalide").max(44, "Adresse à débloquer invalide"),
  proposerWallet: z.string().min(32, "Adresse du proposant invalide").max(44, "Adresse du proposant invalide"),
  title: z.string().min(5, "Le titre est trop court").max(100, "Le titre est trop long"),
  description: z.string().min(10, "La description est trop courte").max(1000, "La description est trop longue"),
  signature: z.string().min(1, "Signature requise"),
  message: z.string().min(1, "Message à signer requis"),
  unblockSchedule: z
    .array(
      z.object({
        amount: z.number().positive("Le montant doit être positif"),
        releaseDate: z.number().positive("La date de libération doit être valide"),
      }),
    )
    .min(1, "Au moins un élément de planning de déblocage est requis"),
  networkId: z.string().optional().default("solana-devnet"),
})

// Schéma de validation pour le vote
const voteSchema = z.object({
  proposalId: z.string().min(1, "ID de proposition requis"),
  voter: z.string().min(32, "Adresse du votant invalide").max(44, "Adresse du votant invalide"),
  voteType: z.enum(["for", "against", "abstain"], {
    errorMap: () => ({ message: "Le type de vote doit être 'for', 'against' ou 'abstain'" }),
  }),
  signature: z.string().min(1, "Signature requise"),
  message: z.string().min(1, "Message à signer requis"),
  networkId: z.string().optional().default("solana-devnet"),
})

// Schéma de validation pour l'exécution
const executeSchema = z.object({
  proposalId: z.string().min(1, "ID de proposition requis"),
  executor: z.string().min(32, "Adresse de l'exécuteur invalide").max(44, "Adresse de l'exécuteur invalide"),
  signature: z.string().min(1, "Signature requise"),
  message: z.string().min(1, "Message à signer requis"),
  networkId: z.string().optional().default("solana-devnet"),
})

export async function POST(req: NextRequest) {
  try {
    // Récupérer les paramètres de la requête
    const data = await req.json()
    const action = req.nextUrl.searchParams.get("action") || "create"

    if (action === "create") {
      // Valider les paramètres
      const validationResult = createProposalSchema.safeParse(data)
      if (!validationResult.success) {
        return NextResponse.json(
          { success: false, error: validationResult.error.errors.map((e) => e.message).join(", ") },
          { status: 400 },
        )
      }

      const validatedData = validationResult.data

      // Vérifier la signature du wallet
      const isValidSignature = await verifyWalletSignature(
        validatedData.proposerWallet,
        validatedData.message,
        validatedData.signature,
      )

      if (!isValidSignature) {
        return NextResponse.json({ success: false, error: "Signature du wallet invalide" }, { status: 403 })
      }

      // Créer la proposition
      const proposalResult = await daoGovernanceService.submitUnblockProposal(
        validatedData.tokenAddress,
        validatedData.walletToUnblock,
        validatedData.proposerWallet,
        "", // Pas besoin de la clé privée dans cette implémentation
        validatedData.title,
        validatedData.description,
        validatedData.unblockSchedule,
        validatedData.networkId,
      )

      if (!proposalResult.success) {
        return NextResponse.json(
          { success: false, error: proposalResult.error || "Échec de la création de la proposition" },
          { status: 500 },
        )
      }

      return NextResponse.json({
        success: true,
        proposalId: proposalResult.proposalId,
      })
    } else if (action === "vote") {
      // Valider les paramètres
      const validationResult = voteSchema.safeParse(data)
      if (!validationResult.success) {
        return NextResponse.json(
          { success: false, error: validationResult.error.errors.map((e) => e.message).join(", ") },
          { status: 400 },
        )
      }

      const validatedData = validationResult.data

      // Vérifier la signature du wallet
      const isValidSignature = await verifyWalletSignature(
        validatedData.voter,
        validatedData.message,
        validatedData.signature,
      )

      if (!isValidSignature) {
        return NextResponse.json({ success: false, error: "Signature du wallet invalide" }, { status: 403 })
      }

      // Voter sur la proposition
      const voteResult = await daoGovernanceService.voteOnProposal(
        validatedData.proposalId,
        validatedData.voter,
        validatedData.voteType,
        "", // Pas besoin de la clé privée dans cette implémentation
        validatedData.networkId,
      )

      if (!voteResult.success) {
        return NextResponse.json({ success: false, error: voteResult.error || "Échec du vote" }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
      })
    } else if (action === "execute") {
      // Valider les paramètres
      const validationResult = executeSchema.safeParse(data)
      if (!validationResult.success) {
        return NextResponse.json(
          { success: false, error: validationResult.error.errors.map((e) => e.message).join(", ") },
          { status: 400 },
        )
      }

      const validatedData = validationResult.data

      // Vérifier la signature du wallet
      const isValidSignature = await verifyWalletSignature(
        validatedData.executor,
        validatedData.message,
        validatedData.signature,
      )

      if (!isValidSignature) {
        return NextResponse.json({ success: false, error: "Signature du wallet invalide" }, { status: 403 })
      }

      // Exécuter la proposition
      const executeResult = await daoGovernanceService.executeProposal(
        validatedData.proposalId,
        validatedData.executor,
        "", // Pas besoin de la clé privée dans cette implémentation
        validatedData.networkId,
      )

      if (!executeResult.success) {
        return NextResponse.json(
          { success: false, error: executeResult.error || "Échec de l'exécution de la proposition" },
          { status: 500 },
        )
      }

      return NextResponse.json({
        success: true,
      })
    } else {
      return NextResponse.json({ success: false, error: "Action non reconnue" }, { status: 400 })
    }
  } catch (error: any) {
    console.error("Erreur lors du traitement de la requête DAO:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Une erreur s'est produite lors du traitement de la requête",
      },
      { status: 500 },
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    const tokenAddress = req.nextUrl.searchParams.get("tokenAddress")
    const proposalId = req.nextUrl.searchParams.get("proposalId")

    if (proposalId) {
      // Récupérer les détails d'une proposition spécifique
      const proposal = daoGovernanceService.getProposalDetails(proposalId)
      if (!proposal) {
        return NextResponse.json({ success: false, error: "Proposition non trouvée" }, { status: 404 })
      }

      const votes = daoGovernanceService.getVotesForProposal(proposalId)

      return NextResponse.json({
        success: true,
        proposal,
        votes,
      })
    } else if (tokenAddress) {
      // Récupérer toutes les propositions pour un token
      const proposals = daoGovernanceService.getProposalsForToken(tokenAddress)

      return NextResponse.json({
        success: true,
        proposals,
      })
    } else {
      return NextResponse.json(
        { success: false, error: "Paramètres manquants: proposalId ou tokenAddress requis" },
        { status: 400 },
      )
    }
  } catch (error: any) {
    console.error("Erreur lors de la récupération des données DAO:", error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Une erreur s'est produite lors de la récupération des données",
      },
      { status: 500 },
    )
  }
}

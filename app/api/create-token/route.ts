import { Connection } from "@solana/web3.js"
import { type NextRequest, NextResponse } from "next/server"
import { grindSuffix, initToken, setupVesting } from "@/lib/solana-token-kit"
import { envConfig } from "@/lib/env-config"

export async function POST(req: NextRequest) {
  try {
    const { name, symbol, suffix } = await req.json()

    // 1. Generate the mint address with the specified suffix
    const { mint, keypair } = await grindSuffix(suffix, 7)

    // 2. Connect to Solana network based on environment configuration
    const connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")

    // 3. Deploy the core contract
    const transactionId = await initToken({
      mint,
      supply: 1_000_000_000,
      decimals: 9,
      immutable: true,
      connection,
      payer: keypair,
    })

    // 4. Configure allocations (example)
    await setupVesting("mock_program_id", {
      society: { amount: 200_000_000, schedule: "3% monthly/10mo" },
      dev: { amount: 50_000_000, schedule: "1% monthly/5mo" },
      marketing: { amount: 50_000_000, schedule: "50%@1w+50%@1m" },
    })

    // 5. Deploy security mechanisms
    // await deployProtections(programId, {
    //   fees: { base: 1%, additional: 10% },
    //   limits: { maxTx: 10M, maxWallet: 50M },
    //   cooldown: 60s
    // });

    return NextResponse.json({
      success: true,
      mint: mint.toBase58(),
      transactionId,
    })
  } catch (error: any) {
    console.error("Error in /api/create-token:", error)
    return NextResponse.json({ error: error.message || "Failed to create token" }, { status: 500 })
  }
}

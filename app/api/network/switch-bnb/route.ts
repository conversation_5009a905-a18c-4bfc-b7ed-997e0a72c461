import { type NextRequest, NextResponse } from "next/server"
import { getNetworkByTypeAndEnv } from "@/lib/network-config"

export async function POST(request: NextRequest) {
  try {
    const { environment } = await request.json()

    if (!environment || !["mainnet", "testnet"].includes(environment)) {
      return NextResponse.json({ error: "Invalid environment. Must be one of: mainnet, testnet" }, { status: 400 })
    }

    const network = getNetworkByTypeAndEnv("bnb", environment as any)

    if (!network) {
      return NextResponse.json({ error: `BNB ${environment} network configuration not found` }, { status: 404 })
    }

    return NextResponse.json({ success: true, network })
  } catch (error: any) {
    console.error("Error switching BNB network:", error)
    return NextResponse.json({ error: error.message || "Failed to switch BNB network" }, { status: 500 })
  }
}

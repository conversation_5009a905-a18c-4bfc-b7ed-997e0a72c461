import { type NextRequest, NextResponse } from "next/server"
import { Connection, PublicKey, LAMPORTS_PER_SOL } from "@solana/web3.js"

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const address = searchParams.get("address")

    if (!address) {
      return NextResponse.json({ error: "Address parameter is required" }, { status: 400 })
    }

    // Get RPC URL
    const rpcUrl = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com"

    // Connect to Solana
    const connection = new Connection(rpcUrl, "confirmed")

    // Get balance
    const publicKey = new PublicKey(address)
    const balance = await connection.getBalance(publicKey)
    const solBalance = balance / LAMPORTS_PER_SOL

    return NextResponse.json({
      success: true,
      address,
      balance: solBalance,
      lamports: balance,
    })
  } catch (error: any) {
    console.error("Error getting wallet balance:", error)
    return NextResponse.json({ error: error.message || "Failed to get wallet balance" }, { status: 500 })
  }
}

"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, ArrowUpRight, Clock, ExternalLink, LineChart, Plus, Users } from "lucide-react"
import Header from "@/components/header"
import { Footer } from "@/components/footer"
import { useTokenRegistry } from "@/lib/token-registry"
import { formatNumber, formatTimeAgo } from "@/lib/utils"
import { getUserTokens } from "@/lib/token-service"

export default function MyTokensPage() {
  const { connected, publicKey } = useWallet()
  const router = useRouter()
  const [userTokens, setUserTokens] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("created")
  const tokens = useTokenRegistry((state) => state.tokens)

  useEffect(() => {
    const loadUserTokens = async () => {
      if (!connected || !publicKey) {
        setUserTokens([])
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      try {
        // Récupérer les tokens créés par l'utilisateur
        const createdTokens = await getUserTokens(publicKey.toString())
        setUserTokens(createdTokens)
      } catch (error) {
        console.error("Error loading user tokens:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadUserTokens()
  }, [connected, publicKey])

  // Filtrer les tokens en fonction de l'onglet actif
  const getFilteredTokens = () => {
    if (activeTab === "created") {
      return userTokens.filter((token) => token.creator === publicKey?.toString())
    } else if (activeTab === "owned") {
      return userTokens.filter((token) => token.balance > 0)
    } else if (activeTab === "watched") {
      // Ici, vous pourriez implémenter une fonctionnalité de "watchlist"
      return userTokens.filter((token) => token.isWatched)
    }
    return userTokens
  }

  const filteredTokens = getFilteredTokens()

  if (!connected) {
    return (
      <>
        <Header />
        <main className="container py-8">
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <h1 className="text-3xl font-bold mb-6">Connectez votre portefeuille</h1>
            <p className="text-muted-foreground mb-8 max-w-md">
              Connectez votre portefeuille Solana pour voir vos tokens, suivre leurs performances et gérer vos
              investissements.
            </p>
            <WalletMultiButton className="bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-6 py-3" />
          </div>
        </main>
        <Footer />
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="container py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold">Mes Tokens</h1>
            <p className="text-muted-foreground">Gérez et suivez vos tokens sur la blockchain Solana</p>
          </div>
          <Button onClick={() => router.push("/token-factory")} className="bg-[#D4AF37] hover:bg-[#B8941F] text-black">
            <Plus className="mr-2 h-4 w-4" />
            Créer un nouveau token
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList>
            <TabsTrigger value="created">Créés</TabsTrigger>
            <TabsTrigger value="owned">Détenus</TabsTrigger>
            <TabsTrigger value="watched">Surveillés</TabsTrigger>
          </TabsList>
        </Tabs>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Skeleton className="h-4 w-full mb-1" />
                      <Skeleton className="h-6 w-3/4" />
                    </div>
                    <div>
                      <Skeleton className="h-4 w-full mb-1" />
                      <Skeleton className="h-6 w-3/4" />
                    </div>
                  </div>
                  <Skeleton className="h-2 w-full" />
                  <div className="grid grid-cols-2 gap-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-10 w-full" />
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : filteredTokens.length === 0 ? (
          <div className="text-center py-12">
            <div className="mb-4">
              {activeTab === "created" ? (
                <Plus className="h-12 w-12 mx-auto text-muted-foreground" />
              ) : activeTab === "owned" ? (
                <Users className="h-12 w-12 mx-auto text-muted-foreground" />
              ) : (
                <LineChart className="h-12 w-12 mx-auto text-muted-foreground" />
              )}
            </div>
            <h3 className="text-xl font-medium mb-2">
              {activeTab === "created"
                ? "Vous n'avez pas encore créé de tokens"
                : activeTab === "owned"
                  ? "Vous ne détenez aucun token"
                  : "Vous ne surveillez aucun token"}
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              {activeTab === "created"
                ? "Créez votre premier token sur la blockchain Solana en quelques clics"
                : activeTab === "owned"
                  ? "Achetez des tokens pour les voir apparaître ici"
                  : "Ajoutez des tokens à votre liste de surveillance pour suivre leurs performances"}
            </p>
            {activeTab === "created" && (
              <Button
                className="bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                onClick={() => router.push("/token-factory")}
              >
                <Plus className="mr-2 h-4 w-4" />
                Créer un token
              </Button>
            )}
            {activeTab === "owned" && (
              <Button className="bg-[#D4AF37] hover:bg-[#B8941F] text-black" onClick={() => router.push("/market")}>
                <ArrowUpRight className="mr-2 h-4 w-4" />
                Explorer le marché
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTokens.map((token) => (
              <Card key={token.mintAddress} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-[#D4AF37] flex items-center justify-center text-black font-bold">
                        {token.symbol.substring(0, 2)}
                      </div>
                      <div>
                        <div className="flex items-center">
                          <CardTitle className="text-lg">{token.name}</CardTitle>
                          {token.isNew && <Badge className="ml-2 bg-green-500/20 text-green-400">Nouveau</Badge>}
                        </div>
                        <CardDescription>{token.symbol}</CardDescription>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">${token.price.toFixed(4)}</div>
                      <div
                        className={
                          token.priceChange24h >= 0
                            ? "text-xs text-green-500 flex items-center justify-end"
                            : "text-xs text-red-500 flex items-center justify-end"
                        }
                      >
                        {token.priceChange24h >= 0 ? "+" : ""}
                        {token.priceChange24h.toFixed(2)}%
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-muted-foreground mb-1">Market Cap</div>
                      <div className="font-medium">${formatNumber(token.marketCap)}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground mb-1">Volume (24h)</div>
                      <div className="font-medium">${formatNumber(token.volume24h)}</div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium text-muted-foreground">Progression DEX</span>
                      <span className="text-xs">{Math.min((token.marketCap / 100000) * 100, 100).toFixed(0)}%</span>
                    </div>
                    <Progress value={Math.min((token.marketCap / 100000) * 100, 100)} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Détenteurs:</span>{" "}
                      <span className="font-medium">{formatNumber(token.holders)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Créé:</span>{" "}
                      <span className="font-medium">{formatTimeAgo(token.createdAt)}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex gap-2">
                  <Button className="flex-1 bg-[#D4AF37] hover:bg-[#B8941F] text-black" asChild>
                    <Link href={`/token/${token.mintAddress}`}>Voir les détails</Link>
                  </Button>
                  <Button variant="outline" size="icon" asChild>
                    <Link
                      href={`https://explorer.solana.com/address/${token.mintAddress}?cluster=devnet`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}

        {activeTab === "created" && filteredTokens.length > 0 && (
          <Alert className="mt-8">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Conseil: Partagez vos tokens sur les réseaux sociaux pour augmenter leur visibilité et attirer plus
              d'investisseurs.
            </AlertDescription>
          </Alert>
        )}
      </main>
      <Footer />
    </>
  )
}

"use server"

import { Keypair } from "@solana/web3.js"

// Caractères valides en base58
const BASE58_CHARS = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

/**
 * Vérifie si une chaîne est valide en base58
 */
function isValidBase58(str: string): boolean {
  for (let i = 0; i < str.length; i++) {
    if (!BASE58_CHARS.includes(str[i])) {
      return false
    }
  }
  return true
}

/**
 * Génère une paire de clés dont l'adresse publique se termine par le suffixe spécifié
 */
export async function grindSuffixAction(suffix: string, networkId = "solana-devnet") {
  console.log(`Grinding for suffix: ${suffix} on network: ${networkId}`)

  try {
    // Vérifier que le suffixe est valide en base58
    if (!isValidBase58(suffix)) {
      console.error(`Invalid suffix: ${suffix} contains non-base58 characters`)
      return {
        success: false,
        error: `Le suffixe "${suffix}" contient des caractères non valides en base58`,
      }
    }

    // Limiter la longueur du suffixe pour des raisons de performance
    if (suffix.length > 5) {
      console.warn(`Suffix length > 5 may take a very long time: ${suffix}`)
      return {
        success: false,
        error: "Le suffixe est trop long (maximum 5 caractères recommandé)",
      }
    }

    let attempts = 0
    const maxAttempts = 1000000 // Limite de sécurité
    let keypair: Keypair | null = null

    // Générer des paires de clés jusqu'à trouver une correspondance
    while (!keypair && attempts < maxAttempts) {
      attempts++

      // Générer une nouvelle paire de clés
      const candidate = Keypair.generate()

      // Convertir l'adresse publique en chaîne base58
      const pubkeyString = candidate.publicKey.toBase58()

      // Vérifier si l'adresse se termine par le suffixe
      if (pubkeyString.endsWith(suffix)) {
        keypair = candidate
        console.log(`Found matching keypair after ${attempts} attempts: ${pubkeyString}`)
      }

      // Log périodique pour le débogage
      if (attempts % 10000 === 0) {
        console.log(`Still searching after ${attempts} attempts...`)
      }
    }

    if (!keypair) {
      console.error(`Failed to find matching keypair after ${attempts} attempts`)
      return {
        success: false,
        error: `Impossible de trouver une adresse correspondante après ${attempts} tentatives`,
        attempts,
      }
    }

    // Convertir la clé secrète en format sérialisable
    const secretKey = Array.from(keypair.secretKey)

    return {
      success: true,
      keypair: {
        public: keypair.publicKey.toBase58(),
        secret: secretKey,
      },
      attempts,
    }
  } catch (error: any) {
    console.error("Error in grindSuffixAction:", error)
    return {
      success: false,
      error: `Erreur lors de la génération de l'adresse: ${error.message || "Erreur inconnue"}`,
    }
  }
}

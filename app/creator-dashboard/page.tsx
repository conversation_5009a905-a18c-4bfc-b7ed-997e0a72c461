"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis, CartesianGrid, BarChart, Bar } from "recharts"
import { AlertCircle, ArrowUpRight, Plus, Share2, Users } from "lucide-react"
import Header from "@/components/header"
import { Footer } from "@/components/footer"
import { getUserTokens } from "@/lib/token-service"
import { formatCurrency, formatNumber } from "@/lib/utils"

export default function CreatorDashboardPage() {
  const { connected, publicKey } = useWallet()
  const router = useRouter()
  const [userTokens, setUserTokens] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedToken, setSelectedToken] = useState<string | null>(null)

  useEffect(() => {
    const loadUserTokens = async () => {
      if (!connected || !publicKey) {
        setUserTokens([])
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      try {
        // Récupérer les tokens créés par l'utilisateur
        const createdTokens = await getUserTokens(publicKey.toString())
        setUserTokens(createdTokens)

        // Sélectionner le premier token par défaut s'il y en a
        if (createdTokens.length > 0 && !selectedToken) {
          setSelectedToken(createdTokens[0].mintAddress)
        }
      } catch (error) {
        console.error("Error loading user tokens:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadUserTokens()
  }, [connected, publicKey, selectedToken])

  // Générer des données aléatoires pour les graphiques
  const generateChartData = (days: number) => {
    const data = []
    const now = Date.now()
    let baseValue = Math.random() * 0.1

    for (let i = days; i >= 0; i--) {
      const date = new Date(now - i * 24 * 60 * 60 * 1000)

      // Variation aléatoire du prix
      baseValue = baseValue * (1 + (Math.random() * 0.2 - 0.1))

      data.push({
        date: date.toISOString(),
        price: baseValue,
        volume: Math.random() * 10000 + 1000,
        holders: Math.floor(Math.random() * 100) + 50,
      })
    }

    return data
  }

  const chartData = generateChartData(30)

  // Trouver le token sélectionné
  const selectedTokenData = userTokens.find((token) => token.mintAddress === selectedToken)

  if (!connected) {
    return (
      <>
        <Header />
        <main className="container py-8">
          <div className="max-w-md mx-auto text-center py-16">
            <h1 className="text-2xl font-bold mb-4">Connectez votre portefeuille</h1>
            <p className="text-muted-foreground mb-6">
              Vous devez connecter votre portefeuille pour accéder au tableau de bord des créateurs.
            </p>
            <Button onClick={() => {}}>Connecter le portefeuille</Button>
          </div>
        </main>
        <Footer />
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="container py-8 space-y-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Tableau de bord créateur</h1>
            <p className="text-muted-foreground">Gérez et analysez vos tokens</p>
          </div>
          <Button onClick={() => router.push("/token-factory/page")}>
            <Plus className="mr-2 h-4 w-4" />
            Créer un nouveau token
          </Button>
        </div>

        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <Skeleton key={index} className="h-32 w-full" />
              ))}
            </div>
            <Skeleton className="h-[400px] w-full" />
          </div>
        ) : userTokens.length === 0 ? (
          <div className="max-w-md mx-auto text-center py-16">
            <h2 className="text-xl font-bold mb-4">Aucun token créé</h2>
            <p className="text-muted-foreground mb-6">
              Vous n'avez pas encore créé de token. Commencez par créer votre premier token.
            </p>
            <Button onClick={() => router.push("/token-factory/page")}>
              <Plus className="mr-2 h-4 w-4" />
              Créer un token
            </Button>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="md:col-span-4">
                <CardHeader className="pb-2">
                  <CardTitle>Sélectionnez un token</CardTitle>
                  <CardDescription>Choisissez un token pour voir ses statistiques</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {userTokens.map((token) => (
                      <Button
                        key={token.mintAddress}
                        variant={selectedToken === token.mintAddress ? "default" : "outline"}
                        className={
                          selectedToken === token.mintAddress ? "bg-[#D4AF37] hover:bg-[#B8941F] text-black" : ""
                        }
                        onClick={() => setSelectedToken(token.mintAddress)}
                      >
                        {token.symbol}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {selectedTokenData && (
              <>
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                  <div className="flex items-center gap-3">
                    <div className="h-12 w-12 rounded-full bg-[#D4AF37] flex items-center justify-center text-black font-bold">
                      {selectedTokenData.symbol.substring(0, 2)}
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold">{selectedTokenData.name}</h2>
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground">{selectedTokenData.symbol}</span>
                        <Badge variant="outline" className="text-xs">
                          {selectedTokenData.isNew
                            ? "Nouveau"
                            : "Créé le " + new Date(selectedTokenData.createdAt).toLocaleDateString()}
                        </Badge>
                        {selectedTokenData.isQuantum && (
                          <Badge className="bg-[#D4AF37] text-black hover:bg-[#B8941F]">Quantum</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Share2 className="mr-2 h-4 w-4" />
                      Partager
                    </Button>
                    <Button
                      className="bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                      size="sm"
                      onClick={() => router.push(`/token/${selectedTokenData.mintAddress}`)}
                    >
                      <ArrowUpRight className="mr-2 h-4 w-4" />
                      Voir le token
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">Prix actuel</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-end justify-between">
                        <div>
                          <div className="text-2xl font-bold">{formatCurrency(selectedTokenData.price)}</div>
                          <div
                            className={
                              selectedTokenData.priceChange24h > 0
                                ? "text-xs text-green-500 flex items-center"
                                : "text-xs text-red-500 flex items-center"
                            }
                          >
                            {selectedTokenData.priceChange24h > 0 ? "+" : ""}
                            {selectedTokenData.priceChange24h.toFixed(2)}% (24h)
                          </div>
                        </div>
                        <div className="h-16 w-24">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={chartData.slice(-7)} margin={{ top: 0, right: 0, bottom: 0, left: 0 }}>
                              <Line
                                type="monotone"
                                dataKey="price"
                                stroke={selectedTokenData.priceChange24h > 0 ? "#22c55e" : "#ef4444"}
                                strokeWidth={2}
                                dot={false}
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">Market Cap</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-end justify-between">
                        <div>
                          <div className="text-2xl font-bold">{formatCurrency(selectedTokenData.marketCap)}</div>
                          <div className="text-xs text-muted-foreground">
                            Basé sur {formatNumber(selectedTokenData.totalSupply)} tokens
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">Détenteurs</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-end justify-between">
                        <div>
                          <div className="text-2xl font-bold">{formatNumber(selectedTokenData.holders)}</div>
                          <div className="text-xs text-muted-foreground">
                            Vous détenez {formatNumber(selectedTokenData.balance)} tokens
                          </div>
                        </div>
                        <div className="h-16 w-24">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={chartData.slice(-7)} margin={{ top: 0, right: 0, bottom: 0, left: 0 }}>
                              <Line type="monotone" dataKey="holders" stroke="#8b5cf6" strokeWidth={2} dot={false} />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-3 mb-4">
                    <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
                    <TabsTrigger value="holders">Détenteurs</TabsTrigger>
                    <TabsTrigger value="settings">Paramètres</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Évolution du prix</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                              <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                              <XAxis
                                dataKey="date"
                                tickFormatter={(date) =>
                                  new Date(date).toLocaleDateString([], { day: "numeric", month: "short" })
                                }
                              />
                              <YAxis yAxisId="left" tickFormatter={(value) => formatCurrency(value, true)} />
                              <YAxis
                                yAxisId="right"
                                orientation="right"
                                tickFormatter={(value) => formatNumber(value, true)}
                              />
                              <Tooltip
                                formatter={(value, name) => {
                                  if (name === "price") return [formatCurrency(value as number), "Prix"]
                                  if (name === "volume") return [formatCurrency(value as number), "Volume"]
                                  return [value, name]
                                }}
                                labelFormatter={(date) => new Date(date as string).toLocaleDateString()}
                              />
                              <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="price"
                                stroke="#D4AF37"
                                strokeWidth={2}
                                dot={false}
                              />
                              <Line
                                yAxisId="right"
                                type="monotone"
                                dataKey="volume"
                                stroke="#8b5cf6"
                                strokeWidth={2}
                                dot={false}
                              />
                            </LineChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Statistiques du token</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <div className="text-sm text-muted-foreground">Offre totale</div>
                                <div className="font-medium">{formatNumber(selectedTokenData.totalSupply)}</div>
                              </div>
                              <div>
                                <div className="text-sm text-muted-foreground">Offre en circulation</div>
                                <div className="font-medium">{formatNumber(selectedTokenData.totalSupply * 0.8)}</div>
                              </div>
                              <div>
                                <div className="text-sm text-muted-foreground">Volume (24h)</div>
                                <div className="font-medium">{formatCurrency(selectedTokenData.volume24h)}</div>
                              </div>
                              <div>
                                <div className="text-sm text-muted-foreground">Liquidité</div>
                                <div className="font-medium">{formatCurrency(selectedTokenData.marketCap * 0.2)}</div>
                              </div>
                            </div>

                            <div>
                              <div className="text-sm text-muted-foreground mb-2">Progression des listings</div>
                              <div className="space-y-2">
                                <div>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span>Raydium</span>
                                    <span>{formatCurrency(10000)}</span>
                                  </div>
                                  <div className="w-full bg-muted rounded-full h-2">
                                    <div
                                      className="bg-green-500 h-2 rounded-full"
                                      style={{
                                        width: `${Math.min(100, (selectedTokenData.marketCap / 10000) * 100)}%`,
                                      }}
                                    ></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span>Orca</span>
                                    <span>{formatCurrency(50000)}</span>
                                  </div>
                                  <div className="w-full bg-muted rounded-full h-2">
                                    <div
                                      className="bg-blue-500 h-2 rounded-full"
                                      style={{
                                        width: `${Math.min(100, (selectedTokenData.marketCap / 50000) * 100)}%`,
                                      }}
                                    ></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span>Jupiter</span>
                                    <span>{formatCurrency(100000)}</span>
                                  </div>
                                  <div className="w-full bg-muted rounded-full h-2">
                                    <div
                                      className="bg-purple-500 h-2 rounded-full"
                                      style={{
                                        width: `${Math.min(100, (selectedTokenData.marketCap / 100000) * 100)}%`,
                                      }}
                                    ></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle>Actions rapides</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-2 gap-3">
                            <Button variant="outline">Ajouter de la liquidité</Button>
                            <Button variant="outline">Verrouiller la liquidité</Button>
                            <Button variant="outline">Configurer les récompenses</Button>
                            <Button variant="outline">Promouvoir le token</Button>
                          </div>

                          <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                              Augmentez la visibilité de votre token en ajoutant plus de liquidité et en verrouillant
                              les tokens.
                            </AlertDescription>
                          </Alert>

                          <div className="pt-2">
                            <Button className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black">
                              Améliorer vers Quantum
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  <TabsContent value="holders" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Distribution des détenteurs</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart
                              data={[
                                { name: "Top 10", value: Math.random() * 40 + 30 },
                                { name: "Top 11-50", value: Math.random() * 20 + 10 },
                                { name: "Top 51-100", value: Math.random() * 10 + 5 },
                                { name: "Autres", value: Math.random() * 20 + 5 },
                              ]}
                              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                              <XAxis dataKey="name" />
                              <YAxis tickFormatter={(value) => `${value}%`} />
                              <Tooltip formatter={(value: number) => [`${value.toFixed(2)}%`, "Pourcentage"]} />
                              <Bar dataKey="value" fill="#D4AF37" />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Principaux détenteurs</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {Array.from({ length: 5 }).map((_, index) => {
                            const percentage = index === 0 ? Math.random() * 20 + 10 : Math.random() * 10 + 1
                            return (
                              <div key={index} className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                  </div>
                                  <div>
                                    <div className="font-medium">
                                      {`${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 6)}`}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {formatNumber(selectedTokenData.totalSupply * (percentage / 100))} tokens
                                    </div>
                                  </div>
                                </div>
                                <div className="text-sm font-medium">{percentage.toFixed(2)}%</div>
                              </div>
                            )
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="settings" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Paramètres du token</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <div className="text-sm text-muted-foreground">Nom</div>
                              <div className="font-medium">{selectedTokenData.name}</div>
                            </div>
                            <div>
                              <div className="text-sm text-muted-foreground">Symbole</div>
                              <div className="font-medium">{selectedTokenData.symbol}</div>
                            </div>
                            <div>
                              <div className="text-sm text-muted-foreground">Adresse</div>
                              <div className="font-medium">{selectedTokenData.mintAddress}</div>
                            </div>
                            <div>
                              <div className="text-sm text-muted-foreground">Décimales</div>
                              <div className="font-medium">9</div>
                            </div>
                          </div>

                          <div className="pt-4">
                            <Button variant="outline" className="w-full">
                              Mettre à jour les métadonnées
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Paramètres avancés</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <div className="text-sm text-muted-foreground">Autorité de frappe</div>
                              <div className="font-medium">{Math.random() > 0.5 ? "Désactivée" : "Activée"}</div>
                            </div>
                            <div>
                              <div className="text-sm text-muted-foreground">Autorité de gel</div>
                              <div className="font-medium">{Math.random() > 0.7 ? "Désactivée" : "Activée"}</div>
                            </div>
                            <div>
                              <div className="text-sm text-muted-foreground">Taxe de transfert</div>
                              <div className="font-medium">
                                {Math.random() > 0.6 ? `${(Math.random() * 5).toFixed(2)}%` : "0%"}
                              </div>
                            </div>
                            <div>
                              <div className="text-sm text-muted-foreground">Taux de burn</div>
                              <div className="font-medium">
                                {Math.random() > 0.7 ? `${(Math.random() * 2).toFixed(2)}%` : "0%"}
                              </div>
                            </div>
                          </div>

                          <div className="pt-4">
                            <Button variant="outline" className="w-full">
                              Configurer les paramètres avancés
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </>
            )}
          </>
        )}
      </main>
      <Footer />
    </>
  )
}

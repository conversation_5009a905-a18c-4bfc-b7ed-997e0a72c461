"use client"

import React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Search, SlidersHorizontal, ArrowUpDown, ChevronLeft, ChevronRight, CheckCircle2, Zap } from "lucide-react"
import { formatCurrency, formatNumber, formatPercentage } from "@/lib/utils"

export default function ExplorePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [tokens, setTokens] = useState<any[]>([])
  const [filteredTokens, setFilteredTokens] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [sortBy, setSortBy] = useState("marketCap")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [showFilters, setShowFilters] = useState(false)
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10])
  const [marketCapRange, setMarketCapRange] = useState<[number, number]>([0, 10000000])
  const [currentPage, setCurrentPage] = useState(1)
  const tokensPerPage = 12

  useEffect(() => {
    const fetchTokens = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Générer des données aléatoires
        const generatedTokens = Array.from({ length: 50 }, (_, index) => {
          const isNew = Math.random() > 0.8
          const price = Math.random() * 10
          const marketCap = price * (Math.random() * 1000000 + 100000)
          const volume24h = marketCap * (Math.random() * 0.3)
          const holders = Math.floor(Math.random() * 10000) + 100
          const priceChange24h = Math.random() * 40 - 20
          const createdAt = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString()

          // Générer un nom et un symbole aléatoires
          const prefixes = ["Sol", "Luna", "Cosmic", "Star", "Quantum", "Crypto", "Digi", "Bit", "Meta", "Nexus"]
          const suffixes = ["Coin", "Token", "Cash", "Finance", "Chain", "Pay", "Swap", "Verse", "Net", "X"]
          const name = `${prefixes[Math.floor(Math.random() * prefixes.length)]}${suffixes[Math.floor(Math.random() * suffixes.length)]}`
          const symbol = name.substring(0, 3).toUpperCase() + Math.floor(Math.random() * 100)

          return {
            id: index + 1,
            mintAddress: `${Math.random().toString(36).substring(2, 15)}`,
            name,
            symbol,
            price,
            marketCap,
            volume24h,
            holders,
            priceChange24h,
            isNew,
            createdAt,
            isVerified: Math.random() > 0.7,
            isQuantum: Math.random() > 0.8,
            totalSupply: Math.floor(marketCap / price),
            category:
              Math.random() > 0.7 ? "DeFi" : Math.random() > 0.5 ? "GameFi" : Math.random() > 0.3 ? "Meme" : "Utility",
          }
        })

        setTokens(generatedTokens)
      } catch (error) {
        console.error("Error fetching tokens:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTokens()
  }, [])

  // Filtrer et trier les tokens
  useEffect(() => {
    let result = [...tokens]

    // Filtrer par onglet
    if (activeTab !== "all") {
      if (activeTab === "new") {
        result = result.filter((token) => token.isNew)
      } else if (activeTab === "trending") {
        result = result.filter((token) => token.priceChange24h > 5)
      } else if (activeTab === "verified") {
        result = result.filter((token) => token.isVerified)
      } else if (activeTab === "quantum") {
        result = result.filter((token) => token.isQuantum)
      } else {
        result = result.filter((token) => token.category.toLowerCase() === activeTab)
      }
    }

    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        (token) =>
          token.name.toLowerCase().includes(query) ||
          token.symbol.toLowerCase().includes(query) ||
          token.mintAddress.toLowerCase().includes(query),
      )
    }

    // Filtrer par plage de prix
    result = result.filter((token) => token.price >= priceRange[0] && token.price <= priceRange[1])

    // Filtrer par plage de capitalisation boursière
    result = result.filter((token) => token.marketCap >= marketCapRange[0] && token.marketCap <= marketCapRange[1])

    // Trier les résultats
    result.sort((a, b) => {
      let comparison = 0
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name)
          break
        case "price":
          comparison = a.price - b.price
          break
        case "priceChange":
          comparison = a.priceChange24h - b.priceChange24h
          break
        case "volume":
          comparison = a.volume24h - b.volume24h
          break
        case "marketCap":
          comparison = a.marketCap - b.marketCap
          break
        case "holders":
          comparison = a.holders - b.holders
          break
        case "createdAt":
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          break
        default:
          comparison = a.marketCap - b.marketCap
      }

      return sortOrder === "asc" ? comparison : -comparison
    })

    setFilteredTokens(result)
  }, [tokens, activeTab, searchQuery, sortBy, sortOrder, priceRange, marketCapRange])

  // Pagination
  const indexOfLastToken = currentPage * tokensPerPage
  const indexOfFirstToken = indexOfLastToken - tokensPerPage
  const currentTokens = filteredTokens.slice(indexOfFirstToken, indexOfLastToken)
  const totalPages = Math.ceil(filteredTokens.length / tokensPerPage)

  const paginate = (pageNumber: number) => {
    setCurrentPage(pageNumber)
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc")
  }

  const resetFilters = () => {
    setSearchQuery("")
    setActiveTab("all")
    setSortBy("marketCap")
    setSortOrder("desc")
    setPriceRange([0, 10])
    setMarketCapRange([0, 10000000])
    setCurrentPage(1)
  }

  const navigateToToken = (mintAddress: string) => {
    router.push(`/token/${mintAddress}`)
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Explorer les tokens</h1>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher par nom, symbole ou adresse..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant={showFilters ? "default" : "outline"}
            onClick={() => setShowFilters(!showFilters)}
            className="whitespace-nowrap"
          >
            <SlidersHorizontal className="h-4 w-4 mr-2" />
            Filtres
          </Button>
          <Button variant="outline" onClick={resetFilters} className="whitespace-nowrap">
            Réinitialiser
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <label className="text-sm font-medium mb-2 block">Trier par</label>
                <div className="flex gap-2">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="marketCap">Capitalisation</SelectItem>
                      <SelectItem value="price">Prix</SelectItem>
                      <SelectItem value="priceChange">Évolution 24h</SelectItem>
                      <SelectItem value="volume">Volume 24h</SelectItem>
                      <SelectItem value="holders">Détenteurs</SelectItem>
                      <SelectItem value="createdAt">Date de création</SelectItem>
                      <SelectItem value="name">Nom</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="icon" onClick={toggleSortOrder}>
                    <ArrowUpDown className={`h-4 w-4 ${sortOrder === "asc" ? "rotate-180" : ""}`} />
                  </Button>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Plage de prix</label>
                <div className="px-2">
                  <Slider
                    defaultValue={priceRange}
                    min={0}
                    max={10}
                    step={0.1}
                    onValueChange={(value) => setPriceRange(value as [number, number])}
                    className="my-4"
                  />
                  <div className="flex justify-between text-sm">
                    <span>{formatCurrency(priceRange[0])}</span>
                    <span>{formatCurrency(priceRange[1])}</span>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Capitalisation boursière</label>
                <div className="px-2">
                  <Slider
                    defaultValue={marketCapRange}
                    min={0}
                    max={10000000}
                    step={100000}
                    onValueChange={(value) => setMarketCapRange(value as [number, number])}
                    className="my-4"
                  />
                  <div className="flex justify-between text-sm">
                    <span>{formatCurrency(marketCapRange[0], true)}</span>
                    <span>{formatCurrency(marketCapRange[1], true)}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="w-full justify-start overflow-x-auto">
          <TabsTrigger value="all">Tous</TabsTrigger>
          <TabsTrigger value="new">Nouveaux</TabsTrigger>
          <TabsTrigger value="trending">Tendance</TabsTrigger>
          <TabsTrigger value="verified">Vérifiés</TabsTrigger>
          <TabsTrigger value="quantum">Quantum</TabsTrigger>
          <TabsTrigger value="defi">DeFi</TabsTrigger>
          <TabsTrigger value="gamefi">GameFi</TabsTrigger>
          <TabsTrigger value="meme">Meme</TabsTrigger>
          <TabsTrigger value="utility">Utility</TabsTrigger>
        </TabsList>
      </Tabs>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: 12 }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="p-4">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2 mb-4" />
                  <Skeleton className="h-8 w-full mb-3" />
                  <div className="grid grid-cols-2 gap-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredTokens.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-xl text-muted-foreground">Aucun token ne correspond à vos critères de recherche.</p>
          <Button variant="outline" onClick={resetFilters} className="mt-4">
            Réinitialiser les filtres
          </Button>
        </div>
      ) : (
        <>
          <div className="mb-4 text-sm text-muted-foreground">
            {filteredTokens.length} token{filteredTokens.length > 1 ? "s" : ""} trouvé
            {filteredTokens.length > 1 ? "s" : ""}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {currentTokens.map((token) => (
              <Card
                key={token.id}
                className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => navigateToToken(token.mintAddress)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold mr-2">
                        {token.symbol.substring(0, 2)}
                      </div>
                      <div>
                        <div className="font-semibold flex items-center">
                          {token.name}
                          {token.isVerified && <CheckCircle2 className="h-3 w-3 text-green-500 ml-1" />}
                          {token.isQuantum && <Zap className="h-3 w-3 text-amber-500 ml-1" />}
                        </div>
                        <div className="text-xs text-muted-foreground">{token.symbol}</div>
                      </div>
                    </div>
                    {token.isNew && (
                      <Badge variant="outline" className="bg-green-500/10 text-green-500 hover:bg-green-500/20">
                        Nouveau
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-baseline mb-4">
                    <div className="text-2xl font-bold">{formatCurrency(token.price)}</div>
                    <div className={`ml-2 text-sm ${token.priceChange24h >= 0 ? "text-green-500" : "text-red-500"}`}>
                      {formatPercentage(token.priceChange24h)}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                    <div>
                      <div className="text-muted-foreground">Cap. Marché</div>
                      <div>{formatCurrency(token.marketCap, true)}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Volume 24h</div>
                      <div>{formatCurrency(token.volume24h, true)}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Détenteurs</div>
                      <div>{formatNumber(token.holders)}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Catégorie</div>
                      <div>{token.category}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => paginate(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter((page) => {
                    // Afficher les 3 premières pages, les 3 dernières pages et les pages autour de la page actuelle
                    return page <= 3 || page > totalPages - 3 || (page >= currentPage - 1 && page <= currentPage + 1)
                  })
                  .map((page, index, array) => {
                    // Ajouter des ellipses si nécessaire
                    if (index > 0 && page - array[index - 1] > 1) {
                      return (
                        <React.Fragment key={`ellipsis-${page}`}>
                          <span className="px-3 py-2 text-muted-foreground">...</span>
                          <Button
                            variant={currentPage === page ? "default" : "outline"}
                            onClick={() => paginate(page)}
                            className="min-w-[40px]"
                          >
                            {page}
                          </Button>
                        </React.Fragment>
                      )
                    }
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        onClick={() => paginate(page)}
                        className="min-w-[40px]"
                      >
                        {page}
                      </Button>
                    )
                  })}

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}

import { PlatformArchitectureDiagram } from "@/components/platform-architecture-diagram"

export const metadata = {
  title: "Architecture de la Plateforme | Documentation",
  description: "Documentation technique de l'architecture de la plateforme de tokens",
}

export default function ArchitecturePage() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Architecture Technique</h1>
          <p className="text-muted-foreground">
            Documentation détaillée de l'architecture technique de notre plateforme de tokens.
          </p>
        </div>

        <div className="space-y-8">
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold">Vue d'ensemble</h2>
            <p>
              Notre plateforme de tokens est construite sur la blockchain Solana pour offrir des performances optimales
              et des frais de transaction minimaux. L'architecture est conçue pour être modulaire, évolutive et
              sécurisée.
            </p>

            <PlatformArchitectureDiagram />
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-semibold">Composants principaux</h2>

            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-medium">Token Platform Service</h3>
                <p className="text-muted-foreground">
                  Service central qui coordonne tous les autres services et fournit une API unifiée pour la création et
                  la gestion des tokens.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Enhanced Token Suffix Service</h3>
                <p className="text-muted-foreground">
                  Génère des adresses de token avec un suffixe spécifique (ex: GFMM, GFAI) en utilisant une technique de
                  "grinding" parallélisée.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Token Fee Service</h3>
                <p className="text-muted-foreground">
                  Gère les frais de transaction (1% de base + 10% anti-gain) et leur distribution entre les wallets de
                  développement, de burn et d'impact de prix.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Token Security Service</h3>
                <p className="text-muted-foreground">
                  Implémente les mécanismes de sécurité comme la blacklist automatique, les limites de transaction et la
                  protection anti-bot.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Token Vesting Service</h3>
                <p className="text-muted-foreground">
                  Gère les schedules de vesting pour les tokens de l'équipe, du marketing et du développement avec des
                  libérations progressives.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Token Bonding Curve Service</h3>
                <p className="text-muted-foreground">
                  Implémente la bonding curve et la réserve dynamique pour stabiliser le prix du token lors de fortes
                  variations.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Token Governance Service</h3>
                <p className="text-muted-foreground">
                  Gère le système de gouvernance DAO pour les décisions communautaires, notamment pour débloquer des
                  adresses de la blacklist.
                </p>
              </div>
            </div>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-semibold">Mécanismes de sécurité</h2>

            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-medium">Base Fees (1%)</h3>
                <p className="text-muted-foreground">
                  Chaque transaction est soumise à des frais de base de 1%, répartis comme suit:
                </p>
                <ul className="list-disc pl-6 mt-2">
                  <li>0.25% vers le wallet de développement</li>
                  <li>0.25% vers le wallet de burn (destruction)</li>
                  <li>0.50% vers le wallet d'impact de prix</li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-medium">Additional Fees (10%)</h3>
                <p className="text-muted-foreground">
                  Une taxe supplémentaire de 10% est appliquée sur les profits dépassant 150% pour décourager les dumps
                  massifs.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Anti-Bot & Transaction Limits</h3>
                <p className="text-muted-foreground">
                  Limite le nombre de transactions par bloc et impose un cooldown entre les transactions pour éviter les
                  manipulations par des bots.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Blacklist Automatique</h3>
                <p className="text-muted-foreground">
                  Détecte et blackliste automatiquement les adresses suspectes, avec un système de gouvernance DAO pour
                  les appels.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-medium">Price Impact & Réserve Dynamique</h3>
                <p className="text-muted-foreground">
                  Utilise une réserve de tokens pour intervenir automatiquement lors de fortes baisses de prix et
                  stabiliser le marché.
                </p>
              </div>
            </div>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-semibold">Stack Technique</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Smart Contracts</h3>
                <p className="text-sm text-muted-foreground">Rust + Anchor Framework</p>
              </div>

              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Oracle Prix</h3>
                <p className="text-sm text-muted-foreground">Pyth Network</p>
              </div>

              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">DAO Governance</h3>
                <p className="text-sm text-muted-foreground">Realms (SPL Governance)</p>
              </div>

              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Automatisation</h3>
                <p className="text-sm text-muted-foreground">Clockwork (tasks scheduling)</p>
              </div>

              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Monitoring</h3>
                <p className="text-sm text-muted-foreground">Sentry + Tenderly</p>
              </div>

              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">Déploiement</h3>
                <p className="text-sm text-muted-foreground">Solana CLI + GitHub Actions</p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  )
}

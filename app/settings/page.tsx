"use client"

import { Card, Card<PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import SolanaConnectionTest from "@/components/solana-connection-test"

export default function SettingsPage() {
  return (
    <main className="container py-6">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">Configure your platform settings and connections</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Blockchain Connection</CardTitle>
            <CardDescription>Test and verify your connection to Solana blockchain</CardDescription>
          </CardHeader>
          <CardContent>
            <SolanaConnectionTest />
          </CardContent>
        </Card>
      </div>
    </main>
  )
}

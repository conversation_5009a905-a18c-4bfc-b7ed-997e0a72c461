"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ArrowRight, Clock, Filter, Rocket, Search, Sparkles, Users } from "lucide-react"
import Header from "@/components/header"

interface LaunchpadToken {
  id: string
  name: string
  symbol: string
  description: string
  image: string
  status: "upcoming" | "live" | "ended" | "filled"
  startDate: string
  endDate: string
  hardCap: number
  softCap: number
  raised: number
  price: number
  totalSupply: number
  participants: number
  category: string
  featured: boolean
  verified: boolean
}

export default function TokenLaunchpadPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("live")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [tokens, setTokens] = useState<LaunchpadToken[]>([])

  useEffect(() => {
    const fetchTokens = async () => {
      setIsLoading(true)
      try {
        // In a real implementation, this would fetch from an API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Mock data
        const mockTokens: LaunchpadToken[] = [
          {
            id: "global-finance-quantum",
            name: "Global Finance Quantum",
            symbol: "GFQ",
            description:
              "Global Finance Quantum est un token innovant sur la blockchain Solana avec des fonctionnalités avancées et une sécurité renforcée.",
            image: "/placeholder.svg?height=100&width=100&text=GFQ",
            status: "live",
            startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
            hardCap: 500000,
            softCap: 100000,
            raised: 275000,
            price: 0.00005,
            totalSupply: 1000000000,
            participants: 342,
            category: "DeFi",
            featured: true,
            verified: true,
          },
          {
            id: "solana-meme-rocket",
            name: "Solana Meme Rocket",
            symbol: "SMRKT",
            description: "Le premier token meme propulsé par la technologie Quantum sur Solana.",
            image: "/placeholder.svg?height=100&width=100&text=SMRKT",
            status: "live",
            startDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
            hardCap: 300000,
            softCap: 50000,
            raised: 120000,
            price: 0.000025,
            totalSupply: 2000000000,
            participants: 256,
            category: "Meme",
            featured: true,
            verified: true,
          },
          {
            id: "quantum-ai-protocol",
            name: "Quantum AI Protocol",
            symbol: "QAI",
            description: "Protocole d'intelligence artificielle décentralisé sur Solana.",
            image: "/placeholder.svg?height=100&width=100&text=QAI",
            status: "upcoming",
            startDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
            hardCap: 750000,
            softCap: 200000,
            raised: 0,
            price: 0.0001,
            totalSupply: 500000000,
            participants: 0,
            category: "AI",
            featured: true,
            verified: true,
          },
          {
            id: "solana-gaming-guild",
            name: "Solana Gaming Guild",
            symbol: "SGG",
            description: "Plateforme de gaming décentralisée sur Solana avec des récompenses en tokens.",
            image: "/placeholder.svg?height=100&width=100&text=SGG",
            status: "upcoming",
            startDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString(),
            hardCap: 400000,
            softCap: 100000,
            raised: 0,
            price: 0.00008,
            totalSupply: 800000000,
            participants: 0,
            category: "Gaming",
            featured: false,
            verified: true,
          },
          {
            id: "defi-yield-aggregator",
            name: "DeFi Yield Aggregator",
            symbol: "DYA",
            description: "Agrégateur de rendement DeFi optimisé pour l'écosystème Solana.",
            image: "/placeholder.svg?height=100&width=100&text=DYA",
            status: "live",
            startDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),
            hardCap: 600000,
            softCap: 150000,
            raised: 320000,
            price: 0.00012,
            totalSupply: 400000000,
            participants: 189,
            category: "DeFi",
            featured: false,
            verified: true,
          },
          {
            id: "solana-nft-marketplace",
            name: "Solana NFT Marketplace",
            symbol: "SNFTM",
            description: "Marketplace NFT décentralisé sur Solana avec des frais réduits.",
            image: "/placeholder.svg?height=100&width=100&text=SNFTM",
            status: "ended",
            startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            hardCap: 500000,
            softCap: 100000,
            raised: 520000,
            price: 0.00007,
            totalSupply: 700000000,
            participants: 412,
            category: "NFT",
            featured: false,
            verified: true,
          },
          {
            id: "solana-dao-governance",
            name: "Solana DAO Governance",
            symbol: "SDAG",
            description: "Protocole de gouvernance DAO pour les projets Solana.",
            image: "/placeholder.svg?height=100&width=100&text=SDAG",
            status: "ended",
            startDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            hardCap: 350000,
            softCap: 80000,
            raised: 280000,
            price: 0.00006,
            totalSupply: 600000000,
            participants: 178,
            category: "DAO",
            featured: false,
            verified: true,
          },
          {
            id: "solana-metaverse-project",
            name: "Solana Metaverse Project",
            symbol: "SMP",
            description: "Projet de metaverse construit sur Solana avec des expériences immersives.",
            image: "/placeholder.svg?height=100&width=100&text=SMP",
            status: "upcoming",
            startDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: new Date(Date.now() + 13 * 24 * 60 * 60 * 1000).toISOString(),
            hardCap: 800000,
            softCap: 200000,
            raised: 0,
            price: 0.00015,
            totalSupply: 300000000,
            participants: 0,
            category: "Metaverse",
            featured: true,
            verified: false,
          },
        ]

        setTokens(mockTokens)
      } catch (error) {
        console.error("Error fetching tokens:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTokens()
  }, [])

  // Filter tokens based on search query, category, and active tab
  const filteredTokens = tokens.filter((token) => {
    // Filter by search query
    const matchesSearch =
      searchQuery === "" ||
      token.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.description.toLowerCase().includes(searchQuery.toLowerCase())

    // Filter by category
    const matchesCategory = selectedCategory === null || token.category === selectedCategory

    // Filter by status (tab)
    const matchesStatus = activeTab === "all" || token.status === activeTab

    return matchesSearch && matchesCategory && matchesStatus
  })

  // Get unique categories from tokens
  const categories = Array.from(new Set(tokens.map((token) => token.category)))

  // Format currency
  const formatCurrency = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    } else {
      return `$${value.toFixed(2)}`
    }
  }

  // Calculate progress percentage
  const getProgressPercentage = (raised: number, hardCap: number) => {
    return Math.min(100, Math.round((raised / hardCap) * 100))
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString(undefined, { month: "short", day: "numeric", year: "numeric" })
  }

  // Calculate time remaining
  const getTimeRemaining = (endDate: string) => {
    const end = new Date(endDate)
    const now = new Date()
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return "Ended"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    if (days > 0) {
      return `${days}d ${hours}h left`
    } else {
      return `${hours}h left`
    }
  }

  return (
    <>
      <Header />
      <main className="container py-8">
        <div className="flex flex-col gap-8">
          {/* Hero Section */}
          <section className="relative rounded-lg overflow-hidden bg-gradient-to-r from-[#0F172A] to-[#1E293B] p-8 md:p-12">
            <div className="absolute inset-0 opacity-10">
              <Image
                src="/placeholder.svg?height=600&width=1200&text=Launchpad"
                alt="Launchpad Background"
                fill
                className="object-cover"
              />
            </div>
            <div className="relative z-10 max-w-3xl">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Token <span className="text-[#D4AF37]">Launchpad</span>
              </h1>
              <p className="text-lg text-gray-300 mb-6">
                Découvrez et participez aux lancements de tokens les plus prometteurs sur la blockchain Solana. Investissez
                tôt dans des projets vérifiés avec des fonctionnalités Quantum avancées.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button className="bg-[#D4AF37] hover:bg-[#B8941F] text-black">
                  <Rocket className="mr-2 h-4 w-4" />
                  Lancer votre token
                </Button>
                <Button variant="outline">
                  <Sparkles className="mr-2 h-4 w-4" />
                  Explorer les projets
                </Button>
              </div>
            </div>
          </section>

          {/* Featured Projects */}
          <section>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <div>
                <h2 className="text-2xl font-bold">Projets en vedette</h2>
                <p className="text-muted-foreground">Les lancements de tokens les plus populaires</p>
              </div>
              <Link
                href="/token-launchpad/featured"
                className="flex items-center text-[#D4AF37] hover:underline mt-2 md:mt-0"
              >
                Voir tous les projets en vedette
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>

            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {Array(3)
                  .fill(0)
                  .map((_, i) => (
                    <Card key={i} className="overflow-hidden">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Skeleton className="h-12 w-12 rounded-full" />
                            <div>
                              <Skeleton className="h-5 w-32" />
                              <Skeleton className="h-4 w-16 mt-1" />
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-2 w-full" />
                        <div className="grid grid-cols-2 gap-4">
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {tokens
                  .filter((token) => token.featured)
                  .slice(0, 3)
                  .map((token) => (
                    <Card key={token.id} className="overflow-hidden">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={token.image || "/placeholder.svg"} alt={token.name} />
                              <AvatarFallback>{token.symbol.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="flex items-center">
                                <CardTitle className="text-lg">{token.name}</CardTitle>
                                {token.verified && (
                                  <Badge variant="outline" className="ml-2 bg-blue-500/10 text-blue-500 border-blue-500/20">
                                    Vérifié
                                  </Badge>
                                )}
                              </div>
                              <CardDescription>{token.symbol}</CardDescription>
                            </div>
                          </div>
                          <Badge
                            className={
                              token.status === "live"
                                ? "bg-green-500/20 text-green-500 border-green-500/20"
                                : token.status === "upcoming"
                                ? "bg-blue-500/20 text-blue-500 border-blue-500/20"
                                : "bg-gray-500/20 text-gray-500 border-gray-500/20"
                            }
                          >
                            {token.status === "live"
                              ? "En cours"
                              : token.status === "upcoming"
                              ? "À venir"
                              : "Terminé"}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-sm line-clamp-2">{token.description}</p>

                        <div className="flex items-center text-sm text-amber-500">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>
                            {token.status === "upcoming"
                              ? `Commence le ${formatDate(token.startDate)}`
                              : token.status === "ended"
                              ? `Terminé le ${formatDate(token.endDate)}`
                              : getTimeRemaining(token.endDate)}
                          </span>
                        </div>

                        <div className="mb-2">
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-muted-foreground">Progression</span>
                            <span>{getProgressPercentage(token.raised, token.hardCap)}%</span>
                          </div>
                          <Progress value={getProgressPercentage(token.raised, token.hardCap)} className="h-2" />
                          <div className="flex justify-between text-xs mt-1">
                            <span className="text-muted-foreground">
                              {formatCurrency(token.raised)} / {formatCurrency(token.hardCap)}
                            </span>
                            <div className="flex items-center">
                              <Users className="h-3 w-3 mr-1 text-muted-foreground" />
                              <span className="text-muted-foreground">{token.participants}</span>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-xs text-muted-foreground">Prix du token</p>
                            <p className="font-medium">${token.price}</p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Catégorie</p>
                            <p className="font-medium">{token.category}</p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button
                          className="w-full"
                          variant={token.status === "live" ? "default" : "outline"}
                          onClick={() => router.push(`/token-launchpad/${token.id}`)}
                        >
                          {token.status === "upcoming"
                            ? "Rappel"
                            : token.status === "live"
                            ? "Participer"
                            : "Voir les détails"}
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
              </div>
            )}
          </section>

          {/* All Projects */}
          <section>
            <h2 className="text-2xl font-bold mb-6">Tous les projets</h2>

            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-grow">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher des projets..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={() => setSelectedCategory(null)}
                >
                  <Filter className="h-4 w-4" />
                  {selectedCategory || "Toutes catégories"}
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category === selectedCategory ? null : category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>

            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="all">Tous</TabsTrigger>
                <TabsTrigger value="live">En cours</TabsTrigger>
                <TabsTrigger value="upcoming">À venir</TabsTrigger>
                <TabsTrigger value="ended">Terminés</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="space-y-6">
                {isLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {Array(6)
                      .fill(0)
                      .map((_, i) => (
                        <Card key={i} className="overflow-hidden">
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <Skeleton className="h-12 w-12 rounded-full" />
                                <div>
                                  <Skeleton className="h-5 w-32" />
                                  <Skeleton className="h-4 w-16 mt-1" />
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-3/4" />
                            <Skeleton className="h-2 w-full" />
                            <div className="grid grid-cols-2 gap-4">
                              <Skeleton className="h-8 w-full" />
                              <Skeleton className="h-8 w-full" />
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                ) : filteredTokens.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {filteredTokens.map((token) => (
                      <Card key={token.id} className="overflow-hidden">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-12 w-12">
                                <AvatarImage src={token.image || "/placeholder.svg"} alt={token.name} />
                                <AvatarFallback>{token.symbol.substring(0, 2)}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="flex items-center">
                                  <CardTitle className="text-lg">{token.name}</CardTitle>
                                  {token.verified && (
                                    <Badge
                                      variant="outline"
                                      className="ml-2 bg-blue-500/10 text-blue-500 border-blue-500/20"
                                    >
                                      Vérifié
                                    </Badge>
                                  )}
                                </div>
                                <CardDescription>{token.symbol}</CardDescription>
                              </div>
                            </div>
                            <Badge
                              className={
                                token.status === "live"
                                  ? "bg-green-500/20 text-green-500 border-green-500/20"
                                  : token.status === "upcoming"
                                  ? "bg-blue-500/20 text-blue-500 border-blue-500/20"
                                  : "bg-gray-500/20 text-gray-500 border-gray-500/20"
                              }
                            >
                              {token.status === "live"
                                ? "En cours"
                                : token.status === "upcoming"
                                ? "À venir"
                                : "Terminé"}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <p className="text-sm line-clamp-2">{token.description}</p>

                          <div className="flex items-center text-sm text-amber-500">
                            <Clock className="h-4 w-4 mr-1" />
                            <span>
                              {token.status === "upcoming"
                                ? `Commence le ${formatDate(token.startDate)}`
                                : token.status === "ended"
                                ? `Terminé le ${formatDate(token.endDate)}`
                                : getTimeRemaining(token.endDate)}
                            </span>
                          </div>

                          <div className="mb-2">
                            <div className="flex justify-between text-sm mb-1">
                              <span className="text-muted-foreground">Progression</span>
                              <span>{getProgressPercentage(token.raised, token.hardCap)}%</span>
                            </div>
                            <Progress value={getProgressPercentage(token.raised, token.hardCap)} className="h-2" />
                            <div className="flex justify-between text-xs mt-1">
                              <span className="text-muted-foreground">
                                {formatCurrency(token.raised)} / {formatCurrency(token.hardCap)}
                              </span>
                              <div className="flex items-center">
                                <Users className="h-3 w-3 mr-1 text-muted-foreground" />
                                <span className="text-muted-foreground">{token.participants}</span>
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-xs text-muted-foreground">Prix du token</p>
                              <p className="font-medium">${token.price}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Catégorie</p>
                              <p className="font-medium">{token.category}</p>
                            </div>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button
                            className="w-full"
                            variant={token.status === "live" ? "default" : "outline"}
                            onClick={() => router.push(`/token-launchpad/${token.id}`)}
                          >
                            {token.status === "upcoming"
                              ? "Rappel"
                              : token.status === "live"
                              ? "Participer"
                              : "Voir les détails"}
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="mb-4">
                      <Search className="h-12 w-12 mx-auto text-muted-foreground" />
                    </div>
                    <h3 className="text-xl font-medium mb-2">Aucun projet trouvé</h3>
                    <p className="text-muted-foreground mb-6">
                      Aucun projet ne correspond à vos critères de recherche. Essayez de modifier vos filtres.
                    </p>
                    <Button onClick={() => {
                      setSearchQuery("")
                      setSelectedCategory(null)
                      setActiveTab("all")
                    }}>
                      Réinitialiser les filtres
                    </Button>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </section>

          {/* How It Works */}
          <section className="bg-gradient-to-r from-[#0F172A] to-[#1E293B] rounded-lg p-8 md:p-12">
            <h2 className="text-2xl font-bold mb-6">Comment ça marche</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 rounded-full bg-[#D\

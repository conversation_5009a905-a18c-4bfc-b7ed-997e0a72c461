"use client"

import { useEffe<PERSON>, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Download, GithubIcon, Globe, Info, Share2, Shield, Twitter } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import Header from "@/components/header"
import { marketAnalysisService } from "@/lib/market-analysis-service"

interface LaunchpadToken {
  id: string
  name: string
  symbol: string
  description: string
  image: string
  status: "upcoming" | "live" | "ended" | "filled"
  startDate: string
  endDate: string
  hardCap: number
  softCap: number
  raised: number
  price: number
  totalSupply: number
  participants: number
  category: string
  featured: boolean
  verified: boolean
  longDescription?: string
  website?: string
  twitter?: string
  github?: string
  team?: {
    name: string
    role: string
    avatar: string
  }[]
  tokenomics?: {
    presale: number
    liquidity: number
    marketing: number
    development: number
    team: number
    reserve: number
  }
  roadmap?: {
    title: string
    description: string
    date: string
    completed: boolean
  }[]
  documents?: {
    title: string
    url: string
  }[]
  features?: string[]
}

export default function TokenDetailPage() {
  const router = useRouter()
  const params = useParams()
  const id = params.id as string
  const [token, setToken] = useState<LaunchpadToken | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [contributionAmount, setContributionAmount] = useState<string>("100")
  const [priceHistory, setPriceHistory] = useState<any[]>([])
  const [similarTokens, setSimilarTokens] = useState<any[]>([])

  useEffect(() => {
    const fetchToken = async () => {
      setIsLoading(true)
      try {
        // In a real implementation, this would fetch from an API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Mock data - you would fetch real data in a production app
        const mockToken: LaunchpadToken = {
          id: "global-finance-quantum",
          name: "Global Finance Quantum",
          symbol: "GFQ",
          description:
            "Global Finance Quantum est un token innovant sur la blockchain Solana avec des fonctionnalités avancées et une sécurité renforcée.",
          image: "/placeholder.svg?height=200&width=200&text=GFQ",
          status: "live",
          startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          hardCap: 500000,
          softCap: 100000,
          raised: 275000,
          price: 0.00005,
          totalSupply: 1000000000,
          participants: 342,
          category: "DeFi",
          featured: true,
          verified: true,
          longDescription: `
            Global Finance Quantum (GFQ) est un token DeFi de nouvelle génération construit sur la blockchain Solana, offrant des transactions ultra-rapides et des frais minimaux.
    
            Notre mission est de démocratiser l'accès aux services financiers avancés grâce à la technologie blockchain, en permettant aux utilisateurs de bénéficier de rendements optimisés et d'une sécurité de premier ordre.
    
            Avec des smart contracts audités par les meilleures firmes de sécurité et une approche transparente de la gestion des fonds, GFQ se positionne comme un leader de l'écosystème DeFi sur Solana.
    
            Notre équipe est composée d'experts en finance traditionnelle, en développement blockchain et en cryptoéconomie, combinant leur expertise pour créer un écosystème financier décentralisé robuste et accessible à tous.
          `,
          website: "https://global-finance-quantum.io",
          twitter: "https://twitter.com/GFQuantum",
          github: "https://github.com/GFQuantum",
          team: [
            {
              name: "Alex Chen",
              role: "CEO & Founder",
              avatar: "/placeholder.svg?height=100&width=100&text=AC",
            },
            {
              name: "Sophia Rodriguez",
              role: "CTO",
              avatar: "/placeholder.svg?height=100&width=100&text=SR",
            },
            {
              name: "Marcus Johnson",
              role: "CMO",
              avatar: "/placeholder.svg?height=100&width=100&text=MJ",
            },
            {
              name: "Elena Petrova",
              role: "Lead Developer",
              avatar: "/placeholder.svg?height=100&width=100&text=EP",
            },
          ],
          tokenomics: {
            presale: 30,
            liquidity: 25,
            marketing: 15,
            development: 15,
            team: 10,
            reserve: 5,
          },
          roadmap: [
            {
              title: "Phase 1: Lancement du Token",
              description: "Presale et listing initial sur les exchanges décentralisés.",
              date: "2023 Q2",
              completed: true,
            },
            {
              title: "Phase 2: Expansion de l'Écosystème",
              description: "Lancement du staking, de la yield farm et des premiers partenariats.",
              date: "2023 Q3",
              completed: true,
            },
            {
              title: "Phase 3: Marketplace DeFi",
              description: "Plateforme d'échange de produits DeFi avec frais réduits pour les détenteurs de GFQ.",
              date: "2023 Q4",
              completed: false,
            },
            {
              title: "Phase 4: Gouvernance DAO",
              description: "Mise en place d'un système de gouvernance décentralisé pour la communauté GFQ.",
              date: "2024 Q1",
              completed: false,
            },
            {
              title: "Phase 5: Expansion Cross-Chain",
              description: "Extension de l'écosystème GFQ vers d'autres blockchains majeures.",
              date: "2024 Q2",
              completed: false,
            },
          ],
          documents: [
            {
              title: "Whitepaper",
              url: "#",
            },
            {
              title: "Tokenomics",
              url: "#",
            },
            {
              title: "Audit Report",
              url: "#",
            },
            {
              title: "Legal Disclaimer",
              url: "#",
            },
          ],
          features: [
            "Staking à rendement optimisé",
            "Frais de transaction réduits",
            "Gouvernance décentralisée",
            "Interopérabilité cross-chain",
            "Sécurité audité de niveau institutionnel",
            "Liquidité verrouillée pendant 2 ans",
          ],
        }

        setToken(mockToken)
        
        // Fetch price history data
        const history = await marketAnalysisService.getTokenPriceHistory(id, "7d")
        setPriceHistory(history)
        
        // Fetch similar tokens
        const similar = await marketAnalysisService.getSimilarTokens(id, 3)
        setSimilarTokens(similar)
        
      } catch (error) {
        console.error("Error fetching token:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (id) {
      fetchToken()
    }
  }, [id])

  // Format currency
  const formatCurrency = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    } else {
      return `$${value.toFixed(2)}`
    }
  }

  // Calculate progress percentage
  const getProgressPercentage = (raised: number, hardCap: number) => {
    return Math.min(100, Math.round((raised / hardCap) * 100))
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString(undefined, { month: "short", day: "numeric", year: "numeric" })
  }

  // Calculate time remaining
  const getTimeRemaining = (endDate: string) => {
    const end = new Date(endDate)
    const now = new Date()
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return "Ended"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    if (days > 0) {
      return `${days}d ${hours}h left`
    } else {
      return `${hours}h left`
    }
  }

  const handleContribute = () => {
    alert(`Contribution de ${contributionAmount} $ effectuée avec succès !`)
  }

  if (isLoading || !token) {
    return (
      <>
        <Header />
        <main className="container py-8">
          <div className="flex items-center mb-6">
            <Button variant="ghost" onClick={() => router.back()} className="mr-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour
            </Button>
            <div className="h-8 w-40 bg-gray-200 animate-pulse rounded-md"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-2 space-y-8">
              <div className="rounded-lg bg-gray-200 animate-pulse h-64"></div>
              <div className="rounded-lg bg-gray-200 animate-pulse h-96"></div>
            </div>
            <div className="space-y-8">
              <div className="rounded-lg bg-gray-200 animate-pulse h-64"></div>
              <div className="rounded-lg bg-gray-200 animate-pulse h-48"></div>
            </div>
          </div>
        </main>
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="container py-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={() => router.back()} className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <h1 className="text-2xl font-bold">{token.name}</h1>
          {token.verified && (
            <Badge variant="outline" className="ml-3 bg-blue-500/10 text-blue-500 border-blue-500/20">
              Vérifié
            </Badge>
          )}
          <Badge
            className={
              token.status === "live"
                ? "ml-3 bg-green-500/20 text-green-500 border-green-500/20"
                : token.status === "upcoming"
                ? "ml-3 bg-blue-500/20 text-blue-500 border-blue-500/20"
                : "ml-3 bg-gray-500/20 text-gray-500 border-gray-500/20"
            }
          >
            {token.status === "live"
              ? "En cours"
              : token.status === "upcoming"
              ? "À venir"
              : "Terminé"}
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="md:col-span-2 space-y-8">
            {/* Token Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Présentation du projet</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col md:flex-row gap-6 items-start">
                  <div className="rounded-lg overflow-hidden w-full max-w-[180px] mx-auto md:mx-0">
                    <Image
                      src={token.image || "/placeholder.svg"}
                      alt={token.name}
                      width={180}
                      height={180}
                      className="w-full object-cover aspect-square"
                    />
                  </div>
                  <div className="flex-1 space-y-4">
                    <div>
                      <h3 className="text-xl font-semibold flex items-center">
                        {token.name} <span className="text-muted-foreground ml-2">({token.symbol})</span>
                      </h3>
                      <p className="text-muted-foreground">{token.category}</p>
                    </div>

                    <p className="text-sm whitespace-pre-line">{token.longDescription || token.description}</p>

                    <div className="flex flex-wrap gap-3">
                      {token.website && (
                        <Link href={token.website} target="_blank" rel="noopener noreferrer">
                          <Button variant="outline" size="sm">
                            <Globe className="h-4 w-4 mr-2" />
                            Site Web
                          </Button>
                        </Link>
                      )}
                      {token.twitter && (
                        <Link href={token.twitter} target="_blank" rel="noopener noreferrer">
                          <Button variant="outline" size="sm">
                            <Twitter className="h-4 w-4 mr-2" />
                            Twitter
                          </Button>
                        </Link>
                      )}
                      {token.github && (
                        <Link href={token.github} target="_blank" rel="noopener noreferrer">
                          <Button variant="outline" size="sm">
                            <GithubIcon className="h-4 w-4 mr-2" />
                            Github
                          </Button>
                        </Link>
                      )}
                      <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4 mr-2" />
                        Partager
                      </Button>
                    </div>
                  </div>
                </div>

                {token.features && token.features.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold mb-3">Caractéristiques</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {token.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center">
                          <Shield className="h-4 w-4 mr-2 text-emerald-500" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Tabs for other details */}
            <Tabs defaultValue="detail">
              <TabsList className="grid grid-cols-3 mb-6">
                <TabsTrigger value="detail">Détails</TabsTrigger>
                <TabsTrigger value="team">Équipe</TabsTrigger>
                <TabsTrigger value="roadmap">Roadmap</TabsTrigger>
              </TabsList>

              {/* Details Tab */}
              <TabsContent value="detail" className="space-y-6">
                {token.tokenomics && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Tokenomics</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Prix du token</p>
                          <p className="font-medium">${token.price}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Offre totale</p>
                          <p className="font-medium">{token.totalSupply.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Tokens en presale</p>
                          <p className="font-medium">{(token.tokenomics.presale / 100 * token.totalSupply).toLocaleString()} ({token.tokenomics.presale}%)</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Liquidité</p>
                          <p className="font-medium">{token.tokenomics.liquidity}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Marketing</p>
                          <p className="font-medium">{token.tokenomics.marketing}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Développement</p>
                          <p className="font-medium">{token.tokenomics.development}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Équipe</p>
                          <p className="font-medium">{token.tokenomics.team}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Réserve</p>
                          <p className="font-medium">{token.tokenomics.reserve}%</p>
                        </div>
                      </div>

                      <div className="mt-6">
                        <h4 className="font-semibold mb-3">Distribution des tokens</h4>
                        <div className="relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200">
                                Presale
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-blue-600">{token.tokenomics.presale}%</span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200">
                            <div style={{ width: `${token.tokenomics.presale}%` }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500"></div>
                          </div>
                        </div>
                        <div className="relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200">
                                Liquidité
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-green-600">{token.tokenomics.liquidity}%</span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-green-200">
                            <div style={{ width: `${token.tokenomics.liquidity}%` }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"></div>
                          </div>
                        </div>
                        <div className="relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-purple-600 bg-purple-200">
                                Marketing
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-purple-600">{token.tokenomics.marketing}%</span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-purple-200">
                            <div style={{ width: `${token.tokenomics.marketing}%` }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-purple-500"></div>
                          </div>
                        </div>
                        <div className="relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-yellow-600 bg-yellow-200">
                                Développement
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-yellow-600">{token.tokenomics.development}%</span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-yellow-200">
                            <div style={{ width: `${token.tokenomics.development}%` }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-yellow-500"></div>
                          </div>
                        </div>
                        <div className="relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-red-600 bg-red-200">
                                Équipe
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-red-600">{token.tokenomics.team}%</span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-red-200">
                            <div style={{ width: `${token.tokenomics.team}%` }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-red-500"></div>
                          </div>
                        </div>
                        <div className="relative pt-1">
                          <div className="flex mb-2 items-center justify-between">
                            <div>
                              <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-gray-600 bg-gray-200">
                                Réserve
                              </span>
                            </div>
                            <div className="text-right">
                              <span className="text-xs font-semibold inline-block text-gray-600">{token.tokenomics.reserve}%</span>
                            </div>
                          </div>
                          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                            <div style={{ width: `${token.tokenomics.reserve}%` }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gray-500"></div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {token.documents && token.documents.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Documents</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {token.documents.map((doc, idx) => (
                          <Link key={idx} href={doc.url} target="_blank" rel="noopener noreferrer">
                            <Button variant="outline" className="w-full justify-start">
                              <Download className="h-4 w-4 mr-2" />
                              {doc.title}
                            </Button>
                          </Link>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              {/* Team Tab */}
              <TabsContent value="team">
                {token.team && token.team.length > 0 ? (
                  <Card>
                    <CardHeader>
                      <CardTitle>L'équipe</CardTitle>
                      <CardDescription>Les personnes qui construisent {token.name}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {token.team.map((member, idx) => (
                          <div key={idx} className="flex flex-col items-center text-center">
                            <Avatar className="h-24 w-24 mb-3">
                              <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                              <AvatarFallback>{member.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <h4 className="font-semibold">{member.name}</h4>
                            <p className="text-sm text-muted-foreground">{member.role}</p>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <Info className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-xl font-medium mb-2">Information sur l'équipe non disponible</h3>
                    <p className="text-muted-foreground">
                      Les informations sur l'équipe du projet seront ajoutées prochainement.
                    </p>
                  </div>
                )}
              </TabsContent>

              {/* Roadmap Tab */}
              <TabsContent value="roadmap">
                {token.roadmap && token.roadmap.length > 0 ? (
                  <Card>
                    <CardHeader>
                      <CardTitle>Roadmap</CardTitle>
                      <CardDescription>Les prochaines étapes du projet {token.name}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="relative">
                        {token.roadmap.map((phase, idx) => (
                          <div key={idx} className="mb-8 flex gap-4">
                            <div className="flex flex-col items-center">
                              <div
                                className={`rounded-full p-2 ${
                                  phase.completed
                                    ? "bg-green-100 text-green-600"
                                    : "bg-gray-100 text-gray-400"
                                }`}
                              >
                                {phase.completed ? (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M5 13l4 4L19 7"
                                    />
                                  </svg>
                                ) : (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                )}
                              </div>
                              {idx < token.roadmap.length - 1 && (
                                <div className="h-full w-0.5 bg-gray-200"></div>
                              )}
                            </div>
                            <div>
                              <h4 className="font-semibold">{phase.title}</h4>
                              <p className="text-sm text-muted-foreground mb-2">{phase.date}</p>
                              <p className="text-sm">{phase.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className\

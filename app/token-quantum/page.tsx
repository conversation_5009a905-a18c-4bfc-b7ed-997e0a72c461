"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Info, AlertTriangle, Check, Sparkles, Shield, Zap, TrendingUp, Lock, Rocket } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import Header from "@/components/header"

// Import des composants avec les bons noms d'export
import { TokenConfigSection } from "@/components/token-quantum/token-config-section"
import { TokenomicsDisplay } from "@/components/token-quantum/tokenomics-display"
import { SecurityFeaturesDisplay } from "@/components/token-quantum/security-features-display"
import { LaunchpadIntegration } from "@/components/token-quantum/launchpad-integration"
import { InitialDistribution } from "@/components/token-quantum/initial-distribution"
import { TokenPreview } from "@/components/token-quantum/token-preview"

export default function TokenQuantumPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // États pour les données du token
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenSupply, setTokenSupply] = useState(1000000000)

  // État pour la distribution initiale
  const [initialDistribution, setInitialDistribution] = useState([
    { address: "", percentage: 70, label: "Liquidity Pool" },
    { address: "", percentage: 15, label: "Marketing" },
    { address: "", percentage: 10, label: "Team" },
    { address: "", percentage: 5, label: "Development" },
  ])

  // État pour les options de sécurité
  const [securityOptions, setSecurityOptions] = useState({
    antiBot: true,
    antiDump: true,
    antiWhale: true,
    lockLiquidity: true,
    renouncedOwnership: false,
  })

  // État pour les options de lancement
  const [launchOptions, setLaunchOptions] = useState({
    launchType: "direct",
    softCap: 50,
    hardCap: 100,
    presaleRate: 1000000,
    listingRate: 900000,
    liquidityPercentage: 70,
    lockDuration: 180,
    startDate: new Date(),
    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    launchPromotion: false,
    telegramAnnouncement: false,
    twitterPromotion: false,
  })

  const handleDistributionChange = (newDistribution) => {
    setInitialDistribution(newDistribution)
  }

  const handleCreateToken = async () => {
    if (!tokenName || !tokenSymbol) {
      setError("Veuillez remplir tous les champs obligatoires")
      return
    }

    setIsCreating(true)
    setError(null)

    try {
      // Simuler la création du token
      toast({
        title: "Création en cours",
        description: "Votre token quantum est en cours de création...",
      })

      // Attendre 3 secondes pour simuler le processus
      await new Promise((resolve) => setTimeout(resolve, 3000))

      setSuccess(`Token ${tokenName} (${tokenSymbol}) créé avec succès!`)

      // Redirection vers la page de succès après 2 secondes
      setTimeout(() => {
        router.push(
          `/token-quantum/success?name=${encodeURIComponent(tokenName)}&symbol=${encodeURIComponent(tokenSymbol)}`,
        )
      }, 2000)
    } catch (error) {
      console.error("Erreur lors de la création du token:", error)
      setError("Une erreur s'est produite lors de la création du token. Veuillez réessayer.")
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Token Quantum</h1>
              <p className="text-muted-foreground">
                Créez votre token de nouvelle génération avec des fonctionnalités avancées
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-[3fr_2fr] gap-6">
            {/* Colonne gauche - Formulaire et informations */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Créer un Token Quantum</CardTitle>
                      <CardDescription>
                        Tokens de nouvelle génération avec des fonctionnalités avancées et une sécurité renforcée
                      </CardDescription>
                    </div>
                    <Badge className="bg-[#D4AF37] text-black">Quantum</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid grid-cols-5 mb-6">
                      <TabsTrigger value="basic">
                        <Info className="h-4 w-4 mr-2" />
                        Basique
                      </TabsTrigger>
                      <TabsTrigger value="distribution">
                        <Zap className="h-4 w-4 mr-2" />
                        Distribution
                      </TabsTrigger>
                      <TabsTrigger value="tokenomics">
                        <TrendingUp className="h-4 w-4 mr-2" />
                        Tokenomics
                      </TabsTrigger>
                      <TabsTrigger value="security">
                        <Shield className="h-4 w-4 mr-2" />
                        Sécurité
                      </TabsTrigger>
                      <TabsTrigger value="launch">
                        <Rocket className="h-4 w-4 mr-2" />
                        Lancement
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic">
                      <TokenConfigSection
                        tokenName={tokenName}
                        tokenSymbol={tokenSymbol}
                        tokenSupply={tokenSupply}
                        setTokenName={setTokenName}
                        setTokenSymbol={setTokenSymbol}
                        setTokenSupply={setTokenSupply}
                      />
                    </TabsContent>

                    <TabsContent value="distribution">
                      <InitialDistribution
                        tokenSupply={tokenSupply.toString()}
                        onDistributionChange={handleDistributionChange}
                      />
                    </TabsContent>

                    <TabsContent value="tokenomics">
                      <TokenomicsDisplay
                        tokenName={tokenName}
                        tokenSymbol={tokenSymbol}
                        tokenSupply={tokenSupply}
                        distribution={initialDistribution}
                      />
                    </TabsContent>

                    <TabsContent value="security">
                      <SecurityFeaturesDisplay options={securityOptions} />
                    </TabsContent>

                    <TabsContent value="launch">
                      <LaunchpadIntegration
                        launchOptions={launchOptions}
                        setLaunchOptions={setLaunchOptions}
                        tokenName={tokenName}
                        tokenSymbol={tokenSymbol}
                      />
                    </TabsContent>
                  </Tabs>
                </CardContent>
                <CardFooter className="flex flex-col space-y-4">
                  {error && (
                    <Alert variant="destructive" className="w-full">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>Erreur</AlertTitle>
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  {success && (
                    <Alert className="w-full bg-green-50 text-green-800 border-green-200">
                      <Check className="h-4 w-4 text-green-600" />
                      <AlertTitle>Succès</AlertTitle>
                      <AlertDescription>{success}</AlertDescription>
                    </Alert>
                  )}

                  <div className="flex justify-between w-full">
                    <Button variant="outline" onClick={() => router.push("/")}>
                      Annuler
                    </Button>
                    <Button
                      className="bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                      onClick={handleCreateToken}
                      disabled={isCreating}
                    >
                      {isCreating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                          Création en cours...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Créer mon Token Quantum
                        </>
                      )}
                    </Button>
                  </div>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Avantages des Tokens Quantum</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start space-x-3">
                      <div className="bg-[#D4AF37]/20 p-2 rounded-full">
                        <Shield className="h-5 w-5 text-[#D4AF37]" />
                      </div>
                      <div>
                        <h3 className="font-medium">Sécurité renforcée</h3>
                        <p className="text-sm text-muted-foreground">
                          Protection contre les bots et les attaques malveillantes
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="bg-[#D4AF37]/20 p-2 rounded-full">
                        <Zap className="h-5 w-5 text-[#D4AF37]" />
                      </div>
                      <div>
                        <h3 className="font-medium">Transactions rapides</h3>
                        <p className="text-sm text-muted-foreground">Optimisé pour la blockchain Solana</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="bg-[#D4AF37]/20 p-2 rounded-full">
                        <TrendingUp className="h-5 w-5 text-[#D4AF37]" />
                      </div>
                      <div>
                        <h3 className="font-medium">Tokenomics avancées</h3>
                        <p className="text-sm text-muted-foreground">
                          Mécanismes de récompense et de distribution personnalisables
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <div className="bg-[#D4AF37]/20 p-2 rounded-full">
                        <Lock className="h-5 w-5 text-[#D4AF37]" />
                      </div>
                      <div>
                        <h3 className="font-medium">Liquidité verrouillée</h3>
                        <p className="text-sm text-muted-foreground">
                          Protection des investisseurs avec verrouillage automatique
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Colonne droite - Aperçu et partage */}
            <div className="space-y-6">
              <Card className="sticky top-6">
                <CardHeader>
                  <CardTitle>Aperçu du Token</CardTitle>
                </CardHeader>
                <CardContent>
                  <TokenPreview
                    tokenName={tokenName}
                    tokenSymbol={tokenSymbol}
                    tokenSupply={tokenSupply}
                    securityOptions={securityOptions}
                    distribution={initialDistribution}
                    launchOptions={launchOptions}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

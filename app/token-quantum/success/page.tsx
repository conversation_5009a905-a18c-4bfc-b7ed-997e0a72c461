"use client"

import { useEffect, useState } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Check, Copy, ExternalLink, ChevronRight, Sparkles } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import Header from "@/components/header"

export default function TokenQuantumSuccessPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()

  const [copied, setCopied] = useState(false)
  const [countdown, setCountdown] = useState(5)

  // Récupérer les paramètres de l'URL
  const tokenName = searchParams.get("name") || "Quantum Token"
  const tokenSymbol = searchParams.get("symbol") || "QTM"
  const tokenAddress = searchParams.get("address") || "Qm1234567890abcdefghijklmnopqrstuvwxyz"

  // Simuler un compte à rebours pour la redirection
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Rediriger après le compte à rebours
  useEffect(() => {
    if (countdown === 0) {
      router.push(`/token-quantum/launch/create?token=${tokenAddress}`)
    }
  }, [countdown, router, tokenAddress])

  // Copier l'adresse du token
  const copyToClipboard = () => {
    navigator.clipboard.writeText(tokenAddress)
    setCopied(true)
    toast({
      title: "Address copied",
      description: "Token address copied to clipboard",
    })
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="max-w-3xl mx-auto">
          <Card className="border-[#D4AF37]">
            <CardHeader className="text-center pb-2">
              <div className="mx-auto bg-[#D4AF37]/20 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                <Sparkles className="h-8 w-8 text-[#D4AF37]" />
              </div>
              <CardTitle className="text-3xl">Token Created Successfully!</CardTitle>
              <CardDescription>Your Quantum token has been created on the Solana blockchain</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div className="bg-muted p-6 rounded-lg space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Token Name</p>
                    <p className="text-xl font-bold">{tokenName}</p>
                  </div>
                  <Badge className="bg-[#D4AF37] text-black">Quantum</Badge>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">Token Symbol</p>
                  <p className="text-xl font-bold">{tokenSymbol}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">Token Address</p>
                  <div className="flex items-center mt-1">
                    <code className="bg-background p-2 rounded text-sm flex-1 overflow-x-auto">{tokenAddress}</code>
                    <Button variant="ghost" size="icon" onClick={copyToClipboard} className="ml-2">
                      {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Next Steps</h3>

                <div className="grid gap-4">
                  <div className="flex items-start gap-4 p-4 border rounded-lg">
                    <div className="bg-[#D4AF37]/20 p-2 rounded-full">
                      <span className="flex h-6 w-6 items-center justify-center rounded-full bg-[#D4AF37] text-black text-sm font-medium">
                        1
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium">Set up your token launch</h4>
                      <p className="text-sm text-muted-foreground">
                        Configure presale, fair launch, or direct listing options for your token
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 border rounded-lg">
                    <div className="bg-[#D4AF37]/20 p-2 rounded-full">
                      <span className="flex h-6 w-6 items-center justify-center rounded-full bg-[#D4AF37] text-black text-sm font-medium">
                        2
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium">Add liquidity to your token</h4>
                      <p className="text-sm text-muted-foreground">
                        Provide initial liquidity to enable trading on decentralized exchanges
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 border rounded-lg">
                    <div className="bg-[#D4AF37]/20 p-2 rounded-full">
                      <span className="flex h-6 w-6 items-center justify-center rounded-full bg-[#D4AF37] text-black text-sm font-medium">
                        3
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium">Promote your token</h4>
                      <p className="text-sm text-muted-foreground">
                        Use our marketing tools to promote your token to potential investors
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <p className="text-center text-sm text-muted-foreground">
                Redirecting to launch setup in {countdown} seconds...
              </p>
              <div className="flex gap-4 w-full">
                <Button variant="outline" className="flex-1" asChild>
                  <a href={`https://solscan.io/token/${tokenAddress}`} target="_blank" rel="noopener noreferrer">
                    View on Explorer
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </a>
                </Button>
                <Button className="flex-1 bg-[#D4AF37] hover:bg-[#B8941F] text-black" asChild>
                  <a href={`/token-quantum/launch/create?token=${tokenAddress}`}>
                    Set Up Launch
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </a>
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      </main>
    </>
  )
}

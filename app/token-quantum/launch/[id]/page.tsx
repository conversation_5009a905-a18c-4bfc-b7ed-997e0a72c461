"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import {
  ExternalLink,
  Globe,
  Twitter,
  MessageCircle,
  XCircle,
  Loader2,
  Clock,
  Lock,
  Shield,
  ArrowUpRight,
  ArrowDownRight,
  Wallet,
  Rocket,
} from "lucide-react"
import Header from "@/components/header"
import type { TokenLaunchStatus } from "@/lib/quantum-launch-service"

export default function TokenLaunchDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { connected, publicKey } = useWallet()
  const { toast } = useToast()

  const [launch, setLaunch] = useState<TokenLaunchStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const [contributionAmount, setContributionAmount] = useState("")
  const [isContributing, setIsContributing] = useState(false)

  const launchId = params.id as string

  useEffect(() => {
    const fetchLaunch = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données de test
        const mockLaunch: TokenLaunchStatus = {
          id: launchId,
          tokenAddress: "7nE1GmnMmDKiycFZXHsPGEtGs1ViPTgRd8SQSFaraYBa",
          ownerAddress: "8ZKS8Xv5M7P9V5QXovA3wUUWiS1J9jJXmsg1E9R7StFx",
          config: {
            name: "Global Finance",
            symbol: "GF",
            suffix: "QUANTUM",
            decimals: 9,
            totalSupply: 1000000000,
            description:
              "Global Finance is a next-generation decentralized finance platform built on Solana. Our mission is to provide accessible financial services to everyone, everywhere. With Global Finance, users can access a wide range of DeFi products including lending, borrowing, staking, and more, all with the speed and low cost of the Solana blockchain.",
            website: "https://globalfinance.io",
            twitter: "https://twitter.com/globalfinance",
            telegram: "https://t.me/globalfinance",
            initialPrice: 0.00005,
            softCap: 50,
            hardCap: 200,
            minBuy: 0.1,
            maxBuy: 5,
            liquidityPercentage: 70,
            teamPercentage: 10,
            marketingPercentage: 10,
            reservePercentage: 10,
            liquidityLockPeriod: 180,
            teamLockPeriod: 90,
            phases: {
              presale: true,
              fairLaunch: true,
              initialDexOffering: false,
            },
            antiBot: true,
            antiDump: true,
            maxWalletPercentage: 2,
            maxTxPercentage: 1,
            buyTax: 5,
            sellTax: 7,
            transferTax: 3,
            teamWallet: "8ZKS8Xv5M7P9V5QXovA3wUUWiS1J9jJXmsg1E9R7StFx",
            marketingWallet: "8ZKS8Xv5M7P9V5QXovA3wUUWiS1J9jJXmsg1E9R7StFx",
            targetDex: "all",
            listingMultiplier: 1.5,
          },
          currentPhase: {
            id: "presale_1",
            name: "Presale",
            description: "Initial fundraising phase at a discounted price",
            startDate: new Date("2023-06-01"),
            endDate: new Date("2023-06-08"),
            targetAmount: 50,
            minContribution: 0.1,
            maxContribution: 5,
            price: 0.00005,
            status: "active",
            participants: 120,
            amountRaised: 35,
            percentageComplete: 70,
          },
          phases: [
            {
              id: "presale_1",
              name: "Presale",
              description: "Initial fundraising phase at a discounted price",
              startDate: new Date("2023-06-01"),
              endDate: new Date("2023-06-08"),
              targetAmount: 50,
              minContribution: 0.1,
              maxContribution: 5,
              price: 0.00005,
              status: "active",
              participants: 120,
              amountRaised: 35,
              percentageComplete: 70,
            },
            {
              id: "fairlaunch_1",
              name: "Fair Launch",
              description: "Equal opportunity launch phase for all participants",
              startDate: new Date("2023-06-08"),
              endDate: new Date("2023-06-09"),
              targetAmount: 150,
              minContribution: 0.1,
              maxContribution: 5,
              price: 0.000055,
              status: "pending",
              participants: 0,
              amountRaised: 0,
              percentageComplete: 0,
            },
          ],
          launchDate: null,
          createdAt: new Date("2023-05-25"),
          updatedAt: new Date("2023-06-02"),
          status: "fundraising",
          totalRaised: 35,
          participants: 120,
          marketCap: 0,
          liquidityValue: 0,
          liquidityPair: null,
          dexListings: [],
          transactions: [
            {
              id: "tx_1",
              type: "contribution",
              hash: "5xGZuM6rQ8JVV7jJK9DP7L1nEZ4hWQUS5QLRNpMnhDJe",
              date: new Date("2023-06-01T10:30:00"),
              amount: 2.5,
              status: "confirmed",
            },
            {
              id: "tx_2",
              type: "contribution",
              hash: "7xGZuM6rQ8JVV7jJK9DP7L1nEZ4hWQUS5QLRNpMnhDJe",
              date: new Date("2023-06-01T14:45:00"),
              amount: 1.8,
              status: "confirmed",
            },
            {
              id: "tx_3",
              type: "contribution",
              hash: "9xGZuM6rQ8JVV7jJK9DP7L1nEZ4hWQUS5QLRNpMnhDJe",
              date: new Date("2023-06-02T09:15:00"),
              amount: 3.2,
              status: "confirmed",
            },
          ],
        }

        setLaunch(mockLaunch)
      } catch (error) {
        console.error("Error fetching launch:", error)
        toast({
          title: "Error",
          description: "Failed to fetch launch details. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchLaunch()
  }, [launchId, toast])

  const handleContribute = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to contribute",
        variant: "destructive",
      })
      return
    }

    if (!launch || !launch.currentPhase) {
      toast({
        title: "No active phase",
        description: "There is no active phase to contribute to",
        variant: "destructive",
      })
      return
    }

    const amount = Number.parseFloat(contributionAmount)
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid contribution amount",
        variant: "destructive",
      })
      return
    }

    if (amount < launch.currentPhase.minContribution) {
      toast({
        title: "Amount too low",
        description: `Minimum contribution is ${launch.currentPhase.minContribution} SOL`,
        variant: "destructive",
      })
      return
    }

    if (amount > launch.currentPhase.maxContribution) {
      toast({
        title: "Amount too high",
        description: `Maximum contribution is ${launch.currentPhase.maxContribution} SOL`,
        variant: "destructive",
      })
      return
    }

    setIsContributing(true)

    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Contribution successful",
        description: `You have successfully contributed ${amount} SOL to the ${launch.currentPhase.name}`,
      })

      // Mettre à jour les données locales
      setLaunch((prevLaunch) => {
        if (!prevLaunch || !prevLaunch.currentPhase) return prevLaunch

        const updatedCurrentPhase = {
          ...prevLaunch.currentPhase,
          amountRaised: prevLaunch.currentPhase.amountRaised + amount,
          participants: prevLaunch.currentPhase.participants + 1,
          percentageComplete: Math.min(
            100,
            ((prevLaunch.currentPhase.amountRaised + amount) / prevLaunch.currentPhase.targetAmount) * 100,
          ),
        }

        return {
          ...prevLaunch,
          currentPhase: updatedCurrentPhase,
          totalRaised: prevLaunch.totalRaised + amount,
          participants: prevLaunch.participants + 1,
        }
      })

      setContributionAmount("")
    } catch (error) {
      console.error("Error contributing:", error)
      toast({
        title: "Contribution failed",
        description: "Failed to process your contribution. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsContributing(false)
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "setup":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Upcoming
          </Badge>
        )
      case "fundraising":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Active
          </Badge>
        )
      case "preparing":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Preparing
          </Badge>
        )
      case "launched":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            Launched
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getPhaseBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Active
          </Badge>
        )
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Pending
          </Badge>
        )
      case "completed":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Completed
          </Badge>
        )
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Cancelled
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const calculateTimeRemaining = (endDate: Date) => {
    const now = new Date()
    const diff = new Date(endDate).getTime() - now.getTime()

    if (diff <= 0) return "Ended"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    return `${days}d ${hours}h ${minutes}m`
  }

  if (isLoading) {
    return (
      <>
        <Header />
        <main className="container py-6">
          <div className="flex justify-center items-center h-64">
            <div className="flex flex-col items-center">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <p className="mt-2 text-muted-foreground">Loading launch details...</p>
            </div>
          </div>
        </main>
      </>
    )
  }

  if (!launch) {
    return (
      <>
        <Header />
        <main className="container py-6">
          <div className="flex flex-col items-center justify-center h-64">
            <XCircle className="h-12 w-12 text-red-500 mb-4" />
            <h2 className="text-2xl font-bold mb-2">Launch Not Found</h2>
            <p className="text-muted-foreground mb-6">The token launch you are looking for does not exist.</p>
            <Button asChild>
              <a href="/token-quantum/launch">View All Launches</a>
            </Button>
          </div>
        </main>
      </>
    )
  }

  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage
                  src={`/abstract-geometric-shapes.png?height=48&width=48&query=${launch.config.name}`}
                  alt={launch.config.name}
                />
                <AvatarFallback>{launch.config.symbol.substring(0, 2)}</AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center gap-2">
                  <h1 className="text-3xl font-bold tracking-tight">{launch.config.name}</h1>
                  {getStatusBadge(launch.status)}
                </div>
                <p className="text-muted-foreground">
                  {launch.config.symbol}
                  {launch.config.suffix}
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              {launch.config.website && (
                <Button variant="outline" size="icon" asChild>
                  <a href={launch.config.website} target="_blank" rel="noopener noreferrer">
                    <Globe className="h-4 w-4" />
                    <span className="sr-only">Website</span>
                  </a>
                </Button>
              )}
              {launch.config.twitter && (
                <Button variant="outline" size="icon" asChild>
                  <a href={launch.config.twitter} target="_blank" rel="noopener noreferrer">
                    <Twitter className="h-4 w-4" />
                    <span className="sr-only">Twitter</span>
                  </a>
                </Button>
              )}
              {launch.config.telegram && (
                <Button variant="outline" size="icon" asChild>
                  <a href={launch.config.telegram} target="_blank" rel="noopener noreferrer">
                    <MessageCircle className="h-4 w-4" />
                    <span className="sr-only">Telegram</span>
                  </a>
                </Button>
              )}
              <Button variant="outline" asChild>
                <a
                  href={`https://explorer.solana.com/address/${launch.tokenAddress}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Explorer
                </a>
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid grid-cols-4 mb-6">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="tokenomics">Tokenomics</TabsTrigger>
                  <TabsTrigger value="security">Security</TabsTrigger>
                  <TabsTrigger value="transactions">Transactions</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Project Description</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">{launch.config.description}</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Launch Phases</CardTitle>
                      <CardDescription>Timeline and progress of the token launch</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {launch.phases.map((phase, index) => (
                        <div key={phase.id} className="space-y-3">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                              <div
                                className={`w-8 h-8 rounded-full flex items-center justify-center 
                                ${
                                  phase.status === "active"
                                    ? "bg-green-100 text-green-700"
                                    : phase.status === "completed"
                                      ? "bg-blue-100 text-blue-700"
                                      : "bg-gray-100 text-gray-700"
                                }`}
                              >
                                {index + 1}
                              </div>
                              <div>
                                <h3 className="font-medium">{phase.name}</h3>
                                <p className="text-xs text-muted-foreground">{phase.description}</p>
                              </div>
                            </div>
                            {getPhaseBadge(phase.status)}
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>
                                Progress: {phase.amountRaised} / {phase.targetAmount} SOL
                              </span>
                              <span>{phase.percentageComplete.toFixed(0)}%</span>
                            </div>
                            <Progress value={phase.percentageComplete} className="h-2" />
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <div className="text-muted-foreground">Start Date</div>
                              <div>{formatDate(phase.startDate)}</div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">End Date</div>
                              <div>{formatDate(phase.endDate)}</div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">Token Price</div>
                              <div>${phase.price.toFixed(6)}</div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">Participants</div>
                              <div>{phase.participants}</div>
                            </div>
                          </div>

                          {phase.status === "active" && (
                            <div className="bg-green-50 p-3 rounded-md flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-green-700" />
                                <span className="text-sm font-medium text-green-700">
                                  Time remaining: {calculateTimeRemaining(phase.endDate)}
                                </span>
                              </div>
                              <Button
                                size="sm"
                                onClick={() =>
                                  document.getElementById("contribute-section")?.scrollIntoView({ behavior: "smooth" })
                                }
                              >
                                Contribute
                              </Button>
                            </div>
                          )}

                          {index < launch.phases.length - 1 && <Separator className="my-4" />}
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Token Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Token Address</span>
                            <span className="text-sm font-medium font-mono">
                              {launch.tokenAddress.substring(0, 6)}...
                              {launch.tokenAddress.substring(launch.tokenAddress.length - 6)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Symbol</span>
                            <span className="text-sm font-medium">
                              {launch.config.symbol}
                              {launch.config.suffix}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Decimals</span>
                            <span className="text-sm font-medium">{launch.config.decimals}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Total Supply</span>
                            <span className="text-sm font-medium">{launch.config.totalSupply.toLocaleString()}</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Initial Price</span>
                            <span className="text-sm font-medium">${launch.config.initialPrice.toFixed(6)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Listing Price</span>
                            <span className="text-sm font-medium">
                              ${(launch.config.initialPrice * launch.config.listingMultiplier).toFixed(6)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Soft Cap</span>
                            <span className="text-sm font-medium">{launch.config.softCap} SOL</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Hard Cap</span>
                            <span className="text-sm font-medium">{launch.config.hardCap} SOL</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="tokenomics" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Token Distribution</CardTitle>
                      <CardDescription>Allocation of tokens across different categories</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <Card>
                          <CardContent className="pt-6">
                            <div className="text-2xl font-bold">{launch.config.liquidityPercentage}%</div>
                            <p className="text-sm text-muted-foreground">Liquidity Pool</p>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="pt-6">
                            <div className="text-2xl font-bold">{launch.config.teamPercentage}%</div>
                            <p className="text-sm text-muted-foreground">Team</p>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="pt-6">
                            <div className="text-2xl font-bold">{launch.config.marketingPercentage}%</div>
                            <p className="text-sm text-muted-foreground">Marketing</p>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="pt-6">
                            <div className="text-2xl font-bold">{launch.config.reservePercentage}%</div>
                            <p className="text-sm text-muted-foreground">Reserve</p>
                          </CardContent>
                        </Card>
                      </div>

                      <div className="mt-6">
                        <h3 className="text-lg font-medium mb-4">Vesting Schedule</h3>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center p-3 bg-blue-50 rounded-md">
                            <div className="flex items-center gap-2">
                              <Lock className="h-4 w-4 text-blue-700" />
                              <span className="font-medium text-blue-700">Liquidity Lock</span>
                            </div>
                            <span className="text-blue-700">{launch.config.liquidityLockPeriod} days</span>
                          </div>
                          <div className="flex justify-between items-center p-3 bg-purple-50 rounded-md">
                            <div className="flex items-center gap-2">
                              <Lock className="h-4 w-4 text-purple-700" />
                              <span className="font-medium text-purple-700">Team Tokens Lock</span>
                            </div>
                            <span className="text-purple-700">{launch.config.teamLockPeriod} days</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Transaction Taxes</CardTitle>
                      <CardDescription>Fees applied to different transaction types</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card>
                          <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <ArrowUpRight className="h-5 w-5 text-green-500 mr-2" />
                                <span className="text-sm font-medium">Buy Tax</span>
                              </div>
                              <div className="text-2xl font-bold">{launch.config.buyTax}%</div>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <ArrowDownRight className="h-5 w-5 text-red-500 mr-2" />
                                <span className="text-sm font-medium">Sell Tax</span>
                              </div>
                              <div className="text-2xl font-bold">{launch.config.sellTax}%</div>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <ArrowUpRight className="h-5 w-5 text-blue-500 mr-2" />
                                <span className="text-sm font-medium">Transfer Tax</span>
                              </div>
                              <div className="text-2xl font-bold">{launch.config.transferTax}%</div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="security" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Security Features</CardTitle>
                      <CardDescription>Protection mechanisms implemented in the token</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {launch.config.antiBot && (
                            <div className="flex items-center p-3 bg-green-50 rounded-md">
                              <Shield className="h-5 w-5 text-green-700 mr-3" />
                              <div>
                                <h4 className="font-medium text-green-700">Anti-Bot Protection</h4>
                                <p className="text-xs text-green-600">
                                  Prevents automated trading bots from manipulating the market
                                </p>
                              </div>
                            </div>
                          )}

                          {launch.config.antiDump && (
                            <div className="flex items-center p-3 bg-blue-50 rounded-md">
                              <Shield className="h-5 w-5 text-blue-700 mr-3" />
                              <div>
                                <h4 className="font-medium text-blue-700">Anti-Dump Protection</h4>
                                <p className="text-xs text-blue-600">
                                  Prevents large sell-offs that could crash the token price
                                </p>
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="space-y-2 mt-4">
                          <h3 className="text-lg font-medium">Transaction Limits</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Max Wallet</span>
                                <span className="text-sm font-medium">
                                  {launch.config.maxWalletPercentage}% of total supply
                                </span>
                              </div>
                              <Progress value={launch.config.maxWalletPercentage} max={10} className="h-2" />
                              <p className="text-xs text-muted-foreground">
                                Limits the maximum amount of tokens a single wallet can hold
                              </p>
                            </div>

                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Max Transaction</span>
                                <span className="text-sm font-medium">
                                  {launch.config.maxTxPercentage}% of total supply
                                </span>
                              </div>
                              <Progress value={launch.config.maxTxPercentage} max={5} className="h-2" />
                              <p className="text-xs text-muted-foreground">
                                Limits the maximum amount of tokens that can be transferred in a single transaction
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="mt-4">
                          <h3 className="text-lg font-medium mb-2">Liquidity Security</h3>
                          <div className="p-3 bg-purple-50 rounded-md">
                            <div className="flex items-center">
                              <Lock className="h-5 w-5 text-purple-700 mr-3" />
                              <div>
                                <h4 className="font-medium text-purple-700">Liquidity Lock</h4>
                                <p className="text-sm text-purple-600">
                                  {launch.config.liquidityPercentage}% of raised funds will be locked for{" "}
                                  {launch.config.liquidityLockPeriod} days
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Target DEX</CardTitle>
                      <CardDescription>Decentralized exchanges where the token will be listed</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {launch.config.targetDex === "all" ? (
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="flex items-center p-3 bg-blue-50 rounded-md">
                              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <span className="font-bold text-blue-700">R</span>
                              </div>
                              <div>
                                <h4 className="font-medium">Raydium</h4>
                                <p className="text-xs text-muted-foreground">Leading Solana DEX</p>
                              </div>
                            </div>

                            <div className="flex items-center p-3 bg-green-50 rounded-md">
                              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                <span className="font-bold text-green-700">O</span>
                              </div>
                              <div>
                                <h4 className="font-medium">Orca</h4>
                                <p className="text-xs text-muted-foreground">User-friendly Solana DEX</p>
                              </div>
                            </div>

                            <div className="flex items-center p-3 bg-purple-50 rounded-md">
                              <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                                <span className="font-bold text-purple-700">J</span>
                              </div>
                              <div>
                                <h4 className="font-medium">Jupiter</h4>
                                <p className="text-xs text-muted-foreground">Solana's liquidity aggregator</p>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center p-3 bg-blue-50 rounded-md">
                            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                              <span className="font-bold text-blue-700">
                                {launch.config.targetDex === "raydium"
                                  ? "R"
                                  : launch.config.targetDex === "orca"
                                    ? "O"
                                    : "J"}
                              </span>
                            </div>
                            <div>
                              <h4 className="font-medium">
                                {launch.config.targetDex === "raydium"
                                  ? "Raydium"
                                  : launch.config.targetDex === "orca"
                                    ? "Orca"
                                    : "Jupiter"}
                              </h4>
                              <p className="text-xs text-muted-foreground">
                                {launch.config.targetDex === "raydium"
                                  ? "Leading Solana DEX"
                                  : launch.config.targetDex === "orca"
                                    ? "User-friendly Solana DEX"
                                    : "Solana's liquidity aggregator"}
                              </p>
                            </div>
                          </div>
                        )}

                        <div className="mt-4">
                          <h3 className="text-lg font-medium mb-2">Listing Details</h3>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Initial Price</span>
                              <span className="text-sm font-medium">${launch.config.initialPrice.toFixed(6)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Listing Price</span>
                              <span className="text-sm font-medium">
                                ${(launch.config.initialPrice * launch.config.listingMultiplier).toFixed(6)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Listing Multiplier</span>
                              <span className="text-sm font-medium">{launch.config.listingMultiplier}x</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="transactions" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Transaction History</CardTitle>
                      <CardDescription>Recent transactions for this token launch</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {launch.transactions.length > 0 ? (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Type</TableHead>
                              <TableHead>Date</TableHead>
                              <TableHead>Amount</TableHead>
                              <TableHead>Transaction</TableHead>
                              <TableHead>Status</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {launch.transactions.map((tx) => (
                              <TableRow key={tx.id}>
                                <TableCell className="font-medium">
                                  {tx.type.charAt(0).toUpperCase() + tx.type.slice(1)}
                                </TableCell>
                                <TableCell>{formatDate(tx.date)}</TableCell>
                                <TableCell>{tx.amount.toFixed(2)} SOL</TableCell>
                                <TableCell>
                                  <a
                                    href={`https://explorer.solana.com/tx/${tx.hash}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:underline font-mono text-xs"
                                  >
                                    {tx.hash.substring(0, 8)}...{tx.hash.substring(tx.hash.length - 8)}
                                  </a>
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant="outline"
                                    className={
                                      tx.status === "confirmed"
                                        ? "bg-green-50 text-green-700 border-green-200"
                                        : tx.status === "pending"
                                          ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                                          : "bg-red-50 text-red-700 border-red-200"
                                    }
                                  >
                                    {tx.status.charAt(0).toUpperCase() + tx.status.slice(1)}
                                  </Badge>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-muted-foreground">No transactions found</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            <div>
              <Card id="contribute-section">
                <CardHeader>
                  <CardTitle>Contribute</CardTitle>
                  <CardDescription>
                    {launch.currentPhase ? <>Current phase: {launch.currentPhase.name}</> : <>No active phase</>}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {launch.status === "fundraising" && launch.currentPhase ? (
                    <>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{launch.currentPhase.percentageComplete.toFixed(0)}%</span>
                        </div>
                        <Progress value={launch.currentPhase.percentageComplete} className="h-2" />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Raised: {launch.currentPhase.amountRaised} SOL</span>
                          <span>Target: {launch.currentPhase.targetAmount} SOL</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium mb-1">Token Price</p>
                          <p className="text-lg">${launch.currentPhase.price.toFixed(6)}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium mb-1">Time Remaining</p>
                          <p className="text-lg">{calculateTimeRemaining(launch.currentPhase.endDate)}</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label htmlFor="amount" className="text-sm font-medium">
                          Your Contribution (SOL)
                        </label>
                        <div className="flex space-x-2">
                          <input
                            id="amount"
                            type="number"
                            value={contributionAmount}
                            onChange={(e) => setContributionAmount(e.target.value)}
                            placeholder={`Min: ${launch.currentPhase.minContribution} SOL`}
                            className="flex-1 px-3 py-2 border rounded-md"
                            min={launch.currentPhase.minContribution}
                            max={launch.currentPhase.maxContribution}
                            step="0.01"
                          />
                          <Button
                            variant="outline"
                            onClick={() => setContributionAmount(launch.currentPhase?.maxContribution.toString() || "")}
                          >
                            Max
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Min: {launch.currentPhase.minContribution} SOL | Max: {launch.currentPhase.maxContribution}{" "}
                          SOL
                        </p>
                      </div>

                      <div className="space-y-2">
                        <p className="text-sm font-medium">You will receive</p>
                        <p className="text-lg">
                          {contributionAmount && !isNaN(Number(contributionAmount))
                            ? (Number(contributionAmount) / launch.currentPhase.price).toLocaleString(undefined, {
                                maximumFractionDigits: 0,
                              })
                            : "0"}{" "}
                          {launch.config.symbol}
                          {launch.config.suffix}
                        </p>
                      </div>

                      <Button className="w-full" onClick={handleContribute} disabled={isContributing || !connected}>
                        {isContributing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : !connected ? (
                          <>
                            <Wallet className="mr-2 h-4 w-4" />
                            Connect Wallet to Contribute
                          </>
                        ) : (
                          <>
                            <Rocket className="mr-2 h-4 w-4" />
                            Contribute
                          </>
                        )}
                      </Button>
                    </>
                  ) : launch.status === "launched" ? (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground mb-4">This token has already been launched.</p>
                      <Button asChild>
                        <a
                          href={`https://explorer.solana.com/address/${launch.tokenAddress}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <ExternalLink className="mr-2 h-4 w-4" />
                          View on Explorer
                        </a>
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground">No active contribution phase at the moment.</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Launch Stats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Total Raised</span>
                    <span className="text-sm font-medium">{launch.totalRaised} SOL</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Participants</span>
                    <span className="text-sm font-medium">{launch.participants}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Created On</span>
                    <span className="text-sm font-medium">{formatDate(launch.createdAt)}</span>
                  </div>
                  {launch.launchDate && (
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Launched On</span>
                      <span className="text-sm font-medium">{formatDate(launch.launchDate)}</span>
                    </div>
                  )}
                  <Progress value={(launch.totalRaised / launch.config.hardCap) * 100} className="h-2 mt-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Soft Cap: {launch.config.softCap} SOL</span>
                    <span>Hard Cap: {launch.config.hardCap} SOL</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

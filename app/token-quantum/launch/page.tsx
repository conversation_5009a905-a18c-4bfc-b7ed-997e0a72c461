import Header from "@/components/header"
import LaunchList from "@/components/token-quantum/launch-list"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Rocket } from "lucide-react"
import Link from "next/link"

export default function TokenLaunchesPage() {
  return (
    <>
      <Header />
      <main className="container py-6">
        <div className="flex flex-col gap-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Token Launches</h1>
              <p className="text-muted-foreground">Discover and participate in Quantum token launches</p>
            </div>
            <Button asChild>
              <Link href="/token-quantum/launch/create">
                <Rocket className="mr-2 h-4 w-4" />
                Create Launch
              </Link>
            </Button>
          </div>

          <LaunchList showTabs={true} />
        </div>
      </main>
    </>
  )
}

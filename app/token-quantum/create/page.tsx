"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { QuantumTokenCreator } from "@/components/token-quantum/quantum-token-creator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Rocket, Shield, Coins } from "lucide-react"

export default function CreateQuantumTokenPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("quantum")

  return (
    <div className="container py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={() => router.back()} className="mr-4">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold"><PERSON><PERSON><PERSON> un token</h1>
          <p className="text-muted-foreground">Choisis<PERSON>z le type de token que vous souhaitez créer</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid grid-cols-3 w-full max-w-2xl mx-auto">
          <TabsTrigger value="standard">Standard</TabsTrigger>
          <TabsTrigger value="quantum">Quantum</TabsTrigger>
          <TabsTrigger value="bonding">Bonding Curve</TabsTrigger>
        </TabsList>

        <TabsContent value="standard">
          <Card>
            <CardHeader>
              <CardTitle>Token Standard</CardTitle>
              <CardDescription>
                Créez un token standard avec des fonctionnalités de base. Idéal pour les débutants.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg flex flex-col items-center text-center">
                  <Coins className="h-8 w-8 text-blue-500 mb-2" />
                  <h3 className="font-medium">Tokenomics simples</h3>
                  <p className="text-sm text-muted-foreground">
                    Configuration simple de l'offre et de la distribution des tokens
                  </p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg flex flex-col items-center text-center">
                  <Shield className="h-8 w-8 text-blue-500 mb-2" />
                  <h3 className="font-medium">Sécurité de base</h3>
                  <p className="text-sm text-muted-foreground">
                    Protections essentielles pour votre token et vos investisseurs
                  </p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg flex flex-col items-center text-center">
                  <Rocket className="h-8 w-8 text-blue-500 mb-2" />
                  <h3 className="font-medium">Lancement rapide</h3>
                  <p className="text-sm text-muted-foreground">
                    Déployez votre token rapidement avec un minimum de configuration
                  </p>
                </div>
              </div>

              <div className="flex justify-center mt-4">
                <Button onClick={() => router.push("/token-factory")}>Créer un token standard</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quantum">
          <QuantumTokenCreator />
        </TabsContent>

        <TabsContent value="bonding">
          <Card>
            <CardHeader>
              <CardTitle>Token avec Bonding Curve</CardTitle>
              <CardDescription>
                Créez un token avec un mécanisme de courbe de liaison qui détermine automatiquement le prix en fonction
                de l'offre.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg flex flex-col items-center text-center">
                  <Coins className="h-8 w-8 text-blue-500 mb-2" />
                  <h3 className="font-medium">Prix dynamique</h3>
                  <p className="text-sm text-muted-foreground">
                    Le prix du token augmente automatiquement avec l'offre en circulation
                  </p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg flex flex-col items-center text-center">
                  <Shield className="h-8 w-8 text-blue-500 mb-2" />
                  <h3 className="font-medium">Liquidité automatique</h3>
                  <p className="text-sm text-muted-foreground">
                    Mécanisme de liquidité intégré qui facilite les transactions
                  </p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg flex flex-col items-center text-center">
                  <Rocket className="h-8 w-8 text-blue-500 mb-2" />
                  <h3 className="font-medium">Tokenomics avancés</h3>
                  <p className="text-sm text-muted-foreground">
                    Paramètres avancés pour une économie de token sophistiquée
                  </p>
                </div>
              </div>

              <div className="flex justify-center mt-4">
                <Button onClick={() => router.push("/token-factory/bonding-curve")}>
                  Créer un token avec Bonding Curve
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

"use client"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, ArrowRight, ArrowUp, ArrowDown, Star, StarOff, ExternalLink, Rocket } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import type { CoinData } from "@/lib/coingecko-service"

interface MarketCarouselProps {
  coins: CoinData[]
  onNavigate?: (id: string) => void
  onToggleFavorite?: (id: string) => void
  favorites?: string[]
  loading?: boolean
  blockchain?: string
  isNew?: boolean
}

export default function MarketCarousel({
  coins,
  onNavigate,
  onToggleFavorite,
  favorites = [],
  loading = false,
  blockchain,
  isNew = false,
}: MarketCarouselProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  const checkScrollButtons = () => {
    if (!containerRef.current) return

    const { scrollLeft, scrollWidth, clientWidth } = containerRef.current
    setCanScrollLeft(scrollLeft > 0)
    setCanScrollRight(scrollLeft + clientWidth < scrollWidth - 10)
  }

  useEffect(() => {
    checkScrollButtons()
    window.addEventListener("resize", checkScrollButtons)
    return () => window.removeEventListener("resize", checkScrollButtons)
  }, [coins])

  const scroll = (direction: "left" | "right") => {
    if (!containerRef.current) return

    const scrollAmount = containerRef.current.clientWidth * 0.8
    const newScrollLeft =
      direction === "left"
        ? containerRef.current.scrollLeft - scrollAmount
        : containerRef.current.scrollLeft + scrollAmount

    containerRef.current.scrollTo({
      left: newScrollLeft,
      behavior: "smooth",
    })

    // Check buttons after scroll animation completes
    setTimeout(checkScrollButtons, 400)
  }

  const formatPrice = (price: number) => {
    if (price < 0.00001) {
      return price.toExponential(2)
    }
    if (price < 0.001) {
      return price.toFixed(6)
    }
    if (price < 1) {
      return price.toFixed(4)
    }
    if (price < 10) {
      return price.toFixed(2)
    }
    return price.toLocaleString(undefined, { maximumFractionDigits: 2 })
  }

  const formatNumber = (num: number, digits = 2) => {
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(digits) + "B"
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(digits) + "M"
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(digits) + "K"
    }
    return num.toFixed(digits)
  }

  if (loading) {
    return (
      <div className="relative">
        <div className="flex overflow-x-auto pb-4 hide-scrollbar" ref={containerRef}>
          <div className="flex gap-4 px-1">
            {[...Array(5)].map((_, index) => (
              <Card key={index} className="min-w-[300px] border-gray-800">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between gap-3 mb-4">
                    <div className="flex items-center gap-3">
                      <Skeleton className="w-10 h-10 rounded-full" />
                      <div>
                        <Skeleton className="h-5 w-24 mb-1" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    </div>
                    <Skeleton className="h-5 w-5 rounded-full" />
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                    <div>
                      <Skeleton className="h-4 w-16 mb-1" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                    <div>
                      <Skeleton className="h-4 w-16 mb-1" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                  </div>
                  <Skeleton className="h-4 w-32" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
        <div className="absolute top-1/2 -left-4 -translate-y-1/2">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full bg-background/80 backdrop-blur-sm"
            disabled={true}
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Précédent</span>
          </Button>
        </div>
        <div className="absolute top-1/2 -right-4 -translate-y-1/2">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full bg-background/80 backdrop-blur-sm"
            disabled={true}
          >
            <ArrowRight className="h-4 w-4" />
            <span className="sr-only">Suivant</span>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {blockchain && (
        <div className="mb-4">
          <Badge className="bg-[#D4AF37] text-black text-sm px-3 py-1">{blockchain}</Badge>
        </div>
      )}

      <div className="flex overflow-x-auto pb-4 hide-scrollbar" ref={containerRef} onScroll={checkScrollButtons}>
        <div className="flex gap-4 px-1">
          {coins.map((coin) => (
            <Card
              key={coin.id}
              className="min-w-[300px] border-gray-800 hover:border-[#D4AF37] transition-colors cursor-pointer"
              onClick={() => onNavigate && onNavigate(coin.id)}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between gap-3 mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full overflow-hidden">
                      <Image
                        src={coin.image || "/placeholder.svg?height=32&width=32"}
                        alt={coin.name}
                        width={32}
                        height={32}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <div className="flex items-center">
                        <h3 className="font-bold">{coin.name}</h3>
                        {(isNew || coin.is_new) && (
                          <Badge className="ml-2 bg-[#D4AF37] text-black">
                            <Rocket className="h-3 w-3 mr-1" /> Nouveau
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">{coin.symbol.toUpperCase()}</p>
                    </div>
                  </div>
                  {onToggleFavorite && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        onToggleFavorite(coin.id)
                      }}
                      className="text-gray-400 hover:text-[#D4AF37]"
                    >
                      {favorites.includes(coin.id) ? (
                        <Star className="h-5 w-5 fill-[#D4AF37] text-[#D4AF37]" />
                      ) : (
                        <StarOff className="h-5 w-5" />
                      )}
                    </button>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                  <div>
                    <span className="text-muted-foreground">Prix:</span>{" "}
                    <div className="font-medium text-lg">${formatPrice(coin.current_price)}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">24h:</span>{" "}
                    <div
                      className={`font-medium flex items-center ${coin.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"}`}
                    >
                      {coin.price_change_percentage_24h >= 0 ? (
                        <ArrowUp className="h-3 w-3 mr-1" />
                      ) : (
                        <ArrowDown className="h-3 w-3 mr-1" />
                      )}
                      {Math.abs(coin.price_change_percentage_24h).toFixed(2)}%
                    </div>
                  </div>
                </div>
                <div className="flex justify-between text-sm">
                  <div>
                    <span className="text-muted-foreground">Cap. Marché:</span>{" "}
                    <span className="font-medium">${formatNumber(coin.market_cap)}</span>
                  </div>
                  <Link
                    href={`/market?token=${coin.id}`}
                    className="text-[#D4AF37] hover:text-[#B8941F] flex items-center gap-1 text-sm font-medium"
                    onClick={(e) => e.stopPropagation()}
                  >
                    Détails <ExternalLink className="h-3 w-3" />
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {canScrollLeft && (
        <div className="absolute top-1/2 -left-4 -translate-y-1/2">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full bg-background/80 backdrop-blur-sm"
            onClick={() => scroll("left")}
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Précédent</span>
          </Button>
        </div>
      )}

      {canScrollRight && (
        <div className="absolute top-1/2 -right-4 -translate-y-1/2">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full bg-background/80 backdrop-blur-sm"
            onClick={() => scroll("right")}
          >
            <ArrowRight className="h-4 w-4" />
            <span className="sr-only">Suivant</span>
          </Button>
        </div>
      )}
    </div>
  )
}

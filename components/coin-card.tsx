import { CardDescription } from "@/components/ui/card"
import { CardTitle } from "@/components/ui/card"
import { CardHeader } from "@/components/ui/card"
import { Card } from "@/components/ui/card"
import Image from "next/image"

export function CoinCard({ logo, ...props }: CoinCardProps) {
  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="relative h-20 w-full">
          {logo && (
            <Image
              src={logo || "/placeholder.svg"}
              alt="Token Logo"
              layout="fill"
              objectFit="contain"
              className="rounded-md"
            />
          )}
        </div>
        <CardTitle>{props.title}</CardTitle>
        <CardDescription>{props.description}</CardDescription>
      </CardHeader>
    </Card>
  )
}

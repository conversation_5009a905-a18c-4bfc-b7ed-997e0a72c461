"use client"

import { useNetwork } from "@/contexts/network-context"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Check, ChevronDown, Loader2 } from "lucide-react"

interface NetworkSelectorProps {
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm"
}

export function NetworkSelector({ variant = "outline", size = "default" }: NetworkSelectorProps) {
  const { network, switchNetwork, isLoading, activeNetwork } = useNetwork()

  const networks = [
    { id: "solana-devnet", name: "Solana Devnet" },
    { id: "solana-mainnet", name: "Solana Mainnet" },
  ]

  const currentNetwork = networks.find((n) => n.id === network) || networks[0]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className="flex items-center gap-1" disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
          ) : (
            <span className={size === "sm" ? "text-xs" : "text-sm"}>{currentNetwork.name}</span>
          )}
          <ChevronDown className={size === "sm" ? "h-3 w-3" : "h-4 w-4"} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {networks.map((item) => (
          <DropdownMenuItem
            key={item.id}
            onClick={() => switchNetwork(item.id)}
            className="flex items-center justify-between"
          >
            {item.name}
            {item.id === network && <Check className="h-4 w-4 ml-2" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

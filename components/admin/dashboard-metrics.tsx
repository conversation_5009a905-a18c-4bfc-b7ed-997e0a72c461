// components/admin/dashboard-metrics.tsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, Coins, DollarSign, FileText } from "lucide-react"

interface DashboardMetricsProps {
  userCount: number
  transactionCount: number
  totalEarnings: number
  tokenCount: number
}

export default function DashboardMetrics({
  userCount,
  transactionCount,
  totalEarnings,
  tokenCount,
}: DashboardMetricsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-4 w-4" />
            Users
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{userCount}</div>
          <p className="text-sm text-muted-foreground">Total number of users on the platform</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Coins className="mr-2 h-4 w-4" />
            Tokens
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{tokenCount}</div>
          <p className="text-sm text-muted-foreground">Total number of tokens created</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="mr-2 h-4 w-4" />
            Earnings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">${totalEarnings.toLocaleString()}</div>
          <p className="text-sm text-muted-foreground">Total earnings generated by the platform</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-4 w-4" />
            Transactions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{transactionCount}</div>
          <p className="text-sm text-muted-foreground">Total number of transactions processed</p>
        </CardContent>
      </Card>
    </div>
  )
}

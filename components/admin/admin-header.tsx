"use client"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { NetworkSelector } from "@/components/network-selector"

export function AdminHeader() {
  // Suppression de toute vérification d'authentification ou redirection

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6">
      <div className="flex flex-1 items-center gap-2">
        <Link href="/admin" className="flex items-center gap-2 font-semibold">
          <span className="hidden md:inline-block">Administration</span>
        </Link>
      </div>
      <div className="flex items-center gap-4">
        <NetworkSelector />
        <Button variant="outline" size="sm" asChild>
          <Link href="/">Retour au site</Link>
        </Button>
      </div>
    </header>
  )
}

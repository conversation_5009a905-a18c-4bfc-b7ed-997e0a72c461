"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, CheckCircle, Trash2, UserPlus } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"

type AdminUser = {
  walletAddress: string
  role: "admin" | "superadmin"
  addedAt: string
  addedBy?: string
}

export function AdminRoleManagement() {
  const [admins, setAdmins] = useState<AdminUser[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [newAdminWallet, setNewAdminWallet] = useState("")
  const [newAdminRole, setNewAdminRole] = useState<"admin" | "superadmin">("admin")
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    fetchAdmins()
  }, [])

  const fetchAdmins = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/users")

      if (!response.ok) {
        throw new Error("Failed to fetch admin users")
      }

      const data = await response.json()
      setAdmins(data.admins)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      console.error("Error fetching admin users:", err)
    } finally {
      setLoading(false)
    }
  }

  const addAdmin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newAdminWallet) {
      setError("Veuillez entrer une adresse de wallet valide")
      return
    }

    try {
      setIsSubmitting(true)
      setError(null)
      setSuccess(null)

      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          walletAddress: newAdminWallet,
          role: newAdminRole,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to add admin")
      }

      setSuccess(`Administrateur ajouté avec succès: ${newAdminWallet}`)
      setNewAdminWallet("")
      fetchAdmins()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      console.error("Error adding admin:", err)
    } finally {
      setIsSubmitting(false)
    }
  }

  const removeAdmin = async (walletAddress: string) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer l'administrateur ${walletAddress} ?`)) {
      return
    }

    try {
      setError(null)
      setSuccess(null)

      const response = await fetch(`/api/admin/users?wallet=${walletAddress}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to remove admin")
      }

      setSuccess(`Administrateur supprimé avec succès: ${walletAddress}`)
      fetchAdmins()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      console.error("Error removing admin:", err)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Gestion des administrateurs</CardTitle>
          <CardDescription>Ajoutez ou supprimez des administrateurs de la plateforme</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-6 bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Succès</AlertTitle>
              <AlertDescription className="text-green-700">{success}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={addAdmin} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="wallet-address">Adresse du wallet</Label>
                <Input
                  id="wallet-address"
                  placeholder="Entrez l'adresse du wallet"
                  value={newAdminWallet}
                  onChange={(e) => setNewAdminWallet(e.target.value)}
                  disabled={isSubmitting}
                />
              </div>

              <div>
                <Label htmlFor="admin-role">Rôle</Label>
                <Select
                  value={newAdminRole}
                  onValueChange={(value) => setNewAdminRole(value as "admin" | "superadmin")}
                  disabled={isSubmitting}
                >
                  <SelectTrigger id="admin-role">
                    <SelectValue placeholder="Sélectionnez un rôle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="superadmin">Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button type="submit" disabled={isSubmitting} className="w-full md:w-auto">
              <UserPlus className="mr-2 h-4 w-4" />
              {isSubmitting ? "Ajout en cours..." : "Ajouter un administrateur"}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Liste des administrateurs</CardTitle>
          <CardDescription>Administrateurs actuels de la plateforme</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-full" />
                </div>
              ))}
            </div>
          ) : admins.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">Aucun administrateur trouvé</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Adresse du wallet</TableHead>
                  <TableHead>Rôle</TableHead>
                  <TableHead>Ajouté le</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {admins.map((admin) => (
                  <TableRow key={admin.walletAddress}>
                    <TableCell className="font-mono text-xs md:text-sm">{admin.walletAddress}</TableCell>
                    <TableCell>
                      <Badge variant={admin.role === "superadmin" ? "default" : "outline"}>
                        {admin.role === "superadmin" ? "Super Admin" : "Admin"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {new Date(admin.addedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeAdmin(admin.walletAddress)}
                        title="Supprimer l'administrateur"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">Total: {loading ? "..." : admins.length} administrateurs</div>
          <Button variant="outline" onClick={fetchAdmins} disabled={loading}>
            Rafraîchir
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

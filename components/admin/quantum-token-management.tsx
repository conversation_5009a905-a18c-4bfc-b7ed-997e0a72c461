"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  CheckCircle,
  Plus,
  Trash2,
  AlertTriangle,
  Info,
  Shield,
  Lock,
  Eye,
  Ban,
  Download,
  <PERSON>lter,
  Search,
  Settings,
  BarChart3,
  Users,
  RefreshCw,
  <PERSON>rkles,
  FileText,
  Copy,
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function QuantumTokenManagement() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("tokens")
  const [searchQuery, setSearchQuery] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [newSuffix, setNewSuffix] = useState("")
  const [isEnabled, setIsEnabled] = useState(true)
  const [creationFee, setCreationFee] = useState("0.1")
  const [minSupply, setMinSupply] = useState("1000000")
  const [maxSupply, setMaxSupply] = useState("1000000000000")
  const [isSaving, setIsSaving] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [selectedToken, setSelectedToken] = useState<any>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Available suffixes
  const [availableSuffixes, setAvailableSuffixes] = useState(["QUANTUM", "GF", "SOL", "MEME", "AI", "BONK", "PUMP"])

  // Simulated data for tokens
  const tokens = [
    {
      id: "1",
      name: "Global Finance",
      symbol: "GFQUANTUM",
      creator: "8xj7...4Fgh",
      createdAt: "2023-10-15",
      supply: "1000000000",
      holders: 124,
      status: "active",
      address: "Gxyz123456789abcdef",
      features: {
        antiBot: true,
        antiDump: true,
        autoLiquidity: true,
        marketingFee: 2,
        liquidityFee: 3,
      },
    },
    {
      id: "2",
      name: "Solana Meme",
      symbol: "MEMESOL",
      creator: "3kL9...7Tpq",
      createdAt: "2023-10-14",
      supply: "500000000",
      holders: 87,
      status: "active",
      address: "Gxyz987654321abcdef",
      features: {
        antiBot: true,
        antiDump: false,
        autoLiquidity: true,
        marketingFee: 1,
        liquidityFee: 2,
      },
    },
    {
      id: "3",
      name: "Quantum Finance",
      symbol: "QFINQUANTUM",
      creator: "5mN2...9Rzt",
      createdAt: "2023-10-12",
      supply: "2000000000",
      holders: 56,
      status: "pending",
      address: "Gxyzabcdef123456789",
      features: {
        antiBot: true,
        antiDump: true,
        autoLiquidity: false,
        marketingFee: 3,
        liquidityFee: 2,
      },
    },
    {
      id: "4",
      name: "Blocked Token",
      symbol: "BLOCKEDQUANTUM",
      creator: "2pK7...1Lxv",
      createdAt: "2023-10-10",
      supply: "100000000",
      holders: 12,
      status: "blocked",
      address: "Gxyz456789abcdef123",
      features: {
        antiBot: false,
        antiDump: false,
        autoLiquidity: false,
        marketingFee: 5,
        liquidityFee: 5,
      },
    },
  ]

  // Filter tokens
  const filteredTokens = tokens.filter((token) => {
    const matchesSearch =
      token.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.creator.toLowerCase().includes(searchQuery.toLowerCase())

    if (filterStatus === "all") return matchesSearch
    return matchesSearch && token.status === filterStatus
  })

  // Get status badge with appropriate color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case "blocked":
        return <Badge className="bg-red-100 text-red-800">Blocked</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Add suffix
  const handleAddSuffix = () => {
    if (!newSuffix) return

    const sanitizedSuffix = newSuffix.toUpperCase().trim()
    if (availableSuffixes.includes(sanitizedSuffix)) {
      setError("This suffix already exists")
      return
    }

    setAvailableSuffixes([...availableSuffixes, sanitizedSuffix])
    setNewSuffix("")
    setSuccess("Suffix added successfully")

    setTimeout(() => {
      setSuccess(null)
    }, 3000)
  }

  // Remove suffix
  const handleRemoveSuffix = (suffix: string) => {
    setAvailableSuffixes(availableSuffixes.filter((s) => s !== suffix))

    toast({
      title: "Suffix removed",
      description: `The suffix ${suffix} has been successfully removed.`,
    })
  }

  // Save configuration
  const handleSaveConfig = async () => {
    setIsSaving(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulation of saving
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setSuccess("Configuration saved successfully")

      setTimeout(() => {
        setSuccess(null)
      }, 3000)
    } catch (err) {
      console.error("Error saving:", err)
      setError("An error occurred while saving")
    } finally {
      setIsSaving(false)
    }
  }

  // View token details
  const handleViewToken = (token: any) => {
    setSelectedToken(token)
    setIsDialogOpen(true)
  }

  // Toggle token status
  const handleToggleStatus = (token: any) => {
    const newStatus = token.status === "blocked" ? "active" : "blocked"

    toast({
      title: `Token ${newStatus === "blocked" ? "Blocked" : "Activated"}`,
      description: `${token.name} (${token.symbol}) has been ${newStatus === "blocked" ? "blocked" : "activated"}.`,
      variant: newStatus === "blocked" ? "destructive" : "default",
    })
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "The text has been copied to your clipboard",
    })
  }

  // Export data
  const handleExport = (format: string) => {
    toast({
      title: "Export initiated",
      description: `Exporting data in ${format.toUpperCase()} format.`,
    })
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="tokens">
            <FileText className="h-4 w-4 mr-2" />
            Tokens
          </TabsTrigger>
          <TabsTrigger value="config">
            <Settings className="h-4 w-4 mr-2" />
            Configuration
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            Users
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tokens" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Quantum Tokens List</CardTitle>
                  <CardDescription>Manage tokens created by users</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => handleExport("csv")}>
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleExport("json")}>
                    <Download className="h-4 w-4 mr-2" />
                    Export JSON
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search..."
                      className="pl-8 w-[250px]"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant={filterStatus === "all" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus("all")}
                  >
                    All
                  </Button>
                  <Button
                    variant={filterStatus === "active" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus("active")}
                  >
                    Active
                  </Button>
                  <Button
                    variant={filterStatus === "pending" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus("pending")}
                  >
                    Pending
                  </Button>
                  <Button
                    variant={filterStatus === "blocked" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus("blocked")}
                  >
                    Blocked
                  </Button>
                </div>
              </div>

              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Symbol</TableHead>
                      <TableHead>Creator</TableHead>
                      <TableHead>Creation Date</TableHead>
                      <TableHead>Supply</TableHead>
                      <TableHead>Holders</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTokens.map((token) => (
                      <TableRow key={token.id}>
                        <TableCell className="font-medium">{token.name}</TableCell>
                        <TableCell>{token.symbol}</TableCell>
                        <TableCell className="font-mono text-xs">{token.creator}</TableCell>
                        <TableCell>{token.createdAt}</TableCell>
                        <TableCell>{Number(token.supply).toLocaleString()}</TableCell>
                        <TableCell>{token.holders}</TableCell>
                        <TableCell>{getStatusBadge(token.status)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm" onClick={() => handleViewToken(token)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            {token.status === "blocked" ? (
                              <Button variant="ghost" size="sm" onClick={() => handleToggleStatus(token)}>
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              </Button>
                            ) : (
                              <Button variant="ghost" size="sm" onClick={() => handleToggleStatus(token)}>
                                <Ban className="h-4 w-4 text-red-500" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config">
          <Card>
            <CardHeader>
              <CardTitle>Quantum Tokens Configuration</CardTitle>
              <CardDescription>Manage settings for Quantum tokens and authorized suffixes</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="tokenEnabled">Enable Quantum Tokens</Label>
                  <p className="text-sm text-muted-foreground">Allow users to create Quantum tokens</p>
                </div>
                <Switch id="tokenEnabled" checked={isEnabled} onCheckedChange={setIsEnabled} />
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">General Settings</h3>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="creationFee">Creation Fee (SOL)</Label>
                    <Input
                      id="creationFee"
                      type="number"
                      step="0.01"
                      value={creationFee}
                      onChange={(e) => setCreationFee(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="networkFee">Estimated Network Fee (SOL)</Label>
                    <Input id="networkFee" type="number" step="0.000001" value="0.00089" disabled />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minSupply">Minimum Supply</Label>
                    <Input
                      id="minSupply"
                      type="number"
                      value={minSupply}
                      onChange={(e) => setMinSupply(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxSupply">Maximum Supply</Label>
                    <Input
                      id="maxSupply"
                      type="number"
                      value={maxSupply}
                      onChange={(e) => setMaxSupply(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Suffix Management</h3>

                <div className="flex items-end gap-2">
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="newSuffix">Add a new suffix</Label>
                    <Input
                      id="newSuffix"
                      placeholder="Ex: QUANTUM"
                      value={newSuffix}
                      onChange={(e) => setNewSuffix(e.target.value)}
                      className="uppercase"
                    />
                  </div>
                  <Button onClick={handleAddSuffix}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add
                  </Button>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="bg-green-50 text-green-800 border-green-200">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription>{success}</AlertDescription>
                  </Alert>
                )}

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Suffix</TableHead>
                        <TableHead>Tokens Created</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {availableSuffixes.map((suffix) => (
                        <TableRow key={suffix}>
                          <TableCell className="font-medium">{suffix}</TableCell>
                          <TableCell>{Math.floor(Math.random() * 50)}</TableCell>
                          <TableCell>
                            <Badge className="bg-green-100 text-green-800 border-green-200">Active</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleRemoveSuffix(suffix)}>
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Suffixes are used to ensure the authenticity of Quantum tokens and prevent counterfeits.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig} disabled={isSaving} className="ml-auto">
                {isSaving ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Quantum Tokens Analytics</CardTitle>
              <CardDescription>Statistics and metrics for Quantum tokens</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{tokens.length}</div>
                    <p className="text-sm text-muted-foreground">Total tokens</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{tokens.filter((t) => t.status === "active").length}</div>
                    <p className="text-sm text-muted-foreground">Active tokens</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">279</div>
                    <p className="text-sm text-muted-foreground">Unique holders</p>
                  </CardContent>
                </Card>
              </div>

              <div className="h-[300px] mt-6 flex items-center justify-center border rounded-md bg-muted/50">
                <p className="text-muted-foreground">Token creation chart (to be implemented)</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Feature Usage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <Shield className="h-4 w-4 mr-2 text-blue-500" />
                          <span>Anti-Bot</span>
                        </div>
                        <span className="font-medium">75%</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full">
                        <div className="h-2 bg-blue-500 rounded-full" style={{ width: "75%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2 mt-4">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <Lock className="h-4 w-4 mr-2 text-purple-500" />
                          <span>Anti-Dump</span>
                        </div>
                        <span className="font-medium">50%</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full">
                        <div className="h-2 bg-purple-500 rounded-full" style={{ width: "50%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2 mt-4">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <RefreshCw className="h-4 w-4 mr-2 text-green-500" />
                          <span>Auto-Liquidity</span>
                        </div>
                        <span className="font-medium">60%</span>
                      </div>
                      <div className="h-2 bg-muted rounded-full">
                        <div className="h-2 bg-green-500 rounded-full" style={{ width: "60%" }}></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Token Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px] flex items-center justify-center">
                      <p className="text-muted-foreground">Distribution chart (to be implemented)</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>Manage users who create Quantum tokens</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Wallet</TableHead>
                      <TableHead>Tokens Created</TableHead>
                      <TableHead>Join Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">John Doe</TableCell>
                      <TableCell className="font-mono text-xs">8xj7...4Fgh</TableCell>
                      <TableCell>3</TableCell>
                      <TableCell>2023-09-15</TableCell>
                      <TableCell>
                        <Badge className="bg-green-100 text-green-800">Active</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Alice Smith</TableCell>
                      <TableCell className="font-mono text-xs">3kL9...7Tpq</TableCell>
                      <TableCell>1</TableCell>
                      <TableCell>2023-09-20</TableCell>
                      <TableCell>
                        <Badge className="bg-green-100 text-green-800">Active</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Bob Johnson</TableCell>
                      <TableCell className="font-mono text-xs">5mN2...9Rzt</TableCell>
                      <TableCell>2</TableCell>
                      <TableCell>2023-09-18</TableCell>
                      <TableCell>
                        <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Carol Williams</TableCell>
                      <TableCell className="font-mono text-xs">2pK7...1Lxv</TableCell>
                      <TableCell>0</TableCell>
                      <TableCell>2023-09-22</TableCell>
                      <TableCell>
                        <Badge className="bg-red-100 text-red-800">Blocked</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Token Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Token Details</DialogTitle>
            <DialogDescription>Detailed information about the selected token</DialogDescription>
          </DialogHeader>

          {selectedToken && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold">{selectedToken.name}</h2>
                  <p className="text-sm text-muted-foreground">
                    {selectedToken.symbol} • Created on {selectedToken.createdAt}
                  </p>
                </div>
                <Badge
                  className={`
                  ${
                    selectedToken.status === "active"
                      ? "bg-green-100 text-green-800"
                      : selectedToken.status === "pending"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-red-100 text-red-800"
                  }
                `}
                >
                  {selectedToken.status.charAt(0).toUpperCase() + selectedToken.status.slice(1)}
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Token Address</Label>
                  <div className="flex items-center mt-1">
                    <code className="text-xs bg-muted p-1 rounded">{selectedToken.address}</code>
                    <Button variant="ghost" size="sm" onClick={() => copyToClipboard(selectedToken.address)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Creator</Label>
                  <div className="font-medium mt-1">{selectedToken.creator}</div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label className="text-muted-foreground">Total Supply</Label>
                  <div className="font-medium mt-1">{Number(selectedToken.supply).toLocaleString()}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Holders</Label>
                  <div className="font-medium mt-1">{selectedToken.holders}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Transactions</Label>
                  <div className="font-medium mt-1">{Math.floor(Math.random() * 1000)}</div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="font-medium mb-2">Security Features</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedToken.features.antiBot && (
                    <Badge variant="outline" className="bg-blue-50">
                      <Shield className="h-3 w-3 mr-1" />
                      Anti-Bot
                    </Badge>
                  )}
                  {selectedToken.features.antiDump && (
                    <Badge variant="outline" className="bg-purple-50">
                      <Lock className="h-3 w-3 mr-1" />
                      Anti-Dump
                    </Badge>
                  )}
                  {selectedToken.features.autoLiquidity && (
                    <Badge variant="outline" className="bg-green-50">
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Auto-Liquidity
                    </Badge>
                  )}
                  <Badge variant="outline" className="bg-amber-50">
                    <Sparkles className="h-3 w-3 mr-1" />
                    {selectedToken.features.marketingFee}% Marketing Fee
                  </Badge>
                  <Badge variant="outline" className="bg-amber-50">
                    <Sparkles className="h-3 w-3 mr-1" />
                    {selectedToken.features.liquidityFee}% Liquidity Fee
                  </Badge>
                </div>
              </div>

              <div className="h-[200px] border rounded-md p-4 bg-muted/20">
                <h3 className="font-medium mb-2">Price Chart</h3>
                <div className="h-[150px] flex items-center justify-center">
                  <p className="text-muted-foreground">Price chart will be displayed here</p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Close
            </Button>
            {selectedToken && selectedToken.status === "blocked" ? (
              <Button onClick={() => handleToggleStatus(selectedToken)}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Activate Token
              </Button>
            ) : (
              selectedToken && (
                <Button variant="destructive" onClick={() => handleToggleStatus(selectedToken)}>
                  <Ban className="h-4 w-4 mr-2" />
                  Block Token
                </Button>
              )
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

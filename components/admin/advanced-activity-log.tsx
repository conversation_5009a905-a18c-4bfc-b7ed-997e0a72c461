"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Search, Download, RefreshCw } from "lucide-react"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

type ActivityLog = {
  id: string
  adminWallet: string
  action: string
  details: string
  timestamp: string
  status: "success" | "warning" | "error"
}

export function AdvancedActivityLog() {
  const [logs, setLogs] = useState<ActivityLog[]>([])
  const [filteredLogs, setFilteredLogs] = useState<ActivityLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [searchTerm, setSearchTerm] = useState("")
  const [actionFilter, setActionFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>(undefined)
  const { toast } = useToast()

  useEffect(() => {
    fetchActivityLogs()
  }, [])

  useEffect(() => {
    // Filtrer les logs en fonction des critères
    let filtered = [...logs]

    // Filtrer par recherche
    if (searchTerm) {
      filtered = filtered.filter(
        (log) =>
          log.adminWallet.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.details.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    // Filtrer par action
    if (actionFilter !== "all") {
      filtered = filtered.filter((log) => log.action === actionFilter)
    }

    // Filtrer par statut
    if (statusFilter !== "all") {
      filtered = filtered.filter((log) => log.status === statusFilter)
    }

    // Filtrer par plage de dates
    if (dateRange?.from && dateRange?.to) {
      filtered = filtered.filter((log) => {
        const logDate = new Date(log.timestamp)
        return logDate >= dateRange.from && logDate <= dateRange.to
      })
    }

    setFilteredLogs(filtered)
    setTotalPages(Math.ceil(filtered.length / itemsPerPage))
    setPage(1) // Réinitialiser à la première page après filtrage
  }, [logs, searchTerm, actionFilter, statusFilter, dateRange, itemsPerPage])

  const fetchActivityLogs = async () => {
    try {
      setIsLoading(true)
      // Simuler un chargement de données
      setTimeout(() => {
        // Générer des données de test plus nombreuses
        const testLogs: ActivityLog[] = []
        const actions = [
          "Login",
          "Token Creation",
          "Settings Update",
          "User Ban",
          "Token Deletion",
          "Configuration Change",
          "Security Alert",
          "Database Backup",
          "API Call",
          "Fee Adjustment",
        ]
        const statuses = ["success", "warning", "error"] as const
        const wallets = [
          "GfEHHkrQY45ZPoKC9srbjKZiCnwEWNVTAn3UxTGTZgwT",
          "BXvEuaBKzLJtZQxwKWgmUEJWAM9JYiVEYBT8HPZjmXbU",
          "5YNmS1R9nNSCDzb5a7mMJ1dwK9uHeAAF4CerVnZgbdr3",
        ]

        // Générer 50 entrées de test
        for (let i = 0; i < 50; i++) {
          const date = new Date()
          date.setDate(date.getDate() - Math.floor(Math.random() * 30))
          date.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), Math.floor(Math.random() * 60))

          testLogs.push({
            id: `log_${i}`,
            adminWallet: wallets[Math.floor(Math.random() * wallets.length)],
            action: actions[Math.floor(Math.random() * actions.length)],
            details: `Detailed information about action #${i}`,
            timestamp: date.toISOString(),
            status: statuses[Math.floor(Math.random() * statuses.length)],
          })
        }

        // Trier par date décroissante
        testLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

        setLogs(testLogs)
        setFilteredLogs(testLogs)
        setTotalPages(Math.ceil(testLogs.length / itemsPerPage))
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error("Error fetching activity logs:", error)
      toast({
        title: "Error",
        description: "Failed to load activity logs. Please try again.",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status: "success" | "warning" | "error") => {
    switch (status) {
      case "success":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            Success
          </Badge>
        )
      case "warning":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            Warning
          </Badge>
        )
      case "error":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Error
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const exportLogs = () => {
    try {
      // Créer un CSV à partir des logs filtrés
      const headers = ["ID", "Admin Wallet", "Action", "Details", "Timestamp", "Status"]
      const csvContent =
        headers.join(",") +
        "\n" +
        filteredLogs
          .map((log) => {
            return `"${log.id}","${log.adminWallet}","${log.action}","${log.details.replace(/"/g, '""')}","${log.timestamp}","${log.status}"`
          })
          .join("\n")

      // Créer un blob et télécharger
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" })
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.setAttribute("href", url)
      link.setAttribute("download", `admin-activity-log-${format(new Date(), "yyyy-MM-dd")}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Exportation réussie",
        description: "Les logs d'activité ont été exportés avec succès",
      })
    } catch (error) {
      console.error("Error exporting logs:", error)
      toast({
        title: "Erreur d'exportation",
        description: "Impossible d'exporter les logs d'activité",
        variant: "destructive",
      })
    }
  }

  // Pagination des données
  const paginatedLogs = filteredLogs.slice((page - 1) * itemsPerPage, page * itemsPerPage)

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <CardTitle>Journal d'activité administrateur</CardTitle>
            <CardDescription>Historique des actions effectuées par les administrateurs</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={fetchActivityLogs}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualiser
            </Button>
            <Button variant="outline" size="sm" onClick={exportLogs}>
              <Download className="h-4 w-4 mr-2" />
              Exporter
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Filtres */}
          <div className="flex flex-col gap-4 md:flex-row md:items-end">
            <div className="flex-1 space-y-2">
              <label className="text-sm font-medium">Recherche</label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher par wallet ou détails..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="w-full md:w-40 space-y-2">
              <label className="text-sm font-medium">Action</label>
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les actions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les actions</SelectItem>
                  <SelectItem value="Login">Connexion</SelectItem>
                  <SelectItem value="Token Creation">Création de token</SelectItem>
                  <SelectItem value="Settings Update">Mise à jour des réglages</SelectItem>
                  <SelectItem value="User Ban">Bannissement d'utilisateur</SelectItem>
                  <SelectItem value="Token Deletion">Suppression de token</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-full md:w-40 space-y-2">
              <label className="text-sm font-medium">Statut</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value="success">Succès</SelectItem>
                  <SelectItem value="warning">Avertissement</SelectItem>
                  <SelectItem value="error">Erreur</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-full md:w-auto space-y-2">
              <label className="text-sm font-medium">Période</label>
              <DateRangePicker
                value={dateRange}
                onChange={setDateRange}
                locale={fr}
                calendarTodayClassName="bg-primary text-primary-foreground"
              />
            </div>
          </div>

          {/* Tableau des logs */}
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Admin</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Détails</TableHead>
                      <TableHead>Date et heure</TableHead>
                      <TableHead>Statut</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedLogs.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="h-24 text-center">
                          Aucun log d'activité trouvé
                        </TableCell>
                      </TableRow>
                    ) : (
                      paginatedLogs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell className="font-mono text-xs">
                            {`${log.adminWallet.substring(0, 6)}...${log.adminWallet.substring(log.adminWallet.length - 4)}`}
                          </TableCell>
                          <TableCell>{log.action}</TableCell>
                          <TableCell>{log.details}</TableCell>
                          <TableCell>{new Date(log.timestamp).toLocaleString("fr-FR")}</TableCell>
                          <TableCell>{getStatusBadge(log.status)}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Select
                    value={itemsPerPage.toString()}
                    onValueChange={(value) => setItemsPerPage(Number.parseInt(value))}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-muted-foreground">par page</span>
                </div>

                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious onClick={() => setPage((p) => Math.max(1, p - 1))} disabled={page === 1} />
                    </PaginationItem>

                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      // Logique pour afficher les bonnes pages en fonction de la page actuelle
                      let pageNum = 1
                      if (totalPages <= 5) {
                        pageNum = i + 1
                      } else if (page <= 3) {
                        pageNum = i + 1
                        if (i === 4) pageNum = totalPages
                      } else if (page >= totalPages - 2) {
                        pageNum = totalPages - 4 + i
                      } else {
                        pageNum = page - 2 + i
                      }

                      return (
                        <PaginationItem key={i}>
                          {i === 3 && page > 3 && totalPages > 5 ? (
                            <PaginationEllipsis />
                          ) : (
                            <PaginationLink isActive={pageNum === page} onClick={() => setPage(pageNum)}>
                              {pageNum}
                            </PaginationLink>
                          )}
                        </PaginationItem>
                      )
                    })}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                        disabled={page === totalPages}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between text-sm text-muted-foreground border-t pt-6">
        <div>
          Total: {filteredLogs.length} log{filteredLogs.length !== 1 ? "s" : ""} d'activité
        </div>
        <div>Dernière actualisation: {format(new Date(), "dd/MM/yyyy HH:mm:ss")}</div>
      </CardFooter>
    </Card>
  )
}

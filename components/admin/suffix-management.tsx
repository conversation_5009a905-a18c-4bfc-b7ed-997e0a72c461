"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Check, AlertCircle, Trash2, Plus, Save } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import tokenSuffixService, { type TokenSuffix } from "@/lib/token-suffix-service"

export function SuffixManagement() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [suffixes, setSuffixes] = useState<TokenSuffix[]>([])
  const [newSuffix, setNewSuffix] = useState<string>("")
  const [newSuffixNetwork, setNewSuffixNetwork] = useState<string>("all")
  const [newSuffixPrice, setNewSuffixPrice] = useState<number>(0)
  const [newSuffixReserved, setNewSuffixReserved] = useState<boolean>(false)
  const [isAddingNew, setIsAddingNew] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Charger les suffixes existants
  useEffect(() => {
    const loadSuffixes = async () => {
      try {
        const data = await tokenSuffixService.getSuffixes()
        setSuffixes(data)
      } catch (err) {
        console.error("Erreur lors du chargement des suffixes:", err)
        setError("Impossible de charger les suffixes. Veuillez réessayer.")
      } finally {
        setIsLoading(false)
      }
    }

    loadSuffixes()
  }, [])

  // Ajouter un nouveau suffixe
  const addSuffix = async () => {
    if (!newSuffix) {
      setError("Le suffixe ne peut pas être vide")
      return
    }

    if (suffixes.some((s) => s.suffix === newSuffix)) {
      setError("Ce suffixe existe déjà")
      return
    }

    try {
      const newSuffixObj = await tokenSuffixService.addSuffix(
        newSuffix,
        newSuffixNetwork,
        newSuffixPrice,
        newSuffixReserved,
      )

      setSuffixes([...suffixes, newSuffixObj])
      setNewSuffix("")
      setNewSuffixNetwork("all")
      setNewSuffixPrice(0)
      setNewSuffixReserved(false)
      setIsAddingNew(false)
      setError(null)
    } catch (err: any) {
      setError(err.message || "Erreur lors de l'ajout du suffixe")
    }
  }

  // Supprimer un suffixe
  const removeSuffix = async (id: string) => {
    try {
      await tokenSuffixService.deleteSuffix(id)
      setSuffixes(suffixes.filter((s) => s.id !== id))
    } catch (err: any) {
      setError(err.message || "Erreur lors de la suppression du suffixe")
    }
  }

  // Mettre à jour un suffixe
  const updateSuffix = (id: string, field: keyof TokenSuffix, value: any) => {
    setSuffixes(suffixes.map((s) => (s.id === id ? { ...s, [field]: value } : s)))
    setHasUnsavedChanges(true)
  }

  // Sauvegarder les modifications
  const saveSuffixes = async () => {
    setIsSaving(true)
    setError(null)

    try {
      await tokenSuffixService.updateSuffixes(suffixes)
      setIsSuccess(true)
      setHasUnsavedChanges(false)
      setTimeout(() => setIsSuccess(false), 3000)
    } catch (err: any) {
      console.error("Erreur lors de la sauvegarde des suffixes:", err)
      setError(err.message || "Une erreur s'est produite lors de la sauvegarde des suffixes")
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Gestion des Suffixes de Tokens</CardTitle>
        <CardDescription>
          Les suffixes sont utilisés pour générer des adresses de tokens reconnaissables. Par exemple, un token avec le
          suffixe "GF" aura une adresse se terminant par "...GF".
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isSuccess && (
          <Alert className="bg-green-50 border-green-200 mb-4">
            <Check className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Modifications enregistrées</AlertTitle>
            <AlertDescription className="text-green-700">Les suffixes ont été mis à jour avec succès.</AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Suffixe</TableHead>
                <TableHead>Réseau</TableHead>
                <TableHead>Prix (SOL)</TableHead>
                <TableHead>Réservé</TableHead>
                <TableHead>Actif</TableHead>
                <TableHead>Utilisations</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {suffixes.map((suffix) => (
                <TableRow key={suffix.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Input
                        value={suffix.suffix}
                        onChange={(e) => updateSuffix(suffix.id, "suffix", e.target.value.toUpperCase())}
                        className="w-24"
                        maxLength={5}
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Select value={suffix.network} onValueChange={(value) => updateSuffix(suffix.id, "network", value)}>
                      <SelectTrigger className="w-28">
                        <SelectValue placeholder="Réseau" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tous</SelectItem>
                        <SelectItem value="mainnet">Mainnet</SelectItem>
                        <SelectItem value="devnet">Devnet</SelectItem>
                        <SelectItem value="testnet">Testnet</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      value={suffix.price}
                      onChange={(e) => updateSuffix(suffix.id, "price", Number.parseFloat(e.target.value))}
                      className="w-24"
                      step="0.01"
                      min="0"
                    />
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={suffix.reserved}
                      onCheckedChange={(checked) => updateSuffix(suffix.id, "reserved", checked)}
                    />
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={suffix.active}
                      onCheckedChange={(checked) => updateSuffix(suffix.id, "active", checked)}
                    />
                  </TableCell>
                  <TableCell>
                    <Badge variant={suffix.usageCount > 0 ? "secondary" : "outline"}>{suffix.usageCount}</Badge>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSuffix(suffix.id)}
                      disabled={suffix.usageCount > 0}
                      title={suffix.usageCount > 0 ? "Impossible de supprimer un suffixe utilisé" : "Supprimer"}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {isAddingNew ? (
            <div className="border p-4 rounded-md space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Ajouter un nouveau suffixe</h3>
                <Button variant="ghost" size="sm" onClick={() => setIsAddingNew(false)}>
                  Annuler
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="newSuffix">Suffixe (2-5 caractères)</Label>
                  <Input
                    id="newSuffix"
                    value={newSuffix}
                    onChange={(e) => setNewSuffix(e.target.value.toUpperCase())}
                    maxLength={5}
                    placeholder="GF"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newSuffixNetwork">Réseau</Label>
                  <Select value={newSuffixNetwork} onValueChange={setNewSuffixNetwork}>
                    <SelectTrigger id="newSuffixNetwork">
                      <SelectValue placeholder="Réseau" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tous</SelectItem>
                      <SelectItem value="mainnet">Mainnet</SelectItem>
                      <SelectItem value="devnet">Devnet</SelectItem>
                      <SelectItem value="testnet">Testnet</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newSuffixPrice">Prix (SOL)</Label>
                  <Input
                    id="newSuffixPrice"
                    type="number"
                    value={newSuffixPrice}
                    onChange={(e) => setNewSuffixPrice(Number.parseFloat(e.target.value))}
                    step="0.01"
                    min="0"
                  />
                </div>
                <div className="space-y-2 flex items-end">
                  <div className="flex items-center space-x-2">
                    <Switch id="newSuffixReserved" checked={newSuffixReserved} onCheckedChange={setNewSuffixReserved} />
                    <Label htmlFor="newSuffixReserved">Réservé</Label>
                  </div>
                </div>
              </div>
              <Button onClick={addSuffix}>Ajouter le suffixe</Button>
            </div>
          ) : (
            <Button onClick={() => setIsAddingNew(true)} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Ajouter un nouveau suffixe
            </Button>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div>
          {hasUnsavedChanges && (
            <span className="text-amber-600 text-sm">Vous avez des modifications non enregistrées</span>
          )}
        </div>
        <div className="flex space-x-2">
          <Button variant="default" onClick={saveSuffixes} disabled={isSaving || !hasUnsavedChanges}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Enregistrement...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Sauvegarder les modifications
              </>
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

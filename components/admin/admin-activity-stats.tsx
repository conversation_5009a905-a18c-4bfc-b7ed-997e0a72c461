"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Skeleton } from "@/components/ui/skeleton"

type ActivityStats = {
  totalActivities: number
  uniqueAdmins: number
  activityByType: Record<string, number>
}

export function AdminActivityStats() {
  const [stats, setStats] = useState<ActivityStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeframe, setTimeframe] = useState("week")

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/admin/activity/stats?timeframe=${timeframe}`)

        if (!response.ok) {
          throw new Error("Failed to fetch activity stats")
        }

        const data = await response.json()
        setStats(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : "An unknown error occurred")
        console.error("Error fetching admin activity stats:", err)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [timeframe])

  // Transformer les données pour le graphique
  const chartData = stats
    ? Object.entries(stats.activityByType).map(([action, count]) => ({
        action,
        count,
      }))
    : []

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Statistiques d'activité admin</CardTitle>
          <CardDescription>Une erreur s'est produite</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-40 text-red-500">{error}</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Statistiques d'activité admin</CardTitle>
        <CardDescription>Aperçu des activités administratives</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="week" className="w-full" onValueChange={setTimeframe}>
          <TabsList className="mb-4">
            <TabsTrigger value="day">Aujourd'hui</TabsTrigger>
            <TabsTrigger value="week">Cette semaine</TabsTrigger>
            <TabsTrigger value="month">Ce mois</TabsTrigger>
            <TabsTrigger value="all">Tout</TabsTrigger>
          </TabsList>

          <TabsContent value={timeframe} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total des activités</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-10 w-20" />
                  ) : (
                    <div className="text-2xl font-bold">{stats?.totalActivities || 0}</div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Admins actifs</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-10 w-20" />
                  ) : (
                    <div className="text-2xl font-bold">{stats?.uniqueAdmins || 0}</div>
                  )}
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Activités par type</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {loading ? (
                  <div className="w-full h-64">
                    <Skeleton className="w-full h-full" />
                  </div>
                ) : (
                  <ChartContainer
                    config={{
                      count: {
                        label: "Nombre d'activités",
                        color: "hsl(var(--chart-1))",
                      },
                    }}
                    className="h-64"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 20 }}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="action" angle={-45} textAnchor="end" height={60} />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Bar dataKey="count" fill="var(--color-count)" name="Nombre d'activités" />
                      </BarChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

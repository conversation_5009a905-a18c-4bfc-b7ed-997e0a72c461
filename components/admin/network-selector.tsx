"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Globe, AlertCircle, Info, Server, Laptop, Cloud, Lock, AlertTriangle } from "lucide-react"

type NetworkType = "localnet" | "devnet" | "testnet" | "mainnet"

interface NetworkInfo {
  name: string
  endpoint: string
  status: "operational" | "degraded" | "maintenance"
  isLocked: boolean
  icon: React.ReactNode
  description: string
  badge: React.ReactNode
}

export default function NetworkSelector() {
  const [selectedNetwork, setSelectedNetwork] = useState<NetworkType>("devnet")
  const [customEndpoint, setCustomEndpoint] = useState("")
  const [useCustomEndpoint, setUseCustomEndpoint] = useState(false)
  const [isChangingNetwork, setIsChangingNetwork] = useState(false)
  const [confirmMainnet, setConfirmMainnet] = useState(false)
  const [showMainnetWarning, setShowMainnetWarning] = useState(false)

  const networks: Record<NetworkType, NetworkInfo> = {
    localnet: {
      name: "Localnet",
      endpoint: "http://localhost:8899",
      status: "operational",
      isLocked: false,
      icon: <Laptop className="h-5 w-5 text-purple-500" />,
      description: "Local development environment for testing",
      badge: <Badge className="bg-purple-100 text-purple-800">Local</Badge>,
    },
    devnet: {
      name: "Devnet",
      endpoint: "https://api.devnet.solana.com",
      status: "operational",
      isLocked: false,
      icon: <Server className="h-5 w-5 text-blue-500" />,
      description: "Development network with free SOL from faucets",
      badge: <Badge className="bg-blue-100 text-blue-800">Dev</Badge>,
    },
    testnet: {
      name: "Testnet",
      endpoint: "https://api.testnet.solana.com",
      status: "operational",
      isLocked: false,
      icon: <Cloud className="h-5 w-5 text-green-500" />,
      description: "Test network for pre-production validation",
      badge: <Badge className="bg-green-100 text-green-800">Test</Badge>,
    },
    mainnet: {
      name: "Mainnet",
      endpoint: "https://api.mainnet-beta.solana.com",
      status: "operational",
      isLocked: true,
      icon: <Globe className="h-5 w-5 text-red-500" />,
      description: "Production network with real value transactions",
      badge: <Badge className="bg-red-100 text-red-800">Live</Badge>,
    },
  }

  const handleNetworkChange = (network: NetworkType) => {
    if (network === "mainnet" && !confirmMainnet) {
      setShowMainnetWarning(true)
      return
    }

    setIsChangingNetwork(true)
    setSelectedNetwork(network)

    // Simulate network change
    setTimeout(() => {
      setIsChangingNetwork(false)
      if (network === "mainnet") {
        setConfirmMainnet(false)
        setShowMainnetWarning(false)
      }
    }, 2000)
  }

  const getStatusIndicator = (status: string) => {
    switch (status) {
      case "operational":
        return <div className="h-3 w-3 rounded-full bg-green-500"></div>
      case "degraded":
        return <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
      case "maintenance":
        return <div className="h-3 w-3 rounded-full bg-red-500"></div>
      default:
        return <div className="h-3 w-3 rounded-full bg-gray-500"></div>
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Globe className="mr-2 h-5 w-5" />
          Solana Network Configuration
        </CardTitle>
        <CardDescription>Select the Solana network for deployment and testing</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {showMainnetWarning && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Mainnet Warning</AlertTitle>
            <AlertDescription className="space-y-2">
              <p>
                Switching to Mainnet will use real SOL and execute transactions with actual value. Only proceed if you
                have completed all testing and audits.
              </p>
              <div className="flex justify-end space-x-2 mt-2">
                <Button variant="outline" onClick={() => setShowMainnetWarning(false)}>
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    setConfirmMainnet(true)
                    handleNetworkChange("mainnet")
                  }}
                >
                  I Understand, Proceed
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(networks).map(([key, network]) => (
            <div
              key={key}
              className={`
                border rounded-lg p-4 cursor-pointer transition-all
                ${selectedNetwork === key ? "border-primary bg-primary/5" : "border-gray-200 hover:border-gray-300"}
                ${network.isLocked && key !== selectedNetwork ? "opacity-60" : ""}
              `}
              onClick={() => !network.isLocked && handleNetworkChange(key as NetworkType)}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-center">
                  {network.icon}
                  <div className="ml-3">
                    <div className="font-medium flex items-center">
                      {network.name}
                      {network.isLocked && <Lock className="ml-1 h-3 w-3 text-gray-400" />}
                      <div className="ml-2">{network.badge}</div>
                    </div>
                    <div className="text-sm text-gray-500">{network.description}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIndicator(network.status)}
                  <span className="text-xs capitalize">{network.status}</span>
                </div>
              </div>

              {selectedNetwork === key && (
                <div className="mt-3 pt-3 border-t text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Endpoint:</span>
                    <span className="font-mono text-xs">{network.endpoint}</span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="customEndpoint">Use Custom RPC Endpoint</Label>
              <p className="text-sm text-muted-foreground">Connect to a specific RPC provider</p>
            </div>
            <Switch id="customEndpoint" checked={useCustomEndpoint} onCheckedChange={setUseCustomEndpoint} />
          </div>

          {useCustomEndpoint && (
            <div className="space-y-2">
              <Label htmlFor="endpointUrl">RPC Endpoint URL</Label>
              <div className="flex space-x-2">
                <Input
                  id="endpointUrl"
                  placeholder="https://your-rpc-provider.com"
                  value={customEndpoint}
                  onChange={(e) => setCustomEndpoint(e.target.value)}
                />
                <Button
                  variant="outline"
                  onClick={() => {
                    // Validate and set custom endpoint
                    if (customEndpoint.startsWith("http")) {
                      // Implementation would go here
                      console.log("Setting custom endpoint:", customEndpoint)
                    }
                  }}
                >
                  Apply
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Using custom endpoints may require additional authentication
              </p>
            </div>
          )}
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Current Configuration</AlertTitle>
          <AlertDescription>
            <div className="grid grid-cols-2 gap-2 text-sm mt-1">
              <div className="text-muted-foreground">Active Network:</div>
              <div className="font-medium">{networks[selectedNetwork].name}</div>

              <div className="text-muted-foreground">RPC Endpoint:</div>
              <div className="font-mono text-xs">
                {useCustomEndpoint ? customEndpoint : networks[selectedNetwork].endpoint}
              </div>

              <div className="text-muted-foreground">Status:</div>
              <div className="flex items-center">
                {getStatusIndicator(networks[selectedNetwork].status)}
                <span className="ml-2 capitalize">{networks[selectedNetwork].status}</span>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div>
          {selectedNetwork === "mainnet" && (
            <Alert className="py-2 bg-yellow-50 text-yellow-800 border-yellow-200">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <AlertTitle>Mainnet Active</AlertTitle>
              <AlertDescription>You are connected to Mainnet. All transactions will use real SOL.</AlertDescription>
            </Alert>
          )}
        </div>
        <Button disabled={isChangingNetwork} onClick={() => handleNetworkChange(selectedNetwork)}>
          {isChangingNetwork ? (
            <>
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent border-white"></div>
              Connecting...
            </>
          ) : (
            <>Reconnect</>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useWallet } from "@solana/wallet-adapter-react"
import { useNetwork } from "@/contexts/network-context"
import { isAdminWallet } from "@/lib/admin-service"
import { useToast } from "@/components/ui/use-toast"
import {
  LayoutDashboard,
  Shield,
  Coins,
  Users,
  Settings,
  Link,
  FileText,
  AlertCircle,
  Activity,
  DollarSign,
} from "lucide-react"

// Import admin components
import SuffixManagement from "./suffix-management"
import SecurityAudit from "./security-audit"
import TokenCreator from "./token-creator"
import UserManagement from "./user-management"
import PlatformSettings from "./platform-settings"
import ApiIntegrations from "./api-integrations"
import TelegramBotConfig from "./telegram-bot-config"
import FinancialReports from "./financial-reports"
import UserRoleManagement from "./user-role-management"
import EnhancedDashboardMetrics from "./enhanced-dashboard-metrics"
import UserTokenManagement from "./user-token-management"
import EcosystemTokenConfiguration from "./ecosystem-token-configuration"

export default function AdminDashboard() {
  const { publicKey } = useWallet()
  const { activeNetwork, switchNetwork } = useNetwork()
  const { toast } = useToast()

  // Admin access state
  const [isAdmin, setIsAdmin] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Dashboard state
  const [activeTab, setActiveTab] = useState("overview")
  const [activeSubTab, setActiveSubTab] = useState("general")

  // Check if the connected wallet is an admin
  useEffect(() => {
    const checkAdminAccess = async () => {
      if (publicKey) {
        try {
          const admin = await isAdminWallet(publicKey.toString())
          setIsAdmin(admin)
        } catch (error) {
          console.error("Error checking admin access:", error)
          setIsAdmin(false)
        }
      } else {
        setIsAdmin(false)
      }
      setIsLoading(false)
    }

    checkAdminAccess()
  }, [publicKey])

  // If not admin or loading, show appropriate message
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Vérification des droits d'administration...</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Accès Refusé</CardTitle>
            <CardDescription>
              Vous n'avez pas les permissions nécessaires pour accéder au tableau de bord d'administration.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
              <div>
                <h3 className="font-medium">Non autorisé</h3>
                <p className="text-sm">
                  Cette zone est réservée aux administrateurs. Veuillez vous connecter avec un portefeuille
                  administrateur pour continuer.
                </p>
              </div>
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full" onClick={() => (window.location.href = "/")}>
                Retour à l'accueil
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Network toggle
  const handleNetworkToggle = () => {
    const newNetwork =
      activeNetwork.id.includes("devnet") || activeNetwork.id.includes("testnet")
        ? activeNetwork.id.replace("devnet", "mainnet").replace("testnet", "mainnet")
        : activeNetwork.id.replace("mainnet", "devnet")

    switchNetwork(newNetwork)
    toast({
      title: "Réseau changé",
      description: `Vous êtes maintenant sur ${newNetwork.includes("mainnet") ? "Mainnet" : "Testnet/Devnet"}`,
    })
  }

  // Render the admin dashboard
  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Tableau de Bord Administrateur</h1>
        <div className="flex items-center gap-2">
          <Button
            variant={activeNetwork.id.includes("mainnet") ? "default" : "outline"}
            size="sm"
            onClick={handleNetworkToggle}
          >
            {activeNetwork.id.includes("mainnet") ? "Mainnet" : "Testnet/Devnet"}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 w-full">
          <TabsTrigger value="overview">
            <LayoutDashboard className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Vue d'ensemble</span>
            <span className="sm:hidden">Vue</span>
          </TabsTrigger>
          <TabsTrigger value="security">
            <Shield className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Sécurité</span>
            <span className="sm:hidden">Séc.</span>
          </TabsTrigger>
          <TabsTrigger value="tokens">
            <Coins className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Tokens</span>
            <span className="sm:hidden">Tokens</span>
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Utilisateurs</span>
            <span className="sm:hidden">Users</span>
          </TabsTrigger>
          <TabsTrigger value="platform">
            <Settings className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Plateforme</span>
            <span className="sm:hidden">Plat.</span>
          </TabsTrigger>
          <TabsTrigger value="integrations">
            <Link className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Intégrations</span>
            <span className="sm:hidden">Intég.</span>
          </TabsTrigger>
          <TabsTrigger value="reports">
            <FileText className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Rapports</span>
            <span className="sm:hidden">Rap.</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Users className="mr-2 h-4 w-4" />
                  Utilisateurs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,250</div>
                <p className="text-xs text-muted-foreground">Total des utilisateurs enregistrés</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Coins className="mr-2 h-4 w-4" />
                  Tokens
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">35</div>
                <p className="text-xs text-muted-foreground">Total des tokens créés</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Activity className="mr-2 h-4 w-4" />
                  Transactions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">5,420</div>
                <p className="text-xs text-muted-foreground">Total des transactions traitées</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <DollarSign className="mr-2 h-4 w-4" />
                  Revenus
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$75,000</div>
                <p className="text-xs text-muted-foreground">Total des revenus de la plateforme</p>
              </CardContent>
            </Card>
          </div>

          <EnhancedDashboardMetrics />
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Tabs defaultValue="audit" className="w-full">
            <TabsList className="grid grid-cols-2 w-full">
              <TabsTrigger value="audit">Audit de Sécurité</TabsTrigger>
              <TabsTrigger value="suffix">Gestion des Suffixes</TabsTrigger>
            </TabsList>

            <TabsContent value="audit" className="mt-4">
              <SecurityAudit />
            </TabsContent>

            <TabsContent value="suffix" className="mt-4">
              <SuffixManagement />
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* Tokens Tab */}
        <TabsContent value="tokens" className="space-y-6">
          <Tabs defaultValue="ecosystem" className="w-full">
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="ecosystem">Token Écosystème</TabsTrigger>
              <TabsTrigger value="user">Tokens Utilisateurs</TabsTrigger>
              <TabsTrigger value="create">Créer un Token</TabsTrigger>
            </TabsList>

            <TabsContent value="ecosystem" className="mt-4">
              <EcosystemTokenConfiguration />
            </TabsContent>

            <TabsContent value="user" className="mt-4">
              <UserTokenManagement />
            </TabsContent>

            <TabsContent value="create" className="mt-4">
              <TokenCreator />
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          <Tabs defaultValue="management" className="w-full">
            <TabsList className="grid grid-cols-2 w-full">
              <TabsTrigger value="management">Gestion des Utilisateurs</TabsTrigger>
              <TabsTrigger value="roles">Gestion des Rôles</TabsTrigger>
            </TabsList>

            <TabsContent value="management" className="mt-4">
              <UserManagement />
            </TabsContent>

            <TabsContent value="roles" className="mt-4">
              <UserRoleManagement />
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* Platform Tab */}
        <TabsContent value="platform" className="space-y-6">
          <PlatformSettings />
        </TabsContent>

        {/* Integrations Tab */}
        <TabsContent value="integrations" className="space-y-6">
          <Tabs defaultValue="api" className="w-full">
            <TabsList className="grid grid-cols-2 w-full">
              <TabsTrigger value="api">API Externes</TabsTrigger>
              <TabsTrigger value="telegram">Bot Telegram</TabsTrigger>
            </TabsList>

            <TabsContent value="api" className="mt-4">
              <ApiIntegrations />
            </TabsContent>

            <TabsContent value="telegram" className="mt-4">
              <TelegramBotConfig />
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports" className="space-y-6">
          <FinancialReports />
        </TabsContent>
      </Tabs>
    </div>
  )
}

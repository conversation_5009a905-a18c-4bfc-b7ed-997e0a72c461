"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowUp, ArrowDown, Users, Coins, DollarSign, BarChart3 } from "lucide-react"
import { Line, LineChart, ResponsiveContainer, XAxis, YAxis } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

export function PlatformMetrics() {
  const [isLoading, setIsLoading] = useState(true)
  const [metrics, setMetrics] = useState({
    userCount: 0,
    userGrowth: 0,
    userGrowthPercentage: 0,
    tokenCount: 0,
    tokenGrowth: 0,
    tokenGrowthPercentage: 0,
    totalEarnings: 0,
    earningsGrowth: 0,
    earningsGrowthPercentage: 0,
    transactionCount: 0,
    transactionGrowth: 0,
    transactionGrowthPercentage: 0,
    dailyActiveUsers: 0,
    weeklyActiveUsers: 0,
    monthlyActiveUsers: 0,
    averageTokenPrice: 0,
    averageTokenVolume: 0,
  })

  const [chartData, setChartData] = useState({
    users: [] as any[],
    tokens: [] as any[],
    earnings: [] as any[],
    transactions: [] as any[],
  })

  useEffect(() => {
    const fetchMetrics = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données fictives pour la démonstration
        setMetrics({
          userCount: 1250,
          userGrowth: 125,
          userGrowthPercentage: 11.2,
          tokenCount: 35,
          tokenGrowth: 5,
          tokenGrowthPercentage: 16.7,
          totalEarnings: 75000,
          earningsGrowth: 12000,
          earningsGrowthPercentage: 19.1,
          transactionCount: 5420,
          transactionGrowth: 420,
          transactionGrowthPercentage: 8.4,
          dailyActiveUsers: 320,
          weeklyActiveUsers: 875,
          monthlyActiveUsers: 1050,
          averageTokenPrice: 0.015,
          averageTokenVolume: 25000,
        })

        // Données fictives pour les graphiques
        const generateChartData = (baseValue: number, volatility: number) => {
          return Array.from({ length: 30 }, (_, i) => {
            const date = new Date()
            date.setDate(date.getDate() - (29 - i))
            const randomFactor = 1 + (Math.random() * volatility * 2 - volatility)
            const value = baseValue * randomFactor * (1 + i / 100)
            return {
              date: date.toISOString().split("T")[0],
              value: Math.round(value),
            }
          })
        }

        setChartData({
          users: generateChartData(1100, 0.05),
          tokens: generateChartData(30, 0.1),
          earnings: generateChartData(60000, 0.15),
          transactions: generateChartData(5000, 0.08),
        })
      } catch (error) {
        console.error("Erreur lors de la récupération des métriques:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchMetrics()
  }, [])

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("fr-FR").format(value)
  }

  const renderGrowthIndicator = (value: number, percentage: number) => {
    const isPositive = value >= 0
    const Icon = isPositive ? ArrowUp : ArrowDown
    const colorClass = isPositive ? "text-green-500" : "text-red-500"

    return (
      <div className={`flex items-center ${colorClass}`}>
        <Icon className="h-4 w-4 mr-1" />
        <span className="text-sm font-medium">
          {isPositive ? "+" : ""}
          {formatNumber(value)} ({percentage.toFixed(1)}%)
        </span>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Métriques de la plateforme</CardTitle>
        <CardDescription>Statistiques clés et tendances de la plateforme</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-80">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
              <TabsTrigger value="users">Utilisateurs</TabsTrigger>
              <TabsTrigger value="tokens">Tokens</TabsTrigger>
              <TabsTrigger value="financial">Financier</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard
                  title="Utilisateurs"
                  value={formatNumber(metrics.userCount)}
                  icon={<Users className="h-5 w-5" />}
                  growth={renderGrowthIndicator(metrics.userGrowth, metrics.userGrowthPercentage)}
                />
                <MetricCard
                  title="Tokens"
                  value={formatNumber(metrics.tokenCount)}
                  icon={<Coins className="h-5 w-5" />}
                  growth={renderGrowthIndicator(metrics.tokenGrowth, metrics.tokenGrowthPercentage)}
                />
                <MetricCard
                  title="Revenus"
                  value={formatCurrency(metrics.totalEarnings)}
                  icon={<DollarSign className="h-5 w-5" />}
                  growth={renderGrowthIndicator(metrics.earningsGrowth, metrics.earningsGrowthPercentage)}
                />
                <MetricCard
                  title="Transactions"
                  value={formatNumber(metrics.transactionCount)}
                  icon={<BarChart3 className="h-5 w-5" />}
                  growth={renderGrowthIndicator(metrics.transactionGrowth, metrics.transactionGrowthPercentage)}
                />
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Tendances des 30 derniers jours</h3>
                <div className="h-80">
                  <ChartContainer
                    config={{
                      users: {
                        label: "Utilisateurs",
                        color: "hsl(var(--chart-1))",
                      },
                      tokens: {
                        label: "Tokens",
                        color: "hsl(var(--chart-2))",
                      },
                      earnings: {
                        label: "Revenus",
                        color: "hsl(var(--chart-3))",
                      },
                      transactions: {
                        label: "Transactions",
                        color: "hsl(var(--chart-4))",
                      },
                    }}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={chartData.users.map((item, index) => ({
                          date: item.date,
                          users: item.value,
                          tokens: chartData.tokens[index]?.value,
                          earnings: chartData.earnings[index]?.value / 1000, // Scaled for visibility
                          transactions: chartData.transactions[index]?.value / 100, // Scaled for visibility
                        }))}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <XAxis
                          dataKey="date"
                          tickFormatter={(value) => {
                            const date = new Date(value)
                            return `${date.getDate()}/${date.getMonth() + 1}`
                          }}
                        />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Line
                          type="monotone"
                          dataKey="users"
                          stroke="var(--color-users)"
                          strokeWidth={2}
                          dot={false}
                          activeDot={{ r: 6 }}
                        />
                        <Line
                          type="monotone"
                          dataKey="tokens"
                          stroke="var(--color-tokens)"
                          strokeWidth={2}
                          dot={false}
                          activeDot={{ r: 6 }}
                        />
                        <Line
                          type="monotone"
                          dataKey="earnings"
                          stroke="var(--color-earnings)"
                          strokeWidth={2}
                          dot={false}
                          activeDot={{ r: 6 }}
                        />
                        <Line
                          type="monotone"
                          dataKey="transactions"
                          stroke="var(--color-transactions)"
                          strokeWidth={2}
                          dot={false}
                          activeDot={{ r: 6 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="users" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <MetricCard
                  title="Utilisateurs actifs (jour)"
                  value={formatNumber(metrics.dailyActiveUsers)}
                  icon={<Users className="h-5 w-5" />}
                />
                <MetricCard
                  title="Utilisateurs actifs (semaine)"
                  value={formatNumber(metrics.weeklyActiveUsers)}
                  icon={<Users className="h-5 w-5" />}
                />
                <MetricCard
                  title="Utilisateurs actifs (mois)"
                  value={formatNumber(metrics.monthlyActiveUsers)}
                  icon={<Users className="h-5 w-5" />}
                />
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Évolution des utilisateurs</h3>
                <div className="h-80">
                  <ChartContainer
                    config={{
                      users: {
                        label: "Utilisateurs",
                        color: "hsl(var(--chart-1))",
                      },
                    }}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData.users} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                        <XAxis
                          dataKey="date"
                          tickFormatter={(value) => {
                            const date = new Date(value)
                            return `${date.getDate()}/${date.getMonth() + 1}`
                          }}
                        />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Line
                          type="monotone"
                          dataKey="value"
                          name="Utilisateurs"
                          stroke="var(--color-users)"
                          strokeWidth={2}
                          dot={false}
                          activeDot={{ r: 6 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="tokens" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <MetricCard
                  title="Nombre total de tokens"
                  value={formatNumber(metrics.tokenCount)}
                  icon={<Coins className="h-5 w-5" />}
                />
                <MetricCard
                  title="Prix moyen des tokens"
                  value={`$${metrics.averageTokenPrice.toFixed(4)}`}
                  icon={<DollarSign className="h-5 w-5" />}
                />
                <MetricCard
                  title="Volume moyen"
                  value={formatNumber(metrics.averageTokenVolume)}
                  icon={<BarChart3 className="h-5 w-5" />}
                />
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Évolution des tokens</h3>
                <div className="h-80">
                  <ChartContainer
                    config={{
                      tokens: {
                        label: "Tokens",
                        color: "hsl(var(--chart-2))",
                      },
                    }}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData.tokens} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                        <XAxis
                          dataKey="date"
                          tickFormatter={(value) => {
                            const date = new Date(value)
                            return `${date.getDate()}/${date.getMonth() + 1}`
                          }}
                        />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Line
                          type="monotone"
                          dataKey="value"
                          name="Tokens"
                          stroke="var(--color-tokens)"
                          strokeWidth={2}
                          dot={false}
                          activeDot={{ r: 6 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="financial" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <MetricCard
                  title="Revenus totaux"
                  value={formatCurrency(metrics.totalEarnings)}
                  icon={<DollarSign className="h-5 w-5" />}
                />
                <MetricCard
                  title="Croissance des revenus"
                  value={`+${formatCurrency(metrics.earningsGrowth)}`}
                  icon={<ArrowUp className="h-5 w-5 text-green-500" />}
                />
                <MetricCard
                  title="Taux de croissance"
                  value={`${metrics.earningsGrowthPercentage.toFixed(1)}%`}
                  icon={<BarChart3 className="h-5 w-5" />}
                />
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Évolution des revenus</h3>
                <div className="h-80">
                  <ChartContainer
                    config={{
                      earnings: {
                        label: "Revenus",
                        color: "hsl(var(--chart-3))",
                      },
                    }}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData.earnings} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                        <XAxis
                          dataKey="date"
                          tickFormatter={(value) => {
                            const date = new Date(value)
                            return `${date.getDate()}/${date.getMonth() + 1}`
                          }}
                        />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Line
                          type="monotone"
                          dataKey="value"
                          name="Revenus"
                          stroke="var(--color-earnings)"
                          strokeWidth={2}
                          dot={false}
                          activeDot={{ r: 6 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

function MetricCard({
  title,
  value,
  icon,
  growth,
}: { title: string; value: string; icon: React.ReactNode; growth?: React.ReactNode }) {
  return (
    <div className="p-4 border rounded-lg">
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold mt-1">{value}</p>
          {growth && <div className="mt-1">{growth}</div>}
        </div>
        <div className="p-2 bg-primary/10 rounded-full">{icon}</div>
      </div>
    </div>
  )
}

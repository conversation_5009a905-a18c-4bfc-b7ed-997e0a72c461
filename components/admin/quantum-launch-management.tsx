"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import {
  Rocket,
  Clock,
  DollarSign,
  ExternalLink,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Trash2,
  Plus,
  Search,
  ArrowUpDown,
  Download,
  BarChart3,
  Settings,
} from "lucide-react"

export default function QuantumLaunchManagement() {
  const { toast } = useToast()
  const [launches, setLaunches] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("date")
  const [sortOrder, setSortOrder] = useState("desc")

  useEffect(() => {
    fetchLaunches()
  }, [activeTab, sortBy, sortOrder])

  const fetchLaunches = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Données fictives pour la démonstration
      const mockLaunches = [
        {
          id: "launch_1",
          name: "Global Finance Quantum",
          symbol: "GF",
          suffix: "QUANTUM",
          description: "The next generation of decentralized finance on Solana",
          status: "fundraising",
          current_phase_name: "Presale",
          current_phase_status: "active",
          current_phase_raised: 75,
          current_phase_target: 100,
          current_phase_percentage: 75,
          participants: 42,
          initial_price: 0.00005,
          soft_cap: 50,
          hard_cap: 200,
          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "8ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
        {
          id: "launch_2",
          name: "Solana Meme Coin",
          symbol: "SMEME",
          suffix: "SOL",
          description: "The most popular meme coin on Solana",
          status: "launched",
          current_phase_name: "Listed",
          current_phase_status: "completed",
          current_phase_raised: 150,
          current_phase_target: 150,
          current_phase_percentage: 100,
          participants: 87,
          initial_price: 0.0001,
          soft_cap: 75,
          hard_cap: 150,
          created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "5ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
        {
          id: "launch_3",
          name: "AI Token",
          symbol: "AI",
          suffix: "QUANTUM",
          description: "Artificial Intelligence powered token on Solana",
          status: "setup",
          current_phase_name: "Setup",
          current_phase_status: "pending",
          current_phase_raised: 0,
          current_phase_target: 100,
          current_phase_percentage: 0,
          participants: 0,
          initial_price: 0.0002,
          soft_cap: 50,
          hard_cap: 100,
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "8ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
        {
          id: "launch_4",
          name: "Metaverse Token",
          symbol: "META",
          suffix: "SOL",
          description: "The future of metaverse on Solana",
          status: "preparing",
          current_phase_name: "Preparing for launch",
          current_phase_status: "active",
          current_phase_raised: 120,
          current_phase_target: 120,
          current_phase_percentage: 100,
          participants: 65,
          initial_price: 0.00015,
          soft_cap: 60,
          hard_cap: 120,
          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "7ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
        {
          id: "launch_5",
          name: "DeFi Protocol",
          symbol: "DEFI",
          suffix: "QUANTUM",
          description: "Decentralized finance protocol on Solana",
          status: "failed",
          current_phase_name: "Presale",
          current_phase_status: "failed",
          current_phase_raised: 20,
          current_phase_target: 80,
          current_phase_percentage: 25,
          participants: 12,
          initial_price: 0.0003,
          soft_cap: 80,
          hard_cap: 160,
          created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "6ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
      ]

      // Filtrer par statut si nécessaire
      let filteredLaunches = mockLaunches
      if (activeTab !== "all") {
        filteredLaunches = mockLaunches.filter((launch) => launch.status === activeTab)
      }

      // Filtrer par recherche
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        filteredLaunches = filteredLaunches.filter(
          (launch) =>
            launch.name.toLowerCase().includes(query) ||
            launch.symbol.toLowerCase().includes(query) ||
            launch.description.toLowerCase().includes(query),
        )
      }

      // Trier les lancements
      filteredLaunches.sort((a, b) => {
        let comparison = 0
        if (sortBy === "date") {
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        } else if (sortBy === "name") {
          comparison = a.name.localeCompare(b.name)
        } else if (sortBy === "raised") {
          comparison = a.current_phase_raised - b.current_phase_raised
        } else if (sortBy === "participants") {
          comparison = a.participants - b.participants
        }

        return sortOrder === "asc" ? comparison : -comparison
      })

      setLaunches(filteredLaunches)
    } catch (error) {
      console.error("Error fetching launches:", error)
      toast({
        title: "Error",
        description: "Failed to fetch launches. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchLaunches()
  }

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc")
  }

  const handleDeleteLaunch = async (id: string) => {
    if (!confirm("Are you sure you want to delete this launch? This action cannot be undone.")) {
      return
    }

    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Supprimer le lancement de la liste
      setLaunches(launches.filter((launch) => launch.id !== id))

      toast({
        title: "Launch deleted",
        description: "The launch has been successfully deleted.",
      })
    } catch (error) {
      console.error("Error deleting launch:", error)
      toast({
        title: "Error",
        description: "Failed to delete launch. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleUpdateLaunchStatus = async (id: string, newStatus: string) => {
    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Mettre à jour le statut du lancement
      setLaunches(
        launches.map((launch) => {
          if (launch.id === id) {
            return { ...launch, status: newStatus }
          }
          return launch
        }),
      )

      toast({
        title: "Status updated",
        description: `Launch status has been updated to ${newStatus}.`,
      })
    } catch (error) {
      console.error("Error updating launch status:", error)
      toast({
        title: "Error",
        description: "Failed to update launch status. Please try again.",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "setup":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            Upcoming
          </Badge>
        )
      case "fundraising":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <DollarSign className="mr-1 h-3 w-3" />
            Active
          </Badge>
        )
      case "preparing":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Preparing
          </Badge>
        )
      case "launched":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <Rocket className="mr-1 h-3 w-3" />
            Launched
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Quantum Launch Management</h2>
          <p className="text-muted-foreground">Manage and monitor all token launches on the Quantum platform.</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/admin/quantum-tokens/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Launch
            </Link>
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search launches..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </form>
        <div className="flex gap-2">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date">Date Created</SelectItem>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="raised">Amount Raised</SelectItem>
              <SelectItem value="participants">Participants</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" onClick={toggleSortOrder}>
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-6">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="setup">Upcoming</TabsTrigger>
          <TabsTrigger value="fundraising">Active</TabsTrigger>
          <TabsTrigger value="preparing">Preparing</TabsTrigger>
          <TabsTrigger value="launched">Launched</TabsTrigger>
          <TabsTrigger value="failed">Failed</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={`skeleton-${index}`}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="space-y-2">
                          <Skeleton className="h-5 w-40" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
                        <Skeleton className="h-9 w-24" />
                        <Skeleton className="h-9 w-24" />
                        <Skeleton className="h-9 w-24" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : launches.length > 0 ? (
            <div className="space-y-4">
              {launches.map((launch) => (
                <Card key={launch.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold">
                          {launch.symbol.substring(0, 2)}
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">{launch.name}</h3>
                            {getStatusBadge(launch.status)}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {launch.symbol}
                            {launch.suffix} • Created {new Date(launch.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/admin/quantum-tokens/${launch.id}`}>
                            <Settings className="mr-1 h-4 w-4" />
                            Manage
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/token-quantum/launch/${launch.id}`}>
                            <ExternalLink className="mr-1 h-4 w-4" />
                            View
                          </Link>
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteLaunch(launch.id)}>
                          <Trash2 className="mr-1 h-4 w-4" />
                          Delete
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                      <div>
                        <p className="text-xs text-muted-foreground">Current Phase</p>
                        <p className="font-medium">{launch.current_phase_name}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Raised</p>
                        <p className="font-medium">
                          {launch.current_phase_raised} / {launch.current_phase_target} SOL
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Participants</p>
                        <p className="font-medium">{launch.participants}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Initial Price</p>
                        <p className="font-medium">${launch.initial_price}</p>
                      </div>
                    </div>

                    {launch.status !== "failed" && launch.status !== "launched" && (
                      <div className="mt-4">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{launch.current_phase_percentage}%</span>
                        </div>
                        <Progress value={launch.current_phase_percentage} className="h-2 mt-1" />
                      </div>
                    )}

                    {launch.status === "setup" && (
                      <div className="mt-4 flex justify-end">
                        <Button size="sm" onClick={() => handleUpdateLaunchStatus(launch.id, "fundraising")}>
                          <Rocket className="mr-1 h-4 w-4" />
                          Start Fundraising
                        </Button>
                      </div>
                    )}

                    {launch.status === "fundraising" && (
                      <div className="mt-4 flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUpdateLaunchStatus(launch.id, "failed")}
                        >
                          <XCircle className="mr-1 h-4 w-4" />
                          Cancel
                        </Button>
                        <Button size="sm" onClick={() => handleUpdateLaunchStatus(launch.id, "preparing")}>
                          <CheckCircle2 className="mr-1 h-4 w-4" />
                          Complete Fundraising
                        </Button>
                      </div>
                    )}

                    {launch.status === "preparing" && (
                      <div className="mt-4 flex justify-end">
                        <Button size="sm" onClick={() => handleUpdateLaunchStatus(launch.id, "launched")}>
                          <Rocket className="mr-1 h-4 w-4" />
                          Launch Token
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 bg-muted/20 rounded-lg">
              <Rocket className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-1">No launches found</h3>
              <p className="text-muted-foreground text-center mb-4">
                {activeTab === "all"
                  ? "There are no token launches available at the moment."
                  : `There are no ${activeTab} token launches available.`}
              </p>
              <Button asChild>
                <Link href="/admin/quantum-tokens/create">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Launch
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Launch Statistics</CardTitle>
          <CardDescription>Overview of all token launches on the platform</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="space-y-1">
              <p className="text-3xl font-bold">{launches.length}</p>
              <p className="text-sm text-muted-foreground">Total Launches</p>
            </div>
            <div className="space-y-1">
              <p className="text-3xl font-bold">{launches.filter((l) => l.status === "fundraising").length}</p>
              <p className="text-sm text-muted-foreground">Active Fundraising</p>
            </div>
            <div className="space-y-1">
              <p className="text-3xl font-bold">{launches.filter((l) => l.status === "launched").length}</p>
              <p className="text-sm text-muted-foreground">Successfully Launched</p>
            </div>
            <div className="space-y-1">
              <p className="text-3xl font-bold">{launches.reduce((total, launch) => total + launch.participants, 0)}</p>
              <p className="text-sm text-muted-foreground">Total Participants</p>
            </div>
          </div>

          <Separator className="my-6" />

          <div className="flex justify-between">
            <Button variant="outline" asChild>
              <Link href="/admin/quantum-dashboard">
                <BarChart3 className="mr-2 h-4 w-4" />
                View Detailed Analytics
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/admin/quantum-settings">
                <Settings className="mr-2 h-4 w-4" />
                Platform Settings
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

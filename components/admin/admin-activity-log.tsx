"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"

interface ActivityLog {
  id: string
  adminWallet: string
  action: string
  details: string
  timestamp: string
  status: "success" | "warning" | "error"
}

export function AdminActivityLog() {
  const [logs, setLogs] = useState<ActivityLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    const fetchActivityLogs = async () => {
      try {
        // Simuler un chargement de données
        setTimeout(() => {
          setLogs([
            {
              id: "1",
              adminWallet: "GfEHHkrQY45ZPoKC9srbjKZiCnwEWNVTAn3UxTGTZgwT",
              action: "Login",
              details: "Admin login from IP ***********",
              timestamp: "2023-06-15 14:32:45",
              status: "success",
            },
            {
              id: "2",
              adminWallet: "BXvEuaBKzLJtZQxwKWgmUEJWAM9JYiVEYBT8HPZjmXbU",
              action: "Token Creation",
              details: "Created new token SOLDEV",
              timestamp: "2023-06-15 13:22:18",
              status: "success",
            },
            {
              id: "3",
              adminWallet: "GfEHHkrQY45ZPoKC9srbjKZiCnwEWNVTAn3UxTGTZgwT",
              action: "Settings Update",
              details: "Updated platform fee settings",
              timestamp: "2023-06-15 11:45:32",
              status: "success",
            },
            {
              id: "4",
              adminWallet: "BXvEuaBKzLJtZQxwKWgmUEJWAM9JYiVEYBT8HPZjmXbU",
              action: "User Ban",
              details: "Banned user wallet 8JkYnPow...",
              timestamp: "2023-06-14 16:12:05",
              status: "warning",
            },
            {
              id: "5",
              adminWallet: "GfEHHkrQY45ZPoKC9srbjKZiCnwEWNVTAn3UxTGTZgwT",
              action: "Token Deletion",
              details: "Failed to delete token SCAM",
              timestamp: "2023-06-14 10:08:59",
              status: "error",
            },
          ])
          setIsLoading(false)
        }, 1000)
      } catch (error) {
        console.error("Error fetching activity logs:", error)
        toast({
          title: "Error",
          description: "Failed to load activity logs. Please try again.",
          variant: "destructive",
        })
        setIsLoading(false)
      }
    }

    fetchActivityLogs()
  }, [toast])

  const getStatusBadge = (status: "success" | "warning" | "error") => {
    switch (status) {
      case "success":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            Success
          </Badge>
        )
      case "warning":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            Warning
          </Badge>
        )
      case "error":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Error
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Admin Activity Log</CardTitle>
        <CardDescription>Recent administrative actions on the platform</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Admin</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Details</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell className="font-mono text-xs">{`${log.adminWallet.substring(0, 6)}...${log.adminWallet.substring(log.adminWallet.length - 4)}`}</TableCell>
                  <TableCell>{log.action}</TableCell>
                  <TableCell>{log.details}</TableCell>
                  <TableCell>{log.timestamp}</TableCell>
                  <TableCell>{getStatusBadge(log.status)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

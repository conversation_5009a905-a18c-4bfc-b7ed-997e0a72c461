"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { addDays, format, isSameDay, startOfToday } from "date-fns"
import { fr } from "date-fns/locale"

interface PresaleEvent {
  id: string
  title: string
  date: Date
  type: "start" | "end"
  status: "upcoming" | "active" | "ended" | "successful" | "failed"
}

export function PresaleCalendar() {
  const [isLoading, setIsLoading] = useState(true)
  const [events, setEvents] = useState<PresaleEvent[]>([])
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(startOfToday())
  const [selectedEvents, setSelectedEvents] = useState<PresaleEvent[]>([])

  useEffect(() => {
    const fetchEvents = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données fictives pour la démonstration
        const today = startOfToday()
        const mockEvents: PresaleEvent[] = [
          {
            id: "event1",
            title: "Solana Platform Token",
            date: today,
            type: "start",
            status: "active",
          },
          {
            id: "event2",
            title: "Meme Coin",
            date: addDays(today, 3),
            type: "start",
            status: "upcoming",
          },
          {
            id: "event3",
            title: "Solana Platform Token",
            date: addDays(today, 14),
            type: "end",
            status: "active",
          },
          {
            id: "event4",
            title: "Utility Token",
            date: addDays(today, -2),
            type: "end",
            status: "successful",
          },
          {
            id: "event5",
            title: "Meme Coin",
            date: addDays(today, 17),
            type: "end",
            status: "upcoming",
          },
          {
            id: "event6",
            title: "New DeFi Token",
            date: addDays(today, 7),
            type: "start",
            status: "upcoming",
          },
          {
            id: "event7",
            title: "New DeFi Token",
            date: addDays(today, 21),
            type: "end",
            status: "upcoming",
          },
        ]

        setEvents(mockEvents)

        // Sélectionner les événements pour la date actuelle
        if (selectedDate) {
          setSelectedEvents(mockEvents.filter((event) => isSameDay(event.date, selectedDate)))
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des événements:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchEvents()
  }, [selectedDate])

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date)
    if (date) {
      setSelectedEvents(events.filter((event) => isSameDay(event.date, date)))
    } else {
      setSelectedEvents([])
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "upcoming":
        return <Badge className="bg-blue-500">À venir</Badge>
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "ended":
        return <Badge className="bg-orange-500">Terminée</Badge>
      case "successful":
        return <Badge className="bg-green-700">Réussie</Badge>
      case "failed":
        return <Badge className="bg-red-500">Échouée</Badge>
      default:
        return <Badge className="bg-gray-500">{status}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "start":
        return (
          <Badge variant="outline" className="border-blue-500 text-blue-500">
            Début
          </Badge>
        )
      case "end":
        return (
          <Badge variant="outline" className="border-orange-500 text-orange-500">
            Fin
          </Badge>
        )
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  // Fonction pour déterminer les jours avec des événements
  const getDayClassNames = (date: Date) => {
    const hasEvents = events.some((event) => isSameDay(event.date, date))
    return hasEvents ? "bg-primary/10 rounded-full font-bold" : ""
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Calendrier des Presales</CardTitle>
        <CardDescription>Planification des débuts et fins de presales</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-80">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                locale={fr}
                className="rounded-md border"
                modifiersClassNames={{
                  selected: "bg-primary text-primary-foreground",
                }}
                modifiers={{
                  event: (date) => events.some((event) => isSameDay(event.date, date)),
                }}
                modifiersStyles={{
                  event: { fontWeight: "bold", backgroundColor: "rgba(var(--primary), 0.1)" },
                }}
              />
            </div>
            <div>
              <h3 className="font-medium mb-2">
                {selectedDate ? format(selectedDate, "d MMMM yyyy", { locale: fr }) : "Sélectionnez une date"}
              </h3>
              {selectedEvents.length === 0 ? (
                <p className="text-muted-foreground text-sm">Aucun événement pour cette date</p>
              ) : (
                <div className="space-y-3">
                  {selectedEvents.map((event) => (
                    <div key={event.id} className="p-3 border rounded-md">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{event.title}</h4>
                          <div className="flex space-x-2 mt-1">
                            {getTypeBadge(event.type)}
                            {getStatusBadge(event.status)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

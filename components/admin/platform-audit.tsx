"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertCircle,
  CheckCircle,
  XCircle,
  Shield,
  RefreshCw,
  Download,
  FileText,
  Terminal,
  Database,
} from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface AuditResult {
  category: string
  name: string
  status: "success" | "warning" | "error" | "info"
  message: string
  details?: string
  timestamp: string
}

interface SecurityMetric {
  name: string
  score: number
  maxScore: number
  status: "good" | "medium" | "critical"
}

export function PlatformAudit() {
  const [isAuditing, setIsAuditing] = useState(false)
  const [activeTab, setActiveTab] = useState('security')
  const [auditProgress, setAuditProgress] = useState(0)
  const [auditCompleted, setAuditCompleted] = useState(false)
  
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetric[]>([
    { name: 'Protection contre les injections SQL', score: 95, maxScore: 100, status: 'good' },
    { name: 'Validation des entrées utilisateur', score: 90, maxScore: 100, status: 'good' },
    { name: 'Sécurité des variables d\'environnement', score: 75, maxScore: 100, status: 'medium' },
    { name: 'Protection CSRF', score: 100, maxScore: 100, status: 'good' },
    { name: 'Sécurité des API', score: 85, maxScore: 100, status: 'good' },
    { name: 'Gestion des autorisations', score: 70, maxScore: 100, status: 'medium' },
    { name: 'Sécurité des transactions blockchain', score: 95, maxScore: 100, status: 'good' },
    { name: 'Protection contre les attaques par force brute', score: 60, maxScore: 100, status: 'critical' },
  ])
  
  const [auditResults, setAuditResults] = useState<AuditResult[]>([
    {
      category: 'security',
      name: 'Validation des entrées',
      status: 'success',
      message: 'Toutes les entrées utilisateur sont correctement validées',
      timestamp: '2023-04-28 14:32:15'
    },
    {
      category: 'security',
      name: 'Protection CSRF',
      status: 'success',
      message: 'Protection CSRF active sur tous les formulaires',
      timestamp: '2023-04-28 14:32:18'
    },
    {
      category: 'security',
      name: 'Variables d\'environnement',
      status: 'warning',
      message: 'Certaines variables d\'environnement sensibles ne sont pas chiffrées',
      details: 'Les variables MARKET_API_KEY et ADMIN_WALLET devraient être stockées de manière plus sécurisée',
      timestamp: '2023-04-28 14:32:22'
    },
    {
      category: 'security',
      name: 'Protection contre les attaques par force brute',
      status: 'error',
      message: 'Aucune limitation de tentatives de connexion détectée',
      details: 'Implémentez un système de limitation de tentatives pour protéger contre les attaques par force brute',
      timestamp: '2023-04-28 14:32:25'
    },
    {
      category: 'performance',
      name: 'Temps de réponse API',
      status: 'success',
      message: 'Temps de réponse moyen: 120ms',
      timestamp: '2023-04-28 14:32:30'
    },
    {
      category: 'performance',
      name: 'Optimisation des requêtes blockchain',
      status: 'warning',
      message: 'Certaines requêtes blockchain sont inefficaces',
      details: 'Les requêtes dans token-service.ts pourraient être optimisées pour réduire la latence',
      timestamp: '2023-04-28 14:32:35'
    },
    {
      category: 'code',
      name: 'Qualité du code',
      status: 'success',
      message: 'Le code respecte les standards de qualité',
      timestamp: '2023-04-28 14:32:40'
    },
    {
      category: 'code',
      name: 'Tests unitaires',
      status: 'warning',
      message: 'Couverture de tests insuffisante',
      details: 'La couverture de tests est de 65%, l\'objectif recommandé est de 80%',
      timestamp: '2023-04-28 14:32:45'
    },
    {
      category: 'blockchain',
      name: 'Validation des transactions',
      status: 'success',
      message: 'Toutes les transactions sont correctement validées',
      timestamp: '2023-04-28 14:32:50'
    },
    {
      category: 'blockchain',
      name: 'Gestion des erreurs blockchain',
      status: 'warning',
      message: 'Certaines erreurs blockchain ne sont pas correctement gérées',
      details: 'Améliorez la gestion des erreurs dans solana-service.ts et bnb-chain-service.ts',
      timestamp: '2023-04-28 14:32:55'
    }
  ])

  const startAudit = async () => {
    setIsAuditing(true)
    setAuditProgress(0)
    setAuditCompleted(false)
    
    // Simuler un audit progressif
    for (let i = 1; i <= 10; i++) {
      await new Promise(resolve => setTimeout(resolve, 500))
      setAuditProgress(i * 10)
    }
    
    // Mettre à jour les résultats avec un horodatage actuel
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19)
    setAuditResults(auditResults.map(result => ({
      ...result,
      timestamp: now
    })))
    
    setIsAuditing(false)
    setAuditCompleted(true)
    
    toast({
      title: "Audit terminé",
      description: "L'audit de la plateforme a été complété avec succès",
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-amber-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'info':
        return <AlertCircle className="h-5 w-5 text-blue-500" />
      default:
        return <AlertCircle className="h-5 w-5" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Succès</Badge>
      case 'warning':
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Avertissement</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Erreur</Badge>
      case 'info':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Info</Badge>
      default:
        return <Badge>Inconnu</Badge>
    }
  }

  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'bg-green-500'
      case 'medium':
        return 'bg-amber-500'
      case 'critical':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const filteredResults = auditResults.filter(result => 
    activeTab === 'all' || result.category === activeTab
  )

  const calculateOverallSecurityScore = () => {
    const totalScore = securityMetrics.reduce((acc, metric) => acc + metric.score, 0)
    const maxPossibleScore = securityMetrics.reduce((acc, metric) => acc + metric.maxScore, 0)
    return Math.round((totalScore / maxPossibleScore) * 100)
  }

  const securityScore = calculateOverallSecurityScore()
  
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-500'
    if (score >= 70) return 'text-amber-500'
    return 'text-red-500'
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Audit de la Plateforme</h2>
          <p className="text-muted-foreground">
            Analysez la sécurité, les performances et la qualité du code de votre plateforme
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            onClick={startAudit}
            disabled={isAuditing}
            className="flex items-center gap-2"
          >
            {isAuditing ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Audit en cours...
              </>
            ) : (
              <>
                <Shield className="h-4 w-4" />
                Lancer un audit complet
              </>
            )}
          </Button>
          {auditCompleted && (
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Exporter le rapport
            </Button>
          )}
        </div>
      </div>

      {isAuditing && (
        <Card>
          <CardHeader>
            <CardTitle>Audit en cours</CardTitle>
            <CardDescription>Veuillez patienter pendant l'analyse de la plateforme</CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={auditProgress} className="h-2" />
            <p className="mt-2 text-sm text-center">{auditProgress}% complété</p>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Score de sécurité global</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center">
              <div className="relative w-40 h-40 flex items-center justify-center">
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="#e2e8f0"
                    strokeWidth="10"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke={securityScore >= 90 ? "#22c55e" : securityScore >= 70 ? "#f59e0b" : "#ef4444"}
                    strokeWidth="10"
                    strokeDasharray={`${securityScore * 2.83} ${283 - securityScore * 2.83}`}
                    strokeDashoffset="70.75"
                  />
                </svg>
                <div className="absolute flex flex-col items-center justify-center">
                  <span className={`text-4xl font-bold ${getScoreColor(securityScore)}`}>
                    {securityScore}
                  </span>
                  <span className="text-xs text-muted-foreground">sur 100</span>
                </div>
              </div>
            </div>
            <div className="mt-4 text-center">
              <p className="text-sm text-muted-foreground">
                Dernière mise à jour: {auditResults[0]?.timestamp || 'Jamais'}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Problèmes détectés</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-3xl font-bold text-green-500">
                  {auditResults.filter(r => r.status === 'success').length}
                </div>
                <div className="text-xs text-muted-foreground mt-1">Réussis</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-amber-500">
                  {auditResults.filter(r => r.status === 'warning').length}
                </div>
                <div className="text-xs text-muted-foreground mt-1">Avertissements</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-red-500">
                  {auditResults.filter(r => r.status === 'error').length}
                </div>
                <div className="text-xs text-muted-foreground mt-1">Erreurs</div>
              </div>
            </div>
            <Separator className="my-4" />
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Sécurité</span>
                <span className="text-sm font-medium">
                  {auditResults.filter(r => r.category === 'security' && r.status !== 'success').length} problèmes
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Performance</span>
                <span className="text-sm font-medium">
                  {auditResults.filter(r => r.category === 'performance' && r.status !== 'success').length} problèmes
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Code</span>
                <span className="text-sm font-medium">
                  {auditResults.filter(r => r.category === 'code' && r.status !== 'success').length} problèmes
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Blockchain</span>
                <span className="text-sm font-medium">
                  {auditResults.filter(r => r.category === 'blockchain' && r.status !== 'success').length} problèmes
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Recommandations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0">
                <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Critique</Badge>
              </div>
              <p className="text-sm">Implémenter une protection contre les attaques par force brute</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0">
                <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Important</Badge>
              </div>
              <p className="text-sm">Améliorer la sécurité des variables d'environnement sensibles</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0">
                <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Important</Badge>
              </div>
              <p className="text-sm">Optimiser les requêtes blockchain pour améliorer les performances</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0">
                <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Important</Badge>
              </div>
              <p className="text-sm">Augmenter la couverture des tests unitaires à au moins 80%</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Tous les résultats
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Sécurité
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="code" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Code
          </TabsTrigger>
          <TabsTrigger value="blockchain" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Blockchain
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tous les résultats d'audit</CardTitle>
              <CardDescription>Vue complète de tous les problèmes détectés</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Catégorie</TableHead>
                    <TableHead>Nom</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Horodatage</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredResults.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell className="capitalize">{result.category}</TableCell>
                      <TableCell>{result.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(result.status)}
                          {getStatusBadge(result.status)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p>{result.message}</p>
                          {result.details && (
                            <p className="text-xs text-muted-foreground mt-1">{result.details}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-xs">{result.timestamp}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Métriques de Sécurité</CardTitle>
              <CardDescription>Évaluation détaillée des mesures de sécurité</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {securityMetrics.map((metric) => (
                  <div key={metric.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getMetricStatusColor(metric.status)}`}></div>
                        <span className="font-medium">{metric.name}</span>
                      </div>
                      <span className="font-bold">{metric.score}/{metric.maxScore}</span>
                    </div>
                    <Progress value={(metric.score / metric.maxScore) * 100} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>
                        {metric.status === 'good' ? 'Bon' : metric.status === 'medium' ? 'À améliorer' : 'Critique'}
                      </span>
                      <span>
                        {Math.round((metric.score / metric.maxScore) * 100)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recommandations de Sécurité</CardTitle>
            </CardHeader>
            <CardContent>
              Liste des recommandations de sécurité
            </CardContent>
          </Card>\

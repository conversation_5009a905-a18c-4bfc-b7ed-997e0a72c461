"use client"

import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Edit, RefreshCw, Trash2, Plus, AlertCircle } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON>Des<PERSON>, AlertTitle } from "@/components/ui/alert"

// Types
interface Network {
  id: string
  name: string
  rpcUrl: string
  chainId: string
  symbol: string
  explorerUrl: string
  isActive: boolean
  isTestnet: boolean
}

export function NetworkManagement() {
  const [networks, setNetworks] = useState<Network[]>([
    {
      id: "solana-mainnet",
      name: "Solana Mainnet",
      rpcUrl: "https://api.mainnet-beta.solana.com",
      chainId: "mainnet-beta",
      symbol: "SOL",
      explorerUrl: "https://explorer.solana.com",
      isActive: true,
      isTestnet: false,
    },
    {
      id: "solana-devnet",
      name: "Solana Devnet",
      rpcUrl: "https://api.devnet.solana.com",
      chainId: "devnet",
      symbol: "SOL",
      explorerUrl: "https://explorer.solana.com/?cluster=devnet",
      isActive: true,
      isTestnet: true,
    },
    {
      id: "bnb-mainnet",
      name: "BNB Smart Chain",
      rpcUrl: "https://bsc-dataseed.binance.org",
      chainId: "56",
      symbol: "BNB",
      explorerUrl: "https://bscscan.com",
      isActive: true,
      isTestnet: false,
    },
    {
      id: "bnb-testnet",
      name: "BNB Smart Chain Testnet",
      rpcUrl: "https://data-seed-prebsc-1-s1.binance.org:8545",
      chainId: "97",
      symbol: "tBNB",
      explorerUrl: "https://testnet.bscscan.com",
      isActive: true,
      isTestnet: true,
    },
  ])
  const [isLoading, setIsLoading] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [selectedNetwork, setSelectedNetwork] = useState<Network | null>(null)
  const [newNetwork, setNewNetwork] = useState({
    name: "",
    rpcUrl: "",
    chainId: "",
    symbol: "",
    explorerUrl: "",
    isActive: true,
    isTestnet: false,
  })
  const [editNetwork, setEditNetwork] = useState({
    id: "",
    name: "",
    rpcUrl: "",
    chainId: "",
    symbol: "",
    explorerUrl: "",
    isActive: true,
    isTestnet: false,
  })
  const [activeTab, setActiveTab] = useState("networks")
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [networkToDelete, setNetworkToDelete] = useState<Network | null>(null)
  const [testResults, setTestResults] = useState<
    Record<string, { success: boolean; latency: number | null; error?: string }>
  >({})
  const { toast } = useToast()

  const testNetworkConnection = async (network: Network) => {
    setTestResults((prev) => ({
      ...prev,
      [network.id]: { success: false, latency: null, error: "Test en cours..." },
    }))

    try {
      const startTime = Date.now()

      // Simuler un appel réseau
      await new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 1000))

      const endTime = Date.now()
      const latency = endTime - startTime

      // Simuler un succès ou un échec aléatoire pour la démonstration
      const success = Math.random() > 0.2

      if (!success) {
        throw new Error("Échec de la connexion au réseau")
      }

      setTestResults((prev) => ({
        ...prev,
        [network.id]: { success: true, latency },
      }))

      toast({
        title: "Test réussi",
        description: `Connexion à ${network.name} établie en ${latency}ms`,
      })
    } catch (error) {
      console.error(`Error testing network ${network.name}:`, error)
      setTestResults((prev) => ({
        ...prev,
        [network.id]: {
          success: false,
          latency: null,
          error: error instanceof Error ? error.message : "Erreur de connexion",
        },
      }))

      toast({
        title: "Échec du test",
        description: `Impossible de se connecter à ${network.name}`,
        variant: "destructive",
      })
    }
  }

  const handleCreateNetwork = () => {
    if (!newNetwork.name || !newNetwork.rpcUrl || !newNetwork.chainId || !newNetwork.symbol) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      })
      return
    }

    const id = newNetwork.name.toLowerCase().replace(/\s+/g, "-")
    const createdNetwork = {
      ...newNetwork,
      id,
    }

    setNetworks([...networks, createdNetwork])
    setNewNetwork({
      name: "",
      rpcUrl: "",
      chainId: "",
      symbol: "",
      explorerUrl: "",
      isActive: true,
      isTestnet: false,
    })
    setActiveTab("networks")

    toast({
      title: "Réseau ajouté",
      description: `Le réseau ${createdNetwork.name} a été ajouté avec succès`,
    })
  }

  const handleUpdateNetwork = () => {
    if (!editNetwork.id || !editNetwork.name || !editNetwork.rpcUrl || !editNetwork.chainId || !editNetwork.symbol) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      })
      return
    }

    setNetworks(networks.map((network) => (network.id === editNetwork.id ? editNetwork : network)))
    setSelectedNetwork(null)

    toast({
      title: "Réseau mis à jour",
      description: `Le réseau ${editNetwork.name} a été mis à jour avec succès`,
    })
  }

  const handleDeleteNetwork = () => {
    if (!networkToDelete) return

    setNetworks(networks.filter((network) => network.id !== networkToDelete.id))
    setShowDeleteDialog(false)
    setNetworkToDelete(null)

    toast({
      title: "Réseau supprimé",
      description: `Le réseau ${networkToDelete.name} a été supprimé avec succès`,
    })
  }

  const confirmDeleteNetwork = (network: Network) => {
    setNetworkToDelete(network)
    setShowDeleteDialog(true)
  }

  const handleEditNetwork = (network: Network) => {
    setSelectedNetwork(network)
    setEditNetwork({ ...network })
  }

  const toggleNetworkStatus = (networkId: string, isActive: boolean) => {
    setNetworks(networks.map((network) => (network.id === networkId ? { ...network, isActive } : network)))

    const network = networks.find((n) => n.id === networkId)
    if (network) {
      toast({
        title: isActive ? "Réseau activé" : "Réseau désactivé",
        description: `Le réseau ${network.name} a été ${isActive ? "activé" : "désactivé"}`,
      })
    }
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="networks" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="networks">Réseaux</TabsTrigger>
          <TabsTrigger value="add">Ajouter un réseau</TabsTrigger>
        </TabsList>

        <TabsContent value="networks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Réseaux blockchain</CardTitle>
              <CardDescription>Gérez les réseaux blockchain supportés par la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <Skeleton className="h-12 w-full" />
                    </div>
                  ))}
                </div>
              ) : networks.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">Aucun réseau configuré</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Réseau</TableHead>
                      <TableHead>RPC URL</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Test</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {networks.map((network) => (
                      <TableRow key={network.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium flex items-center">
                              {network.name}
                              {network.isTestnet && (
                                <Badge variant="outline" className="ml-2">
                                  Testnet
                                </Badge>
                              )}
                            </div>
                            <div className="text-xs text-muted-foreground">{network.symbol}</div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-xs truncate max-w-[200px]">{network.rpcUrl}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={network.isActive}
                              onCheckedChange={(checked) => toggleNetworkStatus(network.id, checked)}
                            />
                            <span className={network.isActive ? "text-green-600" : "text-muted-foreground"}>
                              {network.isActive ? "Actif" : "Inactif"}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => testNetworkConnection(network)}
                              disabled={testResults[network.id]?.error === "Test en cours..."}
                            >
                              <RefreshCw className="h-3.5 w-3.5 mr-1" />
                              Tester
                            </Button>
                            {testResults[network.id] && (
                              <span
                                className={
                                  testResults[network.id].success ? "text-green-600 text-xs" : "text-red-600 text-xs"
                                }
                              >
                                {testResults[network.id].success
                                  ? `${testResults[network.id].latency}ms`
                                  : testResults[network.id].error}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="icon" onClick={() => handleEditNetwork(network)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => confirmDeleteNetwork(network)}
                              disabled={["solana-mainnet", "solana-devnet", "bnb-mainnet", "bnb-testnet"].includes(
                                network.id,
                              )}
                              title={
                                ["solana-mainnet", "solana-devnet", "bnb-mainnet", "bnb-testnet"].includes(network.id)
                                  ? "Les réseaux par défaut ne peuvent pas être supprimés"
                                  : "Supprimer le réseau"
                              }
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {selectedNetwork && (
            <Card>
              <CardHeader>
                <CardTitle>Modifier le réseau: {selectedNetwork.name}</CardTitle>
                <CardDescription>Modifiez les détails du réseau blockchain</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="edit-name">Nom du réseau</Label>
                      <Input
                        id="edit-name"
                        value={editNetwork.name}
                        onChange={(e) => setEditNetwork({ ...editNetwork, name: e.target.value })}
                        disabled={isUpdating}
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-symbol">Symbole</Label>
                      <Input
                        id="edit-symbol"
                        value={editNetwork.symbol}
                        onChange={(e) => setEditNetwork({ ...editNetwork, symbol: e.target.value })}
                        disabled={isUpdating}
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-rpc">URL RPC</Label>
                      <Input
                        id="edit-rpc"
                        value={editNetwork.rpcUrl}
                        onChange={(e) => setEditNetwork({ ...editNetwork, rpcUrl: e.target.value })}
                        disabled={isUpdating}
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-chain-id">Chain ID</Label>
                      <Input
                        id="edit-chain-id"
                        value={editNetwork.chainId}
                        onChange={(e) => setEditNetwork({ ...editNetwork, chainId: e.target.value })}
                        disabled={isUpdating}
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-explorer">URL Explorateur</Label>
                      <Input
                        id="edit-explorer"
                        value={editNetwork.explorerUrl}
                        onChange={(e) => setEditNetwork({ ...editNetwork, explorerUrl: e.target.value })}
                        disabled={isUpdating}
                      />
                    </div>
                    <div className="flex flex-col space-y-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="edit-active"
                          checked={editNetwork.isActive}
                          onCheckedChange={(checked) => setEditNetwork({ ...editNetwork, isActive: checked })}
                          disabled={isUpdating}
                        />
                        <Label htmlFor="edit-active">Actif</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="edit-testnet"
                          checked={editNetwork.isTestnet}
                          onCheckedChange={(checked) => setEditNetwork({ ...editNetwork, isTestnet: checked })}
                          disabled={isUpdating}
                        />
                        <Label htmlFor="edit-testnet">Testnet</Label>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setSelectedNetwork(null)} disabled={isUpdating}>
                  Annuler
                </Button>
                <Button onClick={handleUpdateNetwork} disabled={isUpdating}>
                  {isUpdating ? "Mise à jour..." : "Mettre à jour"}
                </Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="add">
          <Card>
            <CardHeader>
              <CardTitle>Ajouter un nouveau réseau</CardTitle>
              <CardDescription>Configurez un nouveau réseau blockchain pour la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="new-name">Nom du réseau *</Label>
                    <Input
                      id="new-name"
                      placeholder="Ex: Ethereum Mainnet"
                      value={newNetwork.name}
                      onChange={(e) => setNewNetwork({ ...newNetwork, name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-symbol">Symbole *</Label>
                    <Input
                      id="new-symbol"
                      placeholder="Ex: ETH"
                      value={newNetwork.symbol}
                      onChange={(e) => setNewNetwork({ ...newNetwork, symbol: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-rpc">URL RPC *</Label>
                    <Input
                      id="new-rpc"
                      placeholder="Ex: https://mainnet.infura.io/v3/your-api-key"
                      value={newNetwork.rpcUrl}
                      onChange={(e) => setNewNetwork({ ...newNetwork, rpcUrl: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-chain-id">Chain ID *</Label>
                    <Input
                      id="new-chain-id"
                      placeholder="Ex: 1"
                      value={newNetwork.chainId}
                      onChange={(e) => setNewNetwork({ ...newNetwork, chainId: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-explorer">URL Explorateur</Label>
                    <Input
                      id="new-explorer"
                      placeholder="Ex: https://etherscan.io"
                      value={newNetwork.explorerUrl}
                      onChange={(e) => setNewNetwork({ ...newNetwork, explorerUrl: e.target.value })}
                    />
                  </div>
                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="new-active"
                        checked={newNetwork.isActive}
                        onCheckedChange={(checked) => setNewNetwork({ ...newNetwork, isActive: checked })}
                      />
                      <Label htmlFor="new-active">Actif</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="new-testnet"
                        checked={newNetwork.isTestnet}
                        onCheckedChange={(checked) => setNewNetwork({ ...newNetwork, isTestnet: checked })}
                      />
                      <Label htmlFor="new-testnet">Testnet</Label>
                    </div>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Information</AlertTitle>
                  <AlertDescription>
                    Les champs marqués d'un astérisque (*) sont obligatoires. Assurez-vous que l'URL RPC est valide et
                    accessible.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleCreateNetwork} className="ml-auto">
                <Plus className="mr-2 h-4 w-4" />
                Ajouter le réseau
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer le réseau "{networkToDelete?.name}" ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)} disabled={isDeleting}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={handleDeleteNetwork} disabled={isDeleting}>
              {isDeleting ? "Suppression..." : "Supprimer"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  BarChart3,
  Settings,
  Users,
  Shield,
  FileText,
  Activity,
  KeyRound,
  Database,
  Layers,
  UserCog,
  Lock,
  FileKey,
  Gauge,
  Coins,
  Wallet,
  Network,
} from "lucide-react"
import { useAdmin } from "@/hooks/use-admin"

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items: {
    href: string
    title: string
    icon?: React.ReactNode
    requireSuperAdmin?: boolean
  }[]
}

export function SidebarNav({ className, items, ...props }: SidebarNavProps) {
  const pathname = usePathname()
  const { isSuperAdmin } = useAdmin()

  return (
    <nav className={cn("flex flex-col space-y-1", className)} {...props}>
      {items
        .filter((item) => !item.requireSuperAdmin || isSuperAdmin)
        .map((item) => (
          <Button
            key={item.href}
            variant={pathname === item.href ? "secondary" : "ghost"}
            className={cn(
              "justify-start",
              pathname === item.href ? "bg-muted hover:bg-muted" : "hover:bg-transparent hover:underline",
            )}
            asChild
          >
            <Link href={item.href}>
              {item.icon && <span className="mr-2 h-4 w-4">{item.icon}</span>}
              {item.title}
            </Link>
          </Button>
        ))}
    </nav>
  )
}

export function getAdminSidebarItems() {
  return [
    {
      href: "/admin",
      title: "Tableau de bord",
      icon: <BarChart3 className="h-4 w-4" />,
    },
    {
      href: "/admin/user-management",
      title: "Gestion des utilisateurs",
      icon: <Users className="h-4 w-4" />,
    },
    {
      href: "/admin/role-management",
      title: "Gestion des rôles",
      icon: <Shield className="h-4 w-4" />,
      requireSuperAdmin: true,
    },
    {
      href: "/admin/user-role-assignment",
      title: "Attribution des rôles",
      icon: <UserCog className="h-4 w-4" />,
    },
    {
      href: "/admin/token-management",
      title: "Gestion des tokens",
      icon: <Coins className="h-4 w-4" />,
    },
    {
      href: "/admin/security-dashboard",
      title: "Sécurité",
      icon: <Lock className="h-4 w-4" />,
    },
    {
      href: "/admin/reports",
      title: "Rapports",
      icon: <FileText className="h-4 w-4" />,
    },
    {
      href: "/admin/activity-log",
      title: "Journal d'activité",
      icon: <Activity className="h-4 w-4" />,
    },
    {
      href: "/admin/platform-management",
      title: "Gestion de la plateforme",
      icon: <Settings className="h-4 w-4" />,
      requireSuperAdmin: true,
    },
    {
      href: "/admin/keypair-manager",
      title: "Gestionnaire de clés",
      icon: <KeyRound className="h-4 w-4" />,
      requireSuperAdmin: true,
    },
    {
      href: "/admin/suffix-config",
      title: "Configuration des suffixes",
      icon: <FileKey className="h-4 w-4" />,
    },
    {
      href: "/admin/performance-dashboard",
      title: "Performance",
      icon: <Gauge className="h-4 w-4" />,
    },
    {
      href: "/admin/env-manager",
      title: "Variables d'environnement",
      icon: <Database className="h-4 w-4" />,
      requireSuperAdmin: true,
    },
    {
      href: "/admin/quantum-dashboard",
      title: "Quantum Dashboard",
      icon: <Layers className="h-4 w-4" />,
    },
    {
      href: "/admin/network-management",
      title: "Gestion des réseaux",
      icon: <Network className="h-4 w-4" />,
    },
    {
      href: "/admin/wallet-management",
      title: "Gestion des wallets",
      icon: <Wallet className="h-4 w-4" />,
      requireSuperAdmin: true,
    },
  ]
}

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import {
  Search,
  Plus,
  MoreHorizontal,
  Clock,
  DollarSign,
  Rocket,
  AlertCircle,
  XCircle,
  CheckCircle2,
  ExternalLink,
  Edit,
  Trash2,
} from "lucide-react"

export default function QuantumTokenList() {
  const router = useRouter()
  const { toast } = useToast()
  const [tokens, setTokens] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")

  useEffect(() => {
    fetchTokens()
  }, [])

  const fetchTokens = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Données fictives pour la démonstration
      const mockTokens = [
        {
          id: "token1",
          name: "Global Finance Quantum",
          symbol: "GF",
          suffix: "QUANTUM",
          status: "fundraising",
          featured: true,
          verified: true,
          current_phase_name: "Presale",
          current_phase_percentage: 75,
          participants: 42,
          total_supply: 1000000000,
          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "8ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
        {
          id: "token2",
          name: "Solana Meme Quantum",
          symbol: "SM",
          suffix: "QUANTUM",
          status: "setup",
          featured: false,
          verified: true,
          current_phase_name: "Upcoming",
          current_phase_percentage: 0,
          participants: 0,
          total_supply: 500000000,
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "9ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
        {
          id: "token3",
          name: "Crypto Degen Quantum",
          symbol: "CD",
          suffix: "QUANTUM",
          status: "launched",
          featured: true,
          verified: true,
          current_phase_name: "Launched",
          current_phase_percentage: 100,
          participants: 156,
          total_supply: 2000000000,
          created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "7ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
        {
          id: "token4",
          name: "Failed Project Quantum",
          symbol: "FP",
          suffix: "QUANTUM",
          status: "failed",
          featured: false,
          verified: false,
          current_phase_name: "Failed",
          current_phase_percentage: 30,
          participants: 12,
          total_supply: 100000000,
          created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          owner_address: "6ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        },
      ]

      setTokens(mockTokens)
    } catch (error) {
      console.error("Error fetching tokens:", error)
      toast({
        title: "Error",
        description: "Failed to fetch tokens. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteToken = async (id: string) => {
    if (!confirm("Are you sure you want to delete this token? This action cannot be undone.")) {
      return
    }

    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Mettre à jour l'état local
      setTokens(tokens.filter((token) => token.id !== id))

      toast({
        title: "Token deleted",
        description: "The token has been successfully deleted.",
      })
    } catch (error) {
      console.error("Error deleting token:", error)
      toast({
        title: "Error",
        description: "Failed to delete token. Please try again.",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "setup":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            Upcoming
          </Badge>
        )
      case "fundraising":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <DollarSign className="mr-1 h-3 w-3" />
            Active
          </Badge>
        )
      case "preparing":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Preparing
          </Badge>
        )
      case "launched":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <Rocket className="mr-1 h-3 w-3" />
            Launched
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const filteredTokens = tokens.filter((token) => {
    const matchesSearch =
      token.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.owner_address.toLowerCase().includes(searchQuery.toLowerCase())

    if (activeTab === "all") return matchesSearch
    if (activeTab === "active") return matchesSearch && token.status === "fundraising"
    if (activeTab === "upcoming") return matchesSearch && token.status === "setup"
    if (activeTab === "launched") return matchesSearch && token.status === "launched"
    if (activeTab === "failed") return matchesSearch && token.status === "failed"
    return matchesSearch
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h2 className="text-2xl font-bold tracking-tight">Quantum Tokens</h2>
        <Button onClick={() => router.push("/admin/quantum-tokens/create")}>
          <Plus className="mr-2 h-4 w-4" />
          Create New Token
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full sm:w-auto">
          <TabsList className="grid grid-cols-5">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="launched">Launched</TabsTrigger>
            <TabsTrigger value="failed">Failed</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tokens..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Token List</CardTitle>
          <CardDescription>Manage all quantum tokens on the platform</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredTokens.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64">
              <p className="text-muted-foreground mb-4">No tokens found</p>
              <Button variant="outline" onClick={() => router.push("/admin/quantum-tokens/create")}>
                <Plus className="mr-2 h-4 w-4" />
                Create New Token
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Token</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Participants</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTokens.map((token) => (
                    <TableRow key={token.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="h-9 w-9 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-xs">
                            {token.symbol.substring(0, 2)}
                          </div>
                          <div>
                            <div className="font-medium flex items-center gap-2">
                              {token.name}
                              {token.featured && (
                                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                                  Featured
                                </Badge>
                              )}
                              {token.verified && <CheckCircle2 className="h-3 w-3 text-green-500" />}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {token.symbol}
                              {token.suffix}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(token.status)}</TableCell>
                      <TableCell>
                        <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                          <div
                            className={`h-full ${
                              token.status === "failed"
                                ? "bg-red-500"
                                : token.status === "launched"
                                  ? "bg-purple-500"
                                  : "bg-blue-500"
                            }`}
                            style={{ width: `${token.current_phase_percentage}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">{token.current_phase_percentage}%</div>
                      </TableCell>
                      <TableCell>{token.participants}</TableCell>
                      <TableCell>{new Date(token.created_at).toLocaleDateString()}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/quantum-tokens/${token.id}`}>
                                <ExternalLink className="mr-2 h-4 w-4" />
                                View Details
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/token-quantum/launch/${token.id}`} target="_blank">
                                <ExternalLink className="mr-2 h-4 w-4" />
                                View Public Page
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/quantum-tokens/${token.id}/edit`}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteToken(token.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {filteredTokens.length} of {tokens.length} tokens
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}

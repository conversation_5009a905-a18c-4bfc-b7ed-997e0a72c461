"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { format, formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { ArrowUpRight, ArrowDownRight, RefreshCw } from "lucide-react"

// Types
interface PresaleMetrics {
  totalPresales: number
  activePresales: number
  completedPresales: number
  pendingPresales: number
  failedPresales: number
  cancelledPresales: number
  totalRaised: number
  totalParticipants: number
  averageRaisePerPresale: number
  successRate: number
}

interface PresaleTimelineItem {
  id: string
  tokenName: string
  tokenSymbol: string
  date: number
  type: "start" | "end"
  status: "pending" | "active" | "completed" | "cancelled" | "failed"
}

interface Presale {
  id: string
  tokenName: string
  tokenSymbol: string
  price: number
  hardCap: number
  softCap: number
  raisedAmount: number
  participants: number
  progress: number
  startTime: number
  endTime: number
  status: "pending" | "active" | "completed" | "cancelled" | "failed"
}

interface ParticipationStat {
  date: string
  participants: number
  amount: number
}

export function PresaleDashboard() {
  const [metrics, setMetrics] = useState<PresaleMetrics | null>(null)
  const [timeline, setTimeline] = useState<PresaleTimelineItem[]>([])
  const [upcomingPresales, setUpcomingPresales] = useState<Presale[]>([])
  const [activePresales, setActivePresales] = useState<Presale[]>([])
  const [completedPresales, setCompletedPresales] = useState<Presale[]>([])
  const [participationStats, setParticipationStats] = useState<ParticipationStat[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    setIsLoading(true)
    try {
      // Dans un environnement réel, cela serait des appels API
      // Simulons des données pour la démonstration
      setTimeout(() => {
        // Métriques
        const mockMetrics: PresaleMetrics = {
          totalPresales: 25,
          activePresales: 8,
          completedPresales: 12,
          pendingPresales: 3,
          failedPresales: 1,
          cancelledPresales: 1,
          totalRaised: 1250000,
          totalParticipants: 3500,
          averageRaisePerPresale: 50000,
          successRate: 85.7,
        }
        setMetrics(mockMetrics)

        // Timeline
        const now = Date.now()
        const mockTimeline: PresaleTimelineItem[] = [
          {
            id: "presale_1_start",
            tokenName: "Solana Platform Token",
            tokenSymbol: "SPT",
            date: now - 7 * 24 * 60 * 60 * 1000,
            type: "start",
            status: "active",
          },
          {
            id: "presale_2_start",
            tokenName: "Meme Coin",
            tokenSymbol: "MEME",
            date: now + 3 * 24 * 60 * 60 * 1000,
            type: "start",
            status: "pending",
          },
          {
            id: "presale_1_end",
            tokenName: "Solana Platform Token",
            tokenSymbol: "SPT",
            date: now + 14 * 24 * 60 * 60 * 1000,
            type: "end",
            status: "active",
          },
          {
            id: "presale_3_start",
            tokenName: "Utility Token",
            tokenSymbol: "UTIL",
            date: now - 30 * 24 * 60 * 60 * 1000,
            type: "start",
            status: "completed",
          },
          {
            id: "presale_3_end",
            tokenName: "Utility Token",
            tokenSymbol: "UTIL",
            date: now - 2 * 24 * 60 * 60 * 1000,
            type: "end",
            status: "completed",
          },
          {
            id: "presale_2_end",
            tokenName: "Meme Coin",
            tokenSymbol: "MEME",
            date: now + 17 * 24 * 60 * 60 * 1000,
            type: "end",
            status: "pending",
          },
        ]
        setTimeline(mockTimeline)

        // Presales à venir
        const mockUpcomingPresales: Presale[] = [
          {
            id: "presale_2",
            tokenName: "Meme Coin",
            tokenSymbol: "MEME",
            price: 0.001,
            hardCap: 500000,
            softCap: 100000,
            raisedAmount: 0,
            participants: 0,
            progress: 0,
            startTime: now + 3 * 24 * 60 * 60 * 1000,
            endTime: now + 17 * 24 * 60 * 60 * 1000,
            status: "pending",
          },
          {
            id: "presale_4",
            tokenName: "Gaming Token",
            tokenSymbol: "GAME",
            price: 0.005,
            hardCap: 300000,
            softCap: 150000,
            raisedAmount: 0,
            participants: 0,
            progress: 0,
            startTime: now + 5 * 24 * 60 * 60 * 1000,
            endTime: now + 19 * 24 * 60 * 60 * 1000,
            status: "pending",
          },
          {
            id: "presale_5",
            tokenName: "DeFi Protocol",
            tokenSymbol: "DEFI",
            price: 0.01,
            hardCap: 1000000,
            softCap: 500000,
            raisedAmount: 0,
            participants: 0,
            progress: 0,
            startTime: now + 10 * 24 * 60 * 60 * 1000,
            endTime: now + 24 * 24 * 60 * 60 * 1000,
            status: "pending",
          },
        ]
        setUpcomingPresales(mockUpcomingPresales)

        // Presales actives
        const mockActivePresales: Presale[] = [
          {
            id: "presale_1",
            tokenName: "Solana Platform Token",
            tokenSymbol: "SPT",
            price: 0.005,
            hardCap: 1000000,
            softCap: 500000,
            raisedAmount: 350000,
            participants: 120,
            progress: 35,
            startTime: now - 7 * 24 * 60 * 60 * 1000,
            endTime: now + 14 * 24 * 60 * 60 * 1000,
            status: "active",
          },
          {
            id: "presale_6",
            tokenName: "NFT Marketplace",
            tokenSymbol: "NFTM",
            price: 0.008,
            hardCap: 800000,
            softCap: 400000,
            raisedAmount: 520000,
            participants: 230,
            progress: 65,
            startTime: now - 10 * 24 * 60 * 60 * 1000,
            endTime: now + 4 * 24 * 60 * 60 * 1000,
            status: "active",
          },
          {
            id: "presale_7",
            tokenName: "AI Protocol",
            tokenSymbol: "AIP",
            price: 0.015,
            hardCap: 1500000,
            softCap: 750000,
            raisedAmount: 900000,
            participants: 310,
            progress: 60,
            startTime: now - 5 * 24 * 60 * 60 * 1000,
            endTime: now + 10 * 24 * 60 * 60 * 1000,
            status: "active",
          },
        ]
        setActivePresales(mockActivePresales)

        // Presales terminées
        const mockCompletedPresales: Presale[] = [
          {
            id: "presale_3",
            tokenName: "Utility Token",
            tokenSymbol: "UTIL",
            price: 0.01,
            hardCap: 200000,
            softCap: 100000,
            raisedAmount: 180000,
            participants: 75,
            progress: 90,
            startTime: now - 30 * 24 * 60 * 60 * 1000,
            endTime: now - 2 * 24 * 60 * 60 * 1000,
            status: "completed",
          },
          {
            id: "presale_8",
            tokenName: "Metaverse Token",
            tokenSymbol: "META",
            price: 0.02,
            hardCap: 2000000,
            softCap: 1000000,
            raisedAmount: 1850000,
            participants: 420,
            progress: 92.5,
            startTime: now - 45 * 24 * 60 * 60 * 1000,
            endTime: now - 15 * 24 * 60 * 60 * 1000,
            status: "completed",
          },
          {
            id: "presale_9",
            tokenName: "Social Token",
            tokenSymbol: "SOCIAL",
            price: 0.003,
            hardCap: 300000,
            softCap: 150000,
            raisedAmount: 280000,
            participants: 950,
            progress: 93.3,
            startTime: now - 20 * 24 * 60 * 60 * 1000,
            endTime: now - 5 * 24 * 60 * 60 * 1000,
            status: "completed",
          },
        ]
        setCompletedPresales(mockCompletedPresales)

        // Statistiques de participation
        const mockParticipationStats: ParticipationStat[] = [
          { date: "2023-05-01", participants: 15, amount: 7500 },
          { date: "2023-05-02", participants: 22, amount: 11000 },
          { date: "2023-05-03", participants: 18, amount: 9000 },
          { date: "2023-05-04", participants: 25, amount: 12500 },
          { date: "2023-05-05", participants: 30, amount: 15000 },
          { date: "2023-05-06", participants: 28, amount: 14000 },
          { date: "2023-05-07", participants: 35, amount: 17500 },
          { date: "2023-05-08", participants: 40, amount: 20000 },
          { date: "2023-05-09", participants: 38, amount: 19000 },
          { date: "2023-05-10", participants: 45, amount: 22500 },
          { date: "2023-05-11", participants: 50, amount: 25000 },
          { date: "2023-05-12", participants: 48, amount: 24000 },
          { date: "2023-05-13", participants: 55, amount: 27500 },
          { date: "2023-05-14", participants: 60, amount: 30000 },
        ]
        setParticipationStats(mockParticipationStats)

        setIsLoading(false)
      }, 1500)
    } catch (error) {
      console.error("Error fetching dashboard data:", error)
      toast({
        title: "Erreur",
        description: "Impossible de récupérer les données du tableau de bord",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const formatDate = (timestamp: number) => {
    return format(new Date(timestamp), "dd MMMM yyyy", { locale: fr })
  }

  const formatDateTime = (timestamp: number) => {
    return format(new Date(timestamp), "dd MMMM yyyy HH:mm", { locale: fr })
  }

  const formatTimeAgo = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true, locale: fr })
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("fr-FR").format(value)
  }

  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "percent",
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(value / 100)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge className="bg-blue-500">En attente</Badge>
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "completed":
        return <Badge className="bg-green-700">Terminée</Badge>
      case "cancelled":
        return <Badge className="bg-orange-500">Annulée</Badge>
      case "failed":
        return <Badge className="bg-red-500">Échouée</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Tableau de Bord des Presales</h2>
        <Button onClick={fetchDashboardData} variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" /> Actualiser
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Métriques */}
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Total des Presales</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.totalPresales}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    <span className="text-green-500 font-medium">{metrics.activePresales}</span> actives,{" "}
                    <span className="text-blue-500 font-medium">{metrics.pendingPresales}</span> en attente
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Total Levé</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(metrics.totalRaised)}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Moyenne: {formatCurrency(metrics.averageRaisePerPresale)} par presale
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Participants</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(metrics.totalParticipants)}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Moyenne: {formatNumber(Math.round(metrics.totalParticipants / metrics.totalPresales))} par presale
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Taux de Réussite</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatPercentage(metrics.successRate)}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {metrics.completedPresales} réussies sur{" "}
                    {metrics.completedPresales + metrics.failedPresales + metrics.cancelledPresales} terminées
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Onglets */}
          <Tabs defaultValue="active">
            <TabsList className="mb-4">
              <TabsTrigger value="active">Presales Actives</TabsTrigger>
              <TabsTrigger value="upcoming">À Venir</TabsTrigger>
              <TabsTrigger value="completed">Terminées</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
            </TabsList>

            {/* Presales Actives */}
            <TabsContent value="active">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {activePresales.map((presale) => (
                  <Card key={presale.id}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{presale.tokenName}</CardTitle>
                          <CardDescription>{presale.tokenSymbol}</CardDescription>
                        </div>
                        {getStatusBadge(presale.status)}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Prix:</span>
                          <span className="font-medium">{presale.price} SOL</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Levé:</span>
                          <span className="font-medium">
                            {formatNumber(presale.raisedAmount)} / {formatNumber(presale.hardCap)} SOL
                          </span>
                        </div>
                        <Progress value={presale.progress} className="h-2" />
                        <div className="flex justify-between text-xs">
                          <span>{presale.progress}%</span>
                          <span>{presale.participants} participants</span>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Début:</span>{" "}
                          <span className="font-medium">{formatTimeAgo(presale.startTime)}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Fin:</span>{" "}
                          <span className="font-medium">{formatTimeAgo(presale.endTime)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Presales à Venir */}
            <TabsContent value="upcoming">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {upcomingPresales.map((presale) => (
                  <Card key={presale.id}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{presale.tokenName}</CardTitle>
                          <CardDescription>{presale.tokenSymbol}</CardDescription>
                        </div>
                        {getStatusBadge(presale.status)}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Prix:</span>
                          <span className="font-medium">{presale.price} SOL</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Hard Cap:</span>
                          <span className="font-medium">{formatNumber(presale.hardCap)} SOL</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Soft Cap:</span>
                          <span className="font-medium">{formatNumber(presale.softCap)} SOL</span>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Début:</span>{" "}
                          <span className="font-medium">{formatTimeAgo(presale.startTime)}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Fin:</span>{" "}
                          <span className="font-medium">{formatTimeAgo(presale.endTime)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Presales Terminées */}
            <TabsContent value="completed">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {completedPresales.map((presale) => (
                  <Card key={presale.id}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{presale.tokenName}</CardTitle>
                          <CardDescription>{presale.tokenSymbol}</CardDescription>
                        </div>
                        {getStatusBadge(presale.status)}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Prix:</span>
                          <span className="font-medium">{presale.price} SOL</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Levé:</span>
                          <span className="font-medium">
                            {formatNumber(presale.raisedAmount)} / {formatNumber(presale.hardCap)} SOL
                          </span>
                        </div>
                        <Progress value={presale.progress} className="h-2" />
                        <div className="flex justify-between text-xs">
                          <span>{presale.progress}%</span>
                          <span>{presale.participants} participants</span>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Début:</span>{" "}
                          <span className="font-medium">{formatDate(presale.startTime)}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Fin:</span>{" "}
                          <span className="font-medium">{formatDate(presale.endTime)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Timeline */}
            <TabsContent value="timeline">
              <Card>
                <CardHeader>
                  <CardTitle>Timeline des Presales</CardTitle>
                  <CardDescription>Calendrier des événements de presale</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {timeline
                      .sort((a, b) => a.date - b.date)
                      .map((item) => (
                        <div key={item.id} className="flex items-start">
                          <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                            {item.type === "start" ? (
                              <ArrowUpRight className="h-5 w-5" />
                            ) : (
                              <ArrowDownRight className="h-5 w-5" />
                            )}
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium leading-none">
                              {item.tokenName} ({item.tokenSymbol})
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {item.type === "start" ? "Début" : "Fin"} de la presale {getStatusBadge(item.status)}
                            </p>
                            <p className="text-xs text-muted-foreground">{formatDateTime(item.date)}</p>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}

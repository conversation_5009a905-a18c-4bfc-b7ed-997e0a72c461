"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Loader2, Check, AlertCircle, Copy, Info, Shield, Zap, Lock, Coins, Save } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Accordion, Accordion<PERSON>ontent, AccordionI<PERSON>, AccordionTrigger } from "@/components/ui/accordion"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import ecosystemTokenService, {
  type EcosystemToken,
  type EcosystemTokenConfig,
  type TokenDistributionWallets,
} from "@/lib/ecosystem-token-service"

export function EcosystemTokenConfiguration() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("basic")
  const [existingToken, setExistingToken] = useState<EcosystemToken | null>(null)
  const [tokenConfig, setTokenConfig] = useState<EcosystemTokenConfig | null>(null)
  const [isDeploying, setIsDeploying] = useState(false)
  const [isDistributing, setIsDistributing] = useState(false)
  const [deployProgress, setDeployProgress] = useState(0)
  const [distributionProgress, setDistributionProgress] = useState(0)

  // Distribution wallets
  const [distributionWallets, setDistributionWallets] = useState<TokenDistributionWallets>({
    society: "",
    development: "",
    marketing: "",
    priceImpact: "",
    dexLiquidity: "",
    stakingFutures: "",
    presale: "",
    exchangeDevelopment: "",
  })

  // Load existing token and default config on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [token, defaultConfig] = await Promise.all([
          ecosystemTokenService.getToken(),
          ecosystemTokenService.getDefaultConfig(),
        ])

        setExistingToken(token)
        setTokenConfig(token?.config || defaultConfig)
      } catch (err) {
        console.error("Error loading token data:", err)
        setError("Erreur lors du chargement des données du token")
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // Handle config changes
  const handleConfigChange = (section: string, field: string, value: any) => {
    if (!tokenConfig) return

    setTokenConfig({
      ...tokenConfig,
      [section]: {
        ...tokenConfig[section as keyof EcosystemTokenConfig],
        [field]: value,
      },
    })
  }

  // Handle basic field changes
  const handleBasicChange = (field: keyof EcosystemTokenConfig, value: any) => {
    if (!tokenConfig) return

    setTokenConfig({
      ...tokenConfig,
      [field]: value,
    })
  }

  // Handle distribution changes
  const handleDistributionChange = (field: string, value: number) => {
    if (!tokenConfig) return

    setTokenConfig({
      ...tokenConfig,
      distribution: {
        ...tokenConfig.distribution,
        [field]: value,
      },
    })
  }

  // Handle wallet address changes
  const handleWalletChange = (field: keyof TokenDistributionWallets, value: string) => {
    setDistributionWallets({
      ...distributionWallets,
      [field]: value,
    })
  }

  // Save token configuration
  const saveTokenConfig = async () => {
    if (!tokenConfig) return

    setIsSaving(true)
    setError(null)

    try {
      await ecosystemTokenService.updateConfig(tokenConfig)
      setIsSuccess(true)
      setTimeout(() => setIsSuccess(false), 3000)
    } catch (err: any) {
      console.error("Error saving token config:", err)
      setError(err.message || "Erreur lors de la sauvegarde de la configuration")
    } finally {
      setIsSaving(false)
    }
  }

  // Create and deploy token
  const createAndDeployToken = async () => {
    if (!tokenConfig) return

    // Vérifier que la distribution totale est de 100%
    const totalDistribution = calculateTotalDistribution()
    if (totalDistribution !== 100) {
      setError(`La distribution totale doit être de 100% (actuellement ${totalDistribution}%)`)
      return
    }

    // Vérifier que la distribution des frais est de 100%
    const totalFeeDistribution = calculateTotalFeeDistribution()
    if (totalFeeDistribution !== 100) {
      setError(`La distribution des frais doit être de 100% (actuellement ${totalFeeDistribution}%)`)
      return
    }

    setIsDeploying(true)
    setError(null)
    setDeployProgress(0)

    try {
      // Simuler la création du token
      setDeployProgress(20)
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const createdToken = await ecosystemTokenService.createToken(tokenConfig)
      setExistingToken(createdToken)

      setDeployProgress(50)
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Simuler le déploiement du token
      // Dans une implémentation réelle, vous utiliseriez un wallet connecté
      const dummyKeypair = {} as any
      const deployedToken = await ecosystemTokenService.deployToken(dummyKeypair)
      setExistingToken(deployedToken)

      setDeployProgress(100)
      setIsSuccess(true)
      setTimeout(() => setIsSuccess(false), 3000)
    } catch (err: any) {
      console.error("Error creating and deploying token:", err)
      setError(err.message || "Erreur lors de la création et du déploiement du token")
    } finally {
      setIsDeploying(false)
    }
  }

  // Distribute token
  const distributeToken = async () => {
    if (!existingToken || !existingToken.deployedOnChain) return

    // Vérifier que toutes les adresses de wallet sont renseignées
    const missingWallets = Object.entries(distributionWallets)
      .filter(([_, address]) => !address)
      .map(([key]) => key)

    if (missingWallets.length > 0) {
      setError(`Veuillez renseigner toutes les adresses de wallet: ${missingWallets.join(", ")}`)
      return
    }

    setIsDistributing(true)
    setError(null)
    setDistributionProgress(0)

    try {
      // Simuler la distribution du token
      setDistributionProgress(30)
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Dans une implémentation réelle, vous utiliseriez un wallet connecté
      const dummyKeypair = {} as any
      const distributedToken = await ecosystemTokenService.distributeToken(distributionWallets, dummyKeypair)
      setExistingToken(distributedToken)

      setDistributionProgress(100)
      setIsSuccess(true)
      setTimeout(() => setIsSuccess(false), 3000)
    } catch (err: any) {
      console.error("Error distributing token:", err)
      setError(err.message || "Erreur lors de la distribution du token")
    } finally {
      setIsDistributing(false)
    }
  }

  // Reset token (for development purposes)
  const resetToken = async () => {
    if (confirm("Êtes-vous sûr de vouloir réinitialiser le token ? Cette action est irréversible.")) {
      try {
        await ecosystemTokenService.resetToken()
        window.location.reload()
      } catch (err) {
        console.error("Error resetting token:", err)
      }
    }
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert("Copié dans le presse-papier")
  }

  // Calculate total distribution
  const calculateTotalDistribution = () => {
    if (!tokenConfig) return 0

    return Object.values(tokenConfig.distribution).reduce((sum, value) => sum + value, 0)
  }

  // Calculate total fee distribution
  const calculateTotalFeeDistribution = () => {
    if (!tokenConfig) return 0

    return Object.values(tokenConfig.feeDistribution).reduce((sum, value) => sum + value, 0)
  }

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    )
  }

  // Rendu du token déjà déployé et distribué
  if (existingToken && existingToken.deployedOnChain && existingToken.distributionComplete) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Token de l'Écosystème</CardTitle>
              <CardDescription>Détails du token de l'écosystème déployé et distribué.</CardDescription>
            </div>
            <Badge variant="default">Déployé et Distribué</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <Alert className="bg-blue-50 border-blue-200 mb-6">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertTitle className="text-blue-800">Token déployé et distribué</AlertTitle>
            <AlertDescription className="text-blue-700">
              Le token de l'écosystème a été déployé et distribué avec succès. Vous pouvez consulter les détails
              ci-dessous.
            </AlertDescription>
          </Alert>

          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Nom</Label>
                <div className="p-2 bg-muted rounded-md">{existingToken.config.name}</div>
              </div>
              <div className="space-y-2">
                <Label>Symbole</Label>
                <div className="p-2 bg-muted rounded-md">{existingToken.config.symbol}</div>
              </div>
              <div className="space-y-2">
                <Label>Réseau</Label>
                <div className="p-2 bg-muted rounded-md">{existingToken.config.network}</div>
              </div>
              <div className="space-y-2">
                <Label>Approvisionnement Total</Label>
                <div className="p-2 bg-muted rounded-md">
                  {existingToken.config.totalSupply.toLocaleString()} (Décimales: {existingToken.config.decimals})
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Adresse du Token</Label>
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-muted rounded-md flex-1 font-mono text-sm">{existingToken.mintAddress}</div>
                <Button variant="outline" size="sm" onClick={() => copyToClipboard(existingToken.mintAddress)}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Transaction de Déploiement</Label>
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-muted rounded-md flex-1 font-mono text-sm">{existingToken.transactionId}</div>
                <Button variant="outline" size="sm" onClick={() => copyToClipboard(existingToken.transactionId || "")}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="distribution">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Coins className="h-4 w-4 mr-2" />
                    Distribution des Tokens
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Destination</TableHead>
                        <TableHead>Pourcentage</TableHead>
                        <TableHead>Montant</TableHead>
                        <TableHead>Transaction</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Object.entries(existingToken.config.distribution).map(([key, percentage]) => (
                        <TableRow key={key}>
                          <TableCell className="font-medium">{key}</TableCell>
                          <TableCell>{percentage}%</TableCell>
                          <TableCell>
                            {Math.floor((existingToken.config.totalSupply * percentage) / 100).toLocaleString()}
                          </TableCell>
                          <TableCell>
                            {existingToken.distributionTransactions?.[key] && (
                              <div className="flex items-center space-x-2">
                                <span className="truncate max-w-[150px]">
                                  {existingToken.distributionTransactions[key]}
                                </span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(existingToken.distributionTransactions?.[key] || "")}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="features">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Shield className="h-4 w-4 mr-2" />
                    Fonctionnalités du Token
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Peut Minter</Label>
                      <div className="p-2 bg-muted rounded-md">{existingToken.config.canMint ? "Oui" : "Non"}</div>
                    </div>
                    <div className="space-y-2">
                      <Label>Peut Burn</Label>
                      <div className="p-2 bg-muted rounded-md">{existingToken.config.canBurn ? "Oui" : "Non"}</div>
                    </div>
                    <div className="space-y-2">
                      <Label>Peut Freeze</Label>
                      <div className="p-2 bg-muted rounded-md">{existingToken.config.canFreeze ? "Oui" : "Non"}</div>
                    </div>
                    <div className="space-y-2">
                      <Label>Peut Pause</Label>
                      <div className="p-2 bg-muted rounded-md">{existingToken.config.canPause ? "Oui" : "Non"}</div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="fees">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Zap className="h-4 w-4 mr-2" />
                    Frais de Transaction
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Frais de Base</Label>
                        <div className="p-2 bg-muted rounded-md">{existingToken.config.fees.baseFee}%</div>
                      </div>
                      <div className="space-y-2">
                        <Label>Frais Additionnels</Label>
                        <div className="p-2 bg-muted rounded-md">{existingToken.config.fees.additionalFee}%</div>
                      </div>
                      <div className="space-y-2">
                        <Label>Taxe sur Gains Excessifs</Label>
                        <div className="p-2 bg-muted rounded-md">{existingToken.config.fees.antiExcessGainsTax}%</div>
                      </div>
                      <div className="space-y-2">
                        <Label>Seuil de Gains Excessifs</Label>
                        <div className="p-2 bg-muted rounded-md">
                          {existingToken.config.fees.antiExcessGainsThreshold}%
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Distribution des Frais</Label>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Destination</TableHead>
                            <TableHead>Pourcentage</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {Object.entries(existingToken.config.feeDistribution).map(([key, percentage]) => (
                            <TableRow key={key}>
                              <TableCell className="font-medium">{key}</TableCell>
                              <TableCell>{percentage}%</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="limits">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Lock className="h-4 w-4 mr-2" />
                    Limites de Transaction
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Taille Max. de Transaction</Label>
                      <div className="p-2 bg-muted rounded-md">
                        {existingToken.config.transactionLimits.maxTransactionSize.toLocaleString()} tokens
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Détention Max. par Wallet</Label>
                      <div className="p-2 bg-muted rounded-md">
                        {existingToken.config.transactionLimits.maxWalletHolding.toLocaleString()} tokens
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Achats Max. par Bloc</Label>
                      <div className="p-2 bg-muted rounded-md">
                        {existingToken.config.transactionLimits.maxPurchasesPerBlock}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Ventes Max. par Bloc</Label>
                      <div className="p-2 bg-muted rounded-md">
                        {existingToken.config.transactionLimits.maxSalesPerBlock}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Période de Refroidissement</Label>
                      <div className="p-2 bg-muted rounded-md">
                        {existingToken.config.transactionLimits.cooldownPeriod} secondes
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <div className="flex justify-end mt-6">
              <Button variant="outline" onClick={resetToken}>
                Réinitialiser (Dev uniquement)
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Rendu du token déployé mais non distribué
  if (existingToken && existingToken.deployedOnChain) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Distribution du Token de l'Écosystème</CardTitle>
              <CardDescription>Distribuez le token de l'écosystème aux différents wallets.</CardDescription>
            </div>
            <Badge variant="outline">Déployé</Badge>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isSuccess && (
            <Alert className="bg-green-50 border-green-200 mb-4">
              <Check className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Opération réussie</AlertTitle>
              <AlertDescription className="text-green-700">L'opération a été effectuée avec succès.</AlertDescription>
            </Alert>
          )}

          <Alert className="bg-amber-50 border-amber-200 mb-6">
            <Info className="h-4 w-4 text-amber-600" />
            <AlertTitle className="text-amber-800">Token déployé</AlertTitle>
            <AlertDescription className="text-amber-700">
              Le token de l'écosystème a été déployé avec succès. Vous devez maintenant distribuer les tokens.
            </AlertDescription>
          </Alert>

          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Nom</Label>
                <div className="p-2 bg-muted rounded-md">{existingToken.config.name}</div>
              </div>
              <div className="space-y-2">
                <Label>Symbole</Label>
                <div className="p-2 bg-muted rounded-md">{existingToken.config.symbol}</div>
              </div>
              <div className="space-y-2">
                <Label>Réseau</Label>
                <div className="p-2 bg-muted rounded-md">{existingToken.config.network}</div>
              </div>
              <div className="space-y-2">
                <Label>Approvisionnement Total</Label>
                <div className="p-2 bg-muted rounded-md">
                  {existingToken.config.totalSupply.toLocaleString()} (Décimales: {existingToken.config.decimals})
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Adresse du Token</Label>
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-muted rounded-md flex-1 font-mono text-sm">{existingToken.mintAddress}</div>
                <Button variant="outline" size="sm" onClick={() => copyToClipboard(existingToken.mintAddress)}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Transaction de Déploiement</Label>
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-muted rounded-md flex-1 font-mono text-sm">{existingToken.transactionId}</div>
                <Button variant="outline" size="sm" onClick={() => copyToClipboard(existingToken.transactionId || "")}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Separator className="my-6" />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Distribution des Tokens</h3>
              <p className="text-muted-foreground">
                Entrez les adresses des wallets pour distribuer les tokens selon la répartition configurée.
              </p>

              <div className="space-y-4">
                {Object.entries(existingToken.config.distribution).map(([key, percentage]) => (
                  <div key={key} className="grid grid-cols-3 gap-4 items-center">
                    <div>
                      <Label>{key}</Label>
                      <p className="text-sm text-muted-foreground">
                        {percentage}% (
                        {Math.floor((existingToken.config.totalSupply * percentage) / 100).toLocaleString()} tokens)
                      </p>
                    </div>
                    <div className="col-span-2">
                      <Input
                        placeholder={`Adresse du wallet pour ${key}`}
                        value={distributionWallets[key as keyof TokenDistributionWallets] || ""}
                        onChange={(e) => handleWalletChange(key as keyof TokenDistributionWallets, e.target.value)}
                      />
                    </div>
                  </div>
                ))}
              </div>

              {isDistributing && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Distribution en cours...</span>
                    <span>{distributionProgress}%</span>
                  </div>
                  <Progress value={distributionProgress} />
                </div>
              )}

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={resetToken}>
                  Réinitialiser (Dev uniquement)
                </Button>
                <Button onClick={distributeToken} disabled={isDistributing}>
                  {isDistributing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Distribution en cours...
                    </>
                  ) : (
                    "Distribuer les Tokens"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Rendu du formulaire de configuration et de déploiement
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Configuration du Token de l'Écosystème</CardTitle>
        <CardDescription>Configurez et déployez le token principal de l'écosystème Global Finance.</CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isSuccess && (
          <Alert className="bg-green-50 border-green-200 mb-4">
            <Check className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Opération réussie</AlertTitle>
            <AlertDescription className="text-green-700">L'opération a été effectuée avec succès.</AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-5 mb-4">
            <TabsTrigger value="basic">Basique</TabsTrigger>
            <TabsTrigger value="distribution">Distribution</TabsTrigger>
            <TabsTrigger value="features">Fonctionnalités</TabsTrigger>
            <TabsTrigger value="fees">Frais</TabsTrigger>
            <TabsTrigger value="limits">Limites</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom du Token</Label>
                <Input
                  id="name"
                  value={tokenConfig?.name || ""}
                  onChange={(e) => handleBasicChange("name", e.target.value)}
                  placeholder="Global Finance Token"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="symbol">Symbole</Label>
                <Input
                  id="symbol"
                  value={tokenConfig?.symbol || ""}
                  onChange={(e) => handleBasicChange("symbol", e.target.value)}
                  placeholder="GF"
                  maxLength={10}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="network">Réseau</Label>
                <Select
                  value={tokenConfig?.network || "devnet"}
                  onValueChange={(value) => handleBasicChange("network", value)}
                >
                  <SelectTrigger id="network">
                    <SelectValue placeholder="Sélectionnez un réseau" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="devnet">Solana Devnet</SelectItem>
                    <SelectItem value="testnet">Solana Testnet</SelectItem>
                    <SelectItem value="mainnet">Solana Mainnet</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="decimals">Décimales</Label>
                <Input
                  id="decimals"
                  type="number"
                  value={tokenConfig?.decimals || 9}
                  onChange={(e) => handleBasicChange("decimals", Number.parseInt(e.target.value))}
                  min={0}
                  max={18}
                />
              </div>
              <div className="space-y-2 col-span-2">
                <Label htmlFor="totalSupply">Approvisionnement Total</Label>
                <Input
                  id="totalSupply"
                  type="number"
                  value={tokenConfig?.totalSupply || 1000000000}
                  onChange={(e) => handleBasicChange("totalSupply", Number.parseInt(e.target.value))}
                  min={1}
                />
              </div>
              <div className="space-y-2 col-span-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={tokenConfig?.description || ""}
                  onChange={(e) => handleBasicChange("description", e.target.value)}
                  placeholder="Token principal de l'écosystème Global Finance"
                />
              </div>
              <div className="space-y-2 col-span-2">
                <Label htmlFor="website">Site Web</Label>
                <Input
                  id="website"
                  value={tokenConfig?.website || ""}
                  onChange={(e) => handleBasicChange("website", e.target.value)}
                  placeholder="https://globalfinance.com"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="distribution" className="space-y-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Distribution des Tokens</h3>
                <div className="text-sm">
                  Total:{" "}
                  <span className={calculateTotalDistribution() === 100 ? "text-green-600" : "text-red-600"}>
                    {calculateTotalDistribution()}%
                  </span>
                </div>
              </div>

              <Alert
                className={
                  calculateTotalDistribution() === 100 ? "bg-green-50 border-green-200" : "bg-amber-50 border-amber-200"
                }
              >
                <Info
                  className={`h-4 w-4 ${calculateTotalDistribution() === 100 ? "text-green-600" : "text-amber-600"}`}
                />
                <AlertTitle className={calculateTotalDistribution() === 100 ? "text-green-800" : "text-amber-800"}>
                  {calculateTotalDistribution() === 100 ? "Distribution valide" : "Distribution incomplète"}
                </AlertTitle>
                <AlertDescription
                  className={calculateTotalDistribution() === 100 ? "text-green-700" : "text-amber-700"}
                >
                  {calculateTotalDistribution() === 100
                    ? "La distribution totale est de 100%."
                    : `La distribution totale doit être de 100% (actuellement ${calculateTotalDistribution()}%).`}
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                {tokenConfig &&
                  Object.entries(tokenConfig.distribution).map(([key, value]) => (
                    <div key={key} className="grid grid-cols-3 gap-4 items-center">
                      <Label htmlFor={`distribution-${key}`}>{key}</Label>
                      <div className="col-span-2">
                        <div className="flex items-center space-x-2">
                          <Slider
                            id={`distribution-${key}`}
                            value={[value]}
                            min={0}
                            max={100}
                            step={1}
                            onValueChange={(values) => handleDistributionChange(key, values[0])}
                            className="flex-1"
                          />
                          <div className="w-12 text-right">
                            <Input
                              type="number"
                              value={value}
                              onChange={(e) => handleDistributionChange(key, Number.parseInt(e.target.value) || 0)}
                              min={0}
                              max={100}
                              className="w-full"
                            />
                          </div>
                          <div className="w-4">%</div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="features" className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="canMint">Peut Minter</Label>
                  <Switch
                    id="canMint"
                    checked={tokenConfig?.canMint || false}
                    onCheckedChange={(checked) => handleBasicChange("canMint", checked)}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Permet de créer de nouveaux tokens après le déploiement initial.
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="canBurn">Peut Burn</Label>
                  <Switch
                    id="canBurn"
                    checked={tokenConfig?.canBurn || false}
                    onCheckedChange={(checked) => handleBasicChange("canBurn", checked)}
                  />
                </div>
                <p className="text-sm text-muted-foreground">Permet de détruire des tokens existants.</p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="canFreeze">Peut Freeze</Label>
                  <Switch
                    id="canFreeze"
                    checked={tokenConfig?.canFreeze || false}
                    onCheckedChange={(checked) => handleBasicChange("canFreeze", checked)}
                  />
                </div>
                <p className="text-sm text-muted-foreground">Permet de geler des comptes spécifiques.</p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="canPause">Peut Pause</Label>
                  <Switch
                    id="canPause"
                    checked={tokenConfig?.canPause || false}
                    onCheckedChange={(checked) => handleBasicChange("canPause", checked)}
                  />
                </div>
                <p className="text-sm text-muted-foreground">Permet de suspendre temporairement tous les transferts.</p>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Protection Anti-Bot et Anti-Sandwich</h3>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="antiBot">Protection Anti-Bot</Label>
                    <Switch
                      id="antiBot"
                      checked={tokenConfig?.protection?.antiBot || false}
                      onCheckedChange={(checked) => handleConfigChange("protection", "antiBot", checked)}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Empêche les bots d'effectuer des transactions automatisées.
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="antiSandwich">Protection Anti-Sandwich</Label>
                    <Switch
                      id="antiSandwich"
                      checked={tokenConfig?.protection?.antiSandwich || false}
                      onCheckedChange={(checked) => handleConfigChange("protection", "antiSandwich", checked)}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">Empêche les attaques sandwich sur les transactions.</p>
                </div>
                <div className="space-y-2 col-span-2">
                  <Label htmlFor="maxSlippage">Slippage Maximum (%)</Label>
                  <Input
                    id="maxSlippage"
                    type="number"
                    value={tokenConfig?.protection?.maxSlippage || 5}
                    onChange={(e) => handleConfigChange("protection", "maxSlippage", Number.parseFloat(e.target.value))}
                    min={0}
                    max={100}
                    step={0.1}
                  />
                  <p className="text-sm text-muted-foreground">
                    Limite le glissement de prix maximum autorisé pour les transactions.
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Mécanisme de Liste Noire</h3>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="blacklistEnabled">Liste Noire Activée</Label>
                    <Switch
                      id="blacklistEnabled"
                      checked={tokenConfig?.blacklist?.enabled || false}
                      onCheckedChange={(checked) => handleConfigChange("blacklist", "enabled", checked)}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">Permet de bloquer certaines adresses.</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="autoBlacklistThreshold">Seuil d'Auto-Blacklist (%)</Label>
                  <Input
                    id="autoBlacklistThreshold"
                    type="number"
                    value={tokenConfig?.blacklist?.autoBlacklistThreshold || 5}
                    onChange={(e) =>
                      handleConfigChange("blacklist", "autoBlacklistThreshold", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={100}
                    step={0.1}
                  />
                  <p className="text-sm text-muted-foreground">
                    % de l'approvisionnement total qui déclenche l'auto-blacklist.
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="daoUnblockingEnabled">Déblocage DAO Activé</Label>
                    <Switch
                      id="daoUnblockingEnabled"
                      checked={tokenConfig?.blacklist?.daoUnblockingEnabled || false}
                      onCheckedChange={(checked) => handleConfigChange("blacklist", "daoUnblockingEnabled", checked)}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Permet à la communauté de voter pour débloquer des adresses.
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="daoProposalThreshold">Seuil de Proposition DAO (%)</Label>
                  <Input
                    id="daoProposalThreshold"
                    type="number"
                    value={tokenConfig?.blacklist?.daoProposalThreshold || 1}
                    onChange={(e) =>
                      handleConfigChange("blacklist", "daoProposalThreshold", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={100}
                    step={0.1}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="fees" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Configuration des Frais</h3>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="baseFee">Frais de Base (%)</Label>
                  <Input
                    id="baseFee"
                    type="number"
                    value={tokenConfig?.fees?.baseFee || 2}
                    onChange={(e) => handleConfigChange("fees", "baseFee", Number.parseFloat(e.target.value))}
                    min={0}
                    max={100}
                    step={0.1}
                  />
                  <p className="text-sm text-muted-foreground">Frais appliqués à toutes les transactions.</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="additionalFee">Frais Additionnels (%)</Label>
                  <Input
                    id="additionalFee"
                    type="number"
                    value={tokenConfig?.fees?.additionalFee || 1}
                    onChange={(e) => handleConfigChange("fees", "additionalFee", Number.parseFloat(e.target.value))}
                    min={0}
                    max={100}
                    step={0.1}
                  />
                  <p className="text-sm text-muted-foreground">Frais supplémentaires pour certaines opérations.</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="antiExcessGainsTax">Taxe sur Gains Excessifs (%)</Label>
                  <Input
                    id="antiExcessGainsTax"
                    type="number"
                    value={tokenConfig?.fees?.antiExcessGainsTax || 5}
                    onChange={(e) =>
                      handleConfigChange("fees", "antiExcessGainsTax", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={100}
                    step={0.1}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="antiExcessGainsThreshold">Seuil de Gains Excessifs (%)</Label>
                  <Input
                    id="antiExcessGainsThreshold"
                    type="number"
                    value={tokenConfig?.fees?.antiExcessGainsThreshold || 20}
                    onChange={(e) =>
                      handleConfigChange("fees", "antiExcessGainsThreshold", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={1000}
                    step={1}
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Distribution des Frais</h3>
                <div className="text-sm">
                  Total:{" "}
                  <span className={calculateTotalFeeDistribution() === 100 ? "text-green-600" : "text-red-600"}>
                    {calculateTotalFeeDistribution()}%
                  </span>
                </div>
              </div>

              <Alert
                className={
                  calculateTotalFeeDistribution() === 100
                    ? "bg-green-50 border-green-200"
                    : "bg-amber-50 border-amber-200"
                }
              >
                <Info
                  className={`h-4 w-4 ${calculateTotalFeeDistribution() === 100 ? "text-green-600" : "text-amber-600"}`}
                />
                <AlertTitle className={calculateTotalFeeDistribution() === 100 ? "text-green-800" : "text-amber-800"}>
                  {calculateTotalFeeDistribution() === 100 ? "Distribution valide" : "Distribution incomplète"}
                </AlertTitle>
                <AlertDescription
                  className={calculateTotalFeeDistribution() === 100 ? "text-green-700" : "text-amber-700"}
                >
                  {calculateTotalFeeDistribution() === 100
                    ? "La distribution des frais est de 100%."
                    : `La distribution des frais doit être de 100% (actuellement ${calculateTotalFeeDistribution()}%).`}
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                {tokenConfig &&
                  Object.entries(tokenConfig.feeDistribution).map(([key, value]) => (
                    <div key={key} className="grid grid-cols-3 gap-4 items-center">
                      <Label htmlFor={`feeDistribution-${key}`}>{key}</Label>
                      <div className="col-span-2">
                        <div className="flex items-center space-x-2">
                          <Slider
                            id={`feeDistribution-${key}`}
                            value={[value]}
                            min={0}
                            max={100}
                            step={1}
                            onValueChange={(values) => handleConfigChange("feeDistribution", key, values[0])}
                            className="flex-1"
                          />
                          <div className="w-12 text-right">
                            <Input
                              type="number"
                              value={value}
                              onChange={(e) =>
                                handleConfigChange("feeDistribution", key, Number.parseInt(e.target.value) || 0)
                              }
                              min={0}
                              max={100}
                              className="w-full"
                            />
                          </div>
                          <div className="w-4">%</div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="limits" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Limites de Transaction</h3>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="maxTransactionSize">Taille Max. de Transaction</Label>
                  <Input
                    id="maxTransactionSize"
                    type="number"
                    value={tokenConfig?.transactionLimits?.maxTransactionSize || 0}
                    onChange={(e) =>
                      handleConfigChange("transactionLimits", "maxTransactionSize", Number.parseInt(e.target.value))
                    }
                    min={0}
                  />
                  <p className="text-sm text-muted-foreground">
                    Nombre maximum de tokens par transaction (0 = illimité).
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxWalletHolding">Détention Max. par Wallet</Label>
                  <Input
                    id="maxWalletHolding"
                    type="number"
                    value={tokenConfig?.transactionLimits?.maxWalletHolding || 0}
                    onChange={(e) =>
                      handleConfigChange("transactionLimits", "maxWalletHolding", Number.parseInt(e.target.value))
                    }
                    min={0}
                  />
                  <p className="text-sm text-muted-foreground">Nombre maximum de tokens par wallet (0 = illimité).</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxPurchasesPerBlock">Achats Max. par Bloc</Label>
                  <Input
                    id="maxPurchasesPerBlock"
                    type="number"
                    value={tokenConfig?.transactionLimits?.maxPurchasesPerBlock || 0}
                    onChange={(e) =>
                      handleConfigChange("transactionLimits", "maxPurchasesPerBlock", Number.parseInt(e.target.value))
                    }
                    min={0}
                  />
                  <p className="text-sm text-muted-foreground">Nombre maximum d'achats par bloc (0 = illimité).</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxSalesPerBlock">Ventes Max. par Bloc</Label>
                  <Input
                    id="maxSalesPerBlock"
                    type="number"
                    value={tokenConfig?.transactionLimits?.maxSalesPerBlock || 0}
                    onChange={(e) =>
                      handleConfigChange("transactionLimits", "maxSalesPerBlock", Number.parseInt(e.target.value))
                    }
                    min={0}
                  />
                  <p className="text-sm text-muted-foreground">Nombre maximum de ventes par bloc (0 = illimité).</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cooldownPeriod">Période de Refroidissement (secondes)</Label>
                  <Input
                    id="cooldownPeriod"
                    type="number"
                    value={tokenConfig?.transactionLimits?.cooldownPeriod || 0}
                    onChange={(e) =>
                      handleConfigChange("transactionLimits", "cooldownPeriod", Number.parseInt(e.target.value))
                    }
                    min={0}
                  />
                  <p className="text-sm text-muted-foreground">
                    Temps minimum entre deux transactions du même wallet (0 = désactivé).
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Configuration d'Impact sur les Prix</h3>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="priceImpactEnabled">Impact sur les Prix Activé</Label>
                    <Switch
                      id="priceImpactEnabled"
                      checked={tokenConfig?.priceImpact?.enabled || false}
                      onCheckedChange={(checked) => handleConfigChange("priceImpact", "enabled", checked)}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">Active le mécanisme d'impact sur les prix.</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="purchaseSupportThreshold">Seuil de Support d'Achat (%)</Label>
                  <Input
                    id="purchaseSupportThreshold"
                    type="number"
                    value={tokenConfig?.priceImpact?.purchaseSupportThreshold || 5}
                    onChange={(e) =>
                      handleConfigChange("priceImpact", "purchaseSupportThreshold", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={100}
                    step={0.1}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sellTrigger1">Déclencheur de Vente 1 (%)</Label>
                  <Input
                    id="sellTrigger1"
                    type="number"
                    value={tokenConfig?.priceImpact?.sellTrigger1 || 10}
                    onChange={(e) =>
                      handleConfigChange("priceImpact", "sellTrigger1", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={1000}
                    step={0.1}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sellAmount1">Montant de Vente 1 (%)</Label>
                  <Input
                    id="sellAmount1"
                    type="number"
                    value={tokenConfig?.priceImpact?.sellAmount1 || 5}
                    onChange={(e) =>
                      handleConfigChange("priceImpact", "sellAmount1", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={100}
                    step={0.1}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sellTrigger2">Déclencheur de Vente 2 (%)</Label>
                  <Input
                    id="sellTrigger2"
                    type="number"
                    value={tokenConfig?.priceImpact?.sellTrigger2 || 20}
                    onChange={(e) =>
                      handleConfigChange("priceImpact", "sellTrigger2", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={1000}
                    step={0.1}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sellAmount2">Montant de Vente 2 (%)</Label>
                  <Input
                    id="sellAmount2"
                    type="number"
                    value={tokenConfig?.priceImpact?.sellAmount2 || 10}
                    onChange={(e) =>
                      handleConfigChange("priceImpact", "sellAmount2", Number.parseFloat(e.target.value))
                    }
                    min={0}
                    max={100}
                    step={0.1}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minimumInterval">Intervalle Minimum (secondes)</Label>
                  <Input
                    id="minimumInterval"
                    type="number"
                    value={tokenConfig?.priceImpact?.minimumInterval || 300}
                    onChange={(e) =>
                      handleConfigChange("priceImpact", "minimumInterval", Number.parseInt(e.target.value))
                    }
                    min={0}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex space-x-2">
          <Button variant="outline" onClick={saveTokenConfig} disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sauvegarde...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Sauvegarder la Configuration
              </>
            )}
          </Button>
        </div>
        <div>
          {isDeploying && (
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span>Déploiement en cours...</span>
                <span>{deployProgress}%</span>
              </div>
              <Progress value={deployProgress} />
            </div>
          )}
          <Button onClick={createAndDeployToken} disabled={isDeploying}>
            {isDeploying ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Déploiement en cours...
              </>
            ) : (
              "Déployer le Token"
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

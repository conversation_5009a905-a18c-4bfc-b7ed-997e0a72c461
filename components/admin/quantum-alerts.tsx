"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  Bell,
  AlertCircle,
  CheckCircle,
  Clock,
  MoreHorizontal,
  Eye,
  CheckSquare,
  BellOff,
  Plus,
  Trash,
  Edit,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function QuantumAlerts() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Données simulées pour les alertes
  const alerts = [
    {
      id: 1,
      title: "Tentative d'accès non autorisé",
      description: "Plusieurs tentatives d'accès non autorisé détectées",
      severity: "high",
      status: "active",
      createdAt: "2023-04-28 10:45:12",
      source: "Security Module",
    },
    {
      id: 2,
      title: "Utilisation élevée de CPU",
      description: "L'utilisation du CPU a dépassé 90% pendant plus de 5 minutes",
      severity: "medium",
      status: "active",
      createdAt: "2023-04-27 15:30:45",
      source: "System Monitor",
    },
    {
      id: 3,
      title: "Échec de la sauvegarde automatique",
      description: "La sauvegarde automatique n'a pas pu être complétée",
      severity: "medium",
      status: "active",
      createdAt: "2023-04-26 09:15:33",
      source: "Backup Service",
    },
    {
      id: 4,
      title: "Activité suspecte sur le token SOLQ",
      description: "Transactions suspectes détectées sur le token SOLQ",
      severity: "high",
      status: "investigating",
      createdAt: "2023-04-25 14:22:18",
      source: "Token Monitor",
    },
    {
      id: 5,
      title: "Mise à jour de sécurité disponible",
      description: "Une mise à jour de sécurité importante est disponible pour le système",
      severity: "low",
      status: "active",
      createdAt: "2023-04-24 08:10:05",
      source: "Update Service",
    },
    {
      id: 6,
      title: "Problème de connexion RPC",
      description: "Problèmes intermittents avec la connexion RPC Solana",
      severity: "medium",
      status: "resolved",
      createdAt: "2023-04-23 00:00:00",
      source: "Network Monitor",
    },
    {
      id: 7,
      title: "Limite de création de tokens atteinte",
      description: "La limite quotidienne de création de tokens a été atteinte",
      severity: "low",
      status: "resolved",
      createdAt: "2023-04-22 11:30:22",
      source: "Token Factory",
    },
    {
      id: 8,
      title: "Erreur de validation de transaction",
      description: "Erreurs de validation de transactions détectées",
      severity: "high",
      status: "resolved",
      createdAt: "2023-04-21 16:45:37",
      source: "Transaction Validator",
    },
  ]

  // Filtrer les alertes en fonction du terme de recherche et du filtre de statut
  const filteredAlerts = alerts.filter(
    (alert) =>
      (alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.source.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (statusFilter === "all" || alert.status === statusFilter),
  )

  // Données simulées pour les règles d'alerte
  const alertRules = [
    {
      id: 1,
      name: "Détection d'accès non autorisé",
      description: "Alerte en cas de tentatives d'accès non autorisé",
      severity: "high",
      enabled: true,
      conditions: "Multiple failed login attempts",
    },
    {
      id: 2,
      name: "Surveillance de l'utilisation du CPU",
      description: "Alerte lorsque l'utilisation du CPU dépasse un seuil",
      severity: "medium",
      enabled: true,
      conditions: "CPU usage > 90% for 5 minutes",
    },
    {
      id: 3,
      name: "Surveillance des sauvegardes",
      description: "Alerte en cas d'échec de sauvegarde",
      severity: "medium",
      enabled: true,
      conditions: "Backup job fails",
    },
    {
      id: 4,
      name: "Détection d'activité suspecte sur les tokens",
      description: "Alerte en cas d'activité suspecte sur les tokens",
      severity: "high",
      enabled: true,
      conditions: "Unusual transaction patterns",
    },
    {
      id: 5,
      name: "Surveillance des mises à jour",
      description: "Alerte lorsqu'une mise à jour de sécurité est disponible",
      severity: "low",
      enabled: true,
      conditions: "Security update available",
    },
  ]

  // Fonction pour obtenir la couleur du badge en fonction de la sévérité
  const getSeverityBadgeVariant = (severity: string) => {
    switch (severity) {
      case "high":
        return "destructive"
      case "medium":
        return "warning"
      case "low":
        return "default"
      default:
        return "secondary"
    }
  }

  // Fonction pour obtenir la couleur du badge en fonction du statut
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "destructive"
      case "investigating":
        return "warning"
      case "resolved":
        return "success"
      default:
        return "secondary"
    }
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="active" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="active" className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            Alertes actives
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Historique
          </TabsTrigger>
          <TabsTrigger value="rules" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Règles d'alerte
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Alertes actives</CardTitle>
              <CardDescription>Alertes actives nécessitant votre attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Rechercher dans les alertes..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter} className="w-[180px] ml-2">
                  <SelectTrigger>
                    <SelectValue placeholder="Filtrer par statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les statuts</SelectItem>
                    <SelectItem value="active">Actives</SelectItem>
                    <SelectItem value="investigating">En cours d'investigation</SelectItem>
                    <SelectItem value="resolved">Résolues</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Sévérité</TableHead>
                      <TableHead>Titre</TableHead>
                      <TableHead>Source</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Date de création</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAlerts
                      .filter((alert) => alert.status !== "resolved")
                      .map((alert) => (
                        <TableRow key={alert.id}>
                          <TableCell>
                            <Badge variant={getSeverityBadgeVariant(alert.severity)}>
                              {alert.severity.charAt(0).toUpperCase() + alert.severity.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-medium">{alert.title}</TableCell>
                          <TableCell>{alert.source}</TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(alert.status)}>
                              {alert.status.charAt(0).toUpperCase() + alert.status.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell>{alert.createdAt}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Ouvrir le menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem className="flex items-center">
                                  <Eye className="mr-2 h-4 w-4" />
                                  Voir les détails
                                </DropdownMenuItem>
                                <DropdownMenuItem className="flex items-center">
                                  <CheckSquare className="mr-2 h-4 w-4" />
                                  Marquer comme résolu
                                </DropdownMenuItem>
                                <DropdownMenuItem className="flex items-center">
                                  <BellOff className="mr-2 h-4 w-4" />
                                  Ignorer
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    {filteredAlerts.filter((alert) => alert.status !== "resolved").length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4">
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <CheckCircle className="h-8 w-8 mb-2" />
                            <p>Aucune alerte active</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Historique des alertes</CardTitle>
              <CardDescription>Historique complet des alertes du système</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Rechercher dans l'historique..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter} className="w-[180px] ml-2">
                  <SelectTrigger>
                    <SelectValue placeholder="Filtrer par statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les statuts</SelectItem>
                    <SelectItem value="active">Actives</SelectItem>
                    <SelectItem value="investigating">En cours d'investigation</SelectItem>
                    <SelectItem value="resolved">Résolues</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Sévérité</TableHead>
                      <TableHead>Titre</TableHead>
                      <TableHead>Source</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Date de création</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAlerts.map((alert) => (
                      <TableRow key={alert.id}>
                        <TableCell>
                          <Badge variant={getSeverityBadgeVariant(alert.severity)}>
                            {alert.severity.charAt(0).toUpperCase() + alert.severity.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-medium">{alert.title}</TableCell>
                        <TableCell>{alert.source}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(alert.status)}>
                            {alert.status.charAt(0).toUpperCase() + alert.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>{alert.createdAt}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">Voir les détails</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rules" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Règles d'alerte</CardTitle>
                  <CardDescription>Configurer les règles pour la génération automatique d'alertes</CardDescription>
                </div>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Ajouter une règle
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Sévérité</TableHead>
                      <TableHead>Conditions</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {alertRules.map((rule) => (
                      <TableRow key={rule.id}>
                        <TableCell className="font-medium">{rule.name}</TableCell>
                        <TableCell>{rule.description}</TableCell>
                        <TableCell>
                          <Badge variant={getSeverityBadgeVariant(rule.severity)}>
                            {rule.severity.charAt(0).toUpperCase() + rule.severity.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>{rule.conditions}</TableCell>
                        <TableCell>
                          <Badge variant={rule.enabled ? "success" : "secondary"}>
                            {rule.enabled ? "Activée" : "Désactivée"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Ouvrir le menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem className="flex items-center">
                                <Eye className="mr-2 h-4 w-4" />
                                Voir les détails
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center">
                                <Edit className="mr-2 h-4 w-4" />
                                Modifier
                              </DropdownMenuItem>
                              {rule.enabled ? (
                                <DropdownMenuItem className="flex items-center">
                                  <BellOff className="mr-2 h-4 w-4" />
                                  Désactiver
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem className="flex items-center">
                                  <Bell className="mr-2 h-4 w-4" />
                                  Activer
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="flex items-center text-destructive">
                                <Trash className="mr-2 h-4 w-4" />
                                Supprimer
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

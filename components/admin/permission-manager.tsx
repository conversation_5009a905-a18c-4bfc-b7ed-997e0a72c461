"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Save, ShieldCheck, ShieldX } from "lucide-react"
import { useAdmin } from "@/hooks/use-admin"

interface Permission {
  id: string
  name: string
  description: string
}

interface AdminUser {
  walletAddress: string
  role: "admin" | "superadmin"
  permissions?: string[]
  addedAt: string
  addedBy?: string
}

export function PermissionManager({ adminWallet }: { adminWallet: string }) {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])
  const [adminData, setAdminData] = useState<AdminUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const { toast } = useToast()
  const { isSuperAdmin } = useAdmin()

  useEffect(() => {
    fetchPermissions()
    fetchAdminData()
  }, [adminWallet])

  const fetchPermissions = async () => {
    try {
      const response = await fetch("/api/admin/permissions")
      if (!response.ok) throw new Error("Failed to fetch permissions")

      const data = await response.json()
      setPermissions(data.permissions || [])
    } catch (error) {
      console.error("Error fetching permissions:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les permissions",
        variant: "destructive",
      })
    }
  }

  const fetchAdminData = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/admin/users?wallet=${adminWallet}`)
      if (!response.ok) throw new Error("Failed to fetch admin data")

      const data = await response.json()
      setAdminData(data.admin || null)
      setSelectedPermissions(data.admin?.permissions || [])
    } catch (error) {
      console.error("Error fetching admin data:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de l'administrateur",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions([...selectedPermissions, permissionId])
    } else {
      setSelectedPermissions(selectedPermissions.filter((id) => id !== permissionId))
    }
  }

  const savePermissions = async () => {
    if (!adminData || adminData.role === "superadmin") return

    setIsSaving(true)
    try {
      const response = await fetch("/api/admin/permissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          walletAddress: adminWallet,
          permissions: selectedPermissions,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update permissions")
      }

      toast({
        title: "Permissions mises à jour",
        description: "Les permissions de l'administrateur ont été mises à jour avec succès",
      })
    } catch (error) {
      console.error("Error updating permissions:", error)
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Impossible de mettre à jour les permissions",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6 flex justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    )
  }

  if (!adminData) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <ShieldX className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">Administrateur non trouvé</h3>
            <p className="text-muted-foreground mt-2">Impossible de trouver les informations pour cet administrateur</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const isSuperAdminUser = adminData.role === "superadmin"

  return (
    <Card>
      <CardHeader>
        <CardTitle>Gestion des permissions</CardTitle>
        <CardDescription>
          {isSuperAdminUser
            ? "Les super administrateurs ont toutes les permissions par défaut"
            : "Configurer les permissions pour cet administrateur"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isSuperAdminUser ? (
          <div className="flex flex-col items-center justify-center py-4 text-center">
            <ShieldCheck className="h-12 w-12 text-primary mb-4" />
            <h3 className="text-lg font-medium">Super Administrateur</h3>
            <p className="text-muted-foreground mt-2">
              Les super administrateurs ont automatiquement accès à toutes les fonctionnalités
            </p>
          </div>
        ) : (
          <div className="grid gap-4">
            {permissions.map((permission) => (
              <div key={permission.id} className="flex items-start space-x-3 space-y-0">
                <Checkbox
                  id={`permission-${permission.id}`}
                  checked={selectedPermissions.includes(permission.id)}
                  onCheckedChange={(checked) => handlePermissionChange(permission.id, checked === true)}
                  disabled={!isSuperAdmin || isSaving}
                />
                <div className="space-y-1 leading-none">
                  <Label
                    htmlFor={`permission-${permission.id}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {permission.name}
                  </Label>
                  <p className="text-sm text-muted-foreground">{permission.description}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      {!isSuperAdminUser && isSuperAdmin && (
        <CardFooter>
          <Button onClick={savePermissions} disabled={isSaving} className="ml-auto">
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Save className={`h-4 w-4 ${isSaving ? "" : "mr-2"}`} />
            {!isSaving && "Enregistrer les permissions"}
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}

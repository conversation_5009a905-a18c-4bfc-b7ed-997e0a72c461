"use client"

import { useState } from "react"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Shield, Lock, AlertTriangle } from "lucide-react"

export default function QuantumSecuritySettings() {
  const [antiRugEnabled, setAntiRugEnabled] = useState(true)
  const [liquidityLockDays, setLiquidityLockDays] = useState(180)
  const [ownershipRenounced, setOwnershipRenounced] = useState(false)
  const [maxTransactionPercentage, setMaxTransactionPercentage] = useState(2)
  const [maxWalletPercentage, setMaxWalletPercentage] = useState(3)
  const [tradingCooldown, setTradingCooldown] = useState(60)
  const [securityAuditLevel, setSecurityAuditLevel] = useState("standard")

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Shield className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-medium">Anti-Rug Protection</h3>
      </div>

      <div className="grid gap-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="anti-rug">Enable Anti-Rug Protection</Label>
            <p className="text-sm text-muted-foreground">
              Prevents token creators from removing liquidity unexpectedly
            </p>
          </div>
          <Switch id="anti-rug" checked={antiRugEnabled} onCheckedChange={setAntiRugEnabled} />
        </div>

        <div>
          <div className="flex justify-between mb-2">
            <Label htmlFor="liquidity-lock">Liquidity Lock Period (Days)</Label>
            <span className="text-sm">{liquidityLockDays} days</span>
          </div>
          <Slider
            id="liquidity-lock"
            disabled={!antiRugEnabled}
            value={[liquidityLockDays]}
            onValueChange={(value) => setLiquidityLockDays(value[0])}
            max={365}
            min={30}
            step={30}
          />
          <div className="flex justify-between mt-1 text-xs text-muted-foreground">
            <span>30 days</span>
            <span>180 days</span>
            <span>365 days</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="ownership-renounce">Renounce Ownership</Label>
            <p className="text-sm text-muted-foreground">Permanently removes owner privileges after deployment</p>
          </div>
          <Switch id="ownership-renounce" checked={ownershipRenounced} onCheckedChange={setOwnershipRenounced} />
        </div>
      </div>

      <Separator />

      <div className="flex items-center space-x-2">
        <Lock className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-medium">Transaction Limits</h3>
      </div>

      <div className="grid gap-6">
        <div>
          <div className="flex justify-between mb-2">
            <Label htmlFor="max-tx">Max Transaction (% of Supply)</Label>
            <span className="text-sm">{maxTransactionPercentage}%</span>
          </div>
          <Slider
            id="max-tx"
            value={[maxTransactionPercentage]}
            onValueChange={(value) => setMaxTransactionPercentage(value[0])}
            max={10}
            min={0.1}
            step={0.1}
          />
        </div>

        <div>
          <div className="flex justify-between mb-2">
            <Label htmlFor="max-wallet">Max Wallet Size (% of Supply)</Label>
            <span className="text-sm">{maxWalletPercentage}%</span>
          </div>
          <Slider
            id="max-wallet"
            value={[maxWalletPercentage]}
            onValueChange={(value) => setMaxWalletPercentage(value[0])}
            max={10}
            min={0.5}
            step={0.5}
          />
        </div>

        <div>
          <Label htmlFor="trading-cooldown">Trading Cooldown (minutes)</Label>
          <Input
            id="trading-cooldown"
            type="number"
            value={tradingCooldown}
            onChange={(e) => setTradingCooldown(Number.parseInt(e.target.value))}
            className="mt-1"
          />
          <p className="text-xs text-muted-foreground mt-1">Time after launch before trading is enabled</p>
        </div>
      </div>

      <Separator />

      <div className="flex items-center space-x-2">
        <AlertTriangle className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-medium">Audit & Verification</h3>
      </div>

      <div className="grid gap-6">
        <div>
          <Label htmlFor="audit-level">Security Audit Level</Label>
          <Select value={securityAuditLevel} onValueChange={setSecurityAuditLevel}>
            <SelectTrigger id="audit-level" className="mt-1">
              <SelectValue placeholder="Select audit level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="basic">Basic (Automated Checks)</SelectItem>
              <SelectItem value="standard">Standard (Manual Review)</SelectItem>
              <SelectItem value="advanced">Advanced (Full Audit)</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground mt-1">Level of security verification for new tokens</p>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-primary/10 rounded-full">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h4 className="font-medium">Security Recommendation</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  For maximum security, we recommend enabling anti-rug protection with at least 180 days liquidity lock
                  and setting reasonable transaction limits.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, Info } from "lucide-react"
import TokenSuffixService from "@/lib/token-suffix-service"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"

export default function SuffixConfiguration() {
  const [suffix, setSuffix] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const { toast } = useToast()
  const { activeNetwork } = useNetwork()
  const [estimatedTime, setEstimatedTime] = useState<string | null>(null)

  // Charger le suffixe existant
  useEffect(() => {
    const loadSuffix = async () => {
      try {
        const currentSuffix = await TokenSuffixService.getSuffixForNetwork(activeNetwork.id)
        setSuffix(currentSuffix)

        // Estimer le temps de grinding
        updateEstimatedTime(currentSuffix)
      } catch (error) {
        console.error("Error loading suffix:", error)
      }
    }

    loadSuffix()
  }, [activeNetwork])

  const updateEstimatedTime = (suffixValue: string) => {
    if (!suffixValue) return

    const estimatedSeconds = TokenSuffixService.estimateGrindingTime(suffixValue)
    const formattedTime = TokenSuffixService.formatEstimatedTime(estimatedSeconds)
    setEstimatedTime(formattedTime)
  }

  const handleSaveSuffix = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Valider le suffixe
      const validation = TokenSuffixService.validateSuffix(suffix)
      if (!validation.valid) {
        setError(validation.error || "Suffixe invalide")
        setIsLoading(false)
        return
      }

      // Simuler l'enregistrement du suffixe
      // Dans une implémentation réelle, cela serait enregistré dans une base de données
      console.log(`Suffixe configuré pour le réseau ${activeNetwork.id}: ${suffix}`)

      // Mettre à jour l'estimation du temps
      updateEstimatedTime(suffix)

      // Afficher un message de succès
      setSuccess(`Suffixe "${suffix}" configuré avec succès pour le réseau ${activeNetwork.id}`)
      toast({
        title: "Configuration enregistrée",
        description: `Le suffixe "${suffix}" a été configuré avec succès`,
      })
    } catch (err: any) {
      console.error("Erreur lors de l'enregistrement du suffixe:", err)
      setError(err.message || "Une erreur s'est produite lors de l'enregistrement du suffixe")
      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors de l'enregistrement du suffixe",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Configuration du suffixe de token</CardTitle>
        <CardDescription>
          Configurez le suffixe qui sera utilisé pour toutes les adresses de token créées par les utilisateurs
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Succès</AlertTitle>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="suffix">Suffixe de token</Label>
          <Input
            id="suffix"
            placeholder="ex: GF"
            value={suffix}
            onChange={(e) => setSuffix(e.target.value.toUpperCase())}
            maxLength={5}
          />
          <p className="text-xs text-muted-foreground">
            Le suffixe qui sera utilisé pour toutes les adresses de token créées par les utilisateurs
          </p>
        </div>

        {estimatedTime && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Temps estimé pour la génération</AlertTitle>
            <AlertDescription>
              Avec le suffixe "{suffix}", la génération d'une adresse prendra environ {estimatedTime}.
              <br />
              <span className="text-xs text-muted-foreground">
                Plus le suffixe est long, plus la génération prendra du temps.
              </span>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveSuffix} disabled={isLoading}>
          {isLoading ? "Enregistrement..." : "Enregistrer la configuration"}
        </Button>
      </CardFooter>
    </Card>
  )
}

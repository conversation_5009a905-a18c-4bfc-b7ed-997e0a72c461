"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import {
  Activity,
  AlertTriangle,
  ArrowDown,
  ArrowUp,
  Clock,
  Coins,
  DollarSign,
  Download,
  Loader2,
  RefreshCw,
  Users,
} from "lucide-react"
import { useAdmin } from "@/hooks/use-admin"

// Importation fictive pour les graphiques - dans un projet réel, utilisez une bibliothèque comme recharts
const BarChart = ({ data }: { data: any }) => (
  <div className="h-[200px] w-full bg-muted/20 rounded-md flex items-end justify-around p-2">
    {data.map((item: any, index: number) => (
      <div key={index} className="flex flex-col items-center">
        <div
          className="bg-primary w-12 rounded-t-sm"
          style={{ height: `${(item.value / Math.max(...data.map((d: any) => d.value))) * 150}px` }}
        ></div>
        <span className="text-xs mt-1">{item.label}</span>
      </div>
    ))}
  </div>
)

const LineChart = ({ data }: { data: any }) => (
  <div className="h-[200px] w-full bg-muted/20 rounded-md p-2 relative">
    <div className="absolute inset-0 flex items-center justify-center">
      <span className="text-muted-foreground">Graphique linéaire (simulé)</span>
    </div>
  </div>
)

interface PlatformStats {
  totalUsers: number
  totalTokens: number
  totalTransactions: number
  totalFees: number
  activePools: number
  nftsMinted: number
  dailyActiveUsers: number
  weeklyActiveUsers: number
  monthlyRevenue: number
  tokenCreationRate: number
  userGrowth: number
  userGrowthPercentage: number
  revenueGrowth: number
  revenueGrowthPercentage: number
}

interface RecentActivity {
  id: string
  type: string
  user: string
  details: string
  timestamp: string
}

export function EnhancedAdminDashboard() {
  const [stats, setStats] = useState<PlatformStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const { toast } = useToast()
  const { checkPermission } = useAdmin()

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API
      setTimeout(() => {
        // Données fictives pour la démonstration
        const mockStats: PlatformStats = {
          totalUsers: 1250,
          totalTokens: 35,
          totalTransactions: 5420,
          totalFees: 75000,
          activePools: 10,
          nftsMinted: 500,
          dailyActiveUsers: 320,
          weeklyActiveUsers: 875,
          monthlyRevenue: 12500,
          tokenCreationRate: 0.8,
          userGrowth: 125,
          userGrowthPercentage: 11.2,
          revenueGrowth: 1500,
          revenueGrowthPercentage: 13.6,
        }

        const mockActivity: RecentActivity[] = [
          {
            id: "act1",
            type: "token_creation",
            user: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM",
            details: "Token SOLX créé avec succès",
            timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
          },
          {
            id: "act2",
            type: "user_registration",
            user: "5YNmS1R9nNSCDzb5a7mMJ1dwK9uHeAAF4CerVnZgbdr3",
            details: "Nouvel utilisateur inscrit",
            timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
          },
          {
            id: "act3",
            type: "transaction",
            user: "GfEHHkrQY45ZPoKC9srbjKZiCnwEWNVTAn3UxTGTZgwT",
            details: "Transaction de 500 SOL effectuée",
            timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
          },
          {
            id: "act4",
            type: "nft_mint",
            user: "BXvEuaBKzLJtZQxwKWgmUEJWAM9JYiVEYBT8HPZjmXbU",
            details: "NFT #123 créé dans la collection Alpha",
            timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),
          },
          {
            id: "act5",
            type: "pool_creation",
            user: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM",
            details: "Nouveau pool de liquidité créé pour SOLX/SOL",
            timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString(),
          },
        ]

        setStats(mockStats)
        setRecentActivity(mockActivity)
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error("Error fetching dashboard data:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données du tableau de bord",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("fr-FR").format(num)
  }

  const formatCurrency = (num: number) => {
    return new Intl.NumberFormat("fr-FR", { style: "currency", currency: "USD" }).format(num)
  }

  const formatWalletAddress = (address: string) => {
    return `${address.substring(0, 4)}...${address.substring(address.length - 4)}`
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const date = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return "À l'instant"
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `Il y a ${diffInHours}h`

    const diffInDays = Math.floor(diffInHours / 24)
    return `Il y a ${diffInDays}j`
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "token_creation":
        return <Coins className="h-4 w-4 text-green-500" />
      case "user_registration":
        return <Users className="h-4 w-4 text-blue-500" />
      case "transaction":
        return <DollarSign className="h-4 w-4 text-yellow-500" />
      case "nft_mint":
        return <Activity className="h-4 w-4 text-purple-500" />
      case "pool_creation":
        return <Activity className="h-4 w-4 text-cyan-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  // Données fictives pour les graphiques
  const userChartData = [
    { label: "Lun", value: 210 },
    { label: "Mar", value: 250 },
    { label: "Mer", value: 300 },
    { label: "Jeu", value: 280 },
    { label: "Ven", value: 320 },
    { label: "Sam", value: 290 },
    { label: "Dim", value: 350 },
  ]

  const tokenChartData = [
    { label: "Lun", value: 2 },
    { label: "Mar", value: 3 },
    { label: "Mer", value: 1 },
    { label: "Jeu", value: 4 },
    { label: "Ven", value: 2 },
    { label: "Sam", value: 5 },
    { label: "Dim", value: 3 },
  ]

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
        <h3 className="text-lg font-medium">Données non disponibles</h3>
        <p className="text-muted-foreground mt-2">Impossible de charger les données du tableau de bord</p>
        <Button onClick={fetchDashboardData} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Réessayer
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Cartes de statistiques principales */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs totaux</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.totalUsers)}</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              {stats.userGrowthPercentage > 0 ? (
                <>
                  <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-green-500">{stats.userGrowthPercentage}%</span>
                </>
              ) : (
                <>
                  <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-red-500">{Math.abs(stats.userGrowthPercentage)}%</span>
                </>
              )}
              <span className="ml-1">depuis le mois dernier</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tokens créés</CardTitle>
            <Coins className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.totalTokens)}</div>
            <p className="text-xs text-muted-foreground mt-1">~{stats.tokenCreationRate} tokens/jour</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenus mensuels</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.monthlyRevenue)}</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              {stats.revenueGrowthPercentage > 0 ? (
                <>
                  <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-green-500">{stats.revenueGrowthPercentage}%</span>
                </>
              ) : (
                <>
                  <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-red-500">{Math.abs(stats.revenueGrowthPercentage)}%</span>
                </>
              )}
              <span className="ml-1">depuis le mois dernier</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs actifs</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.dailyActiveUsers)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {formatNumber(stats.weeklyActiveUsers)} utilisateurs hebdomadaires
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Onglets pour différentes vues */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="users">Utilisateurs</TabsTrigger>
          <TabsTrigger value="tokens">Tokens</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Graphique des utilisateurs */}
            <Card>
              <CardHeader>
                <CardTitle>Utilisateurs actifs (7 derniers jours)</CardTitle>
                <CardDescription>Évolution du nombre d'utilisateurs actifs</CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart data={userChartData} />
              </CardContent>
            </Card>

            {/* Graphique des tokens */}
            <Card>
              <CardHeader>
                <CardTitle>Création de tokens (7 derniers jours)</CardTitle>
                <CardDescription>Nombre de tokens créés par jour</CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart data={tokenChartData} />
              </CardContent>
            </Card>
          </div>

          {/* Activité récente */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Activité récente</CardTitle>
                <CardDescription>Les dernières actions sur la plateforme</CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Actualiser
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-4">
                    <div className="rounded-full bg-muted p-2">{getActivityIcon(activity.type)}</div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{activity.details}</p>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <span>Par {formatWalletAddress(activity.user)}</span>
                        <span className="mx-1">•</span>
                        <span>{formatTimeAgo(activity.timestamp)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                Voir toute l'activité
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analyse des utilisateurs</CardTitle>
              <CardDescription>Statistiques détaillées sur les utilisateurs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                <span className="text-muted-foreground">Graphique d'analyse des utilisateurs (simulé)</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokens" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analyse des tokens</CardTitle>
              <CardDescription>Statistiques détaillées sur les tokens</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                <span className="text-muted-foreground">Graphique d'analyse des tokens (simulé)</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analyse des transactions</CardTitle>
              <CardDescription>Statistiques détaillées sur les transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
                <span className="text-muted-foreground">Graphique d'analyse des transactions (simulé)</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Actions rapides */}
      <Card>
        <CardHeader>
          <CardTitle>Actions rapides</CardTitle>
          <CardDescription>Accès rapide aux fonctionnalités principales</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Users className="h-5 w-5 mb-2" />
              <span>Gérer les utilisateurs</span>
            </Button>
            <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Coins className="h-5 w-5 mb-2" />
              <span>Créer un token</span>
            </Button>
            <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Download className="h-5 w-5 mb-2" />
              <span>Exporter les rapports</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

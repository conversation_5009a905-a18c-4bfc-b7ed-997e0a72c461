"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertCircle,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  FileText,
  Users,
  Shield,
  Coins,
  DollarSign,
  Activity,
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@solana/wallet-adapter-react"
import { useNetwork } from "@/contexts/network-context"
import {
  getPlatformStats,
  getNetworkKeys,
  updateNetworkKeys,
  getInternalWallets,
  getTransactionHistory,
  performSecurityAudit,
  generateAuditReport,
  getUserTokenConfig,
  updateUserTokenConfig,
  getEcosystemTokenConfig,
  updateEcosystemTokenConfig,
  getStakingConfig,
  updateStakingConfig,
  getPresaleConfig,
  getNftConfig,
  getTelegramBotConfig,
  getApiConfig,
  getSocialMediaConfig,
  getGameConfig,
  getStablecoinConfig,
  updateStablecoinConfig,
  exportFinancialData,
  getUserRoles,
  isAdminWallet,
} from "@/lib/admin-service"

export default function EnhancedAdminDashboard() {
  const { publicKey } = useWallet()
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()
  
  // Admin access state
  const [isAdmin, setIsAdmin] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  
  // Dashboard data
  const [stats, setStats] = useState<any>(null)
  const [networkKeys, setNetworkKeys] = useState<any>(null)
  const [internalWallets, setInternalWallets] = useState<any[]>([])
  const [transactions, setTransactions] = useState<any[]>([])
  const [auditResults, setAuditResults] = useState<any>(null)
  const [userTokenConfig, setUserTokenConfig] = useState<any>(null)
  const [ecosystemTokenConfig, setEcosystemTokenConfig] = useState<any>(null)
  const [stakingConfig, setStakingConfig] = useState<any>(null)
  const [presaleConfig, setPresaleConfig] = useState<any>(null)
  const [nftConfig, setNftConfig] = useState<any>(null)
  const [telegramConfig, setTelegramConfig] = useState<any>(null)
  const [apiConfig, setApiConfig] = useState<any>(null)
  const [socialMediaConfig, setSocialMediaConfig] = useState<any>(null)
  const [gameConfig, setGameConfig] = useState<any>(null)
  const [stablecoinConfig, setStablecoinConfig] = useState<any>(null)
  const [userRoles, setUserRoles] = useState<any[]>([])
  
  // UI state
  const [activeTab, setActiveTab] = useState("overview")
  const [activeSubTab, setActiveSubTab] = useState("general")
  const [activeAuditTab, setActiveAuditTab] = useState("contract")
  const [isAuditing, setIsAuditing] = useState(false)
  const [auditProgress, setAuditProgress] = useState(0)
  const [exportFormat, setExportFormat] = useState<"excel" | "pdf">("excel")
  const [exportPeriod, setExportPeriod] = useState<"day" | "week" | "month" | "year">("month")
  
  // Check if the connected wallet is an admin
  useEffect(() => {
    const checkAdminAccess = async () => {
      if (publicKey) {
        try {
          const admin = await isAdminWallet(publicKey.toString())
          setIsAdmin(admin)
        } catch (error) {
          console.error("Error checking admin access:", error)
          setIsAdmin(false)
        }
      } else {
        setIsAdmin(false)
      }
      setIsLoading(false)
    }
    
    checkAdminAccess()
  }, [publicKey])
  
  // Load dashboard data
  useEffect(() => {
    if (isAdmin) {
      loadDashboardData()
    }
  }, [isAdmin])
  
  // Load all dashboard data
  const loadDashboardData = async () => {
    try {
      setIsLoading(true)
      
      // Load all data in parallel
      const [
        statsData,
        keysData,
        walletsData,
        txData,
        auditData,
        userTokenData,
        ecosystemData,
        stakingData,
        presaleData,
        nftData,
        telegramData,
        apiData,
        socialData,
        gameData,
        stablecoinData,
        rolesData,
      ] = await Promise.all([
        getPlatformStats(),
        getNetworkKeys(),
        getInternalWallets(),
        getTransactionHistory(10),
        performSecurityAudit(),
        getUserTokenConfig(),
        getEcosystemTokenConfig(),
        getStakingConfig(),
        getPresaleConfig(),
        getNftConfig(),
        getTelegramBotConfig(),
        getApiConfig(),
        getSocialMediaConfig(),
        getGameConfig(),
        getStablecoinConfig(),
        getUserRoles(),
      ])
      
      // Set state with loaded data
      setStats(statsData)
      setNetworkKeys(keysData)
      setInternalWallets(walletsData)
      setTransactions(txData)
      setAuditResults(auditData)
      setUserTokenConfig(userTokenData)
      setEcosystemTokenConfig(ecosystemData)
      setStakingConfig(stakingData)
      setPresaleConfig(presaleData)
      setNftConfig(nftData)
      setTelegramConfig(telegramData)
      setApiConfig(apiData)
      setSocialMediaConfig(socialData)
      setGameConfig(gameData)
      setStablecoinConfig(stablecoinData)
      setUserRoles(rolesData)
      
      setIsLoading(false)
    } catch (error) {
      console.error("Error loading dashboard data:", error)
      toast({
        title: "Error",
        description: "Failed to load dashboard data. Please try again.",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }
  
  // Run security audit
  const runSecurityAudit = async () => {
    try {
      setIsAuditing(true)
      setAuditProgress(0)
      
      // Simulate audit progress
      const interval = setInterval(() => {
        setAuditProgress((prev) => {
          if (prev >= 95) {
            clearInterval(interval)
            return 95
          }
          return prev + 5
        })
      }, 200)
      
      // Perform the actual audit
      const results = await performSecurityAudit()
      setAuditResults(results)
      
      // Complete the progress
      clearInterval(interval)
      setAuditProgress(100)
      
      toast({
        title: "Audit Complete",
        description: "Security audit has been completed successfully.",
      })
      
      setTimeout(() => {
        setIsAuditing(false)
      }, 500)
    } catch (error) {
      console.error("Error running security audit:", error)
      toast({
        title: "Audit Failed",
        description: "Failed to complete security audit. Please try again.",
        variant: "destructive",
      })
      setIsAuditing(false)
    }
  }
  
  // Generate audit report
  const handleGenerateReport = async (type: "public" | "internal") => {
    try {
      const report = await generateAuditReport(type)
      
      toast({
        title: "Report Generated",
        description: `${type.charAt(0).toUpperCase() + type.slice(1)} audit report has been generated.`,
      })
      
      // In a real implementation, this would download the report
      console.log(`Report URL: ${report.url}`)
    } catch (error) {
      console.error("Error generating audit report:", error)
      toast({
        title: "Report Generation Failed",
        description: "Failed to generate audit report. Please try again.",
        variant: "destructive",
      })
    }
  }
  
  // Update network keys
  const handleUpdateNetworkKeys = async () => {
    try {
      await updateNetworkKeys(networkKeys)
      
      toast({
        title: "Network Keys Updated",
        description: "Network configuration has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating network keys:", error)
      toast({
        title: "Update Failed",
        description: "Failed to update network configuration. Please try again.",
        variant: "destructive",
      })
    }
  }
  
  // Update user token configuration
  const handleUpdateUserTokenConfig = async () => {
    try {
      await updateUserTokenConfig(userTokenConfig)
      
      toast({
        title: "User Token Configuration Updated",
        description: "User token configuration has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating user token config:", error)
      toast({
        title: "Update Failed",
        description: "Failed to update user token configuration. Please try again.",
        variant: "destructive",
      })
    }
  }
  
  // Export financial data
  const handleExportFinancialData = async () => {
    try {
      const result = await exportFinancialData(exportFormat, exportPeriod)
      
      toast({
        title: "Export Successful",
        description: `Financial data has been exported as ${result.filename}.`,
      })
      
      // In a real implementation, this would download the file
      console.log(`Export URL: ${result.url}`)
    } catch (error) {
      console.error("Error exporting financial data:", error)
      toast({
        title: "Export Failed",
        description: "Failed to export financial data. Please try again.",
        variant: "destructive",
      })
    }
  }
  
  // If not admin or loading, show appropriate message
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }
  
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>You do not have permission to access the admin dashboard.</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Unauthorized</AlertTitle>
              <AlertDescription>
                This area is restricted to admin wallets only. Please connect with an admin wallet to continue.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" onClick={() => window.location.href = "/"}>
              Return to Home
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }
  
  // Render the admin dashboard
  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
            {activeNetwork.name}
          </Badge>
          <Button variant="outline" size="sm" onClick={loadDashboardData}>
            Refresh Data
          </Button>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 w-full">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="tokens">Tokens</TabsTrigger>
          <TabsTrigger value="platform">Platform</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Users className="mr-2 h-4 w-4" />
                    Users
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalUsers}</div>
                  <p className="text-xs text-muted-foreground">Total registered users</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Coins className="mr-2 h-4 w-4" />
                    Tokens
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalTokens}</div>
                  <p className="text-xs text-muted-foreground">Total tokens created</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Activity className="mr-2 h-4 w-4" />
                    Transactions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalTransactions}</div>
                  <p className="text-xs text-muted-foreground">Total transactions processed</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Revenue
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${stats.totalFees.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">Total platform revenue</p>
                </CardContent>
              </Card>
            </div>
          )}
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Latest platform transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Type</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Wallet</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((tx) => (
                      <TableRow key={tx.id}>
                        <TableCell className="font-medium">
                          {tx.type.replace("_", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                        </TableCell>
                        <TableCell>
                          {tx.amount} {tx.currency}
                        </TableCell>
                        <TableCell className="font-mono text-xs">{tx.wallet}</TableCell>
                        <TableCell>
                          {tx.status === "success" ? (
                            <Badge className="bg-green-100 text-green-800 border-green-300">Success</Badge>
                          ) : (
                            <Badge className="bg-red-100 text-red-800 border-red-300">Failed</Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" className="ml-auto">
                  View All Transactions
                </Button>
              </CardFooter>
            </Card>
            
            {/* Internal Wallets */}
            <Card>
              <CardHeader>
                <CardTitle>Internal Wallets</CardTitle>
                <CardDescription>Platform wallet balances</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Address</TableHead>
                      <TableHead>SOL Balance</TableHead>
                      <TableHead>BNB Balance</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {internalWallets.map((wallet, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{wallet.name}</TableCell>
                        <TableCell className="font-mono text-xs">{wallet.address.substring(0, 8)}...</TableCell>
                        <TableCell>{wallet.balance.solana} SOL</TableCell>
                        <TableCell>{wallet.balance.bnb} BNB</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" className="ml-auto">
                  Manage Wallets
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Security Audit
              </CardTitle>
              <CardDescription>Comprehensive security analysis of smart contracts and platform components</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {isAuditing && (
                <div className="mb-6 space-y-2">
                  <div className="flex justify-between">
                    <span>Running security audit...</span>
                    <span>{auditProgress}%</span>
                  </div>
                  <Progress value={auditProgress} />
                </div>
              )}
              
              {auditResults && (
                <>
                  <div className="grid grid-cols-4 gap-4">
                    <Card className="bg-green-50">
                      <CardContent className="p-4 flex flex-col items-center justify-center">
                        <CheckCircle2 className="h-8 w-8 text-green-500 mb-2" />
                        <div className="text-2xl font-bold">{auditResults.summary.passCount}</div>
                        <p className="text-sm text-green-700">Passed</p>
                      </CardContent>
                    </Card>
                    <Card className="bg-yellow-50">
                      <CardContent className="p-4 flex flex-col items-center justify-center">
                        <AlertTriangle className="h-8 w-8 text-yellow-500 mb-2" />
                        <div className="text-2xl font-bold">{auditResults.summary.warningCount}</div>
                        <p className="text-sm text-yellow-700">Warnings</p>
                      </CardContent>
                    </Card>
                    <Card className="bg-red-50">
                      <CardContent className="p-4 flex flex-col items-center justify-center">
                        <XCircle className="h-8 w-8 text-red-500 mb-2" />
                        <div className="text-2xl font-bold">{auditResults.summary.failCount}</div>
                        <p className="text-sm text-red-700">Failed</p>
                      </CardContent>
                    </Card>
                    <Card className="bg-gray-50">
                      <CardContent className="p-4 flex flex-col items-center justify-center">
                        <AlertCircle className="h-8 w-8 text-gray-500 mb-2" />
                        <div className="text-2xl font-bold">{auditResults.summary.pendingCount}</div>
                        <p className="text-sm text-gray-700">Pending</p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <Tabs value={activeAuditTab} onValueChange={setActiveAuditTab}>
                    <TabsList className="grid grid-cols-4 w-full">
                      <TabsTrigger value="contract">Smart Contract</TabsTrigger>
                      <TabsTrigger value="token">Token Standard</TabsTrigger>
                      <TabsTrigger value="dex">DEX Integration</TabsTrigger>
                      <TabsTrigger value="platform">Platform</TabsTrigger>
                    </TabsList>
                    
                    {Object.keys(auditResults.results).map((category) => (
                      <TabsContent key={category} value={category} className="space-y-4">
                        <div className="space-y-2">
                          {Object.entries(auditResults.results[category]).map(([key, value]: [string, any]) => (
                            <Accordion key={key} type="single" collapsible className="border rounded-md">
                              <AccordionItem value={key}>
                                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                                  <div className="flex items-center justify-between w-full">
                                    <div className="flex items-center">
                                      {value.status === "pass" && <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />}
                                      {value.status === "warning" && <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />}
                                      {value.status === "fail" && <XCircle className="h-5 w-5 text-red-500 mr-2" />}
                                      <span>{key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())}</span>
                                    </div>
                                    <div>
                                      {value.severity === "critical" && (
                                        <Badge className="bg-red-100 text-red-800">Critical</Badge>
                                      )}
                                      {value.severity === "high" && (
                                        <Badge className="bg-orange-100 text-orange-800">High</Badge>
                                      )}
                                      {value.severity === "medium" && (
                                        <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>
                                      )}
                                      {value.severity === "low" && (
                                        <Badge className="bg-blue-100 text-blue-800">Low</Badge>
                                      )}
                                    </div>
                                  </div>
                                </AccordionTrigger>
                                <AccordionContent className="px-4 py-2 bg-muted">
                                  <p className="text-sm">
                                    {value.description || "No description available."}
                                  </p>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          ))}
                        </div>
                      </TabsContent>
                    ))}
                  </Tabs>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center"
                        onClick={() => handleGenerateReport("public")}
                      >
                        <FileText className="mr-1 h-4 w-4" />
                        <span>Public Report</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center"
                        onClick={() => handleGenerateReport("internal")}
                      >
                        <FileText className="mr-1 h-4 w-4" />
                        <span>Internal Report</span>
                      </Button>
                    </div>
                    <div>
                      {!auditResults.canDeployToMainnet && (
                        <Badge variant="destructive">Mainnet Deployment Blocked</Badge>
                      )}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={runSecurityAudit} disabled={isAuditing}>
                {isAuditing ? "Running Audit..." : "Run Security Audit"}
              </Button>
            </CardFooter>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Network Configuration</CardTitle>
              <CardDescription>Configure blockchain network settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {networkKeys && (
                <>
                  <Tabs defaultValue="solana" className="w-full">
                    <TabsList className="grid grid-cols-2 w-full">
                      <TabsTrigger value="solana">Solana</TabsTrigger>
                      <TabsTrigger value="bnb">BNB Chain</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="solana" className="space-y-4">
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Devnet Configuration</h3>
                        <div className="space-y-2">
                          <Label htmlFor="solanaDevnetRpc">RPC URL</Label>
                          <Input
                            id="solanaDevnetRpc"
                            value={networkKeys.solana.devnet.rpc}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                solana: {
                                  ...networkKeys.solana,
                                  devnet: {
                                    ...networkKeys.solana.devnet,
                                    rpc: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="solanaDevnetTokenProgram">Token Program ID</Label>
                          <Input
                            id="solanaDevnetTokenProgram"
                            value={networkKeys.solana.devnet.tokenProgram}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                solana: {
                                  ...networkKeys.solana,
                                  devnet: {
                                    ...networkKeys.solana.devnet,
                                    tokenProgram: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Mainnet Configuration</h3>
                        <div className="space-y-2">
                          <Label htmlFor="solanaMainnetRpc">RPC URL</Label>
                          <Input
                            id="solanaMainnetRpc"
                            value={networkKeys.solana.mainnet.rpc}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                solana: {
                                  ...networkKeys.solana,
                                  mainnet: {
                                    ...networkKeys.solana.mainnet,
                                    rpc: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="solanaMainnetTokenProgram">Token Program ID</Label>
                          <Input
                            id="solanaMainnetTokenProgram"
                            value={networkKeys.solana.mainnet.tokenProgram}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                solana: {
                                  ...networkKeys.solana,
                                  mainnet: {
                                    ...networkKeys.solana.mainnet,
                                    tokenProgram: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="bnb" className="space-y-4">
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Testnet Configuration</h3>
                        <div className="space-y-2">
                          <Label htmlFor="bnbTestnetRpc">RPC URL</Label>
                          <Input
                            id="bnbTestnetRpc"
                            value={networkKeys.bnb.testnet.rpc}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                bnb: {
                                  ...networkKeys.bnb,
                                  testnet: {
                                    ...networkKeys.bnb.testnet,
                                    rpc: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bnbTestnetTokenFactory">Token Factory Address</Label>
                          <Input
                            id="bnbTestnetTokenFactory"
                            value={networkKeys.bnb.testnet.tokenFactory}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                bnb: {
                                  ...networkKeys.bnb,
                                  testnet: {
                                    ...networkKeys.bnb.testnet,
                                    tokenFactory: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bnbTestnetRouter">Router Address</Label>
                          <Input
                            id="bnbTestnetRouter"
                            value={networkKeys.bnb.testnet.router}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                bnb: {
                                  ...networkKeys.bnb,
                                  testnet: {
                                    ...networkKeys.bnb.testnet,
                                    router: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Mainnet Configuration</h3>
                        <div className="space-y-2">
                          <Label htmlFor="bnbMainnetRpc">RPC URL</Label>
                          <Input
                            id="bnbMainnetRpc"
                            value={networkKeys.bnb.mainnet.rpc}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                bnb: {
                                  ...networkKeys.bnb,
                                  mainnet: {
                                    ...networkKeys.bnb.mainnet,
                                    rpc: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bnbMainnetTokenFactory">Token Factory Address</Label>
                          <Input
                            id="bnbMainnetTokenFactory"
                            value={networkKeys.bnb.mainnet.tokenFactory}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                bnb: {
                                  ...networkKeys.bnb,
                                  mainnet: {
                                    ...networkKeys.bnb.mainnet,
                                    tokenFactory: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bnbMainnetRouter">Router Address</Label>
                          <Input
                            id="bnbMainnetRouter"
                            value={networkKeys.bnb.mainnet.router}
                            onChange={(e) => {
                              setNetworkKeys({
                                ...networkKeys,
                                bnb: {
                                  ...networkKeys.bnb,
                                  mainnet: {
                                    ...networkKeys.bnb.mainnet,
                                    router: e.target.value,
                                  },
                                },
                              })
                            }}
                          />
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={handleUpdateNetworkKeys}>Save Network Configuration</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Tokens Tab */}
        <TabsContent value="tokens" className="space-y-6">
          <Tabs defaultValue="ecosystem" className="w-full">
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="ecosystem">Ecosystem Token</TabsTrigger>
              <TabsTrigger value="user">User Tokens</TabsTrigger>
              <TabsTrigger value="stablecoin">Stablecoin</TabsTrigger>
            </TabsList>
            
            <TabsContent value="ecosystem" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Ecosystem Token Configuration</CardTitle>
                  <CardDescription>Configure the platform's ecosystem token</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {ecosystemTokenConfig && (
                    <Tabs defaultValue="solana-eco" className="w-full">
                      <TabsList className="grid grid-cols-2 w-full">
                        <TabsTrigger value="solana-eco">Solana</TabsTrigger>
                        <TabsTrigger value="bnb-eco">BNB Chain</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="solana-eco" className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="solanaTokenName">Token Name</Label>
                            <Input
                              id="solanaTokenName"
                              value={ecosystemTokenConfig.solana.name}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  solana: {
                                    ...ecosystemTokenConfig.solana,
                                    name: e.target.value,
                                  },
                                })
                              }}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="solanaTokenSymbol">Token Symbol</Label>
                            <Input
                              id="solanaTokenSymbol"
                              value={ecosystemTokenConfig.solana.symbol}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  solana: {
                                    ...ecosystemTokenConfig.solana,
                                    symbol: e.target.value,
                                  },
                                })
                              }}
                            />
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="solanaTokenAddress">Token Address</Label>
                          <Input
                            id="solanaTokenAddress"
                            value={ecosystemTokenConfig.solana.address}
                            onChange={(e) => {
                              setEcosystemTokenConfig({
                                ...ecosystemTokenConfig,
                                solana: {
                                  ...ecosystemTokenConfig.solana,
                                  address: e.target.value,
                                },
                              })
                            }}
                          />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="solanaTokenDecimals">Decimals</Label>
                            <Input
                              id="solanaTokenDecimals"
                              type="number"
                              value={ecosystemTokenConfig.solana.decimals}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  solana: {
                                    ...ecosystemTokenConfig.solana,
                                    decimals: Number.parseInt(e.target.value),
                                  },
                                })
                              }}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="solanaTokenSupply">Total Supply</Label>
                            <Input
                              id="solanaTokenSupply"
                              value={ecosystemTokenConfig.solana.totalSupply}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  solana: {
                                    ...ecosystemTokenConfig.solana,
                                    totalSupply: e.target.value,
                                  },
                                })
                              }}
                            />
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="solanaTokenCirculating">Circulating Supply</Label>
                            <Input
                              id="solanaTokenCirculating"
                              value={ecosystemTokenConfig.solana.circulatingSupply}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  solana: {
                                    ...ecosystemTokenConfig.solana,
                                    circulatingSupply: e.target.value,
                                  },
                                })
                              }}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="solanaTokenPrice">Initial Price (USD)</Label>
                            <Input
                              id="solanaTokenPrice"
                              type="number"
                              step="0.000001"
                              value={ecosystemTokenConfig.solana.price}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  solana: {
                                    ...ecosystemTokenConfig.solana,
                                    price: Number.parseFloat(e.target.value),
                                  },
                                })
                              }}
                            />
                          </div>
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="bnb-eco" className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="bnbTokenName">Token Name</Label>
                            <Input
                              id="bnbTokenName"
                              value={ecosystemTokenConfig.bnb.name}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  bnb: {
                                    ...ecosystemTokenConfig.bnb,
                                    name: e.target.value,
                                  },
                                })
                              }}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="bnbTokenSymbol">Token Symbol</Label>
                            <Input
                              id="bnbTokenSymbol"
                              value={ecosystemTokenConfig.bnb.symbol}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  bnb: {
                                    ...ecosystemTokenConfig.bnb,
                                    symbol: e.target.value,
                                  },
                                })
                              }}
                            />
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="bnbTokenAddress">Token Address</Label>
                          <Input
                            id="bnbTokenAddress"
                            value={ecosystemTokenConfig.bnb.address}
                            onChange={(e) => {
                              setEcosystemTokenConfig({
                                ...ecosystemTokenConfig,
                                bnb: {
                                  ...ecosystemTokenConfig.bnb,
                                  address: e.target.value,
                                },
                              })
                            }}
                          />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="bnbTokenDecimals">Decimals</Label>
                            <Input
                              id="bnbTokenDecimals"
                              type="number"
                              value={ecosystemTokenConfig.bnb.decimals}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  bnb: {
                                    ...ecosystemTokenConfig.bnb,
                                    decimals: Number.parseInt(e.target.value),
                                  },
                                })
                              }}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="bnbTokenSupply">Total Supply</Label>
                            <Input
                              id="bnbTokenSupply"
                              value={ecosystemTokenConfig.bnb.totalSupply}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  bnb: {
                                    ...ecosystemTokenConfig.bnb,
                                    totalSupply: e.target.value,
                                  },
                                })
                              }}
                            />
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="bnbTokenCirculating">Circulating Supply</Label>
                            <Input
                              id="bnbTokenCirculating"
                              value={ecosystemTokenConfig.bnb.circulatingSupply}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  bnb: {
                                    ...ecosystemTokenConfig.bnb,
                                    circulatingSupply: e.target.value,
                                  },
                                })
                              }}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="bnbTokenPrice">Initial Price (USD)</Label>
                            <Input
                              id="bnbTokenPrice"
                              type="number"
                              step="0.000001"
                              value={ecosystemTokenConfig.bnb.price}
                              onChange={(e) => {
                                setEcosystemTokenConfig({
                                  ...ecosystemTokenConfig,
                                  bnb: {
                                    ...ecosystemTokenConfig.bnb,
                                    price: Number.parseFloat(e.target.value),
                                  },
                                })
                              }}
                            />
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  )}
                </CardContent>
                <CardFooter>
                  <Button onClick={() => updateEcosystemTokenConfig(ecosystemTokenConfig)}>
                    Save Ecosystem Token Configuration
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Create Ecosystem Token</CardTitle>
                  <CardDescription>Deploy a new ecosystem token</CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Important</AlertTitle>
                    <AlertDescription>
                      Creating a new ecosystem token will replace the existing one. This action cannot be undone.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="mt-4">
                    <Button variant="destructive">Create New Ecosystem Token</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="user" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>User Token Configuration</CardTitle>
                  <CardDescription>Configure settings for user-created tokens</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {userTokenConfig && (
                    <>
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="userTokenEnabled">Enable User Token Creation</Label>
                          <p className="text-sm text-muted-foreground">Allow users to create their own tokens</p>
                        </div>
                        <Switch
                          id="userTokenEnabled"
                          checked={userTokenConfig.enabled}
                          onCheckedChange={(checked) => {
                            setUserTokenConfig({
                              ...userTokenConfig,
                              enabled: checked,
                            })
                          }}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="minSupply">Minimum Supply</Label>
                          <Input
                            id="minSupply"
                            type="number"
                            value={userTokenConfig.minSupply}
                            onChange={(e) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                minSupply: Number.parseInt(e.target.value),
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="maxSupply">Maximum Supply</Label>
                          <Input
                            id="maxSupply"
                            type="number"
                            value={userTokenConfig.maxSupply}
                            onChange={(e) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                maxSupply: Number.parseInt(e.target.value),
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="minDecimals">Minimum Decimals</Label>
                          <Input
                            id="minDecimals"
                            type="number"
                            value={userTokenConfig.minDecimals}
                            onChange={(e) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                minDecimals: Number.parseInt(e.target.value),
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="maxDecimals">Maximum Decimals</Label>
                          <Input
                            id="maxDecimals"
                            type="number"
                            value={userTokenConfig.maxDecimals}
                            onChange={(e) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                maxDecimals: Number.parseInt(e.target.value),
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="allowCustomSuffix">Allow Custom Suffix</Label>
                          <p className="text-sm text-muted-foreground">Allow users to set custom token address suffix</p>
                        </div>
                        <Switch
                          id="allowCustomSuffix"
                          checked={userTokenConfig.allowCustomSuffix}
                          onCheckedChange={(checked) => {
                            setUserTokenConfig({
                              ...userTokenConfig,
                              allowCustomSuffix: checked,
                            })
                          }}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="defaultSuffix">Default Suffix</Label>
                        <Input
                          id="defaultSuffix"
                          value={userTokenConfig.defaultSuffix}
                          onChange={(e) => {
                            setUserTokenConfig({
                              ...userTokenConfig,
                              defaultSuffix: e.target.value,
                            })
                          }}
                        />
                      </div>
                      
                      <Separator />
                      
                      <h3 className="text-lg font-medium">Fee Configuration</h3>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="solanaBaseFee">Solana Base Fee (SOL)</Label>
                          <Input
                            id="solanaBaseFee"
                            type="number"
                            step="0.01"
                            value={userTokenConfig.baseFee.solana}
                            onChange={(e) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                baseFee: {
                                  ...userTokenConfig.baseFee,
                                  solana: Number.parseFloat(e.target.value),
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bnbBaseFee">BNB Base Fee (BNB)</Label>
                          <Input
                            id="bnbBaseFee"
                            type="number"
                            step="0.01"
                            value={userTokenConfig.baseFee.bnb}
                            onChange={(e) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                baseFee: {
                                  ...userTokenConfig.baseFee,
                                  bnb: Number.parseFloat(e.target.value),
                                },
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="solanaPremiumFee">Solana Premium Fee (SOL)</Label>
                          <Input
                            id="solanaPremiumFee"
                            type="number"
                            step="0.01"
                            value={userTokenConfig.premiumFee.solana}
                            onChange={(e) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                premiumFee: {
                                  ...userTokenConfig.premiumFee,
                                  solana: Number.parseFloat(e.target.value),
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bnbPremiumFee">BNB Premium Fee (BNB)</Label>
                          <Input
                            id="bnbPremiumFee"
                            type="number"
                            step="0.01"
                            value={userTokenConfig.premiumFee.bnb}
                            onChange={(e) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                premiumFee: {
                                  ...userTokenConfig.premiumFee,
                                  bnb: Number.parseFloat(e.target.value),
                                },
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <h3 className="text-lg font-medium">Feature Configuration</h3>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="antiBot">Anti-Bot Features</Label>
                          <Switch
                            id="antiBot"
                            checked={userTokenConfig.features.antiBot}
                            onCheckedChange={(checked) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                features: {
                                  ...userTokenConfig.features,
                                  antiBot: checked,
                                },
                              })
                            }}
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <Label htmlFor="liquidity">Liquidity Features</Label>
                          <Switch
                            id="liquidity"
                            checked={userTokenConfig.features.liquidity}
                            onCheckedChange={(checked) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                features: {
                                  ...userTokenConfig.features,
                                  liquidity: checked,
                                },
                              })
                            }}
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <Label htmlFor="staking">Staking Features</Label>
                          <Switch
                            id="staking"
                            checked={userTokenConfig.features.staking}
                            onCheckedChange={(checked) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                features: {
                                  ...userTokenConfig.features,
                                  staking: checked,
                                },
                              })
                            }}
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <Label htmlFor="presale">Presale Features</Label>
                          <Switch
                            id="presale"
                            checked={userTokenConfig.features.presale}
                            onCheckedChange={(checked) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                features: {
                                  ...userTokenConfig.features,
                                  presale: checked,
                                },
                              })
                            }}
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <Label htmlFor="nft">NFT Features</Label>
                          <Switch
                            id="nft"
                            checked={userTokenConfig.features.nft}
                            onCheckedChange={(checked) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                features: {
                                  ...userTokenConfig.features,
                                  nft: checked,
                                },
                              })
                            }}
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <Label htmlFor="gameIntegration">Game Integration</Label>
                          <Switch
                            id="gameIntegration"
                            checked={userTokenConfig.features.gameIntegration}
                            onCheckedChange={(checked) => {
                              setUserTokenConfig({
                                ...userTokenConfig,
                                features: {
                                  ...userTokenConfig.features,
                                  gameIntegration: checked,
                                },
                              })
                            }}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
                <CardFooter>
                  <Button onClick={handleUpdateUserTokenConfig}>Save User Token Configuration</Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="stablecoin" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Stablecoin Configuration</CardTitle>
                  <CardDescription>Configure platform stablecoin settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {stablecoinConfig && (
                    <>
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="stablecoinEnabled">Enable Stablecoin</Label>
                          <p className="text-sm text-muted-foreground">Enable platform stablecoin functionality</p>
                        </div>
                        <Switch
                          id="stablecoinEnabled"
                          checked={stablecoinConfig.enabled}
                          onCheckedChange={(checked) => {
                            setStablecoinConfig({
                              ...stablecoinConfig,
                              enabled: checked,
                            })
                          }}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="stablecoinName">Stablecoin Name</Label>
                          <Input
                            id="stablecoinName"
                            value={stablecoinConfig.name}
                            onChange={(e) => {
                              setStablecoinConfig({
                                ...stablecoinConfig,
                                name: e.target.value,
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="stablecoinSymbol">Stablecoin Symbol</Label>
                          <Input
                            id="stablecoinSymbol"
                            value={stablecoinConfig.symbol}
                            onChange={(e) => {
                              setStablecoinConfig({
                                ...stablecoinConfig,
                                symbol: e.target.value,
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="stablecoinDecimals">Decimals</Label>
                          <Input
                            id="stablecoinDecimals"
                            type="number"
                            value={stablecoinConfig.decimals}
                            onChange={(e) => {
                              setStablecoinConfig({
                                ...stablecoinConfig,
                                decimals: Number.parseInt(e.target.value),
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="collateralRatio">Collateral Ratio (%)</Label>
                          <Input
                            id="collateralRatio"
                            type="number"
                            value={stablecoinConfig.collateralRatio}
                            onChange={(e) => {
                              setStablecoinConfig({
                                ...stablecoinConfig,
                                collateralRatio: Number.parseInt(e.target.value),
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="mintFee">Mint Fee (%)</Label>
                          <Input
                            id="mintFee"
                            type="number"
                            step="0.01"
                            value={stablecoinConfig.mintFee}
                            onChange={(e) => {
                              setStablecoinConfig({
                                ...stablecoinConfig,
                                mintFee: Number.parseFloat(e.target.value),
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="burnFee">Burn Fee (%)</Label>
                          <Input
                            id="burnFee"
                            type="number"
                            step="0.01"
                            value={stablecoinConfig.burnFee}
                            onChange={(e) => {
                              setStablecoinConfig({
                                ...stablecoinConfig,
                                burnFee: Number.parseFloat(e.target.value),
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="oracleSource">Oracle Source</Label>
                        <Select
                          value={stablecoinConfig.oracleSource}
                          onValueChange={(value) => {
                            setStablecoinConfig({
                              ...stablecoinConfig,
                              oracleSource: value,
                            })
                          }}
                        >
                          <SelectTrigger id="oracleSource">
                            <SelectValue placeholder="Select oracle source" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="chainlink">Chainlink</SelectItem>
                            <SelectItem value="pyth">Pyth Network</SelectItem>
                            <SelectItem value="switchboard">Switchboard</SelectItem>
                            <SelectItem value="custom">Custom Oracle</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="solanaOracleAddress">Solana Oracle Address</Label>
                          <Input
                            id="solanaOracleAddress"
                            value={stablecoinConfig.oracleAddress.solana}
                            onChange={(e) => {
                              setStablecoinConfig({
                                ...stablecoinConfig,
                                oracleAddress: {
                                  ...stablecoinConfig.oracleAddress,
                                  solana: e.target.value,
                                },
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bnbOracleAddress">BNB Oracle Address</Label>
                          <Input
                            id="bnbOracleAddress"
                            value={stablecoinConfig.oracleAddress.bnb}
                            onChange={(e) => {
                              setStablecoinConfig({
                                ...stablecoinConfig,
                                oracleAddress: {
                                  ...stablecoinConfig.oracleAddress,
                                  bnb: e.target.value,
                                },
                              })
                            }}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
                <CardFooter>
                  <Button onClick={() => updateStablecoinConfig(stablecoinConfig)}>
                    Save Stablecoin Configuration
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Create Stablecoin</CardTitle>
                  <CardDescription>Deploy a new stablecoin</CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Important</AlertTitle>
                    <AlertDescription>
                      Creating a new stablecoin will replace the existing one. This action cannot be undone.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="mt-4">
                    <Button variant="destructive">Create New Stablecoin</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </TabsContent>
        
        {/* Platform Tab */}
        <TabsContent value="platform" className="space-y-6">
          <Tabs defaultValue="staking" className="w-full">
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="staking">Staking</TabsTrigger>
              <TabsTrigger value="presale">Presale</TabsTrigger>
              <TabsTrigger value="nft">NFT</TabsTrigger>
            </TabsList>
            
            <TabsContent value="staking" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Staking Configuration</CardTitle>
                  <CardDescription>Configure platform staking settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {stakingConfig && (
                    <>
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="stakingEnabled">Enable Staking</Label>
                          <p className="text-sm text-muted-foreground">Enable platform staking functionality</p>
                        </div>
                        <Switch
                          id="stakingEnabled"
                          checked={stakingConfig.enabled}
                          onCheckedChange={(checked) => {
                            setStakingConfig({
                              ...stakingConfig,
                              enabled: checked,
                            })
                          }}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="minLockPeriod">Minimum Lock Period (days)</Label>
                          <Input
                            id="minLockPeriod"
                            type="number"
                            value={stakingConfig.minLockPeriod}
                            onChange={(e) => {
                              setStakingConfig({
                                ...stakingConfig,
                                minLockPeriod: Number.parseInt(e.target.value),
                              })
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="maxLockPeriod">Maximum Lock Period (days)</Label>
                          <Input
                            id="maxLockPeriod"
                            type="number"
                            value={stakingConfig.maxLockPeriod}
                            onChange={(e) => {
                              setStakingConfig({
                                ...stakingConfig,
                                maxLockPeriod: Number.parseInt(e.target.value),
                              })
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="defaultApr">Default APR (%)</Label>
                        <Input
                          id="defaultApr"
                          type="number"
                          step="0.1"
                          value={stakingConfig.defaultApr}
                          onChange={(e) => {
                            setStakingConfig({
                              ...stakingConfig,
                              defaultApr: Number.parseFloat(e.target.value),
                            })
                          }}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="ecosystemTokenRewards">Ecosystem Token Rewards</Label>
                          <p className="text-sm text-muted-foreground">Enable rewards in ecosystem token</p>
                        </div>
                        <Switch
                          id="ecosystemTokenRewards"
                          checked={stakingConfig.ecosystemTokenRewards}
                          onCheckedChange={(checked) => {
                            setStakingConfig({
                              ...stakingConfig,
                              ecosystemTokenRewards: checked,
                            })
                          }}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="userTokenRewards">User Token Rewards</Label>
                          <p className="text-sm text-muted-foreground">Enable rewards in user-created tokens</p>
                        </div>
                        <Switch
                          id="userTokenRewards"
                          checked={stakingConfig.userTokenRewards}
                          onCheckedChange={(checked) => {
                            setStakingConfig({
                              ...stakingConfig,
                              userTokenRewards: checked,
                            })
                          }}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="compoundingEnabled">Enable Compounding</Label>
                          <p className="text-sm text-muted-foreground">Allow users to compound their rewards</p>
                        </div>
                        <Switch
                          id="compoundingEnabled"
                          checked={stakingConfig.compoundingEnabled}
                          onCheckedChange={(checked) => {
                            setStakingConfig({
                              ...stakingConfig,
                              compoundingEnabled: checked,
                            })
                          }}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="earlyWithdrawalFee">Early Withdrawal Fee (%)</Label>
                        <Input
                          id="earlyWithdrawalFee"
                          type="number"
                          step="0.1"
                          value={stakingConfig.earlyWithdrawalFee}
                          onChange={(e) => {
                            setStakingConfig({
                              ...stakingConfig,
                              earlyWithdrawalFee: Number.parseFloat(e.target.value),
                            })
                          }}
                        />
                      </div>
                    </>
                  )}
                </CardContent>
                <CardFooter>
                  <Button onClick={() => updateStakingConfig(stakingConfig)}>Save Staking Configuration</Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="presale" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Presale Configuration</CardTitle>
                  <CardDescription>Configure platform presale settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {presaleConfig && (
                    <>
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="presaleEnabled">Enable Presale</Label>
                          <p className="text-sm text-muted-foreground">Enable platform presale functionality</p>
                        </div>
                        <Switch
                          id="presaleEnabled"
                          checked={presaleConfig.enabled}
                          onCheckedChange={(checked) => {
                            setPresaleConfig

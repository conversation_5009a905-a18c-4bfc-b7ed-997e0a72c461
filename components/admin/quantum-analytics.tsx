"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  <PERSON><PERSON>hart3,
  <PERSON><PERSON>hart,
  PieChart,
  Download,
  Calendar,
  ArrowUpRight,
  TrendingUp,
  TrendingDown,
} from "lucide-react"

export default function QuantumAnalytics() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Analytiques des Tokens Quantum</h2>
          <p className="text-muted-foreground">
            Statistiques et métriques détaillées sur l'utilisation des tokens Quantum
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select defaultValue="30d">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 derniers jours</SelectItem>
              <SelectItem value="30d">30 derniers jours</SelectItem>
              <SelectItem value="90d">90 derniers jours</SelectItem>
              <SelectItem value="1y">1 an</SelectItem>
              <SelectItem value="all">Tout</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Calendar className="h-4 w-4" />
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total des tokens créés</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">142</div>
            <div className="flex items-center pt-1 text-xs text-green-600">
              <TrendingUp className="h-3.5 w-3.5 mr-1" />
              <span>+12% par rapport au mois dernier</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Volume de transactions</CardTitle>
            <LineChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1.2M SOL</div>
            <div className="flex items-center pt-1 text-xs text-green-600">
              <TrendingUp className="h-3.5 w-3.5 mr-1" />
              <span>+8% par rapport au mois dernier</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs uniques</CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,853</div>
            <div className="flex items-center pt-1 text-xs text-green-600">
              <TrendingUp className="h-3.5 w-3.5 mr-1" />
              <span>+18% par rapport au mois dernier</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Frais collectés</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">14.2 SOL</div>
            <div className="flex items-center pt-1 text-xs text-red-600">
              <TrendingDown className="h-3.5 w-3.5 mr-1" />
              <span>-3% par rapport au mois dernier</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="creation">
        <TabsList>
          <TabsTrigger value="creation">Création de tokens</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="users">Utilisateurs</TabsTrigger>
          <TabsTrigger value="security">Sécurité</TabsTrigger>
        </TabsList>

        <TabsContent value="creation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Création de tokens au fil du temps</CardTitle>
              <CardDescription>Nombre de tokens Quantum créés par période</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Graphique de création de tokens</p>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Distribution par suffixe</CardTitle>
                <CardDescription>Répartition des tokens par suffixe</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Graphique de distribution par suffixe</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Offre initiale moyenne</CardTitle>
                <CardDescription>Évolution de l'offre initiale moyenne des tokens</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Graphique d'offre initiale</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Volume de transactions</CardTitle>
              <CardDescription>Volume total des transactions par période</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Graphique de volume de transactions</p>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Tokens les plus échangés</CardTitle>
                <CardDescription>Classement des tokens par volume d'échange</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Graphique des tokens les plus échangés</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Frais générés</CardTitle>
                <CardDescription>Frais générés par les transactions</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Graphique des frais générés</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Croissance des utilisateurs</CardTitle>
              <CardDescription>Évolution du nombre d'utilisateurs au fil du temps</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Graphique de croissance des utilisateurs</p>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Utilisateurs par région</CardTitle>
                <CardDescription>Répartition géographique des utilisateurs</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Carte des utilisateurs par région</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Engagement des utilisateurs</CardTitle>
                <CardDescription>Métriques d'engagement des utilisateurs</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Graphique d'engagement des utilisateurs</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Incidents de sécurité</CardTitle>
              <CardDescription>Évolution des incidents de sécurité au fil du temps</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Graphique des incidents de sécurité</p>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Types d'incidents</CardTitle>
                <CardDescription>Répartition des incidents par type</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Graphique des types d'incidents</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Efficacité des protections</CardTitle>
                <CardDescription>Taux de blocage des tentatives malveillantes</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Graphique d'efficacité des protections</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Tendances et prévisions</CardTitle>
          <CardDescription>Analyse des tendances et prévisions pour les prochains mois</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <div className="h-full w-full bg-muted/20 rounded-md flex items-center justify-center">
            <p className="text-muted-foreground">Graphique des tendances et prévisions</p>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button>
          Voir le rapport complet
          <ArrowUpRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

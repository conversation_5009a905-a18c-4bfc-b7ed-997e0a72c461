"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import {
  Rocket,
  Clock,
  Users,
  DollarSign,
  TrendingUp,
  Lock,
  ExternalLink,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Edit,
  Trash2,
  ArrowLeft,
  Save,
  Loader2,
  Copy,
} from "lucide-react"

interface QuantumTokenDetailsProps {
  id: string
}

export default function QuantumTokenDetails({ id }: QuantumTokenDetailsProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [launch, setLaunch] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // États pour l'édition
  const [name, setName] = useState("")
  const [symbol, setSymbol] = useState("")
  const [description, setDescription] = useState("")
  const [website, setWebsite] = useState("")
  const [twitter, setTwitter] = useState("")
  const [telegram, setTelegram] = useState("")
  const [featured, setFeatured] = useState(false)
  const [verified, setVerified] = useState(true)
  const [status, setStatus] = useState("")

  useEffect(() => {
    fetchLaunchDetails()
  }, [id])

  const fetchLaunchDetails = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Données fictives pour la démonstration
      const mockLaunch = {
        id,
        name: "Global Finance Quantum",
        symbol: "GF",
        suffix: "QUANTUM",
        description: "The next generation of decentralized finance on Solana",
        website: "https://globalfinance.com",
        twitter: "https://twitter.com/globalfinance",
        telegram: "https://t.me/globalfinance",
        status: "fundraising",
        featured: true,
        verified: true,
        current_phase_name: "Presale",
        current_phase_status: "active",
        current_phase_raised: 75,
        current_phase_target: 100,
        current_phase_percentage: 75,
        participants: 42,
        initial_price: 0.00005,
        soft_cap: 50,
        hard_cap: 200,
        created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        owner_address: "8ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        token_address: "7ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
        liquidity_percentage: 70,
        team_percentage: 10,
        marketing_percentage: 10,
        reserve_percentage: 10,
        buy_tax: 5,
        sell_tax: 7,
        transfer_tax: 3,
        liquidity_lock_period: 180,
        team_lock_period: 90,
        anti_bot: true,
        anti_dump: true,
        max_wallet_percentage: 2,
        max_tx_percentage: 1,
        total_supply: 1000000000,
        decimals: 9,
        transactions: [
          {
            id: "tx1",
            type: "contribution",
            hash: "5ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
            date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            amount: 5,
            status: "confirmed",
            contributor: "6ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
          },
          {
            id: "tx2",
            type: "contribution",
            hash: "6ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
            date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            amount: 10,
            status: "confirmed",
            contributor: "7ZLnpqEGVPFXv4S4xZhAYcYRNJEJD3dKTJ2yBMX5YdKT",
          },
        ],
        phases: [
          {
            id: "phase1",
            name: "Presale",
            description: "Initial fundraising phase at a discounted price",
            start_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            end_date: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),
            target_amount: 100,
            min_contribution: 0.1,
            max_contribution: 5,
            price: 0.00005,
            status: "active",
            participants: 42,
            amount_raised: 75,
            percentage_complete: 75,
          },
          {
            id: "phase2",
            name: "Fair Launch",
            description: "Equal opportunity launch phase for all participants",
            start_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
            end_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
            target_amount: 100,
            min_contribution: 0.1,
            max_contribution: 10,
            price: 0.000075,
            status: "pending",
            participants: 0,
            amount_raised: 0,
            percentage_complete: 0,
          },
        ],
      }

      setLaunch(mockLaunch)

      // Initialiser les états d'édition
      setName(mockLaunch.name)
      setSymbol(mockLaunch.symbol)
      setDescription(mockLaunch.description)
      setWebsite(mockLaunch.website)
      setTwitter(mockLaunch.twitter)
      setTelegram(mockLaunch.telegram)
      setFeatured(mockLaunch.featured)
      setVerified(mockLaunch.verified)
      setStatus(mockLaunch.status)
    } catch (error) {
      console.error("Error fetching launch details:", error)
      toast({
        title: "Error",
        description: "Failed to fetch launch details. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveChanges = async () => {
    setIsSaving(true)
    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Mettre à jour les données du lancement
      setLaunch({
        ...launch,
        name,
        symbol,
        description,
        website,
        twitter,
        telegram,
        featured,
        verified,
        status,
      })

      setIsEditing(false)
      toast({
        title: "Changes saved",
        description: "The token launch has been updated successfully",
      })
    } catch (error) {
      console.error("Error saving changes:", error)
      toast({
        title: "Error",
        description: "Failed to save changes. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleDeleteLaunch = async () => {
    if (!confirm("Are you sure you want to delete this launch? This action cannot be undone.")) {
      return
    }

    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Launch deleted",
        description: "The launch has been successfully deleted.",
      })

      // Rediriger vers la page de gestion des lancements
      router.push("/admin/quantum-tokens")
    } catch (error) {
      console.error("Error deleting launch:", error)
      toast({
        title: "Error",
        description: "Failed to delete launch. Please try again.",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "setup":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            Upcoming
          </Badge>
        )
      case "fundraising":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <DollarSign className="mr-1 h-3 w-3" />
            Active
          </Badge>
        )
      case "preparing":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Preparing
          </Badge>
        )
      case "launched":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <Rocket className="mr-1 h-3 w-3" />
            Launched
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied!",
      description: message,
    })
  }

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10 rounded-full" />
          <Skeleton className="h-8 w-48" />
        </div>

        <Skeleton className="h-10 w-full" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    )
  }

  if (!launch) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>Launch not found or has been removed.</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/quantum-tokens">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">Quantum Token Details</h2>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveChanges} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={() => setIsEditing(true)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
              <Button variant="destructive" onClick={handleDeleteLaunch}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 bg-muted p-4 rounded-lg">
        <div className="flex items-center gap-4">
          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold">
            {launch.symbol.substring(0, 2)}
          </div>
          <div>
            {isEditing ? (
              <div className="space-y-2">
                <Input value={name} onChange={(e) => setName(e.target.value)} className="font-bold text-lg" />
                <div className="flex gap-2">
                  <Input
                    value={symbol}
                    onChange={(e) => setSymbol(e.target.value.toUpperCase())}
                    className="w-20 uppercase"
                  />
                  <span className="text-muted-foreground">{launch.suffix}</span>
                </div>
              </div>
            ) : (
              <>
                <h3 className="text-lg font-bold">{launch.name}</h3>
                <p className="text-muted-foreground">
                  {launch.symbol}
                  {launch.suffix}
                </p>
              </>
            )}
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          {isEditing ? (
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="setup">Upcoming</SelectItem>
                <SelectItem value="fundraising">Active</SelectItem>
                <SelectItem value="preparing">Preparing</SelectItem>
                <SelectItem value="launched">Launched</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
          ) : (
            <>
              {getStatusBadge(launch.status)}
              {launch.featured && (
                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                  Featured
                </Badge>
              )}
              {launch.verified && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <CheckCircle2 className="mr-1 h-3 w-3" />
                  Verified
                </Badge>
              )}
            </>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tokenomics">Tokenomics</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Launch Information</CardTitle>
                <CardDescription>Current status and progress of the token launch</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {isEditing ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        rows={4}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        value={website}
                        onChange={(e) => setWebsite(e.target.value)}
                        placeholder="https://example.com"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="twitter">Twitter</Label>
                      <Input
                        id="twitter"
                        value={twitter}
                        onChange={(e) => setTwitter(e.target.value)}
                        placeholder="https://twitter.com/example"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="telegram">Telegram</Label>
                      <Input
                        id="telegram"
                        value={telegram}
                        onChange={(e) => setTelegram(e.target.value)}
                        placeholder="https://t.me/example"
                      />
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="featured" className="cursor-pointer">
                        Featured Launch
                      </Label>
                      <Switch id="featured" checked={featured} onCheckedChange={setFeatured} />
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="verified" className="cursor-pointer">
                        Verified Launch
                      </Label>
                      <Switch id="verified" checked={verified} onCheckedChange={setVerified} />
                    </div>
                  </div>
                ) : (
                  <>
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Description</p>
                      <p>{launch.description}</p>
                    </div>

                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Social Links</p>
                      <div className="flex flex-wrap gap-2">
                        {launch.website && (
                          <Button variant="outline" size="sm" asChild>
                            <Link href={launch.website} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="mr-1 h-4 w-4" />
                              Website
                            </Link>
                          </Button>
                        )}
                        {launch.twitter && (
                          <Button variant="outline" size="sm" asChild>
                            <Link href={launch.twitter} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="mr-1 h-4 w-4" />
                              Twitter
                            </Link>
                          </Button>
                        )}
                        {launch.telegram && (
                          <Button variant="outline" size="sm" asChild>
                            <Link href={launch.telegram} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="mr-1 h-4 w-4" />
                              Telegram
                            </Link>
                          </Button>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground mb-2">Current Phase</p>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">{launch.current_phase_name}</Badge>
                        </div>
                        <span className="text-sm font-medium">{launch.current_phase_percentage}%</span>
                      </div>
                      <Progress value={launch.current_phase_percentage} className="h-2" />
                      <div className="flex justify-between text-sm">
                        <span>
                          {launch.current_phase_raised} / {launch.current_phase_target} SOL
                        </span>
                        <span>{launch.participants} participants</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Initial Price</p>
                        <p className="font-medium">${launch.initial_price}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Hard Cap</p>
                        <p className="font-medium">{launch.hard_cap} SOL</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Created</p>
                        <p className="font-medium">{new Date(launch.created_at).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Status</p>
                        <div>{getStatusBadge(launch.status)}</div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Token Details</CardTitle>
                <CardDescription>Technical information about the token</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Supply</p>
                    <p className="font-medium">{launch.total_supply.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Decimals</p>
                    <p className="font-medium">{launch.decimals}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-muted-foreground mb-2">Token Address</p>
                  <div className="flex items-center gap-2 bg-muted p-2 rounded-md">
                    <code className="text-xs truncate flex-1">{launch.token_address}</code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(launch.token_address, "Token address copied to clipboard")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-muted-foreground mb-2">Owner Address</p>
                  <div className="flex items-center gap-2 bg-muted p-2 rounded-md">
                    <code className="text-xs truncate flex-1">{launch.owner_address}</code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(launch.owner_address, "Owner address copied to clipboard")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="flex flex-col gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`https://solscan.io/token/${launch.token_address}`} target="_blank">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View on Solscan
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/token-quantum/launch/${launch.id}`} target="_blank">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View Public Page
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Launch Phases</CardTitle>
              <CardDescription>Timeline of the token launch phases</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {launch.phases.map((phase: any, index: number) => (
                  <div key={phase.id} className="flex gap-4">
                    <div className="flex flex-col items-center">
                      <div
                        className={`rounded-full p-2 ${
                          phase.status === "completed"
                            ? "bg-green-100 text-green-700"
                            : phase.status === "active"
                              ? "bg-blue-100 text-blue-700"
                              : "bg-gray-100 text-gray-500"
                        }`}
                      >
                        {phase.status === "completed" ? (
                          <CheckCircle2 className="h-4 w-4" />
                        ) : phase.status === "active" ? (
                          <Clock className="h-4 w-4" />
                        ) : (
                          <Clock className="h-4 w-4" />
                        )}
                      </div>
                      {index < launch.phases.length - 1 && <div className="w-0.5 bg-gray-200 h-full mt-2"></div>}
                    </div>
                    <div className="flex-1 pb-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{phase.name}</h4>
                        <Badge
                          variant={
                            phase.status === "completed"
                              ? "success"
                              : phase.status === "active"
                                ? "default"
                                : "secondary"
                          }
                        >
                          {phase.status === "completed"
                            ? "Completed"
                            : phase.status === "active"
                              ? "Active"
                              : "Pending"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{phase.description}</p>
                      <div className="flex flex-wrap gap-x-4 gap-y-2 mt-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Start:</span> {formatDate(phase.start_date)}
                        </div>
                        <div>
                          <span className="text-muted-foreground">End:</span> {formatDate(phase.end_date)}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Price:</span> ${phase.price}
                        </div>
                      </div>
                      {(phase.status === "completed" || phase.status === "active") && (
                        <div className="mt-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{phase.percentage_complete}%</span>
                          </div>
                          <Progress value={phase.percentage_complete} className="h-2 mt-1" />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>
                              {phase.amount_raised} / {phase.target_amount} SOL
                            </span>
                            <span>{phase.participants} participants</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokenomics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Token Distribution</CardTitle>
              <CardDescription>Allocation of tokens across different categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Token Information</h4>
                    <div className="grid grid-cols-2 gap-y-2">
                      <div className="text-sm text-muted-foreground">Name</div>
                      <div className="text-sm font-medium">{launch.name}</div>
                      <div className="text-sm text-muted-foreground">Symbol</div>
                      <div className="text-sm font-medium">
                        {launch.symbol}
                        {launch.suffix}
                      </div>
                      <div className="text-sm text-muted-foreground">Decimals</div>
                      <div className="text-sm font-medium">{launch.decimals}</div>
                      <div className="text-sm text-muted-foreground">Total Supply</div>
                      <div className="text-sm font-medium">{launch.total_supply.toLocaleString()}</div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Distribution</h4>
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <DollarSign className="h-4 w-4 mr-1 text-blue-500" />
                            Liquidity
                          </span>
                          <span>{launch.liquidity_percentage}%</span>
                        </div>
                        <Progress value={launch.liquidity_percentage} className="h-2 mt-1" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <Users className="h-4 w-4 mr-1 text-green-500" />
                            Team
                          </span>
                          <span>{launch.team_percentage}%</span>
                        </div>
                        <Progress value={launch.team_percentage} className="h-2 mt-1" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <TrendingUp className="h-4 w-4 mr-1 text-purple-500" />
                            Marketing
                          </span>
                          <span>{launch.marketing_percentage}%</span>
                        </div>
                        <Progress value={launch.marketing_percentage} className="h-2 mt-1" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <Lock className="h-4 w-4 mr-1 text-amber-500" />
                            Reserve
                          </span>
                          <span>{launch.reserve_percentage}%</span>
                        </div>
                        <Progress value={launch.reserve_percentage} className="h-2 mt-1" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Vesting & Locking</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Liquidity Lock</span>
                        <span className="text-sm font-medium">{launch.liquidity_lock_period} days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Team Tokens Lock</span>
                        <span className="text-sm font-medium">{launch.team_lock_period} days</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Tax Structure</h4>
                    <div className="grid grid-cols-2 gap-y-2">
                      <div className="text-sm text-muted-foreground">Buy Tax</div>
                      <div className="text-sm font-medium">{launch.buy_tax}%</div>
                      <div className="text-sm text-muted-foreground">Sell Tax</div>
                      <div className="text-sm font-medium">{launch.sell_tax}%</div>
                      <div className="text-sm text-muted-foreground">Transfer Tax</div>
                      <div className="text-sm font-medium">{launch.transfer_tax}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Features</CardTitle>
              <CardDescription>Protection mechanisms implemented for this token</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Anti-Bot Protection</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Status</span>
                      <Badge variant={launch.anti_bot ? "success" : "outline"}>
                        {launch.anti_bot ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Prevents bots from sniping tokens at launch by implementing transaction limits and timing
                      mechanisms.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Anti-Dump Protection</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Status</span>
                      <Badge variant={launch.anti_dump ? "success" : "outline"}>
                        {launch.anti_dump ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Prevents large holders from dumping tokens all at once by implementing selling limits.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Transaction Limits</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Max Wallet Size</span>
                        <span className="text-sm font-medium">{launch.max_wallet_percentage}% of supply</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Max Transaction Size</span>
                        <span className="text-sm font-medium">{launch.max_tx_percentage}% of supply</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mt-2">
                      Limits the maximum amount of tokens that can be held or transferred in a single transaction to
                      prevent market manipulation.
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Liquidity Lock</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Duration</span>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        <Lock className="mr-1 h-3 w-3" />
                        {launch.liquidity_lock_period} days
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Liquidity tokens are locked for {launch.liquidity_lock_period} days to ensure trading can continue
                      and prevent rug pulls.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Team Token Lock</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Duration</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        <Lock className="mr-1 h-3 w-3" />
                        {launch.team_lock_period} days
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Team tokens are locked for {launch.team_lock_period} days to demonstrate long-term commitment to
                      the project.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Contract Verification</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Status</span>
                      <Badge variant="success">Verified</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      The token contract has been verified on Solscan, allowing anyone to inspect the code.
                    </p>
                    {launch.token_address && (
                      <Button variant="outline" size="sm" className="mt-2" asChild>
                        <Link href={`https://solscan.io/token/${launch.token_address}`} target="_blank">
                          <ExternalLink className="mr-2 h-4 w-4" />
                          View on Solscan
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>Recent transactions related to this token launch</CardDescription>
            </CardHeader>
            <CardContent>
              {launch.transactions && launch.transactions.length > 0 ? (
                <div className="space-y-4">
                  {launch.transactions.map((tx: any) => (
                    <div key={tx.id} className="flex items-center justify-between p-3 bg-muted rounded-md">
                      <div className="flex items-center gap-3">
                        <div className="rounded-full p-2 bg-green-100 text-green-700">
                          <DollarSign className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="font-medium capitalize">{tx.type}</p>
                          <p className="text-xs text-muted-foreground">{new Date(tx.date).toLocaleString()}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{tx.amount} SOL</p>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`https://solscan.io/tx/${tx.hash}`} target="_blank">
                            <ExternalLink className="h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8">
                  <Clock className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-1">No transactions yet</h3>
                  <p className="text-muted-foreground text-center">
                    Transactions will appear here once the token launch is active.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

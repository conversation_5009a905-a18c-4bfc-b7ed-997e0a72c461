"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, UserPlus, MoreHorizontal, Edit, Trash, Lock, Mail, Shield, Check, X } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function QuantumUserManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")

  // Données simulées pour les utilisateurs
  const users = [
    {
      id: 1,
      name: "<PERSON> Doe",
      email: "<EMAIL>",
      role: "Admin",
      status: "Active",
      walletAddress: "0x1a2b...3c4d",
      lastLogin: "2023-04-28 10:45:12",
      quantumAccess: true,
    },
    {
      id: 2,
      name: "Jane Smith",
      email: "<EMAIL>",
      role: "User",
      status: "Active",
      walletAddress: "0x5e6f...7g8h",
      lastLogin: "2023-04-27 15:30:45",
      quantumAccess: true,
    },
    {
      id: 3,
      name: "Bob Johnson",
      email: "<EMAIL>",
      role: "Moderator",
      status: "Active",
      walletAddress: "0x9i0j...1k2l",
      lastLogin: "2023-04-26 09:15:33",
      quantumAccess: false,
    },
    {
      id: 4,
      name: "Alice Brown",
      email: "<EMAIL>",
      role: "User",
      status: "Inactive",
      walletAddress: "0x3m4n...5o6p",
      lastLogin: "2023-04-20 14:22:18",
      quantumAccess: false,
    },
    {
      id: 5,
      name: "Charlie Wilson",
      email: "<EMAIL>",
      role: "User",
      status: "Active",
      walletAddress: "0x7q8r...9s0t",
      lastLogin: "2023-04-28 08:10:05",
      quantumAccess: true,
    },
  ]

  // Filtrer les utilisateurs en fonction du terme de recherche et du filtre de rôle
  const filteredUsers = users.filter(
    (user) =>
      (user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.walletAddress.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (roleFilter === "all" || user.role.toLowerCase() === roleFilter.toLowerCase()),
  )

  // Données simulées pour les invitations
  const invitations = [
    {
      id: 1,
      email: "<EMAIL>",
      role: "User",
      status: "Pending",
      sentAt: "2023-04-28 10:45:12",
      expiresAt: "2023-05-05 10:45:12",
    },
    {
      id: 2,
      email: "<EMAIL>",
      role: "Moderator",
      status: "Expired",
      sentAt: "2023-04-20 15:30:45",
      expiresAt: "2023-04-27 15:30:45",
    },
    {
      id: 3,
      email: "<EMAIL>",
      role: "User",
      status: "Pending",
      sentAt: "2023-04-27 09:15:33",
      expiresAt: "2023-05-04 09:15:33",
    },
  ]

  return (
    <div className="space-y-6">
      <Tabs defaultValue="users" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users">Utilisateurs</TabsTrigger>
          <TabsTrigger value="invitations">Invitations</TabsTrigger>
          <TabsTrigger value="permissions">Permissions Quantum</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Utilisateurs Quantum</CardTitle>
              <CardDescription>Gérer les utilisateurs ayant accès aux fonctionnalités Quantum</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Rechercher par nom, email ou adresse de portefeuille..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={roleFilter} onValueChange={setRoleFilter} className="w-[150px] ml-2">
                  <SelectTrigger>
                    <SelectValue placeholder="Filtrer par rôle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les rôles</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="moderator">Moderator</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="ml-2 flex items-center gap-2">
                  <UserPlus className="h-4 w-4" />
                  Ajouter
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Rôle</TableHead>
                      <TableHead>Accès Quantum</TableHead>
                      <TableHead>Dernière connexion</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              user.role === "Admin" ? "default" : user.role === "Moderator" ? "outline" : "secondary"
                            }
                          >
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {user.quantumAccess ? (
                            <Badge variant="success" className="flex items-center gap-1">
                              <Check className="h-3 w-3" /> Activé
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="flex items-center gap-1">
                              <X className="h-3 w-3" /> Désactivé
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{user.lastLogin}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Ouvrir le menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem className="flex items-center">
                                <Edit className="mr-2 h-4 w-4" />
                                Modifier
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center">
                                <Shield className="mr-2 h-4 w-4" />
                                {user.quantumAccess ? "Révoquer l'accès Quantum" : "Accorder l'accès Quantum"}
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center">
                                <Lock className="mr-2 h-4 w-4" />
                                Réinitialiser le mot de passe
                              </DropdownMenuItem>
                              <DropdownMenuItem className="flex items-center">
                                <Mail className="mr-2 h-4 w-4" />
                                Envoyer un email
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="flex items-center text-destructive">
                                <Trash className="mr-2 h-4 w-4" />
                                Supprimer
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invitations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Invitations</CardTitle>
              <CardDescription>Gérer les invitations pour rejoindre la plateforme Quantum</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end mb-4">
                <Button className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Envoyer une invitation
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Rôle</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Date d'envoi</TableHead>
                      <TableHead>Date d'expiration</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invitations.map((invitation) => (
                      <TableRow key={invitation.id}>
                        <TableCell className="font-medium">{invitation.email}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{invitation.role}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={invitation.status === "Pending" ? "warning" : "destructive"}>
                            {invitation.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{invitation.sentAt}</TableCell>
                        <TableCell>{invitation.expiresAt}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Ouvrir le menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem className="flex items-center">
                                <Mail className="mr-2 h-4 w-4" />
                                Renvoyer
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="flex items-center text-destructive">
                                <Trash className="mr-2 h-4 w-4" />
                                Annuler
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Permissions Quantum</CardTitle>
              <CardDescription>Configurer les permissions pour les fonctionnalités Quantum</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    id: "create-quantum",
                    name: "Création de tokens Quantum",
                    description: "Autoriser la création de tokens Quantum",
                    roles: ["Admin", "Moderator"],
                  },
                  {
                    id: "manage-quantum",
                    name: "Gestion des tokens Quantum",
                    description: "Autoriser la gestion des tokens Quantum existants",
                    roles: ["Admin"],
                  },
                  {
                    id: "view-analytics",
                    name: "Voir les analytiques Quantum",
                    description: "Autoriser l'accès aux analytiques des tokens Quantum",
                    roles: ["Admin", "Moderator", "User"],
                  },
                  {
                    id: "advanced-features",
                    name: "Fonctionnalités avancées",
                    description: "Autoriser l'accès aux fonctionnalités avancées des tokens Quantum",
                    roles: ["Admin"],
                  },
                  {
                    id: "security-settings",
                    name: "Paramètres de sécurité",
                    description: "Autoriser la modification des paramètres de sécurité Quantum",
                    roles: ["Admin"],
                  },
                ].map((permission) => (
                  <div key={permission.id} className="flex items-center justify-between border-b pb-4">
                    <div>
                      <p className="font-medium">{permission.name}</p>
                      <p className="text-sm text-muted-foreground">{permission.description}</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {permission.roles.map((role, i) => (
                          <Badge key={i} variant="outline" className="text-xs">
                            {role}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Modifier
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

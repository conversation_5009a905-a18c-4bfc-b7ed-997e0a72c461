"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Download } from "lucide-react"

type EnvVariableCategory = "blockchain" | "api" | "security" | "platform" | "storage" | "other"

interface EnvVariable {
  key: string
  value: string
  isSecret: boolean
  isLocked: boolean
  description: string
  category: EnvVariableCategory
  lastModified: string
  modifiedBy: string
}

// Liste des variables d'environnement requises avec leurs descriptions
const requiredEnvVariables = [
  {
    key: "NEXT_PUBLIC_SOLANA_RPC_URL",
    description: "URL de l'API Solana pour les connexions RPC",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "COINGECKO_API_KEY",
    description: "Clé API pour CoinGecko",
    category: "api" as const,
    isSecret: true,
  },
  {
    key: "NEXT_PUBLIC_TOKEN_PROGRAM_ID",
    description: "ID du programme de token Solana",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "NEXT_PUBLIC_CUSTOM_MINT_ADDRESS",
    description: "Adresse de mint personnalisée",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "MARKET_API_ENDPOINT",
    description: "Endpoint de l'API pour les données de marché",
    category: "api" as const,
    isSecret: false,
  },
  {
    key: "MARKET_API_KEY",
    description: "Clé API pour les données de marché",
    category: "api" as const,
    isSecret: true,
  },
  {
    key: "PRE_MINED_MMGF_PRIVATE_KEY",
    description: "Clé privée pré-minée (ATTENTION: SENSIBLE)",
    category: "security" as const,
    isSecret: true,
  },
  {
    key: "COINMARKETCAP_API_KEY",
    description: "Clé API pour CoinMarketCap",
    category: "api" as const,
    isSecret: true,
  },
  {
    key: "NEXT_PUBLIC_PANCAKESWAP_ROUTER_ADDRESS",
    description: "Adresse du routeur PancakeSwap",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "NEXT_PUBLIC_BNB_RPC_URL",
    description: "URL de l'API BNB Chain pour les connexions RPC",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "ADMIN_WALLET",
    description: "Adresse du wallet administrateur",
    category: "security" as const,
    isSecret: false,
  },
  {
    key: "NEXT_PUBLIC_BNB_TOKEN_FACTORY_TESTNET",
    description: "Adresse du contrat factory de tokens sur BNB Testnet",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "NEXT_PUBLIC_BNB_TOKEN_FACTORY_MAINNET",
    description: "Adresse du contrat factory de tokens sur BNB Mainnet",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "NEXT_PUBLIC_PANCAKESWAP_ROUTER_TESTNET",
    description: "Adresse du routeur PancakeSwap sur BNB Testnet",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "NEXT_PUBLIC_PANCAKESWAP_ROUTER_MAINNET",
    description: "Adresse du routeur PancakeSwap sur BNB Mainnet",
    category: "blockchain" as const,
    isSecret: false,
  },
  {
    key: "NEXT_PUBLIC_MMGF_PRIVATE_KEY",
    description: "Clé privée pour le market maker (ATTENTION: SENSIBLE)",
    category: "security" as const,
    isSecret: true,
  },
  {
    key: "FILEBASE_API_KEY",
    description: "Clé API pour Filebase (stockage décentralisé)",
    category: "storage" as const,
    isSecret: true,
  },
  {
    key: "ADMIN_KEY",
    description: "Clé d'administration pour les opérations sensibles",
    category: "security" as const,
    isSecret: true,
  },
  {
    key: "JWT_SECRET",
    description: "Secret pour la génération et validation des JWT",
    category: "security" as const,
    isSecret: true,
  },
]

export default function EnvVariablesManager() {
  const { toast } = useToast()
  const [envVariables, setEnvVariables] = useState<EnvVariable[]>([])
  const [filteredVariables, setFilteredVariables] = useState<EnvVariable[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isLocked, setIsLocked] = useState(false)
  const [adminPassword, setAdminPassword] = useState("")
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [editingVariable, setEditingVariable] = useState<EnvVariable | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [variableHistory, setVariableHistory] = useState<any[]>([])
  const [showSecrets, setShowSecrets] = useState(false)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newVariable, setNewVariable] = useState<Partial<EnvVariable>>({
    key: "",
    value: "",
    description: "",
    category: "other",
    isSecret: false,
  })
  const [missingVariables, setMissingVariables] = useState<string[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [variableToDelete, setVariableToDelete] = useState<string | null>(null)
  const [showBackupDialog, setShowBackupDialog] = useState(false)
  const [backupData, setBackupData] = useState<string>("")
  const [showRestoreDialog, setShowRestoreDialog] = useState(false)
  const [restoreData, setRestoreData] = useState<string>("")
  const [isRestoring, setIsRestoring] = useState(false)
  const [isBackingUp, setIsBackingUp] = useState(false)
  const [activeTab, setActiveTab] = useState("all")

  // Load environment variables
  useEffect(() => {
    loadEnvVariables()
  }, [])

  const loadEnvVariables = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API pour récupérer les variables d'environnement
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Données simulées pour les variables d'environnement
      const mockVariables: EnvVariable[] = [
        {
          key: "NEXT_PUBLIC_SOLANA_RPC_URL",
          value: "https://api.devnet.solana.com",
          isSecret: false,
          isLocked: false,
          description: "URL de l'API Solana pour les connexions RPC",
          category: "blockchain",
          lastModified: new Date().toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "MARKET_API_ENDPOINT",
          value: "https://api.market.example.com/v1",
          isSecret: false,
          isLocked: true,
          description: "Endpoint de l'API pour les données de marché",
          category: "api",
          lastModified: new Date(Date.now() - 86400000).toISOString(),
          modifiedBy: "System",
        },
        {
          key: "MARKET_API_KEY",
          value: "api_key_12345678abcdefgh",
          isSecret: true,
          isLocked: true,
          description: "Clé API pour les données de marché",
          category: "api",
          lastModified: new Date(Date.now() - 172800000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "NEXT_PUBLIC_CUSTOM_MINT_ADDRESS",
          value: "Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr",
          isSecret: false,
          isLocked: false,
          description: "Adresse de mint personnalisée",
          category: "blockchain",
          lastModified: new Date(Date.now() - 259200000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "NEXT_PUBLIC_TOKEN_PROGRAM_ID",
          value: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
          isSecret: false,
          isLocked: true,
          description: "ID du programme de token Solana",
          category: "blockchain",
          lastModified: new Date(Date.now() - 345600000).toISOString(),
          modifiedBy: "System",
        },
        {
          key: "COINGECKO_API_KEY",
          value: "coingecko_api_key_12345",
          isSecret: true,
          isLocked: false,
          description: "Clé API pour CoinGecko",
          category: "api",
          lastModified: new Date(Date.now() - 432000000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "COINMARKETCAP_API_KEY",
          value: "coinmarketcap_api_key_12345",
          isSecret: true,
          isLocked: false,
          description: "Clé API pour CoinMarketCap",
          category: "api",
          lastModified: new Date(Date.now() - 518400000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "NEXT_PUBLIC_MMGF_PRIVATE_KEY",
          value: "private_key_12345678abcdefgh",
          isSecret: true,
          isLocked: true,
          description: "Clé privée pour le market maker (ATTENTION: SENSIBLE)",
          category: "security",
          lastModified: new Date(Date.now() - 604800000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "PRE_MINED_MMGF_PRIVATE_KEY",
          value: "pre_mined_private_key_12345",
          isSecret: true,
          isLocked: true,
          description: "Clé privée pré-minée (ATTENTION: SENSIBLE)",
          category: "security",
          lastModified: new Date(Date.now() - 691200000).toISOString(),
          modifiedBy: "System",
        },
        {
          key: "NEXT_PUBLIC_BNB_RPC_URL",
          value: "https://bsc-dataseed.binance.org",
          isSecret: false,
          isLocked: false,
          description: "URL de l'API BNB Chain pour les connexions RPC",
          category: "blockchain",
          lastModified: new Date(Date.now() - 777600000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "ADMIN_WALLET",
          value: "******************************************",
          isSecret: false,
          isLocked: true,
          description: "Adresse du wallet administrateur",
          category: "security",
          lastModified: new Date(Date.now() - 864000000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "NEXT_PUBLIC_PANCAKESWAP_ROUTER_ADDRESS",
          value: "******************************************",
          isSecret: false,
          isLocked: false,
          description: "Adresse du routeur PancakeSwap",
          category: "blockchain",
          lastModified: new Date(Date.now() - 950400000).toISOString(),
          modifiedBy: "System",
        },
        {
          key: "FILEBASE_API_KEY",
          value: "filebase_api_key_12345",
          isSecret: true,
          isLocked: false,
          description: "Clé API pour Filebase (stockage décentralisé)",
          category: "storage",
          lastModified: new Date(Date.now() - 1036800000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "ADMIN_KEY",
          value: "admin_key_12345678abcdefgh",
          isSecret: true,
          isLocked: true,
          description: "Clé d'administration pour les opérations sensibles",
          category: "security",
          lastModified: new Date(Date.now() - 1123200000).toISOString(),
          modifiedBy: "Admin",
        },
        {
          key: "JWT_SECRET",
          value: "jwt_secret_key_12345678abcdefgh",
          isSecret: true,
          isLocked: true,
          description: "Secret pour la génération et validation des JWT",
          category: "security",
          lastModified: new Date(Date.now() - 1209600000).toISOString(),
          modifiedBy: "Admin",
        },
      ]

      setEnvVariables(mockVariables)
      setFilteredVariables(mockVariables)

      // Identifier les variables manquantes
      const missing = requiredEnvVariables
        .filter((reqVar) => !mockVariables.some(v => v.key === reqVar.key && v.value))
        .map((reqVar) => reqVar.key)
      setMissingVariables(missing)

      // Vérifier si l'environnement est verrouillé
      const lockStatus = localStorage.getItem("env_locked")
      setIsLocked(lockStatus === "true")

      setIsLoading(false)
    } catch (error) {
      console.error("Erreur lors du chargement des variables d'environnement:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les variables d'environnement",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  // Filter variables when search or category changes
  useEffect(() => {
    let filtered = [...envVariables]

    if (searchQuery) {
      filtered = filtered.filter(
        (v) =>
          v.key.toLowerCase().includes(searchQuery.toLowerCase()) ||
          v.description.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    }

    if (activeTab !== "all") {
      filtered = filtered.filter((v) => v.category === activeTab)
    }

    setFilteredVariables(filtered)
  }, [searchQuery, activeTab, envVariables])

  // Save environment variables
  const saveEnvVariables = async () => {
    setIsSaving(true)
    try {
      // Dans une implémentation réelle, cela ferait un appel API pour sauvegarder toutes les variables
      // Pour l'instant, nous simulons une sauvegarde réussie
      await new Promise((resolve) => setTimeout(resolve, 1500))

      toast({
        title: "Variables sauvegardées",
        description: "Les variables d'environnement ont été mises à jour avec succès",
      })

      // Mettre à jour la date de modification
      const updatedVariables = envVariables.map((v) => ({
        ...v,
        lastModified: new Date().toISOString(),
        modifiedBy: "Admin",
      }))

      setEnvVariables(updatedVariables)
      
      // Mettre à jour la liste des variables manquantes
      const missing = requiredEnvVariables
        .filter((reqVar) => {
          const envVar = updatedVariables.find(v => v.key === reqVar.key)
          return !envVar || !envVar.value
        })
        .map((reqVar) => reqVar.key)
      setMissingVariables(missing)
      
      setIsSaving(false)
    } catch (error) {
      console.error("Erreur lors de la sauvegarde des variables d'environnement:", error)
      toast({
        title: "Erreur",
        description: "Impossible de sauvegarder les variables d'environnement",
        variant: "destructive",
      })
      setIsSaving(false)
    }
  }

  // Lock/unlock environment
  const toggleLock = () => {
    if (isLocked) {
      setShowPasswordDialog(true)
    } else {
      setIsLocked(true)
      localStorage.setItem("env_locked", "true")
      toast({
        title: "Environnement verrouillé",
        description: "Les variables d'environnement sont maintenant protégées contre les modifications",
      })
    }
  }

  // Unlock with password
  const unlockWithPassword = () => {
    // Dans une implémentation réelle, cela validerait avec un mot de passe stocké ou appellerait une API
    if (adminPassword === "admin123") {
      setIsLocked(false)
      localStorage.setItem("env_locked", "false")
      setShowPasswordDialog(false)
      setAdminPassword("")
      toast({
        title: "Environnement déverrouillé",
        description: "Les variables d'environnement peuvent maintenant être modifiées",
      })
    } else {
      toast({
        title: "Mot de passe incorrect",
        description: "Veuillez réessayer avec le mot de passe administrateur correct",
        variant: "destructive",
      })
    }
  }

  // Update variable value
  const updateVariableValue = (key: string, value: string) => {
    if (isLocked) return

    const updatedVariables = envVariables.map((v) =>
      v.key === key ? { ...v, value, lastModified: new Date().toISOString(), modifiedBy: "Admin" } : v
    )

    setEnvVariables(updatedVariables)
    
    toast({
      title: "Variable mise à jour",
      description: `La variable ${key} a été mise à jour avec succès`,
    })
  }

  // Toggle variable lock
  const toggleVariableLock = (key: string) => {
    if (isLocked) return

    const updatedVariables = envVariables.map((v) => 
      v.key === key ? { ...v, isLocked: !v.isLocked, lastModified: new Date().toISOString(), modifiedBy: "Admin" } : v
    )

    setEnvVariables(updatedVariables)
    
    const variable = updatedVariables.find(v => v.key === key)
    
    toast({
      title: variable?.isLocked ? "Variable verrouillée" : "Variable déverrouillée",
      description: variable?.isLocked 
        ? `La variable ${key} est maintenant protégée contre les modifications` 
        : `La variable ${key} peut maintenant être modifiée`,
    })
  }

  // Copy variable value
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copié",
      description: "Valeur copiée dans le presse-papier",
    })
  }

  // Show variable history
  const showVariableHistoryDialog = (variable: EnvVariable) => {
    setEditingVariable(variable)

    // Dans une implémentation réelle, cela récupérerait l'historique depuis une API
    // Pour l'instant, nous utilisons des données fictives
    const mockHistory = [
      {
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        value:
          variable.value === "true"
            ? "false"
            : variable.value === "https://api.devnet.solana.com"
              ? "https://api.mainnet-beta.solana.com"
              : "old_value",
        modifiedBy: "Admin",
      },
      {
        timestamp: new Date(Date.now() - 172800000).toISOString(),
        value:
          variable.value === "true"
            ? "false"
            : variable.value === "https://api.devnet.solana.com"
              ? "https://api.testnet.solana.com"
              : "older_value",
        modifiedBy: "System",
      },
    ]

    setVariableHistory(mockHistory)
    setShowHistory(true)
  }

  // Edit variable
  const editVariable = (variable: EnvVariable) => {
    if (isLocked) return
    setEditingVariable({...variable})
    setShowEditDialog(true)
  }

  // Save edited variable
  const saveEditedVariable = () => {
    if (!editingVariable) return

    const updatedVariables = envVariables.map((v) =>
      v.key === editingVariable.key ? { 
        ...editingVariable, 
        lastModified: new Date().toISOString(),
        modifiedBy: "Admin"
      } : v
    )

    setEnvVariables(updatedVariables)
    setShowEditDialog(false)
    
    toast({
      title: "Variable mise à jour",
      description: `La variable ${editingVariable.key} a été mise à jour avec succès`,
    })
  }

  // Add new variable
  const addNewVariableHandler = () => {
    if (!newVariable.key || !newVariable.category) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      })
      return
    }

    // Vérifier si la variable existe déjà
    if (envVariables.some((v) => v.key === newVariable.key)) {
      toast({
        title: "Erreur",
        description: "Une variable avec cette clé existe déjà",
        variant: "destructive",
      })
      return
    }

    const newEnvVariable: EnvVariable = {
      key: newVariable.key,
      value: newVariable.value || "",
      isSecret: newVariable.isSecret || false,
      isLocked: false,
      description: newVariable.description || "",
      category: (newVariable.category as EnvVariableCategory) || "other",
      lastModified: new Date().toISOString(),
      modifiedBy: "Admin",
    }

    setEnvVariables([...envVariables, newEnvVariable])
    setShowAddDialog(false)
    setNewVariable({
      key: "",
      value: "",
      description: "",
      category: "other",
      isSecret: false,
    })

    toast({
      title: "Variable ajoutée",
      description: `La variable ${newVariable.key} a été ajoutée avec succès`,
    })
  }

  // Delete variable
  const confirmDeleteVariable = (key: string) => {
    setVariableToDelete(key)
    setShowDeleteDialog(true)
  }

  const deleteVariableHandler = () => {
    if (!variableToDelete) return

    const updatedVariables = envVariables.filter((v) => v.key !== variableToDelete)
    setEnvVariables(updatedVariables)
    setShowDeleteDialog(false)
    setVariableToDelete(null)

    toast({
      title: "Variable supprimée",
      description: `La variable ${variableToDelete} a été supprimée avec succès`,
    })
  }

  // Backup environment variables
  const backupEnvironmentVariables = () => {
    setIsBackingUp(true)
    try {
      const backup = {
        timestamp: new Date().toISOString(),
        variables: envVariables,
      }

      const backupJson = JSON.stringify(backup, null, 2)
      setBackupData(backupJson)
      setShowBackupDialog(true)
    } catch (error) {
      console.error("Erreur lors de la sauvegarde des variables d'environnement:", error)
      toast({
        title: "Erreur",
        description: "Impossible de créer une sauvegarde des variables d'environnement",
        variant: "destructive",
      })
    } finally {
      setIsBackingUp(false)
    }
  }

  // Download backup
  const downloadBackup = () => {
    const blob = new Blob([backupData], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `env-variables-backup-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    setShowBackupDialog(false)
  }

  // Restore environment variables
  const restoreEnvironmentVariables = () => {
    setShowRestoreDialog(true)
  }

  const processRestore = () => {
    setIsRestoring(true)
    try {
      const restoredData = JSON.parse(restoreData)
      
      if (!restoredData.variables || !Array.isArray(restoredData.variables)) {
        throw new Error("Format de sauvegarde invalide")
      }

      setEnvVariables(restoredData.variables)
      setShowRestoreDialog(false)
      setRestoreData("")

      toast({
        title: "Restauration réussie",
        description: `${restoredData.variables.length} variables d'environnement ont été restaurées`,
      })
    } catch (error) {
      console.error("Erreur lors de la restauration des variables d'environnement:", error)
      toast({
        title: "Erreur",
        description: "Impossible de restaurer les variables d'environnement. Format de sauvegarde invalide.",
        variant: "destructive",
      })
    } finally {
      setIsRestoring(false)
    }
  }

  // Get category badge
  const getCategoryBadge = (category: string) => {
    switch (category) {
      case "blockchain":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Blockchain</Badge>
      case "api":
        return <Badge className="bg-green-100 text-green-800 border-green-200">API</Badge>
      case "security":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Sécurité</Badge>
      case "platform":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Plateforme</Badge>
      case "storage":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Stockage</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Autre</Badge>
    }
  }

  // Mask sensitive values
  const maskValue = (value: string): string => {
    if (!value) return ""
    if (value.length <= 8) {
      return "*".repeat(value.length)
    }
    return value.substring(0, 4) + "*".repeat(value.length - 8) + value.substring(value.length - 4)
  }

  // Format date
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date)
    } catch (e) {
      return dateString
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Gestion des Variables d'Environnement</h2>
          <p className="text-muted-foreground">Configurez et gérez les variables d'environnement de la plateforme</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={backupEnvironmentVariables}
            disabled={isBackingUp || isLoading}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            <span>Sauvegarder</span>
          </Button>
          <Button
            variant="outline"
            onClick={restoreEnvironmentVariables}
            disabled={isRestoring || isLoading || isLocked}
            className="flex items\

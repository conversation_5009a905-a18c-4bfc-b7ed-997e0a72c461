"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { Bar, BarChart, Line, LineChart, ResponsiveContainer, XAxis, YAxis } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Download, Filter, FileSpreadsheet, FileText } from "lucide-react"
import { addDays, format, subDays } from "date-fns"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle2, AlertCircle } from "lucide-react"

export function FinancialReports() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isExporting, setIsExporting] = useState(false)
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 30),
    to: new Date(),
  })
  const [revenueData, setRevenueData] = useState<any[]>([])
  const [expenseData, setExpenseData] = useState<any[]>([])
  const [profitData, setProfitData] = useState<any[]>([])
  const [summaryData, setSummaryData] = useState({
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0,
    profitMargin: 0,
    tokenSales: 0,
    platformFees: 0,
    marketingExpenses: 0,
    operationalCosts: 0,
  })
  const [exportFormat, setExportFormat] = useState<"excel" | "pdf">("excel")
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Sample financial data for tables
  const revenueTableData = [
    { source: "Token Creation", amount: 15000, percentage: 45 },
    { source: "Platform Fees", amount: 8500, percentage: 25 },
    { source: "Staking Fees", amount: 5000, percentage: 15 },
    { source: "NFT Minting", amount: 3500, percentage: 10 },
    { source: "Other", amount: 1500, percentage: 5 },
  ]

  const expensesTableData = [
    { category: "Server Costs", amount: 2500, percentage: 30 },
    { category: "Development", amount: 3500, percentage: 40 },
    { category: "Marketing", amount: 1500, percentage: 20 },
    { category: "Legal", amount: 500, percentage: 5 },
    { category: "Other", amount: 500, percentage: 5 },
  ]

  // Mock metrics data
  const metrics = {
    tokenCount: 10000, // Example value, replace with actual data if available
  }

  useEffect(() => {
    const fetchFinancialData = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données fictives pour la démonstration
        const generateFinancialData = () => {
          const data = []
          let date = new Date(dateRange.from)
          const endDate = new Date(dateRange.to)

          while (date <= endDate) {
            const dayOfWeek = date.getDay()
            const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
            const baseRevenue = isWeekend ? 1500 : 2500
            const baseExpense = isWeekend ? 800 : 1200

            // Ajouter une variation aléatoire
            const revenueVariation = Math.random() * 0.4 - 0.2 // -20% à +20%
            const expenseVariation = Math.random() * 0.3 - 0.1 // -10% à +20%

            const revenue = Math.round(baseRevenue * (1 + revenueVariation))
            const expense = Math.round(baseExpense * (1 + expenseVariation))
            const profit = revenue - expense

            data.push({
              date: format(date, "yyyy-MM-dd"),
              revenue,
              expense,
              profit,
            })

            date = addDays(date, 1)
          }
          return data
        }

        const financialData = generateFinancialData()

        setRevenueData(financialData.map((item) => ({ date: item.date, value: item.revenue })))
        setExpenseData(financialData.map((item) => ({ date: item.date, value: item.expense })))
        setProfitData(financialData.map((item) => ({ date: item.date, value: item.profit })))

        // Calculer les totaux
        const totalRevenue = financialData.reduce((sum, item) => sum + item.revenue, 0)
        const totalExpenses = financialData.reduce((sum, item) => sum + item.expense, 0)
        const netProfit = totalRevenue - totalExpenses
        const profitMargin = (netProfit / totalRevenue) * 100

        // Répartition fictive des revenus et dépenses
        const tokenSales = totalRevenue * 0.75
        const platformFees = totalRevenue * 0.25
        const marketingExpenses = totalExpenses * 0.4
        const operationalCosts = totalExpenses * 0.6

        setSummaryData({
          totalRevenue,
          totalExpenses,
          netProfit,
          profitMargin,
          tokenSales,
          platformFees,
          marketingExpenses,
          operationalCosts,
        })
      } catch (error) {
        console.error("Erreur lors de la récupération des données financières:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchFinancialData()
  }, [dateRange])

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const handleDateRangeChange = (range: { from: Date; to: Date }) => {
    setDateRange(range)
  }

  const handleExportReport = async () => {
    setIsExporting(true)
    setError(null)
    setSuccess(null)

    try {
      // Simuler l'exportation avec un délai
      await new Promise((resolve) => setTimeout(resolve, 2000))

      const formatText = exportFormat.toUpperCase()
      const dateRangeText = `${format(dateRange.from, "dd/MM/yyyy")} - ${format(dateRange.to, "dd/MM/yyyy")}`

      setSuccess(`Rapport financier pour la période ${dateRangeText} exporté en ${formatText} avec succès!`)
      toast({
        title: "Rapport exporté",
        description: `Le rapport financier a été exporté en ${formatText}.`,
      })
    } catch (err) {
      console.error("Erreur lors de l'exportation du rapport:", err)
      setError("Échec de l'exportation du rapport financier. Veuillez réessayer.")
      toast({
        title: "Échec de l'exportation",
        description: "Impossible d'exporter le rapport financier. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle>Rapports financiers</CardTitle>
            <CardDescription>Analyse des revenus, dépenses et profits</CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <DateRangePicker
              initialDateFrom={dateRange.from}
              initialDateTo={dateRange.to}
              onUpdate={handleDateRangeChange}
            />
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-80">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Tabs defaultValue="summary" className="space-y-4">
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger value="summary">Résumé</TabsTrigger>
              <TabsTrigger value="revenue">Revenus</TabsTrigger>
              <TabsTrigger value="expenses">Dépenses</TabsTrigger>
              <TabsTrigger value="profit">Profit</TabsTrigger>
            </TabsList>

            <TabsContent value="summary" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="p-4 border rounded-lg">
                  <p className="text-sm text-muted-foreground">Revenus totaux</p>
                  <p className="text-2xl font-bold mt-1">{formatCurrency(summaryData.totalRevenue)}</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <p className="text-sm text-muted-foreground">Dépenses totales</p>
                  <p className="text-2xl font-bold mt-1">{formatCurrency(summaryData.totalExpenses)}</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <p className="text-sm text-muted-foreground">Profit net</p>
                  <p className="text-2xl font-bold mt-1">{formatCurrency(summaryData.netProfit)}</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <p className="text-sm text-muted-foreground">Marge bénéficiaire</p>
                  <p className="text-2xl font-bold mt-1">{summaryData.profitMargin.toFixed(1)}%</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Répartition des revenus</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-60">
                      <ChartContainer
                        config={{
                          tokenSales: {
                            label: "Ventes de tokens",
                            color: "hsl(var(--chart-1))",
                          },
                          platformFees: {
                            label: "Frais de plateforme",
                            color: "hsl(var(--chart-2))",
                          },
                        }}
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={[
                              {
                                name: "Répartition des revenus",
                                tokenSales: summaryData.tokenSales,
                                platformFees: summaryData.platformFees,
                              },
                            ]}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <XAxis dataKey="name" />
                            <YAxis />
                            <ChartTooltip content={<ChartTooltipContent />} />
                            <Bar
                              dataKey="tokenSales"
                              name="Ventes de tokens"
                              stackId="a"
                              fill="var(--color-tokenSales)"
                            />
                            <Bar
                              dataKey="platformFees"
                              name="Frais de plateforme"
                              stackId="a"
                              fill="var(--color-platformFees)"
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Ventes de tokens</p>
                        <p className="text-lg font-medium">{formatCurrency(summaryData.tokenSales)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Frais de plateforme</p>
                        <p className="text-lg font-medium">{formatCurrency(summaryData.platformFees)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Répartition des dépenses</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-60">
                      <ChartContainer
                        config={{
                          marketing: {
                            label: "Marketing",
                            color: "hsl(var(--chart-3))",
                          },
                          operational: {
                            label: "Coûts opérationnels",
                            color: "hsl(var(--chart-4))",
                          },
                        }}
                      >
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={[
                              {
                                name: "Répartition des dépenses",
                                marketing: summaryData.marketingExpenses,
                                operational: summaryData.operationalCosts,
                              },
                            ]}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <XAxis dataKey="name" />
                            <YAxis />
                            <ChartTooltip content={<ChartTooltipContent />} />
                            <Bar dataKey="marketing" name="Marketing" stackId="a" fill="var(--color-marketing)" />
                            <Bar
                              dataKey="operational"
                              name="Coûts opérationnels"
                              stackId="a"
                              fill="var(--color-operational)"
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Marketing</p>
                        <p className="text-lg font-medium">{formatCurrency(summaryData.marketingExpenses)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Coûts opérationnels</p>
                        <p className="text-lg font-medium">{formatCurrency(summaryData.operationalCosts)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="revenue" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Évolution des revenus</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ChartContainer
                      config={{
                        revenue: {
                          label: "Revenus",
                          color: "hsl(var(--chart-1))",
                        },
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={revenueData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                          <XAxis
                            dataKey="date"
                            tickFormatter={(value) => {
                              const date = new Date(value)
                              return `${date.getDate()}/${date.getMonth() + 1}`
                            }}
                          />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Line
                            type="monotone"
                            dataKey="value"
                            name="Revenus"
                            stroke="var(--color-revenue)"
                            strokeWidth={2}
                            dot={false}
                            activeDot={{ r: 6 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Détail des sources de revenus</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Source de revenus</TableHead>
                        <TableHead className="text-right">Montant</TableHead>
                        <TableHead className="text-right">Pourcentage</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {revenueTableData.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.source}</TableCell>
                          <TableCell className="text-right">${item.amount.toLocaleString()}</TableCell>
                          <TableCell className="text-right">{item.percentage}%</TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell className="font-bold">Total</TableCell>
                        <TableCell className="text-right font-bold">$33,500</TableCell>
                        <TableCell className="text-right font-bold">100%</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="expenses" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Évolution des dépenses</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ChartContainer
                      config={{
                        expenses: {
                          label: "Dépenses",
                          color: "hsl(var(--chart-3))",
                        },
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={expenseData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                          <XAxis
                            dataKey="date"
                            tickFormatter={(value) => {
                              const date = new Date(value)
                              return `${date.getDate()}/${date.getMonth() + 1}`
                            }}
                          />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Line
                            type="monotone"
                            dataKey="value"
                            name="Dépenses"
                            stroke="var(--color-expenses)"
                            strokeWidth={2}
                            dot={false}
                            activeDot={{ r: 6 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Détail des catégories de dépenses</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Catégorie de dépenses</TableHead>
                        <TableHead className="text-right">Montant</TableHead>
                        <TableHead className="text-right">Pourcentage</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {expensesTableData.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.category}</TableCell>
                          <TableCell className="text-right">${item.amount.toLocaleString()}</TableCell>
                          <TableCell className="text-right">{item.percentage}%</TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell className="font-bold">Total</TableCell>
                        <TableCell className="text-right font-bold">$8,500</TableCell>
                        <TableCell className="text-right font-bold">100%</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="profit" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Évolution du profit</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ChartContainer
                      config={{
                        profit: {
                          label: "Profit",
                          color: "hsl(var(--chart-2))",
                        },
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={profitData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                          <XAxis
                            dataKey="date"
                            tickFormatter={(value) => {
                              const date = new Date(value)
                              return `${date.getDate()}/${date.getMonth() + 1}`
                            }}
                          />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Line
                            type="monotone"
                            dataKey="value"
                            name="Profit"
                            stroke="var(--color-profit)"
                            strokeWidth={2}
                            dot={false}
                            activeDot={{ r: 6 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Résumé du profit</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-lg">
                      <p className="text-sm text-muted-foreground">Profit net</p>
                      <p className="text-2xl font-bold mt-1">{formatCurrency(summaryData.netProfit)}</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <p className="text-sm text-muted-foreground">Marge bénéficiaire</p>
                      <p className="text-2xl font-bold mt-1">{summaryData.profitMargin.toFixed(1)}%</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <p className="text-sm text-muted-foreground">Profit par token</p>
                      <p className="text-2xl font-bold mt-1">
                        {formatCurrency(summaryData.netProfit / (metrics.tokenCount || 1))}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}

        <div className="mt-6 border-t pt-6">
          <h3 className="text-lg font-medium mb-4">Exporter les rapports</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="exportFormat">Format d'exportation</Label>
              <Select value={exportFormat} onValueChange={(value: "excel" | "pdf") => setExportFormat(value)}>
                <SelectTrigger id="exportFormat">
                  <SelectValue placeholder="Sélectionner un format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">
                    <div className="flex items-center">
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      <span>Excel (.xlsx)</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="pdf">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2" />
                      <span>PDF (.pdf)</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mt-4 bg-green-50 text-green-800 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle>Succès</AlertTitle>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleExportReport} disabled={isExporting} className="flex items-center">
          <Download className="mr-2 h-4 w-4" />
          {isExporting ? "Exportation en cours..." : "Exporter le rapport"}
        </Button>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowUp } from "lucide-react"

export function PresaleStatistics() {
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState({
    totalPresales: 0,
    activePresales: 0,
    upcomingPresales: 0,
    completedPresales: 0,
    totalRaised: 0,
    averageRoi: 0,
    participantsCount: 0,
    successRate: 0,
  })

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données fictives pour la démonstration
        setStats({
          totalPresales: 15,
          activePresales: 3,
          upcomingPresales: 5,
          completedPresales: 7,
          totalRaised: 1250000,
          averageRoi: 3.2,
          participantsCount: 4500,
          successRate: 85,
        })
      } catch (error) {
        console.error("Erreur lors de la récupération des statistiques:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchStats()
  }, [])

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("fr-FR").format(value)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Statistiques des Presales</CardTitle>
        <CardDescription>Vue d'ensemble des performances des presales</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid grid-cols-2 w-full">
              <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Total des presales</p>
                  <p className="text-2xl font-bold">{stats.totalPresales}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Presales actives</p>
                  <p className="text-2xl font-bold">{stats.activePresales}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Presales à venir</p>
                  <p className="text-2xl font-bold">{stats.upcomingPresales}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Presales terminées</p>
                  <p className="text-2xl font-bold">{stats.completedPresales}</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Total levé</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalRaised)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">ROI moyen</p>
                  <div className="flex items-center">
                    <p className="text-2xl font-bold">{stats.averageRoi}x</p>
                    <ArrowUp className="ml-2 h-4 w-4 text-green-500" />
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Participants</p>
                  <p className="text-2xl font-bold">{formatNumber(stats.participantsCount)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Taux de réussite</p>
                  <p className="text-2xl font-bold">{stats.successRate}%</p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

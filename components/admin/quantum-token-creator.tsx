"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Info, Rocket, Shield, TrendingUp, ArrowLeft, Sparkles } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function QuantumTokenCreator() {
  const router = useRouter()
  const { toast } = useToast()

  const [activeTab, setActiveTab] = useState("basic")
  const [isCreating, setIsCreating] = useState(false)

  // Configuration de base
  const [name, setName] = useState("")
  const [symbol, setSymbol] = useState("")
  const [suffix, setSuffix] = useState("QUANTUM")
  const [decimals, setDecimals] = useState("9")
  const [totalSupply, setTotalSupply] = useState("1000000000")
  const [description, setDescription] = useState("")
  const [website, setWebsite] = useState("")
  const [twitter, setTwitter] = useState("")
  const [telegram, setTelegram] = useState("")

  // Configuration du lancement
  const [initialPrice, setInitialPrice] = useState("0.00005")
  const [softCap, setSoftCap] = useState("50")
  const [hardCap, setHardCap] = useState("200")
  const [minBuy, setMinBuy] = useState("0.1")
  const [maxBuy, setMaxBuy] = useState("5")

  // Distribution des tokens
  const [liquidityPercentage, setLiquidityPercentage] = useState(70)
  const [teamPercentage, setTeamPercentage] = useState(10)
  const [marketingPercentage, setMarketingPercentage] = useState(10)
  const [reservePercentage, setReservePercentage] = useState(10)

  // Périodes de verrouillage
  const [liquidityLockPeriod, setLiquidityLockPeriod] = useState("180")
  const [teamLockPeriod, setTeamLockPeriod] = useState("90")

  // Phases de lancement
  const [presaleEnabled, setPresaleEnabled] = useState(true)
  const [fairLaunchEnabled, setFairLaunchEnabled] = useState(true)
  const [idoEnabled, setIdoEnabled] = useState(false)

  // Fonctionnalités de sécurité
  const [antiBot, setAntiBot] = useState(true)
  const [antiDump, setAntiDump] = useState(true)
  const [maxWalletPercentage, setMaxWalletPercentage] = useState(2)
  const [maxTxPercentage, setMaxTxPercentage] = useState(1)

  // Frais et taxes
  const [buyTax, setBuyTax] = useState(5)
  const [sellTax, setSellTax] = useState(7)
  const [transferTax, setTransferTax] = useState(3)

  // Adresses des wallets
  const [teamWallet, setTeamWallet] = useState("")
  const [marketingWallet, setMarketingWallet] = useState("")

  // Paramètres DEX
  const [targetDex, setTargetDex] = useState("all")
  const [listingMultiplier, setListingMultiplier] = useState(1.5)

  // Paramètres administratifs
  const [featured, setFeatured] = useState(false)
  const [verified, setVerified] = useState(true)
  const [discountPercentage, setDiscountPercentage] = useState(0)
  const [ownerWallet, setOwnerWallet] = useState("")

  // Validation du formulaire
  const validateForm = () => {
    if (!name) {
      toast({
        title: "Missing token name",
        description: "Please enter a name for your token",
        variant: "destructive",
      })
      setActiveTab("basic")
      return false
    }

    if (!symbol) {
      toast({
        title: "Missing token symbol",
        description: "Please enter a symbol for your token",
        variant: "destructive",
      })
      setActiveTab("basic")
      return false
    }

    // Vérifier que la distribution totalise 100%
    const totalDistribution = liquidityPercentage + teamPercentage + marketingPercentage + reservePercentage
    if (totalDistribution !== 100) {
      toast({
        title: "Invalid distribution",
        description: `Token distribution must total 100% (currently ${totalDistribution}%)`,
        variant: "destructive",
      })
      setActiveTab("tokenomics")
      return false
    }

    // Vérifier que le soft cap est inférieur au hard cap
    if (Number.parseFloat(softCap) >= Number.parseFloat(hardCap)) {
      toast({
        title: "Invalid caps",
        description: "Soft cap must be less than hard cap",
        variant: "destructive",
      })
      setActiveTab("launch")
      return false
    }

    // Vérifier que le min buy est inférieur au max buy
    if (Number.parseFloat(minBuy) >= Number.parseFloat(maxBuy)) {
      toast({
        title: "Invalid buy limits",
        description: "Minimum buy must be less than maximum buy",
        variant: "destructive",
      })
      setActiveTab("launch")
      return false
    }

    if (!ownerWallet) {
      toast({
        title: "Missing owner wallet",
        description: "Please enter the owner wallet address",
        variant: "destructive",
      })
      setActiveTab("admin")
      return false
    }

    return true
  }

  // Créer le lancement
  const createLaunch = async () => {
    if (!validateForm()) return

    setIsCreating(true)

    try {
      // Préparer la configuration
      const launchConfig = {
        name,
        symbol,
        suffix,
        decimals: Number.parseInt(decimals),
        totalSupply: Number.parseInt(totalSupply),
        description,
        website,
        twitter,
        telegram,

        initialPrice: Number.parseFloat(initialPrice),
        softCap: Number.parseFloat(softCap),
        hardCap: Number.parseFloat(hardCap),
        minBuy: Number.parseFloat(minBuy),
        maxBuy: Number.parseFloat(maxBuy),

        liquidityPercentage,
        teamPercentage,
        marketingPercentage,
        reservePercentage,

        liquidityLockPeriod: Number.parseInt(liquidityLockPeriod),
        teamLockPeriod: Number.parseInt(teamLockPeriod),

        phases: {
          presale: presaleEnabled,
          fairLaunch: fairLaunchEnabled,
          initialDexOffering: idoEnabled,
        },

        antiBot,
        antiDump,
        maxWalletPercentage,
        maxTxPercentage,

        buyTax,
        sellTax,
        transferTax,

        teamWallet,
        marketingWallet,

        targetDex,
        listingMultiplier,

        // Paramètres administratifs
        featured,
        verified,
        discountPercentage,
        ownerWallet,
      }

      // Simuler la création du lancement
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Générer un ID de lancement aléatoire
      const launchId = `launch_${Math.random().toString(36).substring(2, 15)}`

      toast({
        title: "Launch created",
        description: "The token launch has been created successfully",
      })

      // Rediriger vers la page de gestion des lancements
      router.push("/admin/quantum-tokens")
    } catch (error) {
      console.error("Error creating launch:", error)
      toast({
        title: "Launch creation failed",
        description: "Failed to create the token launch. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/quantum-tokens">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">Create Quantum Token</h2>
        </div>
        <Badge className="bg-[#D4AF37] text-black">Admin</Badge>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Token Launch Configuration</CardTitle>
          <CardDescription>Set up the parameters for the new Quantum token launch</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-5 mb-6">
              <TabsTrigger value="basic">
                <Info className="h-4 w-4 mr-2" />
                Basic
              </TabsTrigger>
              <TabsTrigger value="launch">
                <Rocket className="h-4 w-4 mr-2" />
                Launch
              </TabsTrigger>
              <TabsTrigger value="tokenomics">
                <TrendingUp className="h-4 w-4 mr-2" />
                Tokenomics
              </TabsTrigger>
              <TabsTrigger value="security">
                <Shield className="h-4 w-4 mr-2" />
                Security
              </TabsTrigger>
              <TabsTrigger value="admin">
                <Info className="h-4 w-4 mr-2" />
                Admin
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Enter the basic information about the token. This information will be visible to all users.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="tokenName">Token Name</Label>
                    <Input
                      id="tokenName"
                      placeholder="Ex: Global Finance"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tokenSymbol">Token Symbol</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="tokenSymbol"
                        placeholder="Ex: GF"
                        value={symbol}
                        onChange={(e) => setSymbol(e.target.value.toUpperCase())}
                        className="uppercase"
                      />
                      <Select value={suffix} onValueChange={setSuffix}>
                        <SelectTrigger className="w-[140px]">
                          <SelectValue placeholder="Suffix" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="QUANTUM">QUANTUM</SelectItem>
                          <SelectItem value="GF">GF</SelectItem>
                          <SelectItem value="SOL">SOL</SelectItem>
                          <SelectItem value="MEME">MEME</SelectItem>
                          <SelectItem value="AI">AI</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Final symbol will be: {symbol}
                      {suffix}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="tokenDecimals">Decimals</Label>
                    <Select value={decimals} onValueChange={setDecimals}>
                      <SelectTrigger id="tokenDecimals">
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="6">6</SelectItem>
                        <SelectItem value="9">9</SelectItem>
                        <SelectItem value="12">12</SelectItem>
                        <SelectItem value="18">18</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tokenSupply">Total Supply</Label>
                    <Input
                      id="tokenSupply"
                      type="number"
                      placeholder="1000000000"
                      value={totalSupply}
                      onChange={(e) => setTotalSupply(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenDescription">Description</Label>
                  <Textarea
                    id="tokenDescription"
                    placeholder="Describe the token and its utility..."
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={4}
                  />
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Social Links</h3>

                  <div className="space-y-2">
                    <Label htmlFor="tokenWebsite">Website</Label>
                    <Input
                      id="tokenWebsite"
                      placeholder="https://your-site.com"
                      value={website}
                      onChange={(e) => setWebsite(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tokenTwitter">Twitter</Label>
                    <Input
                      id="tokenTwitter"
                      placeholder="https://twitter.com/your-account"
                      value={twitter}
                      onChange={(e) => setTwitter(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tokenTelegram">Telegram</Label>
                    <Input
                      id="tokenTelegram"
                      placeholder="https://t.me/your-group"
                      value={telegram}
                      onChange={(e) => setTelegram(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="launch" className="space-y-4">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <Rocket className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Configure the token launch parameters, including fundraising goals and phases.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="initialPrice">Initial Token Price (USD)</Label>
                    <Input
                      id="initialPrice"
                      type="number"
                      step="0.00001"
                      placeholder="0.00005"
                      value={initialPrice}
                      onChange={(e) => setInitialPrice(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="listingMultiplier">Listing Price Multiplier</Label>
                    <div className="flex items-center space-x-2">
                      <Slider
                        id="listingMultiplier"
                        min={1}
                        max={3}
                        step={0.1}
                        value={[listingMultiplier]}
                        onValueChange={(value) => setListingMultiplier(value[0])}
                      />
                      <span className="w-12 text-center">{listingMultiplier}x</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Listing price: ${(Number.parseFloat(initialPrice) * listingMultiplier).toFixed(6)}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="softCap">Soft Cap (SOL)</Label>
                    <Input
                      id="softCap"
                      type="number"
                      placeholder="50"
                      value={softCap}
                      onChange={(e) => setSoftCap(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">Minimum amount to raise for the launch to proceed</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="hardCap">Hard Cap (SOL)</Label>
                    <Input
                      id="hardCap"
                      type="number"
                      placeholder="200"
                      value={hardCap}
                      onChange={(e) => setHardCap(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">Maximum amount that can be raised</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minBuy">Minimum Buy (SOL)</Label>
                    <Input
                      id="minBuy"
                      type="number"
                      step="0.1"
                      placeholder="0.1"
                      value={minBuy}
                      onChange={(e) => setMinBuy(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxBuy">Maximum Buy (SOL)</Label>
                    <Input
                      id="maxBuy"
                      type="number"
                      step="0.1"
                      placeholder="5"
                      value={maxBuy}
                      onChange={(e) => setMaxBuy(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Launch Phases</h3>

                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="presaleEnabled" className="cursor-pointer">
                      Enable Presale
                      <p className="text-xs text-muted-foreground font-normal">
                        Initial fundraising phase at a discounted price
                      </p>
                    </Label>
                    <Switch id="presaleEnabled" checked={presaleEnabled} onCheckedChange={setPresaleEnabled} />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="fairLaunchEnabled" className="cursor-pointer">
                      Enable Fair Launch
                      <p className="text-xs text-muted-foreground font-normal">
                        Equal opportunity launch phase for all participants
                      </p>
                    </Label>
                    <Switch id="fairLaunchEnabled" checked={fairLaunchEnabled} onCheckedChange={setFairLaunchEnabled} />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="idoEnabled" className="cursor-pointer">
                      Enable IDO (Initial DEX Offering)
                      <p className="text-xs text-muted-foreground font-normal">Final phase before DEX listing</p>
                    </Label>
                    <Switch id="idoEnabled" checked={idoEnabled} onCheckedChange={setIdoEnabled} />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetDex">Target DEX</Label>
                  <Select value={targetDex} onValueChange={setTargetDex}>
                    <SelectTrigger id="targetDex">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Available DEXs</SelectItem>
                      <SelectItem value="raydium">Raydium</SelectItem>
                      <SelectItem value="orca">Orca</SelectItem>
                      <SelectItem value="jupiter">Jupiter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="tokenomics" className="space-y-4">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <TrendingUp className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Configure the token distribution and tax structure. Total distribution must equal 100%.
                </AlertDescription>
              </Alert>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Token Distribution</h3>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="liquidityPercentage">Liquidity Pool (%)</Label>
                        <span className="text-sm font-medium">{liquidityPercentage}%</span>
                      </div>
                      <Slider
                        id="liquidityPercentage"
                        min={50}
                        max={90}
                        step={1}
                        value={[liquidityPercentage]}
                        onValueChange={(value) => setLiquidityPercentage(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">
                        Percentage of tokens allocated to the liquidity pool
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="teamPercentage">Team (%)</Label>
                        <span className="text-sm font-medium">{teamPercentage}%</span>
                      </div>
                      <Slider
                        id="teamPercentage"
                        min={0}
                        max={20}
                        step={1}
                        value={[teamPercentage]}
                        onValueChange={(value) => setTeamPercentage(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">Percentage of tokens allocated to the team</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="marketingPercentage">Marketing (%)</Label>
                        <span className="text-sm font-medium">{marketingPercentage}%</span>
                      </div>
                      <Slider
                        id="marketingPercentage"
                        min={0}
                        max={20}
                        step={1}
                        value={[marketingPercentage]}
                        onValueChange={(value) => setMarketingPercentage(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">Percentage of tokens allocated to marketing</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="reservePercentage">Reserve (%)</Label>
                        <span className="text-sm font-medium">{reservePercentage}%</span>
                      </div>
                      <Slider
                        id="reservePercentage"
                        min={0}
                        max={20}
                        step={1}
                        value={[reservePercentage]}
                        onValueChange={(value) => setReservePercentage(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">Percentage of tokens allocated to reserve</p>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                      <span className="font-medium">Total Distribution</span>
                      <span
                        className={`font-bold ${
                          liquidityPercentage + teamPercentage + marketingPercentage + reservePercentage !== 100
                            ? "text-red-500"
                            : "text-green-500"
                        }`}
                      >
                        {liquidityPercentage + teamPercentage + marketingPercentage + reservePercentage}%
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Transaction Taxes</h3>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="buyTax">Buy Tax (%)</Label>
                        <span className="text-sm font-medium">{buyTax}%</span>
                      </div>
                      <Slider
                        id="buyTax"
                        min={0}
                        max={10}
                        step={1}
                        value={[buyTax]}
                        onValueChange={(value) => setBuyTax(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">Tax applied when buying the token</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="sellTax">Sell Tax (%)</Label>
                        <span className="text-sm font-medium">{sellTax}%</span>
                      </div>
                      <Slider
                        id="sellTax"
                        min={0}
                        max={10}
                        step={1}
                        value={[sellTax]}
                        onValueChange={(value) => setSellTax(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">Tax applied when selling the token</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="transferTax">Transfer Tax (%)</Label>
                        <span className="text-sm font-medium">{transferTax}%</span>
                      </div>
                      <Slider
                        id="transferTax"
                        min={0}
                        max={10}
                        step={1}
                        value={[transferTax]}
                        onValueChange={(value) => setTransferTax(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">Tax applied when transferring the token</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Lock Periods</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="liquidityLockPeriod">Liquidity Lock Period (days)</Label>
                      <Select value={liquidityLockPeriod} onValueChange={setLiquidityLockPeriod}>
                        <SelectTrigger id="liquidityLockPeriod">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="30">30 days</SelectItem>
                          <SelectItem value="90">90 days</SelectItem>
                          <SelectItem value="180">180 days</SelectItem>
                          <SelectItem value="365">365 days</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="teamLockPeriod">Team Token Lock Period (days)</Label>
                      <Select value={teamLockPeriod} onValueChange={setTeamLockPeriod}>
                        <SelectTrigger id="teamLockPeriod">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="30">30 days</SelectItem>
                          <SelectItem value="90">90 days</SelectItem>
                          <SelectItem value="180">180 days</SelectItem>
                          <SelectItem value="365">365 days</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <Shield className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Configure security features to protect the token and its holders from malicious actors.
                </AlertDescription>
              </Alert>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Anti-Bot Protection</h3>

                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="antiBot" className="cursor-pointer">
                      Enable Anti-Bot Protection
                      <p className="text-xs text-muted-foreground font-normal">
                        Prevents bots from sniping tokens at launch
                      </p>
                    </Label>
                    <Switch id="antiBot" checked={antiBot} onCheckedChange={setAntiBot} />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Anti-Dump Protection</h3>

                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="antiDump" className="cursor-pointer">
                      Enable Anti-Dump Protection
                      <p className="text-xs text-muted-foreground font-normal">
                        Prevents large holders from dumping tokens and crashing the price
                      </p>
                    </Label>
                    <Switch id="antiDump" checked={antiDump} onCheckedChange={setAntiDump} />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Transaction Limits</h3>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="maxWalletPercentage">Max Wallet Size (% of supply)</Label>
                        <span className="text-sm font-medium">{maxWalletPercentage}%</span>
                      </div>
                      <Slider
                        id="maxWalletPercentage"
                        min={0.5}
                        max={5}
                        step={0.5}
                        value={[maxWalletPercentage]}
                        onValueChange={(value) => setMaxWalletPercentage(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">
                        Maximum percentage of total supply a single wallet can hold
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="maxTxPercentage">Max Transaction Size (% of supply)</Label>
                        <span className="text-sm font-medium">{maxTxPercentage}%</span>
                      </div>
                      <Slider
                        id="maxTxPercentage"
                        min={0.1}
                        max={3}
                        step={0.1}
                        value={[maxTxPercentage]}
                        onValueChange={(value) => setMaxTxPercentage(value[0])}
                      />
                      <p className="text-xs text-muted-foreground">
                        Maximum percentage of total supply that can be transferred in a single transaction
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Wallet Addresses</h3>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="teamWallet">Team Wallet Address</Label>
                      <Input
                        id="teamWallet"
                        placeholder="Solana wallet address"
                        value={teamWallet}
                        onChange={(e) => setTeamWallet(e.target.value)}
                      />
                      <p className="text-xs text-muted-foreground">Wallet to receive team token allocation</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="marketingWallet">Marketing Wallet Address</Label>
                      <Input
                        id="marketingWallet"
                        placeholder="Solana wallet address"
                        value={marketingWallet}
                        onChange={(e) => setMarketingWallet(e.target.value)}
                      />
                      <p className="text-xs text-muted-foreground">Wallet to receive marketing token allocation</p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="admin" className="space-y-4">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Configure administrative settings for the token launch. These settings are only visible to admins.
                </AlertDescription>
              </Alert>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Owner Settings</h3>

                  <div className="space-y-2">
                    <Label htmlFor="ownerWallet">Owner Wallet Address</Label>
                    <Input
                      id="ownerWallet"
                      placeholder="Solana wallet address"
                      value={ownerWallet}
                      onChange={(e) => setOwnerWallet(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">Wallet that will own the token contract</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Visibility Settings</h3>

                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="featured" className="cursor-pointer">
                      Featured Token
                      <p className="text-xs text-muted-foreground font-normal">
                        Display this token prominently on the platform
                      </p>
                    </Label>
                    <Switch id="featured" checked={featured} onCheckedChange={setFeatured} />
                  </div>

                  <div className="flex items-center justify-between space-x-2 mt-4">
                    <Label htmlFor="verified" className="cursor-pointer">
                      Verified Token
                      <p className="text-xs text-muted-foreground font-normal">
                        Mark this token as verified by the platform
                      </p>
                    </Label>
                    <Switch id="verified" checked={verified} onCheckedChange={setVerified} />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Pricing Settings</h3>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="discountPercentage">Discount Percentage</Label>
                      <span className="text-sm font-medium">{discountPercentage}%</span>
                    </div>
                    <Slider
                      id="discountPercentage"
                      min={0}
                      max={50}
                      step={5}
                      value={[discountPercentage]}
                      onValueChange={(value) => setDiscountPercentage(value[0])}
                    />
                    <p className="text-xs text-muted-foreground">Discount applied to the token creation fee</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/admin/quantum-tokens">Cancel</Link>
          </Button>
          <Button className="bg-[#D4AF37] hover:bg-[#B8941F] text-black" onClick={createLaunch} disabled={isCreating}>
            {isCreating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Create Quantum Token
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

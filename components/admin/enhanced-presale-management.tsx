"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, ChevronDown, ChevronUp, Search, Filter } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

// Types
interface TokenInfo {
  address: string
  name: string
  symbol: string
  decimals: number
  totalSupply: number
}

interface PresaleConfig {
  tokenInfo: TokenInfo
  price: number
  hardCap: number
  softCap: number
  minPurchase: number
  maxPurchase: number
  startTime: Date
  endTime: Date
  vestingPeriod: number
  vestingRelease: "linear" | "cliff" | "staged"
  liquidityPercentage: number
  liquidityLockPeriod: number
  teamTokens: number
  marketingTokens: number
  whitelistEnabled: boolean
  publicSaleDelay: number
  description: string
  website?: string
  twitter?: string
  telegram?: string
  whitepaper?: string
}

interface Presale {
  id: string
  config: PresaleConfig
  status: "pending" | "active" | "completed" | "cancelled" | "failed"
  raisedAmount: number
  participants: number
  progress: number
  createdAt: number
  updatedAt: number
}

interface Participant {
  address: string
  amount: number
  tokens: number
  timestamp: number
  claimed: boolean
  claimTimestamp?: number
  transactionId: string
}

export function EnhancedPresaleManagement() {
  const [presales, setPresales] = useState<Presale[]>([])
  const [filteredPresales, setFilteredPresales] = useState<Presale[]>([])
  const [selectedPresale, setSelectedPresale] = useState<Presale | null>(null)
  const [participants, setParticipants] = useState<Participant[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [isFinalizing, setIsFinalizing] = useState(false)
  const [isCancelling, setIsCancelling] = useState(false)
  const [isRefunding, setIsRefunding] = useState(false)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showParticipantsDialog, setShowParticipantsDialog] = useState(false)
  const [showWhitelistDialog, setShowWhitelistDialog] = useState(false)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [presaleToDelete, setPresaleToDelete] = useState<Presale | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [sortField, setSortField] = useState<string>("createdAt")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [whitelistAddresses, setWhitelistAddresses] = useState<string>("")
  const [newPresale, setNewPresale] = useState<Partial<PresaleConfig>>({
    price: 0.001,
    hardCap: 1000000,
    softCap: 500000,
    minPurchase: 0.1,
    maxPurchase: 10,
    startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // +1 jour
    endTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // +14 jours
    vestingPeriod: 90,
    vestingRelease: "linear",
    liquidityPercentage: 70,
    liquidityLockPeriod: 180,
    teamTokens: 10,
    marketingTokens: 5,
    whitelistEnabled: false,
    publicSaleDelay: 0,
  })
  const { toast } = useToast()

  // Charger les données
  useEffect(() => {
    fetchPresales()
  }, [])

  // Filtrer et trier les presales
  useEffect(() => {
    let filtered = [...presales]

    // Filtrer par statut
    if (statusFilter !== "all") {
      filtered = filtered.filter((presale) => presale.status === statusFilter)
    }

    // Filtrer par recherche
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(
        (presale) =>
          presale.config.tokenInfo.name.toLowerCase().includes(term) ||
          presale.config.tokenInfo.symbol.toLowerCase().includes(term) ||
          presale.id.toLowerCase().includes(term)
      )
    }

    // Trier
    filtered.sort((a, b) => {
      let valueA, valueB

      switch (sortField) {
        case "tokenName":
          valueA = a.config.tokenInfo.name
          valueB = b.config.tokenInfo.name
          break
        case "price":
          valueA = a.config.price
          valueB = b.config.price
          break
        case "hardCap":
          valueA = a.config.hardCap
          valueB = b.config.hardCap
          break
        case "progress":
          valueA = a.progress
          valueB = b.progress
          break
        case "startTime":
          valueA = a.config.startTime.getTime()
          valueB = b.config.startTime.getTime()
          break
        case "endTime":
          valueA = a.config.endTime.getTime()
          valueB = b.config.endTime.getTime()
          break
        case "status":
          valueA = a.status
          valueB = b.status
          break
        case "createdAt":
        default:
          valueA = a.createdAt
          valueB = b.createdAt
          break
      }

      if (typeof valueA === "string" && typeof valueB === "string") {
        return sortDirection === "asc"
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA)
      } else {
        return sortDirection === "asc"
          ? (valueA as number) - (valueB as number)
          : (valueB as number) - (valueA as number)
      }
    })

    setFilteredPresales(filtered)
  }, [presales, searchTerm, statusFilter, sortField, sortDirection])

  const fetchPresales = async () => {
    setIsLoading(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons des données pour la démonstration
      setTimeout(() => {
        const mockPresales: Presale[] = [
          {
            id: "presale_1",
            config: {
              tokenInfo: {
                address: "token_address_1",
                name: "Solana Platform Token",
                symbol: "SPT",
                decimals: 9,
                totalSupply: 1000000000,
              },
              price: 0.005,
              hardCap: 1000000,
              softCap: 500000,
              minPurchase: 0.1,
              maxPurchase: 10,
              startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // -7 jours
              endTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // +14 jours
              vestingPeriod: 90,
              vestingRelease: "linear",
              liquidityPercentage: 70,
              liquidityLockPeriod: 180,
              teamTokens: 10,
              marketingTokens: 5,
              whitelistEnabled: false,
              publicSaleDelay: 0,
              description: "Le token officiel de la plateforme Solana Platform.",
              website: "https://solanaplatform.com",
              twitter: "https://twitter.com/solanaplatform",
              telegram: "https://t.me/solanaplatform",
            },
            status: "active",
            raisedAmount: 350000,
            participants: 120,
            progress: 35,
            createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000,
            updatedAt: Date.now() - 1 * 24 * 60 * 60 * 1000,
          },
          {
            id: "presale_2",
            config: {
              tokenInfo: {
                address: "token_address_2",
                name: "Meme Coin",
                symbol: "MEME",
                decimals: 9,
                totalSupply: 1000000000000,
              },
              price: 0.001,
              hardCap: 500000,
              softCap: 100000,
              minPurchase: 0.1,
              maxPurchase: 5,
              startTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // +3 jours
              endTime: new Date(Date.now() + 17 * 24 * 60 * 60 * 1000), // +17 jours
              vestingPeriod: 60,
              vestingRelease: "cliff",
              liquidityPercentage: 60,
              liquidityLockPeriod: 365,
              teamTokens: 15,
              marketingTokens: 10,
              whitelistEnabled: true,
              publicSaleDelay: 24,
              description: "Le prochain grand meme coin sur Solana.",
              website: "https://memecoin.io",
              twitter: "https://twitter.com/memecoin",
              telegram: "https://t.me/memecoin",
            },
            status: "pending",
            raisedAmount: 0,
            participants: 0,
            progress: 0,
            createdAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
            updatedAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
          },
          {
            id: "presale_3",
            config: {
              tokenInfo: {
                address: "token_address_3",
                name: "Utility Token",
                symbol: "UTIL",
                decimals: 9,
                totalSupply: 100000000,
              },
              price: 0.01,
              hardCap: 200000,
              softCap: 100000,
              minPurchase: 0.5,
              maxPurchase: 20,
              startTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // -30 jours
              endTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // -2 jours
              vestingPeriod: 120,
              vestingRelease: "staged",
              liquidityPercentage: 80,
              liquidityLockPeriod: 730,
              teamTokens: 5,
              marketingTokens: 5,
              whitelistEnabled: false,
              publicSaleDelay: 0,
              description: "Un token utilitaire pour l'écosystème DeFi.",
              website: "https://utilitytoken.finance",
              twitter: "https://twitter.com/utilitytoken",
              telegram: "https://t.me/utilitytoken",
              whitepaper: "https://utilitytoken.finance/whitepaper.pdf",
            },
            status: "completed",
            raisedAmount: 180000,
            participants: 75,
            progress: 90,
            createdAt: Date.now() - 45 * 24 * 60 * 60 * 1000,
            updatedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
          },
        ]

        setPresales(mockPresales)
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error("Error fetching presales:", error)
      toast({
        title: "Erreur",
        description: "Impossible de récupérer les presales",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const fetchParticipants = async (presaleId: string) => {
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons des données pour la démonstration
      setTimeout(() => {
        const mockParticipants: Participant[] = [
          {
            address: "wallet_address_1",
            amount: 5,
            tokens: 5000000,
            timestamp: Date.now() - 5 * 24 * 60 * 60 * 1000,
            claimed: false,
            transactionId: "tx_123456",
          },
          {
            address: "wallet_address_2",
            amount: 2.5,
            tokens: 2500000,
            timestamp: Date.now() - 4 * 24 * 60 * 60 * 1000,
            claimed: false,
            transactionId: "tx_234567",
          },
          {
            address: "wallet_address_3",
            amount: 10,
            tokens: 10000000,
            timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000,
            claimed: true,
            claimTimestamp: Date.now() - 1 * 24 * 60 * 60 * 1000,
            transactionId: "tx_345678",
          },
        ]

        setParticipants(mockParticipants)
      }, 500)
    } catch (error) {
      console.error("Error fetching participants:", error)
      toast({
        title: "Erreur",
        description: "Impossible de récupérer les participants",
        variant: "destructive",
      })
    }
  }

  const handleCreatePresale = async () => {
    setIsCreating(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons une création réussie
      setTimeout(() => {
        const newPresaleId = `presale_${Date.now()}`
        const createdPresale: Presale = {
          id: newPresaleId,
          config: newPresale as PresaleConfig,
          status: "pending",
          raisedAmount: 0,
          participants: 0,
          progress: 0,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }

        setPresales([...presales, createdPresale])
        setShowCreateDialog(false)
        setNewPresale({
          price: 0.001,
          hardCap: 1000000,
          softCap: 500000,
          minPurchase: 0.1,
          maxPurchase: 10,
          startTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
          endTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          vestingPeriod: 90,
          vestingRelease: "linear",
          liquidityPercentage: 70,
          liquidityLockPeriod: 180,
          teamTokens: 10,
          marketingTokens: 5,
          whitelistEnabled: false,
          publicSaleDelay: 0,
        })

        toast({
          title: "Succès",
          description: "Presale créée avec succès",
        })
      }, 1500)
    } catch (error) {
      console.error("Error creating presale:", error)
      toast({
        title: "Erreur",
        description: "Impossible de créer la presale",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  const handleUpdatePresale = async () => {
    if (!selectedPresale) return

    setIsEditing(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons une mise à jour réussie
      setTimeout(() => {
        const updatedPresales = presales.map((p) =>
          p.id === selectedPresale.id ? { ...selectedPresale, updatedAt: Date.now() } : p
        )

        setPresales(updatedPresales)
        setShowEditDialog(false)

        toast({
          title: "Succès",
          description: "Presale mise à jour avec succès",
        })
      }, 1500)
    } catch (error) {
      console.error("Error updating presale:", error)
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour la presale",
        variant: "destructive",
      })
    } finally {
      setIsEditing(false)
    }
  }

  const handleDeletePresale = async () => {
    if (!presaleToDelete) return

    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons une suppression réussie
      setTimeout(() => {
        const updatedPresales = presales.filter((p) => p.id !== presaleToDelete.id)

        setPresales(updatedPresales)
        setShowDeleteDialog(false)
        setPresaleToDelete(null)

        toast({
          title: "Succès",
          description: "Presale supprimée avec succès",
        })
      }, 1000)
    } catch (error) {
      console.error("Error deleting presale:", error)
      toast({
        title: "Erreur",
        description: "Impossible de supprimer la presale",
        variant: "destructive",
      })
    }
  }

  const handleFinalizePresale = async (presaleId: string) => {
    setIsFinalizing(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons une finalisation réussie
      setTimeout(() => {
        const updatedPresales = presales.map((p) =>
          p.id === presaleId ? { ...p, status: "completed", updatedAt: Date.now() } : p
        )

        setPresales(updatedPresales)

        toast({
          title: "Succès",
          description: "Presale finalisée avec succès",
        })
      }, 2000)
    } catch (error) {
      console.error("Error finalizing presale:", error)
      toast({
        title: "Erreur",
        description: "Impossible de finaliser la presale",
        variant: "destructive",
      })
    } finally {
      setIsFinalizing(false)
    }
  }

  const handleCancelPresale = async (presaleId: string) => {
    setIsCancelling(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons une annulation réussie
      setTimeout(() => {
        const updatedPresales = presales.map((p) =>
          p.id === presaleId ? { ...p, status: "cancelled", updatedAt: Date.now() } : p
        )

        setPresales(updatedPresales)

        toast({
          title: "Succès",
          description: "Presale annulée avec succès",
        })
      }, 1500)
    } catch (error) {
      console.error("Error cancelling presale:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'annuler la presale",
        variant: "destructive",
      })
    } finally {
      setIsCancelling(false)
    }
  }

  const handleRefundParticipants = async (presaleId: string) => {
    setIsRefunding(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons un remboursement réussi
      setTimeout(() => {
        const updatedPresales = presales.map((p) =>
          p.id === presaleId
            ? { ...p, status: "failed", raisedAmount: 0, progress: 0, updatedAt: Date.now() }
            : p
        )

        setPresales(updatedPresales)

        toast({
          title: "Succès",
          description: "Participants remboursés avec succès",
        })
      }, 2500)
    } catch (error) {
      console.error("Error refunding participants:", error)
      toast({
        title: "Erreur",
        description: "Impossible de rembourser les participants",
        variant: "destructive",
      })
    } finally {
      setIsRefunding(false)
    }
  }

  const handleAddToWhitelist = async (presaleId: string) => {
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons un ajout réussi
      setTimeout(() => {
        setShowWhitelistDialog(false)
        setWhitelistAddresses("")

        toast({
          title: "Succès",
          description: "Adresses ajoutées à la whitelist avec succès",
        })
      }, 1000)
    } catch (error) {
      console.error("Error adding to whitelist:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'ajouter les adresses à la whitelist",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge className="bg-blue-500">En attente</Badge>
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "completed":
        return <Badge className="bg-green-700">Terminée</Badge>
      case "cancelled":
        return <Badge className="bg-orange-500">Annulée</Badge>
      case "failed":
        return <Badge className="bg-red-500">Échouée</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const formatDate = (date: Date) => {
    return format(date, "dd MMMM yyyy HH:mm", { locale: fr })
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("fr-FR").format(value)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Gestion des Presales</h2>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="mr-2 h-4 w-4" /> Créer une Presale
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher par nom, symbole ou ID..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filtrer par statut" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tous les statuts</SelectItem>
            <SelectItem value="pending">En attente</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="completed">Terminée</SelectItem>
            <SelectItem value="cancelled">Annulée</SelectItem>
            <SelectItem value="failed">Échouée</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des Presales</CardTitle>
          <CardDescription>
            {filteredPresales.length} presale{filteredPresales.length > 1 ? "s" : ""} trouvée
            {filteredPresales.length > 1 ? "s" : ""}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredPresales.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Aucune presale trouvée</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => {
                        if (sortField === "tokenName") {
                          setSortDirection(sortDirection === "asc" ? "desc" : "asc")
                        } else {
                          setSortField("tokenName")
                          setSortDirection("asc")
                        }
                      }}
                    >
                      <div className="flex items-center">
                        Token
                        {sortField === "tokenName" && (
                          <span className="ml-1">
                            {sortDirection === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => {
                        if (sortField === "price") {
                          setSortDirection(sortDirection === "asc" ? "desc" : "asc")
                        } else {
                          setSortField("price")
                          setSortDirection("asc")
                        }
                      }}
                    >
                      <div className="flex items-center">
                        Prix
                        {sortField === "price"

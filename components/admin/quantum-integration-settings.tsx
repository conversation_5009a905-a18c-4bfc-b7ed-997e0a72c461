"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, Lock, Unlock, Save, RefreshCw } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/hooks/use-toast"

interface IntegrationSetting {
  id: string
  name: string
  key: string
  value: string
  isSecret: boolean
  isLocked: boolean
  category: "api" | "security" | "network" | "token" | "system"
  description: string
}

export function QuantumIntegrationSettings() {
  const [settings, setSettings] = useState<IntegrationSetting[]>([
    {
      id: "1",
      name: "API Endpoint Solana",
      key: "NEXT_PUBLIC_SOLANA_RPC_URL",
      value: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "",
      isSecret: false,
      isLocked: false,
      category: "api",
      description: "Point de terminaison RPC Solana pour les transactions blockchain",
    },
    {
      id: "2",
      name: "API Endpoint BNB",
      key: "NEXT_PUBLIC_BNB_RPC_URL",
      value: process.env.NEXT_PUBLIC_BNB_RPC_URL || "",
      isSecret: false,
      isLocked: false,
      category: "api",
      description: "Point de terminaison RPC BNB pour les transactions blockchain",
    },
    {
      id: "3",
      name: "Market API Endpoint",
      key: "MARKET_API_ENDPOINT",
      value: process.env.MARKET_API_ENDPOINT || "",
      isSecret: false,
      isLocked: false,
      category: "api",
      description: "Point de terminaison API pour les données de marché",
    },
    {
      id: "4",
      name: "Market API Key",
      key: "MARKET_API_KEY",
      value: process.env.MARKET_API_KEY || "",
      isSecret: true,
      isLocked: true,
      category: "security",
      description: "Clé API pour l'accès aux données de marché",
    },
    {
      id: "5",
      name: "CoinGecko API Key",
      key: "COINGECKO_API_KEY",
      value: process.env.COINGECKO_API_KEY || "",
      isSecret: true,
      isLocked: true,
      category: "security",
      description: "Clé API pour l'accès aux données CoinGecko",
    },
    {
      id: "6",
      name: "CoinMarketCap API Key",
      key: "COINMARKETCAP_API_KEY",
      value: process.env.COINMARKETCAP_API_KEY || "",
      isSecret: true,
      isLocked: true,
      category: "security",
      description: "Clé API pour l'accès aux données CoinMarketCap",
    },
    {
      id: "7",
      name: "Token Program ID",
      key: "NEXT_PUBLIC_TOKEN_PROGRAM_ID",
      value: process.env.NEXT_PUBLIC_TOKEN_PROGRAM_ID || "",
      isSecret: false,
      isLocked: false,
      category: "token",
      description: "ID du programme de token Solana",
    },
    {
      id: "8",
      name: "Custom Mint Address",
      key: "NEXT_PUBLIC_CUSTOM_MINT_ADDRESS",
      value: process.env.NEXT_PUBLIC_CUSTOM_MINT_ADDRESS || "",
      isSecret: false,
      isLocked: false,
      category: "token",
      description: "Adresse de mint personnalisée",
    },
    {
      id: "9",
      name: "Admin Wallet",
      key: "ADMIN_WALLET",
      value: process.env.ADMIN_WALLET || "",
      isSecret: true,
      isLocked: true,
      category: "security",
      description: "Adresse du portefeuille administrateur",
    },
    {
      id: "10",
      name: "Filebase API Key",
      key: "FILEBASE_API_KEY",
      value: process.env.FILEBASE_API_KEY || "",
      isSecret: true,
      isLocked: true,
      category: "security",
      description: "Clé API pour le stockage Filebase",
    },
    {
      id: "11",
      name: "PancakeSwap Router (Testnet)",
      key: "NEXT_PUBLIC_PANCAKESWAP_ROUTER_TESTNET",
      value: process.env.NEXT_PUBLIC_PANCAKESWAP_ROUTER_TESTNET || "",
      isSecret: false,
      isLocked: false,
      category: "network",
      description: "Adresse du routeur PancakeSwap sur le testnet",
    },
    {
      id: "12",
      name: "PancakeSwap Router (Mainnet)",
      key: "NEXT_PUBLIC_PANCAKESWAP_ROUTER_MAINNET",
      value: process.env.NEXT_PUBLIC_PANCAKESWAP_ROUTER_MAINNET || "",
      isSecret: false,
      isLocked: false,
      category: "network",
      description: "Adresse du routeur PancakeSwap sur le mainnet",
    },
  ])

  const [activeTab, setActiveTab] = useState("all")
  const [isEditMode, setIsEditMode] = useState(false)
  const [masterPassword, setMasterPassword] = useState("")
  const [showMasterPasswordInput, setShowMasterPasswordInput] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")

  const filteredSettings = settings.filter((setting) => {
    if (activeTab !== "all" && setting.category !== activeTab) return false

    return (
      setting.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      setting.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      setting.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  const handleToggleEditMode = () => {
    if (isEditMode) {
      setIsEditMode(false)
      setShowMasterPasswordInput(false)
    } else {
      setShowMasterPasswordInput(true)
    }
  }

  const handleMasterPasswordSubmit = () => {
    // Dans un environnement réel, vous devriez vérifier le mot de passe avec une API
    if (masterPassword === "admin123") {
      // Exemple simplifié
      setIsEditMode(true)
      setShowMasterPasswordInput(false)
      toast({
        title: "Mode édition activé",
        description: "Vous pouvez maintenant modifier les variables d'environnement",
      })
    } else {
      toast({
        title: "Mot de passe incorrect",
        description: "Veuillez réessayer avec le bon mot de passe",
        variant: "destructive",
      })
    }
    setMasterPassword("")
  }

  const handleToggleLock = (id: string) => {
    setSettings(settings.map((setting) => (setting.id === id ? { ...setting, isLocked: !setting.isLocked } : setting)))
  }

  const handleValueChange = (id: string, value: string) => {
    setSettings(settings.map((setting) => (setting.id === id ? { ...setting, value } : setting)))
  }

  const handleSaveChanges = async () => {
    setIsSaving(true)

    // Simuler une requête API
    await new Promise((resolve) => setTimeout(resolve, 1500))

    toast({
      title: "Modifications enregistrées",
      description: "Les variables d'environnement ont été mises à jour avec succès",
    })

    setIsSaving(false)
    setIsEditMode(false)
  }

  const handleTestConnections = async () => {
    toast({
      title: "Test des connexions en cours",
      description: "Vérification des points de terminaison API...",
    })

    // Simuler des tests de connexion
    await new Promise((resolve) => setTimeout(resolve, 2000))

    toast({
      title: "Tests terminés",
      description: "Toutes les connexions sont fonctionnelles",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Paramètres d'Intégration Quantum</h2>
          <p className="text-muted-foreground">
            Gérez les variables d'environnement et les paramètres d'intégration de la plateforme
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleTestConnections} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Tester les connexions
          </Button>
          <Button
            onClick={handleToggleEditMode}
            variant={isEditMode ? "destructive" : "default"}
            className="flex items-center gap-2"
          >
            {isEditMode ? (
              <>
                <Lock className="h-4 w-4" />
                Quitter le mode édition
              </>
            ) : (
              <>
                <Unlock className="h-4 w-4" />
                Mode édition
              </>
            )}
          </Button>
        </div>
      </div>

      {showMasterPasswordInput && (
        <Card className="border-amber-500">
          <CardHeader>
            <CardTitle className="text-amber-500">Authentification requise</CardTitle>
            <CardDescription>
              Veuillez entrer le mot de passe administrateur pour modifier les variables d'environnement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Input
                type="password"
                placeholder="Mot de passe administrateur"
                value={masterPassword}
                onChange={(e) => setMasterPassword(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleMasterPasswordSubmit()}
              />
              <Button onClick={handleMasterPasswordSubmit}>Valider</Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex items-center gap-4">
        <Input
          placeholder="Rechercher des variables..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid grid-cols-6 w-full">
            <TabsTrigger value="all">Toutes</TabsTrigger>
            <TabsTrigger value="api">API</TabsTrigger>
            <TabsTrigger value="security">Sécurité</TabsTrigger>
            <TabsTrigger value="network">Réseau</TabsTrigger>
            <TabsTrigger value="token">Token</TabsTrigger>
            <TabsTrigger value="system">Système</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid gap-4">
        {filteredSettings.map((setting) => (
          <Card key={setting.id} className={setting.isLocked ? "border-amber-200" : ""}>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg flex items-center gap-2">
                    {setting.name}
                    {setting.isSecret && (
                      <Badge variant="outline" className="ml-2 text-amber-500 border-amber-500">
                        Secret
                      </Badge>
                    )}
                    <Badge variant="outline" className="ml-2">
                      {setting.category.charAt(0).toUpperCase() + setting.category.slice(1)}
                    </Badge>
                  </CardTitle>
                  <CardDescription className="mt-1">{setting.key}</CardDescription>
                </div>
                {isEditMode && (
                  <Button variant="ghost" size="icon" onClick={() => handleToggleLock(setting.id)}>
                    {setting.isLocked ? <Lock className="h-4 w-4 text-amber-500" /> : <Unlock className="h-4 w-4" />}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor={`value-${setting.id}`}>Valeur</Label>
                <Input
                  id={`value-${setting.id}`}
                  value={setting.isSecret && !isEditMode ? "••••••••••••••••" : setting.value}
                  onChange={(e) => handleValueChange(setting.id, e.target.value)}
                  disabled={!isEditMode || setting.isLocked}
                  type={setting.isSecret && !isEditMode ? "password" : "text"}
                  className={setting.isLocked ? "bg-amber-50" : ""}
                />
                <p className="text-sm text-muted-foreground">{setting.description}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {isEditMode && (
        <div className="flex justify-end gap-4">
          <Button variant="outline" onClick={() => setIsEditMode(false)}>
            Annuler
          </Button>
          <Button onClick={handleSaveChanges} disabled={isSaving} className="flex items-center gap-2">
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Enregistrement...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Enregistrer les modifications
              </>
            )}
          </Button>
        </div>
      )}

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Important</AlertTitle>
        <AlertDescription>
          Les variables verrouillées ne peuvent pas être modifiées sans déverrouillage explicite. Les modifications des
          variables d'environnement peuvent nécessiter un redémarrage du serveur pour prendre effet.
        </AlertDescription>
      </Alert>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, CheckCircle, Search, UserPlus, X } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"

// Types
interface Role {
  id: string
  name: string
  description: string
}

interface User {
  walletAddress: string
  username?: string
  roles: string[]
  joinedAt: string
}

export function UserRoleAssignment() {
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSearching, setIsSearching] = useState(false)
  const [isAssigning, setIsAssigning] = useState(false)
  const [isRemoving, setIsRemoving] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedRole, setSelectedRole] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchRoles()
  }, [])

  const fetchRoles = async () => {
    try {
      const response = await fetch("/api/admin/roles")
      if (!response.ok) {
        throw new Error("Failed to fetch roles")
      }
      const data = await response.json()
      setRoles(data)
      setIsLoading(false)
    } catch (error) {
      console.error("Error fetching roles:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les rôles",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const searchUsers = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "Erreur",
        description: "Veuillez entrer un terme de recherche",
        variant: "destructive",
      })
      return
    }

    setIsSearching(true)
    setError(null)
    setSuccess(null)
    try {
      const response = await fetch(`/api/admin/users/search?q=${encodeURIComponent(searchQuery)}`)
      if (!response.ok) {
        throw new Error("Failed to search users")
      }
      const data = await response.json()
      setUsers(data.users)
      if (data.users.length === 0) {
        setError("Aucun utilisateur trouvé")
      }
    } catch (error) {
      console.error("Error searching users:", error)
      setError("Erreur lors de la recherche d'utilisateurs")
    } finally {
      setIsSearching(false)
    }
  }

  const assignRole = async () => {
    if (!selectedUser || !selectedRole) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un utilisateur et un rôle",
        variant: "destructive",
      })
      return
    }

    setIsAssigning(true)
    setError(null)
    setSuccess(null)
    try {
      const response = await fetch("/api/admin/user-roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: selectedUser.walletAddress,
          roleId: selectedRole,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to assign role")
      }

      // Mettre à jour l'utilisateur sélectionné avec le nouveau rôle
      const updatedUser = {
        ...selectedUser,
        roles: [...selectedUser.roles, selectedRole],
      }
      setSelectedUser(updatedUser)
      setUsers(users.map((user) => (user.walletAddress === selectedUser.walletAddress ? updatedUser : user)))

      const roleName = roles.find((role) => role.id === selectedRole)?.name || selectedRole
      setSuccess(`Rôle ${roleName} attribué avec succès à ${selectedUser.username || selectedUser.walletAddress}`)
      setSelectedRole("")
    } catch (error) {
      console.error("Error assigning role:", error)
      setError(error instanceof Error ? error.message : "Erreur lors de l'attribution du rôle")
    } finally {
      setIsAssigning(false)
    }
  }

  const removeRole = async (userId: string, roleId: string) => {
    setIsRemoving(true)
    setError(null)
    setSuccess(null)
    try {
      const response = await fetch(`/api/admin/user-roles?userId=${userId}&roleId=${roleId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to remove role")
      }

      // Mettre à jour l'utilisateur sélectionné sans le rôle supprimé
      const updatedUser = {
        ...selectedUser!,
        roles: selectedUser!.roles.filter((role) => role !== roleId),
      }
      setSelectedUser(updatedUser)
      setUsers(users.map((user) => (user.walletAddress === userId ? updatedUser : user)))

      const roleName = roles.find((role) => role.id === roleId)?.name || roleId
      setSuccess(`Rôle ${roleName} retiré avec succès de ${selectedUser?.username || userId}`)
    } catch (error) {
      console.error("Error removing role:", error)
      setError(error instanceof Error ? error.message : "Erreur lors du retrait du rôle")
    } finally {
      setIsRemoving(false)
    }
  }

  const handleUserSelect = (user: User) => {
    setSelectedUser(user)
    setSelectedRole("")
  }

  const getAvailableRoles = () => {
    if (!selectedUser) return roles
    return roles.filter((role) => !selectedUser.roles.includes(role.id))
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Attribution de rôles aux utilisateurs</CardTitle>
          <CardDescription>Recherchez des utilisateurs et attribuez-leur des rôles</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-6 bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Succès</AlertTitle>
              <AlertDescription className="text-green-700">{success}</AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="search-query">Rechercher un utilisateur</Label>
              <div className="flex mt-1">
                <Input
                  id="search-query"
                  placeholder="Adresse du wallet ou nom d'utilisateur"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="rounded-r-none"
                />
                <Button onClick={searchUsers} disabled={isSearching || !searchQuery.trim()} className="rounded-l-none">
                  {isSearching ? "Recherche..." : <Search className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-full" />
                </div>
              ))}
            </div>
          ) : users.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Utilisateur</TableHead>
                  <TableHead>Rôles actuels</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow
                    key={user.walletAddress}
                    className={selectedUser?.walletAddress === user.walletAddress ? "bg-muted/50" : ""}
                  >
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.username || "Sans nom"}</div>
                        <div className="text-xs font-mono text-muted-foreground">{user.walletAddress}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {user.roles.length === 0 ? (
                          <span className="text-muted-foreground text-sm">Aucun rôle</span>
                        ) : (
                          user.roles.map((roleId) => {
                            const role = roles.find((r) => r.id === roleId)
                            return (
                              <Badge key={roleId} variant="outline">
                                {role?.name || roleId}
                              </Badge>
                            )
                          })
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUserSelect(user)}
                        className={selectedUser?.walletAddress === user.walletAddress ? "bg-primary/10" : ""}
                      >
                        Sélectionner
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : searchQuery && !isSearching ? (
            <div className="text-center py-6 text-muted-foreground">Aucun utilisateur trouvé</div>
          ) : null}
        </CardContent>
      </Card>

      {selectedUser && (
        <Card>
          <CardHeader>
            <CardTitle>Gérer les rôles de l'utilisateur</CardTitle>
            <CardDescription>
              {selectedUser.username || "Sans nom"} ({selectedUser.walletAddress})
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-sm font-medium mb-2">Rôles actuels</h3>
              {selectedUser.roles.length === 0 ? (
                <div className="text-muted-foreground">Cet utilisateur n'a aucun rôle</div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {selectedUser.roles.map((roleId) => {
                    const role = roles.find((r) => r.id === roleId)
                    return (
                      <Badge key={roleId} className="flex items-center gap-1 pl-2">
                        {role?.name || roleId}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-5 w-5 ml-1 hover:bg-transparent"
                          onClick={() => removeRole(selectedUser.walletAddress, roleId)}
                          disabled={isRemoving}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    )
                  })}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Ajouter un rôle</h3>
              <div className="flex gap-2">
                <Select value={selectedRole} onValueChange={setSelectedRole} disabled={isAssigning}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Sélectionner un rôle" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableRoles().length === 0 ? (
                      <SelectItem value="none" disabled>
                        Aucun rôle disponible
                      </SelectItem>
                    ) : (
                      getAvailableRoles().map((role) => (
                        <SelectItem key={role.id} value={role.id}>
                          {role.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <Button onClick={assignRole} disabled={isAssigning || !selectedRole}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  {isAssigning ? "Attribution..." : "Attribuer"}
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={() => setSelectedUser(null)} className="ml-auto">
              Fermer
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Loader2, CheckCircle, AlertTriangle, Lock, Shield, Zap } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface TokenCreationParams {
  name: string
  symbol: string
  decimals: number
  initialSupply: string
  maxSupply: string
  isMintable: boolean
  isBurnable: boolean
  isPausable: boolean
  isTransferTaxable: boolean
  transferTaxRate?: number
  suffix: string
  network: "solana" | "bnb"
  isEcosystemToken: boolean
}

export function AdminTokenCreator() {
  const [params, setParams] = useState<TokenCreationParams>({
    name: "",
    symbol: "",
    decimals: 9,
    initialSupply: "1000000000",
    maxSupply: "10000000000",
    isMintable: true,
    isBurnable: true,
    isPausable: true,
    isTransferTaxable: false,
    transferTaxRate: 0,
    suffix: "",
    network: "solana",
    isEcosystemToken: true,
  })

  const [isCreating, setIsCreating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [result, setResult] = useState<{ success: boolean; message: string; address?: string } | null>(null)
  const [availableSuffixes, setAvailableSuffixes] = useState<string[]>([])
  const { toast } = useToast()

  useEffect(() => {
    // Fetch available suffixes
    const fetchSuffixes = async () => {
      try {
        const response = await fetch("/api/admin/suffixes")
        const data = await response.json()
        if (data.success) {
          setAvailableSuffixes(data.suffixes)
        }
      } catch (error) {
        console.error("Failed to fetch suffixes:", error)
      }
    }

    fetchSuffixes()
  }, [])

  const handleChange = (field: keyof TokenCreationParams, value: any) => {
    setParams((prev) => ({ ...prev, [field]: value }))
  }

  const validateParams = () => {
    if (!params.name.trim()) return "Token name is required"
    if (!params.symbol.trim()) return "Token symbol is required"
    if (params.initialSupply === "" || isNaN(Number(params.initialSupply)) || Number(params.initialSupply) <= 0)
      return "Initial supply must be a positive number"
    if (
      params.maxSupply === "" ||
      isNaN(Number(params.maxSupply)) ||
      Number(params.maxSupply) < Number(params.initialSupply)
    )
      return "Max supply must be greater than or equal to initial supply"
    if (
      params.isTransferTaxable &&
      (params.transferTaxRate === undefined || params.transferTaxRate < 0 || params.transferTaxRate > 10)
    )
      return "Transfer tax rate must be between 0 and 10 percent"
    if (!params.suffix && params.isEcosystemToken) return "Suffix is required for ecosystem tokens"

    return null
  }

  const createToken = async () => {
    const validationError = validateParams()
    if (validationError) {
      toast({
        title: "Validation Error",
        description: validationError,
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setProgress(0)
    setResult(null)

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + 5
          if (newProgress >= 95) {
            clearInterval(progressInterval)
            return 95
          }
          return newProgress
        })
      }, 500)

      // Call API to create token
      const response = await fetch("/api/admin/create-token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      })

      clearInterval(progressInterval)
      setProgress(100)

      const data = await response.json()
      if (data.success) {
        setResult({
          success: true,
          message: `Token ${params.symbol} created successfully!`,
          address: data.address,
        })
        toast({
          title: "Success",
          description: `Token ${params.symbol} created successfully!`,
        })
      } else {
        setResult({
          success: false,
          message: data.error || "Failed to create token",
        })
        toast({
          title: "Error",
          description: data.error || "Failed to create token",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error creating token:", error)
      setResult({
        success: false,
        message: "An unexpected error occurred",
      })
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create Ecosystem Token</CardTitle>
        <CardDescription>
          Create new tokens for the platform ecosystem with custom parameters and suffix addresses
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="solana">
          <TabsList className="mb-4">
            <TabsTrigger value="solana" onClick={() => handleChange("network", "solana")}>
              Solana
            </TabsTrigger>
            <TabsTrigger value="bnb" onClick={() => handleChange("network", "bnb")}>
              BNB Chain
            </TabsTrigger>
          </TabsList>

          <TabsContent value="solana" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Token Name</Label>
                <Input
                  id="name"
                  value={params.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  placeholder="Global Finance Token"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="symbol">Token Symbol</Label>
                <Input
                  id="symbol"
                  value={params.symbol}
                  onChange={(e) => handleChange("symbol", e.target.value)}
                  placeholder="GFT"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="initialSupply">Initial Supply</Label>
                <Input
                  id="initialSupply"
                  value={params.initialSupply}
                  onChange={(e) => handleChange("initialSupply", e.target.value)}
                  type="number"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxSupply">Maximum Supply</Label>
                <Input
                  id="maxSupply"
                  value={params.maxSupply}
                  onChange={(e) => handleChange("maxSupply", e.target.value)}
                  type="number"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="decimals">Decimals</Label>
                <Select
                  value={params.decimals.toString()}
                  onValueChange={(value) => handleChange("decimals", Number.parseInt(value))}
                >
                  <SelectTrigger id="decimals">
                    <SelectValue placeholder="Select decimals" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="6">6</SelectItem>
                    <SelectItem value="9">9</SelectItem>
                    <SelectItem value="12">12</SelectItem>
                    <SelectItem value="18">18</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="suffix">Address Suffix</Label>
                <Select value={params.suffix} onValueChange={(value) => handleChange("suffix", value)}>
                  <SelectTrigger id="suffix">
                    <SelectValue placeholder="Select suffix" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableSuffixes.map((suffix) => (
                      <SelectItem key={suffix} value={suffix}>
                        {suffix}
                      </SelectItem>
                    ))}
                    <SelectItem value="custom">Generate New Suffix</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {params.suffix === "custom" && (
              <div className="space-y-2">
                <Label htmlFor="customSuffix">Custom Suffix</Label>
                <Input
                  id="customSuffix"
                  onChange={(e) => handleChange("suffix", e.target.value)}
                  placeholder="GFT"
                  maxLength={4}
                />
                <p className="text-xs text-muted-foreground">
                  Custom suffixes require address grinding which may take time
                </p>
              </div>
            )}

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isMintable"
                  checked={params.isMintable}
                  onCheckedChange={(checked) => handleChange("isMintable", checked)}
                />
                <Label htmlFor="isMintable" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" /> Mintable
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isBurnable"
                  checked={params.isBurnable}
                  onCheckedChange={(checked) => handleChange("isBurnable", checked)}
                />
                <Label htmlFor="isBurnable" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" /> Burnable
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isPausable"
                  checked={params.isPausable}
                  onCheckedChange={(checked) => handleChange("isPausable", checked)}
                />
                <Label htmlFor="isPausable" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" /> Pausable
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isTransferTaxable"
                  checked={params.isTransferTaxable}
                  onCheckedChange={(checked) => handleChange("isTransferTaxable", checked)}
                />
                <Label htmlFor="isTransferTaxable" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" /> Transfer Tax
                </Label>
              </div>

              {params.isTransferTaxable && (
                <div className="pl-6 space-y-2">
                  <Label htmlFor="transferTaxRate">Tax Rate (%)</Label>
                  <Input
                    id="transferTaxRate"
                    value={params.transferTaxRate}
                    onChange={(e) => handleChange("transferTaxRate", Number.parseFloat(e.target.value))}
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                  />
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isEcosystemToken"
                  checked={params.isEcosystemToken}
                  onCheckedChange={(checked) => handleChange("isEcosystemToken", checked)}
                />
                <Label htmlFor="isEcosystemToken" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" /> Ecosystem Token
                </Label>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="bnb" className="space-y-4">
            {/* Similar form fields for BNB Chain with appropriate adjustments */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bnb-name">Token Name</Label>
                <Input
                  id="bnb-name"
                  value={params.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  placeholder="Global Finance Token"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bnb-symbol">Token Symbol</Label>
                <Input
                  id="bnb-symbol"
                  value={params.symbol}
                  onChange={(e) => handleChange("symbol", e.target.value)}
                  placeholder="GFT"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bnb-initialSupply">Initial Supply</Label>
                <Input
                  id="bnb-initialSupply"
                  value={params.initialSupply}
                  onChange={(e) => handleChange("initialSupply", e.target.value)}
                  type="number"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bnb-maxSupply">Maximum Supply</Label>
                <Input
                  id="bnb-maxSupply"
                  value={params.maxSupply}
                  onChange={(e) => handleChange("maxSupply", e.target.value)}
                  type="number"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bnb-decimals">Decimals</Label>
                <Select
                  value={params.decimals.toString()}
                  onValueChange={(value) => handleChange("decimals", Number.parseInt(value))}
                >
                  <SelectTrigger id="bnb-decimals">
                    <SelectValue placeholder="Select decimals" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="6">6</SelectItem>
                    <SelectItem value="9">9</SelectItem>
                    <SelectItem value="12">12</SelectItem>
                    <SelectItem value="18">18</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="bnb-suffix">Address Suffix</Label>
                <Select value={params.suffix} onValueChange={(value) => handleChange("suffix", value)}>
                  <SelectTrigger id="bnb-suffix">
                    <SelectValue placeholder="Select suffix" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableSuffixes.map((suffix) => (
                      <SelectItem key={suffix} value={suffix}>
                        {suffix}
                      </SelectItem>
                    ))}
                    <SelectItem value="custom">Generate New Suffix</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Similar checkboxes for BNB Chain */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bnb-isMintable"
                  checked={params.isMintable}
                  onCheckedChange={(checked) => handleChange("isMintable", checked)}
                />
                <Label htmlFor="bnb-isMintable" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" /> Mintable
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bnb-isBurnable"
                  checked={params.isBurnable}
                  onCheckedChange={(checked) => handleChange("isBurnable", checked)}
                />
                <Label htmlFor="bnb-isBurnable" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" /> Burnable
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bnb-isPausable"
                  checked={params.isPausable}
                  onCheckedChange={(checked) => handleChange("isPausable", checked)}
                />
                <Label htmlFor="bnb-isPausable" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" /> Pausable
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bnb-isTransferTaxable"
                  checked={params.isTransferTaxable}
                  onCheckedChange={(checked) => handleChange("isTransferTaxable", checked)}
                />
                <Label htmlFor="bnb-isTransferTaxable" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" /> Transfer Tax
                </Label>
              </div>

              {params.isTransferTaxable && (
                <div className="pl-6 space-y-2">
                  <Label htmlFor="bnb-transferTaxRate">Tax Rate (%)</Label>
                  <Input
                    id="bnb-transferTaxRate"
                    value={params.transferTaxRate}
                    onChange={(e) => handleChange("transferTaxRate", Number.parseFloat(e.target.value))}
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                  />
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bnb-isEcosystemToken"
                  checked={params.isEcosystemToken}
                  onCheckedChange={(checked) => handleChange("isEcosystemToken", checked)}
                />
                <Label htmlFor="bnb-isEcosystemToken" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" /> Ecosystem Token
                </Label>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {isCreating && (
          <div className="mt-6 space-y-2">
            <div className="flex items-center">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              <span>Creating token...</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {result && (
          <Alert className={`mt-6 ${result.success ? "bg-green-50" : "bg-red-50"}`}>
            {result.success ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-600" />
            )}
            <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
            <AlertDescription>
              {result.message}
              {result.address && (
                <div className="mt-2">
                  <span className="font-medium">Token Address:</span> {result.address}
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={createToken} disabled={isCreating} className="w-full">
          {isCreating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            "Create Token"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

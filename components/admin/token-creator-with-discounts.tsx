"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Loader2, Check, AlertCircle, Info, Plus, Trash2 } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DatePicker } from "@/components/ui/date-picker"

export function TokenCreatorWithDiscounts() {
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isSuc<PERSON>, setIsSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("discounts")

  // État pour les codes de réduction
  const [discountCodes, setDiscountCodes] = useState<
    Array<{
      id: string
      code: string
      discountPercentage: number
      maxUses: number
      currentUses: number
      expiryDate: Date | null
      isActive: boolean
    }>
  >([
    {
      id: "discount_1",
      code: "LAUNCH50",
      discountPercentage: 50,
      maxUses: 100,
      currentUses: 45,
      expiryDate: new Date(2023, 11, 31),
      isActive: true,
    },
    {
      id: "discount_2",
      code: "SOLANA25",
      discountPercentage: 25,
      maxUses: 200,
      currentUses: 12,
      expiryDate: null,
      isActive: true,
    },
  ])

  // État pour le nouveau code de réduction
  const [newCode, setNewCode] = useState("")
  const [newDiscountPercentage, setNewDiscountPercentage] = useState(10)
  const [newMaxUses, setNewMaxUses] = useState(100)
  const [newExpiryDate, setNewExpiryDate] = useState<Date | null>(null)
  const [isAddingNew, setIsAddingNew] = useState(false)

  // État pour les paramètres de remise automatique
  const [volumeDiscounts, setVolumeDiscounts] = useState<
    Array<{
      id: string
      minTokens: number
      discountPercentage: number
    }>
  >([
    { id: "volume_1", minTokens: 1000000, discountPercentage: 5 },
    { id: "volume_2", minTokens: 10000000, discountPercentage: 10 },
    { id: "volume_3", minTokens: 100000000, discountPercentage: 15 },
  ])

  // État pour les paramètres généraux
  const [settings, setSettings] = useState({
    enableDiscountCodes: true,
    enableVolumeDiscounts: true,
    enableReferralDiscounts: false,
    referralDiscountPercentage: 10,
    maxTotalDiscountPercentage: 50,
  })

  // Ajouter un nouveau code de réduction
  const addDiscountCode = () => {
    if (!newCode) {
      setError("Le code de réduction ne peut pas être vide")
      return
    }

    if (discountCodes.some((dc) => dc.code === newCode)) {
      setError("Ce code de réduction existe déjà")
      return
    }

    if (newDiscountPercentage <= 0 || newDiscountPercentage > 100) {
      setError("Le pourcentage de réduction doit être entre 1 et 100")
      return
    }

    const newDiscountCode = {
      id: `discount_${Date.now()}`,
      code: newCode,
      discountPercentage: newDiscountPercentage,
      maxUses: newMaxUses,
      currentUses: 0,
      expiryDate: newExpiryDate,
      isActive: true,
    }

    setDiscountCodes([...discountCodes, newDiscountCode])
    setNewCode("")
    setNewDiscountPercentage(10)
    setNewMaxUses(100)
    setNewExpiryDate(null)
    setIsAddingNew(false)
    setError(null)
  }

  // Supprimer un code de réduction
  const removeDiscountCode = (id: string) => {
    setDiscountCodes(discountCodes.filter((dc) => dc.id !== id))
  }

  // Mettre à jour un code de réduction
  const updateDiscountCode = (id: string, field: string, value: any) => {
    setDiscountCodes(discountCodes.map((dc) => (dc.id === id ? { ...dc, [field]: value } : dc)))
  }

  // Ajouter une remise par volume
  const addVolumeDiscount = () => {
    const newVolumeDiscount = {
      id: `volume_${Date.now()}`,
      minTokens: 1000000,
      discountPercentage: 5,
    }
    setVolumeDiscounts([...volumeDiscounts, newVolumeDiscount])
  }

  // Supprimer une remise par volume
  const removeVolumeDiscount = (id: string) => {
    setVolumeDiscounts(volumeDiscounts.filter((vd) => vd.id !== id))
  }

  // Mettre à jour une remise par volume
  const updateVolumeDiscount = (id: string, field: string, value: any) => {
    setVolumeDiscounts(volumeDiscounts.map((vd) => (vd.id === id ? { ...vd, [field]: value } : vd)))
  }

  // Mettre à jour les paramètres généraux
  const updateSettings = (field: string, value: any) => {
    setSettings({ ...settings, [field]: value })
  }

  // Sauvegarder les modifications
  const saveChanges = async () => {
    setIsSaving(true)
    setError(null)

    try {
      // Simuler un appel API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setIsSuccess(true)
      setTimeout(() => setIsSuccess(false), 3000)
    } catch (err: any) {
      setError(err.message || "Une erreur s'est produite lors de la sauvegarde")
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Gestion des Remises pour la Création de Tokens</CardTitle>
        <CardDescription>
          Configurez les codes de réduction et les remises automatiques pour la création de tokens.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isSuccess && (
          <Alert className="bg-green-50 border-green-200 mb-4">
            <Check className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Modifications enregistrées</AlertTitle>
            <AlertDescription className="text-green-700">
              Les paramètres de remise ont été mis à jour avec succès.
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="discounts">Codes de Réduction</TabsTrigger>
            <TabsTrigger value="volume">Remises par Volume</TabsTrigger>
            <TabsTrigger value="settings">Paramètres Généraux</TabsTrigger>
          </TabsList>

          <TabsContent value="discounts" className="space-y-6 pt-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Codes de Réduction</h3>
                <Switch
                  checked={settings.enableDiscountCodes}
                  onCheckedChange={(checked) => updateSettings("enableDiscountCodes", checked)}
                  id="enableDiscountCodes"
                />
              </div>

              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-800">Information</AlertTitle>
                <AlertDescription className="text-blue-700">
                  Les codes de réduction permettent aux utilisateurs d'obtenir une remise sur les frais de création de
                  tokens. Ils peuvent être limités en nombre d'utilisations et dans le temps.
                </AlertDescription>
              </Alert>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Code</TableHead>
                    <TableHead>Réduction (%)</TableHead>
                    <TableHead>Utilisations</TableHead>
                    <TableHead>Date d'expiration</TableHead>
                    <TableHead>Actif</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {discountCodes.map((code) => (
                    <TableRow key={code.id}>
                      <TableCell>
                        <Input
                          value={code.code}
                          onChange={(e) => updateDiscountCode(code.id, "code", e.target.value.toUpperCase())}
                          className="w-32"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={code.discountPercentage}
                          onChange={(e) => updateDiscountCode(code.id, "discountPercentage", Number(e.target.value))}
                          className="w-20"
                          min={1}
                          max={100}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm">{code.currentUses} / </span>
                          <Input
                            type="number"
                            value={code.maxUses}
                            onChange={(e) => updateDiscountCode(code.id, "maxUses", Number(e.target.value))}
                            className="w-20"
                            min={1}
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <DatePicker
                          date={code.expiryDate}
                          onSelect={(date) => updateDiscountCode(code.id, "expiryDate", date)}
                          className="w-32"
                        />
                      </TableCell>
                      <TableCell>
                        <Switch
                          checked={code.isActive}
                          onCheckedChange={(checked) => updateDiscountCode(code.id, "isActive", checked)}
                        />
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" onClick={() => removeDiscountCode(code.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {isAddingNew ? (
                <div className="border p-4 rounded-md space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Ajouter un nouveau code de réduction</h3>
                    <Button variant="ghost" size="sm" onClick={() => setIsAddingNew(false)}>
                      Annuler
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="newCode">Code</Label>
                      <Input
                        id="newCode"
                        value={newCode}
                        onChange={(e) => setNewCode(e.target.value.toUpperCase())}
                        placeholder="SOLANA50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="newDiscountPercentage">Réduction (%)</Label>
                      <Input
                        id="newDiscountPercentage"
                        type="number"
                        value={newDiscountPercentage}
                        onChange={(e) => setNewDiscountPercentage(Number(e.target.value))}
                        min={1}
                        max={100}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="newMaxUses">Utilisations max.</Label>
                      <Input
                        id="newMaxUses"
                        type="number"
                        value={newMaxUses}
                        onChange={(e) => setNewMaxUses(Number(e.target.value))}
                        min={1}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="newExpiryDate">Date d'expiration (optionnelle)</Label>
                      <DatePicker date={newExpiryDate} onSelect={setNewExpiryDate} />
                    </div>
                  </div>
                  <Button onClick={addDiscountCode}>Ajouter le code</Button>
                </div>
              ) : (
                <Button onClick={() => setIsAddingNew(true)} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter un nouveau code
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent value="volume" className="space-y-6 pt-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Remises par Volume</h3>
                <Switch
                  checked={settings.enableVolumeDiscounts}
                  onCheckedChange={(checked) => updateSettings("enableVolumeDiscounts", checked)}
                  id="enableVolumeDiscounts"
                />
              </div>

              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-800">Information</AlertTitle>
                <AlertDescription className="text-blue-700">
                  Les remises par volume permettent d'offrir automatiquement une réduction aux utilisateurs qui créent
                  des tokens avec un approvisionnement initial élevé.
                </AlertDescription>
              </Alert>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Approvisionnement minimum</TableHead>
                    <TableHead>Réduction (%)</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {volumeDiscounts.map((discount) => (
                    <TableRow key={discount.id}>
                      <TableCell>
                        <Input
                          type="number"
                          value={discount.minTokens}
                          onChange={(e) => updateVolumeDiscount(discount.id, "minTokens", Number(e.target.value))}
                          className="w-40"
                          min={1}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          value={discount.discountPercentage}
                          onChange={(e) =>
                            updateVolumeDiscount(discount.id, "discountPercentage", Number(e.target.value))
                          }
                          className="w-20"
                          min={1}
                          max={100}
                        />
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" onClick={() => removeVolumeDiscount(discount.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <Button onClick={addVolumeDiscount} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Ajouter un palier de remise
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Paramètres Généraux des Remises</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between space-x-2">
                    <div>
                      <Label htmlFor="enableReferralDiscounts">Activer les remises de parrainage</Label>
                      <p className="text-xs text-muted-foreground">
                        Permet aux utilisateurs d'obtenir une remise en parrainant d'autres utilisateurs
                      </p>
                    </div>
                    <Switch
                      id="enableReferralDiscounts"
                      checked={settings.enableReferralDiscounts}
                      onCheckedChange={(checked) => updateSettings("enableReferralDiscounts", checked)}
                    />
                  </div>

                  {settings.enableReferralDiscounts && (
                    <div className="space-y-2">
                      <Label htmlFor="referralDiscountPercentage">Pourcentage de remise pour parrainage (%)</Label>
                      <Input
                        id="referralDiscountPercentage"
                        type="number"
                        value={settings.referralDiscountPercentage}
                        onChange={(e) => updateSettings("referralDiscountPercentage", Number(e.target.value))}
                        min={1}
                        max={100}
                      />
                      <p className="text-xs text-muted-foreground">
                        Pourcentage de remise accordé aux utilisateurs qui parrainent de nouveaux créateurs de tokens
                      </p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxTotalDiscountPercentage">Remise totale maximum (%)</Label>
                    <Input
                      id="maxTotalDiscountPercentage"
                      type="number"
                      value={settings.maxTotalDiscountPercentage}
                      onChange={(e) => updateSettings("maxTotalDiscountPercentage", Number(e.target.value))}
                      min={0}
                      max={100}
                    />
                    <p className="text-xs text-muted-foreground">
                      Pourcentage maximum de remise totale pouvant être appliqué (combinaison de toutes les remises)
                    </p>
                  </div>
                </div>
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Cumul des Remises</AlertTitle>
                <AlertDescription>
                  Les différentes remises (codes, volume, parrainage) peuvent être cumulées jusqu'à la remise totale
                  maximum définie ci-dessus. Au-delà de cette limite, la remise sera plafonnée à{" "}
                  {settings.maxTotalDiscountPercentage}%.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button onClick={saveChanges} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Enregistrement...
            </>
          ) : (
            "Enregistrer les modifications"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Download, Calendar, Filter, Eye, AlertCircle, CheckCircle, Info } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function QuantumAuditLogs() {
  const [searchTerm, setSearchTerm] = useState("")
  const [actionFilter, setActionFilter] = useState("all")
  const [dateRange, setDateRange] = useState("week")

  // Données simulées pour les logs d'audit
  const auditLogs = [
    {
      id: 1,
      action: "Token Created",
      user: "<PERSON>",
      details: "Created token SOLQ",
      timestamp: "2023-04-28 10:45:12",
      severity: "info",
    },
    {
      id: 2,
      action: "Security Setting Changed",
      user: "Admin",
      details: "Changed security level to high",
      timestamp: "2023-04-27 15:30:45",
      severity: "warning",
    },
    {
      id: 3,
      action: "User Permission Changed",
      user: "Admin",
      details: "Granted Quantum access to user Bob",
      timestamp: "2023-04-26 09:15:33",
      severity: "info",
    },
    {
      id: 4,
      action: "Failed Login Attempt",
      user: "Unknown",
      details: "Multiple failed login attempts from IP ***********",
      timestamp: "2023-04-25 14:22:18",
      severity: "error",
    },
    {
      id: 5,
      action: "Token Configuration Updated",
      user: "Jane Smith",
      details: "Updated token MEMQ configuration",
      timestamp: "2023-04-24 08:10:05",
      severity: "info",
    },
    {
      id: 6,
      action: "System Backup",
      user: "System",
      details: "Automatic system backup completed",
      timestamp: "2023-04-23 00:00:00",
      severity: "info",
    },
    {
      id: 7,
      action: "API Key Generated",
      user: "John Doe",
      details: "Generated new API key for external service",
      timestamp: "2023-04-22 11:30:22",
      severity: "warning",
    },
    {
      id: 8,
      action: "Token Deleted",
      user: "Admin",
      details: "Deleted token TEST",
      timestamp: "2023-04-21 16:45:37",
      severity: "warning",
    },
  ]

  // Filtrer les logs en fonction du terme de recherche et des filtres
  const filteredLogs = auditLogs.filter(
    (log) =>
      (log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.details.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (actionFilter === "all" || log.action.toLowerCase().includes(actionFilter.toLowerCase())),
  )

  // Fonction pour obtenir l'icône en fonction de la sévérité
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case "error":
        return <AlertCircle className="h-4 w-4 text-destructive" />
      case "warning":
        return <AlertCircle className="h-4 w-4 text-amber-500" />
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  // Fonction pour obtenir la couleur du badge en fonction de la sévérité
  const getSeverityBadgeVariant = (severity: string) => {
    switch (severity) {
      case "error":
        return "destructive"
      case "warning":
        return "warning"
      case "success":
        return "success"
      default:
        return "default"
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Journaux d'audit</CardTitle>
          <CardDescription>Consulter les journaux d'audit du système Quantum</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row items-center gap-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Rechercher dans les journaux..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={actionFilter} onValueChange={setActionFilter} className="w-full md:w-[200px]">
              <SelectTrigger>
                <SelectValue placeholder="Filtrer par action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les actions</SelectItem>
                <SelectItem value="token">Actions sur les tokens</SelectItem>
                <SelectItem value="security">Actions de sécurité</SelectItem>
                <SelectItem value="user">Actions sur les utilisateurs</SelectItem>
                <SelectItem value="system">Actions système</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateRange} onValueChange={setDateRange} className="w-full md:w-[150px]">
              <SelectTrigger>
                <SelectValue placeholder="Période" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Aujourd'hui</SelectItem>
                <SelectItem value="week">Cette semaine</SelectItem>
                <SelectItem value="month">Ce mois</SelectItem>
                <SelectItem value="year">Cette année</SelectItem>
                <SelectItem value="all">Tout</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" className="w-full md:w-auto flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Personnaliser
            </Button>
            <Button variant="outline" className="w-full md:w-auto flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filtres avancés
            </Button>
            <Button className="w-full md:w-auto flex items-center gap-2">
              <Download className="h-4 w-4" />
              Exporter
            </Button>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Sévérité</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Utilisateur</TableHead>
                  <TableHead>Détails</TableHead>
                  <TableHead>Horodatage</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center">
                        {getSeverityIcon(log.severity)}
                        <Badge variant={getSeverityBadgeVariant(log.severity)} className="ml-2">
                          {log.severity.charAt(0).toUpperCase() + log.severity.slice(1)}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{log.action}</TableCell>
                    <TableCell>{log.user}</TableCell>
                    <TableCell>{log.details}</TableCell>
                    <TableCell>{log.timestamp}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">Voir les détails</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Affichage de {filteredLogs.length} sur {auditLogs.length} entrées
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" disabled>
                Précédent
              </Button>
              <Button variant="outline" size="sm">
                Suivant
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

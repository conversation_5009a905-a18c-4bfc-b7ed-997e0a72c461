"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  Users,
  Coins,
  Settings,
  FileText,
  Shield,
  BarChart2,
  Database,
  Key,
  RefreshCw,
  Network,
  CircleDollarSign,
  Wallet,
  BookOpen,
  Sparkles,
} from "lucide-react"

const items = [
  {
    title: "Vue d'ensemble",
    href: "/admin/dashboard",
    icon: <BarChart2 className="mr-2 h-4 w-4" />,
  },
  {
    title: "Gestion des utilisateurs",
    href: "/admin/user-management",
    icon: <Users className="mr-2 h-4 w-4" />,
  },
  {
    title: "Gestion des tokens",
    href: "/admin/token-management",
    icon: <Coins className="mr-2 h-4 w-4" />,
  },
  {
    title: "Gestion des presales",
    href: "/admin/presale-management",
    icon: <CircleDollarSign className="mr-2 h-4 w-4" />,
  },
  {
    title: "Sécurité",
    href: "/admin/security",
    icon: <Shield className="mr-2 h-4 w-4" />,
  },
  {
    title: "Rapports",
    href: "/admin/reports",
    icon: <FileText className="mr-2 h-4 w-4" />,
  },
  {
    title: "Paramètres réseau",
    href: "/admin/network-management",
    icon: <Network className="mr-2 h-4 w-4" />,
  },
  {
    title: "Variables d'environnement",
    href: "/admin/env-manager",
    icon: <Key className="mr-2 h-4 w-4" />,
  },
  {
    title: "Paramètres globaux",
    href: "/admin/platform-settings",
    icon: <Settings className="mr-2 h-4 w-4" />,
  },
  {
    title: "Gestion des wallets",
    href: "/admin/wallet-management",
    icon: <Wallet className="mr-2 h-4 w-4" />,
  },
  {
    title: "Configuration base de données",
    href: "/admin/storage-config",
    icon: <Database className="mr-2 h-4 w-4" />,
  },
  {
    title: "Audits système",
    href: "/admin/audit-log",
    icon: <BookOpen className="mr-2 h-4 w-4" />,
  },
  {
    title: "Recharger l'application",
    href: "#",
    onClick: () => window.location.reload(),
    icon: <RefreshCw className="mr-2 h-4 w-4" />,
  },
  {
    title: "Intégrations IA",
    href: "/admin/ai-integrations",
    icon: <Sparkles className="mr-2 h-4 w-4" />,
  },
]

export function AdminSidebarNav() {
  const pathname = usePathname()

  return (
    <div className="w-full">
      <div className="space-y-1">
        {items.map((item, index) => {
          const isActive = pathname === item.href

          if (item.onClick) {
            return (
              <button
                key={index}
                onClick={item.onClick}
                className={cn(
                  "flex w-full items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                  isActive ? "bg-accent text-accent-foreground" : "transparent",
                )}
              >
                {item.icon}
                <span>{item.title}</span>
              </button>
            )
          }

          return (
            <Link
              key={index}
              href={item.href}
              className={cn(
                "flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                isActive ? "bg-accent text-accent-foreground" : "transparent",
              )}
            >
              {item.icon}
              <span>{item.title}</span>
            </Link>
          )
        })}
      </div>
    </div>
  )
}

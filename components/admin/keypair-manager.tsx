"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, CheckCircle } from "lucide-react"
import KeypairManager from "@/lib/keypair-manager"
import TokenSuffixService from "@/lib/token-suffix-service"
import { useNetwork } from "@/contexts/network-context"

export default function KeypairManagerComponent() {
  const { toast } = useToast()
  const { activeNetwork } = useNetwork()
  const [suffix, setSuffix] = useState("GF")
  const [count, setCount] = useState(5)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedAddresses, setGeneratedAddresses] = useState<string[]>([])
  const [stats, setStats] = useState<any>(null)
  const [validationError, setValidationError] = useState("")

  // Load the current suffix when the component mounts
  useEffect(() => {
    const loadSuffix = async () => {
      try {
        const currentSuffix = await TokenSuffixService.getSuffixForNetwork(activeNetwork.id)
        setSuffix(currentSuffix)
      } catch (error) {
        console.error("Error loading suffix:", error)
      }
    }

    loadSuffix()
    refreshStats()
  }, [activeNetwork])

  const refreshStats = async () => {
    const currentStats = KeypairManager.getKeypairStats()
    setStats(currentStats)
  }

  const handleGenerateKeypairs = async () => {
    // Validate the suffix
    const validation = TokenSuffixService.validateSuffix(suffix)
    if (!validation.valid) {
      setValidationError(validation.error || "Invalid suffix")
      return
    }

    setValidationError("")
    setIsGenerating(true)
    setGeneratedAddresses([])

    try {
      const addresses = await KeypairManager.preGenerateKeypairs(suffix, activeNetwork.id, count)
      setGeneratedAddresses(addresses)

      toast({
        title: "Keypairs Generated",
        description: `Successfully generated ${addresses.length} keypairs with suffix ${suffix}`,
      })

      refreshStats()
    } catch (error: any) {
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate keypairs",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Keypair Manager</CardTitle>
        <CardDescription>Pre-generate keypairs with the configured suffix for faster token creation</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="suffix">Token Suffix</Label>
          <Input
            id="suffix"
            value={suffix}
            onChange={(e) => setSuffix(e.target.value.toUpperCase())}
            placeholder="e.g. GF"
            maxLength={6}
          />
          {validationError && <p className="text-sm text-red-500">{validationError}</p>}
          <p className="text-xs text-muted-foreground">
            This suffix will be used for all generated keypairs. Addresses will end with this suffix.
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="count">Number of Keypairs</Label>
          <Input
            id="count"
            type="number"
            min={1}
            max={20}
            value={count}
            onChange={(e) => setCount(Number.parseInt(e.target.value))}
          />
          <p className="text-xs text-muted-foreground">How many keypairs to generate. Generation can take some time.</p>
        </div>

        {stats && (
          <div className="mt-4 p-4 bg-muted rounded-md">
            <h3 className="font-medium mb-2">Keypair Statistics</h3>
            <div className="grid grid-cols-3 gap-2 text-sm">
              <div>
                <p className="text-muted-foreground">Total</p>
                <p className="font-medium">{stats.total}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Used</p>
                <p className="font-medium">{stats.used}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Available</p>
                <p className="font-medium">{stats.available}</p>
              </div>
            </div>

            {Object.entries(stats.bySuffix).length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">By Suffix</h4>
                <div className="space-y-2">
                  {Object.entries(stats.bySuffix).map(([key, suffixStats]: [string, any]) => {
                    const [networkId, suffixValue] = key.split(":")
                    return (
                      <div key={key} className="flex justify-between items-center text-sm">
                        <span>
                          {suffixValue} ({networkId}):
                        </span>
                        <span>
                          {suffixStats.available} available / {suffixStats.total} total
                        </span>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </div>
        )}

        {generatedAddresses.length > 0 && (
          <div className="mt-4">
            <h3 className="font-medium mb-2">Generated Addresses</h3>
            <div className="max-h-40 overflow-y-auto space-y-1 text-xs">
              {generatedAddresses.map((address, index) => (
                <div key={index} className="flex items-center">
                  <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                  <code className="bg-muted p-1 rounded">{address}</code>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={refreshStats} disabled={isGenerating}>
          Refresh Stats
        </Button>
        <Button onClick={handleGenerateKeypairs} disabled={isGenerating}>
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            "Generate Keypairs"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

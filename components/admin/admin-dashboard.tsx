"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Users,
  Coins,
  DollarSign,
  FileText,
  Clock,
  ChevronRight,
  CheckCircle,
  XCircle,
  Rocket,
  Shield,
  Settings,
  BarChart2,
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@solana/wallet-adapter-react"
import { useNetwork } from "@/contexts/network-context"

interface DashboardStats {
  totalUsers: number
  totalTokens: number
  totalTransactions: number
  totalFees: number
  usageStats: {
    date: string
    count: number
  }[]
  creationStats: {
    date: string
    count: number
  }[]
  recentActivities: {
    id: string
    type: string
    user: string
    description: string
    time: string
    status: "success" | "pending" | "failed"
  }[]
}

export function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { publicKey } = useWallet()
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)

        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données simulées pour test
        const mockData: DashboardStats = {
          totalUsers: 1250,
          totalTokens: 532,
          totalTransactions: 18743,
          totalFees: 32546,
          usageStats: [
            { date: "Lun", count: 120 },
            { date: "Mar", count: 234 },
            { date: "Mer", count: 185 },
            { date: "Jeu", count: 312 },
            { date: "Ven", count: 240 },
            { date: "Sam", count: 189 },
            { date: "Dim", count: 210 },
          ],
          creationStats: [
            { date: "Lun", count: 12 },
            { date: "Mar", count: 24 },
            { date: "Mer", count: 8 },
            { date: "Jeu", count: 31 },
            { date: "Ven", count: 18 },
            { date: "Sam", count: 29 },
            { date: "Dim", count: 22 },
          ],
          recentActivities: [
            {
              id: "act-1",
              type: "token_creation",
              user: "0x7a8d...e5f1",
              description: "Nouveau token SOL créé avec succès",
              time: "Il y a 5 minutes",
              status: "success",
            },
            {
              id: "act-2",
              type: "presale_launch",
              user: "0x3c9a...b7d2",
              description: "Lancement de presale configuré pour MEMEX",
              time: "Il y a 23 minutes",
              status: "success",
            },
            {
              id: "act-3",
              type: "verification",
              user: "0xa1b2...c3d4",
              description: "Transaction de vérification en attente",
              time: "Il y a 47 minutes",
              status: "pending",
            },
            {
              id: "act-4",
              type: "liquidity_add",
              user: "0xe5f6...g7h8",
              description: "Échec d'ajout de liquidité pour TOKEN",
              time: "Il y a 1 heure",
              status: "failed",
            },
            {
              id: "act-5",
              type: "token_mint",
              user: "0x1a2b...c3d4",
              description: "1000 NFT mintés avec succès",
              time: "Il y a 2 heures",
              status: "success",
            },
          ],
        }

        setStats(mockData)
      } catch (error) {
        console.error("Erreur lors du chargement des données du tableau de bord:", error)
        toast({
          variant: "destructive",
          title: "Erreur de chargement",
          description: "Impossible de charger les données du tableau de bord",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [toast])

  const getActivityIcon = (type: string, status: string) => {
    if (status === "failed") return <XCircle className="h-5 w-5 text-red-500" />
    if (status === "pending") return <Clock className="h-5 w-5 text-yellow-500" />

    switch (type) {
      case "token_creation":
        return <Coins className="h-5 w-5 text-green-500" />
      case "presale_launch":
        return <Rocket className="h-5 w-5 text-purple-500" />
      case "verification":
        return <Shield className="h-5 w-5 text-blue-500" />
      case "liquidity_add":
        return <DollarSign className="h-5 w-5 text-emerald-500" />
      case "token_mint":
        return <FileText className="h-5 w-5 text-indigo-500" />
      default:
        return <CheckCircle className="h-5 w-5 text-green-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <MetricCard
          isLoading={isLoading}
          icon={<Users className="h-5 w-5" />}
          title="Utilisateurs"
          value={stats?.totalUsers}
          description="Total des utilisateurs"
        />

        <MetricCard
          isLoading={isLoading}
          icon={<Coins className="h-5 w-5" />}
          title="Tokens"
          value={stats?.totalTokens}
          description="Total des tokens créés"
        />

        <MetricCard
          isLoading={isLoading}
          icon={<FileText className="h-5 w-5" />}
          title="Transactions"
          value={stats?.totalTransactions}
          description="Total des transactions"
        />

        <MetricCard
          isLoading={isLoading}
          icon={<DollarSign className="h-5 w-5" />}
          title="Revenus"
          value={stats?.totalFees}
          description="Revenus totaux ($)"
          isCurrency
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Utilisation quotidienne (7 derniers jours)</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="w-full h-[200px]">
                <Skeleton className="w-full h-full" />
              </div>
            ) : (
              <ChartContainer
                config={{
                  count: {
                    label: "Nombre d'utilisations",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="h-[200px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={stats?.usageStats || []} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="count" fill="var(--color-count)" name="Utilisations" />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Création de tokens (7 derniers jours)</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="w-full h-[200px]">
                <Skeleton className="w-full h-full" />
              </div>
            ) : (
              <ChartContainer
                config={{
                  count: {
                    label: "Tokens créés",
                    color: "hsl(var(--chart-2))",
                  },
                }}
                className="h-[200px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={stats?.creationStats || []} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="count" fill="var(--color-count)" name="Tokens créés" />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Activité récente</CardTitle>
            <Button variant="outline" size="sm" className="h-8">
              Voir tout
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-start space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-5 w-full" />
                    <Skeleton className="h-4 w-4/5" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {stats?.recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-4">
                  <div className="bg-muted rounded-full p-2">{getActivityIcon(activity.type, activity.status)}</div>
                  <div className="space-y-1 flex-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{activity.description}</p>
                      <div>
                        {activity.status === "success" && (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            Succès
                          </Badge>
                        )}
                        {activity.status === "pending" && (
                          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                            En attente
                          </Badge>
                        )}
                        {activity.status === "failed" && (
                          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                            Échec
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <span>Par {activity.user}</span>
                      <span className="mx-2">•</span>
                      <span>{activity.time}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <ActionCard
          icon={<Coins className="h-5 w-5" />}
          title="Créer un token"
          description="Lancer un nouveau token personnalisé"
          href="/admin/token-creator"
        />

        <ActionCard
          icon={<BarChart2 className="h-5 w-5" />}
          title="Rapports financiers"
          description="Générer des rapports détaillés"
          href="/admin/reports"
        />

        <ActionCard
          icon={<Settings className="h-5 w-5" />}
          title="Configurer la plateforme"
          description="Modifier les paramètres système"
          href="/admin/platform-settings"
        />
      </div>
    </div>
  )
}

interface MetricCardProps {
  isLoading: boolean
  icon: React.ReactNode
  title: string
  value?: number
  description: string
  isCurrency?: boolean
}

function MetricCard({ isLoading, icon, title, value, description, isCurrency = false }: MetricCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-4 w-4 text-muted-foreground">{icon}</div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="h-7 w-20" />
        ) : (
          <div className="text-2xl font-bold">
            {isCurrency && "$"}
            {value?.toLocaleString()}
          </div>
        )}
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  )
}

interface ActionCardProps {
  icon: React.ReactNode
  title: string
  description: string
  href: string
}

function ActionCard({ icon, title, description, href }: ActionCardProps) {
  return (
    <Card className="transition-all hover:border-primary">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-4 w-4 text-primary">{icon}</div>
      </CardHeader>
      <CardContent>
        <p className="text-xs text-muted-foreground mb-4">{description}</p>
        <Button variant="outline" size="sm" className="w-full" asChild>
          <a href={href}>
            Accéder
            <ChevronRight className="ml-2 h-4 w-4" />
          </a>
        </Button>
      </CardContent>
    </Card>
  )
}

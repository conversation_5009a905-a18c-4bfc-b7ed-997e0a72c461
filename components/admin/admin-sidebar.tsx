"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { adminRoutes } from "@/app/admin/routes"
import type { LucideIcon } from "lucide-react"
import * as Icons from "lucide-react"

export function AdminSidebar() {
  const pathname = usePathname()

  // Fonction pour obtenir l'icône dynamiquement
  const getIcon = (iconName: string): LucideIcon => {
    return Icons[iconName as keyof typeof Icons] || Icons.Circle
  }

  return (
    <div className="hidden border-r bg-muted/40 lg:block lg:w-64">
      <div className="flex h-full max-h-screen flex-col gap-2">
        <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
          <Link href="/admin" className="flex items-center gap-2 font-semibold">
            <Icons.Shield className="h-6 w-6" />
            <span>Admin Panel</span>
          </Link>
        </div>
        <ScrollArea className="flex-1 px-2 py-2">
          <div className="space-y-2">
            {adminRoutes.map((route) => {
              const Icon = getIcon(route.icon)
              return (
                <Button
                  key={route.href}
                  variant={pathname === route.href ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    route.highlight && "bg-primary/10 text-primary hover:bg-primary/20",
                  )}
                  asChild
                >
                  <Link href={route.href}>
                    <Icon className="mr-2 h-4 w-4" />
                    {route.title}
                  </Link>
                </Button>
              )
            })}
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { LogOut } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export function AdminLogout() {
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const handleLogout = async () => {
    setIsLoggingOut(true)

    try {
      // Récupérer le token depuis les cookies
      const cookies = document.cookie.split(";")
      const tokenCookie = cookies.find((cookie) => cookie.trim().startsWith("admin_token="))
      const token = tokenCookie ? tokenCookie.split("=")[1] : ""

      // Appeler l'API de déconnexion
      await fetch("/api/admin/auth/logout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token }),
      })

      // Déconnecter le wallet Phantom si disponible
      if (typeof window !== "undefined" && window.solana?.disconnect) {
        await window.solana.disconnect()
      }

      toast({
        title: "Déconnexion réussie",
        description: "Vous avez été déconnecté de l'interface d'administration",
        variant: "success",
      })

      // Rediriger vers la page d'accueil
      router.push("/")
    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error)

      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la déconnexion",
        variant: "destructive",
      })
    } finally {
      setIsLoggingOut(false)
    }
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleLogout}
      disabled={isLoggingOut}
      className="text-red-500 hover:text-red-700 hover:bg-red-100"
    >
      {isLoggingOut ? (
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500 mr-2"></div>
          Déconnexion...
        </div>
      ) : (
        <div className="flex items-center">
          <LogOut className="h-4 w-4 mr-2" />
          Déconnexion
        </div>
      )}
    </Button>
  )
}

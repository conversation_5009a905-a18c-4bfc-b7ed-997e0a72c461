"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle, ExternalLink, RefreshCw, Shield, Loader2, Sparkles } from "lucide-react"

// Types pour les intégrations AI
interface AiIntegration {
  id: string
  name: string
  description: string
  enabled: boolean
  apiKey: string
  apiEndpoint: string
  status: "connected" | "disconnected" | "error" | "pending"
  lastChecked?: string
  features: string[]
}

export default function AiIntegrationsManager() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isTesting, setIsTesting] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState<string | null>(null)
  const [aiIntegrations, setAiIntegrations] = useState<AiIntegration[]>([])
  const [editingIntegration, setEditingIntegration] = useState<AiIntegration | null>(null)
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})

  // Charger les intégrations AI
  useEffect(() => {
    fetchAiIntegrations()
  }, [])

  // Récupérer les intégrations AI
  const fetchAiIntegrations = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/admin/integrations")

      if (!response.ok) {
        throw new Error("Erreur lors de la récupération des intégrations AI")
      }

      const data = await response.json()
      // Filtrer uniquement les intégrations AI
      const aiIntegrationsData = data.integrations.filter((integration: any) => integration.category === "ai") || []

      // Ajouter les fonctionnalités pour chaque intégration
      const enhancedIntegrations = aiIntegrationsData.map((integration: any) => ({
        ...integration,
        features: getIntegrationFeatures(integration.id),
      }))

      setAiIntegrations(enhancedIntegrations)
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de charger les intégrations AI",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Obtenir les fonctionnalités pour chaque intégration
  const getIntegrationFeatures = (id: string): string[] => {
    switch (id) {
      case "openai":
        return ["Génération de texte", "Génération d'images", "Analyse de données", "Complétion de code"]
      case "deepseek":
        return [
          "Génération de texte avancée",
          "Génération d'images haute qualité",
          "Analyse contextuelle",
          "Création de tokens AI",
        ]
      default:
        return ["Génération de contenu"]
    }
  }

  // Tester une connexion AI
  const testAiConnection = async (id: string) => {
    setIsTesting(id)
    try {
      const integration = aiIntegrations.find((ai) => ai.id === id)
      if (!integration) return

      const response = await fetch(`/api/admin/integrations/${id}/test`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          apiKey: integration.apiKey,
          apiEndpoint: integration.apiEndpoint,
        }),
      })

      if (!response.ok) {
        throw new Error("Échec du test de connexion")
      }

      const result = await response.json()

      // Mettre à jour le statut de l'intégration
      const updatedIntegrations = aiIntegrations.map((ai) =>
        ai.id === id
          ? { ...ai, status: result.success ? "connected" : "error", lastChecked: new Date().toISOString() }
          : ai,
      )
      setAiIntegrations(updatedIntegrations)

      toast({
        title: result.success ? "Connexion réussie" : "Échec de la connexion",
        description: result.message,
        variant: result.success ? "default" : "destructive",
      })
    } catch (error) {
      console.error("Erreur lors du test de connexion:", error)

      // Mettre à jour le statut de l'intégration en cas d'erreur
      const updatedIntegrations = aiIntegrations.map((ai) =>
        ai.id === id ? { ...ai, status: "error", lastChecked: new Date().toISOString() } : ai,
      )
      setAiIntegrations(updatedIntegrations)

      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de tester la connexion AI",
      })
    } finally {
      setIsTesting(null)
    }
  }

  // Sauvegarder les modifications d'une intégration AI
  const saveAiIntegration = async (integration: AiIntegration) => {
    setIsSaving(integration.id)
    try {
      const response = await fetch(`/api/admin/integrations/${integration.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(integration),
      })

      if (!response.ok) {
        throw new Error("Erreur lors de la sauvegarde de l'intégration AI")
      }

      // Mettre à jour la liste des intégrations
      const updatedIntegrations = aiIntegrations.map((ai) => (ai.id === integration.id ? integration : ai))
      setAiIntegrations(updatedIntegrations)

      toast({
        title: "Intégration sauvegardée",
        description: `Les paramètres de ${integration.name} ont été mis à jour avec succès`,
      })

      setEditingIntegration(null)
    } catch (error) {
      console.error("Erreur lors de la sauvegarde de l'intégration:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de sauvegarder l'intégration AI",
      })
    } finally {
      setIsSaving(null)
    }
  }

  // Activer/désactiver une intégration AI
  const toggleAiIntegration = async (id: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/admin/integrations/${id}/toggle`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ enabled }),
      })

      if (!response.ok) {
        throw new Error("Erreur lors de la modification du statut de l'intégration")
      }

      // Mettre à jour la liste des intégrations
      const updatedIntegrations = aiIntegrations.map((ai) => (ai.id === id ? { ...ai, enabled } : ai))
      setAiIntegrations(updatedIntegrations)

      toast({
        title: enabled ? "Intégration activée" : "Intégration désactivée",
        description: `L'intégration a été ${enabled ? "activée" : "désactivée"} avec succès`,
      })
    } catch (error) {
      console.error("Erreur lors de la modification du statut:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de modifier le statut de l'intégration",
      })
    }
  }

  // Basculer l'affichage des secrets
  const toggleShowSecret = (id: string) => {
    setShowSecrets((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  // Obtenir le badge de statut
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "connected":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Connecté</Badge>
      case "disconnected":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Déconnecté</Badge>
      case "error":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Erreur</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">En attente</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="bg-slate-50 dark:bg-slate-800">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl font-bold">Gestionnaire d'Intégrations IA</CardTitle>
            <CardDescription>
              Configurez et gérez les intégrations d'intelligence artificielle pour la plateforme
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={fetchAiIntegrations}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Actualiser
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2].map((i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-md">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-40" />
                  <Skeleton className="h-3 w-60" />
                </div>
                <div className="flex space-x-2">
                  <Skeleton className="h-9 w-9 rounded-md" />
                  <Skeleton className="h-9 w-9 rounded-md" />
                </div>
              </div>
            ))}
          </div>
        ) : aiIntegrations.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">Aucune intégration IA trouvée</p>
          </div>
        ) : (
          <div className="space-y-4">
            {aiIntegrations.map((integration) => (
              <div key={integration.id} className="border rounded-md overflow-hidden">
                <div className="flex flex-col md:flex-row md:items-center justify-between p-4 bg-slate-50 dark:bg-slate-800">
                  <div className="space-y-1 mb-2 md:mb-0">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium">{integration.name}</h3>
                      {getStatusBadge(integration.status)}
                    </div>
                    <p className="text-sm text-muted-foreground">{integration.description}</p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={integration.enabled}
                      onCheckedChange={(checked) => toggleAiIntegration(integration.id, checked)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => testAiConnection(integration.id)}
                      disabled={isTesting === integration.id || !integration.enabled}
                    >
                      {isTesting === integration.id ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Test...
                        </>
                      ) : (
                        "Tester"
                      )}
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => setEditingIntegration(integration)}>
                      Configurer
                    </Button>
                  </div>
                </div>

                {editingIntegration?.id === integration.id && (
                  <div className="p-4 border-t">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor={`${integration.id}-apiKey`}>Clé API</Label>
                        <div className="flex">
                          <Input
                            id={`${integration.id}-apiKey`}
                            value={editingIntegration.apiKey}
                            onChange={(e) =>
                              setEditingIntegration({
                                ...editingIntegration,
                                apiKey: e.target.value,
                              })
                            }
                            type={showSecrets[integration.id] ? "text" : "password"}
                            className="flex-1"
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleShowSecret(integration.id)}
                            className="ml-2"
                          >
                            {showSecrets[integration.id] ? "Masquer" : "Afficher"}
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`${integration.id}-apiEndpoint`}>Point de terminaison API</Label>
                        <Input
                          id={`${integration.id}-apiEndpoint`}
                          value={editingIntegration.apiEndpoint}
                          onChange={(e) =>
                            setEditingIntegration({
                              ...editingIntegration,
                              apiEndpoint: e.target.value,
                            })
                          }
                        />
                      </div>

                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-2">Fonctionnalités disponibles</h4>
                        <div className="flex flex-wrap gap-2">
                          {integration.features.map((feature, index) => (
                            <Badge key={index} variant="secondary" className="flex items-center">
                              <Sparkles className="h-3 w-3 mr-1" />
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {integration.id === "openai" && (
                        <Alert>
                          <ExternalLink className="h-4 w-4" />
                          <AlertTitle>Information</AlertTitle>
                          <AlertDescription>
                            Vous pouvez obtenir une clé API OpenAI en vous inscrivant sur{" "}
                            <a
                              href="https://platform.openai.com/api-keys"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:underline"
                            >
                              OpenAI API
                            </a>
                          </AlertDescription>
                        </Alert>
                      )}

                      {integration.id === "deepseek" && (
                        <Alert>
                          <ExternalLink className="h-4 w-4" />
                          <AlertTitle>Information</AlertTitle>
                          <AlertDescription>
                            Vous pouvez obtenir une clé API DeepSeek en vous inscrivant sur{" "}
                            <a
                              href="https://platform.deepseek.com/"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:underline"
                            >
                              DeepSeek Platform
                            </a>
                          </AlertDescription>
                        </Alert>
                      )}

                      {integration.status === "error" && (
                        <Alert variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertTitle>Erreur de connexion</AlertTitle>
                          <AlertDescription>
                            La dernière tentative de connexion a échoué. Veuillez vérifier vos paramètres et réessayer.
                          </AlertDescription>
                        </Alert>
                      )}

                      <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => setEditingIntegration(null)}>
                          Annuler
                        </Button>
                        <Button
                          onClick={() => saveAiIntegration(editingIntegration)}
                          disabled={isSaving === integration.id}
                        >
                          {isSaving === integration.id ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Sauvegarde...
                            </>
                          ) : (
                            "Sauvegarder"
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {integration.lastChecked && (
                  <div className="px-4 py-2 text-xs text-muted-foreground border-t">
                    Dernière vérification: {new Date(integration.lastChecked).toLocaleString()}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>

      <CardFooter className="bg-slate-50 dark:bg-slate-800 border-t">
        <div className="flex items-center space-x-2">
          <Shield className="h-4 w-4 text-muted-foreground" />
          <p className="text-xs text-muted-foreground">
            Les clés API sont stockées de manière sécurisée et chiffrées dans la base de données.
          </p>
        </div>
      </CardFooter>
    </Card>
  )
}

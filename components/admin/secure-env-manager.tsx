"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@solana/wallet-adapter-react"
import { Plus, Shield, Download, Upload, Eye, EyeOff, RefreshCw, Edit, Trash2 } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Types pour les variables d'environnement
interface EnvVariable {
  key: string
  value: string
  isEncrypted: boolean
  lastUpdated: string
  updatedBy: string
  category: string
  description?: string
}

// Catégories disponibles
const categories = [
  { value: "blockchain", label: "Blockchain" },
  { value: "api", label: "API" },
  { value: "security", label: "Sécurité" },
  { value: "storage", label: "Stockage" },
  { value: "payment", label: "Paiement" },
  { value: "general", label: "Général" },
]

export default function SecureEnvManager() {
  const [variables, setVariables] = useState<EnvVariable[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [adminPassword, setAdminPassword] = useState("")
  const [isPasswordVerified, setIsPasswordVerified] = useState(false)
  const [visibleValues, setVisibleValues] = useState<Record<string, boolean>>({})
  
  const [newVariable, setNewVariable] = useState<Partial<EnvVariable>>({
    key: "",
    value: "",
    isEncrypted: true,
    category: "general",
    description: ""
  })
  
  const [editingVariable, setEditingVariable] = useState<EnvVariable | null>(null)
  const [deletingVariable, setDeletingVariable] = useState<EnvVariable | null>(null)
  const [importData, setImportData] = useState("")
  const [exportFormat, setExportFormat] = useState<"json" | "env">("env")
  
  const { toast } = useToast()
  const { publicKey } = useWallet()

  // Charger les variables d'environnement
  useEffect(() => {
    fetchVariables()
  }, [])

  // Fonction pour récupérer les variables
  const fetchVariables = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/admin/env/variables")
      
      if (!response.ok) {
        throw new Error("Erreur lors de la récupération des variables")
      }
      
      const data = await response.json()
      setVariables(data.variables || [])
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de charger les variables d'environnement"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fonction pour vérifier le mot de passe administrateur
  const verifyAdminPassword = async () => {
    try {
      const response = await fetch("/api/admin/auth/verify-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ password: adminPassword })
      })
      
      if (!response.ok) {
        throw new Error("Mot de passe incorrect")
      }
      
      setIsPasswordVerified(true)
      return true
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur d'authentification",
        description: "Mot de passe administrateur incorrect"
      })
      return false
    }
  }

  // Fonction pour ajouter une variable
  const handleAddVariable = async () => {
    if (!newVariable.key || !newVariable.value) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "La clé et la valeur sont requises"
      })
      return
    }
    
    try {
      const response = await fetch("/api/admin/env/variables", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          ...newVariable,
          updatedBy: publicKey?.toString() || "admin"
        })
      })
      
      if (!response.ok) {
        throw new Error("Erreur lors de l'ajout de la variable")
      }
      
      toast({
        title: "Succès",
        description: `La variable ${newVariable.key} a été ajoutée avec succès`
      })
      
      setIsAddDialogOpen(false)
      setNewVariable({
        key: "",
        value: "",
        isEncrypted: true,
        category: "general",
        description: ""
      })
      
      fetchVariables()
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'ajouter la variable"
      })
    }
  }

  // Fonction pour mettre à jour une variable
  const handleUpdateVariable = async () => {
    if (!editingVariable) return
    
    if (editingVariable.isEncrypted && !isPasswordVerified) {
      const isValid = await verifyAdminPassword()
      if (!isValid) return
    }
    
    try {
      const response = await fetch(`/api/admin/env/variables/${editingVariable.key}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          ...editingVariable,
          updatedBy: publicKey?.toString() || "admin"
        })
      })
      
      if (!response.ok) {
        throw new Error("Erreur lors de la mise à jour de la variable")
      }
      
      toast({
        title: "Succès",
        description: `La variable ${editingVariable.key} a été mise à jour avec succès`
      })
      
      setIsEditDialogOpen(false)
      setEditingVariable(null)
      setAdminPassword("")
      setIsPasswordVerified(false)
      
      fetchVariables()
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de mettre à jour la variable"
      })
    }
  }

  // Fonction pour supprimer une variable
  const handleDeleteVariable = async () => {
    if (!deletingVariable) return
    
    if (!isPasswordVerified) {
      const isValid = await verifyAdminPassword()
      if (!isValid) return
    }
    
    try {
      const response = await fetch(`/api/admin/env/variables/${deletingVariable.key}`, {
        method: "DELETE"
      })
      
      if (!response.ok) {
        throw new Error("Erreur lors de la suppression de la variable")
      }
      
      toast({
        title: "Succès",
        description: `La variable ${deletingVariable.key} a été supprimée avec succès`
      })
      
      setIsDeleteDialogOpen(false)
      setDeletingVariable(null)
      setAdminPassword("")
      setIsPasswordVerified(false)
      
      fetchVariables()
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de supprimer la variable"
      })
    }
  }

  // Fonction pour importer des variables
  const handleImportVariables = async () => {
    if (!importData.trim()) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Aucune donnée à importer"
      })
      return
    }
    
    if (!isPasswordVerified) {
      const isValid = await verifyAdminPassword()
      if (!isValid) return
    }
    
    try {
      let variablesToImport: Partial<EnvVariable>[] = []
      
      // Essayer de parser comme JSON
      try {
        variablesToImport = JSON.parse(importData)
      } catch {
        // Si ce n'est pas du JSON, essayer de parser comme .env
        const lines = importData.split("\n")
        variablesToImport = lines
          .filter(line => line.trim() && !line.startsWith("#"))
          .map(line => {
            const [key, ...valueParts] = line.split("=")
            const value = valueParts.join("=")
            return {
              key: key.trim(),
              value: value.trim(),
              isEncrypted: true,
              category: "general"
            }
          })
      }
      
      if (!Array.isArray(variablesToImport) && typeof variablesToImport === "object") {
        variablesToImport = Object.entries(variablesToImport).map(([key, value]) => ({
          key,
          value: String(value),
          isEncrypted: true,
          category: "general"
        }))
      }
      
      if (!variablesToImport.length) {
        throw new Error("Aucune variable valide trouvée")
      }
      
      const response = await fetch("/api/admin/env/variables/import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          variables: variablesToImport,
          updatedBy: publicKey?.toString() || "admin"
        })
      })
      
      if (!response.ok) {
        throw new Error("Erreur lors de l'importation des variables")
      }
      
      toast({
        title: "Succès",
        description: `${variablesToImport.length} variables ont été importées avec succès`
      })
      
      setIsImportDialogOpen(false)
      setImportData("")
      setAdminPassword("")
      setIsPasswordVerified(false)
      
      fetchVariables()
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'importer les variables"
      })
    }
  }

  // Fonction pour exporter des variables
  const handleExportVariables = async () => {
    if (!isPasswordVerified) {
      const isValid = await verifyAdminPassword()
      if (!isValid) return
    }
    
    try {
      const response = await fetch("/api/admin/env/variables/export", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          format: exportFormat
        })
      })
      
      if (!response.ok) {
        throw new Error("Erreur lors de l'exportation des variables")
      }
      
      const data = await response.json()
      
      // Créer un blob et un lien de téléchargement
      const blob = new Blob([data.content], { type: "text/plain" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = exportFormat === "env" ? ".env.export" : "env-variables.json"
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      setIsExportDialogOpen(false)
      setAdminPassword("")
      setIsPasswordVerified(false)
      
      toast({
        title: "Succès",
        description: `Les variables ont été exportées avec succès au format ${exportFormat.toUpperCase()}`
      })
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'exporter les variables"
      })
    }
  }

  // Filtrer les variables en fonction de l'onglet actif
  const filteredVariables = activeTab === "all" 
    ? variables 
    : variables.filter(v => v.category === activeTab)

  // Fonction pour basculer la visibilité d'une valeur
  const toggleValueVisibility = (key: string) => {
    setVisibleValues(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="bg-slate-50 dark:bg-slate-800">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl font-bold">Gestionnaire de Variables d'Environnement Sécurisées</CardTitle>
            <CardDescription>
              Configurez et gérez les variables d'environnement de la plateforme de manière sécurisée
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Importer
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Importer des variables d'environnement</DialogTitle>
                  <DialogDescription>
                    Importez des variables d'environnement à partir d'un fichier .env ou JSON
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="importData">Données à importer</Label>
                    <Textarea
                      id="importData"
                      placeholder="KEY=value ou format JSON"
                      value={importData}
                      onChange={(e) => setImportData(e.target.value)}
                      className="h-40"
                    />
                    <p className="text-xs text-muted-foreground">
                      Formats acceptés: .env (KEY=value) ou JSON
                    </p>
                  </div>
                  
                  {!isPasswordVerified && (
                    <div className="space-y-2">
                      <Label htmlFor="adminPassword">Mot de passe administrateur</Label>
                      <Input
                        id="adminPassword"
                        type="password"
                        placeholder="Entrez le mot de passe administrateur"
                        value={adminPassword}
                        onChange={(e) => setAdminPassword(e.target.value)}
                      />
                    </div>
                  )}
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>Annuler</Button>
                  <Button onClick={handleImportVariables}>Importer</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Exporter
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Exporter les variables d'environnement</DialogTitle>
                  <DialogDescription>
                    Choisissez le format d'exportation pour vos variables d'environnement
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>Format d'exportation</Label>
                    <div className="flex space-x-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="env-format"
                          checked={exportFormat === "env"}
                          onChange={() => setExportFormat("env")}
                        />
                        <Label htmlFor="env-format">.env</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="json-format"
                          checked={exportFormat === "json"}
                          onChange={() => setExportFormat("json")}
                        />
                        <Label htmlFor="json-format">JSON</Label>
                      </div>
                    </div>
                  </div>
                  
                  {!isPasswordVerified && (
                    <div className="space-y-2">
                      <Label htmlFor="adminPassword">Mot de passe administrateur</Label>
                      <Input
                        id="adminPassword"
                        type="password"
                        placeholder="Entrez le mot de passe administrateur"
                        value={adminPassword}
                        onChange={(e) => setAdminPassword(e.target.value)}
                      />
                    </div>
                  )}
                  
                  <Alert>
                    <Shield className="h-4 w-4" />
                    <AlertTitle>Attention</AlertTitle>
                    <AlertDescription>
                      L'exportation inclura les valeurs en clair des variables non chiffrées. Assurez-vous de stocker le fichier exporté de manière sécurisée.
                    </AlertDescription>
                  </Alert>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>Annuler</Button>
                  <Button onClick={handleExportVariables}>Exporter</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Ajouter
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Ajouter une variable d'environnement</DialogTitle>
                  <DialogDescription>
                    Ajoutez une nouvelle variable d'environnement à la plateforme
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="key">Clé</Label>
                    <Input
                      id="key"
                      placeholder="NEXT_PUBLIC_API_URL"
                      value={newVariable.key}
                      onChange={(e) => setNewVariable({ ...newVariable, key: e.target.value })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="value">Valeur</Label>
                    <Input
                      id="value"
                      placeholder="https://api.example.com"
                      value={newVariable.value}
                      onChange={(e) => setNewVariable({ ...newVariable, value: e.target.value })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="category">Catégorie</Label>
                    <Select
                      value={newVariable.category}
                      onValueChange={(value) => setNewVariable({ ...newVariable, category: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionnez une catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Description de la variable"
                      value={newVariable.description || ""}
                      onChange={(e) => setNewVariable({ ...newVariable, description: e.target.value })}
                    />
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="encrypt"
                      checked={newVariable.isEncrypted}
                      onCheckedChange={(checked) => setNewVariable({ ...newVariable, isEncrypted: checked })}
                    />
                    <Label htmlFor="encrypt">Chiffrer la valeur</Label>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Annuler</Button>
                  <Button onClick={handleAddVariable}>Ajouter</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Button variant="outline" onClick={fetchVariables}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Actualiser
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="all">Toutes</TabsTrigger>
            {categories.map((category) => (
              <TabsTrigger key={category.value} value={category.value}>
                {category.label}
              </TabsTrigger>
            ))}
          </TabsList>
          
          <TabsContent value={activeTab} className="mt-0">
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center justify-between p-4 border rounded-md">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-40" />
                      <Skeleton className="h-3 w-60" />
                    </div>
                    <div className="flex space-x-2">
                      <Skeleton className="h-9 w-9 rounded-md" />
                      <Skeleton className="h-9 w-9 rounded-md" />
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredVariables.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Aucune variable trouvée dans cette catégorie</p>
                <Button className="mt-4" onClick={() => setIsAddDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Ajouter une variable
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredVariables.map((variable) => (
                  <div
                    key={variable.key}
                    className="flex flex-col md:flex-row md:items-center justify-between p-4 border rounded-md hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors"
                  >
                    <div className="space-y-1 mb-2 md:mb-0">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium">{variable.key}</h3>
                        <Badge variant={variable.isEncrypted ? "secondary" : "outline"}>
                          {variable.isEncrypted ? "Chiffré" : "Non chiffré"}
                        </Badge>
                        <Badge variant="outline">{categories.find(c => c.value === variable.category)?.label || variable.category}</Badge>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <p className="text-sm text-muted-foreground font-mono bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                          {variable.isEncrypted && !visibleValues[variable.key] 
                            ? "••••••••••••••••" 
                            : variable.value}
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleValueVisibility(variable.key)}
                          className="h-8 w-8 p-0"
                        >
                          {visibleValues[variable.key] ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      
                      {variable.description && (
                        <p className="text-xs text-muted-foreground">{variable.description}</p>
                      )}
                      
                      <p className="text-xs text-muted-foreground">
                        Dernière mise à jour: {new Date(variable.lastUpdated).toLocaleString()} par {variable.updatedBy}
                      </p>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingVariable(variable)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Modifier
                      </Button>
                      
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => {
                          setDeletingVariable(variable)
                          setIsDeleteDialogOpen(true)
                        }}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Supprimer
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
        
        {/* Dialogue de modification */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Modifier une variable d'environnement</DialogTitle>
              <DialogDescription>
                Modifiez les détails de la variable d'environnement
              </DialogDescription>
            </DialogHeader>
            {editingVariable && (
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-key">Clé</Label>
                  <Input
                    id="edit-key"
                    value={editingVariable.key}
                    disabled
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-value">Valeur</Label>
                  <Input
                    id="edit-value"
                    value={editingVariable.value}
                    onChange={(e) => setEditingVariable({ ...editingVariable, value: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-category">Catégorie</Label>
                  <Select
                    value={editingVariable.category}
                    onValueChange={(value) => setEditingVariable({ ...editingVariable, category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionnez une catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    value={editingVariable.description || ""}
                    onChange={(e) => setEditingVariable({ ...editingVariable, description: e.target.value })}
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-encrypt"
                    checked={editingVariable.isEncrypted}
                    onCheckedChange={(checked) => setEditingVariable({ ...\

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, Trash2, UserPlus } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface Role {
  id: string
  name: string
  permissions: string[]
  wallets: string[]
}

interface Permission {
  id: string
  name: string
  description: string
}

export default function UserRoleManagement() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [newWalletAddress, setNewWalletAddress] = useState("")
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [isAddingWallet, setIsAddingWallet] = useState(false)

  // Sample roles data
  const [roles, setRoles] = useState<Role[]>([
    {
      id: "admin",
      name: "Administrator",
      permissions: ["all"],
      wallets: ["8xyt45jkLmn9PQvDTbW23jkLmNpQrStUv1234567890"],
    },
    {
      id: "moderator",
      name: "Moderator",
      permissions: ["view_dashboard", "manage_users", "approve_tokens"],
      wallets: [],
    },
    {
      id: "creator",
      name: "Token Creator",
      permissions: ["create_token", "manage_own_tokens"],
      wallets: [],
    },
    {
      id: "user",
      name: "Regular User",
      permissions: ["view_tokens", "participate_presale", "stake_tokens"],
      wallets: [],
    },
  ])

  // Sample permissions data
  const permissions: Permission[] = [
    { id: "all", name: "All Permissions", description: "Full access to all platform features" },
    { id: "view_dashboard", name: "View Dashboard", description: "Access to view the admin dashboard" },
    { id: "manage_users", name: "Manage Users", description: "Ability to manage user accounts" },
    { id: "approve_tokens", name: "Approve Tokens", description: "Ability to approve user-created tokens" },
    { id: "create_token", name: "Create Token", description: "Ability to create new tokens" },
    { id: "manage_own_tokens", name: "Manage Own Tokens", description: "Ability to manage tokens created by the user" },
    { id: "view_tokens", name: "View Tokens", description: "Ability to view token details" },
    {
      id: "participate_presale",
      name: "Participate in Presales",
      description: "Ability to participate in token presales",
    },
    { id: "stake_tokens", name: "Stake Tokens", description: "Ability to stake tokens for rewards" },
  ]

  const handleAddWallet = (roleId: string) => {
    if (!newWalletAddress) return

    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Update the role with the new wallet
      const updatedRoles = roles.map((role) => {
        if (role.id === roleId) {
          return {
            ...role,
            wallets: [...role.wallets, newWalletAddress],
          }
        }
        return role
      })

      setRoles(updatedRoles)
      setNewWalletAddress("")
      setSuccess(`Wallet added to ${roles.find((r) => r.id === roleId)?.name} role successfully!`)
      toast({
        title: "Wallet Added",
        description: `Wallet has been added to the ${roles.find((r) => r.id === roleId)?.name} role.`,
      })
    } catch (err) {
      console.error("Error adding wallet:", err)
      setError("Failed to add wallet. Please try again.")
      toast({
        title: "Error",
        description: "Failed to add wallet. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setIsAddingWallet(false)
    }
  }

  const handleRemoveWallet = (roleId: string, wallet: string) => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Update the role by removing the wallet
      const updatedRoles = roles.map((role) => {
        if (role.id === roleId) {
          return {
            ...role,
            wallets: role.wallets.filter((w) => w !== wallet),
          }
        }
        return role
      })

      setRoles(updatedRoles)
      setSuccess(`Wallet removed from ${roles.find((r) => r.id === roleId)?.name} role successfully!`)
      toast({
        title: "Wallet Removed",
        description: `Wallet has been removed from the ${roles.find((r) => r.id === roleId)?.name} role.`,
      })
    } catch (err) {
      console.error("Error removing wallet:", err)
      setError("Failed to remove wallet. Please try again.")
      toast({
        title: "Error",
        description: "Failed to remove wallet. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleTogglePermission = (roleId: string, permissionId: string) => {
    // Don't allow modifying the "all" permission for admin role
    if (roleId === "admin" && permissionId === "all") return

    setRoles((prevRoles) =>
      prevRoles.map((role) => {
        if (role.id === roleId) {
          // If the role already has the permission, remove it
          if (role.permissions.includes(permissionId)) {
            return {
              ...role,
              permissions: role.permissions.filter((p) => p !== permissionId),
            }
          }
          // Otherwise, add the permission
          return {
            ...role,
            permissions: [...role.permissions, permissionId],
          }
        }
        return role
      }),
    )
  }

  const handleSaveRoles = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulate saving roles with a delay
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setSuccess("User roles saved successfully!")
      toast({
        title: "Roles Saved",
        description: "User roles have been updated successfully.",
      })
    } catch (err) {
      console.error("Error saving roles:", err)
      setError("Failed to save roles. Please try again.")
      toast({
        title: "Error",
        description: "Failed to save roles. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>User Role Management</CardTitle>
        <CardDescription>Manage user roles and permissions</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Role</TableHead>
              <TableHead>Permissions</TableHead>
              <TableHead>Wallets</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {roles.map((role) => (
              <TableRow key={role.id}>
                <TableCell className="font-medium">{role.name}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {role.permissions.includes("all") ? (
                      <Badge className="bg-purple-100 text-purple-800">All Permissions</Badge>
                    ) : (
                      role.permissions.map((permission) => (
                        <Badge key={permission} className="bg-blue-100 text-blue-800">
                          {permission.replace(/_/g, " ")}
                        </Badge>
                      ))
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {role.wallets.length > 0 ? (
                      <span>{role.wallets.length} wallet(s) assigned</span>
                    ) : (
                      <span className="text-muted-foreground">No wallets assigned</span>
                    )}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm" onClick={() => setSelectedRole(role)}>
                          Permissions
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>{role.name} Permissions</DialogTitle>
                          <DialogDescription>
                            Configure permissions for the {role.name.toLowerCase()} role
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4 my-4">
                          {permissions.map((permission) => (
                            <div key={permission.id} className="flex items-start space-x-2">
                              <Checkbox
                                id={`${role.id}-${permission.id}`}
                                checked={role.permissions.includes(permission.id)}
                                onCheckedChange={() => handleTogglePermission(role.id, permission.id)}
                                disabled={role.id === "admin" && permission.id === "all"}
                              />
                              <div className="grid gap-1.5">
                                <Label htmlFor={`${role.id}-${permission.id}`} className="font-medium">
                                  {permission.name}
                                </Label>
                                <p className="text-sm text-muted-foreground">{permission.description}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                        <DialogFooter>
                          <Button type="button">Save Permissions</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>

                    <Dialog
                      open={isAddingWallet && selectedRole?.id === role.id}
                      onOpenChange={(open) => {
                        setIsAddingWallet(open)
                        if (open) setSelectedRole(role)
                      }}
                    >
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedRole(role)
                            setIsAddingWallet(true)
                          }}
                        >
                          <UserPlus className="h-4 w-4 mr-1" />
                          Add Wallet
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add Wallet to {role.name} Role</DialogTitle>
                          <DialogDescription>Enter the wallet address to assign to this role</DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4 my-4">
                          <div className="space-y-2">
                            <Label htmlFor="walletAddress">Wallet Address</Label>
                            <Input
                              id="walletAddress"
                              placeholder="Enter wallet address"
                              value={newWalletAddress}
                              onChange={(e) => setNewWalletAddress(e.target.value)}
                            />
                          </div>

                          {role.wallets.length > 0 && (
                            <div className="space-y-2">
                              <Label>Current Wallets</Label>
                              <div className="max-h-40 overflow-y-auto border rounded-md p-2">
                                {role.wallets.map((wallet, index) => (
                                  <div key={index} className="flex justify-between items-center py-1">
                                    <span className="text-xs font-mono">{wallet}</span>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveWallet(role.id, wallet)}
                                    >
                                      <Trash2 className="h-4 w-4 text-red-500" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                        <DialogFooter>
                          <Button type="button" onClick={() => handleAddWallet(role.id)} disabled={!newWalletAddress}>
                            Add Wallet
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm" onClick={() => setSelectedRole(role)}>
                          View Wallets
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>{role.name} Wallets</DialogTitle>
                          <DialogDescription>Wallets assigned to the {role.name.toLowerCase()} role</DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4 my-4">
                          {role.wallets.length > 0 ? (
                            <div className="max-h-60 overflow-y-auto border rounded-md p-2">
                              {role.wallets.map((wallet, index) => (
                                <div key={index} className="flex justify-between items-center py-1">
                                  <span className="text-xs font-mono">{wallet}</span>
                                  <Button variant="ghost" size="sm" onClick={() => handleRemoveWallet(role.id, wallet)}>
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="text-center py-4 text-muted-foreground">
                              No wallets assigned to this role
                            </div>
                          )}
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveRoles} disabled={isLoading}>
          {isLoading ? "Saving..." : "Save Role Configuration"}
        </Button>
      </CardFooter>
    </Card>
  )
}

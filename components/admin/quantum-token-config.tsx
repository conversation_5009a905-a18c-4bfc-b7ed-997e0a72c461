"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { CheckCircle, Plus, Trash2, AlertTriangle, Info, Shield, Settings, Code } from "lucide-react"
import { useTokenSuffixStore } from "@/lib/token-suffix-store"
import { useToast } from "@/components/ui/use-toast"

export default function QuantumTokenConfig() {
  const { availableSuffixes, addSuffix, removeSuffix } = useTokenSuffixStore()
  const { toast } = useToast()
  const [newSuffix, setNewSuffix] = useState("")
  const [isEnabled, setIsEnabled] = useState(true)
  const [creationFee, setCreationFee] = useState("0.1")
  const [minSupply, setMinSupply] = useState("1000000")
  const [maxSupply, setMaxSupply] = useState("1000000000000")
  const [isSaving, setIsSaving] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("general")

  // Paramètres de sécurité
  const [antiBot, setAntiBot] = useState(true)
  const [antiDump, setAntiDump] = useState(true)
  const [autoLiquidity, setAutoLiquidity] = useState(true)
  const [marketingFee, setMarketingFee] = useState(2)
  const [liquidityFee, setLiquidityFee] = useState(3)
  const [maxWallet, setMaxWallet] = useState(2)
  const [maxTransaction, setMaxTransaction] = useState(1)
  const [blacklist, setBlacklist] = useState(true)
  const [pauseTrading, setPauseTrading] = useState(true)

  // Diagnostics
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false)
  const [logs, setLogs] = useState<string[]>([])

  const handleAddSuffix = () => {
    if (!newSuffix) return

    const sanitizedSuffix = newSuffix.toUpperCase().trim()
    if (availableSuffixes.includes(sanitizedSuffix)) {
      setError("Ce suffixe existe déjà")
      return
    }

    addSuffix(sanitizedSuffix)
    setNewSuffix("")
    setSuccess("Suffixe ajouté avec succès")

    setTimeout(() => {
      setSuccess(null)
    }, 3000)
  }

  const handleRemoveSuffix = (suffix: string) => {
    removeSuffix(suffix)

    toast({
      title: "Suffixe supprimé",
      description: `Le suffixe ${suffix} a été supprimé avec succès.`,
    })
  }

  const handleSaveConfig = async () => {
    setIsSaving(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulation de sauvegarde
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setSuccess("Configuration sauvegardée avec succès")

      setTimeout(() => {
        setSuccess(null)
      }, 3000)
    } catch (err) {
      console.error("Erreur lors de la sauvegarde:", err)
      setError("Une erreur s'est produite lors de la sauvegarde")
    } finally {
      setIsSaving(false)
    }
  }

  // Fonction pour exécuter les diagnostics
  const runDiagnostics = async () => {
    setIsRunningDiagnostics(true)
    setLogs([])

    const addLog = (message: string) => {
      setLogs((prev) => [...prev, `${new Date().toISOString().split("T")[1].split(".")[0]} - ${message}`])
    }

    addLog("Démarrage des diagnostics...")

    try {
      // Test de connexion RPC
      addLog("Test de la connexion RPC...")
      await new Promise((resolve) => setTimeout(resolve, 500))
      addLog("✅ Connexion RPC réussie")

      // Test de validité des suffixes
      addLog("Vérification des suffixes...")
      await new Promise((resolve) => setTimeout(resolve, 300))
      addLog("✅ Suffixes valides")

      // Test des paramètres de sécurité
      addLog("Vérification des paramètres de sécurité...")
      await new Promise((resolve) => setTimeout(resolve, 400))
      addLog("✅ Paramètres de sécurité configurés correctement")

      // Test de la configuration des frais
      addLog("Vérification des frais de transaction...")
      await new Promise((resolve) => setTimeout(resolve, 350))
      addLog("✅ Frais de transaction configurés correctement")

      // Test de la configuration des limites
      addLog("Vérification des limites de transaction...")
      await new Promise((resolve) => setTimeout(resolve, 300))
      addLog("✅ Limites de transaction configurées correctement")

      addLog("Diagnostics terminés avec succès")
    } catch (error: any) {
      addLog(`❌ Erreur pendant les diagnostics: ${error.message}`)
    } finally {
      setIsRunningDiagnostics(false)
    }
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="general">
            <Settings className="h-4 w-4 mr-2" />
            Général
          </TabsTrigger>
          <TabsTrigger value="security">
            <Shield className="h-4 w-4 mr-2" />
            Sécurité
          </TabsTrigger>
          <TabsTrigger value="diagnostics">
            <Code className="h-4 w-4 mr-2" />
            Diagnostics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Configuration des Tokens Quantum</CardTitle>
              <CardDescription>Gérez les paramètres des tokens Quantum et les suffixes autorisés</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="tokenEnabled">Activer les Tokens Quantum</Label>
                  <p className="text-sm text-muted-foreground">
                    Permettre aux utilisateurs de créer des tokens Quantum
                  </p>
                </div>
                <Switch id="tokenEnabled" checked={isEnabled} onCheckedChange={setIsEnabled} />
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Paramètres généraux</h3>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="creationFee">Frais de création (SOL)</Label>
                    <Input
                      id="creationFee"
                      type="number"
                      step="0.01"
                      value={creationFee}
                      onChange={(e) => setCreationFee(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="networkFee">Frais réseau estimés (SOL)</Label>
                    <Input id="networkFee" type="number" step="0.000001" value="0.00089" disabled />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minSupply">Offre minimale</Label>
                    <Input
                      id="minSupply"
                      type="number"
                      value={minSupply}
                      onChange={(e) => setMinSupply(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxSupply">Offre maximale</Label>
                    <Input
                      id="maxSupply"
                      type="number"
                      value={maxSupply}
                      onChange={(e) => setMaxSupply(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gestion des suffixes</h3>

                <div className="flex items-end gap-2">
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="newSuffix">Ajouter un nouveau suffixe</Label>
                    <Input
                      id="newSuffix"
                      placeholder="Ex: QUANTUM"
                      value={newSuffix}
                      onChange={(e) => setNewSuffix(e.target.value)}
                      className="uppercase"
                    />
                  </div>
                  <Button onClick={handleAddSuffix}>
                    <Plus className="h-4 w-4 mr-2" />
                    Ajouter
                  </Button>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="bg-green-50 text-green-800 border-green-200">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription>{success}</AlertDescription>
                  </Alert>
                )}

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Suffixe</TableHead>
                        <TableHead>Tokens créés</TableHead>
                        <TableHead>Statut</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {availableSuffixes.map((suffix) => (
                        <TableRow key={suffix}>
                          <TableCell className="font-medium">{suffix}</TableCell>
                          <TableCell>{Math.floor(Math.random() * 50)}</TableCell>
                          <TableCell>
                            <Badge className="bg-green-100 text-green-800 border-green-200">Actif</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleRemoveSuffix(suffix)}>
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Les suffixes sont utilisés pour garantir l'authenticité des tokens Quantum et éviter les
                    contrefaçons.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig} disabled={isSaving} className="ml-auto">
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Enregistrement...
                  </>
                ) : (
                  "Enregistrer les modifications"
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de sécurité</CardTitle>
              <CardDescription>Configurez les fonctionnalités de sécurité pour tous les tokens Quantum</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <Shield className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Ces paramètres seront appliqués à tous les nouveaux tokens Quantum créés par les utilisateurs.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <h3 className="font-medium">Protection contre les bots</h3>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="antiBot" className="cursor-pointer">
                    Protection anti-bot
                    <p className="text-xs text-muted-foreground font-normal">
                      Empêche les bots d'acheter les tokens lors du lancement
                    </p>
                  </Label>
                  <Switch id="antiBot" checked={antiBot} onCheckedChange={setAntiBot} />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Protection contre le dumping</h3>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="antiDump" className="cursor-pointer">
                    Protection anti-dump
                    <p className="text-xs text-muted-foreground font-normal">
                      Empêche les ventes massives qui font chuter le prix
                    </p>
                  </Label>
                  <Switch id="antiDump" checked={antiDump} onCheckedChange={setAntiDump} />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Liquidité automatique</h3>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="autoLiquidity" className="cursor-pointer">
                    Auto-liquidité
                    <p className="text-xs text-muted-foreground font-normal">
                      Ajoute automatiquement de la liquidité au pool pour stabiliser le prix
                    </p>
                  </Label>
                  <Switch id="autoLiquidity" checked={autoLiquidity} onCheckedChange={setAutoLiquidity} />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Frais de transaction</h3>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="marketingFee">Frais de marketing (%)</Label>
                    <span className="text-sm font-medium">{marketingFee}%</span>
                  </div>
                  <Slider
                    id="marketingFee"
                    min={0}
                    max={10}
                    step={0.5}
                    value={[marketingFee]}
                    onValueChange={(value) => setMarketingFee(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="liquidityFee">Frais de liquidité (%)</Label>
                    <span className="text-sm font-medium">{liquidityFee}%</span>
                  </div>
                  <Slider
                    id="liquidityFee"
                    min={0}
                    max={10}
                    step={0.5}
                    value={[liquidityFee]}
                    onValueChange={(value) => setLiquidityFee(value[0])}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Limites de transaction</h3>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="maxWallet">Maximum par portefeuille (%)</Label>
                    <span className="text-sm font-medium">{maxWallet}%</span>
                  </div>
                  <Slider
                    id="maxWallet"
                    min={0}
                    max={10}
                    step={0.5}
                    value={[maxWallet]}
                    onValueChange={(value) => setMaxWallet(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="maxTransaction">Maximum par transaction (%)</Label>
                    <span className="text-sm font-medium">{maxTransaction}%</span>
                  </div>
                  <Slider
                    id="maxTransaction"
                    min={0}
                    max={5}
                    step={0.5}
                    value={[maxTransaction]}
                    onValueChange={(value) => setMaxTransaction(value[0])}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Autres protections</h3>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="blacklist" className="cursor-pointer">
                    Fonction de blacklist
                    <p className="text-xs text-muted-foreground font-normal">
                      Permet de bloquer les adresses malveillantes
                    </p>
                  </Label>
                  <Switch id="blacklist" checked={blacklist} onCheckedChange={setBlacklist} />
                </div>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="pauseTrading" className="cursor-pointer">
                    Pause des transactions
                    <p className="text-xs text-muted-foreground font-normal">
                      Permet de suspendre temporairement les transactions en cas d'urgence
                    </p>
                  </Label>
                  <Switch id="pauseTrading" checked={pauseTrading} onCheckedChange={setPauseTrading} />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig} disabled={isSaving} className="ml-auto">
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Enregistrement...
                  </>
                ) : (
                  "Enregistrer les paramètres de sécurité"
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="diagnostics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Diagnostics du système</CardTitle>
              <CardDescription>Vérifiez l'état du système de tokens Quantum</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Exécutez les diagnostics pour vérifier que tous les composants du système fonctionnent correctement.
                </AlertDescription>
              </Alert>

              <Button onClick={runDiagnostics} disabled={isRunningDiagnostics} className="w-full">
                {isRunningDiagnostics ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Exécution des diagnostics...
                  </>
                ) : (
                  "Exécuter les diagnostics"
                )}
              </Button>

              {logs.length > 0 && (
                <div className="mt-4">
                  <Label>Logs de diagnostic</Label>
                  <div className="mt-2 p-4 bg-black text-green-400 font-mono text-xs h-64 overflow-y-auto rounded">
                    {logs.map((log, index) => (
                      <div key={index}>{log}</div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

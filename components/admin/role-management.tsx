"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { useAdmin } from "@/hooks/use-admin"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Edit, Plus, Trash2 } from "lucide-react"
import { Select, SelectContent, SelectI<PERSON>, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"

// Types
interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  createdAt: string
  updatedAt: string
  createdBy?: string
}

interface Permission {
  id: string
  name: string
  description: string
  category: string
}

export function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [newRole, setNewRole] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
  })
  const [editRole, setEditRole] = useState({
    id: "",
    name: "",
    description: "",
    permissions: [] as string[],
  })
  const [activeTab, setActiveTab] = useState("roles")
  const [permissionFilter, setPermissionFilter] = useState("all")
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null)
  const { toast } = useToast()
  const { isSuperAdmin } = useAdmin()

  useEffect(() => {
    fetchRolesAndPermissions()
  }, [])

  const fetchRolesAndPermissions = async () => {
    setIsLoading(true)
    try {
      // Récupérer les rôles
      const rolesResponse = await fetch("/api/admin/roles")
      if (!rolesResponse.ok) {
        throw new Error("Failed to fetch roles")
      }
      const rolesData = await rolesResponse.json()
      setRoles(rolesData)

      // Récupérer les permissions
      const permissionsResponse = await fetch("/api/admin/permissions")
      if (!permissionsResponse.ok) {
        throw new Error("Failed to fetch permissions")
      }
      const permissionsData = await permissionsResponse.json()
      setPermissions(permissionsData.permissions || [])

      setIsLoading(false)
    } catch (error) {
      console.error("Error fetching data:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const handleCreateRole = async () => {
    if (!newRole.name || !newRole.description || newRole.permissions.length === 0) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs et sélectionner au moins une permission",
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    try {
      const response = await fetch("/api/admin/roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newRole),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to create role")
      }

      const createdRole = await response.json()
      setRoles([...roles, createdRole])
      setNewRole({
        name: "",
        description: "",
        permissions: [],
      })
      toast({
        title: "Succès",
        description: `Le rôle ${createdRole.name} a été créé avec succès`,
      })
    } catch (error) {
      console.error("Error creating role:", error)
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Impossible de créer le rôle",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  const handleUpdateRole = async () => {
    if (!editRole.id || !editRole.name || !editRole.description || editRole.permissions.length === 0) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs et sélectionner au moins une permission",
        variant: "destructive",
      })
      return
    }

    setIsUpdating(true)
    try {
      const response = await fetch("/api/admin/roles", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(editRole),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to update role")
      }

      const updatedRole = await response.json()
      setRoles(roles.map((role) => (role.id === updatedRole.id ? updatedRole : role)))
      setSelectedRole(null)
      toast({
        title: "Succès",
        description: `Le rôle ${updatedRole.name} a été mis à jour avec succès`,
      })
    } catch (error) {
      console.error("Error updating role:", error)
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Impossible de mettre à jour le rôle",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDeleteRole = async () => {
    if (!roleToDelete) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/admin/roles?id=${roleToDelete.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Failed to delete role")
      }

      setRoles(roles.filter((role) => role.id !== roleToDelete.id))
      setShowDeleteDialog(false)
      setRoleToDelete(null)
      toast({
        title: "Succès",
        description: `Le rôle ${roleToDelete.name} a été supprimé avec succès`,
      })
    } catch (error) {
      console.error("Error deleting role:", error)
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Impossible de supprimer le rôle",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const confirmDeleteRole = (role: Role) => {
    setRoleToDelete(role)
    setShowDeleteDialog(true)
  }

  const handleEditRole = (role: Role) => {
    setSelectedRole(role)
    setEditRole({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: [...role.permissions],
    })
  }

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setNewRole({
        ...newRole,
        permissions: [...newRole.permissions, permissionId],
      })
    } else {
      setNewRole({
        ...newRole,
        permissions: newRole.permissions.filter((id) => id !== permissionId),
      })
    }
  }

  const handleEditPermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setEditRole({
        ...editRole,
        permissions: [...editRole.permissions, permissionId],
      })
    } else {
      setEditRole({
        ...editRole,
        permissions: editRole.permissions.filter((id) => id !== permissionId),
      })
    }
  }

  const filteredPermissions =
    permissionFilter === "all"
      ? permissions
      : permissions.filter((permission) => permission.category === permissionFilter)

  const permissionCategories = Array.from(new Set(permissions.map((permission) => permission.category)))

  return (
    <div className="space-y-6">
      <Tabs defaultValue="roles" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="roles">Rôles</TabsTrigger>
          <TabsTrigger value="create" disabled={!isSuperAdmin}>
            Créer un rôle
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Liste des rôles</CardTitle>
              <CardDescription>Gérez les rôles et leurs permissions</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <Skeleton className="h-12 w-full" />
                    </div>
                  ))}
                </div>
              ) : roles.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">Aucun rôle trouvé</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell className="font-medium">{role.name}</TableCell>
                        <TableCell>{role.description}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {role.permissions.includes("all") ? (
                              <Badge>Toutes les permissions</Badge>
                            ) : (
                              role.permissions.map((permId) => {
                                const perm = permissions.find((p) => p.id === permId)
                                return perm ? (
                                  <Badge key={permId} variant="outline">
                                    {perm.name}
                                  </Badge>
                                ) : null
                              })
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditRole(role)}
                              disabled={!isSuperAdmin || ["superadmin", "admin"].includes(role.id)}
                              title={
                                !isSuperAdmin
                                  ? "Vous n'avez pas les permissions nécessaires"
                                  : ["superadmin", "admin"].includes(role.id)
                                    ? "Les rôles système ne peuvent pas être modifiés"
                                    : "Modifier le rôle"
                              }
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => confirmDeleteRole(role)}
                              disabled={!isSuperAdmin || ["superadmin", "admin"].includes(role.id)}
                              title={
                                !isSuperAdmin
                                  ? "Vous n'avez pas les permissions nécessaires"
                                  : ["superadmin", "admin"].includes(role.id)
                                    ? "Les rôles système ne peuvent pas être supprimés"
                                    : "Supprimer le rôle"
                              }
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">Total: {isLoading ? "..." : roles.length} rôles</div>
              <Button variant="outline" onClick={fetchRolesAndPermissions} disabled={isLoading}>
                Rafraîchir
              </Button>
            </CardFooter>
          </Card>

          {selectedRole && (
            <Card>
              <CardHeader>
                <CardTitle>Modifier le rôle: {selectedRole.name}</CardTitle>
                <CardDescription>Modifiez les détails et les permissions du rôle</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <Label htmlFor="edit-name">Nom du rôle</Label>
                      <Input
                        id="edit-name"
                        value={editRole.name}
                        onChange={(e) => setEditRole({ ...editRole, name: e.target.value })}
                        disabled={isUpdating}
                      />
                    </div>
                    <div>
                      <Label htmlFor="edit-description">Description</Label>
                      <Textarea
                        id="edit-description"
                        value={editRole.description}
                        onChange={(e) => setEditRole({ ...editRole, description: e.target.value })}
                        disabled={isUpdating}
                      />
                    </div>
                  </div>

                  <div>
                    <Label>Permissions</Label>
                    <div className="mt-2">
                      <Select value={permissionFilter} onValueChange={setPermissionFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="Filtrer par catégorie" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Toutes les catégories</SelectItem>
                          {permissionCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                      {filteredPermissions.map((permission) => (
                        <div key={permission.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`edit-permission-${permission.id}`}
                            checked={editRole.permissions.includes(permission.id)}
                            onCheckedChange={(checked) => handleEditPermissionChange(permission.id, checked === true)}
                            disabled={isUpdating}
                          />
                          <Label htmlFor={`edit-permission-${permission.id}`} className="text-sm font-normal">
                            {permission.name}
                            <span className="block text-xs text-muted-foreground">{permission.description}</span>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setSelectedRole(null)} disabled={isUpdating}>
                  Annuler
                </Button>
                <Button onClick={handleUpdateRole} disabled={isUpdating}>
                  {isUpdating ? "Mise à jour..." : "Mettre à jour le rôle"}
                </Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="create">
          <Card>
            <CardHeader>
              <CardTitle>Créer un nouveau rôle</CardTitle>
              <CardDescription>Définissez un nouveau rôle avec des permissions spécifiques</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="role-name">Nom du rôle</Label>
                    <Input
                      id="role-name"
                      placeholder="Ex: Gestionnaire de contenu"
                      value={newRole.name}
                      onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                      disabled={isCreating}
                    />
                  </div>
                  <div>
                    <Label htmlFor="role-description">Description</Label>
                    <Textarea
                      id="role-description"
                      placeholder="Décrivez le rôle et ses responsabilités"
                      value={newRole.description}
                      onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                      disabled={isCreating}
                    />
                  </div>
                </div>

                <div>
                  <Label>Permissions</Label>
                  <div className="mt-2">
                    <Select value={permissionFilter} onValueChange={setPermissionFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="Filtrer par catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Toutes les catégories</SelectItem>
                        {permissionCategories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                    {filteredPermissions.map((permission) => (
                      <div key={permission.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`permission-${permission.id}`}
                          checked={newRole.permissions.includes(permission.id)}
                          onCheckedChange={(checked) => handlePermissionChange(permission.id, checked === true)}
                          disabled={isCreating}
                        />
                        <Label htmlFor={`permission-${permission.id}`} className="text-sm font-normal">
                          {permission.name}
                          <span className="block text-xs text-muted-foreground">{permission.description}</span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleCreateRole} disabled={isCreating} className="ml-auto">
                <Plus className="mr-2 h-4 w-4" />
                {isCreating ? "Création en cours..." : "Créer le rôle"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer le rôle "{roleToDelete?.name}" ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)} disabled={isDeleting}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={handleDeleteRole} disabled={isDeleting}>
              {isDeleting ? "Suppression..." : "Supprimer"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

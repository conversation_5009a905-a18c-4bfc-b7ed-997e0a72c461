"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AlertCircle, CheckCircle2, ExternalLink } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function ApiIntegrations() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // CoinGecko API state
  const [coingeckoEnabled, setCoingeckoEnabled] = useState(true)
  const [coingeckoApiKey, setCoingeckoApiKey] = useState(process.env.COINGECKO_API_KEY || "")
  const [coingeckoEndpoint, setCoingeckoEndpoint] = useState("https://api.coingecko.com/api/v3")

  // CoinMarketCap API state
  const [coinmarketcapEnabled, setCoinmarketcapEnabled] = useState(true)
  const [coinmarketcapApiKey, setCoinmarketcapApiKey] = useState(process.env.COINMARKETCAP_API_KEY || "")
  const [coinmarketcapEndpoint, setCoinmarketcapEndpoint] = useState("https://pro-api.coinmarketcap.com/v1")

  // TradingView API state
  const [tradingviewEnabled, setTradingviewEnabled] = useState(false)
  const [tradingviewApiKey, setTradingviewApiKey] = useState("")
  const [tradingviewEndpoint, setTradingviewEndpoint] = useState("")

  // Binance API state
  const [binanceEnabled, setBinanceEnabled] = useState(false)
  const [binanceApiKey, setBinanceApiKey] = useState("")
  const [binanceSecretKey, setBinanceSecretKey] = useState("")
  const [binanceEndpoint, setBinanceEndpoint] = useState("https://api.binance.com")

  const handleSaveSettings = async (apiName: string) => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulate saving settings with a delay
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setSuccess(`${apiName} API settings saved successfully!`)
      toast({
        title: "Settings Saved",
        description: `${apiName} API settings have been updated successfully.`,
      })
    } catch (err) {
      console.error(`Error saving ${apiName} settings:`, err)
      setError(`Failed to save ${apiName} settings. Please try again.`)
      toast({
        title: "Error",
        description: `Failed to save ${apiName} settings. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestConnection = async (apiName: string) => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulate testing connection with a delay
      await new Promise((resolve) => setTimeout(resolve, 2000))

      setSuccess(`${apiName} API connection test successful!`)
      toast({
        title: "Connection Successful",
        description: `Successfully connected to ${apiName} API.`,
      })
    } catch (err) {
      console.error(`Error testing ${apiName} connection:`, err)
      setError(`Failed to connect to ${apiName} API. Please check your settings.`)
      toast({
        title: "Connection Failed",
        description: `Failed to connect to ${apiName} API. Please check your settings.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>API Integrations</CardTitle>
        <CardDescription>Configure external API integrations for market data and charts</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="coingecko" className="w-full">
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="coingecko">CoinGecko</TabsTrigger>
            <TabsTrigger value="coinmarketcap">CoinMarketCap</TabsTrigger>
            <TabsTrigger value="tradingview">TradingView</TabsTrigger>
            <TabsTrigger value="binance">Binance</TabsTrigger>
          </TabsList>

          {/* CoinGecko Tab */}
          <TabsContent value="coingecko" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="coingeckoEnabled">Enable CoinGecko API</Label>
                <p className="text-sm text-muted-foreground">Use CoinGecko for market data and price information</p>
              </div>
              <Switch id="coingeckoEnabled" checked={coingeckoEnabled} onCheckedChange={setCoingeckoEnabled} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="coingeckoApiKey">API Key</Label>
              <Input
                id="coingeckoApiKey"
                value={coingeckoApiKey}
                onChange={(e) => setCoingeckoApiKey(e.target.value)}
                placeholder="Enter your CoinGecko API key"
                disabled={!coingeckoEnabled}
              />
              <p className="text-xs text-muted-foreground">
                Get your API key from{" "}
                <a
                  href="https://www.coingecko.com/en/api"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline inline-flex items-center"
                >
                  CoinGecko <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="coingeckoEndpoint">API Endpoint</Label>
              <Input
                id="coingeckoEndpoint"
                value={coingeckoEndpoint}
                onChange={(e) => setCoingeckoEndpoint(e.target.value)}
                placeholder="Enter CoinGecko API endpoint"
                disabled={!coingeckoEnabled}
              />
            </div>

            <div className="flex justify-between mt-4">
              <Button
                variant="outline"
                onClick={() => handleTestConnection("CoinGecko")}
                disabled={isLoading || !coingeckoEnabled}
              >
                Test Connection
              </Button>
              <Button onClick={() => handleSaveSettings("CoinGecko")} disabled={isLoading || !coingeckoEnabled}>
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </div>
          </TabsContent>

          {/* CoinMarketCap Tab */}
          <TabsContent value="coinmarketcap" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="coinmarketcapEnabled">Enable CoinMarketCap API</Label>
                <p className="text-sm text-muted-foreground">Use CoinMarketCap for market data and price information</p>
              </div>
              <Switch
                id="coinmarketcapEnabled"
                checked={coinmarketcapEnabled}
                onCheckedChange={setCoinmarketcapEnabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="coinmarketcapApiKey">API Key</Label>
              <Input
                id="coinmarketcapApiKey"
                value={coinmarketcapApiKey}
                onChange={(e) => setCoinmarketcapApiKey(e.target.value)}
                placeholder="Enter your CoinMarketCap API key"
                disabled={!coinmarketcapEnabled}
              />
              <p className="text-xs text-muted-foreground">
                Get your API key from{" "}
                <a
                  href="https://coinmarketcap.com/api/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline inline-flex items-center"
                >
                  CoinMarketCap <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="coinmarketcapEndpoint">API Endpoint</Label>
              <Input
                id="coinmarketcapEndpoint"
                value={coinmarketcapEndpoint}
                onChange={(e) => setCoinmarketcapEndpoint(e.target.value)}
                placeholder="Enter CoinMarketCap API endpoint"
                disabled={!coinmarketcapEnabled}
              />
            </div>

            <div className="flex justify-between mt-4">
              <Button
                variant="outline"
                onClick={() => handleTestConnection("CoinMarketCap")}
                disabled={isLoading || !coinmarketcapEnabled}
              >
                Test Connection
              </Button>
              <Button onClick={() => handleSaveSettings("CoinMarketCap")} disabled={isLoading || !coinmarketcapEnabled}>
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </div>
          </TabsContent>

          {/* TradingView Tab */}
          <TabsContent value="tradingview" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="tradingviewEnabled">Enable TradingView API</Label>
                <p className="text-sm text-muted-foreground">
                  Use TradingView for advanced charts and technical analysis
                </p>
              </div>
              <Switch id="tradingviewEnabled" checked={tradingviewEnabled} onCheckedChange={setTradingviewEnabled} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tradingviewApiKey">API Key</Label>
              <Input
                id="tradingviewApiKey"
                value={tradingviewApiKey}
                onChange={(e) => setTradingviewApiKey(e.target.value)}
                placeholder="Enter your TradingView API key"
                disabled={!tradingviewEnabled}
              />
              <p className="text-xs text-muted-foreground">
                Get your API key from{" "}
                <a
                  href="https://www.tradingview.com/brokerage-integration/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline inline-flex items-center"
                >
                  TradingView <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tradingviewEndpoint">API Endpoint</Label>
              <Input
                id="tradingviewEndpoint"
                value={tradingviewEndpoint}
                onChange={(e) => setTradingviewEndpoint(e.target.value)}
                placeholder="Enter TradingView API endpoint"
                disabled={!tradingviewEnabled}
              />
            </div>

            <div className="flex justify-between mt-4">
              <Button
                variant="outline"
                onClick={() => handleTestConnection("TradingView")}
                disabled={isLoading || !tradingviewEnabled}
              >
                Test Connection
              </Button>
              <Button onClick={() => handleSaveSettings("TradingView")} disabled={isLoading || !tradingviewEnabled}>
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </div>
          </TabsContent>

          {/* Binance Tab */}
          <TabsContent value="binance" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="binanceEnabled">Enable Binance API</Label>
                <p className="text-sm text-muted-foreground">Use Binance for market data and trading information</p>
              </div>
              <Switch id="binanceEnabled" checked={binanceEnabled} onCheckedChange={setBinanceEnabled} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="binanceApiKey">API Key</Label>
              <Input
                id="binanceApiKey"
                value={binanceApiKey}
                onChange={(e) => setBinanceApiKey(e.target.value)}
                placeholder="Enter your Binance API key"
                disabled={!binanceEnabled}
              />
              <p className="text-xs text-muted-foreground">
                Get your API key from{" "}
                <a
                  href="https://www.binance.com/en/my/settings/api-management"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline inline-flex items-center"
                >
                  Binance <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="binanceSecretKey">Secret Key</Label>
              <Input
                id="binanceSecretKey"
                type="password"
                value={binanceSecretKey}
                onChange={(e) => setBinanceSecretKey(e.target.value)}
                placeholder="Enter your Binance secret key"
                disabled={!binanceEnabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="binanceEndpoint">API Endpoint</Label>
              <Input
                id="binanceEndpoint"
                value={binanceEndpoint}
                onChange={(e) => setBinanceEndpoint(e.target.value)}
                placeholder="Enter Binance API endpoint"
                disabled={!binanceEnabled}
              />
            </div>

            <div className="flex justify-between mt-4">
              <Button
                variant="outline"
                onClick={() => handleTestConnection("Binance")}
                disabled={isLoading || !binanceEnabled}
              >
                Test Connection
              </Button>
              <Button onClick={() => handleSaveSettings("Binance")} disabled={isLoading || !binanceEnabled}>
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mt-4 bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}

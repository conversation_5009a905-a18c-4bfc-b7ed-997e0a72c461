"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle2, AlertTriangle, AlertCircle, Info } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface AuditItem {
  title: string
  severity: "critical" | "high" | "medium" | "low"
  description: string
  details: string
  recommendation: string
  status: "pass" | "warning" | "fail" | "pending"
}

interface AuditCategory {
  name: string
  items: AuditItem[]
}

export default function SecurityAuditReport() {
  const [activeTab, setActiveTab] = useState("contract")

  const auditData: AuditCategory[] = [
    {
      name: "Contract Security",
      items: [
        {
          title: "Reentrancy Protection",
          severity: "critical",
          description: "Check for reentrancy vulnerabilities in contract logic",
          details:
            "No reentrancy vulnerabilities detected in the contract code. All state changes are performed before external calls.",
          recommendation: "Continue to follow the checks-effects-interactions pattern in future updates.",
          status: "pass",
        },
        {
          title: "Authority Validation",
          severity: "high",
          description: "Verify proper authority checks on sensitive operations",
          details:
            "Some authority checks are present but could be bypassed in certain edge cases. The token creation function doesn't fully validate the caller's permissions.",
          recommendation:
            "Implement comprehensive authority validation for all privileged operations. Consider using a dedicated authority account with proper PDA derivation.",
          status: "warning",
        },
        {
          title: "Integer Overflow/Underflow",
          severity: "high",
          description: "Check for arithmetic operations that could overflow or underflow",
          details: "No integer overflow or underflow vulnerabilities detected. The contract uses safe math operations.",
          recommendation: "Continue using safe math operations for all arithmetic calculations.",
          status: "pass",
        },
        {
          title: "Proper PDA Derivation",
          severity: "critical",
          description: "Verify PDAs are derived with correct seeds and bumps",
          details:
            "PDA derivation in the token restriction logic uses incorrect seeds, potentially allowing unauthorized access to restricted accounts.",
          recommendation:
            "Revise PDA derivation to use proper seeds including the program ID and appropriate account identifiers. Verify bump seed handling.",
          status: "fail",
        },
      ],
    },
    {
      name: "Token Security",
      items: [
        {
          title: "SPL Token Compliance",
          severity: "critical",
          description: "Verify token implementation follows SPL Token standard",
          details:
            "The token implementation correctly follows the SPL Token standard with proper mint, freeze, and transfer authorities.",
          recommendation: "No action needed. Continue following SPL Token standards for future token implementations.",
          status: "pass",
        },
        {
          title: "Metadata Program Integration",
          severity: "medium",
          description: "Check correct integration with Metaplex Metadata program",
          details:
            "Metadata is created but some fields are not properly set, which could cause issues with certain wallets or marketplaces.",
          recommendation:
            "Update metadata creation to include all required fields and follow the latest Metaplex standards.",
          status: "warning",
        },
        {
          title: "Token Account Initialization",
          severity: "high",
          description: "Verify proper initialization of token accounts",
          details: "Token accounts are properly initialized with correct owners and mint references.",
          recommendation: "No action needed. Continue following proper initialization patterns.",
          status: "pass",
        },
        {
          title: "Decimals Configuration",
          severity: "medium",
          description: "Check for proper decimals configuration in token mint",
          details: "Token decimals are properly configured during mint creation.",
          recommendation: "No action needed.",
          status: "pass",
        },
      ],
    },
    {
      name: "DEX Security",
      items: [
        {
          title: "Raydium Integration",
          severity: "high",
          description: "Verify correct integration with Raydium DEX",
          details:
            "Integration with Raydium uses outdated pool creation methods that may not be compatible with the latest Raydium version.",
          recommendation: "Update Raydium integration to use the latest pool creation methods and APIs.",
          status: "warning",
        },
        {
          title: "Liquidity Pool Security",
          severity: "critical",
          description: "Check for vulnerabilities in liquidity pool creation and management",
          details: "Liquidity pool creation and management follows secure practices with proper authority checks.",
          recommendation: "No action needed. Continue following secure liquidity pool management practices.",
          status: "pass",
        },
        {
          title: "Slippage Protection",
          severity: "high",
          description: "Verify proper slippage protection in swap operations",
          details:
            "Swap operations do not implement proper slippage protection, potentially exposing users to front-running attacks.",
          recommendation:
            "Implement minimum output amount checks in swap operations based on user-defined slippage tolerance.",
          status: "fail",
        },
        {
          title: "Fee Calculation",
          severity: "medium",
          description: "Check for correct fee calculation and distribution",
          details: "Fee calculation and distribution follows the expected pattern with proper accounting.",
          recommendation: "No action needed.",
          status: "pass",
        },
      ],
    },
    {
      name: "Token Protection",
      items: [
        {
          title: "Transaction Limits",
          severity: "medium",
          description: "Verify proper implementation of transaction limits",
          details:
            "Transaction limits are implemented but could be bypassed by splitting transactions across multiple accounts.",
          recommendation:
            "Enhance transaction limit checks to consider account relationships and implement global limits where appropriate.",
          status: "warning",
        },
        {
          title: "Cooldown Periods",
          severity: "medium",
          description: "Check for proper implementation of transaction cooldowns",
          details: "Cooldown periods are properly implemented with secure timestamp validation.",
          recommendation: "No action needed. Continue using secure timestamp validation for cooldown periods.",
          status: "pass",
        },
        {
          title: "Blacklist Functionality",
          severity: "high",
          description: "Verify blacklist implementation and authority controls",
          details:
            "Blacklist functionality has a critical vulnerability allowing blacklisted addresses to bypass restrictions through proxy accounts.",
          recommendation:
            "Revise blacklist implementation to check for both direct and indirect transfers. Implement proper authority controls for blacklist management.",
          status: "fail",
        },
        {
          title: "Anti-Whale Measures",
          severity: "medium",
          description: "Check for proper implementation of holding limits",
          details: "Verification of anti-whale measures is pending further analysis.",
          recommendation: "Complete the implementation and testing of anti-whale measures before deployment.",
          status: "pending",
        },
      ],
    },
  ]

  // Calculate summary statistics
  const summary = {
    total: 0,
    pass: 0,
    warning: 0,
    fail: 0,
    pending: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
  }

  auditData.forEach((category) => {
    category.items.forEach((item) => {
      summary.total++
      summary[item.status]++
      summary[item.severity]++
    })
  })

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "text-red-600 bg-red-50 border-red-200"
      case "high":
        return "text-orange-600 bg-orange-50 border-orange-200"
      case "medium":
        return "text-yellow-600 bg-yellow-50 border-yellow-200"
      case "low":
        return "text-blue-600 bg-blue-50 border-blue-200"
      default:
        return "text-gray-600 bg-gray-50 border-gray-200"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pass":
        return <CheckCircle2 className="h-5 w-5 text-green-500" />
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case "fail":
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case "pending":
        return <Info className="h-5 w-5 text-blue-500" />
      default:
        return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pass":
        return "text-green-600 bg-green-50 border-green-200"
      case "warning":
        return "text-yellow-600 bg-yellow-50 border-yellow-200"
      case "fail":
        return "text-red-600 bg-red-50 border-red-200"
      case "pending":
        return "text-blue-600 bg-blue-50 border-blue-200"
      default:
        return "text-gray-600 bg-gray-50 border-gray-200"
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">Security Audit Report</CardTitle>
        <CardDescription>Comprehensive security analysis of the Solana platform smart contracts</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Audit Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>Pass</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{summary.pass}</span>
                    <Progress value={(summary.pass / summary.total) * 100} className="w-24 bg-gray-200">
                      <div className="h-full bg-green-500 rounded-full" />
                    </Progress>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span>Warning</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{summary.warning}</span>
                    <Progress value={(summary.warning / summary.total) * 100} className="w-24 bg-gray-200">
                      <div className="h-full bg-yellow-500 rounded-full" />
                    </Progress>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span>Fail</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{summary.fail}</span>
                    <Progress value={(summary.fail / summary.total) * 100} className="w-24 bg-gray-200">
                      <div className="h-full bg-red-500 rounded-full" />
                    </Progress>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span>Pending</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{summary.pending}</span>
                    <Progress value={(summary.pending / summary.total) * 100} className="w-24 bg-gray-200">
                      <div className="h-full bg-blue-500 rounded-full" />
                    </Progress>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Severity Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>Critical</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{summary.critical}</span>
                    <Progress value={(summary.critical / summary.total) * 100} className="w-24 bg-gray-200">
                      <div className="h-full bg-red-500 rounded-full" />
                    </Progress>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span>High</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{summary.high}</span>
                    <Progress value={(summary.high / summary.total) * 100} className="w-24 bg-gray-200">
                      <div className="h-full bg-orange-500 rounded-full" />
                    </Progress>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span>Medium</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{summary.medium}</span>
                    <Progress value={(summary.medium / summary.total) * 100} className="w-24 bg-gray-200">
                      <div className="h-full bg-yellow-500 rounded-full" />
                    </Progress>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span>Low</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{summary.low}</span>
                    <Progress value={(summary.low / summary.total) * 100} className="w-24 bg-gray-200">
                      <div className="h-full bg-blue-500 rounded-full" />
                    </Progress>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="contract" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 mb-4">
            {auditData.map((category) => (
              <TabsTrigger key={category.name} value={category.name.toLowerCase().replace(/\s+/g, "")}>
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {auditData.map((category) => (
            <TabsContent key={category.name} value={category.name.toLowerCase().replace(/\s+/g, "")}>
              <div className="space-y-4">
                {category.items.map((item, index) => (
                  <Card
                    key={index}
                    className={`border-l-4 ${item.status === "fail" ? "border-l-red-500" : item.status === "warning" ? "border-l-yellow-500" : item.status === "pending" ? "border-l-blue-500" : "border-l-green-500"}`}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(item.status)}
                          <CardTitle className="text-lg">{item.title}</CardTitle>
                        </div>
                        <Badge className={getSeverityColor(item.severity)}>
                          {item.severity.charAt(0).toUpperCase() + item.severity.slice(1)}
                        </Badge>
                      </div>
                      <CardDescription>{item.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-1">Details</h4>
                        <p className="text-sm text-gray-600">{item.details}</p>
                      </div>
                      <Alert className={getStatusColor(item.status)}>
                        <AlertTitle>Recommendation</AlertTitle>
                        <AlertDescription>{item.recommendation}</AlertDescription>
                      </Alert>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Overall Assessment */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Overall Assessment</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert
              className={
                summary.fail > 0
                  ? "bg-red-50 border-red-200"
                  : summary.warning > 0
                    ? "bg-yellow-50 border-yellow-200"
                    : "bg-green-50 border-green-200"
              }
            >
              <AlertTitle className="flex items-center">
                {summary.fail > 0 ? (
                  <>
                    <AlertCircle className="h-5 w-5 mr-2 text-red-500" /> Not Ready for Production
                  </>
                ) : summary.warning > 0 ? (
                  <>
                    <AlertTriangle className="h-5 w-5 mr-2 text-yellow-500" /> Needs Improvements
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="h-5 w-5 mr-2 text-green-500" /> Ready for Production
                  </>
                )}
              </AlertTitle>
              <AlertDescription>
                {summary.fail > 0 ? (
                  <p>The contract has critical security issues that must be addressed before deployment.</p>
                ) : summary.warning > 0 ? (
                  <p>The contract has some security concerns that should be addressed before deployment.</p>
                ) : (
                  <p>The contract passes all security checks and is ready for production deployment.</p>
                )}
              </AlertDescription>
            </Alert>

            <div className="mt-4">
              <p className="text-sm text-gray-600">
                This security audit was conducted on {new Date().toLocaleDateString()} and represents the state of the
                code at the time of the audit. Future code changes may introduce new security concerns that are not
                covered by this report.
              </p>
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { AlertCircle, CheckCircle, Clock, Database, Download, RefreshCw, Zap } from "lucide-react"
import type { PerformanceMetric, ResourceUsage, OptimizationRecommendation } from "@/lib/performance-service"

export default function PerformanceDashboard() {
  const { toast } = useToast()
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [resources, setResources] = useState<ResourceUsage[]>([])
  const [recommendations, setRecommendations] = useState<OptimizationRecommendation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchPerformanceData = async () => {
    try {
      setIsRefreshing(true)
      const response = await fetch("/api/admin/performance")
      const data = await response.json()

      if (data.success) {
        setMetrics(data.data.metrics)
        setResources(data.data.resources)
        setRecommendations(data.data.recommendations)
        setLastUpdated(new Date(data.data.lastUpdated))
      } else {
        throw new Error(data.error || "Failed to fetch performance data")
      }
    } catch (error) {
      console.error("Error fetching performance data:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de performance",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    fetchPerformanceData()

    // Rafraîchir les données toutes les 30 secondes
    const interval = setInterval(() => {
      fetchPerformanceData()
    }, 30000)

    return () => clearInterval(interval)
  }, [toast])

  const handleRefresh = () => {
    fetchPerformanceData()
  }

  const handleOptimize = async (recommendationId?: string) => {
    try {
      const action = recommendationId ? "optimize" : "optimize_all"
      const body = recommendationId ? { action, recommendationId } : { action }

      const response = await fetch("/api/admin/performance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Succès",
          description: recommendationId
            ? "L'optimisation a été appliquée avec succès"
            : "Toutes les optimisations ont été appliquées avec succès",
        })

        // Rafraîchir les données après l'optimisation
        fetchPerformanceData()
      } else {
        throw new Error(data.error || "Failed to apply optimization")
      }
    } catch (error) {
      console.error("Error applying optimization:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'appliquer l'optimisation",
        variant: "destructive",
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good":
        return "text-green-500"
      case "warning":
        return "text-amber-500"
      case "critical":
        return "text-red-500"
      default:
        return ""
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <span className="text-green-500">↑</span>
      case "down":
        return <span className="text-red-500">↓</span>
      case "stable":
        return <span className="text-gray-500">→</span>
      default:
        return null
    }
  }

  const getResourceUsageColor = (current: number, max: number) => {
    const percentage = (current / max) * 100
    if (percentage > 90) return "bg-red-500"
    if (percentage > 70) return "bg-amber-500"
    return "bg-green-500"
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Tableau de Bord des Performances</h2>
          <p className="text-muted-foreground">Surveillez les performances de la plateforme en temps réel</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing} className="flex items-center gap-2">
            {isRefreshing ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Rafraîchissement...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4" />
                Rafraîchir
              </>
            )}
          </Button>
          <Button onClick={() => handleOptimize()} className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Optimiser tout
          </Button>
        </div>
      </div>

      {lastUpdated && (
        <p className="text-sm text-muted-foreground">Dernière mise à jour: {lastUpdated.toLocaleString()}</p>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Clock className="h-5 w-5 mr-2 text-blue-500" />
              Temps de réponse moyen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {metrics.find((m) => m.id === "api_response_time")?.value.toFixed(0)} ms
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <span className={getStatusColor(metrics.find((m) => m.id === "api_response_time")?.status || "")}>
                {getTrendIcon(metrics.find((m) => m.id === "api_response_time")?.trend || "")}
              </span>
              <span className="ml-1">
                {metrics.find((m) => m.id === "api_response_time")?.trend === "down"
                  ? "Amélioration"
                  : metrics.find((m) => m.id === "api_response_time")?.trend === "up"
                    ? "Dégradation"
                    : "Stable"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Zap className="h-5 w-5 mr-2 text-amber-500" />
              Débit de transactions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {metrics.find((m) => m.id === "transaction_throughput")?.value.toFixed(0)} tx/min
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <span className={getStatusColor(metrics.find((m) => m.id === "transaction_throughput")?.status || "")}>
                {getTrendIcon(metrics.find((m) => m.id === "transaction_throughput")?.trend || "")}
              </span>
              <span className="ml-1">
                {metrics.find((m) => m.id === "transaction_throughput")?.trend === "up"
                  ? "Amélioration"
                  : metrics.find((m) => m.id === "transaction_throughput")?.trend === "down"
                    ? "Dégradation"
                    : "Stable"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Database className="h-5 w-5 mr-2 text-purple-500" />
              Temps de requête BDD
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {metrics.find((m) => m.id === "database_query_time")?.value.toFixed(0)} ms
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <span className={getStatusColor(metrics.find((m) => m.id === "database_query_time")?.status || "")}>
                {getTrendIcon(metrics.find((m) => m.id === "database_query_time")?.trend || "")}
              </span>
              <span className="ml-1">
                {metrics.find((m) => m.id === "database_query_time")?.trend === "down"
                  ? "Amélioration"
                  : metrics.find((m) => m.id === "database_query_time")?.trend === "up"
                    ? "Dégradation"
                    : "Stable"}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="metrics" className="space-y-4">
        <TabsList>
          <TabsTrigger value="metrics">Métriques</TabsTrigger>
          <TabsTrigger value="resources">Ressources</TabsTrigger>
          <TabsTrigger value="recommendations">Recommandations</TabsTrigger>
        </TabsList>

        <TabsContent value="metrics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Métriques de performance</CardTitle>
              <CardDescription>Analyse détaillée des métriques de performance de la plateforme</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {metrics.map((metric) => (
                <div key={metric.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{metric.name}</h3>
                    <div className="flex items-center">
                      <span className={`font-medium ${getStatusColor(metric.status)}`}>
                        {metric.value.toFixed(1)} {metric.unit}
                      </span>
                      <span className="ml-2">{getTrendIcon(metric.trend)}</span>
                    </div>
                  </div>
                  <Progress
                    value={metric.id.includes("time") ? 100 - metric.value / 10 : metric.value / 10}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground">
                    {metric.status === "good"
                      ? "Performance optimale"
                      : metric.status === "warning"
                        ? "Performance sous-optimale"
                        : "Performance critique"}
                  </p>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Utilisation des ressources</CardTitle>
              <CardDescription>Analyse détaillée de l'utilisation des ressources système</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {resources.map((resource) => (
                <div key={resource.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{resource.name}</h3>
                    <span className={getStatusColor(resource.status)}>
                      {resource.current.toFixed(1)}/{resource.max} {resource.unit} (
                      {((resource.current / resource.max) * 100).toFixed(0)}%)
                    </span>
                  </div>
                  <div className="h-4 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${getResourceUsageColor(resource.current, resource.max)}`}
                      style={{ width: `${(resource.current / resource.max) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {resource.status === "good"
                      ? "Utilisation normale"
                      : resource.status === "warning"
                        ? "Utilisation élevée - Envisagez d'optimiser"
                        : "Utilisation critique - Action requise"}
                  </p>
                </div>
              ))}

              <div className="flex justify-end">
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Télécharger le rapport complet
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recommandations d'optimisation</CardTitle>
              <CardDescription>Suggestions pour améliorer les performances de la plateforme</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recommendations.length === 0 ? (
                <Alert className="bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <AlertTitle>Aucune recommandation</AlertTitle>
                  <AlertDescription>
                    Toutes les métriques de performance sont dans les plages optimales.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  {recommendations.map((recommendation) => (
                    <div
                      key={recommendation.id}
                      className={`p-4 border rounded-md ${
                        recommendation.status === "completed"
                          ? "bg-green-50 border-green-200"
                          : recommendation.status === "in_progress"
                            ? "bg-blue-50 border-blue-200"
                            : recommendation.impact === "high"
                              ? "bg-red-50 border-red-200"
                              : "bg-amber-50 border-amber-200"
                      }`}
                    >
                      <h3
                        className={`font-medium flex items-center ${
                          recommendation.status === "completed"
                            ? "text-green-800"
                            : recommendation.status === "in_progress"
                              ? "text-blue-800"
                              : recommendation.impact === "high"
                                ? "text-red-800"
                                : "text-amber-800"
                        }`}
                      >
                        <AlertCircle className="h-4 w-4 mr-2" />
                        {recommendation.title}
                      </h3>
                      <p
                        className={`mt-1 text-sm ${
                          recommendation.status === "completed"
                            ? "text-green-700"
                            : recommendation.status === "in_progress"
                              ? "text-blue-700"
                              : recommendation.impact === "high"
                                ? "text-red-700"
                                : "text-amber-700"
                        }`}
                      >
                        {recommendation.description}
                      </p>
                      <div className="mt-2">
                        {recommendation.status === "pending" ? (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleOptimize(recommendation.id)}
                            className={`${
                              recommendation.impact === "high"
                                ? "text-red-800 border-red-300 bg-red-100 hover:bg-red-200"
                                : "text-amber-800 border-amber-300 bg-amber-100 hover:bg-amber-200"
                            }`}
                          >
                            Appliquer l'optimisation
                          </Button>
                        ) : recommendation.status === "in_progress" ? (
                          <div className="flex items-center">
                            <RefreshCw className="h-4 w-4 animate-spin mr-2 text-blue-500" />
                            <span className="text-sm text-blue-700">Optimisation en cours...</span>
                          </div>
                        ) : recommendation.status === "completed" ? (
                          <div className="flex items-center">
                            <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                            <span className="text-sm text-green-700">Optimisation appliquée</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
                            <span className="text-sm text-red-700">Échec de l'optimisation</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}

                  <div className="mt-6">
                    <Button
                      className="w-full"
                      onClick={() => handleOptimize()}
                      disabled={recommendations.every((r) => r.status === "completed" || r.status === "in_progress")}
                    >
                      <Zap className="mr-2 h-4 w-4" />
                      Appliquer toutes les optimisations recommandées
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

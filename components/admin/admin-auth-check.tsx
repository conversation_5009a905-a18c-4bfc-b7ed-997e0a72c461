"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, ShieldAlert } from "lucide-react"
import { Button } from "@/components/ui/button"

interface AdminAuthCheckProps {
  children: React.ReactNode
  redirectTo?: string
}

export function AdminAuthCheck({ children, redirectTo = "/" }: AdminAuthCheckProps) {
  const { publicKey, connected } = useWallet()
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!connected || !publicKey) {
        setIsAdmin(false)
        setIsLoading(false)
        setError("Veuillez connecter votre wallet")
        return
      }

      try {
        const walletAddress = publicKey.toString()
        console.log("Checking admin status for wallet:", walletAddress)

        // Appel à l'API pour vérifier le statut admin
        const response = await fetch(`/api/admin/check-auth?wallet=${walletAddress}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        })

        if (!response.ok) {
          throw new Error("Failed to verify admin status")
        }

        const data = await response.json()
        console.log("Admin status response:", data)

        setIsAdmin(data.isAdmin)
        setIsLoading(false)

        if (!data.isAdmin) {
          setError("Vous n'avez pas les permissions d'administrateur")
        } else {
          setError(null)
          // Set cookies client-side as well to ensure middleware gets it
          document.cookie = `walletAddress=${walletAddress}; path=/; max-age=3600; SameSite=Strict`
          document.cookie = `isAdmin=true; path=/; max-age=3600; SameSite=Strict`
        }
      } catch (error) {
        console.error("Error checking admin status:", error)
        setIsAdmin(false)
        setIsLoading(false)
        setError("Une erreur s'est produite lors de la vérification du statut admin")
        toast({
          title: "Erreur d'authentification",
          description: "Impossible de vérifier votre statut d'administrateur. Veuillez réessayer.",
          variant: "destructive",
        })
      }
    }

    checkAdminStatus()
  }, [connected, publicKey, toast])

  useEffect(() => {
    if (isAdmin === false && !isLoading) {
      toast({
        title: "Accès refusé",
        description: "Vous n'avez pas la permission d'accéder à l'espace administrateur.",
        variant: "destructive",
      })
      // Delay redirect slightly to allow toast to be seen
      const timer = setTimeout(() => {
        router.push(redirectTo)
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [isAdmin, isLoading, router, redirectTo, toast])

  if (isLoading) {
    return (
      <div className="flex h-[80vh] w-full items-center justify-center">
        <Card className="w-[400px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShieldAlert className="h-5 w-5" />
              Authentification Admin
            </CardTitle>
            <CardDescription>Vérification des identifiants administrateur...</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center gap-4 py-6">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-center text-sm text-muted-foreground">Vérification de l'adresse du wallet...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isAdmin === false) {
    return (
      <div className="flex h-[80vh] w-full items-center justify-center">
        <Card className="w-[400px]">
          <CardHeader>
            <CardTitle className="text-destructive flex items-center gap-2">
              <ShieldAlert className="h-5 w-5" />
              Accès refusé
            </CardTitle>
            <CardDescription>{error || "Vous n'avez pas la permission d'accéder à cette page."}</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center gap-4 py-6">
            <Button onClick={() => router.push(redirectTo)}>Retour à l'accueil</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isAdmin) {
    return <>{children}</>
  }

  return null
}

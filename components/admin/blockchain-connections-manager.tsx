"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle, RefreshCw, Loader2, Network } from "lucide-react"

// Types pour les connexions blockchain
interface NetworkConnection {
  url: string
  enabled: boolean
  lastChecked?: string
  status: "connected" | "disconnected" | "error"
}

export default function BlockchainConnectionsManager() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isTesting, setIsTesting] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("solana")
  const [networkConnections, setNetworkConnections] = useState<Record<string, NetworkConnection>>({})
  const [editingConnection, setEditingConnection] = useState<string | null>(null)
  const [editedUrl, setEditedUrl] = useState<string>("")

  // Charger les connexions blockchain
  useEffect(() => {
    fetchNetworkConnections()
  }, [])

  // Récupérer les connexions blockchain
  const fetchNetworkConnections = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/admin/blockchain/connections")

      if (!response.ok) {
        throw new Error("Erreur lors de la récupération des connexions blockchain")
      }

      const data = await response.json()
      setNetworkConnections(data.connections || {})
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de charger les connexions blockchain",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Tester une connexion blockchain
  const testNetworkConnection = async (networkKey: string) => {
    setIsTesting(networkKey)
    try {
      const connection = networkConnections[networkKey]
      if (!connection) return

      const response = await fetch(`/api/admin/blockchain/connections/${networkKey}/test`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: connection.url,
        }),
      })

      if (!response.ok) {
        throw new Error("Échec du test de connexion")
      }

      const result = await response.json()

      // Mettre à jour le statut de la connexion
      const updatedConnections = {
        ...networkConnections,
        [networkKey]: {
          ...connection,
          status: result.success ? "connected" : "error",
          lastChecked: new Date().toISOString(),
        },
      }
      setNetworkConnections(updatedConnections)

      toast({
        title: result.success ? "Connexion réussie" : "Échec de la connexion",
        description: result.message,
        variant: result.success ? "default" : "destructive",
      })
    } catch (error) {
      console.error("Erreur lors du test de connexion:", error)

      // Mettre à jour le statut de la connexion en cas d'erreur
      const connection = networkConnections[networkKey]
      if (connection) {
        const updatedConnections = {
          ...networkConnections,
          [networkKey]: {
            ...connection,
            status: "error",
            lastChecked: new Date().toISOString(),
          },
        }
        setNetworkConnections(updatedConnections)
      }

      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de tester la connexion blockchain",
      })
    } finally {
      setIsTesting(null)
    }
  }

  // Sauvegarder les modifications d'une connexion blockchain
  const saveNetworkConnection = async (networkKey: string) => {
    setIsSaving(networkKey)
    try {
      const connection = networkConnections[networkKey]
      if (!connection) return

      const response = await fetch(`/api/admin/blockchain/connections/${networkKey}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: editedUrl,
          enabled: connection.enabled,
        }),
      })

      if (!response.ok) {
        throw new Error("Erreur lors de la sauvegarde de la connexion blockchain")
      }

      // Mettre à jour la liste des connexions
      const updatedConnections = {
        ...networkConnections,
        [networkKey]: {
          ...connection,
          url: editedUrl,
          status: "disconnected", // Réinitialiser le statut après modification
        },
      }
      setNetworkConnections(updatedConnections)

      toast({
        title: "Connexion sauvegardée",
        description: `Les paramètres de la connexion ${networkKey} ont été mis à jour avec succès`,
      })

      setEditingConnection(null)
    } catch (error) {
      console.error("Erreur lors de la sauvegarde de la connexion:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de sauvegarder la connexion blockchain",
      })
    } finally {
      setIsSaving(null)
    }
  }

  // Activer/désactiver une connexion blockchain
  const toggleNetworkConnection = async (networkKey: string, enabled: boolean) => {
    try {
      const connection = networkConnections[networkKey]
      if (!connection) return

      const response = await fetch(`/api/admin/blockchain/connections/${networkKey}/toggle`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ enabled }),
      })

      if (!response.ok) {
        throw new Error("Erreur lors de la modification du statut de la connexion")
      }

      // Mettre à jour la liste des connexions
      const updatedConnections = {
        ...networkConnections,
        [networkKey]: {
          ...connection,
          enabled,
        },
      }
      setNetworkConnections(updatedConnections)

      toast({
        title: enabled ? "Connexion activée" : "Connexion désactivée",
        description: `La connexion ${networkKey} a été ${enabled ? "activée" : "désactivée"} avec succès`,
      })
    } catch (error) {
      console.error("Erreur lors de la modification du statut:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de modifier le statut de la connexion",
      })
    }
  }

  // Obtenir le badge de statut
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "connected":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Connecté</Badge>
      case "disconnected":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Déconnecté</Badge>
      case "error":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Erreur</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  // Filtrer les connexions en fonction de l'onglet actif
  const filteredConnections = Object.entries(networkConnections).filter(([key]) => key.startsWith(activeTab))

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="bg-slate-50 dark:bg-slate-800">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl font-bold">Gestionnaire de Connexions Blockchain</CardTitle>
            <CardDescription>
              Configurez et gérez les connexions aux différentes blockchains supportées par la plateforme
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={fetchNetworkConnections}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Actualiser
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="solana">Solana</TabsTrigger>
            <TabsTrigger value="bnb">BNB Chain</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-0">
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center justify-between p-4 border rounded-md">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-40" />
                      <Skeleton className="h-3 w-60" />
                    </div>
                    <div className="flex space-x-2">
                      <Skeleton className="h-9 w-9 rounded-md" />
                      <Skeleton className="h-9 w-9 rounded-md" />
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredConnections.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Aucune connexion blockchain trouvée pour ce réseau</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredConnections.map(([key, connection]) => (
                  <div key={key} className="border rounded-md overflow-hidden">
                    <div className="flex flex-col md:flex-row md:items-center justify-between p-4 bg-slate-50 dark:bg-slate-800">
                      <div className="space-y-1 mb-2 md:mb-0">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">{key.split("_")[1].toUpperCase()}</h3>
                          {getStatusBadge(connection.status)}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {key.includes("mainnet") ? "Réseau principal" : "Réseau de test"}
                        </p>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={connection.enabled}
                          onCheckedChange={(checked) => toggleNetworkConnection(key, checked)}
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => testNetworkConnection(key)}
                          disabled={isTesting === key || !connection.enabled}
                        >
                          {isTesting === key ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Test...
                            </>
                          ) : (
                            "Tester"
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setEditingConnection(key)
                            setEditedUrl(connection.url)
                          }}
                        >
                          Configurer
                        </Button>
                      </div>
                    </div>

                    {editingConnection === key && (
                      <div className="p-4 border-t">
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor={`${key}-url`}>URL RPC</Label>
                            <Input
                              id={`${key}-url`}
                              value={editedUrl}
                              onChange={(e) => setEditedUrl(e.target.value)}
                              placeholder="https://api.example.com"
                            />
                          </div>

                          {key.includes("solana") && key.includes("mainnet") && (
                            <Alert>
                              <Network className="h-4 w-4" />
                              <AlertTitle>Information</AlertTitle>
                              <AlertDescription>
                                Pour de meilleures performances sur Solana Mainnet, utilisez un point de terminaison RPC
                                dédié comme{" "}
                                <a
                                  href="https://www.quicknode.com/"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  QuickNode
                                </a>{" "}
                                ou{" "}
                                <a
                                  href="https://www.helius.dev/"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  Helius
                                </a>
                              </AlertDescription>
                            </Alert>
                          )}

                          {connection.status === "error" && (
                            <Alert variant="destructive">
                              <AlertCircle className="h-4 w-4" />
                              <AlertTitle>Erreur de connexion</AlertTitle>
                              <AlertDescription>
                                La dernière tentative de connexion a échoué. Veuillez vérifier l'URL RPC et réessayer.
                              </AlertDescription>
                            </Alert>
                          )}

                          <div className="flex justify-end space-x-2">
                            <Button variant="outline" onClick={() => setEditingConnection(null)}>
                              Annuler
                            </Button>
                            <Button onClick={() => saveNetworkConnection(key)} disabled={isSaving === key}>
                              {isSaving === key ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Sauvegarde...
                                </>
                              ) : (
                                "Sauvegarder"
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}

                    {connection.lastChecked && (
                      <div className="px-4 py-2 text-xs text-muted-foreground border-t">
                        Dernière vérification: {new Date(connection.lastChecked).toLocaleString()}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      <CardFooter className="bg-slate-50 dark:bg-slate-800 border-t">
        <div className="flex items-center space-x-2">
          <Network className="h-4 w-4 text-muted-foreground" />
          <p className="text-xs text-muted-foreground">
            Les connexions blockchain sont essentielles pour le bon fonctionnement de la plateforme.
          </p>
        </div>
      </CardFooter>
    </Card>
  )
}

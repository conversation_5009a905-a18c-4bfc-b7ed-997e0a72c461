"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertTriangle,
  ArrowUpRight,
  CheckCircle2,
  Lock,
  RefreshCw,
  Shield,
  ShieldAlert,
  ShieldCheck,
  XCircle,
} from "lucide-react"

interface SecurityMetric {
  name: string
  score: number
  status: "good" | "warning" | "critical"
  description: string
}

interface SecurityAlert {
  id: string
  severity: "low" | "medium" | "high" | "critical"
  title: string
  description: string
  timestamp: string
  resolved: boolean
}

export function SecurityDashboard() {
  const [isLoading, setIsLoading] = useState(true)
  const [securityScore, setSecurityScore] = useState(0)
  const [metrics, setMetrics] = useState<SecurityMetric[]>([])
  const [alerts, setAlerts] = useState<SecurityAlert[]>([])
  const { toast } = useToast()

  useEffect(() => {
    loadSecurityData()
  }, [])

  const loadSecurityData = async () => {
    setIsLoading(true)
    try {
      // Simuler le chargement des données
      setTimeout(() => {
        // Métriques de sécurité
        const mockMetrics: SecurityMetric[] = [
          {
            name: "Authentification",
            score: 90,
            status: "good",
            description: "Authentification à deux facteurs activée. Rotation des clés recommandée.",
          },
          {
            name: "Autorisation",
            score: 85,
            status: "good",
            description: "Les contrôles d'accès sont bien définis. Quelques permissions à revoir.",
          },
          {
            name: "Sécurité des API",
            score: 78,
            status: "warning",
            description: "Limitation de débit mise en place. Validation d'entrée à améliorer.",
          },
          {
            name: "Gestion des clés",
            score: 95,
            status: "good",
            description: "Stockage et rotation des clés conformes aux meilleures pratiques.",
          },
          {
            name: "Surveillance",
            score: 65,
            status: "warning",
            description: "Système de journalisation en place. Alertes temps réel à améliorer.",
          },
          {
            name: "Protection contre les attaques",
            score: 40,
            status: "critical",
            description: "Vulnérabilité potentielle détectée. Correction urgente requise.",
          },
        ]

        // Alertes de sécurité
        const mockAlerts: SecurityAlert[] = [
          {
            id: "alert-1",
            severity: "critical",
            title: "Tentative d'accès administrateur non autorisée",
            description: "Multiples tentatives d'authentification échouées depuis l'adresse IP *************",
            timestamp: "2023-06-15T08:12:34Z",
            resolved: false,
          },
          {
            id: "alert-2",
            severity: "high",
            title: "Élévation de privilèges suspecte",
            description: "Utilisateur standard a tenté d'accéder à des fonctions admin via une modification de requête",
            timestamp: "2023-06-14T22:45:11Z",
            resolved: true,
          },
          {
            id: "alert-3",
            severity: "medium",
            title: "Activité inhabituelle d'API",
            description: "Volume de requêtes anormalement élevé détecté pour l'endpoint /api/token",
            timestamp: "2023-06-14T14:22:56Z",
            resolved: false,
          },
          {
            id: "alert-4",
            severity: "low",
            title: "Rotation de clé expirée",
            description: "La rotation programmée des clés API est en retard de 5 jours",
            timestamp: "2023-06-13T09:07:23Z",
            resolved: false,
          },
        ]

        // Calculer le score global de sécurité
        const averageScore = Math.round(mockMetrics.reduce((sum, metric) => sum + metric.score, 0) / mockMetrics.length)

        setMetrics(mockMetrics)
        setAlerts(mockAlerts)
        setSecurityScore(averageScore)
        setIsLoading(false)
      }, 1200)
    } catch (error) {
      console.error("Failed to load security data:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de sécurité",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500"
    if (score >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "critical":
        return (
          <div className="flex items-center gap-1 text-red-600 font-medium">
            <AlertTriangle className="h-4 w-4" />
            <span>Critique</span>
          </div>
        )
      case "high":
        return (
          <div className="flex items-center gap-1 text-orange-600 font-medium">
            <ShieldAlert className="h-4 w-4" />
            <span>Élevée</span>
          </div>
        )
      case "medium":
        return (
          <div className="flex items-center gap-1 text-yellow-600 font-medium">
            <Shield className="h-4 w-4" />
            <span>Moyenne</span>
          </div>
        )
      case "low":
        return (
          <div className="flex items-center gap-1 text-blue-600 font-medium">
            <ShieldCheck className="h-4 w-4" />
            <span>Faible</span>
          </div>
        )
      default:
        return (
          <div className="flex items-center gap-1 text-gray-600 font-medium">
            <Shield className="h-4 w-4" />
            <span>Inconnue</span>
          </div>
        )
    }
  }

  const resolveAlert = (id: string) => {
    setAlerts(alerts.map((alert) => (alert.id === id ? { ...alert, resolved: true } : alert)))

    toast({
      title: "Alerte résolue",
      description: "L'alerte a été marquée comme résolue",
    })
  }

  return (
    <div className="space-y-4">
      {/* Score de sécurité global */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Score de sécurité global</CardTitle>
              <CardDescription>Évaluation de la sécurité de la plateforme</CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={loadSecurityData} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
              Actualiser
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center space-y-2">
            <div className="relative w-40 h-40 flex items-center justify-center rounded-full border-8 border-muted">
              <div className={`absolute inset-0 rounded-full ${getScoreColor(securityScore)} opacity-20`}></div>
              <div className="text-4xl font-bold">{securityScore}%</div>
            </div>
            <div className="text-center">
              {securityScore >= 80 ? (
                <Alert className="bg-green-50 border-green-200">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <AlertTitle className="text-green-800">Sécurité optimale</AlertTitle>
                  <AlertDescription className="text-green-700">
                    La plateforme respecte les meilleures pratiques de sécurité
                  </AlertDescription>
                </Alert>
              ) : securityScore >= 60 ? (
                <Alert className="bg-yellow-50 border-yellow-200">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <AlertTitle className="text-yellow-800">Améliorations nécessaires</AlertTitle>
                  <AlertDescription className="text-yellow-700">
                    Certains aspects de sécurité doivent être améliorés
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertTitle>Risques de sécurité importants</AlertTitle>
                  <AlertDescription>
                    Attention: Des vulnérabilités critiques nécessitent une intervention immédiate
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="metrics" className="space-y-4">
        <TabsList>
          <TabsTrigger value="metrics">Métriques de sécurité</TabsTrigger>
          <TabsTrigger value="alerts">
            Alertes
            {alerts.filter((a) => !a.resolved).length > 0 && (
              <span className="ml-2 inline-flex items-center justify-center h-5 w-5 rounded-full bg-red-100 text-red-600 text-xs font-medium">
                {alerts.filter((a) => !a.resolved).length}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger value="recommendations">Recommandations</TabsTrigger>
        </TabsList>

        <TabsContent value="metrics">
          <Card>
            <CardHeader>
              <CardTitle>Métriques de sécurité détaillées</CardTitle>
              <CardDescription>Analyse des différents aspects de sécurité</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.map((metric, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="font-medium">{metric.name}</div>
                      <div
                        className={`
                        text-sm font-medium
                        ${
                          metric.status === "good"
                            ? "text-green-600"
                            : metric.status === "warning"
                              ? "text-yellow-600"
                              : "text-red-600"
                        }
                      `}
                      >
                        {metric.score}%
                      </div>
                    </div>
                    <Progress
                      value={metric.score}
                      className={`
                        h-2
                        ${
                          metric.status === "good"
                            ? "bg-green-100"
                            : metric.status === "warning"
                              ? "bg-yellow-100"
                              : "bg-red-100"
                        }
                      `}
                      indicatorClassName={`
                        ${
                          metric.status === "good"
                            ? "bg-green-500"
                            : metric.status === "warning"
                              ? "bg-yellow-500"
                              : "bg-red-500"
                        }
                      `}
                    />
                    <p className="text-sm text-muted-foreground">{metric.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>Alertes de sécurité</CardTitle>
              <CardDescription>Menaces et vulnérabilités détectées</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.length === 0 ? (
                  <div className="text-center py-8">
                    <ShieldCheck className="mx-auto h-12 w-12 text-green-500 mb-2" />
                    <h3 className="text-lg font-medium">Aucune alerte</h3>
                    <p className="text-muted-foreground">
                      Tout est en ordre. Aucune alerte de sécurité n'a été détectée.
                    </p>
                  </div>
                ) : (
                  alerts.map((alert) => (
                    <div
                      key={alert.id}
                      className={`border rounded-lg p-4 ${
                        alert.resolved
                          ? "border-gray-200 bg-gray-50"
                          : alert.severity === "critical"
                            ? "border-red-200 bg-red-50"
                            : alert.severity === "high"
                              ? "border-orange-200 bg-orange-50"
                              : alert.severity === "medium"
                                ? "border-yellow-200 bg-yellow-50"
                                : "border-blue-200 bg-blue-50"
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            {getSeverityBadge(alert.severity)}
                            {alert.resolved && (
                              <span className="text-xs px-2 py-0.5 rounded bg-gray-200 text-gray-700">Résolu</span>
                            )}
                          </div>
                          <h4 className="font-medium">{alert.title}</h4>
                          <p className="text-sm text-muted-foreground mt-1">{alert.description}</p>
                          <div className="text-xs text-muted-foreground mt-2">
                            {new Date(alert.timestamp).toLocaleString("fr-FR")}
                          </div>
                        </div>
                        {!alert.resolved && (
                          <Button variant="outline" size="sm" onClick={() => resolveAlert(alert.id)}>
                            Marquer comme résolu
                          </Button>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations">
          <Card>
            <CardHeader>
              <CardTitle>Recommandations de sécurité</CardTitle>
              <CardDescription>Actions suggérées pour améliorer la sécurité</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Lock className="h-5 w-5 text-primary" />
                    Authentification et autorisation
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <ArrowUpRight className="h-4 w-4 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">
                          Activer l'authentification à 2 facteurs pour tous les administrateurs
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Augmente la sécurité des comptes d'administration contre les tentatives d'accès non
                          autorisées.
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start gap-2">
                      <ArrowUpRight className="h-4 w-4 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">Revoir les permissions des utilisateurs avec accès administratif</p>
                        <p className="text-sm text-muted-foreground">
                          Appliquer le principe du moindre privilège pour limiter l'exposition aux risques.
                        </p>
                      </div>
                    </li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Shield className="h-5 w-5 text-primary" />
                    Protection des données
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <ArrowUpRight className="h-4 w-4 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">Mettre en place une rotation régulière des clés API</p>
                        <p className="text-sm text-muted-foreground">
                          Limiter l'impact potentiel d'une compromission des clés d'accès.
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start gap-2">
                      <ArrowUpRight className="h-4 w-4 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">Activer le chiffrement des données sensibles au repos</p>
                        <p className="text-sm text-muted-foreground">
                          Protéger les données stockées contre les accès non autorisés.
                        </p>
                      </div>
                    </li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <ShieldAlert className="h-5 w-5 text-primary" />
                    Surveillance et détection
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <ArrowUpRight className="h-4 w-4 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">Configurer des alertes en temps réel pour les activités suspectes</p>
                        <p className="text-sm text-muted-foreground">
                          Détecter et répondre rapidement aux incidents de sécurité potentiels.
                        </p>
                      </div>
                    </li>
                    <li className="flex items-start gap-2">
                      <ArrowUpRight className="h-4 w-4 text-primary mt-0.5" />
                      <div>
                        <p className="font-medium">Mettre en place un processus de réponse aux incidents</p>
                        <p className="text-sm text-muted-foreground">
                          Établir des procédures claires pour gérer les violations de sécurité.
                        </p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                <Shield className="mr-2 h-4 w-4" />
                Générer un rapport de sécurité complet
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

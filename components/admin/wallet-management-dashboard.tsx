"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Plus, RefreshCw, Loader2, Download, Upload } from "lucide-react"

interface WalletInfo {
  id: string
  address: string
  name: string
  type: "admin" | "treasury" | "development" | "marketing" | "liquidity" | "staking" | "other"
  network: "solana" | "bnb" | "ethereum"
  balance: number
  tokenBalances: {
    symbol: string
    name: string
    balance: number
  }[]
  createdAt: string
  lastActivity: string
  status: "active" | "inactive" | "locked"
  permissions: string[]
  privateKey?: string
}

export function WalletManagementDashboard() {
  const { toast } = useToast()
  const [wallets, setWallets] = useState<WalletInfo[]>([])
  const [filteredWallets, setFilteredWallets] = useState<WalletInfo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [showAddWalletDialog, setShowAddWalletDialog] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [selectedWallet, setSelectedWallet] = useState<WalletInfo | null>(null)
  const [isCreatingWallet, setIsCreatingWallet] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [newWallet, setNewWallet] = useState({
    name: "",
    type: "other",
    network: "solana",
  })
  const [importData, setImportData] = useState("")
  const [exportData, setExportData] = useState("")
  const [privateKeyVisible, setPrivateKeyVisible] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [walletToDelete, setWalletToDelete] = useState<string | null>(null)
  const [showLockDialog, setShowLockDialog] = useState(false)
  const [walletToLock, setWalletToLock] = useState<WalletInfo | null>(null)
  const [lockReason, setLockReason] = useState("")
  const [showBackupKeysDialog, setShowBackupKeysDialog] = useState(false)
  const [backupPassword, setBackupPassword] = useState("")
  const [confirmBackupPassword, setConfirmBackupPassword] = useState("")
  const [backupError, setBackupError] = useState("")
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false)
  const [editingPermissions, setEditingPermissions] = useState<string[]>([])

  // Charger les wallets
  useEffect(() => {
    loadWallets()
  }, [])

  const loadWallets = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Données simulées
      const mockWallets: WalletInfo[] = [
        {
          id: "wallet-1",
          address: "5YNmS1R9nNSCDzb5a7mMJ1dwK9uHeAAF4CmPEwKgVWr8",
          name: "Admin Principal",
          type: "admin",
          network: "solana",
          balance: 25.75,
          tokenBalances: [
            { symbol: "GF", name: "Global Finance", balance: 1000000 },
            { symbol: "USDC", name: "USD Coin", balance: 50000 }
          ],
          createdAt: "2025-01-15T10:30:00Z",
          lastActivity: "2025-03-01T14:45:00Z",
          status: "active",
          permissions: ["admin", "deploy", "mint", "burn"],
          privateKey: "5xgEQBqTuEHWB6g9mMshzhMuQKdW7fZ7tTiVwz6Xv7QzYG8aT9VFjRwMQ6z7LTkN"
        },
        {
          id: "wallet-2",
          address: "7nYS1R9nNSCDzb5a7mMJ1dwK9uHeAAF4CmPEwKgVWr9",
          name: "Trésorerie",
          type: "treasury",
          network: "solana",
          balance: 150.25,
          tokenBalances: [
            { symbol: "GF", name: "Global Finance", balance: 5000000 },
            { symbol: "USDC", name: "USD Coin", balance: 250000 }
          ],
          createdAt: "2025-01-20T11:15:00Z",
          lastActivity: "2025-02-28T09:30:00Z",
          status: "active",
          permissions: ["transfer", "receive"],
          privateKey: "7xgEQBqTuEHWB6g9mMshzhMuQKdW7fZ7tTiVwz6Xv7QzYG8aT9VFjRwMQ6z7LTkN"
        },
        {
          id: "wallet-3",
          address: "******************************************",
          name: "Marketing",
          type: "marketing",
          network: "bnb",
          balance: 75.5,
          tokenBalances: [
            { symbol: "GF", name: "Global Finance", balance: 2000000 },
            { symbol: "BNB", name: "Binance Coin", balance: 75.5 }
          ],
          createdAt: "2025-01-25T14:20:00Z",
          lastActivity: "2025-02-25T16:10:00Z",
          status: "active",
          permissions: ["transfer", "receive"],
          privateKey: "******************************************5xgEQBqTuEHWB6g9mMshzh"
        },
        {
          id: "wallet-4",
          address: "******************************************",
          name: "Développement",
          type: "development",
          network: "bnb",
          balance: 50.25,
          tokenBalances: [
            { symbol: "GF", name: "Global Finance", balance: 1500000 },
            { symbol: "BNB", name: "Binance Coin", balance: 50.25 }
          ],
          createdAt: "2025-02-01T09:45:00Z",
          lastActivity: "2025-02-20T11:30:00Z",
          status: "active",
          permissions: ["transfer", "receive", "deploy"],
          privateKey: "******************************************5xgEQBqTuEHWB6g9mMshzh"
        },
        {
          id: "wallet-5",
          address: "*******************************************",
          name: "Liquidité",
          type: "liquidity",
          network: "solana",
          balance: 200.5,
          tokenBalances: [
            { symbol: "GF", name: "Global Finance", balance: 3000000 },
            { symbol: "USDC", name: "USD Coin", balance: 300000 }
          ],
          createdAt: "2025-02-05T13:10:00Z",
          lastActivity: "2025-02-15T10:20:00Z",
          status: "active",
          permissions: ["transfer", "receive", "liquidity"],
          privateKey: "*******************************************5xgEQBqTuEHWB6g9mMshzh"
        },
        {
          id: "wallet-6",
          address: "******************************************",
          name: "Staking",
          type: "staking",
          network: "ethereum",
          balance: 15.75,
          tokenBalances: [
            { symbol: "GF", name: "Global Finance", balance: 4000000 },
            { symbol: "ETH", name: "Ethereum", balance: 15.75 }
          ],
          createdAt: "2025-02-10T15:30:00Z",
          lastActivity: "2025-02-10T15:30:00Z",
          status: "inactive",
          permissions: ["transfer", "receive", "stake"],
          privateKey: "******************************************5xgEQBqTuEHWB6g9mMshzh"
        }
      ]

      setWallets(mockWallets)
      setFilteredWallets(mockWallets)
      setIsLoading(false)
    } catch (error) {
      console.error("Erreur lors du chargement des wallets:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les wallets",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  // Filtrer les wallets
  useEffect(() => {
    let filtered = [...wallets]

    if (searchQuery) {
      filtered = filtered.filter(
        (w) =>
          w.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          w.address.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    if (activeTab !== "all") {
      if (activeTab === "solana" || activeTab === "bnb" || activeTab === "ethereum") {
        filtered = filtered.filter((w) => w.network === activeTab)
      } else if (activeTab === "locked") {
        filtered = filtered.filter((w) => w.status === "locked")
      } else if (activeTab === "inactive") {
        filtered = filtered.filter((w) => w.status === "inactive")
      } else {
        filtered = filtered.filter((w) => w.type === activeTab)
      }
    }

    setFilteredWallets(filtered)
  }, [searchQuery, activeTab, wallets])

  // Créer un nouveau wallet
  const createWallet = async () => {
    if (!newWallet.name) {
      toast({
        title: "Erreur",
        description: "Veuillez saisir un nom pour le wallet",
        variant: "destructive",
      })
      return
    }

    setIsCreatingWallet(true)
    try {
      // Simuler un appel API
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Générer une adresse aléatoire selon le réseau
      let address = ""
      let privateKey = ""
      
      if (newWallet.network === "solana") {
        address = `${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`
        privateKey = `${address}${Math.random().toString(36).substring(2, 15)}`
      } else if (newWallet.network === "bnb" || newWallet.network === "ethereum") {
        address = `0x${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`
        privateKey = `${address}${Math.random().toString(36).substring(2, 15)}`
      }

      const newWalletInfo: WalletInfo = {
        id: `wallet-${wallets.length + 1}`,
        address,
        name: newWallet.name,
        type: newWallet.type as any,
        network: newWallet.network as any,
        balance: 0,
        tokenBalances: [],
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        status: "active",
        permissions: ["transfer", "receive"],
        privateKey,
      }

      setWallets([...wallets, newWalletInfo])
      setShowAddWalletDialog(false)
      setNewWallet({
        name: "",
        type: "other",
        network: "solana",
      })

      toast({
        title: "Wallet créé",
        description: `Le wallet ${newWalletInfo.name} a été créé avec succès`,
      })
    } catch (error) {
      console.error("Erreur lors de la création du wallet:", error)
      toast({
        title: "Erreur",
        description: "Impossible de créer le wallet",
        variant: "destructive",
      })
    } finally {
      setIsCreatingWallet(false)
    }
  }

  // Rafraîchir les soldes
  const refreshBalances = async () => {
    setIsRefreshing(true)
    try {
      // Simuler un appel API
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mettre à jour les soldes avec des valeurs aléatoires
      const updatedWallets = wallets.map(wallet => ({
        ...wallet,
        balance: Number.parseFloat((Math.random() * 100 + wallet.balance).toFixed(2)),
        lastActivity: new Date().toISOString(),
      }))

      setWallets(updatedWallets)

      toast({
        title: "Soldes mis à jour",
        description: "Les soldes des wallets ont été mis à jour avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour des soldes:", error)
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour les soldes",
        variant: "destructive",
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  // Afficher les détails d'un wallet
  const showWalletDetails = (wallet: WalletInfo) => {
    setSelectedWallet(wallet)
    setShowDetailsDialog(true)
    setPrivateKeyVisible(false)
  }

  // Copier l'adresse
  const copyAddress = (address: string) => {
    navigator.clipboard.writeText(address)
    toast({
      title: "Adresse copiée",
      description: "L'adresse a été copiée dans le presse-papier",
    })
  }

  // Copier la clé privée
  const copyPrivateKey = (privateKey: string) => {
    navigator.clipboard.writeText(privateKey)
    toast({
      title: "Clé privée copiée",
      description: "La clé privée a été copiée dans le presse-papier",
      variant: "destructive",
    })
  }

  // Exporter les wallets
  const exportWallets = () => {
    const exportData = JSON.stringify(wallets, null, 2)
    setExportData(exportData)
    setShowExportDialog(true)
  }

  // Télécharger l'export
  const downloadExport = () => {
    const blob = new Blob([exportData], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `wallets-export-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    setShowExportDialog(false)
  }

  // Importer des wallets
  const importWallets = () => {
    try {
      const importedData = JSON.parse(importData)
      
      if (!Array.isArray(importedData)) {
        throw new Error("Format d'import invalide")
      }

      // Vérifier que chaque wallet a les propriétés requises
      const validWallets = importedData.filter(wallet => 
        wallet.address && wallet.name && wallet.type && wallet.network
      )

      if (validWallets.length === 0) {
        throw new Error("Aucun wallet valide trouvé dans les données importées")
      }

      // Fusionner les wallets importés avec les wallets existants
      const mergedWallets = [...wallets]
      let newWalletsCount = 0

      validWallets.forEach(importedWallet => {
        const existingWalletIndex = mergedWallets.findIndex(w => w.address === importedWallet.address)
        
        if (existingWalletIndex === -1) {
          mergedWallets.push({
            ...importedWallet,
            id: `wallet-${mergedWallets.length + 1}`,
            createdAt: importedWallet.createdAt || new Date().toISOString(),
            lastActivity: importedWallet.lastActivity || new Date().toISOString(),
            status: importedWallet.status || "active",
            permissions: importedWallet.permissions || ["transfer", "receive"],
          })
          newWalletsCount++
        }
      })

      setWallets(mergedWallets)
      setShowImportDialog(false)
      setImportData("")

      toast({
        title: "Import réussi",
        description: `${newWalletsCount} nouveaux wallets ont été importés`,
      })
    } catch (error) {
      console.error("Erreur lors de l'import des wallets:", error)
      toast({
        title: "Erreur",
        description: "Impossible d'importer les wallets. Format invalide.",
        variant: "destructive",
      })
    }
  }

  // Supprimer un wallet
  const confirmDeleteWallet = (id: string) => {
    setWalletToDelete(id)
    setShowDeleteDialog(true)
  }

  const deleteWallet = () => {
    if (!walletToDelete) return

    const updatedWallets = wallets.filter(w => w.id !== walletToDelete)
    setWallets(updatedWallets)
    setShowDeleteDialog(false)
    setWalletToDelete(null)

    toast({
      title: "Wallet supprimé",
      description: "Le wallet a été supprimé avec succès",
    })
  }

  // Verrouiller/déverrouiller un wallet
  const confirmLockWallet = (wallet: WalletInfo) => {
    setWalletToLock(wallet)
    setLockReason("")
    setShowLockDialog(true)
  }

  const toggleWalletLock = () => {
    if (!walletToLock) return

    const updatedWallets = wallets.map(w => {
      if (w.id === walletToLock.id) {
        const newStatus = w.status === "locked" ? "active" : "locked"
        return {
          ...w,
          status: newStatus,
          lastActivity: new Date().toISOString(),
        }
      }
      return w
    })

    setWallets(updatedWallets)
    setShowLockDialog(false)
    setWalletToLock(null)
    setLockReason("")

    toast({
      title: walletToLock.status === "locked" ? "Wallet déverrouillé" : "Wallet verrouillé",
      description: walletToLock.status === "locked" 
        ? "Le wallet a été déverrouillé avec succès" 
        : "Le wallet a été verrouillé avec succès",
    })
  }

  // Sauvegarder les clés privées
  const backupPrivateKeys = () => {
    if (backupPassword !== confirmBackupPassword) {
      setBackupError("Les mots de passe ne correspondent pas")
      return
    }

    if (backupPassword.length < 8) {
      setBackupError("Le mot de passe doit contenir au moins 8 caractères")
      return
    }

    try {
      // Dans une implémentation réelle, les clés seraient chiffrées avec le mot de passe
      const keysData = wallets.map(w => ({
        name: w.name,
        address: w.address,
        network: w.network,
        privateKey: w.privateKey
      }))

      const blob = new Blob([JSON.stringify(keysData, null, 2)], { type: "application/json" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `wallet-keys-backup-${new Date().toISOString().slice(0, 10)}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      setShowBackupKeysDialog(false)
      setBackupPassword("")
      setConfirmBackupPassword("")
      setBackupError("")

      toast({
        title: "Sauvegarde réussie",
        description: "Les clés privées ont été sauvegardées avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de la sauvegarde des clés privées:", error)
      setBackupError("Erreur lors de la sauvegarde des clés privées")
    }
  }

  // Gérer les permissions
  const openPermissionsDialog = (wallet: WalletInfo) => {
    setSelectedWallet(wallet)
    setEditingPermissions([...wallet.permissions])
    setShowPermissionsDialog(true)
  }

  const togglePermission = (permission: string) => {
    if (editingPermissions.includes(permission)) {
      setEditingPermissions(editingPermissions.filter(p => p !== permission))
    } else {
      setEditingPermissions([...editingPermissions, permission])
    }
  }

  const savePermissions = () => {
    if (!selectedWallet) return

    const updatedWallets = wallets.map(w => {
      if (w.id === selectedWallet.id) {
        return {
          ...w,
          permissions: editingPermissions,
          lastActivity: new Date().toISOString(),
        }
      }
      return w
    })

    setWallets(updatedWallets)
    setShowPermissionsDialog(false)

    toast({
      title: "Permissions mises à jour",
      description: "Les permissions du wallet ont été mises à jour avec succès",
    })
  }

  // Obtenir le badge de réseau
  const getNetworkBadge = (network: string) => {
    switch (network) {
      case "solana":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Solana</Badge>
      case "bnb":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">BNB Chain</Badge>
      case "ethereum":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Ethereum</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{network}</Badge>
    }
  }

  // Obtenir le badge de type
  const getTypeBadge = (type: string) => {
    switch (type) {
      case "admin":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Admin</Badge>
      case "treasury":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Trésorerie</Badge>
      case "development":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Développement</Badge>
      case "marketing":
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Marketing</Badge>
      case "liquidity":
        return <Badge className="bg-cyan-100 text-cyan-800 border-cyan-200">Liquidité</Badge>
      case "staking":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Staking</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Autre</Badge>
    }
  }

  // Obtenir le badge de statut
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Actif</Badge>
      case "inactive":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Inactif</Badge>
      case "locked":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Verrouillé</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>
    }
  }

  // Formater la date
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date)
    } catch (e) {
      return dateString
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Gestion des Wallets</h2>
          <p className="text-muted-foreground">Gérez les wallets de la plateforme et leurs permissions</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowBackupKeysDialog(true)}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            <span>Sauvegarder les clés</span>
          </Button>
          <Button
            variant="outline"
            onClick={exportWallets}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            <span>Exporter</span>
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowImportDialog(true)}
            className="flex items-center gap-2"
          >
            <Upload className="h-4 w-4" />
            <span>Importer</span>
          </Button>
          <Button
            variant="outline"
            onClick={refreshBalances}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            {isRefreshing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Mise à jour...</span>
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4" />
                <span>Rafraîchir les soldes</span>
              </>
            )}
          </Button>
          <Button
            onClick={() => setShowAddWalletDialog(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            <span>Ajouter un wallet</span>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Wallets</CardTitle>
          <CardDescription>
            Gérez les wallets de la plateforme et surveillez leurs soldes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <Input
              placeholder="Rechercher par nom ou adresse..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-[300px]"
            />
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all">Tous</TabsTrigger>
                <TabsTrigger value="admin">Admin</TabsTrigger>
                <TabsTrigger value="treasury">Trésorerie</TabsTrigger>
                <TabsTrigger value="solana">Solana</TabsTrigger>
                <TabsTrigger value="bnb">BNB Chain</TabsTrigger>
                <TabsTrigger value="ethereum">Ethereum</TabsTrigger>
                <TabsTrigger value="locked">\

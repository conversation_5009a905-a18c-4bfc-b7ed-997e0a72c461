"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { AlertCircle, Loader2, Info } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import TokenSuffixService from "@/lib/token-suffix-service"
import KeypairManager from "@/lib/keypair-manager"
import { useNetwork } from "@/contexts/network-context"

export default function KeypairGenerator() {
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()

  const [suffix, setSuffix] = useState("")
  const [count, setCount] = useState(10)
  const [isGenerating, setIsGenerating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [generatedAddresses, setGeneratedAddresses] = useState<string[]>([])
  const [keypairStats, setKeypairStats] = useState<any>(null)

  // Load the current suffix and keypair stats
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load suffix
        const currentSuffix = await TokenSuffixService.getSuffixForNetwork(activeNetwork.id)
        setSuffix(currentSuffix)

        // Load keypair stats
        const stats = KeypairManager.getKeypairStats()
        setKeypairStats(stats)
      } catch (error) {
        console.error("Error loading data:", error)
      }
    }

    loadData()
  }, [activeNetwork.id])

  // Generate keypairs
  const generateKeypairs = async () => {
    if (!suffix) {
      toast({
        title: "Missing suffix",
        description: "Please enter a suffix",
        variant: "destructive",
      })
      return
    }

    if (count <= 0 || count > 100) {
      toast({
        title: "Invalid count",
        description: "Please enter a count between 1 and 100",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)
    setProgress(0)
    setError(null)
    setCurrentStep("Initializing...")
    setGeneratedAddresses([])

    try {
      // Estimate time
      const estimatedSeconds = TokenSuffixService.estimateGrindingTime(suffix) * count
      const estimatedTime = TokenSuffixService.formatEstimatedTime(estimatedSeconds)
      setCurrentStep(`Generating ${count} keypairs with suffix "${suffix}" (estimated time: ${estimatedTime})...`)

      // Generate keypairs
      const addresses = await KeypairManager.preGenerateKeypairs(suffix, activeNetwork.id, count)

      // Update stats
      const stats = KeypairManager.getKeypairStats()
      setKeypairStats(stats)

      // Set generated addresses
      setGeneratedAddresses(addresses)

      toast({
        title: "Keypairs generated",
        description: `Successfully generated ${addresses.length} keypairs with suffix "${suffix}"`,
      })
    } catch (error: any) {
      console.error("Error generating keypairs:", error)
      setError(error.message || "An error occurred while generating keypairs")

      toast({
        title: "Generation failed",
        description: error.message || "An error occurred while generating keypairs",
        variant: "destructive",
      })
    } finally {
      setIsGenerating(false)
      setProgress(100)
      setCurrentStep("Generation complete")
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Pre-generate Token Addresses</CardTitle>
        <CardDescription>Generate keypairs with the configured suffix for faster token creation</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>About Pre-generated Keypairs</AlertTitle>
          <AlertDescription>
            Pre-generating keypairs with a specific suffix allows for faster token creation. Users will be able to
            create tokens instantly without waiting for address generation.
          </AlertDescription>
        </Alert>

        {keypairStats && (
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Current Keypair Stats</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-gray-100 p-3 rounded-md">
                <div className="text-sm text-gray-500">Total</div>
                <div className="text-xl font-bold">{keypairStats.total}</div>
              </div>
              <div className="bg-gray-100 p-3 rounded-md">
                <div className="text-sm text-gray-500">Available</div>
                <div className="text-xl font-bold">{keypairStats.available}</div>
              </div>
              <div className="bg-gray-100 p-3 rounded-md">
                <div className="text-sm text-gray-500">Used</div>
                <div className="text-xl font-bold">{keypairStats.used}</div>
              </div>
            </div>

            {Object.entries(keypairStats.bySuffix).length > 0 && (
              <div className="mt-4">
                <h4 className="text-md font-medium mb-2">By Suffix</h4>
                <div className="space-y-2">
                  {Object.entries(keypairStats.bySuffix).map(([key, stats]: [string, any]) => (
                    <div key={key} className="flex justify-between items-center bg-gray-50 p-2 rounded-md">
                      <span className="font-medium">{key}</span>
                      <span>
                        {stats.available} available / {stats.total} total
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="suffix">Token Suffix</Label>
            <Input
              id="suffix"
              value={suffix}
              onChange={(e) => setSuffix(e.target.value.toUpperCase())}
              placeholder="GF"
              maxLength={5}
              disabled={isGenerating}
            />
            <p className="text-xs text-muted-foreground">The suffix that will be used for all token addresses</p>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="count">Number of Keypairs</Label>
            <Input
              id="count"
              type="number"
              value={count}
              onChange={(e) => setCount(Number.parseInt(e.target.value))}
              min="1"
              max="100"
              disabled={isGenerating}
            />
            <p className="text-xs text-muted-foreground">How many keypairs to generate (1-100)</p>
          </div>

          {isGenerating && (
            <div className="space-y-2">
              <div className="text-center">
                <h3 className="text-lg font-medium">{currentStep}</h3>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {generatedAddresses.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Generated Addresses</h3>
              <div className="max-h-60 overflow-y-auto border rounded-md p-2">
                {generatedAddresses.map((address, index) => (
                  <div key={index} className="text-xs font-mono mb-1">
                    {index + 1}. {address}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={generateKeypairs} disabled={isGenerating} className="w-full">
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            "Generate Keypairs"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

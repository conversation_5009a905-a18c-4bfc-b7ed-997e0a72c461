"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle, ExternalLink, RefreshCw, Shield, Loader2 } from "lucide-react"

// Types pour les intégrations API
interface ApiIntegration {
  id: string
  name: string
  description: string
  enabled: boolean
  apiKey: string
  apiSecret?: string
  apiEndpoint: string
  category: "market" | "blockchain" | "storage" | "ai" | "payment" | "other"
  status: "connected" | "disconnected" | "error" | "pending"
  lastChecked?: string
  requiredForModule?: string[]
}

export default function ApiIntegrationsManager() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [isTesting, setIsTesting] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("all")
  const [apiIntegrations, setApiIntegrations] = useState<ApiIntegration[]>([])
  const [editingIntegration, setEditingIntegration] = useState<ApiIntegration | null>(null)
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({})

  // Charger les intégrations API
  useEffect(() => {
    fetchApiIntegrations()
  }, [])

  // Récupérer les intégrations API
  const fetchApiIntegrations = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/admin/integrations")

      if (!response.ok) {
        throw new Error("Erreur lors de la récupération des intégrations API")
      }

      const data = await response.json()
      setApiIntegrations(data.integrations || [])
    } catch (error) {
      console.error("Erreur:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de charger les intégrations API",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Tester une connexion API
  const testApiConnection = async (id: string) => {
    setIsTesting(id)
    try {
      const integration = apiIntegrations.find((api) => api.id === id)
      if (!integration) return

      const response = await fetch(`/api/admin/integrations/${id}/test`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          apiKey: integration.apiKey,
          apiSecret: integration.apiSecret,
          apiEndpoint: integration.apiEndpoint,
        }),
      })

      if (!response.ok) {
        throw new Error("Échec du test de connexion")
      }

      const result = await response.json()

      // Mettre à jour le statut de l'intégration
      const updatedIntegrations = apiIntegrations.map((api) =>
        api.id === id
          ? { ...api, status: result.success ? "connected" : "error", lastChecked: new Date().toISOString() }
          : api,
      )
      setApiIntegrations(updatedIntegrations)

      toast({
        title: result.success ? "Connexion réussie" : "Échec de la connexion",
        description: result.message,
        variant: result.success ? "default" : "destructive",
      })
    } catch (error) {
      console.error("Erreur lors du test de connexion:", error)

      // Mettre à jour le statut de l'intégration en cas d'erreur
      const updatedIntegrations = apiIntegrations.map((api) =>
        api.id === id ? { ...api, status: "error", lastChecked: new Date().toISOString() } : api,
      )
      setApiIntegrations(updatedIntegrations)

      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de tester la connexion API",
      })
    } finally {
      setIsTesting(null)
    }
  }

  // Sauvegarder les modifications d'une intégration API
  const saveApiIntegration = async (integration: ApiIntegration) => {
    setIsSaving(integration.id)
    try {
      const response = await fetch(`/api/admin/integrations/${integration.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(integration),
      })

      if (!response.ok) {
        throw new Error("Erreur lors de la sauvegarde de l'intégration API")
      }

      // Mettre à jour la liste des intégrations
      const updatedIntegrations = apiIntegrations.map((api) => (api.id === integration.id ? integration : api))
      setApiIntegrations(updatedIntegrations)

      toast({
        title: "Intégration sauvegardée",
        description: `Les paramètres de ${integration.name} ont été mis à jour avec succès`,
      })

      setEditingIntegration(null)
    } catch (error) {
      console.error("Erreur lors de la sauvegarde de l'intégration:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de sauvegarder l'intégration API",
      })
    } finally {
      setIsSaving(null)
    }
  }

  // Activer/désactiver une intégration API
  const toggleApiIntegration = async (id: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/admin/integrations/${id}/toggle`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ enabled }),
      })

      if (!response.ok) {
        throw new Error("Erreur lors de la modification du statut de l'intégration")
      }

      // Mettre à jour la liste des intégrations
      const updatedIntegrations = apiIntegrations.map((api) => (api.id === id ? { ...api, enabled } : api))
      setApiIntegrations(updatedIntegrations)

      toast({
        title: enabled ? "Intégration activée" : "Intégration désactivée",
        description: `L'intégration a été ${enabled ? "activée" : "désactivée"} avec succès`,
      })
    } catch (error) {
      console.error("Erreur lors de la modification du statut:", error)
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de modifier le statut de l'intégration",
      })
    }
  }

  // Filtrer les intégrations en fonction de l'onglet actif
  const filteredIntegrations =
    activeTab === "all" ? apiIntegrations : apiIntegrations.filter((api) => api.category === activeTab)

  // Basculer l'affichage des secrets
  const toggleShowSecret = (id: string) => {
    setShowSecrets((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  // Obtenir le badge de statut
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "connected":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Connecté</Badge>
      case "disconnected":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Déconnecté</Badge>
      case "error":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Erreur</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">En attente</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="bg-slate-50 dark:bg-slate-800">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl font-bold">Gestionnaire d'Intégrations API</CardTitle>
            <CardDescription>Configurez et gérez les intégrations API externes pour la plateforme</CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={fetchApiIntegrations}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Actualiser
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="all">Toutes</TabsTrigger>
            <TabsTrigger value="market">Marché</TabsTrigger>
            <TabsTrigger value="blockchain">Blockchain</TabsTrigger>
            <TabsTrigger value="storage">Stockage</TabsTrigger>
            <TabsTrigger value="ai">IA</TabsTrigger>
            <TabsTrigger value="payment">Paiement</TabsTrigger>
            <TabsTrigger value="other">Autre</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-0">
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center justify-between p-4 border rounded-md">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-40" />
                      <Skeleton className="h-3 w-60" />
                    </div>
                    <div className="flex space-x-2">
                      <Skeleton className="h-9 w-9 rounded-md" />
                      <Skeleton className="h-9 w-9 rounded-md" />
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredIntegrations.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Aucune intégration API trouvée dans cette catégorie</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredIntegrations.map((integration) => (
                  <div key={integration.id} className="border rounded-md overflow-hidden">
                    <div className="flex flex-col md:flex-row md:items-center justify-between p-4 bg-slate-50 dark:bg-slate-800">
                      <div className="space-y-1 mb-2 md:mb-0">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">{integration.name}</h3>
                          {getStatusBadge(integration.status)}
                          {integration.requiredForModule && integration.requiredForModule.length > 0 && (
                            <Badge variant="outline">Requis pour: {integration.requiredForModule.join(", ")}</Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{integration.description}</p>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={integration.enabled}
                          onCheckedChange={(checked) => toggleApiIntegration(integration.id, checked)}
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => testApiConnection(integration.id)}
                          disabled={isTesting === integration.id || !integration.enabled}
                        >
                          {isTesting === integration.id ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Test...
                            </>
                          ) : (
                            "Tester"
                          )}
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => setEditingIntegration(integration)}>
                          Configurer
                        </Button>
                      </div>
                    </div>

                    {editingIntegration?.id === integration.id && (
                      <div className="p-4 border-t">
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor={`${integration.id}-apiKey`}>Clé API</Label>
                              <div className="flex">
                                <Input
                                  id={`${integration.id}-apiKey`}
                                  value={editingIntegration.apiKey}
                                  onChange={(e) =>
                                    setEditingIntegration({
                                      ...editingIntegration,
                                      apiKey: e.target.value,
                                    })
                                  }
                                  type={showSecrets[integration.id] ? "text" : "password"}
                                  className="flex-1"
                                />
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleShowSecret(integration.id)}
                                  className="ml-2"
                                >
                                  {showSecrets[integration.id] ? "Masquer" : "Afficher"}
                                </Button>
                              </div>
                            </div>

                            {integration.apiSecret !== undefined && (
                              <div className="space-y-2">
                                <Label htmlFor={`${integration.id}-apiSecret`}>Secret API</Label>
                                <div className="flex">
                                  <Input
                                    id={`${integration.id}-apiSecret`}
                                    value={editingIntegration.apiSecret || ""}
                                    onChange={(e) =>
                                      setEditingIntegration({
                                        ...editingIntegration,
                                        apiSecret: e.target.value,
                                      })
                                    }
                                    type={showSecrets[integration.id] ? "text" : "password"}
                                    className="flex-1"
                                  />
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleShowSecret(integration.id)}
                                    className="ml-2"
                                  >
                                    {showSecrets[integration.id] ? "Masquer" : "Afficher"}
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`${integration.id}-apiEndpoint`}>Point de terminaison API</Label>
                            <Input
                              id={`${integration.id}-apiEndpoint`}
                              value={editingIntegration.apiEndpoint}
                              onChange={(e) =>
                                setEditingIntegration({
                                  ...editingIntegration,
                                  apiEndpoint: e.target.value,
                                })
                              }
                            />
                          </div>

                          {integration.id === "coingecko" && (
                            <Alert>
                              <ExternalLink className="h-4 w-4" />
                              <AlertTitle>Information</AlertTitle>
                              <AlertDescription>
                                Vous pouvez obtenir une clé API CoinGecko en vous inscrivant sur{" "}
                                <a
                                  href="https://www.coingecko.com/en/api/pricing"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  CoinGecko API
                                </a>
                              </AlertDescription>
                            </Alert>
                          )}

                          {integration.id === "coinmarketcap" && (
                            <Alert>
                              <ExternalLink className="h-4 w-4" />
                              <AlertTitle>Information</AlertTitle>
                              <AlertDescription>
                                Vous pouvez obtenir une clé API CoinMarketCap en vous inscrivant sur{" "}
                                <a
                                  href="https://coinmarketcap.com/api/"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  CoinMarketCap API
                                </a>
                              </AlertDescription>
                            </Alert>
                          )}

                          {integration.id === "openai" && (
                            <Alert>
                              <ExternalLink className="h-4 w-4" />
                              <AlertTitle>Information</AlertTitle>
                              <AlertDescription>
                                Vous pouvez obtenir une clé API OpenAI en vous inscrivant sur{" "}
                                <a
                                  href="https://platform.openai.com/api-keys"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  OpenAI API
                                </a>
                              </AlertDescription>
                            </Alert>
                          )}

                          {integration.id === "filebase" && (
                            <Alert>
                              <ExternalLink className="h-4 w-4" />
                              <AlertTitle>Information</AlertTitle>
                              <AlertDescription>
                                Vous pouvez obtenir une clé API Filebase en vous inscrivant sur{" "}
                                <a
                                  href="https://filebase.com/"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  Filebase
                                </a>
                              </AlertDescription>
                            </Alert>
                          )}

                          {integration.status === "error" && (
                            <Alert variant="destructive">
                              <AlertCircle className="h-4 w-4" />
                              <AlertTitle>Erreur de connexion</AlertTitle>
                              <AlertDescription>
                                La dernière tentative de connexion a échoué. Veuillez vérifier vos paramètres et
                                réessayer.
                              </AlertDescription>
                            </Alert>
                          )}

                          <div className="flex justify-end space-x-2">
                            <Button variant="outline" onClick={() => setEditingIntegration(null)}>
                              Annuler
                            </Button>
                            <Button
                              onClick={() => saveApiIntegration(editingIntegration)}
                              disabled={isSaving === integration.id}
                            >
                              {isSaving === integration.id ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Sauvegarde...
                                </>
                              ) : (
                                "Sauvegarder"
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}

                    {integration.lastChecked && (
                      <div className="px-4 py-2 text-xs text-muted-foreground border-t">
                        Dernière vérification: {new Date(integration.lastChecked).toLocaleString()}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      <CardFooter className="bg-slate-50 dark:bg-slate-800 border-t">
        <div className="flex items-center space-x-2">
          <Shield className="h-4 w-4 text-muted-foreground" />
          <p className="text-xs text-muted-foreground">
            Les clés API sont stockées de manière sécurisée et chiffrées dans la base de données.
          </p>
        </div>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Plus, Trash2 } from "lucide-react"

interface AdminUser {
  id: string
  wallet: string
  role: string
  addedAt: string
}

export function AdminManagement() {
  const [admins, setAdmins] = useState<AdminUser[]>([])
  const [newAdminWallet, setNewAdminWallet] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    const fetchAdmins = async () => {
      try {
        // Simuler un chargement de données
        setTimeout(() => {
          setAdmins([
            {
              id: "1",
              wallet: "GfEHHkrQY45ZPoKC9srbjKZiCnwEWNVTAn3UxTGTZgwT",
              role: "Super Admin",
              addedAt: "2023-04-15",
            },
            {
              id: "2",
              wallet: "BXvEuaBKzLJtZQxwKWgmUEJWAM9JYiVEYBT8HPZjmXbU",
              role: "Admin",
              addedAt: "2023-05-22",
            },
            {
              id: "3",
              wallet: process.env.ADMIN_WALLET || "5YNmS1R9nNSCDzb5a7mMJ1dwK9uHeAAF4CerVnZgbdr3",
              role: "Super Admin",
              addedAt: "2023-03-10",
            },
          ])
          setIsLoading(false)
        }, 1000)
      } catch (error) {
        console.error("Error fetching admins:", error)
        toast({
          title: "Error",
          description: "Failed to load admin users. Please try again.",
          variant: "destructive",
        })
        setIsLoading(false)
      }
    }

    fetchAdmins()
  }, [toast])

  const handleAddAdmin = async () => {
    if (!newAdminWallet || newAdminWallet.trim() === "") {
      toast({
        title: "Error",
        description: "Please enter a valid wallet address.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Simuler l'ajout d'un admin
      setTimeout(() => {
        const newAdmin: AdminUser = {
          id: (admins.length + 1).toString(),
          wallet: newAdminWallet,
          role: "Admin",
          addedAt: new Date().toISOString().split("T")[0],
        }

        setAdmins([...admins, newAdmin])
        setNewAdminWallet("")
        setIsSubmitting(false)

        toast({
          title: "Success",
          description: "Admin user added successfully.",
        })
      }, 1000)
    } catch (error) {
      console.error("Error adding admin:", error)
      toast({
        title: "Error",
        description: "Failed to add admin user. Please try again.",
        variant: "destructive",
      })
      setIsSubmitting(false)
    }
  }

  const handleRemoveAdmin = async (id: string) => {
    try {
      // Simuler la suppression d'un admin
      setTimeout(() => {
        setAdmins(admins.filter((admin) => admin.id !== id))
        toast({
          title: "Success",
          description: "Admin user removed successfully.",
        })
      }, 500)
    } catch (error) {
      console.error("Error removing admin:", error)
      toast({
        title: "Error",
        description: "Failed to remove admin user. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Admin Management</CardTitle>
        <CardDescription>Add or remove admin users from the platform</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Input
              placeholder="Enter wallet address"
              value={newAdminWallet}
              onChange={(e) => setNewAdminWallet(e.target.value)}
              disabled={isSubmitting}
            />
            <Button onClick={handleAddAdmin} disabled={isSubmitting}>
              {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Plus className="mr-2 h-4 w-4" />}
              Add Admin
            </Button>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Wallet Address</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Added On</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {admins.map((admin) => (
                  <TableRow key={admin.id}>
                    <TableCell className="font-mono text-xs">{admin.wallet}</TableCell>
                    <TableCell>{admin.role}</TableCell>
                    <TableCell>{admin.addedAt}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveAdmin(admin.id)}
                        disabled={admin.role === "Super Admin"}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </CardContent>
      <CardFooter className="text-sm text-muted-foreground">
        Super Admin users cannot be removed through this interface.
      </CardFooter>
    </Card>
  )
}

"use client"

import { CardDescription } from "@/components/ui/card"

// components/admin/token-reports.tsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FileText, Download } from "lucide-react"

export default function TokenReports() {
  const handleDownloadReport = (type: string) => {
    // Simulate report download
    alert(`Downloading ${type} token report...`)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="mr-2 h-5 w-5" />
          Token Reports
        </CardTitle>
        <CardDescription>Generate and download reports for tokens</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Normal Tokens</h3>
          <Button variant="outline" onClick={() => handleDownloadReport("normal")} className="w-full">
            <Download className="mr-2 h-4 w-4" />
            Download Report
          </Button>
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-medium">Quantum Tokens</h3>
          <Button variant="outline" onClick={() => handleDownloadReport("quantum")} className="w-full">
            <Download className="mr-2 h-4 w-4" />
            Download Report
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

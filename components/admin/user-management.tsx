"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2 } from "lucide-react"
import { Users } from "lucide-react"

export default function UserManagement() {
  const [adminWallets, setAdminWallets] = useState<string[]>([])
  const [blacklistedWallets, setBlacklistedWallets] = useState<string[]>([])
  const [newAdminWallet, setNewAdminWallet] = useState("")
  const [newBlacklistedWallet, setNewBlacklistedWallet] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleAddAdminWallet = async () => {
    if (!newAdminWallet) return
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulate adding admin wallet with a delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setAdminWallets([...adminWallets, newAdminWallet])
      setNewAdminWallet("")
      setSuccess("Admin wallet added successfully!")
    } catch (err) {
      console.error("Error adding admin wallet:", err)
      setError("Failed to add admin wallet. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddBlacklistedWallet = async () => {
    if (!newBlacklistedWallet) return
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulate adding blacklisted wallet with a delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setBlacklistedWallets([...blacklistedWallets, newBlacklistedWallet])
      setNewBlacklistedWallet("")
      setSuccess("Blacklisted wallet added successfully!")
    } catch (err) {
      console.error("Error adding blacklisted wallet:", err)
      setError("Failed to add blacklisted wallet. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5" />
          User Management
        </CardTitle>
        <CardDescription>Manage admin and blacklisted wallets</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Admin Wallets</h3>
          <div className="space-y-2">
            <Label htmlFor="newAdminWallet">Add Admin Wallet</Label>
            <div className="flex space-x-2">
              <Input
                id="newAdminWallet"
                placeholder="Enter wallet address"
                value={newAdminWallet}
                onChange={(e) => setNewAdminWallet(e.target.value)}
              />
              <Button onClick={handleAddAdminWallet} disabled={isLoading}>
                Add
              </Button>
            </div>
          </div>
          <Textarea
            readOnly
            className="min-h-[100px] bg-muted"
            value={adminWallets.join("\n")}
            placeholder="No admin wallets added yet"
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Blacklisted Wallets</h3>
          <div className="space-y-2">
            <Label htmlFor="newBlacklistedWallet">Add Blacklisted Wallet</Label>
            <div className="flex space-x-2">
              <Input
                id="newBlacklistedWallet"
                placeholder="Enter wallet address"
                value={newBlacklistedWallet}
                onChange={(e) => setNewBlacklistedWallet(e.target.value)}
              />
              <Button onClick={handleAddBlacklistedWallet} disabled={isLoading}>
                Add
              </Button>
            </div>
          </div>
          <Textarea
            readOnly
            className="min-h-[100px] bg-muted"
            value={blacklistedWallets.join("\n")}
            placeholder="No blacklisted wallets added yet"
          />
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Bell, Check, Clock, Info, Loader2, MoreHorizontal, Set<PERSON>s, Trash2, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/components/ui/use-toast"

interface Notification {
  id: string
  title: string
  message: string
  type: "info" | "warning" | "success" | "error"
  timestamp: string
  read: boolean
}

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [isOpen, setIsOpen] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchNotifications()
  }, [])

  useEffect(() => {
    // Mettre à jour le compteur de notifications non lues
    setUnreadCount(notifications.filter((n) => !n.read).length)
  }, [notifications])

  const fetchNotifications = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API
      setTimeout(() => {
        // Données fictives pour la démonstration
        const mockNotifications: Notification[] = [
          {
            id: "notif1",
            title: "Nouvelle tentative de connexion",
            message: "Une tentative de connexion a été détectée depuis une nouvelle adresse IP",
            type: "warning",
            timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString(),
            read: false,
          },
          {
            id: "notif2",
            title: "Mise à jour système",
            message: "La plateforme a été mise à jour vers la version 2.5.0",
            type: "info",
            timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
            read: false,
          },
          {
            id: "notif3",
            title: "Nouveau token créé",
            message: "Le token SOLX a été créé avec succès",
            type: "success",
            timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
            read: true,
          },
          {
            id: "notif4",
            title: "Erreur de transaction",
            message: "Une transaction a échoué en raison d'un problème de réseau",
            type: "error",
            timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),
            read: true,
          },
          {
            id: "notif5",
            title: "Nouveau rapport disponible",
            message: "Le rapport mensuel d'activité est maintenant disponible",
            type: "info",
            timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString(),
            read: true,
          },
        ]

        setNotifications(mockNotifications)
        setIsLoading(false)
      }, 800)
    } catch (error) {
      console.error("Error fetching notifications:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les notifications",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const markAsRead = (id: string) => {
    setNotifications(notifications.map((notif) => (notif.id === id ? { ...notif, read: true } : notif)))
  }

  const markAllAsRead = () => {
    setNotifications(notifications.map((notif) => ({ ...notif, read: true })))
    toast({
      title: "Notifications",
      description: "Toutes les notifications ont été marquées comme lues",
    })
  }

  const deleteNotification = (id: string) => {
    setNotifications(notifications.filter((notif) => notif.id !== id))
    toast({
      title: "Notification supprimée",
      description: "La notification a été supprimée avec succès",
    })
  }

  const clearAllNotifications = () => {
    setNotifications([])
    toast({
      title: "Notifications effacées",
      description: "Toutes les notifications ont été supprimées",
    })
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const date = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return "À l'instant"
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `Il y a ${diffInHours}h`

    const diffInDays = Math.floor(diffInHours / 24)
    return `Il y a ${diffInDays}j`
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "info":
        return <Info className="h-4 w-4 text-blue-500" />
      case "warning":
        return <Info className="h-4 w-4 text-yellow-500" />
      case "success":
        return <Check className="h-4 w-4 text-green-500" />
      case "error":
        return <X className="h-4 w-4 text-red-500" />
      default:
        return <Info className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 px-1 min-w-[18px] h-[18px] flex items-center justify-center bg-red-500 text-white">
              {unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <Card className="border-0">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle>Notifications</CardTitle>
              <div className="flex items-center gap-1">
                <Button variant="ghost" size="icon" onClick={fetchNotifications}>
                  <Loader2 className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Options</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={markAllAsRead}>
                      <Check className="h-4 w-4 mr-2" />
                      Tout marquer comme lu
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={clearAllNotifications}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Effacer toutes les notifications
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Settings className="h-4 w-4 mr-2" />
                      Paramètres de notification
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <CardDescription>
              {unreadCount > 0
                ? `Vous avez ${unreadCount} notification${unreadCount > 1 ? "s" : ""} non lue${unreadCount > 1 ? "s" : ""}`
                : "Aucune nouvelle notification"}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs defaultValue="all">
              <div className="border-b px-4">
                <TabsList className="bg-transparent">
                  <TabsTrigger value="all" className="data-[state=active]:bg-muted">
                    Toutes
                  </TabsTrigger>
                  <TabsTrigger value="unread" className="data-[state=active]:bg-muted">
                    Non lues
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="all" className="max-h-[300px] overflow-y-auto">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : notifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">Aucune notification</h3>
                    <p className="text-muted-foreground mt-2">Vous n'avez aucune notification pour le moment</p>
                  </div>
                ) : (
                  <div className="divide-y">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 hover:bg-muted/50 ${notification.read ? "" : "bg-muted/20"}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3">
                            <div className="mt-0.5">{getNotificationIcon(notification.type)}</div>
                            <div>
                              <h4 className="text-sm font-medium">{notification.title}</h4>
                              <p className="text-xs text-muted-foreground mt-1">{notification.message}</p>
                              <div className="flex items-center text-xs text-muted-foreground mt-2">
                                <Clock className="h-3 w-3 mr-1" />
                                <span>{formatTimeAgo(notification.timestamp)}</span>
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {!notification.read && (
                                <DropdownMenuItem onClick={() => markAsRead(notification.id)}>
                                  <Check className="h-4 w-4 mr-2" />
                                  Marquer comme lu
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem onClick={() => deleteNotification(notification.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                Supprimer
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="unread" className="max-h-[300px] overflow-y-auto">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : notifications.filter((n) => !n.read).length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Check className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">Tout est lu</h3>
                    <p className="text-muted-foreground mt-2">Vous avez lu toutes vos notifications</p>
                  </div>
                ) : (
                  <div className="divide-y">
                    {notifications
                      .filter((n) => !n.read)
                      .map((notification) => (
                        <div key={notification.id} className="p-4 hover:bg-muted/50 bg-muted/20">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3">
                              <div className="mt-0.5">{getNotificationIcon(notification.type)}</div>
                              <div>
                                <h4 className="text-sm font-medium">{notification.title}</h4>
                                <p className="text-xs text-muted-foreground mt-1">{notification.message}</p>
                                <div className="flex items-center text-xs text-muted-foreground mt-2">
                                  <Clock className="h-3 w-3 mr-1" />
                                  <span>{formatTimeAgo(notification.timestamp)}</span>
                                </div>
                              </div>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => markAsRead(notification.id)}>
                                  <Check className="h-4 w-4 mr-2" />
                                  Marquer comme lu
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => deleteNotification(notification.id)}>
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Supprimer
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between border-t p-4">
            <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
              Fermer
            </Button>
            <Button variant="outline" size="sm">
              Voir toutes les notifications
            </Button>
          </CardFooter>
        </Card>
      </PopoverContent>
    </Popover>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { fr } from "date-fns/locale"
import {
  AlertTriangle,
  ArrowDown,
  ArrowUp,
  Download,
  Eye,
  Loader2,
  RefreshCw,
  Search,
  Shield,
  User,
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface AuditLogEntry {
  id: string
  timestamp: string
  adminWallet: string
  adminRole: "admin" | "superadmin"
  action: string
  category: "user" | "token" | "system" | "security" | "network"
  details: string
  ipAddress: string
  status: "success" | "failure" | "warning"
  targetId?: string
  targetType?: string
}

export function AdminAuditLog() {
  const [logs, setLogs] = useState<AuditLogEntry[]>([])
  const [filteredLogs, setFilteredLogs] = useState<AuditLogEntry[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedLog, setSelectedLog] = useState<AuditLogEntry | null>(null)
  const [isLogDetailsOpen, setIsLogDetailsOpen] = useState(false)
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>(undefined)
  const [categoryFilter, setCategoryFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [sortConfig, setSortConfig] = useState<{ key: keyof AuditLogEntry; direction: "asc" | "desc" }>({
    key: "timestamp",
    direction: "desc",
  })
  const { toast } = useToast()

  useEffect(() => {
    fetchLogs()
  }, [])

  useEffect(() => {
    if (logs.length > 0) {
      filterLogs()
    }
  }, [searchQuery, logs, categoryFilter, statusFilter, dateRange])

  const fetchLogs = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API
      setTimeout(() => {
        // Données fictives pour la démonstration
        const mockLogs: AuditLogEntry[] = Array.from({ length: 50 }, (_, i) => {
          const categories = ["user", "token", "system", "security", "network"]
          const category = categories[Math.floor(Math.random() * categories.length)] as AuditLogEntry["category"]

          const actions = {
            user: ["create", "update", "delete", "ban", "unban"],
            token: ["create", "update", "delete", "transfer", "mint"],
            system: ["config_update", "restart", "maintenance", "backup"],
            security: ["login", "logout", "permission_change", "role_update"],
            network: ["switch_network", "rpc_update", "node_status_change"],
          }

          const action = actions[category][Math.floor(Math.random() * actions[category].length)]

          const statuses = ["success", "failure", "warning"]
          const status = statuses[Math.floor(Math.random() * statuses.length)] as AuditLogEntry["status"]

          const adminRoles = ["admin", "superadmin"]
          const adminRole = adminRoles[Math.floor(Math.random() * adminRoles.length)] as AuditLogEntry["adminRole"]

          return {
            id: `log-${i + 1}`,
            timestamp: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
            adminWallet: `${generateRandomWalletAddress()}`,
            adminRole,
            action,
            category,
            details: `${action.charAt(0).toUpperCase() + action.slice(1)} operation performed on ${category}`,
            ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
            status,
            targetId: category !== "system" ? `${category}-${Math.floor(Math.random() * 1000)}` : undefined,
            targetType: category !== "system" ? category : undefined,
          }
        })

        // Trier par timestamp décroissant
        mockLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

        setLogs(mockLogs)
        setFilteredLogs(mockLogs)
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error("Error fetching audit logs:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les journaux d'audit",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const generateRandomWalletAddress = () => {
    const chars = "**********************************************************"
    let result = ""
    for (let i = 0; i < 44; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  const filterLogs = () => {
    let filtered = [...logs]

    // Filtre par recherche
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (log) =>
          log.adminWallet.toLowerCase().includes(query) ||
          log.action.toLowerCase().includes(query) ||
          log.details.toLowerCase().includes(query) ||
          (log.targetId && log.targetId.toLowerCase().includes(query)),
      )
    }

    // Filtre par catégorie
    if (categoryFilter !== "all") {
      filtered = filtered.filter((log) => log.category === categoryFilter)
    }

    // Filtre par statut
    if (statusFilter !== "all") {
      filtered = filtered.filter((log) => log.status === statusFilter)
    }

    // Filtre par date
    if (dateRange && dateRange.from && dateRange.to) {
      const fromTime = dateRange.from.getTime()
      const toTime = dateRange.to.getTime()
      filtered = filtered.filter((log) => {
        const logTime = new Date(log.timestamp).getTime()
        return logTime >= fromTime && logTime <= toTime
      })
    }

    // Tri
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]

      if (aValue < bValue) return sortConfig.direction === "asc" ? -1 : 1
      if (aValue > bValue) return sortConfig.direction === "asc" ? 1 : -1
      return 0
    })

    setFilteredLogs(filtered)
  }

  const handleSort = (key: keyof AuditLogEntry) => {
    const direction = sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc"
    setSortConfig({ key, direction })

    const sortedLogs = [...filteredLogs].sort((a, b) => {
      const aValue = a[key]
      const bValue = b[key]

      if (aValue < bValue) return direction === "asc" ? -1 : 1
      if (aValue > bValue) return direction === "asc" ? 1 : -1
      return 0
    })

    setFilteredLogs(sortedLogs)
  }

  const handleViewLogDetails = (log: AuditLogEntry) => {
    setSelectedLog(log)
    setIsLogDetailsOpen(true)
  }

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    }).format(date)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return <Badge className="bg-green-500">Succès</Badge>
      case "failure":
        return <Badge variant="destructive">Échec</Badge>
      case "warning":
        return <Badge className="bg-yellow-500">Avertissement</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getCategoryBadge = (category: string) => {
    switch (category) {
      case "user":
        return <Badge className="bg-blue-500">Utilisateur</Badge>
      case "token":
        return <Badge className="bg-purple-500">Token</Badge>
      case "system":
        return <Badge className="bg-gray-500">Système</Badge>
      case "security":
        return <Badge className="bg-red-500">Sécurité</Badge>
      case "network":
        return <Badge className="bg-cyan-500">Réseau</Badge>
      default:
        return <Badge variant="outline">{category}</Badge>
    }
  }

  const exportLogs = () => {
    // Convertir les logs filtrés en CSV
    const headers = [
      "ID",
      "Date",
      "Admin",
      "Rôle",
      "Action",
      "Catégorie",
      "Détails",
      "IP",
      "Statut",
      "Cible ID",
      "Type de cible",
    ]
    const csvRows = [headers]

    filteredLogs.forEach((log) => {
      csvRows.push([
        log.id,
        formatDate(log.timestamp),
        log.adminWallet,
        log.adminRole,
        log.action,
        log.category,
        log.details,
        log.ipAddress,
        log.status,
        log.targetId || "",
        log.targetType || "",
      ])
    })

    const csvContent = csvRows.map((row) => row.join(",")).join("\n")
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)

    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `audit_logs_${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "Export réussi",
      description: `${filteredLogs.length} entrées de journal exportées au format CSV.`,
    })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Journal d'audit administrateur</CardTitle>
          <CardDescription>Consultez l'historique des actions effectuées par les administrateurs</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <div className="relative w-full sm:w-auto">
                  <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8 w-full sm:w-[250px]"
                  />
                </div>
                <Button variant="outline" size="icon" onClick={fetchLogs}>
                  <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
                </Button>
                <Button variant="outline" size="icon" onClick={exportLogs}>
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
                <DateRangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  locale={fr}
                  calendarTodayClassName="bg-primary text-primary-foreground"
                  align="end"
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex items-center gap-2">
                <Label htmlFor="category-filter" className="whitespace-nowrap">
                  Catégorie:
                </Label>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger id="category-filter" className="w-[180px]">
                    <SelectValue placeholder="Toutes les catégories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les catégories</SelectItem>
                    <SelectItem value="user">Utilisateur</SelectItem>
                    <SelectItem value="token">Token</SelectItem>
                    <SelectItem value="system">Système</SelectItem>
                    <SelectItem value="security">Sécurité</SelectItem>
                    <SelectItem value="network">Réseau</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <Label htmlFor="status-filter" className="whitespace-nowrap">
                  Statut:
                </Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger id="status-filter" className="w-[180px]">
                    <SelectValue placeholder="Tous les statuts" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les statuts</SelectItem>
                    <SelectItem value="success">Succès</SelectItem>
                    <SelectItem value="failure">Échec</SelectItem>
                    <SelectItem value="warning">Avertissement</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[180px]">
                      <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort("timestamp")}>
                        Date
                        {sortConfig.key === "timestamp" ? (
                          sortConfig.direction === "asc" ? (
                            <ArrowUp className="ml-2 h-4 w-4" />
                          ) : (
                            <ArrowDown className="ml-2 h-4 w-4" />
                          )
                        ) : (
                          <ArrowDown className="ml-2 h-4 w-4 opacity-30" />
                        )}
                      </Button>
                    </TableHead>
                    <TableHead>Admin</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Catégorie</TableHead>
                    <TableHead>Détails</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                      </TableCell>
                    </TableRow>
                  ) : filteredLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center">
                          <AlertTriangle className="h-8 w-8 text-yellow-500 mb-2" />
                          <p>Aucune entrée de journal trouvée</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>{formatDate(log.timestamp)}</TableCell>
                        <TableCell className="font-mono">
                          <div className="flex items-center gap-1">
                            {log.adminRole === "superadmin" ? (
                              <Shield className="h-3 w-3 text-purple-500" />
                            ) : (
                              <User className="h-3 w-3 text-blue-500" />
                            )}
                            {truncateAddress(log.adminWallet)}
                          </div>
                        </TableCell>
                        <TableCell className="capitalize">{log.action.replace("_", " ")}</TableCell>
                        <TableCell>{getCategoryBadge(log.category)}</TableCell>
                        <TableCell className="max-w-[200px] truncate">{log.details}</TableCell>
                        <TableCell>{getStatusBadge(log.status)}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm" onClick={() => handleViewLogDetails(log)}>
                            <Eye className="h-4 w-4 mr-1" />
                            Détails
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Affichage de {filteredLogs.length} entrées sur {logs.length}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              Précédent
            </Button>
            <Button variant="outline" size="sm">
              1
            </Button>
            <Button variant="outline" size="sm">
              2
            </Button>
            <Button variant="outline" size="sm">
              3
            </Button>
            <Button variant="outline" size="sm">
              Suivant
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Dialogue de détails du log */}
      <Dialog open={isLogDetailsOpen} onOpenChange={setIsLogDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Détails de l'entrée de journal</DialogTitle>
            <DialogDescription>Informations détaillées sur l'action enregistrée</DialogDescription>
          </DialogHeader>
          {selectedLog && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">ID</Label>
                <div className="col-span-3">
                  <code className="rounded bg-muted px-2 py-1 font-mono text-sm">{selectedLog.id}</code>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Date</Label>
                <div className="col-span-3">{formatDate(selectedLog.timestamp)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Admin</Label>
                <div className="col-span-3 flex items-center gap-2">
                  <code className="rounded bg-muted px-2 py-1 font-mono text-sm">{selectedLog.adminWallet}</code>
                  <Badge variant="outline">{selectedLog.adminRole === "superadmin" ? "Super Admin" : "Admin"}</Badge>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Action</Label>
                <div className="col-span-3 capitalize">{selectedLog.action.replace("_", " ")}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Catégorie</Label>
                <div className="col-span-3">{getCategoryBadge(selectedLog.category)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Détails</Label>
                <div className="col-span-3">{selectedLog.details}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Adresse IP</Label>
                <div className="col-span-3">{selectedLog.ipAddress}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Statut</Label>
                <div className="col-span-3">{getStatusBadge(selectedLog.status)}</div>
              </div>
              {selectedLog.targetId && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">ID Cible</Label>
                  <div className="col-span-3">
                    <code className="rounded bg-muted px-2 py-1 font-mono text-sm">{selectedLog.targetId}</code>
                  </div>
                </div>
              )}
              {selectedLog.targetType && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Type de cible</Label>
                  <div className="col-span-3 capitalize">{selectedLog.targetType}</div>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsLogDetailsOpen(false)}>
              Fermer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

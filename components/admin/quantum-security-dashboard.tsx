"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Shield,
  AlertTriangle,
  Info,
  Lock,
  Eye,
  RefreshCw,
  Zap,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  ArrowUpRight,
  Filter,
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function QuantumSecurityDashboard() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("overview")
  const [isRunningTest, setIsRunningTest] = useState(false)
  const [testLogs, setTestLogs] = useState<string[]>([])

  // Paramètres de sécurité
  const [antiBot, setAntiBot] = useState(true)
  const [antiDump, setAntiDump] = useState(true)
  const [autoLiquidity, setAutoLiquidity] = useState(true)
  const [marketingFee, setMarketingFee] = useState(2)
  const [liquidityFee, setLiquidityFee] = useState(3)
  const [maxWallet, setMaxWallet] = useState(2)
  const [maxTransaction, setMaxTransaction] = useState(1)
  const [blacklist, setBlacklist] = useState(true)
  const [pauseTrading, setPauseTrading] = useState(true)
  const [honeypot, setHoneypot] = useState(false)
  const [advancedMonitoring, setAdvancedMonitoring] = useState(true)

  // Données simulées pour les incidents de sécurité
  const securityIncidents = [
    {
      id: "1",
      type: "Tentative de bot",
      token: "SOLQUANTUM",
      timestamp: "2023-11-15 14:23:45",
      address: "0x1a2b3c4d5e6f7g8h9i0j",
      severity: "high",
      status: "blocked",
    },
    {
      id: "2",
      type: "Dump massif",
      token: "MEMEQUANTUM",
      timestamp: "2023-11-14 09:12:33",
      address: "0x9i8h7g6f5e4d3c2b1a0",
      severity: "medium",
      status: "blocked",
    },
    {
      id: "3",
      type: "Tentative de contournement",
      token: "AIQUANTUM",
      timestamp: "2023-11-13 18:45:22",
      address: "0x2b3c4d5e6f7g8h9i0j1a",
      severity: "low",
      status: "investigating",
    },
    {
      id: "4",
      type: "Transaction suspecte",
      token: "GFQUANTUM",
      timestamp: "2023-11-12 11:33:17",
      address: "0x8h9i0j1a2b3c4d5e6f7g",
      severity: "medium",
      status: "resolved",
    },
  ]

  // Données simulées pour les adresses blacklistées
  const blacklistedAddresses = [
    {
      address: "0x1a2b3c4d5e6f7g8h9i0j",
      reason: "Bot trading",
      date: "2023-11-15",
      tokens: ["SOLQUANTUM", "MEMEQUANTUM"],
      status: "active",
    },
    {
      address: "0x9i8h7g6f5e4d3c2b1a0",
      reason: "Dump massif",
      date: "2023-11-14",
      tokens: ["MEMEQUANTUM"],
      status: "active",
    },
    {
      address: "0x2b3c4d5e6f7g8h9i0j1a",
      reason: "Manipulation de marché",
      date: "2023-11-10",
      tokens: ["AIQUANTUM", "GFQUANTUM"],
      status: "active",
    },
    {
      address: "0x8h9i0j1a2b3c4d5e6f7g",
      reason: "Tentative de phishing",
      date: "2023-11-05",
      tokens: ["SOLQUANTUM"],
      status: "expired",
    },
  ]

  // Fonction pour exécuter un test de sécurité
  const runSecurityTest = async () => {
    setIsRunningTest(true)
    setTestLogs([])

    const addLog = (message: string) => {
      setTestLogs((prev) => [...prev, `${new Date().toISOString().split("T")[1].split(".")[0]} - ${message}`])
    }

    addLog("Démarrage du test de sécurité...")

    try {
      // Test de la protection anti-bot
      addLog("Test de la protection anti-bot...")
      await new Promise((resolve) => setTimeout(resolve, 800))
      addLog("✅ Protection anti-bot fonctionnelle")

      // Test de la protection anti-dump
      addLog("Test de la protection anti-dump...")
      await new Promise((resolve) => setTimeout(resolve, 600))
      addLog("✅ Protection anti-dump fonctionnelle")

      // Test du système de blacklist
      addLog("Test du système de blacklist...")
      await new Promise((resolve) => setTimeout(resolve, 700))
      addLog("✅ Système de blacklist fonctionnel")

      // Test des limites de transaction
      addLog("Test des limites de transaction...")
      await new Promise((resolve) => setTimeout(resolve, 500))
      addLog("✅ Limites de transaction fonctionnelles")

      // Test de la pause des transactions
      addLog("Test de la pause des transactions...")
      await new Promise((resolve) => setTimeout(resolve, 600))
      addLog("✅ Fonction de pause des transactions fonctionnelle")

      // Test de la surveillance avancée
      addLog("Test de la surveillance avancée...")
      await new Promise((resolve) => setTimeout(resolve, 900))
      addLog("✅ Système de surveillance avancée fonctionnel")

      addLog("Tests de sécurité terminés avec succès")
    } catch (error: any) {
      addLog(`❌ Erreur pendant les tests: ${error.message}`)
    } finally {
      setIsRunningTest(false)
    }
  }

  // Fonction pour sauvegarder les paramètres
  const saveSecuritySettings = () => {
    toast({
      title: "Paramètres de sécurité sauvegardés",
      description: "Les nouveaux paramètres de sécurité ont été appliqués avec succès.",
    })
  }

  // Fonction pour ajouter une adresse à la blacklist
  const addToBlacklist = (address: string) => {
    toast({
      title: "Adresse ajoutée à la blacklist",
      description: `L'adresse ${address} a été ajoutée à la blacklist avec succès.`,
    })
  }

  // Fonction pour supprimer une adresse de la blacklist
  const removeFromBlacklist = (address: string) => {
    toast({
      title: "Adresse retirée de la blacklist",
      description: `L'adresse ${address} a été retirée de la blacklist avec succès.`,
    })
  }

  // Fonction pour obtenir le badge de sévérité
  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "high":
        return <Badge className="bg-red-100 text-red-800">Élevée</Badge>
      case "medium":
        return <Badge className="bg-orange-100 text-orange-800">Moyenne</Badge>
      case "low":
        return <Badge className="bg-yellow-100 text-yellow-800">Faible</Badge>
      default:
        return <Badge variant="outline">Inconnue</Badge>
    }
  }

  // Fonction pour obtenir le badge de statut
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "blocked":
        return <Badge className="bg-green-100 text-green-800">Bloqué</Badge>
      case "investigating":
        return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>
      case "resolved":
        return <Badge className="bg-gray-100 text-gray-800">Résolu</Badge>
      case "active":
        return <Badge className="bg-red-100 text-red-800">Actif</Badge>
      case "expired":
        return <Badge className="bg-gray-100 text-gray-800">Expiré</Badge>
      default:
        return <Badge variant="outline">Inconnu</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">
            <Shield className="h-4 w-4 mr-2" />
            Vue d'ensemble
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Lock className="h-4 w-4 mr-2" />
            Paramètres
          </TabsTrigger>
          <TabsTrigger value="incidents">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Incidents
          </TabsTrigger>
          <TabsTrigger value="blacklist">
            <XCircle className="h-4 w-4 mr-2" />
            Blacklist
          </TabsTrigger>
          <TabsTrigger value="tests">
            <Zap className="h-4 w-4 mr-2" />
            Tests
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tableau de bord de sécurité</CardTitle>
              <CardDescription>Vue d'ensemble de la sécurité des tokens Quantum</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="bg-green-50 text-green-800 border-green-200">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription>
                  Tous les systèmes de sécurité sont opérationnels et fonctionnent correctement.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Incidents récents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{securityIncidents.length}</div>
                    <p className="text-xs text-muted-foreground">Derniers 7 jours</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Adresses blacklistées</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {blacklistedAddresses.filter((a) => a.status === "active").length}
                    </div>
                    <p className="text-xs text-muted-foreground">Actives actuellement</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Dernier test</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <Clock className="h-5 w-5 inline-block mr-1" />
                      2h
                    </div>
                    <p className="text-xs text-muted-foreground">Il y a 2 heures</p>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Statut des protections</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-green-600" />
                      <span>Protection anti-bot</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Actif</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-green-600" />
                      <span>Protection anti-dump</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Actif</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-green-600" />
                      <span>Système de blacklist</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Actif</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-green-600" />
                      <span>Limites de transaction</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Actif</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-green-600" />
                      <span>Surveillance avancée</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">Actif</Badge>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Derniers incidents</h3>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Type</TableHead>
                        <TableHead>Token</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Sévérité</TableHead>
                        <TableHead>Statut</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {securityIncidents.slice(0, 3).map((incident) => (
                        <TableRow key={incident.id}>
                          <TableCell>{incident.type}</TableCell>
                          <TableCell>{incident.token}</TableCell>
                          <TableCell>{incident.timestamp}</TableCell>
                          <TableCell>{getSeverityBadge(incident.severity)}</TableCell>
                          <TableCell>{getStatusBadge(incident.status)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end">
                  <Button variant="outline" size="sm">
                    Voir tous les incidents
                    <ArrowUpRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Paramètres de sécurité</CardTitle>
              <CardDescription>Configurez les fonctionnalités de sécurité pour tous les tokens Quantum</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <Shield className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Ces paramètres seront appliqués à tous les nouveaux tokens Quantum créés par les utilisateurs.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <h3 className="font-medium">Protection contre les bots</h3>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="antiBot" className="cursor-pointer">
                    Protection anti-bot
                    <p className="text-xs text-muted-foreground font-normal">
                      Empêche les bots d'acheter les tokens lors du lancement
                    </p>
                  </Label>
                  <Switch id="antiBot" checked={antiBot} onCheckedChange={setAntiBot} />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Protection contre le dumping</h3>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="antiDump" className="cursor-pointer">
                    Protection anti-dump
                    <p className="text-xs text-muted-foreground font-normal">
                      Empêche les ventes massives qui font chuter le prix
                    </p>
                  </Label>
                  <Switch id="antiDump" checked={antiDump} onCheckedChange={setAntiDump} />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Liquidité automatique</h3>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="autoLiquidity" className="cursor-pointer">
                    Auto-liquidité
                    <p className="text-xs text-muted-foreground font-normal">
                      Ajoute automatiquement de la liquidité au pool pour stabiliser le prix
                    </p>
                  </Label>
                  <Switch id="autoLiquidity" checked={autoLiquidity} onCheckedChange={setAutoLiquidity} />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Frais de transaction</h3>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="marketingFee">Frais de marketing (%)</Label>
                    <span className="text-sm font-medium">{marketingFee}%</span>
                  </div>
                  <Slider
                    id="marketingFee"
                    min={0}
                    max={10}
                    step={0.5}
                    value={[marketingFee]}
                    onValueChange={(value) => setMarketingFee(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="liquidityFee">Frais de liquidité (%)</Label>
                    <span className="text-sm font-medium">{liquidityFee}%</span>
                  </div>
                  <Slider
                    id="liquidityFee"
                    min={0}
                    max={10}
                    step={0.5}
                    value={[liquidityFee]}
                    onValueChange={(value) => setLiquidityFee(value[0])}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Limites de transaction</h3>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="maxWallet">Maximum par portefeuille (%)</Label>
                    <span className="text-sm font-medium">{maxWallet}%</span>
                  </div>
                  <Slider
                    id="maxWallet"
                    min={0}
                    max={10}
                    step={0.5}
                    value={[maxWallet]}
                    onValueChange={(value) => setMaxWallet(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="maxTransaction">Maximum par transaction (%)</Label>
                    <span className="text-sm font-medium">{maxTransaction}%</span>
                  </div>
                  <Slider
                    id="maxTransaction"
                    min={0}
                    max={5}
                    step={0.5}
                    value={[maxTransaction]}
                    onValueChange={(value) => setMaxTransaction(value[0])}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Protections avancées</h3>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="blacklist" className="cursor-pointer">
                    Fonction de blacklist
                    <p className="text-xs text-muted-foreground font-normal">
                      Permet de bloquer les adresses malveillantes
                    </p>
                  </Label>
                  <Switch id="blacklist" checked={blacklist} onCheckedChange={setBlacklist} />
                </div>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="pauseTrading" className="cursor-pointer">
                    Pause des transactions
                    <p className="text-xs text-muted-foreground font-normal">
                      Permet de suspendre temporairement les transactions en cas d'urgence
                    </p>
                  </Label>
                  <Switch id="pauseTrading" checked={pauseTrading} onCheckedChange={setPauseTrading} />
                </div>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="honeypot" className="cursor-pointer">
                    Détection honeypot
                    <p className="text-xs text-muted-foreground font-normal">
                      Détecte et bloque les tentatives de création de tokens frauduleux
                    </p>
                  </Label>
                  <Switch id="honeypot" checked={honeypot} onCheckedChange={setHoneypot} />
                </div>

                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="advancedMonitoring" className="cursor-pointer">
                    Surveillance avancée
                    <p className="text-xs text-muted-foreground font-normal">
                      Surveille en temps réel les comportements suspects
                    </p>
                  </Label>
                  <Switch
                    id="advancedMonitoring"
                    checked={advancedMonitoring}
                    onCheckedChange={setAdvancedMonitoring}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSecuritySettings} className="ml-auto">
                Enregistrer les paramètres de sécurité
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="incidents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Incidents de sécurité</CardTitle>
              <CardDescription>Historique des incidents de sécurité détectés</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Input placeholder="Rechercher..." className="w-[250px]" />
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filtrer
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    Exporter
                  </Button>
                  <Button variant="outline" size="sm">
                    Archiver
                  </Button>
                </div>
              </div>

              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Type</TableHead>
                      <TableHead>Token</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Adresse</TableHead>
                      <TableHead>Sévérité</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {securityIncidents.map((incident) => (
                      <TableRow key={incident.id}>
                        <TableCell>{incident.type}</TableCell>
                        <TableCell>{incident.token}</TableCell>
                        <TableCell>{incident.timestamp}</TableCell>
                        <TableCell className="font-mono text-xs">{incident.address.substring(0, 10)}...</TableCell>
                        <TableCell>{getSeverityBadge(incident.severity)}</TableCell>
                        <TableCell>{getStatusBadge(incident.status)}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="blacklist" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Gestion de la blacklist</CardTitle>
              <CardDescription>Gérez les adresses blacklistées pour tous les tokens Quantum</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Input placeholder="Rechercher une adresse..." className="w-[350px]" />
                  <Button>Ajouter à la blacklist</Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    Exporter
                  </Button>
                  <Button variant="outline" size="sm">
                    Importer
                  </Button>
                </div>
              </div>

              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Adresse</TableHead>
                      <TableHead>Raison</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Tokens affectés</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {blacklistedAddresses.map((address, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-mono text-xs">{address.address.substring(0, 10)}...</TableCell>
                        <TableCell>{address.reason}</TableCell>
                        <TableCell>{address.date}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {address.tokens.map((token, i) => (
                              <Badge key={i} variant="outline" className="text-xs">
                                {token}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(address.status)}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm" onClick={() => removeFromBlacklist(address.address)}>
                            <XCircle className="h-4 w-4 text-red-500" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tests" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tests de sécurité</CardTitle>
              <CardDescription>Exécutez des tests pour vérifier l'efficacité des mesures de sécurité</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  Les tests de sécurité simulent des attaques pour vérifier que les protections fonctionnent
                  correctement.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button onClick={runSecurityTest} disabled={isRunningTest} className="w-full">
                    {isRunningTest ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Exécution des tests...
                      </>
                    ) : (
                      <>
                        <Zap className="mr-2 h-4 w-4" />
                        Exécuter les tests de sécurité
                      </>
                    )}
                  </Button>

                  <Button variant="outline" className="w-full">
                    <FileText className="mr-2 h-4 w-4" />
                    Générer un rapport de sécurité
                  </Button>
                </div>

                {testLogs.length > 0 && (
                  <div className="mt-4">
                    <Label>Logs des tests</Label>
                    <div className="mt-2 p-4 bg-black text-green-400 font-mono text-xs h-64 overflow-y-auto rounded">
                      {testLogs.map((log, index) => (
                        <div key={index}>{log}</div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

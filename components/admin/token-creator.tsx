"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"

import { AlertDescription } from "@/components/ui/alert"

import { AlertTitle } from "@/components/ui/alert"

import type React from "react"
import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { Keypair, LAMPORTS_PER_SOL, Connection, Transaction } from "@solana/web3.js"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BadgeAlertIcon as Alert, BadgeAlertIcon as Alert, BadgeAlertIcon as Alert } from "lucide-react"
import { useTokenRegistry, type TokenInfo } from "@/lib/token-registry"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>rigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Textarea } from "@/components/ui/textarea"
import Link from "next/link"
import { envConfig } from "@/lib/env-config"
import { useAdminSettings } from "@/hooks/use-admin-settings"
import {
  createInitializeMintInstruction,
  TOKEN_PROGRAM_ID,
  getOrCreateAssociatedTokenAccount,
  mintTo,
} from "@solana/spl-token"
import { grindSuffixAction } from "@/app/grind-suffix-action"
import { useToast } from "@/components/ui/use-toast"
import { CheckCircle2, AlertCircle, Info, ExternalLink } from "lucide-react"

export default function AdminTokenCreator() {
  const { publicKey, signTransaction, sendTransaction } = useWallet()
  const { minTokenSupply, maxTokenDecimals, networkType } = useAdminSettings()
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [customSuffix, setCustomSuffix] = useState("MM")
  const [tokenDecimals, setTokenDecimals] = useState("9")
  const [tokenSupply, setTokenSupply] = useState("**********")
  const [tokenDescription, setTokenDescription] = useState("")
  const [tokenWebsite, setTokenWebsite] = useState("")
  const [tokenTwitter, setTokenTwitter] = useState("")
  const [tokenTelegram, setTokenTelegram] = useState("")
  const [tokenType, setTokenType] = useState("basic")
  const [baseFee, setBaseFee] = useState([1])
  const [additionalFee, setAdditionalFee] = useState([10])
  const [antiWhale, setAntiWhale] = useState(false)
  const [antiWhalePercentage, setAntiWhalePercentage] = useState([150])
  const [blacklistEnabled, setBlacklistEnabled] = useState(false)
  const [maxTxPercentage, setMaxTxPercentage] = useState([2])
  const [maxWalletPercentage, setMaxWalletPercentage] = useState([3])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [mintAddress, setMintAddress] = useState<string | null>(null)
  const [tokenProgramId, setTokenProgramId] = useState<string | null>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const addToken = useTokenRegistry((state) => state.addToken)
  const router = useRouter()
  const [grindingResult, setGrindingResult] = useState<{
    success: boolean
    keypair?: { publicKey: string; secret: number[] }
    attempts?: number
    error?: string
    address?: string
  } | null>(null)
  const { toast } = useToast()

  // Vérifier le solde du portefeuille
  useEffect(() => {
    const checkBalance = async () => {
      if (publicKey) {
        try {
          const connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
          const balance = await connection.getBalance(publicKey)
          setWalletBalance(balance / LAMPORTS_PER_SOL)
        } catch (err: any) {
          console.error("Error checking wallet balance:", err)
          setWalletBalance(null)
          toast({
            title: "Wallet Balance Error",
            description: err.message || "Failed to retrieve wallet balance.",
            variant: "destructive",
          })
        }
      } else {
        setWalletBalance(null)
      }
    }

    checkBalance()
  }, [publicKey])

  const handleCreateToken = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!publicKey || !signTransaction || !sendTransaction) {
      setError("Portefeuille non connecté ou fonctionnalités manquantes")
      toast({
        title: "Wallet Error",
        description: "Please connect your wallet and ensure it supports signing transactions.",
        variant: "destructive",
      })
      return
    }

    // Validate token supply against admin settings
    if (Number(tokenSupply) < 1000) {
      setError(`Token supply must be at least ${minTokenSupply}`)
      toast({
        title: "Invalid Token Supply",
        description: `Token supply must be at least ${minTokenSupply}.`,
        variant: "destructive",
      })
      return
    }

    // Validate token decimals against admin settings
    if (Number(tokenDecimals) > 9) {
      setError(`Token decimals cannot exceed ${maxTokenDecimals}`)
      toast({
        title: "Invalid Token Decimals",
        description: `Token decimals cannot exceed ${maxTokenDecimals}.`,
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)
    setSuccess(null)
    setMintAddress(null)
    setTokenProgramId(null)

    try {
      let mintKeypair: Keypair

      if (tokenType === "premium") {
        // Use pre-mined keypair for premium tokens
        const privateKey = process.env.PRE_MINED_MMGF_PRIVATE_KEY
        if (!privateKey) {
          throw new Error("PRE_MINED_MMGF_PRIVATE_KEY not set")
        }
        mintKeypair = Keypair.fromSecretKey(Uint8Array.from(JSON.parse(privateKey)))
        setMintAddress(mintKeypair.publicKey.toString())
      } else {
        // 1. Generate keypair with the specified suffix
        const grindingResult = await grindSuffixAction(customSuffix)

        if (!grindingResult || !grindingResult.success || !grindingResult.keypair) {
          setError(grindingResult?.error || "Échec de la génération de la paire de clés avec le suffixe")
          toast({
            title: "Keypair Generation Failed",
            description: grindingResult?.error || "Failed to generate keypair with the specified suffix.",
            variant: "destructive",
          })
          setIsLoading(false)
          return
        }

        setGrindingResult(grindingResult)
        mintKeypair = Keypair.fromSecretKey(Uint8Array.from(grindingResult.keypair.secret))
        setMintAddress(mintKeypair.publicKey.toString())
      }

      // 2. Connect to Solana Devnet
      const connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")

      // 3. Use the provided MMGF address directly
      const mint = mintKeypair.publicKey

      // 4. Initialize the mint (no need to create account, it already exists)
      const initMintInstruction = createInitializeMintInstruction(
        mint,
        Number(tokenDecimals),
        publicKey,
        publicKey,
        TOKEN_PROGRAM_ID,
      )

      // 5. Create a transaction
      const transaction = new Transaction()
      transaction.add(initMintInstruction)

      // 6. Get recent blockhash
      const { blockhash } = await connection.getLatestBlockhash("confirmed")
      transaction.recentBlockhash = blockhash

      // 7. Set the fee payer
      transaction.feePayer = publicKey

      // 8. Sign the transaction
      let signedTransaction
      try {
        console.log("Transaction before signing:", transaction)
        signedTransaction = await signTransaction(transaction)
        if (!signedTransaction) {
          throw new Error("Transaction signature failed")
        }
      } catch (signError: any) {
        console.error("Transaction signature failed:", signError)
        setError(`Transaction signature failed: ${signError.message}`)
        toast({
          title: "Transaction Signature Failed",
          description: signError.message || "Failed to sign the transaction.",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      // 9. Send the transaction
      let transactionId
      try {
        transactionId = await sendTransaction(signedTransaction, connection)
        console.log(`Transaction submitted: ${transactionId}`)
      } catch (sendError: any) {
        console.error("Transaction send failed:", sendError)
        console.error("Raw sendError object:", sendError)
        console.error("Decoded transaction:", transaction)
        console.error("Environment variables:", {
          SOLANA_RPC_URL: envConfig.SOLANA_RPC_URL,
          TOKEN_PROGRAM_ID: envConfig.TOKEN_PROGRAM_ID,
        })
        setError(`Transaction send failed: ${sendError.message}`)
        toast({
          title: "Transaction Send Failed",
          description: sendError.message || "Failed to send the transaction.",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      // 10. Get the token account of the creator address
      const tokenAccount = await getOrCreateAssociatedTokenAccount(
        connection,
        Keypair.fromSecretKey(Uint8Array.from(grindingResult.keypair.secret)), // Use the pre-generated keypair
        mint,
        publicKey, // Use the connected wallet's public key
      )

      // 11. Mint the initial supply to the creator
      const initialAmount = Number(tokenSupply) * Math.pow(10, Number(tokenDecimals))
      const mintTx = await mintTo(
        connection,
        Keypair.fromSecretKey(Uint8Array.from(grindingResult.keypair.secret)), // Use the pre-generated keypair
        mint,
        tokenAccount.address,
        publicKey, // Mint authority
        initialAmount,
      )

      // Set the mint address for display
      setMintAddress(mint.toString())

      // 12. Add the token to the registry
      const newToken: TokenInfo = {
        id: tokenSymbol.toLowerCase(),
        name: tokenName,
        symbol: `${tokenSymbol}${customSuffix}`, // Use the custom suffix
        mintAddress: mint.toString(),
        decimals: Number.parseInt(tokenDecimals),
        totalSupply: tokenSupply,
        createdAt: Date.now(),
        price: 0.001, // Prix initial
        priceChange24h: 0,
        creator: publicKey.toString(),
        description: tokenDescription,
        website: tokenWebsite,
        twitter: tokenTwitter,
        telegram: tokenTelegram,
        isVerified: false,
      }

      addToken(newToken)

      setSuccess(
        `Token ${tokenName} (${tokenSymbol}${customSuffix}) créé avec succès sur Solana Devnet et intégré à la plateforme! Vérifiez votre token sur Solscan ou l'explorateur Solana.`,
      )
      toast({
        title: "Token Created",
        description: `Token ${tokenName} (${tokenSymbol}${customSuffix}) created successfully!`,
      })

      // Réinitialiser le formulaire après une création réussie
      setTimeout(() => {
        setTokenName("")
        setTokenSymbol("")
        setTokenDecimals("9")
        setTokenSupply("**********")
        setTokenDescription("")
        setTokenWebsite("")
        setTokenTwitter("")
        setTokenTelegram("")
        // Rediriger vers l'onglet de gestion des tokens
        router.push("/token-factory?tab=manage")
      }, 5000)
    } catch (err: any) {
      console.error("Error creating token:", err)
      setError(err.message || "Échec de la création du token. Veuillez réessayer.")
      toast({
        title: "Token Creation Failed",
        description: err.message || "Failed to create token. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getTokenPrice = () => {
    return tokenType === "basic" ? 0.1 : 0.5
  }

  return (
    <Tabs defaultValue="basic" onValueChange={setTokenType} className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="basic">Token Basique</TabsTrigger>
        <TabsTrigger value="premium">Token Premium</TabsTrigger>
      </TabsList>

      <TabsContent value="basic" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Créer un Token Basique</CardTitle>
            <CardDescription>
              Déployez un nouveau token SPL sur Solana Devnet avec des fonctionnalités de base
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleCreateToken}>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Package Token Basique</AlertTitle>
                <AlertDescription>
                  <p>Prix: 0.1 SOL (frais de réseau + frais de plateforme)</p>
                  <p>Fonctionnalités: Token SPL standard avec offre fixe</p>
                  <p>Le contrat se termine par: {customSuffix}</p>
                </AlertDescription>
              </Alert>

              {walletBalance !== null && (
                <Alert
                  className={
                    walletBalance < getTokenPrice()
                      ? "bg-red-50 text-red-800 border-red-200"
                      : "bg-blue-50 text-blue-800 border-blue-200"
                  }
                >
                  <Info className="h-4 w-4" />
                  <AlertTitle>Solde du portefeuille</AlertTitle>
                  <AlertDescription>
                    <p>Vous avez actuellement {walletBalance.toFixed(4)} SOL</p>
                    {walletBalance < getTokenPrice() && (
                      <p className="text-red-600 font-semibold">
                        Solde insuffisant pour créer un token. Vous avez besoin d'au moins {getTokenPrice()} SOL.
                      </p>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="tokenName">Nom du Token</Label>
                <Input
                  id="tokenName"
                  placeholder="ex: Mon Token"
                  value={tokenName}
                  onChange={(e) => setTokenName(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tokenSymbol">Symbole du Token</Label>
                <Input
                  id="tokenSymbol"
                  placeholder="ex: MTK"
                  value={tokenSymbol}
                  onChange={(e) => setTokenSymbol(e.target.value)}
                  required
                  maxLength={10}
                />
                <p className="text-xs text-muted-foreground">
                  Le suffixe "{customSuffix}" sera automatiquement ajouté à votre symbole
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tokenDecimals">Décimales</Label>
                  <Input
                    id="tokenDecimals"
                    type="number"
                    min="0"
                    max="9"
                    value={tokenDecimals}
                    onChange={(e) => setTokenDecimals(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenSupply">Offre Initiale</Label>
                  <Input
                    id="tokenSupply"
                    type="number"
                    min="1"
                    value={tokenSupply}
                    onChange={(e) => setTokenSupply(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tokenDescription">Description (Optionnel)</Label>
                <Textarea
                  id="tokenDescription"
                  placeholder="Décrivez votre token..."
                  value={tokenDescription}
                  onChange={(e) => setTokenDescription(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tokenWebsite">Site Web (Optionnel)</Label>
                <Input
                  id="tokenWebsite"
                  placeholder="https://monsite.com"
                  value={tokenWebsite}
                  onChange={(e) => setTokenWebsite(e.target.value)}
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Erreur</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="bg-green-50 text-green-800 border-green-200">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <AlertTitle>Succès</AlertTitle>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              {mintAddress && (
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-sm font-medium mb-1">Adresse du Token Mint:</p>
                  <p className="text-xs font-mono break-all">{mintAddress}</p>
                  <div className="flex mt-2">
                    <Button variant="outline" size="sm" className="text-xs" asChild>
                      <Link href={`https://explorer.solana.com/address/${mintAddress}?cluster=devnet`} target="_blank">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Explorer Solana
                      </Link>
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs ml-2" asChild>
                      <Link href={`https://solscan.io/token/${mintAddress}?cluster=devnet`} target="_blank">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Solscan
                      </Link>
                    </Button>
                  </div>
                  <p className="text-xs mt-2 text-muted-foreground">
                    Votre token a été automatiquement intégré à tous les modules de la plateforme.
                  </p>
                </div>
              )}

              {tokenProgramId && (
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-sm font-medium mb-1">ID du Programme Token:</p>
                  <p className="text-xs font-mono break-all">{tokenProgramId}</p>
                  <p className="text-xs mt-2 text-muted-foreground">
                    L'ID du programme se termine par {customSuffix} comme demandé.
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading || !publicKey || (walletBalance !== null && walletBalance < getTokenPrice())}
              >
                {isLoading ? "Création du Token..." : `Créer un Token (${getTokenPrice()} SOL)`}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </TabsContent>

      <TabsContent value="premium" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Créer un Token Premium</CardTitle>
            <CardDescription>
              Déployez un nouveau token SPL sur Solana Devnet avec des fonctionnalités avancées
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleCreateToken}>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Package Token Premium</AlertTitle>
                <AlertDescription>
                  <p>Prix: 0.5 SOL (frais de réseau + frais de plateforme)</p>
                  <p>
                    Fonctionnalités: Token avancé avec frais personnalisables, mécanismes anti-baleine et capacités de
                    staking
                  </p>
                  <p>Le contrat se termine par: MMGF</p>
                </AlertDescription>
              </Alert>

              {walletBalance !== null && (
                <Alert
                  className={
                    walletBalance < getTokenPrice()
                      ? "bg-red-50 text-red-800 border-red-200"
                      : "bg-blue-50 text-blue-800 border-blue-200"
                  }
                >
                  <Info className="h-4 w-4" />
                  <AlertTitle>Solde du portefeuille</AlertTitle>
                  <AlertDescription>
                    <p>Vous avez actuellement {walletBalance.toFixed(4)} SOL</p>
                    {walletBalance < getTokenPrice() && (
                      <p className="text-red-600 font-semibold">
                        Solde insuffisant pour créer un token. Vous avez besoin d'au moins {getTokenPrice()} SOL.
                      </p>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="tokenName">Nom du Token</Label>
                <Input
                  id="tokenName"
                  placeholder="ex: Mon Token"
                  value={tokenName}
                  onChange={(e) => setTokenName(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tokenSymbol">Symbole du Token</Label>
                <Input
                  id="tokenSymbol"
                  placeholder="ex: MTK"
                  value={tokenSymbol}
                  onChange={(e) => setTokenSymbol(e.target.value)}
                  required
                  maxLength={10}
                />
                <p className="text-xs text-muted-foreground">
                  Le suffixe "MMGF" sera automatiquement ajouté à votre symbole
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tokenDecimals">Décimales</Label>
                  <Input
                    id="tokenDecimals"
                    type="number"
                    min="0"
                    max="9"
                    value={tokenDecimals}
                    onChange={(e) => setTokenDecimals(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenSupply">Offre Initiale</Label>
                  <Input
                    id="tokenSupply"
                    type="number"
                    min="1"
                    value={tokenSupply}
                    onChange={(e) => setTokenSupply(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tokenDescription">Description</Label>
                <Textarea
                  id="tokenDescription"
                  placeholder="Décrivez votre token..."
                  value={tokenDescription}
                  onChange={(e) => setTokenDescription(e.target.value)}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tokenWebsite">Site Web (Optionnel)</Label>
                  <Input
                    id="tokenWebsite"
                    placeholder="https://monsite.com"
                    value={tokenWebsite}
                    onChange={(e) => setTokenWebsite(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenTwitter">Twitter (Optionnel)</Label>
                  <Input
                    id="tokenTwitter"
                    placeholder="https://twitter.com/montoken"
                    value={tokenTwitter}
                    onChange={(e) => setTokenTwitter(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenTelegram">Telegram (Optionnel)</Label>
                  <Input
                    id="tokenTelegram"
                    placeholder="https://t.me/montoken"
                    value={tokenTelegram}
                    onChange={(e) => setTokenTelegram(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Configuration des Frais</h3>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="baseFee">Frais de Base ({baseFee}%)</Label>
                  </div>
                  <Slider id="baseFee" min={0} max={10} step={0.1} value={baseFee} onValueChange={setBaseFee} />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="additionalFee">Frais Additionnels ({additionalFee}%)</Label>
                  </div>
                  <Slider
                    id="additionalFee"
                    min={0}
                    max={20}
                    step={1}
                    value={additionalFee}
                    onValueChange={setAdditionalFee}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Mécanismes de Protection</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="antiWhale">Mécanisme Anti-Baleine</Label>
                    <p className="text-sm text-muted-foreground">Taxe sur les gains dépassant le seuil</p>
                  </div>
                  <Switch id="antiWhale" checked={antiWhale} onCheckedChange={setAntiWhale} />
                </div>

                {antiWhale && (
                  <div className="space-y-2 pl-6 border-l-2 border-muted">
                    <div className="flex justify-between">
                      <Label htmlFor="antiWhalePercentage">Seuil ({antiWhalePercentage}%)</Label>
                    </div>
                    <Slider
                      id="antiWhalePercentage"
                      min={50}
                      max={300}
                      step={10}
                      value={antiWhalePercentage}
                      onValueChange={setAntiWhalePercentage}
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="blacklistEnabled">Système de Liste Noire</Label>
                    <p className="text-sm text-muted-foreground">Bloquer automatiquement les portefeuilles suspects</p>
                  </div>
                  <Switch id="blacklistEnabled" checked={blacklistEnabled} onCheckedChange={setBlacklistEnabled} />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="maxTxPercentage">Transaction Max ({maxTxPercentage}% de l'offre)</Label>
                  </div>
                  <Slider
                    id="maxTxPercentage"
                    min={0.1}
                    max={10}
                    step={0.1}
                    value={maxTxPercentage}
                    onValueChange={setMaxTxPercentage}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="maxWalletPercentage">
                      Détention Max par Portefeuille ({maxWalletPercentage}% de l'offre)
                    </Label>
                  </div>
                  <Slider
                    id="maxWalletPercentage"
                    min={0.1}
                    max={10}
                    step={0.1}
                    value={maxWalletPercentage}
                    onValueChange={setMaxWalletPercentage}
                  />
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Erreur</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="bg-green-50 text-green-800 border-green-200">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <AlertTitle>Succès</AlertTitle>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              {mintAddress && (
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-sm font-medium mb-1">Adresse du Token Mint:</p>
                  <p className="text-xs font-mono break-all">{mintAddress}</p>
                  <div className="flex mt-2">
                    <Button variant="outline" size="sm" className="text-xs" asChild>
                      <Link href={`https://explorer.solana.com/address/${mintAddress}?cluster=devnet`} target="_blank">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Explorer Solana
                      </Link>
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs ml-2" asChild>
                      <Link href={`https://solscan.io/token/${mintAddress}?cluster=devnet`} target="_blank">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Solscan
                      </Link>
                    </Button>
                  </div>
                  <p className="text-xs mt-2 text-muted-foreground">
                    Votre token a été automatiquement intégré à tous les modules de la plateforme.
                  </p>
                </div>
              )}

              {tokenProgramId && (
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-sm font-medium mb-1">ID du Programme Token:</p>
                  <p className="text-xs font-mono break-all">{tokenProgramId}</p>
                  <p className="text-xs mt-2 text-muted-foreground">
                    L'ID du programme se termine par MMGF comme demandé.
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading || !publicKey || (walletBalance !== null && walletBalance < getTokenPrice())}
              >
                {isLoading ? "Création du Token..." : `Créer un Token (${getTokenPrice()} SOL)`}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </TabsContent>
    </Tabs>
  )
}

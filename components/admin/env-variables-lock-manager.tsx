"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { AlertCircle, Lock, Unlock, Plus, Edit, Shield, FileText } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/hooks/use-toast"

// Types pour les variables d'environnement
interface EnvVariable {
  id: string
  key: string
  value: string
  isLocked: boolean
  category: "blockchain" | "api" | "security" | "platform" | "other"
  description: string
  lastModified: Date
  modifiedBy: string
}

// Données d'exemple pour les variables d'environnement
const initialEnvVariables: EnvVariable[] = [
  {
    id: "1",
    key: "NEXT_PUBLIC_SOLANA_RPC_URL",
    value: "https://api.mainnet-beta.solana.com",
    isLocked: true,
    category: "blockchain",
    description: "URL du RPC Solana pour les connexions blockchain",
    lastModified: new Date("2023-04-15"),
    modifiedBy: "admin",
  },
  {
    id: "2",
    key: "MARKET_API_ENDPOINT",
    value: "https://api.market.example.com/v1",
    isLocked: false,
    category: "api",
    description: "Point de terminaison API pour les données de marché",
    lastModified: new Date("2023-04-10"),
    modifiedBy: "admin",
  },
  {
    id: "3",
    key: "MARKET_API_KEY",
    value: "••••••••••••••••",
    isLocked: true,
    category: "api",
    description: "Clé API pour l'accès aux données de marché",
    lastModified: new Date("2023-03-22"),
    modifiedBy: "admin",
  },
  {
    id: "4",
    key: "NEXT_PUBLIC_CUSTOM_MINT_ADDRESS",
    value: "So11111111111111111111111111111111111111112",
    isLocked: true,
    category: "blockchain",
    description: "Adresse de mint pour le token personnalisé",
    lastModified: new Date("2023-03-15"),
    modifiedBy: "admin",
  },
  {
    id: "5",
    key: "NEXT_PUBLIC_TOKEN_PROGRAM_ID",
    value: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
    isLocked: true,
    category: "blockchain",
    description: "ID du programme de token Solana",
    lastModified: new Date("2023-02-28"),
    modifiedBy: "admin",
  },
]

// Fonction pour masquer les valeurs sensibles
const maskSensitiveValue = (value: string, isVisible: boolean) => {
  if (!isVisible && value.length > 0) {
    return "•".repeat(Math.min(20, value.length))
  }
  return value
}

export function EnvVariablesLockManager() {
  const [envVariables, setEnvVariables] = useState<EnvVariable[]>(initialEnvVariables)
  const [newVariable, setNewVariable] = useState<Partial<EnvVariable>>({
    key: '',
    value: '',
    isLocked: false,
    category: 'other',
    description: ''
  })
  const [editingVariable, setEditingVariable] = useState<EnvVariable | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [adminPassword, setAdminPassword] = useState('')
  const [isPasswordCorrect, setIsPasswordCorrect] = useState(false)
  const [visibleValues, setVisibleValues] = useState<Record<string, boolean>>({})
  const [activeTab, setActiveTab] = useState('all')
  const [isLockdownMode, setIsLockdownMode] = useState(false)
  const [lockdownPassword, setLockdownPassword] = useState('')
  const [isLockdownDialogOpen, setIsLockdownDialogOpen] = useState(false)
  const [exportFormat, setExportFormat] = useState<'json' | 'env'>('env')
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)

  // Fonction pour ajouter une nouvelle variable
  const handleAddVariable = () => {
    if (!newVariable.key || !newVariable.value) {
      toast({
        title: "Erreur",
        description: "La clé et la valeur sont requises",
        variant: "destructive"
      })
      return
    }

    const newId = (envVariables.length + 1).toString()
    const variableToAdd: EnvVariable = {
      id: newId,
      key: newVariable.key,
      value: newVariable.value,
      isLocked: newVariable.isLocked || false,
      category: newVariable.category as any || 'other',
      description: newVariable.description || '',
      lastModified: new Date(),
      modifiedBy: 'admin'
    }

    setEnvVariables([...envVariables, variableToAdd])
    setNewVariable({
      key: '',
      value: '',
      isLocked: false,
      category: 'other',
      description: ''
    })
    setIsAddDialogOpen(false)
    
    toast({
      title: "Variable ajoutée",
      description: `La variable ${variableToAdd.key} a été ajoutée avec succès`,
    })
  }

  // Fonction pour mettre à jour une variable
  const handleUpdateVariable = () => {
    if (!editingVariable) return

    // Vérifier si la variable est verrouillée et si le mot de passe est correct
    if (editingVariable.isLocked && !isPasswordCorrect) {
      toast({
        title: "Erreur",
        description: "Mot de passe administrateur requis pour modifier une variable verrouillée",
        variant: "destructive"
      })
      return
    }

    const updatedVariables = envVariables.map(variable => 
      variable.id === editingVariable.id ? 
        {...editingVariable, lastModified: new Date(), modifiedBy: 'admin'} : 
        variable
    )

    setEnvVariables(updatedVariables)
    setEditingVariable(null)
    setIsEditDialogOpen(false)
    setAdminPassword('')
    setIsPasswordCorrect(false)
    
    toast({
      title: "Variable mise à jour",
      description: `La variable ${editingVariable.key} a été mise à jour avec succès`,
    })
  }

  // Fonction pour supprimer une variable
  const handleDeleteVariable = (id: string) => {
    const variableToDelete = envVariables.find(v => v.id === id)
    
    if (variableToDelete?.isLocked && !isPasswordCorrect) {
      toast({
        title: "Erreur",
        description: "Mot de passe administrateur requis pour supprimer une variable verrouillée",
        variant: "destructive"
      })
      return
    }
    
    setEnvVariables(envVariables.filter(variable => variable.id !== id))
    
    toast({
      title: "Variable supprimée",
      description: `La variable a été supprimée avec succès`,
    })
  }

  // Fonction pour vérifier le mot de passe administrateur
  const verifyAdminPassword = () => {
    // Dans un environnement réel, cela devrait être vérifié côté serveur
    const isValid = adminPassword === 'admin123' // Exemple simplifié
    setIsPasswordCorrect(isValid)
    
    if (!isValid) {
      toast({
        title: "Erreur d'authentification",
        description: "Mot de passe administrateur incorrect",
        variant: "destructive"
      })
    }
    
    return isValid
  }

  // Fonction pour activer/désactiver le mode verrouillage global
  const toggleLockdownMode = () => {
    // Dans un environnement réel, cela devrait être vérifié côté serveur
    const isValid = lockdownPassword === 'lockdown123' // Exemple simplifié
    
    if (isValid) {
      const newLockdownState = !isLockdownMode
      setIsLockdownMode(newLockdownState)
      
      // Si on active le mode verrouillage, verrouiller toutes les variables
      if (newLockdownState) {
        const lockedVariables = envVariables.map(variable => ({
          ...variable,
          isLocked: true,
          lastModified: new Date(),
          modifiedBy: 'admin (lockdown)'
        }))
        setEnvVariables(lockedVariables)
        
        toast({
          title: "Mode verrouillage activé",
          description: "Toutes les variables d'environnement sont maintenant verrouillées",
        })
      } else {
        toast({
          title: "Mode verrouillage désactivé",
          description: "Les variables peuvent maintenant être modifiées avec les permissions appropriées",
        })
      }
      
      setIsLockdownDialogOpen(false)
      setLockdownPassword('')
    } else {
      toast({
        title: "Erreur d'authentification",
        description: "Mot de passe de verrouillage incorrect",
        variant: "destructive"
      })
    }
  }

  // Fonction pour exporter les variables d'environnement
  const exportEnvVariables = () => {
    let exportContent = ''
    
    if (exportFormat === 'env') {
      // Format .env
      exportContent = envVariables.map(v => `${v.key}=${v.value}`).join('\n')
    } else {
      // Format JSON
      exportContent = JSON.stringify(
        envVariables.map(({ id, key, value, isLocked, category, description }) => 
          ({ key, value, isLocked, category, description })
        ), 
        null, 
        2
      )
    }
    
    // Créer un blob et un lien de téléchargement
    const blob = new Blob([exportContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = exportFormat === 'env' ? '.env.export' : 'env-variables.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    setIsExportDialogOpen(false)
    
    toast({
      title: "Export réussi",
      description: `Les variables d'environnement ont été exportées au format ${exportFormat.toUpperCase()}`,
    })
  }

  // Filtrer les variables en fonction de l'onglet actif
  const filteredVariables = activeTab === 'all' 
    ? envVariables 
    : envVariables.filter(v => v.category === activeTab)

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="bg-slate-50 dark:bg-slate-800">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl font-bold">Gestionnaire de Variables d'Environnement</CardTitle>
            <CardDescription>
              Configurez et verrouillez les variables d'environnement de la plateforme
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Dialog open={isLockdownDialogOpen} onOpenChange={setIsLockdownDialogOpen}>
              <DialogTrigger asChild>
                <Button variant={isLockdownMode ? "destructive" : "outline"}>
                  {isLockdownMode ? <Unlock className="mr-2 h-4 w-4" /> : <Lock className="mr-2 h-4 w-4" />}
                  {isLockdownMode ? "Désactiver Verrouillage" : "Activer Verrouillage"}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{isLockdownMode ? "Désactiver" : "Activer"} le mode verrouillage</DialogTitle>
                  <DialogDescription>
                    {isLockdownMode 
                      ? "Cette action permettra la modification des variables avec les permissions appropriées." 
                      : "Cette action verrouillera toutes les variables d'environnement pour empêcher toute modification non autorisée."}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="lockdownPassword">Mot de passe de verrouillage</Label>
                    <Input
                      id="lockdownPassword"
                      type="password"
                      placeholder="Entrez le mot de passe de verrouillage"
                      value={lockdownPassword}
                      onChange={(e) => setLockdownPassword(e.target.value)}
                    />
                  </div>
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Attention</AlertTitle>
                    <AlertDescription>
                      {isLockdownMode 
                        ? "Désactiver le mode verrouillage peut exposer votre plateforme à des modifications non autorisées." 
                        : "Activer le mode verrouillage empêchera toute modification des variables sans le mot de passe approprié."}
                    </AlertDescription>
                  </Alert>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsLockdownDialogOpen(false)}>Annuler</Button>
                  <Button 
                    variant={isLockdownMode ? "destructive" : "default"}
                    onClick={toggleLockdownMode}
                  >
                    {isLockdownMode ? "Désactiver" : "Activer"} le verrouillage
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Exporter
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Exporter les variables d'environnement</DialogTitle>
                  <DialogDescription>
                    Choisissez le format d'exportation pour vos variables d'environnement
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>Format d'exportation</Label>
                    <div className="flex space-x-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="env-format"
                          checked={exportFormat === 'env'}
                          onChange={() => setExportFormat('env')}
                        />
                        <Label htmlFor="env-format">.env</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="json-format"
                          checked={exportFormat === 'json'}
                          onChange={() => setExportFormat('json')}
                        />
                        <Label htmlFor="json-format">JSON</Label>
                      </div>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>Annuler</Button>
                  <Button onClick={exportEnvVariables}>
                    Exporter
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button disabled={isLockdownMode}>
                  <Plus className="mr-2 h-4 w-4" />
                  Ajouter Variable
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Ajouter une nouvelle variable d'environnement</DialogTitle>
                  <DialogDescription>
                    Remplissez les détails pour ajouter une nouvelle variable d'environnement
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="key" className="text-right">
                      Clé
                    </Label>
                    <Input
                      id="key"
                      placeholder="NEXT_PUBLIC_API_URL"
                      className="col-span-3"
                      value={newVariable.key}
                      onChange={(e) => setNewVariable({...newVariable, key: e.target.value})}
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="value" className="text-right">
                      Valeur
                    </Label>
                    <Input
                      id="value"
                      placeholder="https://api.example.com"
                      className="col-span-3"
                      value={newVariable.value}
                      onChange={(e) => setNewVariable({...newVariable, value: e.target.value})}
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="category" className="text-right">
                      Catégorie
                    </Label>
                    <select
                      id="category"
                      className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={newVariable.category as string}
                      onChange={(e) => setNewVariable({...newVariable, category: e.target.value as any})}
                    >
                      <option value="blockchain">Blockchain</option>
                      <option value="api">API</option>
                      <option value="security">Sécurité</option>
                      <option value="platform">Plateforme</option>
                      <option value="other">Autre</option>
                    </select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="description" className="text-right">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      placeholder="Description de la variable..."
                      className="col-span-3"
                      value={newVariable.description}
                      onChange={(e) => setNewVariable({...newVariable, description: e.target.value})}
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="isLocked" className="text-right">
                      Verrouillée
                    </Label>
                    <div className="flex items-center space-x-2 col-span-3">
                      <Switch
                        id="isLocked"
                        checked={newVariable.isLocked}
                        onCheckedChange={(checked) => setNewVariable({...newVariable, isLocked: checked})}
                      />
                      <Label htmlFor="isLocked">
                        {newVariable.isLocked ? "Oui" : "Non"}
                      </Label>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Annuler</Button>
                  <Button onClick={handleAddVariable}>Ajouter</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="all">Toutes</TabsTrigger>
            <TabsTrigger value="blockchain">Blockchain</TabsTrigger>
            <TabsTrigger value="api">API</TabsTrigger>
            <TabsTrigger value="security">Sécurité</TabsTrigger>
            <TabsTrigger value="platform">Plateforme</TabsTrigger>
            <TabsTrigger value="other">Autres</TabsTrigger>
          </TabsList>
          <TabsContent value={activeTab} className="mt-0">
            {isLockdownMode && (
              <Alert className="mb-4">
                <Shield className="h-4 w-4" />
                <AlertTitle>Mode verrouillage activé</AlertTitle>
                <AlertDescription>
                  Toutes les variables sont verrouillées. Désactivez le mode verrouillage pour effectuer des modifications.
                </AlertDescription>
              </Alert>
            )}
            
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Clé</TableHead>
                  <TableHead className="w-[300px]">Valeur</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Dernière modification</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVariables.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                      Aucune variable trouvée
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredVariables.map((variable) => (
                    <TableRow key={variable.id}>
                      <TableCell className="font-medium">
                        {variable.key}
                        {variable.isLocked && (
                          <Badge variant="outline" className="ml-2">
                            <Lock className="h-3 w-3 mr-1" />
                            Verrouillée
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="font-mono">
                            {maskSensitiveValue(variable.value, !!visibleValues[variable.id])}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setVisibleValues({
                              ...visibleValues,
                              [variable.id]: !visibleValues[variable.id]
                            })}
                          >
                            {visibleValues[variable.id] ? "Masquer" : "Afficher"}
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {variable.category.charAt(0).toUpperCase() + variable.category.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate" title={variable.description}>
                        {variable.description || "-"}
                      </TableCell>
                      <TableCell>
                        {variable.lastModified.toLocaleDateString()} par {variable.modifiedBy}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Dialog open={isEditDialogOpen && editingVariable?.id === variable.id} onOpenChange={(open) => {
                            if (!open) {
                              setEditingVariable(null)
                              setAdminPassword('')
                              setIsPasswordCorrect(false)
                            }
                            setIsEditDialogOpen(open)
                          }}>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                disabled={isLockdownMode}
                                onClick={() => setEditingVariable(variable)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Modifier la variable d'environnement</DialogTitle>
                                <DialogDescription>
                                  Modifiez les détails de la variable d'environnement
                                </DialogDescription>
                              </DialogHeader>
                              {editingVariable && (
                                <>
                                  <div className="grid gap-4 py-4">
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="edit-key" className="text-right">
                                        Clé
                                      </Label>
                                      <Input
                                        id="edit-key"
                                        value={editingVariable.key}
                                        onChange={(e) => setEditingVariable({...editingVariable, key: e.target.value})}
                                        className="col-span-3"
                                        disabled={editingVariable.isLocked && !isPasswordCorrect}
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="edit-value" className="text-right">
                                        Valeur
                                      </Label>
                                      <Input
                                        id="edit-value"
                                        value={editingVariable.value}
                                        onChange={(e) => setEditingVariable({...editingVariable, value: e.target.value})}
                                        className="col-span-3"
                                        disabled={editingVariable.isLocked && !isPasswordCorrect}
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="edit-category" className="text-right">
                                        Catégorie
                                      </Label>
                                      <select
                                        id="edit-category"
                                        className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        value={editingVariable.category}
                                        onChange={(e) => setEditingVariable({...editingVariable, category: e.target.value as any})}
                                        disabled={editingVariable.isLocked && !isPasswordCorrect}
                                      >
                                        <option value="blockchain">Blockchain</option>
                                        <option value="api">API</option>
                                        <option value="security">Sécurité</option>
                                        <option value="platform">Plateforme</option>
                                        <option value="other">Autre</option>
                                      </select>
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="edit-description" className="text-right">
                                        Description
                                      </Label>
                                      <Textarea
                                        id="edit-description"
                                        value={editingVariable.description}
                                        onChange={(e) => setEditingVariable({...editingVariable, description: e.target.value})}
                                        className="col-span-3"
                                        disabled={editingVariable.isLocked && !isPasswordCorrect}
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label htmlFor="edit-isLocked" className="text-right">
                                        Verrouillée
                                      </Label>
                                      <div className="flex items-center space-x-2 col-span-3">
                                        <Switch
                                          id="edit-isLocked"
                                          checked={editingVariable.isLocked}
                                          onCheckedChange={(checked) => setEditingVariable({...editingVariable, isLocked: checked})}
                                          disabled={!isPasswordCorrect}
                                        />
                                        <Label htmlFor="edit-isLocked">
                                          {editingVariable.isLocked ? "Oui" : "Non"}
                                        </Label>
                                      </div>
                                    </div>
                                    
                                    {editingVariable.isLocked && !isPasswordCorrect && (
                                      <div className="space-y-4 mt-4 p-4 border rounded-md bg-amber-50 dark:bg-amber-950">
                                        <h4 className="font-semibold flex items-center">
                                          <Lock className="h-4 w-4 mr-2" />
                                          Authentification requise
                                        </h4>
                                        <p className="text-sm">&lt;!-- Amélioration de l'Interface d'Administration de la Plateforme Solana

Je vais créer une solution complète pour améliorer l'interface d'administration de votre plateforme Solana, en mettant l'accent sur la gestion des variables d'environnement, les fonctionnalités d'audit, et la création de tokens avec système de remises.

## 1. Gestion des Variables d'Environnement avec Verrouillage

Commençons par créer un gestionnaire de variables d'environnement avancé avec possibilité de verrouillage : --&gt;</p>

\

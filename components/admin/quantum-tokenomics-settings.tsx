"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip } from "recharts"
import { ChartContainer } from "@/components/ui/chart"
import { Coins, Percent, TrendingUp } from "lucide-react"

export default function QuantumTokenomicsSettings() {
  const [buyTaxPercentage, setBuyTaxPercentage] = useState(5)
  const [sellTaxPercentage, setSellTaxPercentage] = useState(5)
  const [transferTaxPercentage, setTransferTaxPercentage] = useState(2)
  const [marketingPercentage, setMarketingPercentage] = useState(30)
  const [developmentPercentage, setDevelopmentPercentage] = useState(30)
  const [liquidityPercentage, setLiquidityPercentage] = useState(40)
  const [enableBurnMechanism, setEnableBurnMechanism] = useState(true)
  const [burnPercentage, setBurnPercentage] = useState(10)
  const [maxSupply, setMaxSupply] = useState("1000000000")
  const [initialBurn, setInitialBurn] = useState(20)
  
  // Calculate remaining percentage for tokenomics allocation
  const remainingPercentage = 100 - marketingPercentage - developmentPercentage - liquidityPercentage
  
  // Data for the tokenomics pie chart
  const tokenomicsData = [
    { name: 'Marketing', value: marketingPercentage },
    { name: 'Development', value: developmentPercentage },
    { name: 'Liquidity', value: liquidityPercentage },
  ]
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28']
  
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Percent className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-medium">Tax Configuration</h3>
      </div>
      
      <div className="grid gap-6">
        <div>
          <div className="flex justify-between mb-2">
            <Label htmlFor="buy-tax">Buy Tax (%)</Label>
            <span className="text-sm">{buyTaxPercentage}%</span>
          </div>
          <Slider
            id="buy-tax"
            value={[buyTaxPercentage]}
            onValueChange={(value) => setBuyTaxPercentage(value[0])}
            max={15}
            min={0}
            step={0.5}
          />
        </div>
        
        <div>
          <div className="flex justify-between mb-2">
            <Label htmlFor="sell-tax">Sell Tax (%)</Label>
            <span className="text-sm">{sellTaxPercentage}%</span>
          </div>
          <Slider
            id="sell-tax"
            value={[sellTaxPercentage]}
            onValueChange={(value) => setSellTaxPercentage(value[0])}
            max={15}
            min={0}
            step={0.5}
          />
        </div>
        
        <div>
          <div className="flex justify-between mb-2">
            <Label htmlFor="transfer-tax">Transfer Tax (%)</Label>
            <span className="text-sm">{transferTaxPercentage}%</span>
          </div>
          <Slider
            id="transfer-tax"
            value={[transferTaxPercentage]}
            onValueChange={(value) => setTransferTaxPercentage(value[0])}
            max={10}
            min={0}
            step={0.5}
          />
        </div>
      </div>
      
      <Separator />
      
      <div className="flex items-center space-x-2">
        <TrendingUp className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-medium">Tax Distribution</h3>
      </div>
      
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-6">
          <div>
            <div className="flex justify-between mb-2">
              <Label htmlFor="marketing">Marketing (%)</Label>
              <span className="text-sm">{marketingPercentage}%</span>
            </div>
            <Slider
              id="marketing"
              value={[marketingPercentage]}
              onValueChange={(value) => {
                if (value[0] + developmentPercentage + liquidityPercentage <= 100) {
                  setMarketingPercentage(value[0])
                }
              }}
              max={100}
              min={0}
              step={5}
            />
          </div>
          
          <div>
            <div className="flex justify-between mb-2">
              <Label htmlFor="development">Development (%)</Label>
              <span className="text-sm">{developmentPercentage}%</span>
            </div>
            <Slider
              id="development"
              value={[developmentPercentage]}
              onValueChange={(value) => {
                if (marketingPercentage + value[0] + liquidityPercentage <= 100) {
                  setDevelopmentPercentage(value[0])
                }
              }}
              max={100}
              min={0}
              step={5}
            />
          </div>
          
          <div>
            <div className="flex justify-between mb-2">
              <Label htmlFor="liquidity">Liquidity (%)</Label>
              <span className="text-sm">{liquidityPercentage}%</span>
            </div>
            <Slider
              id="liquidity"
              value={[liquidityPercentage]}
              onValueChange={(value) => {
                if (marketingPercentage + developmentPercentage + value[0] <= 100) {
                  setLiquidityPercentage(value[0])
                }
              }}
              max={100}
              min={0}
              step={5}
            />
          </div>
          
          <div className="text-sm text-muted-foreground">
            {remainingPercentage > 0 ? (
              <span>Remaining: {remainingPercentage}%</span>
            ) : remainingPercentage < 0 ? (
              <span className="text-red-500">Exceeds 100% by {Math.abs(remainingPercentage)}%</span>
            ) : (
              <span className="text-green-500">Perfect distribution (100%)</span>
            )}
          </div>
        </div>
        
        <Card>
          <CardContent className="pt-6">
            <ChartContainer
              config={{
                marketing: {
                  label: "Marketing",
                  color: COLORS[0],
                },
                development: {
                  label: "Development",
                  color: COLORS[1],
                },
                liquidity: {
                  label: "Liquidity",
                  color: COLORS[2],
                },
              }}
              className="h-[200px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={tokenomicsData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({name, percent}) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {tokenomicsData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
      
      <Separator />
      
      <div className="flex items-center space-x-2">
        <Coins className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-medium">Supply & Burn</h3>
      </div>
      
      <div className="grid gap-6">
        <div>
          <Label htmlFor="max-supply">Maximum Supply</Label>
          <Input 
            id="max-supply" 
            value={maxSupply}
            onChange={(e) => setMaxSupply(e.target.value)}
            className="mt-1"
          />
        </div>
        
        <div>
          <div className="flex justify-between mb-2">
            <Label htmlFor="initial-burn">Initial Burn (%)</Label>
            <span className="text-sm">{initialBurn}%</span>
          </div>
          <Slider
            id="initial-burn"
            value={[initialBurn]}
            onValueChange={(value) => setInitialBurn(value[0])}
            max={50}
            min={0}
            step={1}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="burn-mechanism">Enable Burn</Label>\

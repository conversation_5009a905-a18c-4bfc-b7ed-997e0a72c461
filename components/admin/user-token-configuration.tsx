"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { Percent, Save } from "lucide-react"

interface TokenConfigurationSettings {
  minInitialSupply: string
  maxInitialSupply: string
  allowedDecimals: number[]
  defaultDecimals: number
  minTaxRate: number
  maxTaxRate: number
  defaultTaxRate: number
  allowMinting: boolean
  allowBurning: boolean
  allowPausing: boolean
  allowTransferTax: boolean
  requireKYC: boolean
  requireAudit: boolean
  creationFee: string
  creationFeeToken: "SOL" | "BNB" | "ECOSYSTEM"
  suffixEnabled: boolean
  suffixFee: string
  suffixFeeToken: "SOL" | "BNB" | "ECOSYSTEM"
  network: "solana" | "bnb"
}

const defaultSettings: TokenConfigurationSettings = {
  minInitialSupply: "1000000",
  maxInitialSupply: "1000000000000",
  allowedDecimals: [6, 9, 18],
  defaultDecimals: 9,
  minTaxRate: 0,
  maxTaxRate: 10,
  defaultTaxRate: 5,
  allowMinting: true,
  allowBurning: true,
  allowPausing: true,
  allowTransferTax: true,
  requireKYC: false,
  requireAudit: false,
  creationFee: "0.1",
  creationFeeToken: "SOL",
  suffixEnabled: true,
  suffixFee: "1",
  suffixFeeToken: "ECOSYSTEM",
  network: "solana",
}

export default function UserTokenConfiguration() {
  const [settings, setSettings] = useState<TokenConfigurationSettings>(defaultSettings)
  const [isSaving, setIsSaving] = useState(false)
  const { toast } = useToast()

  const handleChange = (field: keyof TokenConfigurationSettings, value: any) => {
    setSettings((prev) => ({ ...prev, [field]: value }))
  }

  const handleDecimalToggle = (decimal: number) => {
    setSettings((prev) => {
      const allowedDecimals = [...prev.allowedDecimals]
      if (allowedDecimals.includes(decimal)) {
        return {
          ...prev,
          allowedDecimals: allowedDecimals.filter((d) => d !== decimal),
          defaultDecimals:
            prev.defaultDecimals === decimal
              ? allowedDecimals.filter((d) => d !== decimal)[0] || 9
              : prev.defaultDecimals,
        }
      } else {
        return {
          ...prev,
          allowedDecimals: [...allowedDecimals, decimal].sort((a, b) => a - b),
        }
      }
    })
  }

  const saveSettings = async () => {
    setIsSaving(true)

    try {
      // Call API to save settings
      const response = await fetch("/api/admin/token-configuration", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Settings Saved",
          description: "Token configuration settings have been updated successfully.",
        })
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to save settings",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error saving settings:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>User Token Configuration</CardTitle>
        <CardDescription>Configure the parameters and restrictions for user-created tokens</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="solana" onValueChange={(value) => handleChange("network", value as "solana" | "bnb")}>
          <TabsList className="mb-4">
            <TabsTrigger value="solana">Solana</TabsTrigger>
            <TabsTrigger value="bnb">BNB Chain</TabsTrigger>
          </TabsList>

          <TabsContent value="solana" className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Supply Settings</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minInitialSupply">Minimum Initial Supply</Label>
                  <Input
                    id="minInitialSupply"
                    value={settings.minInitialSupply}
                    onChange={(e) => handleChange("minInitialSupply", e.target.value)}
                    type="number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxInitialSupply">Maximum Initial Supply</Label>
                  <Input
                    id="maxInitialSupply"
                    value={settings.maxInitialSupply}
                    onChange={(e) => handleChange("maxInitialSupply", e.target.value)}
                    type="number"
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-medium mb-2">Decimal Settings</h3>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {[6, 9, 12, 18].map((decimal) => (
                    <Button
                      key={decimal}
                      variant={settings.allowedDecimals.includes(decimal) ? "default" : "outline"}
                      onClick={() => handleDecimalToggle(decimal)}
                      className="h-8"
                    >
                      {decimal}
                    </Button>
                  ))}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="defaultDecimals">Default Decimals</Label>
                  <Select
                    value={settings.defaultDecimals.toString()}
                    onValueChange={(value) => handleChange("defaultDecimals", Number.parseInt(value))}
                    disabled={settings.allowedDecimals.length === 0}
                  >
                    <SelectTrigger id="defaultDecimals">
                      <SelectValue placeholder="Select default decimals" />
                    </SelectTrigger>
                    <SelectContent>
                      {settings.allowedDecimals.map((decimal) => (
                        <SelectItem key={decimal} value={decimal.toString()}>
                          {decimal}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-medium mb-2">Tax Settings</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="allowTransferTax" className="flex items-center gap-2">
                    <Percent className="h-4 w-4" /> Allow Transfer Tax
                  </Label>
                  <Switch
                    id="allowTransferTax"
                    checked={settings.allowTransferTax}
                    onCheckedChange={(checked) => handleChange("allowTransferTax", checked)}
                  />
                </div>

                {settings.allowTransferTax && (
                  <div className="pl-6 space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="taxRange">Tax Rate Range (%)</Label>
                        <span className="text-sm text-muted-foreground">
                          {settings.minTaxRate}% - {settings.maxTaxRate}%
                        </span>
                      </div>
                      <div className="flex gap-4">
                        <Input
                          value={settings.minTaxRate}
                          onChange={(e) => handleChange("minTaxRate", Number.parseFloat(e.target.value))}
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          className="w-20"
                        />
                        <span className="self-center">-</span>
                        <Input
                          value={settings.maxTaxRate}
                          onChange={(e) => handleChange("maxTaxRate", Number.parseFloat(e.target.value))}
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          className="w-20"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="defaultTaxRate">Default Tax Rate (%)</Label>
                      <Input
                        id="defaultTaxRate"
                        value={settings.defaultTaxRate}
                        onChange={(e) => handleChange("defaultTaxRate", Number.parseFloat(e.target.value))}
                        type="number"
                        min={settings.minTaxRate}
                        max={settings.maxTaxRate}
                        step="0.1"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-medium mb-2">Token Features</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="allowMinting">Allow Minting</Label>
                  <Switch
                    id="allowMinting"
                    checked={settings.allowMinting}
                    onCheckedChange={(checked) => handleChange("allowMinting", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="allowBurning">Allow Burning</Label>
                  <Switch
                    id="allowBurning"
                    checked={settings.allowBurning}
                    onCheckedChange={(checked) => handleChange("allowBurning", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="allowPausing">Allow Pausing</Label>
                  <Switch
                    id="allowPausing"
                    checked={settings.allowPausing}
                    onCheckedChange={(checked) => handleChange("allowPausing", checked)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-medium mb-2">Security Requirements</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="requireKYC">Require KYC</Label>
                  <Switch
                    id="requireKYC"
                    checked={settings.requireKYC}
                    onCheckedChange={(checked) => handleChange("requireKYC", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="requireAudit">Require Security Audit</Label>
                  <Switch
                    id="requireAudit"
                    checked={settings.requireAudit}
                    onCheckedChange={(checked) => handleChange("requireAudit", checked)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-medium mb-2">Fee Settings</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="creationFee">Token Creation Fee</Label>
                    <Input
                      id="creationFee"
                      value={settings.creationFee}
                      onChange={(e) => handleChange("creationFee", e.target.value)}
                      type="number"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="creationFeeToken">Fee Token</Label>
                    <Select
                      value={settings.creationFeeToken}
                      onValueChange={(value) => handleChange("creationFeeToken", value as "SOL" | "BNB" | "ECOSYSTEM")}
                    >
                      <SelectTrigger id="creationFeeToken">
                        <SelectValue placeholder="Select token" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SOL">SOL</SelectItem>
                        <SelectItem value="ECOSYSTEM">Ecosystem Token</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="suffixEnabled">Enable Custom Suffix</Label>
                  <Switch
                    id="suffixEnabled"
                    checked={settings.suffixEnabled}
                    onCheckedChange={(checked) => handleChange("suffixEnabled", checked)}
                  />
                </div>

                {settings.suffixEnabled && (
                  <div className="pl-6 grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="suffixFee">Suffix Fee</Label>
                      <Input
                        id="suffixFee"
                        value={settings.suffixFee}
                        onChange={(e) => handleChange("suffixFee", e.target.value)}
                        type="number"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="suffixFeeToken">Fee Token</Label>
                      <Select
                        value={settings.suffixFeeToken}
                        onValueChange={(value) => handleChange("suffixFeeToken", value as "SOL" | "BNB" | "ECOSYSTEM")}
                      >
                        <SelectTrigger id="suffixFeeToken">
                          <SelectValue placeholder="Select token" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="SOL">SOL</SelectItem>
                          <SelectItem value="ECOSYSTEM">Ecosystem Token</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="bnb" className="space-y-6">
            {/* Similar settings for BNB Chain with appropriate adjustments */}
            <div>
              <h3 className="text-lg font-medium mb-2">Supply Settings</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bnb-minInitialSupply">Minimum Initial Supply</Label>
                  <Input
                    id="bnb-minInitialSupply"
                    value={settings.minInitialSupply}
                    onChange={(e) => handleChange("minInitialSupply", e.target.value)}
                    type="number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bnb-maxInitialSupply">Maximum Initial Supply</Label>
                  <Input
                    id="bnb-maxInitialSupply"
                    value={settings.maxInitialSupply}
                    onChange={(e) => handleChange("maxInitialSupply", e.target.value)}
                    type="number"
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-medium mb-2">Decimal Settings</h3>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {[6, 9, 12, 18].map((decimal) => (
                    <Button
                      key={decimal}
                      variant={settings.allowedDecimals.includes(decimal) ? "default" : "outline"}
                      onClick={() => handleDecimalToggle(decimal)}
                      className="h-8"
                    >
                      {decimal}
                    </Button>
                  ))}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bnb-defaultDecimals">Default Decimals</Label>
                  <Select
                    value={settings.defaultDecimals.toString()}
                    onValueChange={(value) => handleChange("defaultDecimals", Number.parseInt(value))}
                    disabled={settings.allowedDecimals.length === 0}
                  >
                    <SelectTrigger id="bnb-defaultDecimals">
                      <SelectValue placeholder="Select default decimals" />
                    </SelectTrigger>
                    <SelectContent>
                      {settings.allowedDecimals.map((decimal) => (
                        <SelectItem key={decimal} value={decimal.toString()}>
                          {decimal}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Additional BNB-specific settings would go here */}
            <Separator />

            <div>
              <h3 className="text-lg font-medium mb-2">Fee Settings</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bnb-creationFee">Token Creation Fee</Label>
                    <Input
                      id="bnb-creationFee"
                      value={settings.creationFee}
                      onChange={(e) => handleChange("creationFee", e.target.value)}
                      type="number"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bnb-creationFeeToken">Fee Token</Label>
                    <Select
                      value={settings.creationFeeToken}
                      onValueChange={(value) => handleChange("creationFeeToken", value as "SOL" | "BNB" | "ECOSYSTEM")}
                    >
                      <SelectTrigger id="bnb-creationFeeToken">
                        <SelectValue placeholder="Select token" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="BNB">BNB</SelectItem>
                        <SelectItem value="ECOSYSTEM">Ecosystem Token</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-6">
          <Button onClick={saveSettings} disabled={isSaving} className="w-full">
            {isSaving ? (
              <>
                <span className="mr-2 h-4 w-4 animate-spin">...</span>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Configuration
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AlertCircle, CheckCircle2, Plus, Trash2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Badge } from "@/components/ui/badge"

export default function TelegramBotConfig() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Bot configuration state
  const [botEnabled, setBotEnabled] = useState(true)
  const [botToken, setBotToken] = useState("1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ")
  const [adminGroupId, setAdminGroupId] = useState("-1001234567890")
  const [announcementChannelId, setAnnouncementChannelId] = useState("-1001234567891")

  // Security features state
  const [antiSpam, setAntiSpam] = useState(true)
  const [antiScam, setAntiScam] = useState(true)
  const [captcha, setCaptcha] = useState(true)
  const [userVerification, setUserVerification] = useState(true)

  // Commands state
  const [priceCommand, setPriceCommand] = useState(true)
  const [chartCommand, setChartCommand] = useState(true)
  const [websiteCommand, setWebsiteCommand] = useState(true)
  const [socialsCommand, setSocialsCommand] = useState(true)
  const [customCommands, setCustomCommands] = useState<string[]>(["tokenomics", "roadmap", "team"])
  const [newCommand, setNewCommand] = useState("")

  // Add custom command
  const handleAddCommand = () => {
    if (newCommand && !customCommands.includes(newCommand)) {
      setCustomCommands([...customCommands, newCommand])
      setNewCommand("")
    }
  }

  // Remove custom command
  const handleRemoveCommand = (command: string) => {
    setCustomCommands(customCommands.filter((cmd) => cmd !== command))
  }

  const handleSaveSettings = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulate saving settings with a delay
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setSuccess("Telegram bot settings saved successfully!")
      toast({
        title: "Settings Saved",
        description: "Telegram bot settings have been updated successfully.",
      })
    } catch (err) {
      console.error("Error saving Telegram bot settings:", err)
      setError("Failed to save Telegram bot settings. Please try again.")
      toast({
        title: "Error",
        description: "Failed to save Telegram bot settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestBot = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulate testing bot with a delay
      await new Promise((resolve) => setTimeout(resolve, 2000))

      setSuccess("Telegram bot test successful!")
      toast({
        title: "Bot Test Successful",
        description: "Successfully connected to Telegram bot API.",
      })
    } catch (err) {
      console.error("Error testing Telegram bot:", err)
      setError("Failed to connect to Telegram bot. Please check your settings.")
      toast({
        title: "Bot Test Failed",
        description: "Failed to connect to Telegram bot. Please check your settings.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Telegram Bot Configuration</CardTitle>
        <CardDescription>Configure the platform's Telegram bot for community management</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="commands">Commands</TabsTrigger>
          </TabsList>

          {/* General Tab */}
          <TabsContent value="general" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="botEnabled">Enable Telegram Bot</Label>
                <p className="text-sm text-muted-foreground">Enable the platform's Telegram bot</p>
              </div>
              <Switch id="botEnabled" checked={botEnabled} onCheckedChange={setBotEnabled} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="botToken">Bot Token</Label>
              <Input
                id="botToken"
                value={botToken}
                onChange={(e) => setBotToken(e.target.value)}
                placeholder="Enter your Telegram bot token"
                disabled={!botEnabled}
              />
              <p className="text-xs text-muted-foreground">Get your bot token from BotFather on Telegram</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="adminGroupId">Admin Group ID</Label>
              <Input
                id="adminGroupId"
                value={adminGroupId}
                onChange={(e) => setAdminGroupId(e.target.value)}
                placeholder="Enter admin group ID"
                disabled={!botEnabled}
              />
              <p className="text-xs text-muted-foreground">The Telegram group ID for platform administrators</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="announcementChannelId">Announcement Channel ID</Label>
              <Input
                id="announcementChannelId"
                value={announcementChannelId}
                onChange={(e) => setAnnouncementChannelId(e.target.value)}
                placeholder="Enter announcement channel ID"
                disabled={!botEnabled}
              />
              <p className="text-xs text-muted-foreground">The Telegram channel ID for platform announcements</p>
            </div>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="antiSpam">Anti-Spam Protection</Label>
                <p className="text-sm text-muted-foreground">Automatically detect and prevent spam messages</p>
              </div>
              <Switch id="antiSpam" checked={antiSpam} onCheckedChange={setAntiSpam} disabled={!botEnabled} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="antiScam">Anti-Scam Protection</Label>
                <p className="text-sm text-muted-foreground">Detect and block potential scam attempts</p>
              </div>
              <Switch id="antiScam" checked={antiScam} onCheckedChange={setAntiScam} disabled={!botEnabled} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="captcha">CAPTCHA Verification</Label>
                <p className="text-sm text-muted-foreground">Require new users to complete a CAPTCHA</p>
              </div>
              <Switch id="captcha" checked={captcha} onCheckedChange={setCaptcha} disabled={!botEnabled} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="userVerification">User Verification</Label>
                <p className="text-sm text-muted-foreground">Verify new users before allowing them to post</p>
              </div>
              <Switch
                id="userVerification"
                checked={userVerification}
                onCheckedChange={setUserVerification}
                disabled={!botEnabled}
              />
            </div>
          </TabsContent>

          {/* Commands Tab */}
          <TabsContent value="commands" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="priceCommand">Price Command</Label>
                <p className="text-sm text-muted-foreground">Enable /price command to show token price</p>
              </div>
              <Switch
                id="priceCommand"
                checked={priceCommand}
                onCheckedChange={setPriceCommand}
                disabled={!botEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="chartCommand">Chart Command</Label>
                <p className="text-sm text-muted-foreground">Enable /chart command to show token price chart</p>
              </div>
              <Switch
                id="chartCommand"
                checked={chartCommand}
                onCheckedChange={setChartCommand}
                disabled={!botEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="websiteCommand">Website Command</Label>
                <p className="text-sm text-muted-foreground">Enable /website command to show project website</p>
              </div>
              <Switch
                id="websiteCommand"
                checked={websiteCommand}
                onCheckedChange={setWebsiteCommand}
                disabled={!botEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="socialsCommand">Socials Command</Label>
                <p className="text-sm text-muted-foreground">Enable /socials command to show social media links</p>
              </div>
              <Switch
                id="socialsCommand"
                checked={socialsCommand}
                onCheckedChange={setSocialsCommand}
                disabled={!botEnabled}
              />
            </div>

            <div className="space-y-2 mt-4">
              <Label>Custom Commands</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {customCommands.map((command) => (
                  <Badge key={command} className="flex items-center gap-1 px-3 py-1">
                    /{command}
                    <button onClick={() => handleRemoveCommand(command)} className="ml-1 text-muted hover:text-primary">
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
              <div className="flex space-x-2">
                <Input
                  placeholder="New command name"
                  value={newCommand}
                  onChange={(e) => setNewCommand(e.target.value)}
                  disabled={!botEnabled}
                />
                <Button type="button" size="icon" onClick={handleAddCommand} disabled={!botEnabled || !newCommand}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Add custom commands for your Telegram bot (without the / prefix)
              </p>
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mt-4 bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleTestBot} disabled={isLoading || !botEnabled}>
          Test Bot Connection
        </Button>
        <Button onClick={handleSaveSettings} disabled={isLoading || !botEnabled}>
          {isLoading ? "Saving..." : "Save Settings"}
        </Button>
      </CardFooter>
    </Card>
  )
}

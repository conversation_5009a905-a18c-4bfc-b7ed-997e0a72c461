"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

export default function AdminPlatformSettings() {
  const [networkType, setNetworkType] = useState("devnet")
  const [platformFee, setPlatformFee] = useState([2.5])
  const [maintenanceMode, setMaintenanceMode] = useState(false)
  const [tokenCreationEnabled, setTokenCreationEnabled] = useState(true)
  const [stakingEnabled, setStakingEnabled] = useState(true)
  const [marketEnabled, setMarketEnabled] = useState(true)
  const [governanceEnabled, setGovernanceEnabled] = useState(false)
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Simulate saving settings with a delay
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setSuccess("Platform settings saved successfully!")
    } catch (err) {
      console.error("Error saving settings:", err)
      setError("Failed to save settings. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid grid-cols-1 md:grid-cols-4 w-full">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="modules">Modules</TabsTrigger>
          <TabsTrigger value="fees">Fees & Pricing</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Configure basic platform settings</CardDescription>
            </CardHeader>
            <form onSubmit={handleSaveSettings}>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="networkType">Network Type</Label>
                  <Select value={networkType} onValueChange={setNetworkType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select network" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="devnet">Solana Devnet</SelectItem>
                      <SelectItem value="testnet">Solana Testnet</SelectItem>
                      <SelectItem value="mainnet">Solana Mainnet</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">The Solana network that the platform will operate on</p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                    <p className="text-sm text-muted-foreground">Temporarily disable all platform features</p>
                  </div>
                  <Switch id="maintenanceMode" checked={maintenanceMode} onCheckedChange={setMaintenanceMode} />
                </div>

                {maintenanceMode && (
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceMessage">Maintenance Message</Label>
                    <Textarea
                      id="maintenanceMessage"
                      placeholder="Enter message to display during maintenance"
                      className="min-h-[100px]"
                      defaultValue="The platform is currently undergoing scheduled maintenance. We'll be back shortly. Thank you for your patience."
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="rpcEndpoint">Custom RPC Endpoint</Label>
                  <Input id="rpcEndpoint" defaultValue="https://api.devnet.solana.com" disabled className="bg-muted" />
                  <p className="text-sm text-muted-foreground">
                    Solana Devnet RPC endpoint for all blockchain interactions
                  </p>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="bg-green-50 text-green-800 border-green-200">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <AlertTitle>Success</AlertTitle>
                    <AlertDescription>{success}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Saving..." : "Save Settings"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="modules" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Module Settings</CardTitle>
              <CardDescription>Enable or disable platform modules</CardDescription>
            </CardHeader>
            <form onSubmit={handleSaveSettings}>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="tokenCreationEnabled">Token Creation</Label>
                    <p className="text-sm text-muted-foreground">Allow users to create custom tokens</p>
                  </div>
                  <Switch
                    id="tokenCreationEnabled"
                    checked={tokenCreationEnabled}
                    onCheckedChange={setTokenCreationEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="stakingEnabled">Staking</Label>
                    <p className="text-sm text-muted-foreground">Allow users to stake tokens for rewards</p>
                  </div>
                  <Switch id="stakingEnabled" checked={stakingEnabled} onCheckedChange={setStakingEnabled} />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="marketEnabled">Market</Label>
                    <p className="text-sm text-muted-foreground">Enable trading and market data</p>
                  </div>
                  <Switch id="marketEnabled" checked={marketEnabled} onCheckedChange={setMarketEnabled} />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="governanceEnabled">Governance</Label>
                    <p className="text-sm text-muted-foreground">Enable DAO voting and proposals</p>
                  </div>
                  <Switch id="governanceEnabled" checked={governanceEnabled} onCheckedChange={setGovernanceEnabled} />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="analyticsEnabled">Analytics</Label>
                    <p className="text-sm text-muted-foreground">Enable detailed analytics and reporting</p>
                  </div>
                  <Switch id="analyticsEnabled" checked={analyticsEnabled} onCheckedChange={setAnalyticsEnabled} />
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="bg-green-50 text-green-800 border-green-200">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <AlertTitle>Success</AlertTitle>
                    <AlertDescription>{success}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Saving..." : "Save Settings"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="fees" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Fees & Pricing</CardTitle>
              <CardDescription>Configure platform fees and pricing options</CardDescription>
            </CardHeader>
            <form onSubmit={handleSaveSettings}>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="platformFee">Platform Fee ({platformFee}%)</Label>
                  </div>
                  <Slider
                    id="platformFee"
                    min={0}
                    max={10}
                    step={0.1}
                    value={platformFee}
                    onValueChange={setPlatformFee}
                  />
                  <p className="text-sm text-muted-foreground">
                    Fee charged on all transactions processed through the platform
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="basicPrice">Basic Token Creation Price</Label>
                  <Input id="basicPrice" type="number" min="0" step="0.01" defaultValue="0.1" />
                  <p className="text-sm text-muted-foreground">
                    Price in SOL for basic token creation (network fee + platform fee)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="premiumPrice">Premium Token Creation Price</Label>
                  <Input id="premiumPrice" type="number" min="0" step="0.01" defaultValue="0.5" />
                  <p className="text-sm text-muted-foreground">
                    Price in SOL for premium token creation with all features (staking, presale, etc.)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stakingFee">Staking Fee</Label>
                  <Input id="stakingFee" type="number" min="0" step="0.01" defaultValue="0.05" />
                  <p className="text-sm text-muted-foreground">Fee in SOL for setting up staking pools</p>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="bg-green-50 text-green-800 border-green-200">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <AlertTitle>Success</AlertTitle>
                    <AlertDescription>{success}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Saving..." : "Save Settings"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Configure platform security options</CardDescription>
            </CardHeader>
            <form onSubmit={handleSaveSettings}>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="adminWallets">Admin Wallets</Label>
                  <Textarea
                    id="adminWallets"
                    placeholder="Enter admin wallet addresses (one per line)"
                    className="min-h-[100px]"
                    defaultValue="8xyt45jkLmn9PQvDTbW23jkLmNpQrStUv1234567890"
                  />
                  <p className="text-sm text-muted-foreground">
                    Wallet addresses that have admin access to the platform
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="blacklistedWallets">Blacklisted Wallets</Label>
                  <Textarea
                    id="blacklistedWallets"
                    placeholder="Enter blacklisted wallet addresses (one per line)"
                    className="min-h-[100px]"
                  />
                  <p className="text-sm text-muted-foreground">
                    Wallet addresses that are blocked from using the platform
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="autoBlacklist">Automatic Blacklisting</Label>
                    <p className="text-sm text-muted-foreground">Automatically blacklist suspicious wallets</p>
                  </div>
                  <Switch id="autoBlacklist" defaultChecked={true} />
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="bg-green-50 text-green-800 border-green-200">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <AlertTitle>Success</AlertTitle>
                    <AlertDescription>{success}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Saving..." : "Save Settings"}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

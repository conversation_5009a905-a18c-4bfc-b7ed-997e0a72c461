"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"

export default function EnvVariablesGuide() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Guide des Variables d'Environnement</CardTitle>
        <CardDescription>
          Configuration des variables d'environnement pour les adresses des contrats et la génération de tokens avec
          suffixes personnalisés
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs defaultValue="structure">
          <TabsList>
            <TabsTrigger value="structure">Structure</TabsTrigger>
            <TabsTrigger value="suffix">Suffixes Personnalisés</TabsTrigger>
            <TabsTrigger value="example">.env Exemple</TabsTrigger>
          </TabsList>

          <TabsContent value="structure" className="space-y-4 pt-4">
            <Alert>
              <InfoIcon className="h-4 w-4" />
              <AlertTitle>Information</AlertTitle>
              <AlertDescription>
                Les variables d'environnement sont maintenant séparées par réseau (testnet/mainnet) pour une meilleure
                flexibilité.
              </AlertDescription>
            </Alert>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Variable</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Utilisation</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-mono text-xs">NEXT_PUBLIC_BNB_TOKEN_FACTORY_TESTNET</TableCell>
                  <TableCell>Adresse du contrat factory de tokens sur BNB Testnet</TableCell>
                  <TableCell>Création de tokens sur le testnet</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-mono text-xs">NEXT_PUBLIC_BNB_TOKEN_FACTORY_MAINNET</TableCell>
                  <TableCell>Adresse du contrat factory de tokens sur BNB Mainnet</TableCell>
                  <TableCell>Création de tokens sur le mainnet</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-mono text-xs">NEXT_PUBLIC_PANCAKESWAP_ROUTER_TESTNET</TableCell>
                  <TableCell>Adresse du routeur PancakeSwap sur BNB Testnet</TableCell>
                  <TableCell>Ajout de liquidité sur le testnet</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-mono text-xs">NEXT_PUBLIC_PANCAKESWAP_ROUTER_MAINNET</TableCell>
                  <TableCell>Adresse du routeur PancakeSwap sur BNB Mainnet</TableCell>
                  <TableCell>Ajout de liquidité sur le mainnet</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="suffix" className="space-y-4 pt-4">
            <Alert>
              <InfoIcon className="h-4 w-4" />
              <AlertTitle>Génération d'adresses avec suffixes personnalisés</AlertTitle>
              <AlertDescription>
                Le système génère automatiquement des adresses de tokens avec des suffixes personnalisés en utilisant
                différentes méthodes selon la blockchain.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Solana</h3>
                <p className="text-sm text-muted-foreground">
                  Pour Solana, le système utilise une technique de "grinding" qui génère des paires de clés jusqu'à
                  trouver une adresse publique se terminant par le suffixe souhaité. Ces paires de clés sont ensuite
                  stockées de manière sécurisée pour être utilisées lors de la création de tokens.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">BNB Chain</h3>
                <p className="text-sm text-muted-foreground">
                  Pour BNB Chain, le système utilise la méthode CREATE2 d'Ethereum qui permet de prédire l'adresse d'un
                  contrat avant son déploiement. Le système génère différents "salt" jusqu'à trouver une adresse qui se
                  termine par le suffixe souhaité. Le contrat factory utilise ensuite ce salt pour déployer le token à
                  l'adresse prédite.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Configuration</h3>
                <p className="text-sm text-muted-foreground">
                  Les suffixes sont configurés dans le panneau d'administration sous "Suffix Configuration". Vous pouvez
                  définir différents suffixes pour chaque réseau (par exemple, "GF" pour le mainnet et "BETAGF" pour le
                  testnet).
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="example" className="space-y-4 pt-4">
            <div className="bg-muted p-4 rounded-md">
              <pre className="text-xs overflow-auto">
                {`# BNB Chain Configuration
NEXT_PUBLIC_BNB_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545
NEXT_PUBLIC_BNB_TOKEN_FACTORY_TESTNET=0x0000000000000000000000000000000000000000
NEXT_PUBLIC_BNB_TOKEN_FACTORY_MAINNET=0x0000000000000000000000000000000000000000
NEXT_PUBLIC_PANCAKESWAP_ROUTER_TESTNET=0x9Ac64Cc6e4415144C455BD8E4837Fea55603e5c3
NEXT_PUBLIC_PANCAKESWAP_ROUTER_MAINNET=0x10ED43C718714eb63d5aA57B78B54704E256024E`}
              </pre>
            </div>

            <Alert>
              <InfoIcon className="h-4 w-4" />
              <AlertTitle>Note</AlertTitle>
              <AlertDescription>
                Remplacez les adresses "0x0000000000000000000000000000000000000000" par les adresses réelles de vos
                contrats factory déployés.
              </AlertDescription>
            </Alert>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

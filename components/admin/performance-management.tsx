"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"
import { AlertCircle, CheckCircle, Clock, Database, Download, FileText, RefreshCw, Zap } from "lucide-react"

interface PerformanceMetric {
  id: string
  name: string
  value: number
  unit: string
  status: "good" | "warning" | "critical"
  trend: "up" | "down" | "stable"
  history: { timestamp: string; value: number }[]
}

interface ResourceUsage {
  id: string
  name: string
  current: number
  max: number
  unit: string
  status: "good" | "warning" | "critical"
}

export default function PerformanceManagement() {
  const { toast } = useToast()
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [resources, setResources] = useState<ResourceUsage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [optimizationProgress, setOptimizationProgress] = useState(0)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    const fetchPerformanceData = async () => {
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données simulées
        const mockMetrics: PerformanceMetric[] = [
          {
            id: "api_response_time",
            name: "Temps de réponse API",
            value: 120,
            unit: "ms",
            status: "good",
            trend: "down",
            history: Array.from({ length: 24 }, (_, i) => ({
              timestamp: new Date(Date.now() - i * 3600000).toISOString(),
              value: 120 + Math.random() * 50 - 25,
            })),
          },
          {
            id: "token_creation_time",
            name: "Temps de création de token",
            value: 3.2,
            unit: "s",
            status: "warning",
            trend: "up",
            history: Array.from({ length: 24 }, (_, i) => ({
              timestamp: new Date(Date.now() - i * 3600000).toISOString(),
              value: 3.2 + Math.random() * 1 - 0.5,
            })),
          },
          {
            id: "transaction_throughput",
            name: "Débit de transactions",
            value: 450,
            unit: "tx/min",
            status: "good",
            trend: "stable",
            history: Array.from({ length: 24 }, (_, i) => ({
              timestamp: new Date(Date.now() - i * 3600000).toISOString(),
              value: 450 + Math.random() * 50 - 25,
            })),
          },
          {
            id: "database_query_time",
            name: "Temps de requête BDD",
            value: 85,
            unit: "ms",
            status: "good",
            trend: "down",
            history: Array.from({ length: 24 }, (_, i) => ({
              timestamp: new Date(Date.now() - i * 3600000).toISOString(),
              value: 85 + Math.random() * 20 - 10,
            })),
          },
          {
            id: "blockchain_sync_time",
            name: "Temps de synchronisation blockchain",
            value: 5.8,
            unit: "s",
            status: "warning",
            trend: "up",
            history: Array.from({ length: 24 }, (_, i) => ({
              timestamp: new Date(Date.now() - i * 3600000).toISOString(),
              value: 5.8 + Math.random() * 2 - 1,
            })),
          },
          {
            id: "page_load_time",
            name: "Temps de chargement des pages",
            value: 1.2,
            unit: "s",
            status: "good",
            trend: "down",
            history: Array.from({ length: 24 }, (_, i) => ({
              timestamp: new Date(Date.now() - i * 3600000).toISOString(),
              value: 1.2 + Math.random() * 0.5 - 0.25,
            })),
          },
        ]

        const mockResources: ResourceUsage[] = [
          {
            id: "cpu",
            name: "CPU",
            current: 35,
            max: 100,
            unit: "%",
            status: "good",
          },
          {
            id: "memory",
            name: "Mémoire",
            current: 6.2,
            max: 8,
            unit: "GB",
            status: "warning",
          },
          {
            id: "disk",
            name: "Espace disque",
            current: 120,
            max: 500,
            unit: "GB",
            status: "good",
          },
          {
            id: "bandwidth",
            name: "Bande passante",
            current: 75,
            max: 100,
            unit: "Mbps",
            status: "good",
          },
          {
            id: "database_connections",
            name: "Connexions BDD",
            current: 85,
            max: 100,
            unit: "",
            status: "warning",
          },
          {
            id: "rpc_connections",
            name: "Connexions RPC",
            current: 120,
            max: 200,
            unit: "",
            status: "good",
          },
        ]

        setMetrics(mockMetrics)
        setResources(mockResources)
        setIsLoading(false)
      } catch (error) {
        console.error("Erreur lors du chargement des données de performance:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger les données de performance",
          variant: "destructive",
        })
        setIsLoading(false)
      }
    }

    fetchPerformanceData()

    // Simuler des mises à jour en temps réel
    const interval = setInterval(() => {
      if (!isLoading) {
        setMetrics((prev) =>
          prev.map((metric) => ({
            ...metric,
            value: metric.value + (Math.random() * 0.1 - 0.05) * metric.value,
            history: [
              {
                timestamp: new Date().toISOString(),
                value: metric.value + (Math.random() * 0.1 - 0.05) * metric.value,
              },
              ...metric.history.slice(0, 23),
            ],
          })),
        )

        setResources((prev) =>
          prev.map((resource) => ({
            ...resource,
            current: Math.min(
              resource.max,
              Math.max(0, resource.current + (Math.random() * 0.05 - 0.025) * resource.current),
            ),
            status:
              resource.current > resource.max * 0.8
                ? "warning"
                : resource.current > resource.max * 0.9
                  ? "critical"
                  : "good",
          })),
        )
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [toast, isLoading])

  const handleOptimize = async () => {
    setIsOptimizing(true)
    setOptimizationProgress(0)

    // Simuler une optimisation
    for (let i = 1; i <= 10; i++) {
      await new Promise((resolve) => setTimeout(resolve, 500))
      setOptimizationProgress(i * 10)
    }

    // Simuler des améliorations
    setMetrics((prev) =>
      prev.map((metric) => ({
        ...metric,
        value:
          metric.name.includes("temps") || metric.name.includes("Time")
            ? metric.value * 0.8
            : // Réduire les temps de 20%
              metric.value * 1.1, // Augmenter les débits de 10%
        status: "good",
        trend: metric.name.includes("temps") || metric.name.includes("Time") ? "down" : "up",
      })),
    )

    setResources((prev) =>
      prev.map((resource) => ({
        ...resource,
        current: resource.current * 0.7, // Réduire l'utilisation des ressources de 30%
        status: "good",
      })),
    )

    setIsOptimizing(false)

    toast({
      title: "Optimisation réussie",
      description: "Les performances de la plateforme ont été optimisées avec succès",
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good":
        return "text-green-500"
      case "warning":
        return "text-amber-500"
      case "critical":
        return "text-red-500"
      default:
        return ""
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <span className="text-green-500">↑</span>
      case "down":
        return <span className="text-red-500">↓</span>
      case "stable":
        return <span className="text-gray-500">→</span>
      default:
        return null
    }
  }

  const getResourceUsageColor = (current: number, max: number) => {
    const percentage = (current / max) * 100
    if (percentage > 90) return "bg-red-500"
    if (percentage > 70) return "bg-amber-500"
    return "bg-green-500"
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Gestion des Performances</h2>
          <p className="text-muted-foreground">Surveillez et optimisez les performances de la plateforme</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Rapport
          </Button>
          <Button onClick={handleOptimize} disabled={isOptimizing} className="flex items-center gap-2">
            {isOptimizing ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Optimisation...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4" />
                Optimiser
              </>
            )}
          </Button>
        </div>
      </div>

      {isOptimizing && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Optimisation en cours</CardTitle>
            <CardDescription>Veuillez patienter pendant l'optimisation des performances</CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={optimizationProgress} className="h-2" />
            <div className="flex justify-between mt-2 text-xs text-muted-foreground">
              <span>Analyse</span>
              <span>Optimisation des requêtes</span>
              <span>Nettoyage du cache</span>
              <span>Optimisation des ressources</span>
              <span>Finalisation</span>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Clock className="h-5 w-5 mr-2 text-blue-500" />
              Temps de réponse moyen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {metrics.find((m) => m.id === "api_response_time")?.value.toFixed(0)} ms
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <span className={getStatusColor(metrics.find((m) => m.id === "api_response_time")?.status || "")}>
                {getTrendIcon(metrics.find((m) => m.id === "api_response_time")?.trend || "")}
              </span>
              <span className="ml-1">
                {metrics.find((m) => m.id === "api_response_time")?.trend === "down"
                  ? "Amélioration"
                  : metrics.find((m) => m.id === "api_response_time")?.trend === "up"
                    ? "Dégradation"
                    : "Stable"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Zap className="h-5 w-5 mr-2 text-amber-500" />
              Débit de transactions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {metrics.find((m) => m.id === "transaction_throughput")?.value.toFixed(0)} tx/min
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <span className={getStatusColor(metrics.find((m) => m.id === "transaction_throughput")?.status || "")}>
                {getTrendIcon(metrics.find((m) => m.id === "transaction_throughput")?.trend || "")}
              </span>
              <span className="ml-1">
                {metrics.find((m) => m.id === "transaction_throughput")?.trend === "up"
                  ? "Amélioration"
                  : metrics.find((m) => m.id === "transaction_throughput")?.trend === "down"
                    ? "Dégradation"
                    : "Stable"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Database className="h-5 w-5 mr-2 text-purple-500" />
              Temps de requête BDD
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {metrics.find((m) => m.id === "database_query_time")?.value.toFixed(0)} ms
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <span className={getStatusColor(metrics.find((m) => m.id === "database_query_time")?.status || "")}>
                {getTrendIcon(metrics.find((m) => m.id === "database_query_time")?.trend || "")}
              </span>
              <span className="ml-1">
                {metrics.find((m) => m.id === "database_query_time")?.trend === "down"
                  ? "Amélioration"
                  : metrics.find((m) => m.id === "database_query_time")?.trend === "up"
                    ? "Dégradation"
                    : "Stable"}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="metrics">Métriques détaillées</TabsTrigger>
          <TabsTrigger value="resources">Utilisation des ressources</TabsTrigger>
          <TabsTrigger value="recommendations">Recommandations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>État des performances</CardTitle>
              <CardDescription>Vue d'ensemble des performances de la plateforme</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium mb-2">Métriques clés</h3>
                  <div className="space-y-2">
                    {metrics.map((metric) => (
                      <div key={metric.id} className="flex items-center justify-between">
                        <span className="text-sm">{metric.name}</span>
                        <div className="flex items-center">
                          <span className={`font-medium ${getStatusColor(metric.status)}`}>
                            {metric.value.toFixed(1)} {metric.unit}
                          </span>
                          <span className="ml-2">{getTrendIcon(metric.trend)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Utilisation des ressources</h3>
                  <div className="space-y-4">
                    {resources.map((resource) => (
                      <div key={resource.id} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>{resource.name}</span>
                          <span className={getStatusColor(resource.status)}>
                            {resource.current.toFixed(1)}/{resource.max} {resource.unit}
                          </span>
                        </div>
                        <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={`h-full ${getResourceUsageColor(resource.current, resource.max)}`}
                            style={{ width: `${(resource.current / resource.max) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {metrics.some((m) => m.status === "critical") && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Problèmes critiques détectés</AlertTitle>
                  <AlertDescription>
                    Des problèmes de performance critiques ont été détectés. Veuillez consulter les recommandations pour
                    résoudre ces problèmes.
                  </AlertDescription>
                </Alert>
              )}

              {metrics.some((m) => m.status === "warning") && !metrics.some((m) => m.status === "critical") && (
                <Alert variant="warning">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Avertissements de performance</AlertTitle>
                  <AlertDescription>
                    Certaines métriques de performance sont sous-optimales. Consultez les recommandations pour améliorer
                    les performances.
                  </AlertDescription>
                </Alert>
              )}

              {metrics.every((m) => m.status === "good") && (
                <Alert className="bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <AlertTitle>Performances optimales</AlertTitle>
                  <AlertDescription>
                    Toutes les métriques de performance sont dans les plages optimales.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Métriques détaillées</CardTitle>
              <CardDescription>Analyse détaillée des métriques de performance</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Métrique</TableHead>
                    <TableHead>Valeur actuelle</TableHead>
                    <TableHead>Moyenne (24h)</TableHead>
                    <TableHead>Min (24h)</TableHead>
                    <TableHead>Max (24h)</TableHead>
                    <TableHead>Tendance</TableHead>
                    <TableHead>Statut</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {metrics.map((metric) => {
                    const values = metric.history.map((h) => h.value)
                    const avg = values.reduce((a, b) => a + b, 0) / values.length
                    const min = Math.min(...values)
                    const max = Math.max(...values)

                    return (
                      <TableRow key={metric.id}>
                        <TableCell className="font-medium">{metric.name}</TableCell>
                        <TableCell>
                          {metric.value.toFixed(1)} {metric.unit}
                        </TableCell>
                        <TableCell>
                          {avg.toFixed(1)} {metric.unit}
                        </TableCell>
                        <TableCell>
                          {min.toFixed(1)} {metric.unit}
                        </TableCell>
                        <TableCell>
                          {max.toFixed(1)} {metric.unit}
                        </TableCell>
                        <TableCell>{getTrendIcon(metric.trend)}</TableCell>
                        <TableCell>
                          <span className={getStatusColor(metric.status)}>
                            {metric.status === "good"
                              ? "Bon"
                              : metric.status === "warning"
                                ? "Avertissement"
                                : "Critique"}
                          </span>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Utilisation des ressources</CardTitle>
              <CardDescription>Analyse détaillée de l'utilisation des ressources système</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {resources.map((resource) => (
                <div key={resource.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{resource.name}</h3>
                    <span className={getStatusColor(resource.status)}>
                      {resource.current.toFixed(1)}/{resource.max} {resource.unit} (
                      {((resource.current / resource.max) * 100).toFixed(0)}%)
                    </span>
                  </div>
                  <div className="h-4 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${getResourceUsageColor(resource.current, resource.max)}`}
                      style={{ width: `${(resource.current / resource.max) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {resource.status === "good"
                      ? "Utilisation normale"
                      : resource.status === "warning"
                        ? "Utilisation élevée - Envisagez d'optimiser"
                        : "Utilisation critique - Action requise"}
                  </p>
                </div>
              ))}

              <div className="flex justify-end">
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Télécharger le rapport complet
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recommandations d'optimisation</CardTitle>
              <CardDescription>Suggestions pour améliorer les performances de la plateforme</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="p-4 border rounded-md bg-amber-50 border-amber-200">
                  <h3 className="font-medium flex items-center text-amber-800">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Optimiser le temps de création de token
                  </h3>
                  <p className="mt-1 text-sm text-amber-700">
                    Le temps de création de token est supérieur à la valeur cible de 2 secondes. Envisagez d'optimiser
                    le processus de création de token en mettant en cache les données fréquemment utilisées et en
                    parallélisant certaines opérations.
                  </p>
                  <div className="mt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-amber-800 border-amber-300 bg-amber-100 hover:bg-amber-200"
                    >
                      Appliquer l'optimisation
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-md bg-amber-50 border-amber-200">
                  <h3 className="font-medium flex items-center text-amber-800">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Optimiser le temps de synchronisation blockchain
                  </h3>
                  <p className="mt-1 text-sm text-amber-700">
                    Le temps de synchronisation blockchain est élevé. Envisagez d'utiliser un nœud RPC plus rapide ou
                    d'optimiser les requêtes blockchain en utilisant des techniques de mise en cache et de regroupement.
                  </p>
                  <div className="mt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-amber-800 border-amber-300 bg-amber-100 hover:bg-amber-200"
                    >
                      Appliquer l'optimisation
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-md bg-amber-50 border-amber-200">
                  <h3 className="font-medium flex items-center text-amber-800">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Optimiser l'utilisation de la mémoire
                  </h3>
                  <p className="mt-1 text-sm text-amber-700">
                    L'utilisation de la mémoire est élevée (77.5%). Envisagez d'optimiser l'utilisation de la mémoire en
                    identifiant et en résolvant les fuites de mémoire potentielles, en optimisant les structures de
                    données et en ajustant les paramètres du garbage collector.
                  </p>
                  <div className="mt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-amber-800 border-amber-300 bg-amber-100 hover:bg-amber-200"
                    >
                      Appliquer l'optimisation
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-md bg-amber-50 border-amber-200">
                  <h3 className="font-medium flex items-center text-amber-800">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Optimiser les connexions à la base de données
                  </h3>
                  <p className="mt-1 text-sm text-amber-700">
                    Le nombre de connexions à la base de données est élevé (85%). Envisagez d'implémenter un système de
                    pooling de connexions plus efficace et d'optimiser les requêtes pour réduire le temps de connexion.
                  </p>
                  <div className="mt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-amber-800 border-amber-300 bg-amber-100 hover:bg-amber-200"
                    >
                      Appliquer l'optimisation
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-md bg-green-50 border-green-200">
                  <h3 className="font-medium flex items-center text-green-800">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Temps de réponse API optimal
                  </h3>
                  <p className="mt-1 text-sm text-green-700">
                    Le temps de réponse API est dans la plage optimale. Continuez à surveiller pour maintenir ces bonnes
                    performances.
                  </p>
                </div>
              </div>

              <div className="mt-6">
                <Button className="w-full" onClick={handleOptimize} disabled={isOptimizing}>
                  {isOptimizing ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Optimisation en cours...
                    </>
                  ) : (
                    <>
                      <Zap className="mr-2 h-4 w-4" />
                      Appliquer toutes les optimisations recommandées
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

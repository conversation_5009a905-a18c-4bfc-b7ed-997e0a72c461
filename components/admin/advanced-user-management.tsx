"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  Dialog<PERSON>it<PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  ArrowUpDown,
  Ban,
  Check,
  Copy,
  Edit,
  Eye,
  Loader2,
  MoreHorizontal,
  Search,
  Trash2,
  UserPlus,
} from "lucide-react"

interface User {
  id: string
  walletAddress: string
  joinedAt: string
  lastActive: string
  tokensCreated: number
  transactionsCount: number
  status: "active" | "inactive" | "banned"
  role: "user" | "admin" | "superadmin"
}

export function AdvancedUserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isUserDialogOpen, setIsUserDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isBanDialogOpen, setIsBanDialogOpen] = useState(false)
  const [sortConfig, setSortConfig] = useState<{ key: keyof User; direction: "asc" | "desc" } | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchUsers()
  }, [])

  useEffect(() => {
    if (users.length > 0) {
      filterUsers()
    }
  }, [searchQuery, users])

  const fetchUsers = async () => {
    setIsLoading(true)
    try {
      // Simuler un appel API
      setTimeout(() => {
        // Données fictives pour la démonstration
        const mockUsers: User[] = Array.from({ length: 20 }, (_, i) => ({
          id: `user-${i + 1}`,
          walletAddress: `${generateRandomWalletAddress()}`,
          joinedAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
          lastActive: new Date(Date.now() - Math.random() * 1000000000).toISOString(),
          tokensCreated: Math.floor(Math.random() * 10),
          transactionsCount: Math.floor(Math.random() * 100),
          status: ["active", "inactive", "banned"][Math.floor(Math.random() * 3)] as "active" | "inactive" | "banned",
          role: ["user", "admin", "superadmin"][Math.floor(Math.random() * 3)] as "user" | "admin" | "superadmin",
        }))

        setUsers(mockUsers)
        setFilteredUsers(mockUsers)
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error("Error fetching users:", error)
      toast({
        title: "Erreur",
        description: "Impossible de charger les utilisateurs",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const generateRandomWalletAddress = () => {
    const chars = "**********************************************************"
    let result = ""
    for (let i = 0; i < 44; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  const filterUsers = () => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users)
      return
    }

    const query = searchQuery.toLowerCase()
    const filtered = users.filter(
      (user) =>
        user.walletAddress.toLowerCase().includes(query) ||
        user.status.toLowerCase().includes(query) ||
        user.role.toLowerCase().includes(query),
    )
    setFilteredUsers(filtered)
  }

  const handleSort = (key: keyof User) => {
    let direction: "asc" | "desc" = "asc"

    if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc"
    }

    setSortConfig({ key, direction })

    const sortedUsers = [...filteredUsers].sort((a, b) => {
      if (a[key] < b[key]) return direction === "asc" ? -1 : 1
      if (a[key] > b[key]) return direction === "asc" ? 1 : -1
      return 0
    })

    setFilteredUsers(sortedUsers)
  }

  const handleViewUser = (user: User) => {
    setSelectedUser(user)
    setIsUserDialogOpen(true)
  }

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user)
    setIsDeleteDialogOpen(true)
  }

  const handleBanUser = (user: User) => {
    setSelectedUser(user)
    setIsBanDialogOpen(true)
  }

  const confirmDeleteUser = () => {
    if (!selectedUser) return

    // Simuler la suppression
    setUsers(users.filter((user) => user.id !== selectedUser.id))
    setFilteredUsers(filteredUsers.filter((user) => user.id !== selectedUser.id))

    toast({
      title: "Utilisateur supprimé",
      description: `L'utilisateur ${truncateAddress(selectedUser.walletAddress)} a été supprimé avec succès.`,
    })

    setIsDeleteDialogOpen(false)
    setSelectedUser(null)
  }

  const confirmBanUser = () => {
    if (!selectedUser) return

    // Simuler le bannissement
    const updatedUsers = users.map((user) => (user.id === selectedUser.id ? { ...user, status: "banned" } : user))

    setUsers(updatedUsers)
    setFilteredUsers(filteredUsers.map((user) => (user.id === selectedUser.id ? { ...user, status: "banned" } : user)))

    toast({
      title: "Utilisateur banni",
      description: `L'utilisateur ${truncateAddress(selectedUser.walletAddress)} a été banni avec succès.`,
    })

    setIsBanDialogOpen(false)
    setSelectedUser(null)
  }

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Actif</Badge>
      case "inactive":
        return <Badge variant="outline">Inactif</Badge>
      case "banned":
        return <Badge variant="destructive">Banni</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "superadmin":
        return <Badge className="bg-purple-500">Super Admin</Badge>
      case "admin":
        return <Badge className="bg-blue-500">Admin</Badge>
      case "user":
        return <Badge variant="outline">Utilisateur</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Gestion des utilisateurs</CardTitle>
          <CardDescription>Gérez les utilisateurs de la plateforme</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="space-y-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <TabsList>
                <TabsTrigger value="all">Tous</TabsTrigger>
                <TabsTrigger value="active">Actifs</TabsTrigger>
                <TabsTrigger value="inactive">Inactifs</TabsTrigger>
                <TabsTrigger value="banned">Bannis</TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2 w-full sm:w-auto">
                <div className="relative w-full sm:w-auto">
                  <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8 w-full sm:w-[250px]"
                  />
                </div>
                <Button variant="outline" size="icon" onClick={fetchUsers}>
                  <Loader2 className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
                </Button>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Ajouter
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Ajouter un utilisateur</DialogTitle>
                      <DialogDescription>Ajoutez un nouvel utilisateur à la plateforme</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid gap-2">
                        <Label htmlFor="wallet-address">Adresse du wallet</Label>
                        <Input id="wallet-address" placeholder="Entrez l'adresse du wallet" />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="role">Rôle</Label>
                        <select
                          id="role"
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                          <option value="user">Utilisateur</option>
                          <option value="admin">Admin</option>
                          <option value="superadmin">Super Admin</option>
                        </select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline">Annuler</Button>
                      <Button>Ajouter</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            <TabsContent value="all" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[250px]">
                        <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort("walletAddress")}>
                          Adresse du wallet
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort("joinedAt")}>
                          Inscription
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort("lastActive")}>
                          Dernière activité
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort("tokensCreated")}>
                          Tokens
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </Button>
                      </TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Rôle</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        </TableCell>
                      </TableRow>
                    ) : filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          Aucun utilisateur trouvé
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-mono">{truncateAddress(user.walletAddress)}</TableCell>
                          <TableCell>{formatDate(user.joinedAt)}</TableCell>
                          <TableCell>{formatDate(user.lastActive)}</TableCell>
                          <TableCell>{user.tokensCreated}</TableCell>
                          <TableCell>{getStatusBadge(user.status)}</TableCell>
                          <TableCell>{getRoleBadge(user.role)}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleViewUser(user)}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Voir les détails
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(user.walletAddress)}>
                                  <Copy className="h-4 w-4 mr-2" />
                                  Copier l'adresse
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Modifier
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {user.status !== "banned" ? (
                                  <DropdownMenuItem onClick={() => handleBanUser(user)} className="text-red-600">
                                    <Ban className="h-4 w-4 mr-2" />
                                    Bannir
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem className="text-green-600">
                                    <Check className="h-4 w-4 mr-2" />
                                    Réactiver
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem onClick={() => handleDeleteUser(user)} className="text-red-600">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Supprimer
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="active" className="space-y-4">
              {/* Contenu similaire filtré pour les utilisateurs actifs */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[250px]">Adresse du wallet</TableHead>
                      <TableHead>Inscription</TableHead>
                      <TableHead>Dernière activité</TableHead>
                      <TableHead>Tokens</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Rôle</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers
                      .filter((user) => user.status === "active")
                      .map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-mono">{truncateAddress(user.walletAddress)}</TableCell>
                          <TableCell>{formatDate(user.joinedAt)}</TableCell>
                          <TableCell>{formatDate(user.lastActive)}</TableCell>
                          <TableCell>{user.tokensCreated}</TableCell>
                          <TableCell>{getStatusBadge(user.status)}</TableCell>
                          <TableCell>{getRoleBadge(user.role)}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="inactive" className="space-y-4">
              {/* Contenu similaire filtré pour les utilisateurs inactifs */}
            </TabsContent>

            <TabsContent value="banned" className="space-y-4">
              {/* Contenu similaire filtré pour les utilisateurs bannis */}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Affichage de {filteredUsers.length} utilisateurs sur {users.length}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              Précédent
            </Button>
            <Button variant="outline" size="sm">
              1
            </Button>
            <Button variant="outline" size="sm">
              2
            </Button>
            <Button variant="outline" size="sm">
              3
            </Button>
            <Button variant="outline" size="sm">
              Suivant
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Dialogue de détails utilisateur */}
      <Dialog open={isUserDialogOpen} onOpenChange={setIsUserDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Détails de l'utilisateur</DialogTitle>
            <DialogDescription>Informations détaillées sur l'utilisateur</DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Adresse</Label>
                <div className="col-span-3 flex items-center gap-2">
                  <code className="rounded bg-muted px-2 py-1 font-mono text-sm">{selectedUser.walletAddress}</code>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => navigator.clipboard.writeText(selectedUser.walletAddress)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Statut</Label>
                <div className="col-span-3">{getStatusBadge(selectedUser.status)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Rôle</Label>
                <div className="col-span-3">{getRoleBadge(selectedUser.role)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Inscription</Label>
                <div className="col-span-3">{formatDate(selectedUser.joinedAt)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Dernière activité</Label>
                <div className="col-span-3">{formatDate(selectedUser.lastActive)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Tokens créés</Label>
                <div className="col-span-3">{selectedUser.tokensCreated}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Transactions</Label>
                <div className="col-span-3">{selectedUser.transactionsCount}</div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUserDialogOpen(false)}>
              Fermer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialogue de confirmation de suppression */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="py-4">
              <div className="flex items-center justify-center gap-2 mb-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
              </div>
              <p className="text-center">
                Vous êtes sur le point de supprimer l'utilisateur avec l'adresse :{" "}
                <code className="rounded bg-muted px-2 py-1 font-mono text-sm">
                  {truncateAddress(selectedUser.walletAddress)}
                </code>
              </p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={confirmDeleteUser}>
              Supprimer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialogue de confirmation de bannissement */}
      <Dialog open={isBanDialogOpen} onOpenChange={setIsBanDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer le bannissement</DialogTitle>
            <DialogDescription>Êtes-vous sûr de vouloir bannir cet utilisateur ?</DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="py-4">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Ban className="h-12 w-12 text-red-500" />
              </div>
              <p className="text-center">
                Vous êtes sur le point de bannir l'utilisateur avec l'adresse :{" "}
                <code className="rounded bg-muted px-2 py-1 font-mono text-sm">
                  {truncateAddress(selectedUser.walletAddress)}
                </code>
              </p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBanDialogOpen(false)}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={confirmBanUser}>
              Bannir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

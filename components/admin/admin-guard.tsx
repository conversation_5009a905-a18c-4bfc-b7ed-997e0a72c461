"use client"

import { useEffect, useState, type ReactNode } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useRouter } from "next/navigation"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { ShieldAlert, Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface AdminGuardProps {
  children: ReactNode
  requireOnChainVerification?: boolean
  redirectTo?: string
}

export function AdminGuard({
  children,
  requireOnChainVerification = false,
  redirectTo = "/admin/auth",
}: AdminGuardProps) {
  const [isVerifying, setIsVerifying] = useState(true)
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { connected, publicKey, signMessage } = useWallet()
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    async function verifyAdminStatus() {
      try {
        setIsVerifying(true)
        setError(null)

        // Check if already authenticated via cookies
        const response = await fetch("/api/admin/auth/check", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        })

        const data = await response.json()

        if (response.ok && data.isAuthenticated) {
          setIsAuthorized(true)

          // If on-chain verification is required and we have a connected wallet
          if (requireOnChainVerification && connected && publicKey) {
            try {
              // Request on-chain verification
              const onChainResponse = await fetch("/api/admin/auth/verify-onchain", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  walletAddress: publicKey.toString(),
                }),
              })

              const onChainData = await onChainResponse.json()

              if (!onChainResponse.ok || !onChainData.verified) {
                setError("On-chain verification failed. Please ensure you're using an authorized admin wallet.")
                setIsAuthorized(false)
              }
            } catch (onChainError) {
              console.error("On-chain verification error:", onChainError)
              setError("Failed to perform on-chain verification. Please try again.")
              setIsAuthorized(false)
            }
          }
        } else {
          setIsAuthorized(false)

          // If wallet is connected, try to authenticate
          if (connected && publicKey) {
            try {
              // Request a challenge
              const challengeResponse = await fetch("/api/admin/auth/challenge", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  walletAddress: publicKey.toString(),
                }),
              })

              if (!challengeResponse.ok) {
                const errorData = await challengeResponse.json()
                throw new Error(errorData.error || "Failed to get authentication challenge")
              }

              const { nonce, timestamp } = await challengeResponse.json()

              // Create the message to sign
              const message = new TextEncoder().encode(`Admin authentication request: ${nonce} at ${timestamp}`)

              // Request signature from wallet
              const signature = await signMessage?.(message)

              if (!signature) {
                throw new Error("Failed to sign the authentication message")
              }

              // Verify the signature
              const verifyResponse = await fetch("/api/admin/auth/verify", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  walletAddress: publicKey.toString(),
                  nonce,
                  timestamp,
                  signature: Array.from(signature),
                }),
              })

              const verifyData = await verifyResponse.json()

              if (verifyResponse.ok && verifyData.isAdmin) {
                setIsAuthorized(true)
                toast({
                  title: "Authentication successful",
                  description: "You have been authenticated as an administrator",
                })
              } else {
                setError(verifyData.error || "Authentication failed")
              }
            } catch (authError: any) {
              console.error("Authentication error:", authError)
              setError(authError.message || "Authentication failed")
            }
          } else {
            // No wallet connected and not authenticated
            router.push(redirectTo)
          }
        }
      } catch (error: any) {
        console.error("Admin verification error:", error)
        setError(error.message || "Failed to verify admin status")
      } finally {
        setIsVerifying(false)
      }
    }

    verifyAdminStatus()
  }, [connected, publicKey, signMessage, router, redirectTo, requireOnChainVerification, toast])

  if (isVerifying) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">Verifying admin credentials...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <ShieldAlert className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 flex justify-center">
          <Button onClick={() => router.push(redirectTo)}>Return to Login</Button>
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    router.push(redirectTo)
    return null
  }

  return <>{children}</>
}

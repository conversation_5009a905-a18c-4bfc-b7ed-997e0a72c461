"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { DatePicker } from "@/components/ui/date-picker"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, Edit } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import type { PresaleData } from "@/lib/presale-service"

export function PresaleManagement() {
  const [presales, setPresales] = useState<PresaleData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [selectedPresale, setSelectedPresale] = useState<PresaleData | null>(null)
  const [newPresale, setNewPresale] = useState({
    tokenName: "",
    tokenSymbol: "",
    price: 0.001,
    hardCap: 1000000,
    softCap: 500000,
    minPurchase: 0.1,
    maxPurchase: 10,
    startTime: new Date(),
    endTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // +14 jours
    vestingPeriod: 90,
    vestingReleases: 3,
  })
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [presaleToDelete, setPresaleToDelete] = useState<PresaleData | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchPresales()
  }, [])

  const fetchPresales = async () => {
    setIsLoading(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons des données pour la démonstration
      setTimeout(() => {
        const mockPresales: PresaleData[] = [
          {
            id: "presale_1",
            tokenMint: "token_mint_1",
            tokenName: "Solana Platform Token",
            tokenSymbol: "SPT",
            tokenDecimals: 9,
            price: 0.005,
            hardCap: 1000000,
            softCap: 500000,
            minPurchase: 0.1,
            maxPurchase: 10,
            startTime: Date.now() - 7 * 24 * 60 * 60 * 1000, // -7 jours
            endTime: Date.now() + 14 * 24 * 60 * 60 * 1000, // +14 jours
            raised: 350000,
            sold: 70000000,
            status: "active",
            vestingPeriod: 90,
            vestingReleases: 3,
            createdBy: "Admin",
            createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000,
          },
          {
            id: "presale_2",
            tokenMint: "token_mint_2",
            tokenName: "Meme Coin",
            tokenSymbol: "MEME",
            tokenDecimals: 9,
            price: 0.001,
            hardCap: 500000,
            softCap: 100000,
            minPurchase: 0.1,
            maxPurchase: 5,
            startTime: Date.now() + 3 * 24 * 60 * 60 * 1000, // +3 jours
            endTime: Date.now() + 17 * 24 * 60 * 60 * 1000, // +17 jours
            raised: 0,
            sold: 0,
            status: "upcoming",
            vestingPeriod: 60,
            vestingReleases: 2,
            createdBy: "Admin",
            createdAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
          },
          {
            id: "presale_3",
            tokenMint: "token_mint_3",
            tokenName: "Utility Token",
            tokenSymbol: "UTIL",
            tokenDecimals: 9,
            price: 0.01,
            hardCap: 200000,
            softCap: 100000,
            minPurchase: 0.5,
            maxPurchase: 20,
            startTime: Date.now() - 30 * 24 * 60 * 60 * 1000, // -30 jours
            endTime: Date.now() - 2 * 24 * 60 * 60 * 1000, // -2 jours
            raised: 180000,
            sold: 18000000,
            status: "successful",
            vestingPeriod: 120,
            vestingReleases: 4,
            createdBy: "Admin",
            createdAt: Date.now() - 45 * 24 * 60 * 60 * 1000,
          },
        ]

        setPresales(mockPresales)
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error("Error fetching presales:", error)
      toast({
        title: "Erreur",
        description: "Impossible de récupérer les presales",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const handleCreatePresale = async () => {
    setIsCreating(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons la création pour la démonstration
      await new Promise((resolve) => setTimeout(resolve, 1500))

      const newPresaleData: PresaleData = {
        id: `presale_${Date.now()}`,
        tokenMint: `token_mint_${Date.now()}`,
        tokenName: newPresale.tokenName,
        tokenSymbol: newPresale.tokenSymbol,
        tokenDecimals: 9,
        price: newPresale.price,
        hardCap: newPresale.hardCap,
        softCap: newPresale.softCap,
        minPurchase: newPresale.minPurchase,
        maxPurchase: newPresale.maxPurchase,
        startTime: newPresale.startTime.getTime(),
        endTime: newPresale.endTime.getTime(),
        raised: 0,
        sold: 0,
        status: newPresale.startTime.getTime() > Date.now() ? "upcoming" : "active",
        vestingPeriod: newPresale.vestingPeriod,
        vestingReleases: newPresale.vestingReleases,
        createdBy: "Admin",
        createdAt: Date.now(),
      }

      setPresales([...presales, newPresaleData])
      setShowCreateDialog(false)
      toast({
        title: "Succès",
        description: `La presale ${newPresaleData.tokenName} a été créée avec succès`,
      })
    } catch (error) {
      console.error("Error creating presale:", error)
      toast({
        title: "Erreur",
        description: "Impossible de créer la presale",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  const handleEditPresale = async () => {
    if (!selectedPresale) return

    setIsEditing(true)
    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons la modification pour la démonstration
      await new Promise((resolve) => setTimeout(resolve, 1500))

      const updatedPresales = presales.map((presale) =>
        presale.id === selectedPresale.id ? selectedPresale : presale
      )

      setPresales(updatedPresales)
      setShowEditDialog(false)
      toast({
        title: "Succès",
        description: `La presale ${selectedPresale.tokenName} a été mise à jour avec succès`,
      })
    } catch (error) {
      console.error("Error updating presale:", error)
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour la presale",
        variant: "destructive",
      })
    } finally {
      setIsEditing(false)
    }
  }

  const handleDeletePresale = async () => {
    if (!presaleToDelete) return

    try {
      // Dans un environnement réel, cela serait un appel API
      // Simulons la suppression pour la démonstration
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const updatedPresales = presales.filter((presale) => presale.id !== presaleToDelete.id)
      setPresales(updatedPresales)
      setShowDeleteDialog(false)
      setPresaleToDelete(null)
      toast({
        title: "Succès",
        description: `La presale ${presaleToDelete.tokenName} a été supprimée avec succès`,
      })
    } catch (error) {
      console.error("Error deleting presale:", error)
      toast({
        title: "Erreur",
        description: "Impossible de supprimer la presale",
        variant: "destructive",
      })
    }
  }

  const confirmDeletePresale = (presale: PresaleData) => {
    setPresaleToDelete(presale)
    setShowDeleteDialog(true)
  }

  const openEditDialog = (presale: PresaleData) => {
    setSelectedPresale(presale)
    setShowEditDialog(true)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "upcoming":
        return <Badge className="bg-blue-500">À venir</Badge>
      case "active":
        return <Badge className="bg-green-500">Active</Badge>
      case "ended":
        return <Badge className="bg-orange-500">Terminée</Badge>
      case "successful":
        return <Badge className="bg-green-700">Réussie</Badge>
      case "failed":
        return <Badge className="bg-red-500">Échouée</Badge>
      default:
        return <Badge className="bg-gray-500">{status}</Badge>
    }
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("fr-FR", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(value)
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("fr-FR").format(value)
  }

  const calculateProgress = (raised: number, hardCap: number) => {
    return (raised / hardCap) * 100
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Gestion des Presales</CardTitle>
            <CardDescription>Configurez et gérez les presales de tokens</CardDescription>
          </div>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Nouvelle Presale
          </Button>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Chargement des presales...</p>
              </div>
            </div>
          ) : presales.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-muted-foreground">Aucune presale trouvée</p>
              <Button variant="outline" className="mt-4" onClick={() => setShowCreateDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Créer une presale
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Token</TableHead>
                    <TableHead>Prix</TableHead>
                    <TableHead>Hard Cap</TableHead>
                    <TableHead>Progression</TableHead>
                    <TableHead>Période</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {presales.map((presale) => (
                    <TableRow key={presale.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{presale.tokenName}</div>
                          <div className="text-sm text-muted-foreground">{presale.tokenSymbol}</div>
                        </div>
                      </TableCell>
                      <TableCell>{formatCurrency(presale.price)}</TableCell>
                      <TableCell>{formatCurrency(presale.hardCap)}</TableCell>
                      <TableCell>
                        <div className="w-full">
                          <div className="flex justify-between text-xs mb-1">
                            <span>{formatCurrency(presale.raised)}</span>
                            <span>{Math.round(calculateProgress(presale.raised, presale.hardCap))}%</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className="bg-primary rounded-full h-2"
                              style={{ width: `${calculateProgress(presale.raised, presale.hardCap)}%` }}
                            ></div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-xs">
                          <div>Début: {formatDate(presale.startTime)}</div>
                          <div>Fin: {formatDate(presale.endTime)}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(presale.status)}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon" onClick={() => openEditDialog(presale)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => confirmDeletePresale(presale)}
                            disabled={presale.status === "active" && presale.raised > 0}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog pour créer une nouvelle presale */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Créer une nouvelle presale</DialogTitle>
            <DialogDescription>
              Configurez les détails de la presale. Les utilisateurs pourront acheter des tokens pendant la période
              définie.
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="basic">Informations de base</TabsTrigger>
              <TabsTrigger value="limits">Limites & Prix</TabsTrigger>
              <TabsTrigger value="vesting">Vesting & Distribution</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tokenName">Nom du Token</Label>
                  <Input
                    id="tokenName"
                    value={newPresale.tokenName}
                    onChange={(e) => setNewPresale({ ...newPresale, tokenName: e.target.value })}
                    placeholder="Ex: Solana Platform Token"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tokenSymbol">Symbole du Token</Label>
                  <Input
                    id="tokenSymbol"
                    value={newPresale.tokenSymbol}
                    onChange={(e) => setNewPresale({ ...newPresale, tokenSymbol: e.target.value })}
                    placeholder="Ex: SPT"
                    maxLength={10}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Date de début</Label>
                  <DatePicker
                    id="startDate"
                    date={newPresale.startTime}
                    setDate={(date) => date && setNewPresale({ ...newPresale, startTime: date })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">Date de fin</Label>
                  <DatePicker
                    id="endDate"
                    date={newPresale.endTime}
                    setDate={(date) => date && setNewPresale({ ...newPresale, endTime: date })}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="limits" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">Prix du Token (en USD)</Label>
                  <Input
                    id="price"
                    type="number"
                    min="0.00001"
                    step="0.00001"
                    value={newPresale.price}
                    onChange={(e) => setNewPresale({ ...newPresale, price: Number.parseFloat(e.target.value) })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="hardCap">Hard Cap (en USD)</Label>
                  <Input
                    id="hardCap"
                    type="number"
                    min="1000"
                    step="1000"
                    value={newPresale.hardCap}
                    onChange={(e) => setNewPresale({ ...newPresale, hardCap: Number.parseFloat(e.target.value) })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="softCap">Soft Cap (en USD)</Label>
                  <Input
                    id="softCap"
                    type="number"
                    min="100"
                    step="100"
                    value={newPresale.softCap}
                    onChange={(e) => setNewPresale({ ...newPresale, softCap: Number.parseFloat(e.target.value) })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minPurchase">Achat minimum (en SOL)</Label>
                  <Input
                    id="minPurchase"
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={newPresale.minPurchase}
                    onChange={(e) => setNewPresale({ ...newPresale, minPurchase: Number.parseFloat(e.target.value) })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxPurchase">Achat maximum (en SOL)</Label>
                <Input
                  id="maxPurchase"
                  type="number"
                  min="0.1"
                  step="0.1"
                  value={newPresale.maxPurchase}
                  onChange={(e) => setNewPresale({ ...newPresale, maxPurchase: Number.parseFloat(e.target.value) })}
                />
              </div>
            </TabsContent>

            <TabsContent value="vesting" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="vestingPeriod">Période de vesting (en jours)</Label>
                <div className="flex items-center space-x-4">
                  <Slider
                    id="vestingPeriod"
                    min={0}
                    max={365}
                    step={1}
                    value={[newPresale.vestingPeriod]}
                    onValueChange={(value) => setNewPresale({ ...newPresale, vestingPeriod: value[0] })}
                    className="flex-1"
                  />
                  <span className="w-12 text-center">{newPresale.vestingPeriod}</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  0 = Pas de vesting, distribution immédiate après la fin de la presale
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="vestingReleases">Nombre de libérations</Label>
                <div className="flex items-center space-x-4">
                  <Slider
                    id="vestingReleases"
                    min={1}
                    max={12}
                    step={1}
                    value={[newPresale.vestingReleases]}
                    onValueChange={(value) => setNewPresale({ ...newPresale, vestingReleases: value[0] })}
                    className="flex-1"
                    disabled={newPresale.vestingPeriod === 0}
                  />
                  <span className="w-12 text-center">{newPresale.vestingReleases}</span>
                </div>
              </div>

              <div className="mt-4 p-4 bg-muted rounded-md">
                <h4 className="font-medium mb-2">Aperçu du calendrier de vesting</h4>
                {newPresale.vestingPeriod === 0 ? (
                  <p className="text-sm">Distribution immédiate après la fin de la presale</p>
                ) : (
                  <div className="space-y-2">
                    <p className="text-sm">
                      Période totale: <span className="font-medium">{newPresale.vestingPeriod} jours</span>
                    </p>
                    <p className="text-sm">
                      Fréquence de libération:{" "}
                      <span className="font-medium">
                        Tous les {Math.floor(newPresale.vestingPeriod / newPresale.vestingReleases)} jours
                      </span>
                    </p>
                    <p className="text-sm">
                      Pourcentage par libération:{" "}
                      <span className="font-medium">
                        {Math.floor(100 / newPresale.vestingReleases)}% par libération
                      </span>
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)} disabled={isCreating}>
              Annuler
            </Button>
            <Button onClick={handleCreatePresale} disabled={isCreating}>
              {isCreating ? "Création en cours..." : "Créer la presale"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog pour éditer une presale */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Modifier la presale</DialogTitle>
            <DialogDescription>
              Modifiez les détails de la presale. Certains paramètres peuvent être limités si la presale est déjà active.
            </DialogDescription>
          </DialogHeader>

          {selectedPresale && (
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="basic">Informations de base</TabsTrigger>
                <TabsTrigger value="limits">Limites & Prix</TabsTrigger>
                <TabsTrigger value="vesting">Vesting & Distribution</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="editTokenName">Nom du Token</Label>
                    <Input
                      id="editTokenName"
                      value={selectedPresale.tokenName}
                      onChange={(e) => setSelectedPresale({ ...selectedPresale, tokenName: e.target.value })}
                      disabled={selectedPresale.status !== "upcoming"}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="editTokenSymbol">Symbole du Token</Label>
                    <Input
                      id="editTokenSymbol"
                      value={selectedPresale.tokenSymbol}
                      onChange={(e) => setSelectedPresale({ ...selectedPresale, tokenSymbol: e.target.value })}
                      maxLength={10}
                      disabled={selectedPresale.status !== "upcoming"}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="editStartDate">Date de début</Label>
                    <DatePicker
                      id="editStartDate"
                      date={new Date(selectedPresale.startTime)}
                      setDate={(date) => date && setSelectedPresale({ ...selectedPresale, startTime: date.getTime() })}
                      disabled={selectedPresale.status !== "upcoming"}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="editEndDate">Date de fin</Label>
                    <DatePicker
                      id="editEndDate"
                      date={new Date(selectedPresale.endTime)}
                      setDate={(date) => date && setSelectedPresale({ ...selectedPresale, endTime: date.getTime() })}
                      disabled={selectedPresale.status === "successful" || selectedPresale.status === "failed"}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="limits" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="editPrice">Prix du Token (en USD)</Label>
                    <Input
                      id="editPrice"
                      type="number"
                      min="0.00001"
                      step="0.00001"
                      value={selectedPresale.price}
                      onChange={(e) =>
                        setSelectedPresale({ ...selectedPresale, price: Number.parseFloat(e.target.value) })
                      }
                      disabled={selectedPresale.status !== "upcoming"}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="editHardCap">Hard Cap (en USD)</Label>
                    <Input
                      id="editHardCap"
                      type="number"
                      min="1000"
                      step="1000"
                      value={selectedPresale.hardCap}
                      onChange={(e) =>
                        setSelectedPresale({ ...selectedPresale, hardCap: Number.parseFloat(e.target.value) })
                      }
                      disabled={selectedPresale.status !== "upcoming"}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="editSoftCap">Soft Cap (en USD)</Label>
                    <Input
                      id="editSoftCap"
                      type="number"
                      min="100"
                      step="100"
                      value={selectedPresale.softCap}
                      onChange={(e) =>
                        setSelectedPresale({ ...selectedPresale, softCap: Number.parseFloat(e.target.value) })
                      }
                      disabled={selectedPresale.status !== "upcoming"}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="editMinPurchase">Achat minimum (en SOL)</Label>
                    <Input
                      id="editMinPurchase"
                      type="number"
                      min="0.01"
                      step="0.01"
                      value={selectedPresale.minPurchase

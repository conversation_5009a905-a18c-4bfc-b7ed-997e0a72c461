"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { Calendar, Download, FileSpreadsheet, FileText, Loader2, Settings, Share2 } from "lucide-react"
import { fr } from "date-fns/locale"

export function ReportGenerator() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [reportType, setReportType] = useState("user")
  const [reportFormat, setReportFormat] = useState("pdf")
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>(undefined)
  const [reportName, setReportName] = useState("")
  const [includeCharts, setIncludeCharts] = useState(true)
  const [includeRawData, setIncludeRawData] = useState(true)
  const { toast } = useToast()

  const handleGenerateReport = () => {
    if (!reportName.trim()) {
      toast({
        title: "Nom du rapport requis",
        description: "Veuillez saisir un nom pour votre rapport",
        variant: "destructive",
      })
      return
    }

    if (!dateRange?.from || !dateRange?.to) {
      toast({
        title: "Période requise",
        description: "Veuillez sélectionner une période pour votre rapport",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)

    // Simuler la génération du rapport
    setTimeout(() => {
      setIsGenerating(false)
      toast({
        title: "Rapport généré avec succès",
        description: `Le rapport "${reportName}" a été généré et est prêt à être téléchargé.`,
      })
    }, 2000)
  }

  const reportTypes = [
    { value: "user", label: "Utilisateurs", description: "Statistiques et activité des utilisateurs" },
    { value: "token", label: "Tokens", description: "Création et performance des tokens" },
    { value: "transaction", label: "Transactions", description: "Volume et valeur des transactions" },
    { value: "revenue", label: "Revenus", description: "Revenus et frais de la plateforme" },
    { value: "security", label: "Sécurité", description: "Incidents et alertes de sécurité" },
  ]

  const reportFormats = [
    { value: "pdf", label: "PDF", icon: <FileText className="h-4 w-4" /> },
    { value: "excel", label: "Excel", icon: <FileSpreadsheet className="h-4 w-4" /> },
    { value: "csv", label: "CSV", icon: <FileText className="h-4 w-4" /> },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Générateur de rapports</CardTitle>
        <CardDescription>Créez des rapports personnalisés pour analyser les données de la plateforme</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="standard" className="space-y-4">
          <TabsList>
            <TabsTrigger value="standard">Rapport standard</TabsTrigger>
            <TabsTrigger value="custom">Rapport personnalisé</TabsTrigger>
            <TabsTrigger value="scheduled">Rapports programmés</TabsTrigger>
          </TabsList>

          <TabsContent value="standard" className="space-y-4">
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="report-name">Nom du rapport</Label>
                <Input
                  id="report-name"
                  placeholder="Saisissez un nom pour votre rapport"
                  value={reportName}
                  onChange={(e) => setReportName(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label>Type de rapport</Label>
                <Select value={reportType} onValueChange={setReportType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un type de rapport" />
                  </SelectTrigger>
                  <SelectContent>
                    {reportTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex flex-col">
                          <span>{type.label}</span>
                          <span className="text-xs text-muted-foreground">{type.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Période</Label>
                <DateRangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  locale={fr}
                  calendarTodayClassName="bg-primary text-primary-foreground"
                />
              </div>

              <div className="space-y-2">
                <Label>Format du rapport</Label>
                <div className="flex flex-wrap gap-2">
                  {reportFormats.map((format) => (
                    <Button
                      key={format.value}
                      type="button"
                      variant={reportFormat === format.value ? "default" : "outline"}
                      className="flex-1"
                      onClick={() => setReportFormat(format.value)}
                    >
                      {format.icon}
                      <span className="ml-2">{format.label}</span>
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Options</Label>
                <div className="flex flex-col gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-charts"
                      checked={includeCharts}
                      onCheckedChange={(checked) => setIncludeCharts(checked === true)}
                    />
                    <Label htmlFor="include-charts" className="text-sm">
                      Inclure les graphiques
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-raw-data"
                      checked={includeRawData}
                      onCheckedChange={(checked) => setIncludeRawData(checked === true)}
                    />
                    <Label htmlFor="include-raw-data" className="text-sm">
                      Inclure les données brutes
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="custom" className="space-y-4">
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Settings className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">Rapport personnalisé</h3>
              <p className="text-muted-foreground mt-2 max-w-md">
                Créez des rapports entièrement personnalisés avec des métriques, des filtres et des visualisations
                spécifiques.
              </p>
              <Button className="mt-4">Configurer un rapport personnalisé</Button>
            </div>
          </TabsContent>

          <TabsContent value="scheduled" className="space-y-4">
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">Rapports programmés</h3>
              <p className="text-muted-foreground mt-2 max-w-md">
                Configurez des rapports à générer automatiquement selon un calendrier défini.
              </p>
              <Button className="mt-4">Configurer un rapport programmé</Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">
          <Share2 className="h-4 w-4 mr-2" />
          Options de partage
        </Button>
        <Button onClick={handleGenerateReport} disabled={isGenerating}>
          {isGenerating ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Génération en cours...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Générer le rapport
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

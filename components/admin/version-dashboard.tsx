"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"
import {
  AlertCircle,
  CheckCircle,
  Download,
  GitBranch,
  GitCommit,
  GitMerge,
  GitPullRequest,
  History,
  Package,
  RefreshCw,
  Tag,
  Upload,
} from "lucide-react"

interface Version {
  id: string
  version: string
  name: string
  status: "development" | "staging" | "production" | "deprecated"
  releaseDate: string
  description: string
  changelog: string[]
  author: string
  commitHash: string
  dependencies: {
    name: string
    version: string
    status: "compatible" | "outdated" | "vulnerable"
  }[]
}

export default function VersionDashboard() {
  const { toast } = useToast()
  const [versions, setVersions] = useState<Version[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("current")
  const [deploymentProgress, setDeploymentProgress] = useState(0)
  const [isDeploying, setIsDeploying] = useState(false)

  useEffect(() => {
    const fetchVersions = async () => {
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données simulées
        const mockVersions: Version[] = [
          {
            id: "v1.5.0",
            version: "1.5.0",
            name: "Quantum Leap",
            status: "production",
            releaseDate: "2023-04-15",
            description: "Version stable avec support complet pour les tokens Quantum",
            changelog: [
              "Ajout du support pour les tokens Quantum",
              "Amélioration des performances de création de tokens",
              "Correction de bugs dans le module de staking",
              "Mise à jour des dépendances de sécurité",
            ],
            author: "Équipe de développement",
            commitHash: "8f7e6d5c4b3a2910",
            dependencies: [
              { name: "solana/web3.js", version: "1.73.0", status: "compatible" },
              { name: "next", version: "13.4.1", status: "compatible" },
              { name: "react", version: "18.2.0", status: "compatible" },
            ],
          },
          {
            id: "v1.4.2",
            version: "1.4.2",
            name: "Stability Update",
            status: "deprecated",
            releaseDate: "2023-03-10",
            description: "Mise à jour de stabilité et corrections de bugs",
            changelog: [
              "Correction de bugs critiques dans le module de création de tokens",
              "Amélioration de la stabilité des transactions",
              "Optimisation des performances",
            ],
            author: "Équipe de développement",
            commitHash: "7e6d5c4b3a291080",
            dependencies: [
              { name: "solana/web3.js", version: "1.70.1", status: "outdated" },
              { name: "next", version: "13.2.4", status: "outdated" },
              { name: "react", version: "18.2.0", status: "compatible" },
            ],
          },
          {
            id: "v1.6.0-beta",
            version: "1.6.0-beta",
            name: "Multi-Chain Revolution",
            status: "staging",
            releaseDate: "2023-05-20",
            description: "Version bêta avec support multi-chaînes et nouvelles fonctionnalités",
            changelog: [
              "Support pour Ethereum et BNB Chain",
              "Nouvelle interface utilisateur pour la gestion multi-chaînes",
              "Amélioration des performances de création de tokens",
              "Nouveau module de gouvernance DAO",
            ],
            author: "Équipe de développement",
            commitHash: "9g8f7e6d5c4b3a29",
            dependencies: [
              { name: "solana/web3.js", version: "1.74.0", status: "compatible" },
              { name: "ethers", version: "5.7.2", status: "compatible" },
              { name: "next", version: "13.4.3", status: "compatible" },
              { name: "react", version: "18.2.0", status: "compatible" },
            ],
          },
          {
            id: "v1.7.0-dev",
            version: "1.7.0-dev",
            name: "Quantum Nexus",
            status: "development",
            releaseDate: "En développement",
            description: "Version en développement avec de nouvelles fonctionnalités avancées",
            changelog: [
              "Support pour les tokens non-fongibles (NFT)",
              "Intégration avec les marketplaces NFT",
              "Nouveau système de staking avancé",
              "Support pour les contrats intelligents personnalisés",
            ],
            author: "Équipe de développement",
            commitHash: "dev-main-latest",
            dependencies: [
              { name: "solana/web3.js", version: "1.75.0", status: "compatible" },
              { name: "ethers", version: "6.0.0", status: "compatible" },
              { name: "next", version: "13.5.0", status: "compatible" },
              { name: "react", version: "18.3.0-next", status: "vulnerable" },
            ],
          },
        ]

        setVersions(mockVersions)
        setIsLoading(false)
      } catch (error) {
        console.error("Erreur lors du chargement des versions:", error)
        toast({
          title: "Erreur",
          description: "Impossible de charger les versions",
          variant: "destructive",
        })
        setIsLoading(false)
      }
    }

    fetchVersions()
  }, [toast])

  const getCurrentVersion = () => {
    return versions.find((v) => v.status === "production")
  }

  const getStagingVersion = () => {
    return versions.find((v) => v.status === "staging")
  }

  const getDevelopmentVersion = () => {
    return versions.find((v) => v.status === "development")
  }

  const handleDeployToStaging = async () => {
    const devVersion = getDevelopmentVersion()
    if (!devVersion) return

    setIsDeploying(true)
    setDeploymentProgress(0)

    // Simuler un déploiement
    for (let i = 1; i <= 10; i++) {
      await new Promise((resolve) => setTimeout(resolve, 500))
      setDeploymentProgress(i * 10)
    }

    // Mettre à jour le statut
    const updatedVersions = versions.map((v) => {
      if (v.id === devVersion.id) {
        return { ...v, status: "staging" as const }
      }
      if (v.status === "staging") {
        return { ...v, status: "deprecated" as const }
      }
      return v
    })

    setVersions(updatedVersions)
    setIsDeploying(false)

    toast({
      title: "Déploiement réussi",
      description: `Version ${devVersion.version} déployée en staging`,
    })
  }

  const handleDeployToProduction = async () => {
    const stagingVersion = getStagingVersion()
    if (!stagingVersion) return

    setIsDeploying(true)
    setDeploymentProgress(0)

    // Simuler un déploiement
    for (let i = 1; i <= 10; i++) {
      await new Promise((resolve) => setTimeout(resolve, 500))
      setDeploymentProgress(i * 10)
    }

    // Mettre à jour le statut
    const updatedVersions = versions.map((v) => {
      if (v.id === stagingVersion.id) {
        return { ...v, status: "production" as const }
      }
      if (v.status === "production") {
        return { ...v, status: "deprecated" as const }
      }
      return v
    })

    setVersions(updatedVersions)
    setIsDeploying(false)

    toast({
      title: "Déploiement réussi",
      description: `Version ${stagingVersion.version} déployée en production`,
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "production":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Production</Badge>
      case "staging":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Staging</Badge>
      case "development":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Développement</Badge>
      case "deprecated":
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Obsolète</Badge>
      case "compatible":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Compatible</Badge>
      case "outdated":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Obsolète</Badge>
      case "vulnerable":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Vulnérable</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Gestion des Versions</h2>
          <p className="text-muted-foreground">
            Gérez les versions de la plateforme et déployez de nouvelles mises à jour
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            Historique
          </Button>
          <Button className="flex items-center gap-2">
            <GitBranch className="h-4 w-4" />
            Nouvelle Version
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Package className="h-5 w-5 mr-2 text-green-500" />
              Version en Production
            </CardTitle>
          </CardHeader>
          <CardContent>
            {getCurrentVersion() ? (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="font-semibold text-xl">{getCurrentVersion()?.version}</div>
                  {getStatusBadge("production")}
                </div>
                <p className="text-sm">{getCurrentVersion()?.name}</p>
                <p className="text-xs text-muted-foreground">Déployée le {getCurrentVersion()?.releaseDate}</p>
                <div className="flex items-center text-xs text-muted-foreground mt-2">
                  <GitCommit className="h-3 w-3 mr-1" />
                  {getCurrentVersion()?.commitHash.substring(0, 8)}
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">Aucune version en production</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <GitPullRequest className="h-5 w-5 mr-2 text-blue-500" />
              Version en Staging
            </CardTitle>
          </CardHeader>
          <CardContent>
            {getStagingVersion() ? (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="font-semibold text-xl">{getStagingVersion()?.version}</div>
                  {getStatusBadge("staging")}
                </div>
                <p className="text-sm">{getStagingVersion()?.name}</p>
                <p className="text-xs text-muted-foreground">Déployée le {getStagingVersion()?.releaseDate}</p>
                <div className="flex justify-end mt-2">
                  <Button size="sm" onClick={handleDeployToProduction} disabled={isDeploying}>
                    {isDeploying ? (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        Déploiement...
                      </>
                    ) : (
                      <>
                        <Upload className="h-3 w-3 mr-1" />
                        Déployer en Production
                      </>
                    )}
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">Aucune version en staging</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <GitMerge className="h-5 w-5 mr-2 text-purple-500" />
              Version en Développement
            </CardTitle>
          </CardHeader>
          <CardContent>
            {getDevelopmentVersion() ? (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="font-semibold text-xl">{getDevelopmentVersion()?.version}</div>
                  {getStatusBadge("development")}
                </div>
                <p className="text-sm">{getDevelopmentVersion()?.name}</p>
                <p className="text-xs text-muted-foreground">En développement</p>
                <div className="flex justify-end mt-2">
                  <Button size="sm" onClick={handleDeployToStaging} disabled={isDeploying}>
                    {isDeploying ? (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        Déploiement...
                      </>
                    ) : (
                      <>
                        <Upload className="h-3 w-3 mr-1" />
                        Déployer en Staging
                      </>
                    )}
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">Aucune version en développement</p>
            )}
          </CardContent>
        </Card>
      </div>

      {isDeploying && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Déploiement en cours</CardTitle>
            <CardDescription>Veuillez patienter pendant le déploiement</CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={deploymentProgress} className="h-2" />
            <div className="flex justify-between mt-2 text-xs text-muted-foreground">
              <span>Préparation</span>
              <span>Installation des dépendances</span>
              <span>Compilation</span>
              <span>Tests</span>
              <span>Déploiement</span>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="current">Version Actuelle</TabsTrigger>
          <TabsTrigger value="all">Toutes les Versions</TabsTrigger>
          <TabsTrigger value="dependencies">Dépendances</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Détails de la Version Actuelle</CardTitle>
              <CardDescription>
                {getCurrentVersion()?.version} - {getCurrentVersion()?.name}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Description</h3>
                <p className="text-sm text-muted-foreground">{getCurrentVersion()?.description}</p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Journal des modifications</h3>
                <ul className="list-disc pl-5 space-y-1">
                  {getCurrentVersion()?.changelog.map((change, index) => (
                    <li key={index} className="text-sm text-muted-foreground">
                      {change}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">Informations</h3>
                  <p className="text-sm text-muted-foreground">Auteur: {getCurrentVersion()?.author}</p>
                  <p className="text-sm text-muted-foreground">Date de sortie: {getCurrentVersion()?.releaseDate}</p>
                </div>
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Télécharger le package
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Historique des Versions</CardTitle>
              <CardDescription>Liste de toutes les versions de la plateforme</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Version</TableHead>
                    <TableHead>Nom</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Date de sortie</TableHead>
                    <TableHead>Commit</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {versions.map((version) => (
                    <TableRow key={version.id}>
                      <TableCell className="font-medium">{version.version}</TableCell>
                      <TableCell>{version.name}</TableCell>
                      <TableCell>{getStatusBadge(version.status)}</TableCell>
                      <TableCell>{version.releaseDate}</TableCell>
                      <TableCell className="font-mono text-xs">{version.commitHash.substring(0, 8)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Tag className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dependencies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Dépendances</CardTitle>
              <CardDescription>Liste des dépendances de la version actuelle</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nom</TableHead>
                    <TableHead>Version</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getCurrentVersion()?.dependencies.map((dep) => (
                    <TableRow key={dep.name}>
                      <TableCell className="font-medium">{dep.name}</TableCell>
                      <TableCell>{dep.version}</TableCell>
                      <TableCell>{getStatusBadge(dep.status)}</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          Mettre à jour
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {getCurrentVersion()?.dependencies.some((d) => d.status === "vulnerable") && (
                <Alert variant="destructive" className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Vulnérabilités détectées</AlertTitle>
                  <AlertDescription>
                    Certaines dépendances présentent des vulnérabilités de sécurité connues. Veuillez les mettre à jour
                    dès que possible.
                  </AlertDescription>
                </Alert>
              )}

              {getCurrentVersion()?.dependencies.some((d) => d.status === "outdated") && (
                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Dépendances obsolètes</AlertTitle>
                  <AlertDescription>
                    Certaines dépendances sont obsolètes. Envisagez de les mettre à jour pour bénéficier des dernières
                    fonctionnalités et corrections de bugs.
                  </AlertDescription>
                </Alert>
              )}

              {getCurrentVersion()?.dependencies.every((d) => d.status === "compatible") && (
                <Alert className="mt-4 bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <AlertTitle>Toutes les dépendances sont à jour</AlertTitle>
                  <AlertDescription>Toutes les dépendances sont compatibles et à jour.</AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

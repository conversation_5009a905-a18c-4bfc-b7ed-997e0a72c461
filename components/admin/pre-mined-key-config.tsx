"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, Key, Copy, Eye, EyeOff } from "lucide-react"
import TokenSuffixService from "@/lib/token-suffix-service"
import { useToast } from "@/components/ui/use-toast"

export default function PreMinedKeyConfig() {
  const [privateKey, setPrivateKey] = useState("")
  const [publicKey, setPublicKey] = useState("")
  const [suffix, setSuffix] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showPrivateKey, setShowPrivateKey] = useState(false)
  const { toast } = useToast()

  // Charger la clé pré-minée existante
  useEffect(() => {
    const loadExistingKey = () => {
      try {
        const keypair = TokenSuffixService.getPreMinedKeypair()
        if (keypair) {
          setPublicKey(keypair.publicKey.toString())
          // Ne pas afficher la clé privée par défaut, juste indiquer qu'elle est configurée
          setPrivateKey("*".repeat(20))
        } else {
          setPublicKey("")
          setPrivateKey("")
        }
      } catch (error) {
        console.error("Erreur lors du chargement de la clé pré-minée:", error)
      }
    }

    loadExistingKey()
  }, [])

  // Vérifier si l'adresse publique se termine par le suffixe
  useEffect(() => {
    // Intentionally empty. The original code had a typo here: ngKey()
  }, [])

  // Vérifier si l'adresse publique se termine par le suffixe
  useEffect(() => {
    if (publicKey && suffix) {
      const hasSuffix = TokenSuffixService.hasCorrectSuffix(publicKey, suffix)
      if (!hasSuffix) {
        setError(`L'adresse publique ne se termine pas par le suffixe "${suffix}"`)
      } else {
        setError(null)
      }
    }
  }, [publicKey, suffix])

  const handleSaveKey = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Vérifier que la clé privée est au bon format
      if (!privateKey || privateKey === "*".repeat(20)) {
        setError("Veuillez entrer une clé privée valide")
        setIsLoading(false)
        return
      }

      // Vérifier que le suffixe est défini
      if (!suffix) {
        setError("Veuillez entrer un suffixe")
        setIsLoading(false)
        return
      }

      // Simuler l'enregistrement de la clé privée
      // Dans une implémentation réelle, cela serait enregistré dans une base de données sécurisée
      // ou dans les variables d'environnement
      console.log("Clé privée à enregistrer:", privateKey)
      console.log("Suffixe configuré:", suffix)

      // Afficher un message de succès
      setSuccess("Clé pré-minée enregistrée avec succès")
      toast({
        title: "Configuration enregistrée",
        description: "La clé pré-minée a été configurée avec succès",
      })
    } catch (err: any) {
      console.error("Erreur lors de l'enregistrement de la clé pré-minée:", err)
      setError(err.message || "Une erreur s'est produite lors de l'enregistrement de la clé")
      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors de l'enregistrement de la clé",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copié",
      description: "Texte copié dans le presse-papiers",
    })
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Key className="h-5 w-5 mr-2" />
          Configuration de la clé pré-minée
        </CardTitle>
        <CardDescription>
          Configurez une clé privée pré-minée pour générer des tokens avec un suffixe spécifique
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Succès</AlertTitle>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="suffix">Suffixe de token</Label>
          <Input
            id="suffix"
            placeholder="ex: GF"
            value={suffix}
            onChange={(e) => setSuffix(e.target.value)}
            maxLength={5}
          />
          <p className="text-xs text-muted-foreground">
            Le suffixe qui sera utilisé pour toutes les adresses de token créées par les utilisateurs
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="privateKey">Clé privée pré-minée</Label>
            <Button
              variant="ghost"
              size="icon"
              type="button"
              onClick={() => setShowPrivateKey(!showPrivateKey)}
              className="h-8 w-8"
            >
              {showPrivateKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
          <div className="flex">
            <Input
              id="privateKey"
              type={showPrivateKey ? "text" : "password"}
              placeholder="Clé privée au format tableau d'octets"
              value={privateKey}
              onChange={(e) => setPrivateKey(e.target.value)}
              className="flex-1"
            />
            {privateKey && (
              <Button
                variant="outline"
                size="icon"
                type="button"
                onClick={() => copyToClipboard(privateKey)}
                className="ml-2"
              >
                <Copy className="h-4 w-4" />
              </Button>
            )}
          </div>
          <p className="text-xs text-muted-foreground">
            La clé privée doit être au format tableau d'octets (ex: 123,45,67,...)
          </p>
        </div>

        {publicKey && (
          <div className="space-y-2">
            <Label htmlFor="publicKey">Adresse publique</Label>
            <div className="flex">
              <Input id="publicKey" value={publicKey} readOnly className="flex-1" />
              <Button
                variant="outline"
                size="icon"
                type="button"
                onClick={() => copyToClipboard(publicKey)}
                className="ml-2"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">L'adresse publique correspondant à la clé privée</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveKey} disabled={isLoading}>
          {isLoading ? "Enregistrement..." : "Enregistrer la configuration"}
        </Button>
      </CardFooter>
    </Card>
  )
}

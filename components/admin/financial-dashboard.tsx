"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Line, LineChart, Bar, BarChart, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Legend } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  ArrowDownIcon,
  ArrowUpIcon,
  DownloadIcon,
  RefreshCw,
  Calendar,
  Wallet,
  CreditCard,
  DollarSign,
  Users,
  Coins,
  Activity,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Données de démonstration pour les revenus
const revenueData = [
  { month: "Jan", revenue: 4500, expenses: 2100, profit: 2400 },
  { month: "Fév", revenue: 5200, expenses: 2300, profit: 2900 },
  { month: "Mar", revenue: 6100, expenses: 2500, profit: 3600 },
  { month: "Avr", revenue: 7800, expenses: 2800, profit: 5000 },
  { month: "Mai", revenue: 8900, expenses: 3100, profit: 5800 },
  { month: "Juin", revenue: 10200, expenses: 3400, profit: 6800 },
  { month: "Juil", revenue: 11500, expenses: 3700, profit: 7800 },
  { month: "Août", revenue: 13200, expenses: 4000, profit: 9200 },
  { month: "Sep", revenue: 14800, expenses: 4300, profit: 10500 },
  { month: "Oct", revenue: 16500, expenses: 4600, profit: 11900 },
  { month: "Nov", revenue: 18200, expenses: 4900, profit: 13300 },
  { month: "Déc", revenue: 20000, expenses: 5200, profit: 14800 },
]

// Données de démonstration pour les transactions
const transactionData = [
  { day: "Lun", transactions: 145, volume: 12500 },
  { day: "Mar", transactions: 132, volume: 10800 },
  { day: "Mer", transactions: 164, volume: 14200 },
  { day: "Jeu", transactions: 187, volume: 16500 },
  { day: "Ven", transactions: 213, volume: 18900 },
  { day: "Sam", transactions: 178, volume: 15600 },
  { day: "Dim", transactions: 152, volume: 13100 },
]

// Données de démonstration pour les tokens
const tokenData = [
  { name: "SOL", volume: 45000, change: 5.2, transactions: 1250 },
  { name: "BNB", volume: 32000, change: -2.1, transactions: 980 },
  { name: "MMGF", volume: 28000, change: 12.5, transactions: 850 },
  { name: "USDC", volume: 18000, change: 0.1, transactions: 720 },
  { name: "ETH", volume: 15000, change: 3.8, transactions: 650 },
]

// Données de démonstration pour les frais
const feesData = [
  { category: "Création de token", amount: 8500, percentage: 42.5 },
  { category: "Transactions", amount: 6200, percentage: 31 },
  { category: "Staking", amount: 2800, percentage: 14 },
  { category: "NFT", amount: 1500, percentage: 7.5 },
  { category: "Autres", amount: 1000, percentage: 5 },
]

// Données de démonstration pour les transactions récentes
const recentTransactions = [
  {
    id: "TX123456",
    type: "Création Token",
    amount: 250,
    status: "completed",
    user: "0x1a2b...3c4d",
    date: "2023-04-28 14:32:15",
  },
  {
    id: "TX123457",
    type: "Staking",
    amount: 120,
    status: "completed",
    user: "0x5e6f...7g8h",
    date: "2023-04-28 13:45:22",
  },
  {
    id: "TX123458",
    type: "NFT Mint",
    amount: 75,
    status: "completed",
    user: "0x9i0j...1k2l",
    date: "2023-04-28 12:18:05",
  },
  {
    id: "TX123459",
    type: "Swap",
    amount: 45,
    status: "pending",
    user: "0x3m4n...5o6p",
    date: "2023-04-28 11:52:37",
  },
  {
    id: "TX123460",
    type: "Liquidity",
    amount: 180,
    status: "completed",
    user: "0x7q8r...9s0t",
    date: "2023-04-28 10:29:14",
  },
]

export function FinancialDashboard() {
  const [period, setPeriod] = useState("year")
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Fonction pour rafraîchir les données
  const refreshData = async () => {
    setIsRefreshing(true)
    // Simuler une requête API
    await new Promise((resolve) => setTimeout(resolve, 1500))
    setIsRefreshing(false)
  }

  // Calculer les totaux
  const totalRevenue = revenueData.reduce((sum, item) => sum + item.revenue, 0)
  const totalProfit = revenueData.reduce((sum, item) => sum + item.profit, 0)
  const totalExpenses = revenueData.reduce((sum, item) => sum + item.expenses, 0)
  const totalTransactions = transactionData.reduce((sum, item) => sum + item.transactions, 0)
  const totalVolume = transactionData.reduce((sum, item) => sum + item.volume, 0)

  // Calculer les variations
  const revenueChange = ((revenueData[11].revenue - revenueData[10].revenue) / revenueData[10].revenue) * 100
  const profitChange = ((revenueData[11].profit - revenueData[10].profit) / revenueData[10].profit) * 100
  const transactionChange =
    ((transactionData[6].transactions - transactionData[5].transactions) / transactionData[5].transactions) * 100

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Tableau de Bord Financier</h2>
          <p className="text-muted-foreground">Aperçu des performances financières et des métriques de la plateforme</p>
        </div>
        <div className="flex items-center gap-2">
          <Select defaultValue={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sélectionner une période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Cette semaine</SelectItem>
              <SelectItem value="month">Ce mois</SelectItem>
              <SelectItem value="quarter">Ce trimestre</SelectItem>
              <SelectItem value="year">Cette année</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" onClick={refreshData} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
          </Button>
          <Button variant="outline" size="icon">
            <DownloadIcon className="h-4 w-4" />
          </Button>
          <Button>
            <Calendar className="mr-2 h-4 w-4" />
            Rapports
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenu Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRevenue.toLocaleString()} $</div>
            <div className="flex items-center space-x-2">
              <p className={`text-xs ${revenueChange >= 0 ? "text-green-500" : "text-red-500"}`}>
                {revenueChange >= 0 ? "+" : ""}
                {revenueChange.toFixed(1)}%
              </p>
              {revenueChange >= 0 ? (
                <ArrowUpIcon className="h-4 w-4 text-green-500" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 text-red-500" />
              )}
              <p className="text-xs text-muted-foreground">par rapport au mois précédent</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profit Net</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalProfit.toLocaleString()} $</div>
            <div className="flex items-center space-x-2">
              <p className={`text-xs ${profitChange >= 0 ? "text-green-500" : "text-red-500"}`}>
                {profitChange >= 0 ? "+" : ""}
                {profitChange.toFixed(1)}%
              </p>
              {profitChange >= 0 ? (
                <ArrowUpIcon className="h-4 w-4 text-green-500" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 text-red-500" />
              )}
              <p className="text-xs text-muted-foreground">par rapport au mois précédent</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTransactions.toLocaleString()}</div>
            <div className="flex items-center space-x-2">
              <p className={`text-xs ${transactionChange >= 0 ? "text-green-500" : "text-red-500"}`}>
                {transactionChange >= 0 ? "+" : ""}
                {transactionChange.toFixed(1)}%
              </p>
              {transactionChange >= 0 ? (
                <ArrowUpIcon className="h-4 w-4 text-green-500" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 text-red-500" />
              )}
              <p className="text-xs text-muted-foreground">par rapport à la semaine précédente</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Volume Total</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalVolume.toLocaleString()} $</div>
            <div className="flex items-center space-x-2">
              <p className="text-xs text-green-500">+12.5%</p>
              <ArrowUpIcon className="h-4 w-4 text-green-500" />
              <p className="text-xs text-muted-foreground">par rapport à la semaine précédente</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="tokens">Tokens</TabsTrigger>
          <TabsTrigger value="fees">Frais</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Revenus et Profits</CardTitle>
                <CardDescription>Aperçu des revenus, dépenses et profits sur l'année</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <ChartContainer
                  config={{
                    revenue: {
                      label: "Revenus",
                      color: "hsl(var(--chart-1))",
                    },
                    expenses: {
                      label: "Dépenses",
                      color: "hsl(var(--chart-2))",
                    },
                    profit: {
                      label: "Profit",
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                  className="aspect-[4/3]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="revenue"
                        stroke="var(--color-revenue)"
                        strokeWidth={2}
                        activeDot={{ r: 8 }}
                      />
                      <Line type="monotone" dataKey="expenses" stroke="var(--color-expenses)" strokeWidth={2} />
                      <Line type="monotone" dataKey="profit" stroke="var(--color-profit)" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Répartition des Revenus</CardTitle>
                <CardDescription>Sources de revenus par catégorie</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-1/2 text-sm font-medium">Création de token</div>
                    <div className="w-1/2 flex items-center gap-2">
                      <Progress value={42.5} className="h-2" />
                      <span className="text-sm font-medium">42.5%</span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-1/2 text-sm font-medium">Transactions</div>
                    <div className="w-1/2 flex items-center gap-2">
                      <Progress value={31} className="h-2" />
                      <span className="text-sm font-medium">31%</span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-1/2 text-sm font-medium">Staking</div>
                    <div className="w-1/2 flex items-center gap-2">
                      <Progress value={14} className="h-2" />
                      <span className="text-sm font-medium">14%</span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-1/2 text-sm font-medium">NFT</div>
                    <div className="w-1/2 flex items-center gap-2">
                      <Progress value={7.5} className="h-2" />
                      <span className="text-sm font-medium">7.5%</span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-1/2 text-sm font-medium">Autres</div>
                    <div className="w-1/2 flex items-center gap-2">
                      <Progress value={5} className="h-2" />
                      <span className="text-sm font-medium">5%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Transactions Récentes</CardTitle>
                <CardDescription>Les 5 dernières transactions sur la plateforme</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Montant</TableHead>
                      <TableHead>Utilisateur</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentTransactions.map((tx) => (
                      <TableRow key={tx.id}>
                        <TableCell className="font-medium">{tx.id}</TableCell>
                        <TableCell>{tx.type}</TableCell>
                        <TableCell>{tx.amount} $</TableCell>
                        <TableCell className="font-mono text-xs">{tx.user}</TableCell>
                        <TableCell>
                          <Badge
                            variant={tx.status === "completed" ? "default" : "outline"}
                            className={
                              tx.status === "completed"
                                ? "bg-green-100 text-green-800 hover:bg-green-100"
                                : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                            }
                          >
                            {tx.status === "completed" ? "Complété" : "En attente"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-xs">{tx.date}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  Voir toutes les transactions
                </Button>
              </CardFooter>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Statistiques Utilisateurs</CardTitle>
                <CardDescription>Activité et croissance des utilisateurs</CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Utilisateurs actifs</div>
                    <div className="flex items-center">
                      <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="font-bold">12,543</span>
                    </div>
                  </div>
                  <Progress value={78} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <div>+18.2% par rapport au mois dernier</div>
                    <div>78% de l'objectif</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Nouveaux utilisateurs</div>
                    <div className="flex items-center">
                      <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span className="font-bold">1,875</span>
                    </div>
                  </div>
                  <Progress value={65} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <div>+12.5% par rapport au mois dernier</div>
                    <div>65% de l'objectif</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Taux de rétention</div>
                    <div className="flex items-center">
                      <span className="font-bold">85%</span>
                    </div>
                  </div>
                  <Progress value={85} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <div>+5.3% par rapport au mois dernier</div>
                    <div>85% de l'objectif</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="transactions" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Volume de Transactions</CardTitle>
                <CardDescription>Volume quotidien des transactions sur la dernière semaine</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <ChartContainer
                  config={{
                    volume: {
                      label: "Volume ($)",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="aspect-[4/3]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={transactionData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Bar dataKey="volume" fill="var(--color-volume)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Nombre de Transactions</CardTitle>
                <CardDescription>Nombre quotidien de transactions sur la dernière semaine</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <ChartContainer
                  config={{
                    transactions: {
                      label: "Transactions",
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                  className="aspect-[4/3]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={transactionData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Bar dataKey="transactions" fill="var(--color-transactions)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Détails des Transactions</CardTitle>
              <CardDescription>Analyse détaillée des transactions par type</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Nombre</TableHead>
                    <TableHead>Volume</TableHead>
                    <TableHead>Frais moyens</TableHead>
                    <TableHead>Variation</TableHead>
                    <TableHead>Part</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Création de token</TableCell>
                    <TableCell>245</TableCell>
                    <TableCell>61,250 $</TableCell>
                    <TableCell>250 $</TableCell>
                    <TableCell className="text-green-500">+15.2%</TableCell>
                    <TableCell>28.5%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Swap</TableCell>
                    <TableCell>1,872</TableCell>
                    <TableCell>84,240 $</TableCell>
                    <TableCell>45 $</TableCell>
                    <TableCell className="text-green-500">****%</TableCell>
                    <TableCell>39.2%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Staking</TableCell>
                    <TableCell>532</TableCell>
                    <TableCell>37,240 $</TableCell>
                    <TableCell>70 $</TableCell>
                    <TableCell className="text-red-500">-3.1%</TableCell>
                    <TableCell>17.3%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">NFT Mint</TableCell>
                    <TableCell>187</TableCell>
                    <TableCell>14,025 $</TableCell>
                    <TableCell>75 $</TableCell>
                    <TableCell className="text-green-500">+22.5%</TableCell>
                    <TableCell>6.5%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Liquidity</TableCell>
                    <TableCell>98</TableCell>
                    <TableCell>17,640 $</TableCell>
                    <TableCell>180 $</TableCell>
                    <TableCell className="text-green-500">****%</TableCell>
                    <TableCell>8.2%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Autres</TableCell>
                    <TableCell>43</TableCell>
                    <TableCell>645 $</TableCell>
                    <TableCell>15 $</TableCell>
                    <TableCell className="text-red-500">-1.2%</TableCell>
                    <TableCell>0.3%</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="tokens" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tokens Actifs</CardTitle>
                <Coins className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,245</div>
                <p className="text-xs text-muted-foreground">+156 nouveaux ce mois-ci</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Volume Total</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">138,000 $</div>
                <p className="text-xs text-muted-foreground">+12.5% par rapport au mois dernier</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Token le Plus Actif</CardTitle>
                <Coins className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">MMGF</div>
                <p className="text-xs text-muted-foreground">45,000 $ de volume quotidien</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Frais Générés</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8,250 $</div>
                <p className="text-xs text-muted-foreground">+18.3% par rapport au mois dernier</p>
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Performance des Tokens</CardTitle>
              <CardDescription>Volume et transactions par token</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Token</TableHead>
                    <TableHead>Volume</TableHead>
                    <TableHead>Variation</TableHead>
                    <TableHead>Transactions</TableHead>
                    <TableHead>Prix</TableHead>
                    <TableHead>Liquidité</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tokenData.map((token) => (
                    <TableRow key={token.name}>
                      <TableCell className="font-medium">{token.name}</TableCell>
                      <TableCell>{token.volume.toLocaleString()} $</TableCell>
                      <TableCell className={token.change >= 0 ? "text-green-500" : "text-red-500"}>
                        {token.change >= 0 ? "+" : ""}
                        {token.change}%
                      </TableCell>
                      <TableCell>{token.transactions}</TableCell>
                      <TableCell>
                        {token.name === "SOL"
                          ? "105.23 $"
                          : token.name === "BNB"
                            ? "312.45 $"
                            : token.name === "MMGF"
                              ? "0.0025 $"
                              : token.name === "USDC"
                                ? "1.00 $"
                                : "1,850.75 $"}
                      </TableCell>
                      <TableCell>
                        <div className="flex w-full items-center gap-2">
                          <Progress
                            value={
                              token.name === "SOL"
                                ? 85
                                : token.name === "BNB"
                                  ? 72
                                  : token.name === "MMGF"
                                    ? 95
                                    : token.name === "USDC"
                                      ? 65
                                      : 78
                            }
                            className="h-2"
                          />
                          <span className="text-xs font-medium">
                            {token.name === "SOL"
                              ? "85%"
                              : token.name === "BNB"
                                ? "72%"
                                : token.name === "MMGF"
                                  ? "95%"
                                  : token.name === "USDC"
                                    ? "65%"
                                    : "78%"}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                Voir tous les tokens
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="fees" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Répartition des Frais</CardTitle>
                <CardDescription>Distribution des frais par catégorie</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <ChartContainer
                  config={{
                    amount: {
                      label: "Montant ($)",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="aspect-[4/3]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={feesData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="category" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Bar dataKey="amount" fill="var(--color-amount)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Détails des Frais</CardTitle>
                <CardDescription>Analyse des frais par catégorie</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {feesData.map((fee) => (
                    <div key={fee.category} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">{fee.category}</div>
                        <div className="font-bold">{fee.amount.toLocaleString()} $</div>
                      </div>
                      <Progress value={fee.percentage} className="h-2" />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <div>{fee.percentage}% du total</div>
                        <div>
                          {fee.category === "Création de token"
                            ? "+15.2%"
                            : fee.category === "Transactions"
                              ? "****%"
                              : fee.category === "Staking"
                                ? "-3.1%"
                                : fee.category === "NFT"
                                  ? "+22.5%"
                                  : "-1.2%"}{" "}
                          par rapport au mois dernier
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Historique des Frais</CardTitle>
              <CardDescription>Évolution mensuelle des frais</CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ChartContainer
                config={{
                  fees: {
                    label: "Frais ($)",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="aspect-[5/1]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={[
                      { month: "Jan", fees: 4200 },
                      { month: "Fév", fees: 4800 },
                      { month: "Mar", fees: 5500 },
                      { month: "Avr", fees: 7000 },
                      { month: "Mai", fees: 8000 },
                      { month: "Juin", fees: 9200 },
                      { month: "Juil", fees: 10300 },
                      { month: "Août", fees: 11800 },
                      { month: "Sep", fees: 13300 },
                      { month: "Oct", fees: 14800 },
                      { month: "Nov", fees: 16400 },
                      { month: "Déc", fees: 18000 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="fees"
                      stroke="var(--color-fees)"
                      strokeWidth={2}
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

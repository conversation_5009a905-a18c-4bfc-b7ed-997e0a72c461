"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { Badge } from "@/components/ui/badge"
import { CheckCircle2, AlertCircle, ShieldAlert, Shield } from "lucide-react"

// Types
interface AdminWallet {
  address: string
  label: string
  role: "superadmin" | "admin" | "moderator" | "readonly" | "treasury" | "marketing" | "development" | "liquidity"
  network: "solana" | "bnb" | "ethereum"
  balance: number
  addedAt: string
  addedBy: string
  lastActive?: string
  status: "active" | "locked" | "inactive"
  privateKey?: string
  tokenBalances?: {
    symbol: string
    name: string
    balance: number
  }[]
  permissions: string[]
}

export function WalletManagement() {
 const [wallets, setWallets] = useState<AdminWallet[]>([
   {
     address: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM",
     label: "Admin Principal",
     role: "superadmin",
     network: "solana",
     balance: 25.75,
     addedAt: "2023-01-15T12:00:00Z",
     addedBy: "System",
     lastActive: "2023-06-10T14:30:00Z",
     status: "active",
     privateKey: "5xgEQBqTuEHWB6g9mMshzhMuQKdW7fZ7tTiVwz6Xv7QzYG8aT9VFjRwMQ6z7LTkN",
     tokenBalances: [
       { symbol: "GF", name: "Global Finance", balance: 1000000 },
       { symbol: "USDC", name: "USD Coin", balance: 50000 }
     ],
     permissions: ["admin", "deploy", "mint", "burn"]
   },
   {
     address: "5FHwkrdxkRzDNkwkjM5aMVxnvE4UzwgJhEEtrJHAzKqJ",
     label: "Trésorerie",
     role: "treasury",
     network: "solana",
     balance: 150.25,
     addedAt: "2023-02-20T14:30:00Z",
     addedBy: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM",
     lastActive: "2023-06-09T10:15:00Z",
     status: "active",
     privateKey: "7xgEQBqTuEHWB6g9mMshzhMuQKdW7fZ7tTiVwz6Xv7QzYG8aT9VFjRwMQ6z7LTkN",
     tokenBalances: [
       { symbol: "GF", name: "Global Finance", balance: 5000000 },
       { symbol: "USDC", name: "USD Coin", balance: 250000 }
     ],
     permissions: ["transfer", "receive"]
   },
   {
     address: "0x742d35Cc6634C0532925a3b844Bc454e4438f44e",
     label: "Marketing",
     role: "marketing",
     network: "bnb",
     balance: 75.5,
     addedAt: "2023-03-10T09:15:00Z",
     addedBy: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM",
     lastActive: "2023-06-08T16:45:00Z",
     status: "active",
     privateKey: "0x742d35Cc6634C0532925a3b844Bc454e4438f44e5xgEQBqTuEHWB6g9mMshzh",
     tokenBalances: [
       { symbol: "GF", name: "Global Finance", balance: 2000000 },
       { symbol: "BNB", name: "Binance Coin", balance: 75.5 }
     ],
     permissions: ["transfer", "receive"]
   },
   {
     address: "0x912d35Cc6634C0532925a3b844Bc454e4438f55f",
     label: "Développement",
     role: "development",
     network: "bnb",
     balance: 50.25,
     addedAt: "2023-04-05T16:45:00Z",
     addedBy: "5FHwkrdxkRzDNkwkjM5aMVxnvE4UzwgJhEEtrJHAzKqJ",
     lastActive: "2023-05-20T11:30:00Z",
     status: "active",
     privateKey: "0x912d35Cc6634C0532925a3b844Bc454e4438f55f5xgEQBqTuEHWB6g9mMshzh",
     tokenBalances: [
       { symbol: "GF", name: "Global Finance", balance: 1500000 },
       { symbol: "BNB", name: "Binance Coin", balance: 50.25 }
     ],
     permissions: ["transfer", "receive", "deploy"]
   },
   {
     address: "3XdUVd35L8zgpEJVWWQc9iVVJqmF5SgxfmxmVNgEhJTW",
     label: "Liquidité",
     role: "liquidity",
     network: "solana",
     balance: 200.5,
     addedAt: "2023-05-15T10:30:00Z",
     addedBy: "5FHwkrdxkRzDNkwkjM5aMVxnvE4UzwgJhEEtrJHAzKqJ",
     lastActive: "2023-06-01T09:45:00Z",
     status: "active",
     privateKey: "3XdUVd35L8zgpEJVWWQc9iVVJqmF5SgxfmxmVNgEhJTW5xgEQBqTuEHWB6g9mMshzh",
     tokenBalances: [
       { symbol: "GF", name: "Global Finance", balance: 3000000 },
       { symbol: "USDC", name: "USD Coin", balance: 300000 }
     ],
     permissions: ["transfer", "receive", "liquidity"]
   },
   {
     address: "******************************************",
     label: "Staking",
     role: "moderator",
     network: "ethereum",
     balance: 15.75,
     addedAt: "2023-05-20T14:15:00Z",
     addedBy: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM",
     status: "locked",
     privateKey: "******************************************5xgEQBqTuEHWB6g9mMshzh",
     tokenBalances: [
       { symbol: "GF", name: "Global Finance", balance: 4000000 },
       { symbol: "ETH", name: "Ethereum", balance: 15.75 }
     ],
     permissions: ["transfer", "receive", "stake"]
   }
 ])
 const [isLoading, setIsLoading] = useState(false)
 const [isAdding, setIsAdding] = useState(false)
 const [isDeleting, setIsDeleting] = useState(false)
 const [isRefreshing, setIsRefreshing] = useState(false)
 const [showAddDialog, setShowAddDialog] = useState(false)
 const [showDeleteDialog, setShowDeleteDialog] = useState(false)
 const [showDetailsDialog, setShowDetailsDialog] = useState(false)
 const [showExportDialog, setShowExportDialog] = useState(false)
 const [showImportDialog, setShowImportDialog] = useState(false)
 const [showPermissionsDialog, setShowPermissionsDialog] = useState(false)
 const [showBackupDialog, setShowBackupDialog] = useState(false)
 const [walletToDelete, setWalletToDelete] = useState<AdminWallet | null>(null)
 const [selectedWallet, setSelectedWallet] = useState<AdminWallet | null>(null)
 const [newWallet, setNewWallet] = useState({
   address: "",
   label: "",
   role: "admin" as "superadmin" | "admin" | "moderator" | "readonly" | "treasury" | "marketing" | "development" | "liquidity",
   network: "solana" as "solana" | "bnb" | "ethereum"
 })
 const [bulkAddMode, setBulkAddMode] = useState(false)
 const [bulkAddresses, setBulkAddresses] = useState("")
 const [validationError, setValidationError] = useState<string | null>(null)
 const [searchQuery, setSearchQuery] = useState("")
 const [activeTab, setActiveTab] = useState("all")
 const [filteredWallets, setFilteredWallets] = useState<AdminWallet[]>([])
 const [showPrivateKey, setShowPrivateKey] = useState(false)
 const [exportData, setExportData] = useState("")
 const [importData, setImportData] = useState("")
 const [backupPassword, setBackupPassword] = useState("")
 const [confirmBackupPassword, setConfirmBackupPassword] = useState("")
 const [editingPermissions, setEditingPermissions] = useState<string[]>([])
 const { toast } = useToast()

 // Filtrer les wallets lorsque la recherche ou l'onglet change
 useEffect(() => {
   let filtered = [...wallets]

   if (searchQuery) {
     filtered = filtered.filter(
       (w) =>
         w.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
         w.address.toLowerCase().includes(searchQuery.toLowerCase())
     )
   }

   if (activeTab !== "all") {
     if (activeTab === "solana" || activeTab === "bnb" || activeTab === "ethereum") {
       filtered = filtered.filter((w) => w.network === activeTab)
     } else if (activeTab === "locked") {
       filtered = filtered.filter((w) => w.status === "locked")
     } else if (activeTab === "inactive") {
       filtered = filtered.filter((w) => w.status === "inactive")
     } else {
       filtered = filtered.filter((w) => w.role === activeTab)
     }
   }

   setFilteredWallets(filtered)
 }, [searchQuery, activeTab, wallets])

 const validateSolanaAddress = (address: string): boolean => {
   // Validation simplifiée pour l'exemple
   return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)
 }

 const validateEthAddress = (address: string): boolean => {
   // Validation simplifiée pour l'exemple
   return /^0x[a-fA-F0-9]{40}$/.test(address)
 }

 const validateAddress = (address: string, network: string): boolean => {
   if (network === "solana") {
     return validateSolanaAddress(address)
   } else if (network === "bnb" || network === "ethereum") {
     return validateEthAddress(address)
   }
   return false
 }

 const handleAddWallet = () => {
   if (bulkAddMode) {
     handleBulkAddWallets()
     return
   }

   if (!newWallet.address || !newWallet.label) {
     setValidationError("L'adresse et le libellé sont obligatoires")
     return
   }

   if (!validateAddress(newWallet.address, newWallet.network)) {
     setValidationError(`L'adresse du wallet n'est pas valide pour le réseau ${newWallet.network}`)
     return
   }

   if (wallets.some((wallet) => wallet.address === newWallet.address)) {
     setValidationError("Cette adresse de wallet est déjà enregistrée")
     return
   }

   setIsAdding(true)
   setValidationError(null)

   // Simuler un délai d'ajout
   setTimeout(() => {
     // Générer une clé privée fictive
     let privateKey = ""
     if (newWallet.network === "solana") {
       privateKey = `${newWallet.address}${Math.random().toString(36).substring(2, 15)}`
     } else {
       privateKey = `${newWallet.address}${Math.random().toString(36).substring(2, 15)}`
     }

     const newAdminWallet: AdminWallet = {
       ...newWallet,
       addedAt: new Date().toISOString(),
       addedBy: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM", // Adresse de l'admin actuel
       status: "active",
       balance: 0,
       privateKey,
       tokenBalances: [],
       permissions: ["transfer", "receive"]
     }

     setWallets([...wallets, newAdminWallet])
     setNewWallet({
       address: "",
       label: "",
       role: "admin",
       network: "solana"
     })
     setShowAddDialog(false)
     setIsAdding(false)

     toast({
       title: "Wallet ajouté",
       description: `Le wallet ${newAdminWallet.label} a été ajouté avec succès`,
     })
   }, 1000)
 }

 const handleBulkAddWallets = () => {
   if (!bulkAddresses.trim()) {
     setValidationError("Veuillez entrer au moins une adresse")
     return
   }

   const addresses = bulkAddresses
     .split("\n")
     .map((line) => line.trim())
     .filter((line) => line.length > 0)

   if (addresses.length === 0) {
     setValidationError("Aucune adresse valide trouvée")
     return
   }

   // Valider les adresses
   const invalidAddresses = addresses.filter((address) => !validateAddress(address, newWallet.network))
   if (invalidAddresses.length > 0) {
     setValidationError(`${invalidAddresses.length} adresse(s) invalide(s)`)
     return
   }

   // Vérifier les doublons
   const existingAddresses = wallets.map((wallet) => wallet.address)
   const duplicates = addresses.filter((address) => existingAddresses.includes(address))
   if (duplicates.length > 0) {
     setValidationError(`${duplicates.length} adresse(s) déjà enregistrée(s)`)
     return
   }

   setIsAdding(true)
   setValidationError(null)

   // Simuler un délai d'ajout
   setTimeout(() => {
     const newWallets = addresses.map((address) => {
       // Générer une clé privée fictive
       let privateKey = ""
       if (newWallet.network === "solana") {
         privateKey = `${address}${Math.random().toString(36).substring(2, 15)}`
       } else {
         privateKey = `${address}${Math.random().toString(36).substring(2, 15)}`
       }

       return {
         address,
         label: `${newWallet.role} ${address.substring(0, 6)}...`,
         role: newWallet.role,
         network: newWallet.network,
         balance: 0,
         addedAt: new Date().toISOString(),
         addedBy: "8YUULWbGxMqZ4LEHe6LKEwZ1yZKGnN1hWqLeyp5NzQoM", // Adresse de l'admin actuel
         status: "active",
         privateKey,
         tokenBalances: [],
         permissions: ["transfer", "receive"]
       }
     })

     setWallets([...wallets, ...newWallets])
     setBulkAddresses("")
     setNewWallet({
       address: "",
       label: "",
       role: "admin",
       network: "solana"
     })
     setShowAddDialog(false)
     setIsAdding(false)
     setBulkAddMode(false)

     toast({
       title: "Wallets ajoutés",
       description: `${addresses.length} wallets ont été ajoutés avec succès`,
     })
   }, 1500)
 }

 const handleDeleteWallet = () => {
   if (!walletToDelete) return

   setIsDeleting(true)

   // Simuler un délai de suppression
   setTimeout(() => {
     setWallets(wallets.filter((wallet) => wallet.address !== walletToDelete.address))
     setShowDeleteDialog(false)
     setWalletToDelete(null)
     setIsDeleting(false)

     toast({
       title: "Wallet supprimé",
       description: `Le wallet ${walletToDelete.label} a été supprimé avec succès`,
     })
   }, 1000)
 }

 const confirmDeleteWallet = (wallet: AdminWallet) => {
   setWalletToDelete(wallet)
   setShowDeleteDialog(true)
 }

 const showWalletDetails = (wallet: AdminWallet) => {
   setSelectedWallet(wallet)
   setShowPrivateKey(false)
   setShowDetailsDialog(true)
 }

 const toggleWalletLock = (wallet: AdminWallet) => {
   const updatedWallets = wallets.map((w) => {
     if (w.address === wallet.address) {
       return {
         ...w,
         status: w.status === "locked" ? "active" : "locked",
         lastActive: new Date().toISOString()
       }
     }
     return w
   })

   setWallets(updatedWallets)

   toast({
     title: wallet.status === "locked" ? "Wallet déverrouillé" : "Wallet verrouillé",
     description: wallet.status === "locked" 
       ? `Le wallet ${wallet.label} a été déverrouillé avec succès` 
       : `Le wallet ${wallet.label} a été verrouillé avec succès`,
   })
 }

 const refreshBalances = async () => {
   setIsRefreshing(true)

   // Simuler un délai de rafraîchissement
   setTimeout(() => {
     const updatedWallets = wallets.map((wallet) => ({
       ...wallet,
       balance: Number((Math.random() * 100 + wallet.balance).toFixed(2)),
       lastActive: new Date().toISOString()
     }))

     setWallets(updatedWallets)
     setIsRefreshing(false)

     toast({
       title: "Soldes mis à jour",
       description: "Les soldes des wallets ont été mis à jour avec succès",
     })
   }, 2000)
 }

 const copyToClipboard = (text: string) => {
   navigator.clipboard.writeText(text)
   toast({
     title: "Copié !",
     description: "L'adresse a été copiée dans le presse-papiers",
   })
 }

 const copyPrivateKey = (privateKey: string) => {
   navigator.clipboard.writeText(privateKey)
   toast({
     title: "Clé privée copiée",
     description: "La clé privée a été copiée dans le presse-papiers. Attention: information sensible!",
     variant: "destructive",
   })
 }

 const exportWallets = () => {
   const exportData = JSON.stringify(wallets, null, 2)
   setExportData(exportData)
   setShowExportDialog(true)
 }

 const downloadExport = () => {
   const blob = new Blob([exportData], { type: "application/json" })
   const url = URL.createObjectURL(blob)
   const a = document.createElement("a")
   a.href = url
   a.download = `wallets-export-${new Date().toISOString().slice(0, 10)}.json`
   document.body.appendChild(a)
   a.click()
   document.body.removeChild(a)
   URL.revokeObjectURL(url)
   setShowExportDialog(false)
 }

 const importWallets = () => {
   try {
     const importedData = JSON.parse(importData)
     
     if (!Array.isArray(importedData)) {
       throw new Error("Format d'import invalide")
     }

     // Vérifier que chaque wallet a les propriétés requises
     const validWallets = importedData.filter(wallet => 
       wallet.address && wallet.label && wallet.role && wallet.network
     )

     if (validWallets.length === 0) {
       throw new Error("Aucun wallet valide trouvé dans les données importées")
     }

     // Fusionner les wallets importés avec les wallets existants
     const mergedWallets = [...wallets]
     let newWalletsCount = 0

     validWallets.forEach(importedWallet => {
       const existingWalletIndex = mergedWallets.findIndex(w => w.address === importedWallet.address)
       
       if (existingWalletIndex === -1) {
         mergedWallets.push({
           ...importedWallet,
           addedAt: importedWallet.addedAt || new Date().toISOString(),
           addedBy: importedWallet.addedBy || "Import",
           status: importedWallet.status || "active",
           balance: importedWallet.balance || 0,
           tokenBalances: importedWallet.tokenBalances || [],
           permissions: importedWallet.permissions || ["transfer", "receive"]
         })
         newWalletsCount++
       }
     })

     setWallets(mergedWallets)
     setShowImportDialog(false)
     setImportData("")

     toast({
       title: "Import réussi",
       description: `${newWalletsCount} nouveaux wallets ont été importés`,
     })
   } catch (error) {
     console.error("Erreur lors de l'import des wallets:", error)
     toast({
       title: "Erreur",
       description: "Impossible d'importer les wallets. Format de sauvegarde invalide.",
       variant: "destructive",
     })
   }
 }

 const backupPrivateKeys = () => {
   if (backupPassword !== confirmBackupPassword) {
     toast({
       title: "Erreur",
       description: "Les mots de passe ne correspondent pas",
       variant: "destructive",
     })
     return
   }

   if (backupPassword.length < 8) {
     toast({
       title: "Erreur",
       description: "Le mot de passe doit contenir au moins 8 caractères",
       variant: "destructive",
     })
     return
   }

   // Dans une implémentation réelle, les clés seraient chiffrées avec le mot de passe
   const keysData = wallets.map(w => ({
     label: w.label,
     address: w.address,
     network: w.network,
     privateKey: w.privateKey
   }))

   const blob = new Blob([JSON.stringify(keysData, null, 2)], { type: "application/json" })
   const url = URL.createObjectURL(blob)
   const a = document.createElement("a")
   a.href = url
   a.download = `wallet-keys-backup-${new Date().toISOString().slice(0, 10)}.json`
   document.body.appendChild(a)
   a.click()
   document.body.removeChild(a)
   URL.revokeObjectURL(url)

   setShowBackupDialog(false)
   setBackupPassword("")
   setConfirmBackupPassword("")

   toast({
     title: "Sauvegarde réussie",
     description: "Les clés privées ont été sauvegardées avec succès",
   })
 }

 const openPermissionsDialog = (wallet: AdminWallet) => {
   setSelectedWallet(wallet)
   setEditingPermissions([...wallet.permissions])
   setShowPermissionsDialog(true)
 }

 const togglePermission = (permission: string) => {
   if (editingPermissions.includes(permission)) {
     setEditingPermissions(editingPermissions.filter(p => p !== permission))
   } else {
     setEditingPermissions([...editingPermissions, permission])
   }
 }

 const savePermissions = () => {
   if (!selectedWallet) return

   const updatedWallets = wallets.map(w => {
     if (w.address === selectedWallet.address) {
       return {
         ...w,
         permissions: editingPermissions,
         lastActive: new Date().toISOString(),
       }
     }
     return w
   })

   setWallets(updatedWallets)
   setShowPermissionsDialog(false)

   toast({
     title: "Permissions mises à jour",
     description: "Les permissions du wallet ont été mises à jour avec succès",
   })
 }

 const getRoleBadge = (role: string) => {
   switch (role) {
     case "superadmin":
       return (
         <Badge className="bg-red-100 text-red-800 hover:bg-red-200">
           <ShieldAlert className="h-3 w-3 mr-1" />
           Super Admin
         </Badge>
       )
     case "admin":
       return (
         <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
           <Shield className="h-3 w-3 mr-1" />
           Admin
         </Badge>
       )
     case "moderator":
       return (
         <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
           <CheckCircle2 className="h-3 w-3 mr-1" />
           Modérateur
         </Badge>
       )
     case "readonly":
       return (
         <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">
           <AlertCircle className="h-3 w-3 mr-1" />
           Lecture seule
         </Badge>
       )
     case "treasury":
       return (
         <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
           <AlertCircle className="h-3 w-3 mr-1" />
           Trésorerie
         </Badge>
       )
     case "marketing":
       return (
         <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">
           <AlertCircle className="h-3 w-3 mr-1" />
           Marketing
         </Badge>
       )
     case "development":
       return (
         <Badge className="bg-cyan-100 text-cyan-800 hover:bg-cyan-200">
           <AlertCircle className="h-3 w-3 mr-1" />
           Développement
         </Badge>
       )
     case "liquidity":
       return (
         <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
           <AlertCircle className="h-3 w-3 mr-1" />
           Liquidité
         </Badge>
       )
     default:
       return <Badge>{role}</Badge>
   }
 }

 const getNetworkBadge = (network: string) => {
   switch (network) {
     case "solana":
       return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Solana</Badge>
     case "bnb":
       return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">BNB Chain</Badge>
     case "ethereum":
       return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Ethereum</Badge>
     default:
       return <Badge>{network}</Badge>
   }
 }

 const getStatusBadge = (status: string) => {
   switch (status) {
     case "active":
       return <Badge className="bg-green-100 text-green-800 border-green-200">Actif</Badge>
     case "locked":
       return <Badge className="bg-red-100 text-red-800 border-red-200">Verrouillé</Badge>
     case "inactive":
       return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Inactif</Badge>
     default:
       return <Badge>{status}</Badge>
   }
 }

 // Format date
 const formatDate = (dateString: string): string => {
   try {
     const date = new Date(dateString)
     return new Intl.DateTimeFormat('fr-FR', {
       day: '2-digit',
       month: '2-digit',
       year: 'numeric',
       hour: '2-digit',
       minute: '2-digit'
     }).format(date)
   } catch (e)\

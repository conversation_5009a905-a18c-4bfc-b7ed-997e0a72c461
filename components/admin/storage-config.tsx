"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { AlertCircle, CheckCircle2, ExternalLink, Upload, Info } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { getStorageConfig, updateStorageConfig } from "@/lib/admin-service"
import StorageService, { type StorageConfig } from "@/lib/storage-service"

export default function StorageConfiguration() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [testFile, setTestFile] = useState<File | null>(null)
  const [testResult, setTestResult] = useState<{ success: boolean; url: string } | null>(null)

  // Configuration IPFS
  const [ipfsEnabled, setIpfsEnabled] = useState(true)
  const [ipfsProvider, setIpfsProvider] = useState<"nft.storage" | "pinata" | "infura" | "filebase">("nft.storage")
  const [ipfsApiKey, setIpfsApiKey] = useState("")
  const [ipfsGateway, setIpfsGateway] = useState("https://ipfs.io/ipfs/")
  const [ipfsEndpoint, setIpfsEndpoint] = useState<string | null>(null)

  // Configuration Arweave
  const [arweaveEnabled, setArweaveEnabled] = useState(false)
  const [arweaveJwk, setArweaveJwk] = useState("")
  const [arweaveGateway, setArweaveGateway] = useState("https://arweave.net/")

  // Charger la configuration initiale
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsLoading(true)
        const config = await getStorageConfig()

        // Configuration IPFS
        setIpfsEnabled(config.ipfs.enabled)
        setIpfsProvider(config.ipfs.provider)
        setIpfsApiKey(config.ipfs.apiKey)
        setIpfsGateway(config.ipfs.gateway)
        setIpfsEndpoint(config.ipfs.endpoint || null)

        // Configuration Arweave
        setArweaveEnabled(config.arweave.enabled)
        setArweaveJwk(config.arweave.jwk ? JSON.stringify(config.arweave.jwk, null, 2) : "")
        setArweaveGateway(config.arweave.gateway)
      } catch (err) {
        console.error("Error loading storage config:", err)
        setError("Failed to load storage configuration")
      } finally {
        setIsLoading(false)
      }
    }

    loadConfig()
  }, [])

  // Sauvegarder la configuration
  const handleSaveSettings = async () => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      // Valider la configuration
      if (ipfsEnabled && !ipfsApiKey) {
        throw new Error("IPFS API key is required when IPFS is enabled")
      }

      if (arweaveEnabled && !arweaveJwk) {
        throw new Error("Arweave JWK is required when Arweave is enabled")
      }

      // Préparer la configuration
      const config: StorageConfig = {
        ipfs: {
          enabled: ipfsEnabled,
          provider: ipfsProvider,
          apiKey: ipfsApiKey,
          gateway: ipfsGateway,
          endpoint: ipfsEndpoint || null,
        },
        arweave: {
          enabled: arweaveEnabled,
          jwk: arweaveJwk ? JSON.parse(arweaveJwk) : null,
          gateway: arweaveGateway,
        },
      }

      // Mettre à jour la configuration
      const result = await updateStorageConfig(config)

      if (result.success) {
        setSuccess("Storage configuration saved successfully!")
        toast({
          title: "Settings Saved",
          description: "Storage configuration has been updated successfully.",
        })
      } else {
        throw new Error("Failed to save storage configuration")
      }
    } catch (err: any) {
      console.error("Error saving storage config:", err)
      setError(err.message || "Failed to save storage configuration")
      toast({
        title: "Error",
        description: err.message || "Failed to save storage configuration",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Tester le téléchargement
  const handleTestUpload = async () => {
    if (!testFile) {
      toast({
        title: "No file selected",
        description: "Please select a file to test the upload",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)
    setTestResult(null)

    try {
      // Initialiser le service de stockage avec la configuration actuelle
      const config: StorageConfig = {
        ipfs: {
          enabled: ipfsEnabled,
          provider: ipfsProvider,
          apiKey: ipfsApiKey,
          gateway: ipfsGateway,
          endpoint: ipfsEndpoint || null,
        },
        arweave: {
          enabled: arweaveEnabled,
          jwk: arweaveJwk ? JSON.parse(arweaveJwk) : null,
          gateway: arweaveGateway,
        },
      }

      // Mettre à jour la configuration temporairement pour le test
      await updateStorageConfig(config)

      // Télécharger le fichier
      const url = await StorageService.uploadFile(testFile)

      if (!url) {
        throw new Error("Failed to upload test file")
      }

      setTestResult({
        success: true,
        url,
      })

      toast({
        title: "Upload Successful",
        description: "Test file was uploaded successfully",
      })
    } catch (err: any) {
      console.error("Error testing upload:", err)
      setError(err.message || "Failed to upload test file")
      toast({
        title: "Upload Failed",
        description: err.message || "Failed to upload test file",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Gérer la sélection de fichier
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setTestFile(e.target.files[0])
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Storage Configuration</CardTitle>
        <CardDescription>Configure decentralized storage for token metadata and images</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="ipfs" className="w-full">
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="ipfs">IPFS</TabsTrigger>
            <TabsTrigger value="arweave">Arweave</TabsTrigger>
          </TabsList>

          {/* IPFS Tab */}
          <TabsContent value="ipfs" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="ipfsEnabled">Enable IPFS Storage</Label>
                <p className="text-sm text-muted-foreground">Use IPFS for storing token metadata and images</p>
              </div>
              <Switch id="ipfsEnabled" checked={ipfsEnabled} onCheckedChange={setIpfsEnabled} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="ipfsProvider">IPFS Provider</Label>
              <Select
                value={ipfsProvider}
                onValueChange={(value: any) => setIpfsProvider(value)}
                disabled={!ipfsEnabled}
              >
                <SelectTrigger id="ipfsProvider">
                  <SelectValue placeholder="Select IPFS provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="nft.storage">NFT.Storage</SelectItem>
                  <SelectItem value="pinata">Pinata</SelectItem>
                  <SelectItem value="infura">Infura</SelectItem>
                  <SelectItem value="filebase">Filebase</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {ipfsProvider === "filebase" && (
              <div className="space-y-2">
                <Label htmlFor="ipfsEndpoint">Filebase S3 Endpoint</Label>
                <Input
                  id="ipfsEndpoint"
                  value={ipfsEndpoint || "https://s3.filebase.com"}
                  onChange={(e) => setIpfsEndpoint(e.target.value)}
                  placeholder="https://s3.filebase.com"
                  disabled={!ipfsEnabled}
                />
                <p className="text-xs text-muted-foreground">L'endpoint S3 de Filebase pour le stockage IPFS</p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="ipfsApiKey">API Key</Label>
              <Input
                id="ipfsApiKey"
                value={ipfsApiKey}
                onChange={(e) => setIpfsApiKey(e.target.value)}
                placeholder="Enter your IPFS provider API key"
                disabled={!ipfsEnabled}
                type="password"
              />
              <p className="text-xs text-muted-foreground">
                Get your API key from{" "}
                <a
                  href="https://nft.storage/login/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline inline-flex items-center"
                >
                  NFT.Storage <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ipfsGateway">IPFS Gateway</Label>
              <Input
                id="ipfsGateway"
                value={ipfsGateway}
                onChange={(e) => setIpfsGateway(e.target.value)}
                placeholder="Enter IPFS gateway URL"
                disabled={!ipfsEnabled}
              />
              <p className="text-xs text-muted-foreground">
                Example: https://ipfs.io/ipfs/, https://gateway.pinata.cloud/ipfs/
              </p>
            </div>
            <p className="text-xs text-muted-foreground">
              Pour Filebase, utilisez la clé secrète de votre compte Filebase.
              <a
                href="https://docs.filebase.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline inline-flex items-center ml-1"
              >
                Documentation Filebase <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </p>
          </TabsContent>

          {/* Arweave Tab */}
          <TabsContent value="arweave" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="arweaveEnabled">Enable Arweave Storage</Label>
                <p className="text-sm text-muted-foreground">
                  Use Arweave for permanent storage of token metadata and images
                </p>
              </div>
              <Switch id="arweaveEnabled" checked={arweaveEnabled} onCheckedChange={setArweaveEnabled} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="arweaveJwk">Arweave JWK</Label>
              <Textarea
                id="arweaveJwk"
                value={arweaveJwk}
                onChange={(e) => setArweaveJwk(e.target.value)}
                placeholder="Paste your Arweave JWK JSON"
                disabled={!arweaveEnabled}
                rows={8}
              />
              <p className="text-xs text-muted-foreground">
                Get your JWK from{" "}
                <a
                  href="https://www.arweave.org/wallet"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline inline-flex items-center"
                >
                  Arweave <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="arweaveGateway">Arweave Gateway</Label>
              <Input
                id="arweaveGateway"
                value={arweaveGateway}
                onChange={(e) => setArweaveGateway(e.target.value)}
                placeholder="Enter Arweave gateway URL"
                disabled={!arweaveEnabled}
              />
              <p className="text-xs text-muted-foreground">Example: https://arweave.net/</p>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-6 space-y-4">
          <div className="border rounded-md p-4">
            <h3 className="text-lg font-medium mb-2">Test Upload</h3>
            <p className="text-sm text-muted-foreground mb-4">Test your storage configuration by uploading a file</p>

            <div className="flex items-center gap-4 mb-4">
              <Input id="testFile" type="file" onChange={handleFileChange} disabled={isLoading} />
              <Button onClick={handleTestUpload} disabled={isLoading || !testFile}>
                <Upload className="mr-2 h-4 w-4" />
                Test Upload
              </Button>
            </div>

            {testResult && (
              <Alert className="bg-green-50 border-green-200">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertTitle>Upload Successful</AlertTitle>
                <AlertDescription>
                  <p>File uploaded successfully!</p>
                  <a
                    href={testResult.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline inline-flex items-center mt-2"
                  >
                    View File <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </AlertDescription>
              </Alert>
            )}
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mt-4 bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <Alert className="mt-4">
          <Info className="h-4 w-4" />
          <AlertTitle>Important</AlertTitle>
          <AlertDescription>
            <p>Storing data on decentralized networks requires tokens/credits:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>NFT.Storage is free but has rate limits</li>
              <li>Arweave requires AR tokens to pay for permanent storage</li>
              <li>Pinata and Infura have free tiers with limitations</li>
            </ul>
          </AlertDescription>
        </Alert>

        <div className="flex justify-end mt-4">
          <Button onClick={handleSaveSettings} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Settings"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

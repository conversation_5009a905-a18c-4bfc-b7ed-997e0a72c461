import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

interface WalletConnectionErrorProps {
  error: string
}

export function WalletConnectionError({ error }: WalletConnectionErrorProps) {
  // Analyser l'erreur pour fournir des conseils spécifiques
  let errorTitle = "Erreur de connexion au wallet"
  let errorDescription = error
  let solution = "Veuillez réessayer ou utiliser un autre wallet."

  if (error.includes("User rejected")) {
    errorTitle = "Connexion rejetée"
    errorDescription = "Vous avez rejeté la demande de connexion."
    solution = "Veuillez approuver la connexion dans votre wallet pour continuer."
  } else if (error.includes("wallet adapter not found")) {
    errorTitle = "Wallet non détecté"
    errorDescription = "Aucun wallet compatible n'a été détecté dans votre navigateur."
    solution = "Veuillez installer l'extension Phantom, Solflare ou un autre wallet Solana compatible."
  } else if (error.includes("timeout")) {
    errorTitle = "Délai d'attente dépassé"
    errorDescription = "La connexion au wallet a pris trop de temps."
    solution = "Vérifiez que votre wallet est déverrouillé et réessayez."
  } else if (error.includes("not supported")) {
    errorTitle = "Méthode non supportée"
    errorDescription = "Votre wallet ne supporte pas cette opération."
    solution = "Essayez d'utiliser un wallet plus récent ou avec plus de fonctionnalités."
  }

  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{errorTitle}</AlertTitle>
      <AlertDescription>
        <p>{errorDescription}</p>
        <p className="mt-2 font-medium">{solution}</p>
      </AlertDescription>
    </Alert>
  )
}

"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { AlertCircle } from "lucide-react"

const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>[svg]]:h-4 [&>[svg]]:w-4 [&>[svg]]:text-foreground",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive: "border-destructive/50 bg-destructive text-destructive-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
)

const Alert = React.forwardRef<
  React.ElementRef<"div">,
  React.ComponentPropsWithoutRef<"div"> & VariantProps<typeof alertVariants>
>(({ className, variant, children, ...props }, ref) => (
  <div className={cn(alertVariants({ variant }), className)} role="alert" ref={ref} {...props}>
    {children}
  </div>
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<React.ElementRef<"div">, React.ComponentPropsWithoutRef<"div">>(
  ({ className, children, ...props }, ref) => (
    <div className={cn("text-sm font-semibold leading-none tracking-tight", className)} {...props} ref={ref}>
      {children}
    </div>
  ),
)
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<React.ElementRef<"div">, React.ComponentPropsWithoutRef<"div">>(
  ({ className, children, ...props }, ref) => (
    <div className={cn("text-sm opacity-70", className)} {...props} ref={ref}>
      {children}
    </div>
  ),
)
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription, AlertCircle }

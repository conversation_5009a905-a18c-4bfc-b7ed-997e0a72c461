"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { CheckIcon } from "lucide-react"

interface StepProps {
  title: string
  description?: string
  isActive?: boolean
  isComplete?: boolean
  children?: React.ReactNode
  onClick?: () => void
}

export function Step({ title, description, isActive, isComplete, children, onClick }: StepProps) {
  return (
    <div className={cn("flex-1 cursor-pointer", onClick && "cursor-pointer")} onClick={onClick}>
      <div className="flex items-center">
        <div
          className={cn(
            "flex h-8 w-8 shrink-0 items-center justify-center rounded-full border text-center font-medium",
            isActive && "border-primary bg-primary text-primary-foreground",
            isComplete && "border-primary bg-primary text-primary-foreground",
            !isActive && !isComplete && "border-muted-foreground text-muted-foreground",
          )}
        >
          {isComplete ? <CheckIcon className="h-4 w-4" /> : <span>{children}</span>}
        </div>
        <div className="ml-2">
          <StepTitle>{title}</StepTitle>
          {description && <StepDescription>{description}</StepDescription>}
        </div>
      </div>
    </div>
  )
}

interface StepTitleProps {
  children: React.ReactNode
}

export function StepTitle({ children }: StepTitleProps) {
  return <div className="text-sm font-medium">{children}</div>
}

interface StepDescriptionProps {
  children: React.ReactNode
}

export function StepDescription({ children }: StepDescriptionProps) {
  return <div className="text-xs text-muted-foreground">{children}</div>
}

interface StepperProps {
  currentStep: number
  className?: string
  orientation?: "horizontal" | "vertical"
  children: React.ReactNode
}

export function Stepper({ currentStep, className, orientation = "horizontal", children }: StepperProps) {
  const steps = React.Children.toArray(children)

  return (
    <div
      className={cn(
        "flex w-full",
        orientation === "vertical" && "flex-col space-y-4",
        orientation === "horizontal" && "space-x-4",
        className,
      )}
    >
      {steps.map((step, index) => {
        const isActive = currentStep === index
        const isComplete = currentStep > index

        if (React.isValidElement(step)) {
          return React.cloneElement(step, {
            key: index,
            isActive,
            isComplete,
            children: index + 1,
          })
        }

        return null
      })}
    </div>
  )
}

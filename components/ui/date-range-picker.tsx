"use client"
import type { DateRange } from "react-day-picker"

import { useEffect, useState } from "react"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import type { DateRange as DateRangeType } from "react-day-picker"
import { fr } from "date-fns/locale"
import type { Locale } from "date-fns"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

interface DateRangePickerProps {
  value?: DateRange
  onChange?: (value?: DateRange) => void
  calendarTodayClassName?: string
  locale?: Locale
}

export function DateRangePicker({ value, onChange, calendarTodayClassName, locale = fr }: DateRangePickerProps) {
  const [date, setDate] = useState<DateRangeType | undefined>(value)

  useEffect(() => {
    setDate(value)
  }, [value])

  return (
    <div className={cn("grid gap-2")}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn("w-[300px] justify-start text-left font-normal", !date && "text-muted-foreground")}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "P", { locale })} - {format(date.to, "P", { locale })}
                </>
              ) : (
                format(date.from, "P", { locale })
              )
            ) : (
              <span>Sélectionner une période</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={(newDate) => {
              setDate(newDate)
              onChange?.(newDate)
            }}
            numberOfMonths={2}
            locale={locale}
            todayClassName={calendarTodayClassName}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}

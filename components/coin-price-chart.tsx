"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Area, AreaChart } from "recharts"
import { ArrowUp, ArrowDown, Info } from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import blockchainService from "@/lib/blockchain-service"

interface CoinPriceChartProps {
  coinId: string
  coinName?: string
  coinSymbol?: string
  currentPrice?: number
  priceChange24h?: number
  chartData?: [number, number][]
  loading?: boolean
}

export default function CoinPriceChart({
  coinId,
  coinName = "Token",
  coinSymbol = "",
  currentPrice = 0,
  priceChange24h = 0,
  chartData: initialChartData,
  loading: initialLoading = false,
}: CoinPriceChartProps) {
  const [timeframe, setTimeframe] = useState("24h")
  const [formattedData, setFormattedData] = useState<any[]>([])
  const [chartData, setChartData] = useState<[number, number][]>(initialChartData || [])
  const [loading, setLoading] = useState<boolean>(initialLoading)
  const [error, setError] = useState<string | null>(null)
  const [highestPrice, setHighestPrice] = useState<number>(0)
  const [lowestPrice, setLowestPrice] = useState<number>(0)
  const [priceAtStart, setPriceAtStart] = useState<number>(0)
  const [priceChangePercent, setPriceChangePercent] = useState<number>(priceChange24h || 0)

  // Convertir le timeframe en jours
  const timeframeToDays = useMemo(() => {
    switch (timeframe) {
      case "24h":
        return 1
      case "7d":
        return 7
      case "30d":
        return 30
      case "1y":
        return 365
      default:
        return 1
    }
  }, [timeframe])

  // Charger les données du graphique en fonction du timeframe
  useEffect(() => {
    const fetchChartData = async () => {
      if (!coinId) return

      setLoading(true)
      setError(null)

      try {
        // Utiliser le service blockchain pour récupérer l'historique des prix
        const data = await blockchainService.getTokenPriceHistory(coinId, timeframeToDays)

        if (data && data.length > 0) {
          setChartData(data)

          // Calculer les statistiques
          const prices = data.map((point) => point[1])
          const highest = Math.max(...prices)
          const lowest = Math.min(...prices)
          const first = prices[0]
          const last = prices[prices.length - 1]
          const changePercent = ((last - first) / first) * 100

          setHighestPrice(highest)
          setLowestPrice(lowest)
          setPriceAtStart(first)
          setPriceChangePercent(changePercent)
        } else {
          setError("Aucune donnée disponible pour cette période")
        }
      } catch (err) {
        console.error("Error fetching chart data:", err)
        setError("Erreur lors du chargement des données")
      } finally {
        setLoading(false)
      }
    }

    fetchChartData()
  }, [coinId, timeframeToDays])

  // Formater les données pour le graphique
  useEffect(() => {
    if (chartData && chartData.length > 0) {
      const formatted = chartData.map((point) => {
        const date = new Date(point[0])

        // Formater la date en fonction du timeframe
        let formattedTime
        if (timeframeToDays <= 1) {
          formattedTime = date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
        } else if (timeframeToDays <= 7) {
          formattedTime = date.toLocaleDateString([], { weekday: "short", hour: "2-digit", minute: "2-digit" })
        } else {
          formattedTime = date.toLocaleDateString([], { month: "short", day: "numeric" })
        }

        return {
          time: formattedTime,
          timestamp: point[0],
          price: point[1],
          // Ajouter des données pour l'aire sous la courbe
          priceFill: point[1],
        }
      })

      setFormattedData(formatted)
    }
  }, [chartData, timeframeToDays])

  // Déterminer la couleur du graphique en fonction du changement de prix
  const chartColor = priceChangePercent >= 0 ? "#10b981" : "#ef4444"
  const chartGradientStart = priceChangePercent >= 0 ? "rgba(16, 185, 129, 0.2)" : "rgba(239, 68, 68, 0.2)"
  const chartGradientEnd = "rgba(0, 0, 0, 0)"

  // Formater les nombres pour l'affichage
  const formatPrice = (price: number) => {
    if (price < 0.001) return price.toFixed(6)
    if (price < 1) return price.toFixed(4)
    return price.toFixed(2)
  }

  // Calculer le domaine Y pour le graphique
  const calculateYDomain = () => {
    if (formattedData.length === 0) return ["auto", "auto"]

    const prices = formattedData.map((d) => d.price)
    const min = Math.min(...prices)
    const max = Math.max(...prices)
    const padding = (max - min) * 0.1

    return [min - padding, max + padding]
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <span>{coinName} </span>
            {coinSymbol && <span className="text-muted-foreground ml-2">({coinSymbol.toUpperCase()})</span>}
          </div>
          <div className="flex items-center">
            <span className="text-xl font-bold mr-2">{formatCurrency(currentPrice || 0)}</span>
            <span
              className={`flex items-center text-sm ${priceChangePercent >= 0 ? "text-green-500" : "text-red-500"}`}
            >
              {priceChangePercent >= 0 ? <ArrowUp className="h-4 w-4 mr-1" /> : <ArrowDown className="h-4 w-4 mr-1" />}
              {Math.abs(priceChangePercent).toFixed(2)}%
            </span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="24h" value={timeframe} onValueChange={setTimeframe} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="24h">24h</TabsTrigger>
            <TabsTrigger value="7d">7j</TabsTrigger>
            <TabsTrigger value="30d">30j</TabsTrigger>
            <TabsTrigger value="1y">1a</TabsTrigger>
          </TabsList>

          <TabsContent value={timeframe}>
            {loading ? (
              <div className="w-full h-[300px] flex items-center justify-center">
                <Skeleton className="w-full h-full" />
              </div>
            ) : error ? (
              <div className="w-full h-[300px] flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <Info className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                  <p>{error}</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="w-full h-[300px]">
                  {formattedData && formattedData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={formattedData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                        <defs>
                          <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={chartColor} stopOpacity={0.3} />
                            <stop offset="95%" stopColor={chartColor} stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#333" opacity={0.1} />
                        <XAxis
                          dataKey="time"
                          tick={{ fontSize: 12 }}
                          tickLine={{ stroke: "#666" }}
                          axisLine={{ stroke: "#666" }}
                          minTickGap={20}
                        />
                        <YAxis
                          domain={calculateYDomain()}
                          tick={{ fontSize: 12 }}
                          tickLine={{ stroke: "#666" }}
                          axisLine={{ stroke: "#666" }}
                          tickFormatter={(value) => `$${formatPrice(value)}`}
                          width={60}
                        />
                        <Tooltip
                          formatter={(value: number) => [`$${formatPrice(value)}`, "Prix"]}
                          labelFormatter={(label) => `${label}`}
                          contentStyle={{ backgroundColor: "#1f2937", border: "none", borderRadius: "4px" }}
                        />
                        <ReferenceLine
                          y={priceAtStart}
                          stroke="#666"
                          strokeDasharray="3 3"
                          label={{
                            value: `$${formatPrice(priceAtStart)}`,
                            position: "left",
                            fill: "#666",
                            fontSize: 10,
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="price"
                          stroke={chartColor}
                          strokeWidth={2}
                          fillOpacity={1}
                          fill="url(#colorPrice)"
                          activeDot={{ r: 6, strokeWidth: 0 }}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                      Aucune donnée disponible pour cette période
                    </div>
                  )}
                </div>

                {/* Statistiques du graphique */}
                <div className="grid grid-cols-4 gap-2 text-sm">
                  <div className="bg-muted p-2 rounded-md">
                    <div className="text-muted-foreground">Prix de départ</div>
                    <div className="font-medium">${formatPrice(priceAtStart)}</div>
                  </div>
                  <div className="bg-muted p-2 rounded-md">
                    <div className="text-muted-foreground">Plus haut</div>
                    <div className="font-medium text-green-500">${formatPrice(highestPrice)}</div>
                  </div>
                  <div className="bg-muted p-2 rounded-md">
                    <div className="text-muted-foreground">Plus bas</div>
                    <div className="font-medium text-red-500">${formatPrice(lowestPrice)}</div>
                  </div>
                  <div className="bg-muted p-2 rounded-md">
                    <div className="text-muted-foreground">Variation</div>
                    <div className={`font-medium ${priceChangePercent >= 0 ? "text-green-500" : "text-red-500"}`}>
                      {priceChangePercent >= 0 ? "+" : ""}
                      {priceChangePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Line, LineChart, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

interface MarketStatsProps {
  tokenAddress: string
  tokenSymbol: string
}

interface PriceData {
  timestamp: number
  price: number
}

interface VolumeData {
  timestamp: number
  volume: number
}

interface MarketData {
  currentPrice: number
  priceChange24h: number
  volume24h: number
  marketCap: number
  totalSupply: number
  priceHistory: PriceData[]
  volumeHistory: VolumeData[]
}

export default function MarketStats({ tokenAddress, tokenSymbol }: MarketStatsProps) {
  const [marketData, setMarketData] = useState<MarketData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeframe, setTimeframe] = useState<"24h" | "7d" | "30d" | "all">("7d")

  useEffect(() => {
    async function fetchMarketData() {
      try {
        // In a real implementation, this would fetch from an API
        // For now, we'll use mock data

        // Generate mock price history
        const now = Date.now()
        const priceHistory: PriceData[] = []
        const volumeHistory: VolumeData[] = []

        // Generate data points for the last 30 days
        for (let i = 30; i >= 0; i--) {
          const timestamp = now - i * 24 * 60 * 60 * 1000
          // Generate a price that fluctuates between 0.04 and 0.06
          const price = 0.05 + (Math.random() * 0.02 - 0.01)
          priceHistory.push({ timestamp, price })

          // Generate random volume between 10000 and 50000
          const volume = 10000 + Math.random() * 40000
          volumeHistory.push({ timestamp, volume })
        }

        const mockMarketData: MarketData = {
          currentPrice: priceHistory[priceHistory.length - 1].price,
          priceChange24h: 5.2, // percentage
          volume24h: 42500,
          marketCap: 5000000,
          totalSupply: 100000000,
          priceHistory,
          volumeHistory,
        }

        setMarketData(mockMarketData)
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching market data:", error)
        setIsLoading(false)
      }
    }

    fetchMarketData()
  }, [tokenAddress])

  // Filter data based on selected timeframe
  const getFilteredData = () => {
    if (!marketData) return []

    const now = Date.now()
    let cutoff: number

    switch (timeframe) {
      case "24h":
        cutoff = now - 24 * 60 * 60 * 1000
        break
      case "7d":
        cutoff = now - 7 * 24 * 60 * 60 * 1000
        break
      case "30d":
        cutoff = now - 30 * 24 * 60 * 60 * 1000
        break
      case "all":
      default:
        cutoff = 0
    }

    const filteredPriceHistory = marketData.priceHistory.filter((data) => data.timestamp >= cutoff)

    // Format data for chart
    return filteredPriceHistory.map((data) => ({
      date: new Date(data.timestamp).toLocaleDateString(),
      price: data.price,
    }))
  }

  const chartData = getFilteredData()

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Market Stats for {tokenSymbol}</CardTitle>
        <div className="flex space-x-2 mt-2">
          <button
            onClick={() => setTimeframe("24h")}
            className={`px-3 py-1 rounded text-sm ${timeframe === "24h" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
          >
            24H
          </button>
          <button
            onClick={() => setTimeframe("7d")}
            className={`px-3 py-1 rounded text-sm ${timeframe === "7d" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
          >
            7D
          </button>
          <button
            onClick={() => setTimeframe("30d")}
            className={`px-3 py-1 rounded text-sm ${timeframe === "30d" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
          >
            30D
          </button>
          <button
            onClick={() => setTimeframe("all")}
            className={`px-3 py-1 rounded text-sm ${timeframe === "all" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
          >
            All
          </button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : marketData ? (
          <>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="text-sm text-gray-500">Price</div>
                <div className="text-xl font-bold">${marketData.currentPrice.toFixed(4)}</div>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="text-sm text-gray-500">24h Change</div>
                <div
                  className={`text-xl font-bold ${marketData.priceChange24h >= 0 ? "text-green-500" : "text-red-500"}`}
                >
                  {marketData.priceChange24h >= 0 ? "+" : ""}
                  {marketData.priceChange24h.toFixed(2)}%
                </div>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="text-sm text-gray-500">24h Volume</div>
                <div className="text-xl font-bold">${marketData.volume24h.toLocaleString()}</div>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="text-sm text-gray-500">Market Cap</div>
                <div className="text-xl font-bold">${marketData.marketCap.toLocaleString()}</div>
              </div>
            </div>

            <div className="h-64">
              <ChartContainer
                config={{
                  price: {
                    label: "Price",
                    color: "hsl(var(--chart-1))",
                  },
                }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => {
                        // Simplify date format based on timeframe
                        if (timeframe === "24h") {
                          return new Date(value).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
                        }
                        return value
                      }}
                    />
                    <YAxis
                      domain={["dataMin", "dataMax"]}
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => `$${value.toFixed(4)}`}
                    />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line type="monotone" dataKey="price" stroke="var(--color-price)" dot={false} strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">No market data available</div>
        )}
      </CardContent>
    </Card>
  )
}

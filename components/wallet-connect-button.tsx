"use client"

import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { useEffect, useState } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

export function WalletConnectButton() {
  const { wallet, connecting, connected, publicKey } = useWallet()
  const [error, setError] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (wallet?.adapter) {
      const onError = (error: any) => {
        console.error("Wallet error:", error)
        setError(error.message || "Une erreur s'est produite lors de la connexion au wallet")
        setTimeout(() => setError(null), 5000)
      }

      wallet.adapter.on("error", onError)
      return () => {
        wallet.adapter.off("error", onError)
      }
    }
  }, [wallet])

  if (!isClient) {
    return <div className="h-[38px] w-[180px] bg-gray-200 rounded-md animate-pulse"></div>
  }

  return (
    <div className="relative">
      <WalletMultiButton className="!bg-primary hover:!bg-primary/90" />

      {error && (
        <Alert variant="destructive" className="mt-2 absolute top-full right-0 w-80 z-50">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {connected && publicKey && (
        <div className="mt-2 text-xs text-green-600">
          Connecté: {publicKey.toString().slice(0, 4)}...{publicKey.toString().slice(-4)}
        </div>
      )}
    </div>
  )
}

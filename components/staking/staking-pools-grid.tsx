"use client"

import { useState, useEffect } from "react"
import { getStakingPools, getUserStakings, type StakingPool } from "@/lib/staking-service"
import { StakingPoolCard } from "./staking-pool-card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useWallet } from "@solana/wallet-adapter-react"
import { Search, SlidersHorizontal } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

export function StakingPoolsGrid() {
  const [pools, setPools] = useState<StakingPool[]>([])
  const [userStakings, setUserStakings] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("apr")
  const [showFilters, setShowFilters] = useState(false)
  const { publicKey } = useWallet()

  useEffect(() => {
    const fetchPools = async () => {
      setIsLoading(true)
      try {
        const poolsData = await getStakingPools()
        setPools(poolsData)

        if (publicKey) {
          const stakingsData = await getUserStakings(publicKey.toString())
          setUserStakings(stakingsData)
        }
      } catch (error) {
        console.error("Error fetching staking pools:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPools()
  }, [publicKey])

  const refreshData = async () => {
    try {
      const poolsData = await getStakingPools()
      setPools(poolsData)

      if (publicKey) {
        const stakingsData = await getUserStakings(publicKey.toString())
        setUserStakings(stakingsData)
      }
    } catch (error) {
      console.error("Error refreshing staking data:", error)
    }
  }

  // Enrichir les pools avec les données de staking de l'utilisateur
  const enrichedPools = pools.map((pool) => {
    const userStaking = userStakings.find((staking) => staking.poolId === pool.id)
    if (userStaking) {
      return {
        ...pool,
        userStaked: userStaking.amount,
        userRewards: userStaking.rewards,
        userStartTime: userStaking.startTime,
        userEndTime: userStaking.endTime,
      }
    }
    return pool
  })

  // Filtrer les pools en fonction de l'onglet actif et de la recherche
  const filteredPools = enrichedPools.filter((pool) => {
    // Filtrer par onglet
    if (activeTab === "my-stakes" && !userStakings.some((staking) => staking.poolId === pool.id)) {
      return false
    }

    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return pool.tokenName.toLowerCase().includes(query) || pool.tokenSymbol.toLowerCase().includes(query)
    }

    return true
  })

  // Trier les pools
  const sortedPools = [...filteredPools].sort((a, b) => {
    switch (sortBy) {
      case "apr":
        return b.apr - a.apr
      case "lockPeriod":
        return a.lockPeriod - b.lockPeriod
      case "totalStaked":
        return b.totalStaked - a.totalStaked
      case "newest":
        return b.createdAt - a.createdAt
      default:
        return 0
    }
  })

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between">
          <Skeleton className="h-10 w-[200px]" />
          <Skeleton className="h-10 w-[200px]" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-[400px] w-full" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">Tous les pools</TabsTrigger>
            <TabsTrigger value="my-stakes" disabled={!publicKey || userStakings.length === 0}>
              Mes stakes
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher..."
              className="pl-8 w-[200px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            variant={showFilters ? "default" : "outline"}
            size="icon"
            onClick={() => setShowFilters(!showFilters)}
          >
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {showFilters && (
        <div className="flex flex-wrap gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="space-y-1">
            <label htmlFor="sort-by" className="text-sm font-medium">
              Trier par
            </label>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger id="sort-by" className="w-[180px]">
                <SelectValue placeholder="Trier par" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="apr">APR (décroissant)</SelectItem>
                <SelectItem value="lockPeriod">Période de lock (croissant)</SelectItem>
                <SelectItem value="totalStaked">Total staké (décroissant)</SelectItem>
                <SelectItem value="newest">Plus récent</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {sortedPools.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Aucun pool de staking trouvé.</p>
          {activeTab === "my-stakes" && (
            <Button variant="outline" className="mt-4" onClick={() => setActiveTab("all")}>
              Voir tous les pools
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedPools.map((pool) => (
            <StakingPoolCard key={pool.id} pool={pool} onStakeSuccess={refreshData} />
          ))}
        </div>
      )}
    </div>
  )
}

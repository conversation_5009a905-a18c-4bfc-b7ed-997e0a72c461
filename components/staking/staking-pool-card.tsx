"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Coins, Clock, Lock, ArrowRight, Info } from "lucide-react"
import { formatNumber } from "@/lib/utils"
import { stakeTokens, unstakeTokens, claimRewards } from "@/lib/staking-service"
import { useWallet } from "@solana/wallet-adapter-react"
import { Keypair } from "@solana/web3.js"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface StakingPoolCardProps {
  pool: {
    id: string
    tokenMint: string
    tokenName: string
    tokenSymbol: string
    apr: number
    lockPeriod: number // en jours
    totalStaked: number
    stakerCount: number
    createdAt: number
    userStaked?: number
    userRewards?: number
    userStartTime?: number
    userEndTime?: number
  }
  onStakeSuccess?: () => void
}

export function StakingPoolCard({ pool, onStakeSuccess }: StakingPoolCardProps) {
  const [stakeAmount, setStakeAmount] = useState("")
  const [unstakeAmount, setUnstakeAmount] = useState("")
  const [isStaking, setIsStaking] = useState(false)
  const [isUnstaking, setIsUnstaking] = useState(false)
  const [isClaiming, setIsClaiming] = useState(false)
  const [activeTab, setActiveTab] = useState<"stake" | "unstake" | "info">("stake")
  const { toast } = useToast()
  const { publicKey, signTransaction } = useWallet()

  const handleStake = async () => {
    if (!publicKey || !signTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour staker des tokens.",
        variant: "destructive",
      })
      return
    }

    if (!stakeAmount || Number.parseFloat(stakeAmount) <= 0) {
      toast({
        title: "Montant invalide",
        description: "Veuillez entrer un montant valide à staker.",
        variant: "destructive",
      })
      return
    }

    setIsStaking(true)
    try {
      // Dans une implémentation réelle, nous utiliserions le keypair du portefeuille connecté
      // Ici, nous simulons avec un keypair temporaire
      const tempKeypair = Keypair.generate()

      const success = await stakeTokens(tempKeypair, pool.id, pool.tokenMint, Number.parseFloat(stakeAmount))

      if (success) {
        toast({
          title: "Staking réussi",
          description: `Vous avez staké ${stakeAmount} ${pool.tokenSymbol} avec succès.`,
        })
        setStakeAmount("")
        if (onStakeSuccess) onStakeSuccess()
      } else {
        throw new Error("Échec du staking")
      }
    } catch (error: any) {
      console.error("Staking error:", error)
      toast({
        title: "Erreur de staking",
        description: error.message || "Une erreur s'est produite lors du staking.",
        variant: "destructive",
      })
    } finally {
      setIsStaking(false)
    }
  }

  const handleUnstake = async () => {
    if (!publicKey || !signTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour unstaker des tokens.",
        variant: "destructive",
      })
      return
    }

    if (!unstakeAmount || Number.parseFloat(unstakeAmount) <= 0) {
      toast({
        title: "Montant invalide",
        description: "Veuillez entrer un montant valide à unstaker.",
        variant: "destructive",
      })
      return
    }

    if (!pool.userStaked || Number.parseFloat(unstakeAmount) > pool.userStaked) {
      toast({
        title: "Montant trop élevé",
        description: "Vous ne pouvez pas unstaker plus que ce que vous avez staké.",
        variant: "destructive",
      })
      return
    }

    setIsUnstaking(true)
    try {
      // Dans une implémentation réelle, nous utiliserions le keypair du portefeuille connecté
      // Ici, nous simulons avec un keypair temporaire
      const tempKeypair = Keypair.generate()

      const success = await unstakeTokens(tempKeypair, pool.id, pool.tokenMint, Number.parseFloat(unstakeAmount))

      if (success) {
        toast({
          title: "Unstaking réussi",
          description: `Vous avez unstaké ${unstakeAmount} ${pool.tokenSymbol} avec succès.`,
        })
        setUnstakeAmount("")
        if (onStakeSuccess) onStakeSuccess()
      } else {
        throw new Error("Échec de l'unstaking")
      }
    } catch (error: any) {
      console.error("Unstaking error:", error)
      toast({
        title: "Erreur d'unstaking",
        description: error.message || "Une erreur s'est produite lors de l'unstaking.",
        variant: "destructive",
      })
    } finally {
      setIsUnstaking(false)
    }
  }

  const handleClaimRewards = async () => {
    if (!publicKey || !signTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour réclamer vos récompenses.",
        variant: "destructive",
      })
      return
    }

    if (!pool.userRewards || pool.userRewards <= 0) {
      toast({
        title: "Aucune récompense",
        description: "Vous n'avez pas de récompenses à réclamer.",
        variant: "destructive",
      })
      return
    }

    setIsClaiming(true)
    try {
      // Dans une implémentation réelle, nous utiliserions le keypair du portefeuille connecté
      // Ici, nous simulons avec un keypair temporaire
      const tempKeypair = Keypair.generate()

      const rewards = await claimRewards(tempKeypair, pool.id, pool.tokenMint)

      if (rewards > 0) {
        toast({
          title: "Récompenses réclamées",
          description: `Vous avez réclamé ${rewards} ${pool.tokenSymbol} de récompenses.`,
        })
        if (onStakeSuccess) onStakeSuccess()
      } else {
        throw new Error("Échec de la réclamation des récompenses")
      }
    } catch (error: any) {
      console.error("Claim rewards error:", error)
      toast({
        title: "Erreur de réclamation",
        description: error.message || "Une erreur s'est produite lors de la réclamation des récompenses.",
        variant: "destructive",
      })
    } finally {
      setIsClaiming(false)
    }
  }

  const calculateTimeRemaining = () => {
    if (!pool.userEndTime) return null

    const now = Date.now()
    const endTime = pool.userEndTime

    if (now >= endTime) return "Période de lock terminée"

    const remainingMs = endTime - now
    const days = Math.floor(remainingMs / (1000 * 60 * 60 * 24))
    const hours = Math.floor((remainingMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    return `${days}j ${hours}h restants`
  }

  const timeRemaining = calculateTimeRemaining()
  const isLocked = pool.userEndTime ? Date.now() < pool.userEndTime : false
  const percentComplete =
    pool.userStartTime && pool.userEndTime
      ? Math.min(100, Math.max(0, ((Date.now() - pool.userStartTime) / (pool.userEndTime - pool.userStartTime)) * 100))
      : 0

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-purple-500/10 to-blue-500/10">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center">
              <Coins className="mr-2 h-5 w-5 text-primary" />
              {pool.tokenName}
              <Badge variant="outline" className="ml-2">
                {pool.tokenSymbol}
              </Badge>
            </CardTitle>
            <CardDescription>Pool de staking</CardDescription>
          </div>
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">{pool.apr}% APR</Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="flex space-x-2 mb-6">
          <Button
            variant={activeTab === "stake" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveTab("stake")}
          >
            Staker
          </Button>
          <Button
            variant={activeTab === "unstake" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveTab("unstake")}
            disabled={!pool.userStaked}
          >
            Unstaker
          </Button>
          <Button variant={activeTab === "info" ? "default" : "outline"} size="sm" onClick={() => setActiveTab("info")}>
            Infos
          </Button>
        </div>

        {activeTab === "stake" && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Période de lock:</span>
                <div className="font-medium">{pool.lockPeriod} jours</div>
              </div>
              <div>
                <span className="text-muted-foreground">Total staké:</span>
                <div className="font-medium">
                  {formatNumber(pool.totalStaked)} {pool.tokenSymbol}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <label htmlFor="stakeAmount" className="text-sm font-medium">
                  Montant à staker
                </label>
                {publicKey && (
                  <span className="text-xs text-muted-foreground">
                    Balance: {formatNumber(10000)} {pool.tokenSymbol}
                  </span>
                )}
              </div>
              <div className="flex space-x-2">
                <Input
                  id="stakeAmount"
                  type="number"
                  placeholder="0.00"
                  value={stakeAmount}
                  onChange={(e) => setStakeAmount(e.target.value)}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setStakeAmount("10000")}
                  className="whitespace-nowrap"
                >
                  Max
                </Button>
              </div>
            </div>

            <div className="bg-muted p-3 rounded-md text-sm">
              <div className="flex items-start space-x-2">
                <Info className="h-4 w-4 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">Informations importantes</p>
                  <p className="text-muted-foreground">
                    En stakant vos tokens, vous acceptez de les verrouiller pendant {pool.lockPeriod} jours. Vous
                    recevrez {pool.apr}% APR en récompenses.
                  </p>
                </div>
              </div>
            </div>

            <Button
              className="w-full"
              onClick={handleStake}
              disabled={isStaking || !publicKey || !stakeAmount || Number.parseFloat(stakeAmount) <= 0}
            >
              {isStaking ? "Staking en cours..." : "Staker"}
            </Button>
          </div>
        )}

        {activeTab === "unstake" && (
          <div className="space-y-4">
            {pool.userStaked && (
              <>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Montant staké:</span>
                    <div className="font-medium">
                      {formatNumber(pool.userStaked)} {pool.tokenSymbol}
                    </div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Récompenses:</span>
                    <div className="font-medium">
                      {formatNumber(pool.userRewards || 0)} {pool.tokenSymbol}
                    </div>
                  </div>
                </div>

                {timeRemaining && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Période de lock:</span>
                      <span className={isLocked ? "text-yellow-500" : "text-green-500"}>{timeRemaining}</span>
                    </div>
                    <Progress value={percentComplete} className="h-2" />
                  </div>
                )}

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <label htmlFor="unstakeAmount" className="text-sm font-medium">
                      Montant à unstaker
                    </label>
                    <span className="text-xs text-muted-foreground">
                      Staké: {formatNumber(pool.userStaked)} {pool.tokenSymbol}
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    <Input
                      id="unstakeAmount"
                      type="number"
                      placeholder="0.00"
                      value={unstakeAmount}
                      onChange={(e) => setUnstakeAmount(e.target.value)}
                      disabled={isLocked}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setUnstakeAmount(pool.userStaked?.toString() || "0")}
                      className="whitespace-nowrap"
                      disabled={isLocked}
                    >
                      Max
                    </Button>
                  </div>
                </div>

                {isLocked && (
                  <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-md text-sm text-yellow-800">
                    <div className="flex items-start space-x-2">
                      <Lock className="h-4 w-4 mt-0.5" />
                      <div>
                        <p className="font-medium">Période de lock active</p>
                        <p>
                          Vos tokens sont verrouillés jusqu'à la fin de la période de lock.
                          {timeRemaining && ` Il reste ${timeRemaining}.`}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex flex-col space-y-2">
                  <Button
                    onClick={handleUnstake}
                    disabled={
                      isUnstaking || !publicKey || !unstakeAmount || Number.parseFloat(unstakeAmount) <= 0 || isLocked
                    }
                  >
                    {isUnstaking ? "Unstaking en cours..." : "Unstaker"}
                  </Button>

                  {pool.userRewards && pool.userRewards > 0 && (
                    <Button variant="outline" onClick={handleClaimRewards} disabled={isClaiming || !publicKey}>
                      {isClaiming
                        ? "Réclamation en cours..."
                        : `Réclamer ${formatNumber(pool.userRewards)} ${pool.tokenSymbol}`}
                    </Button>
                  )}
                </div>
              </>
            )}

            {!pool.userStaked && (
              <div className="text-center py-6">
                <p className="text-muted-foreground">Vous n'avez pas de tokens stakés dans ce pool.</p>
                <Button variant="outline" className="mt-2" onClick={() => setActiveTab("stake")}>
                  Staker maintenant
                </Button>
              </div>
            )}
          </div>
        )}

        {activeTab === "info" && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">APR:</span>
                <div className="font-medium">{pool.apr}%</div>
              </div>
              <div>
                <span className="text-muted-foreground">Période de lock:</span>
                <div className="font-medium">{pool.lockPeriod} jours</div>
              </div>
              <div>
                <span className="text-muted-foreground">Total staké:</span>
                <div className="font-medium">
                  {formatNumber(pool.totalStaked)} {pool.tokenSymbol}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Nombre de stakers:</span>
                <div className="font-medium">{formatNumber(pool.stakerCount)}</div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Calcul des récompenses</h4>
              <div className="bg-muted p-3 rounded-md text-sm">
                <p className="text-muted-foreground">
                  Les récompenses sont calculées quotidiennement en fonction du montant staké et de l'APR.
                  <br />
                  Formule: <span className="font-mono">montant_staké * (APR / 365) * jours_stakés</span>
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Exemple de récompenses</h4>
              <div className="bg-muted p-3 rounded-md text-sm">
                <p className="text-muted-foreground">
                  Pour 10,000 {pool.tokenSymbol} stakés pendant {pool.lockPeriod} jours à {pool.apr}% APR:
                  <br />
                  <span className="font-mono">
                    10,000 * ({pool.apr}% / 365) * {pool.lockPeriod} ={" "}
                    {formatNumber(((10000 * (pool.apr / 100)) / 365) * pool.lockPeriod)} {pool.tokenSymbol}
                  </span>
                </p>
              </div>
            </div>

            {pool.userStaked && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Vos statistiques</h4>
                <div className="bg-muted p-3 rounded-md text-sm">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <span className="text-muted-foreground">Montant staké:</span>
                      <div className="font-medium">
                        {formatNumber(pool.userStaked)} {pool.tokenSymbol}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Récompenses accumulées:</span>
                      <div className="font-medium">
                        {formatNumber(pool.userRewards || 0)} {pool.tokenSymbol}
                      </div>
                    </div>
                    {timeRemaining && (
                      <div className="col-span-2">
                        <span className="text-muted-foreground">Temps restant:</span>
                        <div className="font-medium">{timeRemaining}</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="bg-muted/50 flex justify-between">
        <div className="text-xs text-muted-foreground">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                Créé il y a {Math.floor((Date.now() - pool.createdAt) / (1000 * 60 * 60 * 24))} jours
              </TooltipTrigger>
              <TooltipContent>
                <p>Date de création: {new Date(pool.createdAt).toLocaleDateString()}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Button variant="ghost" size="sm" className="text-xs">
          <ArrowRight className="h-3 w-3 mr-1" />
          Voir les détails
        </Button>
      </CardFooter>
    </Card>
  )
}

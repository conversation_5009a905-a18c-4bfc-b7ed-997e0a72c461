"use client"

import { useState } from "react"
import { useConnection } from "@solana/wallet-adapter-react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle2, AlertCircle } from "lucide-react"

export default function SolanaConnectionTest() {
  const { connection } = useConnection()
  const [isConnected, setIsConnected] = useState<boolean | null>(null)
  const [blockHeight, setBlockHeight] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testConnection = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Try to get the latest block height
      const height = await connection.getBlockHeight()
      setBlockHeight(height)
      setIsConnected(true)
    } catch (err) {
      console.error("Error connecting to Solana:", err)
      setError("Failed to connect to Solana Devnet. Please check your RPC URL.")
      setIsConnected(false)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Solana Devnet Connection</h3>
        <Button onClick={testConnection} disabled={isLoading} variant="outline" size="sm">
          {isLoading ? "Testing..." : "Test Connection"}
        </Button>
      </div>

      {isConnected === true && (
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertTitle>Connected to Solana Devnet</AlertTitle>
          <AlertDescription>
            Successfully connected to Solana Devnet. Current block height: {blockHeight}
          </AlertDescription>
        </Alert>
      )}

      {isConnected === false && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Connection Failed</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="text-sm text-muted-foreground">
        <p>RPC URL: {process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com"}</p>
      </div>
    </div>
  )
}

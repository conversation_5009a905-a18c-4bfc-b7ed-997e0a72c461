import Link from "next/link"
import Image from "next/image"

export function Footer() {
  return (
    <footer className="bg-background border-t py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Image
                src="/images/global-finance-logo.png"
                alt="Global Finance Logo"
                width={40}
                height={40}
                className="rounded-full"
              />
              <span className="font-bold text-xl">Global Finance</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Plateforme de création et gestion de tokens sur Solana et BNB Chain
            </p>
          </div>

          <div>
            <h3 className="font-semibold mb-4">Produits</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/token-factory" className="text-muted-foreground hover:text-primary">
                  Token Factory
                </Link>
              </li>
              <li>
                <Link href="/memecoin-launchpad" className="text-muted-foreground hover:text-primary">
                  Memecoin Launchpad
                </Link>
              </li>
              <li>
                <Link href="/nft-gallery" className="text-muted-foreground hover:text-primary">
                  NFT Gallery
                </Link>
              </li>
              <li>
                <Link href="/staking" className="text-muted-foreground hover:text-primary">
                  Staking
                </Link>
              </li>
              <li>
                <Link href="/presale" className="text-muted-foreground hover:text-primary">
                  Presale
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-4">Ressources</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/docs" className="text-muted-foreground hover:text-primary">
                  Documentation
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-muted-foreground hover:text-primary">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/tutorials" className="text-muted-foreground hover:text-primary">
                  Tutoriels
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-muted-foreground hover:text-primary">
                  Blog
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-4">Légal</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/terms" className="text-muted-foreground hover:text-primary">
                  Conditions d'utilisation
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-muted-foreground hover:text-primary">
                  Politique de confidentialité
                </Link>
              </li>
              <li>
                <Link href="/disclaimer" className="text-muted-foreground hover:text-primary">
                  Avertissement
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-4 border-t text-center text-sm text-muted-foreground">
          <p>&copy; {new Date().getFullYear()} Global Finance. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  )
}

"use client"

import { useEffect, useState } from "react"

export function PlatformArchitectureDiagram() {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return null
  }

  return (
    <div className="w-full overflow-auto bg-white p-4 rounded-lg shadow">
      <div className="min-w-[800px]">
        <h3 className="text-lg font-semibold mb-4 text-center">Architecture de la Plateforme de Tokens</h3>

        {/* Mermaid diagram will be rendered here */}
        <div className="mermaid">
          {`
          graph TD
            A["Client Interface"] --> B["Token Platform Service"]
            B --> C["Enhanced Token Suffix Service"]
            B --> D["Token Fee Service"]
            B --> E["Token Security Service"]
            B --> F["Token Vesting Service"]
            B --> G["Token Bonding Curve Service"]
            B --> H["Token Governance Service"]
            
            C --> I["Suffix Grinding Workers"]
            D --> J["Fee Distribution"]
            E --> K["Blacklist Management"]
            E --> L["Transaction Limits"]
            F --> M["Vesting Schedules"]
            G --> N["Price Impact Reserve"]
            H --> O["DAO Governance"]
            
            J --> P["Dev Wallet"]
            J --> Q["Burn Wallet"]
            J --> R["Price Impact Wallet"]
            
            subgraph "Blockchain"
              S["Solana Programs"]
              T["Token Accounts"]
              U["PDA State Accounts"]
            end
            
            B --> S
            J --> T
            K --> U
            L --> U
            M --> T
            N --> T
            O --> U
          `}
        </div>
      </div>
    </div>
  )
}

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { X } from "lucide-react"
import Link from "next/link"

interface HowItWorksModalProps {
  onClose: () => void
}

export default function HowItWorksModal({ onClose }: HowItWorksModalProps) {
  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-[#111] border border-[#333] rounded-lg max-w-2xl w-full max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center p-6 border-b border-[#333]">
          <h2 className="text-xl font-bold">how it works</h2>
          <Button variant="ghost" size="icon" onClick={onClose} className="text-gray-400 hover:text-white">
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          <p className="text-center text-gray-300">
            pump allows anyone to create coins. all coins created on Pump are fair-launch, meaning everyone has equal
            access to buy and sell when the coin is first created.
          </p>

          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="font-bold">step 1: pick a coin that you like</h3>
              <p className="text-gray-400">
                Browse through the available coins and find one that catches your interest, or create your own coin with
                a unique name, ticker, and image.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="font-bold">step 2: buy the coin on the bonding curve</h3>
              <p className="text-gray-400">
                Each coin follows a bonding curve, which means the price increases as more tokens are bought and
                decreases as tokens are sold. This creates a fair market for everyone.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="font-bold">step 3: sell at any time to lock in your profits or losses</h3>
              <p className="text-gray-400">
                You can sell your tokens at any time. The bonding curve ensures there is always liquidity, so you don't
                need to find a buyer to sell your tokens.
              </p>
            </div>
          </div>

          <div className="pt-4 border-t border-[#333] text-center">
            <Button className="bg-green-500 hover:bg-green-600 text-black font-bold" onClick={onClose}>
              I'm ready to pump
            </Button>

            <div className="flex justify-center space-x-4 mt-4 text-sm text-gray-400">
              <Link href="/privacy" className="hover:text-green-400">
                privacy policy
              </Link>
              <Link href="/terms" className="hover:text-green-400">
                terms of service
              </Link>
              <Link href="/fees" className="hover:text-green-400">
                fees
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

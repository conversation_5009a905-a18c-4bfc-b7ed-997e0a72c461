"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Check, Search, ArrowUpDown, Clock, Users, Flame, Star } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface TokenLaunchpadItem {
  id: string
  name: string
  symbol: string
  description: string
  image: string
  status: "upcoming" | "live" | "ended"
  startDate: string
  endDate: string
  hardCap: number
  softCap: number
  raised: number
  price: number
  totalSupply: number
  participants: number
  category: string
  featured: boolean
  verified: boolean
}

export function LaunchpadTokenList() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [sortBy, setSortBy] = useState("trending")

  // Simuler le chargement des données des tokens
  const [tokens, setTokens] = useState<TokenLaunchpadItem[]>([
    {
      id: "gfq-token",
      name: "Global Finance Quantum",
      symbol: "GFQ",
      description:
        "Global Finance Quantum est un token innovant sur la blockchain Solana avec des fonctionnalités avancées et une sécurité renforcée.",
      image: "/placeholder.svg?height=100&width=100&text=GFQ",
      status: "live",
      startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      hardCap: 500000,
      softCap: 100000,
      raised: 275000,
      price: 0.00005,
      totalSupply: 1000000000,
      participants: 342,
      category: "DeFi",
      featured: true,
      verified: true,
    },
    {
      id: "solana-meme",
      name: "Solana Meme",
      symbol: "SMEME",
      description: "Le premier token meme sur Solana avec des fonctionnalités de staking et de gouvernance.",
      image: "/placeholder.svg?height=100&width=100&text=SMEME",
      status: "upcoming",
      startDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
      hardCap: 300000,
      softCap: 50000,
      raised: 0,
      price: 0.0001,
      totalSupply: 500000000,
      participants: 0,
      category: "Meme",
      featured: false,
      verified: true,
    },
    {
      id: "sol-game",
      name: "Solana Game",
      symbol: "SGAME",
      description: "Un token de jeu sur Solana qui permet aux joueurs de gagner des récompenses en jouant.",
      image: "/placeholder.svg?height=100&width=100&text=SGAME",
      status: "live",
      startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      hardCap: 200000,
      softCap: 50000,
      raised: 150000,
      price: 0.0002,
      totalSupply: 200000000,
      participants: 210,
      category: "Gaming",
      featured: true,
      verified: true,
    },
    {
      id: "sol-ai",
      name: "Solana AI",
      symbol: "SAI",
      description:
        "Un token qui combine l'IA et la blockchain pour créer des applications décentralisées intelligentes.",
      image: "/placeholder.svg?height=100&width=100&text=SAI",
      status: "ended",
      startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      hardCap: 400000,
      softCap: 100000,
      raised: 400000,
      price: 0.0003,
      totalSupply: 300000000,
      participants: 520,
      category: "AI",
      featured: false,
      verified: true,
    },
    {
      id: "sol-nft",
      name: "Solana NFT",
      symbol: "SNFT",
      description: "Un token qui permet aux utilisateurs de créer et d'échanger des NFT sur la blockchain Solana.",
      image: "/placeholder.svg?height=100&width=100&text=SNFT",
      status: "live",
      startDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      hardCap: 250000,
      softCap: 50000,
      raised: 100000,
      price: 0.00015,
      totalSupply: 400000000,
      participants: 180,
      category: "NFT",
      featured: true,
      verified: false,
    },
  ])

  // Filtrer les tokens
  const filteredTokens = tokens.filter((token) => {
    // Filtre de recherche
    const matchesSearch =
      token.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.description.toLowerCase().includes(searchQuery.toLowerCase())

    // Filtre de statut
    const matchesStatus = statusFilter === "all" || token.status === statusFilter

    // Filtre de catégorie
    const matchesCategory = categoryFilter === "all" || token.category === categoryFilter

    return matchesSearch && matchesStatus && matchesCategory
  })

  // Trier les tokens
  const sortedTokens = [...filteredTokens].sort((a, b) => {
    switch (sortBy) {
      case "trending":
        return b.participants - a.participants
      case "newest":
        return new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
      case "ending-soon":
        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime()
      case "most-raised":
        return b.raised - a.raised
      case "most-participants":
        return b.participants - a.participants
      default:
        return 0
    }
  })

  // Calculer le pourcentage de progression
  const calculateProgress = (raised: number, hardCap: number) => {
    return Math.min(100, Math.round((raised / hardCap) * 100))
  }

  // Calculer le temps restant
  const getTimeRemaining = (endDate: string) => {
    const end = new Date(endDate)
    const now = new Date()
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return "Terminé"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

    if (days > 0) {
      return `${days}j ${hours}h`
    } else {
      return `${hours}h`
    }
  }

  // Obtenir la couleur du badge de statut
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "upcoming":
        return "bg-blue-500/10 text-blue-500 border-blue-500/20"
      case "live":
        return "bg-green-500/10 text-green-500 border-green-500/20"
      case "ended":
        return "bg-gray-500/10 text-gray-500 border-gray-500/20"
      default:
        return ""
    }
  }

  // Obtenir le texte du badge de statut
  const getStatusBadgeText = (status: string) => {
    switch (status) {
      case "upcoming":
        return "À venir"
      case "live":
        return "En cours"
      case "ended":
        return "Terminé"
      default:
        return ""
    }
  }

  // Naviguer vers la page de détails du token
  const navigateToTokenDetails = (id: string) => {
    router.push(`/token-launchpad/${id}`)
  }

  // Obtenir les catégories uniques
  const uniqueCategories = Array.from(new Set(tokens.map((token) => token.category)))

  return (
    <div className="space-y-6">
      {/* Filtres et recherche */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un token..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Statut" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les statuts</SelectItem>
              <SelectItem value="upcoming">À venir</SelectItem>
              <SelectItem value="live">En cours</SelectItem>
              <SelectItem value="ended">Terminé</SelectItem>
            </SelectContent>
          </Select>

          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Catégorie" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les catégories</SelectItem>
              {uniqueCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-1">
                <ArrowUpDown className="h-4 w-4" />
                Trier
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setSortBy("trending")}>
                <Flame className="h-4 w-4 mr-2" />
                Tendance
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("newest")}>
                <Star className="h-4 w-4 mr-2" />
                Plus récent
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("ending-soon")}>
                <Clock className="h-4 w-4 mr-2" />
                Se termine bientôt
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("most-raised")}>
                <ArrowUpDown className="h-4 w-4 mr-2" />
                Plus levé
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("most-participants")}>
                <Users className="h-4 w-4 mr-2" />
                Plus de participants
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Liste des tokens */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedTokens.map((token) => (
          <Card
            key={token.id}
            className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => navigateToTokenDetails(token.id)}
          >
            <CardContent className="p-0">
              <div className="p-6">
                <div className="flex items-start gap-4">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={token.image || "/placeholder.svg"} alt={token.name} />
                    <AvatarFallback>{token.symbol.substring(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold">{token.name}</h3>
                      <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                        {token.symbol}
                      </Badge>
                      {token.verified && (
                        <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                          <Check className="h-3 w-3 mr-1" />
                          Vérifié
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2">{token.description}</p>
                  </div>
                </div>

                <div className="mt-4 space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progression</span>
                      <span>
                        {calculateProgress(token.raised, token.hardCap)}% ({token.raised.toLocaleString()} /{" "}
                        {token.hardCap.toLocaleString()} SOL)
                      </span>
                    </div>
                    <Progress value={calculateProgress(token.raised, token.hardCap)} className="h-2" />
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <div className="text-xs text-muted-foreground">Prix</div>
                      <div className="font-medium">${token.price}</div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">Participants</div>
                      <div className="font-medium">{token.participants}</div>
                    </div>
                    <div>
                      <Badge
                        variant="outline"
                        className={`${getStatusBadgeColor(token.status)} flex items-center gap-1`}
                      >
                        <Clock className="h-3 w-3" />
                        {token.status === "live" ? getTimeRemaining(token.endDate) : getStatusBadgeText(token.status)}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {token.featured && (
                <div className="bg-primary/10 p-2 text-xs font-medium text-primary text-center">
                  <Flame className="h-3 w-3 inline-block mr-1" />
                  Token en vedette
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTokens.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Aucun token ne correspond à vos critères de recherche.</p>
        </div>
      )}
    </div>
  )
}

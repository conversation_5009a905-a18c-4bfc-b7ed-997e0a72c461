"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Check, Info, Clock, Users, Globe, Twitter, MessageCircle, Copy, ExternalLink } from "lucide-react"

interface TokenDetailsProps {
  id: string
}

export function LaunchpadTokenDetails({ id }: TokenDetailsProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("overview")
  const [contributionAmount, setContributionAmount] = useState("")
  const [isContributing, setIsContributing] = useState(false)

  // Simuler le chargement des données du token
  const [token, setToken] = useState({
    id,
    name: "Global Finance Quantum",
    symbol: "GFQ",
    description:
      "Global Finance Quantum est un token innovant sur la blockchain Solana avec des fonctionnalités avancées et une sécurité renforcée. Il vise à révolutionner les services financiers décentralisés avec une approche axée sur la sécurité et la scalabilité.",
    image: "/placeholder.svg?height=100&width=100&text=GFQ",
    status: "live",
    startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
    hardCap: 500000,
    softCap: 100000,
    raised: 275000,
    price: 0.00005,
    totalSupply: 1000000000,
    participants: 342,
    category: "DeFi",
    featured: true,
    verified: true,
    website: "https://globalfinancequantum.com",
    twitter: "https://twitter.com/globalfinancequantum",
    telegram: "https://t.me/globalfinancequantum",
    tokenomics: {
      liquidity: 50,
      marketing: 15,
      development: 10,
      team: 15,
      reserve: 10,
    },
    security: {
      auditStatus: "Audité",
      kycStatus: "Vérifié",
      liquidityLockPeriod: 180,
      teamTokensLocked: true,
      antiBot: true,
      antiDump: true,
    },
    roadmap: [
      {
        title: "Phase 1: Lancement",
        description: "Lancement du token et listing sur les DEX",
        status: "completed",
      },
      {
        title: "Phase 2: Expansion",
        description: "Développement de la plateforme DeFi et partenariats stratégiques",
        status: "in-progress",
      },
      {
        title: "Phase 3: Adoption",
        description: "Intégration avec d'autres projets et expansion de l'écosystème",
        status: "upcoming",
      },
      {
        title: "Phase 4: Évolution",
        description: "Lancement de nouveaux produits et services financiers",
        status: "upcoming",
      },
    ],
    team: [
      {
        name: "John Doe",
        role: "CEO & Fondateur",
        avatar: "/placeholder.svg?height=100&width=100&text=JD",
      },
      {
        name: "Jane Smith",
        role: "CTO",
        avatar: "/placeholder.svg?height=100&width=100&text=JS",
      },
      {
        name: "Alex Johnson",
        role: "CMO",
        avatar: "/placeholder.svg?height=100&width=100&text=AJ",
      },
    ],
    faq: [
      {
        question: "Qu'est-ce que Global Finance Quantum?",
        answer:
          "Global Finance Quantum est un token innovant sur la blockchain Solana qui vise à révolutionner les services financiers décentralisés avec une approche axée sur la sécurité et la scalabilité.",
      },
      {
        question: "Comment puis-je participer au lancement?",
        answer:
          "Vous pouvez participer au lancement en connectant votre portefeuille Solana et en contribuant avec SOL. Vous recevrez des tokens GFQ en fonction du prix de lancement.",
      },
      {
        question: "Quand les tokens seront-ils distribués?",
        answer:
          "Les tokens seront distribués immédiatement après la fin de la période de lancement, à condition que le soft cap soit atteint.",
      },
      {
        question: "La liquidité sera-t-elle verrouillée?",
        answer:
          "Oui, la liquidité sera verrouillée pendant 180 jours pour assurer la stabilité du prix et la confiance des investisseurs.",
      },
    ],
    recentContributions: [
      {
        address: "7xKX...9Yfr",
        amount: 500,
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      },
      {
        address: "3jHm...2Pqw",
        amount: 1000,
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      },
      {
        address: "9rTz...5Kxs",
        amount: 250,
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
      },
      {
        address: "2vBn...8Lpo",
        amount: 750,
        timestamp: new Date(Date.now() - 120 * 60 * 1000).toISOString(),
      },
    ],
  })

  // Calculer le pourcentage de progression
  const progressPercentage = Math.min(100, Math.round((token.raised / token.hardCap) * 100))

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString(undefined, { month: "long", day: "numeric", year: "numeric" })
  }

  // Calculer le temps restant
  const getTimeRemaining = (endDate: string) => {
    const end = new Date(endDate)
    const now = new Date()
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return "Terminé"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
      return `${days}j ${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  // Formater le montant
  const formatAmount = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(2)}M`
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(2)}K`
    } else {
      return `$${amount.toFixed(2)}`
    }
  }

  // Formater le temps écoulé
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 60) {
      return `il y a ${minutes} minute${minutes > 1 ? "s" : ""}`
    } else if (hours < 24) {
      return `il y a ${hours} heure${hours > 1 ? "s" : ""}`
    } else {
      return `il y a ${days} jour${days > 1 ? "s" : ""}`
    }
  }

  // Copier l'adresse du token
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      description: "Adresse copiée dans le presse-papiers",
    })
  }

  // Gérer la contribution
  const handleContribute = async () => {
    if (!contributionAmount || Number.parseFloat(contributionAmount) <= 0) {
      toast({
        title: "Montant invalide",
        description: "Veuillez entrer un montant valide",
        variant: "destructive",
      })
      return
    }

    setIsContributing(true)

    try {
      // Simuler une transaction
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Mettre à jour les données du token
      const amount = Number.parseFloat(contributionAmount)
      setToken((prev) => ({
        ...prev,
        raised: prev.raised + amount,
        participants: prev.participants + 1,
        recentContributions: [
          {
            address: "Votre adresse",
            amount,
            timestamp: new Date().toISOString(),
          },
          ...prev.recentContributions.slice(0, 3),
        ],
      }))

      setContributionAmount("")
      toast({
        title: "Contribution réussie",
        description: `Vous avez contribué ${amount} SOL au lancement de ${token.name}`,
      })
    } catch (error) {
      console.error("Erreur lors de la contribution:", error)
      toast({
        title: "Erreur",
        description: "Une erreur s'est produite lors de la contribution",
        variant: "destructive",
      })
    } finally {
      setIsContributing(false)
    }
  }

  return (
    <div className="container py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* En-tête du token */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                <Avatar className="w-16 h-16">
                  <AvatarImage src={token.image || "/placeholder.svg"} alt={token.name} />
                  <AvatarFallback>{token.symbol.substring(0, 2)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex flex-wrap items-center gap-2 mb-1">
                    <h1 className="text-2xl font-bold">{token.name}</h1>
                    <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                      {token.symbol}
                    </Badge>
                    {token.verified && (
                      <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                        <Check className="h-3 w-3 mr-1" />
                        Vérifié
                      </Badge>
                    )}
                  </div>
                  <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                    <span className="flex items-center">
                      <Info className="h-4 w-4 mr-1" />
                      {token.category}
                    </span>
                    <span className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {getTimeRemaining(token.endDate)} restant
                    </span>
                    <span className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {token.participants} participants
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mt-4">
                {token.website && (
                  <a
                    href={token.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
                  >
                    <Globe className="h-4 w-4 mr-1" />
                    Site web
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                )}
                {token.twitter && (
                  <a
                    href={token.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
                  >
                    <Twitter className="h-4 w-4 mr-1" />
                    Twitter
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                )}
                {token.telegram && (
                  <a
                    href={token.telegram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
                  >
                    <MessageCircle className="h-4 w-4 mr-1" />
                    Telegram
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                )}
                <button
                  onClick={() => copyToClipboard(id)}
                  className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copier l'adresse
                </button>
              </div>
            </CardContent>
          </Card>

          {/* Onglets d'information */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="overview">Aperçu</TabsTrigger>
              <TabsTrigger value="tokenomics">Tokenomics</TabsTrigger>
              <TabsTrigger value="roadmap">Roadmap</TabsTrigger>
              <TabsTrigger value="team">Équipe</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">À propos du projet</h2>
                  <p className="text-muted-foreground">{token.description}</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">FAQ</h2>
                  <div className="space-y-4">
                    {token.faq.map((item, index) => (
                      <div key={index} className="border-b border-border pb-4 last:border-0 last:pb-0">
                        <h3 className="font-medium mb-2">{item.question}</h3>
                        <p className="text-sm text-muted-foreground">{item.answer}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tokenomics" className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Distribution des tokens</h2>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {Object.entries(token.tokenomics).map(([key, value]) => (
                      <div key={key} className="bg-muted rounded-lg p-4">
                        <div className="text-2xl font-bold">{value}%</div>
                        <div className="text-sm text-muted-foreground capitalize">{key}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Informations sur le token</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Nom</div>
                      <div className="font-medium">{token.name}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Symbole</div>
                      <div className="font-medium">{token.symbol}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Offre totale</div>
                      <div className="font-medium">{token.totalSupply.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Prix de lancement</div>
                      <div className="font-medium">${token.price}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Sécurité</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Audit</div>
                      <div className="font-medium">{token.security.auditStatus}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">KYC</div>
                      <div className="font-medium">{token.security.kycStatus}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Verrouillage de la liquidité</div>
                      <div className="font-medium">{token.security.liquidityLockPeriod} jours</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Tokens de l'équipe verrouillés</div>
                      <div className="font-medium">{token.security.teamTokensLocked ? "Oui" : "Non"}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Protection anti-bot</div>
                      <div className="font-medium">{token.security.antiBot ? "Activée" : "Désactivée"}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground mb-1">Protection anti-dump</div>
                      <div className="font-medium">{token.security.antiDump ? "Activée" : "Désactivée"}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="roadmap" className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Roadmap</h2>
                  <div className="space-y-8">
                    {token.roadmap.map((phase, index) => (
                      <div key={index} className="relative pl-8 pb-8 border-l border-border last:pb-0">
                        <div
                          className={`absolute left-[-8px] w-4 h-4 rounded-full ${
                            phase.status === "completed"
                              ? "bg-green-500"
                              : phase.status === "in-progress"
                                ? "bg-blue-500"
                                : "bg-gray-300"
                          }`}
                        ></div>
                        <h3 className="font-medium mb-2">{phase.title}</h3>
                        <p className="text-sm text-muted-foreground">{phase.description}</p>
                        <Badge
                          variant="outline"
                          className={`mt-2 ${
                            phase.status === "completed"
                              ? "bg-green-500/10 text-green-500 border-green-500/20"
                              : phase.status === "in-progress"
                                ? "bg-blue-500/10 text-blue-500 border-blue-500/20"
                                : "bg-gray-500/10 text-gray-500 border-gray-500/20"
                          }`}
                        >
                          {phase.status === "completed"
                            ? "Terminé"
                            : phase.status === "in-progress"
                              ? "En cours"
                              : "À venir"}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="team" className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Équipe</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {token.team.map((member, index) => (
                      <div key={index} className="flex flex-col items-center text-center">
                        <Avatar className="w-24 h-24 mb-4">
                          <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                          <AvatarFallback>{member.name.substring(0, 2)}</AvatarFallback>
                        </Avatar>
                        <h3 className="font-medium">{member.name}</h3>
                        <p className="text-sm text-muted-foreground">{member.role}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          {/* Carte de contribution */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-4">Contribuer</h2>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progression</span>
                    <span>
                      {token.raised.toLocaleString()} / {token.hardCap.toLocaleString()} SOL ({progressPercentage}%)
                    </span>
                  </div>
                  <Progress value={progressPercentage} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Soft Cap</div>
                    <div className="font-medium">{token.softCap.toLocaleString()} SOL</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Hard Cap</div>
                    <div className="font-medium">{token.hardCap.toLocaleString()} SOL</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Prix</div>
                    <div className="font-medium">${token.price}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Fin</div>
                    <div className="font-medium">{formatDate(token.endDate)}</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Montant à contribuer (SOL)</div>
                  <Input
                    type="number"
                    value={contributionAmount}
                    onChange={(e) => setContributionAmount(e.target.value)}
                    placeholder="0.0"
                    min="0"
                    step="0.1"
                  />
                  <div className="text-xs text-muted-foreground">
                    Vous recevrez environ{" "}
                    {contributionAmount && !isNaN(Number.parseFloat(contributionAmount))
                      ? (Number.parseFloat(contributionAmount) / token.price).toLocaleString()
                      : "0"}{" "}
                    {token.symbol}
                  </div>
                </div>

                <Button
                  className="w-full"
                  onClick={handleContribute}
                  disabled={isContributing || !contributionAmount || Number.parseFloat(contributionAmount) <= 0}
                >
                  {isContributing ? "Contribution en cours..." : "Contribuer"}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Contributions récentes */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold mb-4">Contributions récentes</h2>
              <div className="space-y-4">
                {token.recentContributions.map((contribution, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Avatar className="w-8 h-8 mr-2">
                        <AvatarFallback className="text-xs">{contribution.address.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="text-sm font-medium">{contribution.address}</div>
                        <div className="text-xs text-muted-foreground">{formatTimeAgo(contribution.timestamp)}</div>
                      </div>
                    </div>
                    <div className="font-medium">{contribution.amount} SOL</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

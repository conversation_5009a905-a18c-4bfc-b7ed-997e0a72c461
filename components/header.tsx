"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { NetworkSelector } from "@/components/network-selector"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { isAdmin } from "@/lib/admin-service"
import { useAdmin } from "@/hooks/use-admin"
import { useMobile } from "@/hooks/use-mobile"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Menu, X } from "lucide-react"

export default function Header() {
  const pathname = usePathname()
  const { toast } = useToast()
  const { publicKey, connected } = useWallet()
  const { isMobile } = useMobile()
  const { isAdmin: isAdminUser, isLoading: isAdminLoading } = useAdmin()
  const [showAdminLink, setShowAdminLink] = useState(false)
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  useEffect(() => {
    if (connected && publicKey) {
      const walletAddress = publicKey.toString()
      const adminStatus = isAdmin(walletAddress)
      setShowAdminLink(adminStatus)
    } else {
      setShowAdminLink(false)
    }
  }, [connected, publicKey, isAdminUser])

  const navigation = [
    { name: "Accueil", href: "/" },
    { name: "Marché", href: "/market" },
    { name: "Token Factory", href: "/token-factory" },
    { name: "Token Quantum", href: "/token-quantum" },
    { name: "Memecoin Launchpad", href: "/memecoin-launchpad" },
    { name: "NFT Gallery", href: "/nft-gallery" },
    { name: "Staking", href: "/staking" },
    { name: "Presale", href: "/presale" },
    { name: "Paramètres", href: "/settings" },
  ]

  const handleCopyWalletAddress = () => {
    if (publicKey) {
      navigator.clipboard.writeText(publicKey.toString())
      toast({
        title: "Adresse copiée",
        description: "L'adresse du wallet a été copiée dans le presse-papiers",
      })
    }
  }

  return (
    <header className="bg-background border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/images/global-finance-logo.png"
                alt="Global Finance Logo"
                width={40}
                height={40}
                className="rounded-full"
              />
              <span className="font-bold text-xl hidden md:inline-block">Global Finance</span>
            </Link>
            {!isMobile && (
              <nav className="hidden md:flex items-center space-x-4">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`text-sm font-medium transition-colors hover:text-primary ${
                      pathname === item.href ? "text-primary font-semibold" : "text-muted-foreground"
                    }`}
                  >
                    {item.name}
                  </Link>
                ))}
                {showAdminLink && (
                  <Link
                    href="/admin"
                    className={`text-sm font-medium transition-colors hover:text-primary ${
                      pathname?.startsWith("/admin") ? "text-primary font-semibold" : "text-muted-foreground"
                    }`}
                  >
                    Admin
                  </Link>
                )}
              </nav>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <NetworkSelector />
            <WalletMultiButton />

            {isMobile && (
              <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon">
                    {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                  </Button>
                </SheetTrigger>
                <SheetContent side="right">
                  <SheetHeader>
                    <SheetTitle>Menu</SheetTitle>
                    <SheetDescription>Navigation de la plateforme</SheetDescription>
                  </SheetHeader>
                  <nav className="flex flex-col space-y-4 mt-6">
                    {navigation.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`text-sm font-medium transition-colors hover:text-primary ${
                          pathname === item.href ? "text-primary font-semibold" : "text-muted-foreground"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {item.name}
                      </Link>
                    ))}
                    {showAdminLink && (
                      <Link
                        href="/admin"
                        className={`text-sm font-medium transition-colors hover:text-primary ${
                          pathname?.startsWith("/admin") ? "text-primary font-semibold" : "text-muted-foreground"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Admin
                      </Link>
                    )}
                  </nav>
                </SheetContent>
              </Sheet>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Image } from "@/components/ui/image"

interface TokenInfoProps {
  image?: string
}

export default function TokenInfo(props: TokenInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>GF-beta Token (GF-b1)</CardTitle>
        <CardDescription>Core platform token information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Image src={props.image || "/placeholder.svg?height=32&width=32"} alt="Token Logo" width={32} height={32} />
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-1">Current Price</div>
            <div className="text-2xl font-bold">$0.0125</div>
            <div className="text-xs text-green-500">+2.4% (24h)</div>
          </div>
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-1">Market Cap</div>
            <div className="text-2xl font-bold">$12.5M</div>
            <div className="text-xs text-green-500">+1.8% (24h)</div>
          </div>
        </div>

        <div>
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium text-muted-foreground">Circulating Supply</span>
            <span className="text-sm">750,000,000 / 1,000,000,000 GF-b1</span>
          </div>
          <Progress value={75} className="h-2" />
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Token Type:</span> <span className="font-medium">SPL Token</span>
          </div>
          <div>
            <span className="text-muted-foreground">Decimals:</span> <span className="font-medium">9</span>
          </div>
          <div>
            <span className="text-muted-foreground">Base Fee:</span> <span className="font-medium">1%</span>
          </div>
          <div>
            <span className="text-muted-foreground">Additional Fee:</span> <span className="font-medium">10%</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

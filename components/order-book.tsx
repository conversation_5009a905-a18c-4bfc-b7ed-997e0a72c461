"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface Order {
  price: number
  amount: number
  total: number
}

interface OrderBookProps {
  tokenAddress: string
  tokenSymbol: string
}

export default function OrderBook({ tokenAddress, tokenSymbol }: OrderBookProps) {
  const [buyOrders, setBuyOrders] = useState<Order[]>([])
  const [sellOrders, setSellOrders] = useState<Order[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchOrderBook() {
      try {
        // In a real implementation, this would fetch from an API
        // For now, we'll use mock data

        // Generate mock buy orders (bids)
        const mockBuyOrders: Order[] = []
        let basePrice = 0.049
        for (let i = 0; i < 10; i++) {
          const price = basePrice - i * 0.0005
          const amount = 100 + Math.random() * 900
          mockBuyOrders.push({
            price,
            amount,
            total: price * amount,
          })
        }

        // Generate mock sell orders (asks)
        const mockSellOrders: Order[] = []
        basePrice = 0.051
        for (let i = 0; i < 10; i++) {
          const price = basePrice + i * 0.0005
          const amount = 100 + Math.random() * 900
          mockSellOrders.push({
            price,
            amount,
            total: price * amount,
          })
        }

        setBuyOrders(mockBuyOrders)
        setSellOrders(mockSellOrders)
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching order book:", error)
        setIsLoading(false)
      }
    }

    fetchOrderBook()
  }, [tokenAddress])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Order Book - {tokenSymbol}/USDC</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Buy Orders (Bids) */}
            <div>
              <h3 className="text-lg font-medium mb-2 text-green-600">Buy Orders</h3>
              <div className="overflow-x-auto max-h-64">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Price (USDC)</TableHead>
                      <TableHead>Amount ({tokenSymbol})</TableHead>
                      <TableHead>Total (USDC)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {buyOrders.map((order, index) => (
                      <TableRow key={index} className="bg-green-50">
                        <TableCell className="text-green-600">${order.price.toFixed(6)}</TableCell>
                        <TableCell>{order.amount.toFixed(2)}</TableCell>
                        <TableCell>${order.total.toFixed(2)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>

            {/* Sell Orders (Asks) */}
            <div>
              <h3 className="text-lg font-medium mb-2 text-red-600">Sell Orders</h3>
              <div className="overflow-x-auto max-h-64">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Price (USDC)</TableHead>
                      <TableHead>Amount ({tokenSymbol})</TableHead>
                      <TableHead>Total (USDC)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sellOrders.map((order, index) => (
                      <TableRow key={index} className="bg-red-50">
                        <TableCell className="text-red-600">${order.price.toFixed(6)}</TableCell>
                        <TableCell>{order.amount.toFixed(2)}</TableCell>
                        <TableCell>${order.total.toFixed(2)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

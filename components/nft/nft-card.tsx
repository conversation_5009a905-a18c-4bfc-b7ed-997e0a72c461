"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { formatCurrency } from "@/lib/utils"
import { useWallet } from "@solana/wallet-adapter-react"
import { Keypair } from "@solana/web3.js"
import { buyNFT } from "@/lib/nft-service"
import { ExternalLink, Info, ShoppingCart, Tag } from "lucide-react"

interface NFTCardProps {
  nft: {
    mint: string
    metadata: {
      name: string
      symbol: string
      description: string
      image: string
      attributes: { trait_type: string; value: string }[]
      external_url?: string
      animation_url?: string
    }
    owner: string
    price?: number
    forSale: boolean
    createdAt: number
  }
  onBuySuccess?: () => void
}

export function NFTCard({ nft, onBuySuccess }: NFTCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isBuying, setIsBuying] = useState(false)
  const { toast } = useToast()
  const { publicKey, signTransaction } = useWallet()

  const handleBuy = async () => {
    if (!publicKey || !signTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour acheter ce NFT.",
        variant: "destructive",
      })
      return
    }

    if (!nft.forSale || !nft.price) {
      toast({
        title: "NFT non disponible",
        description: "Ce NFT n'est pas à vendre actuellement.",
        variant: "destructive",
      })
      return
    }

    setIsBuying(true)
    try {
      // Dans une implémentation réelle, nous utiliserions le keypair du portefeuille connecté
      // Ici, nous simulons avec un keypair temporaire
      const tempKeypair = Keypair.generate()

      const success = await buyNFT(tempKeypair, nft.mint, nft.price, nft.owner)

      if (success) {
        toast({
          title: "Achat réussi",
          description: `Vous avez acheté "${nft.metadata.name}" avec succès.`,
        })
        if (onBuySuccess) onBuySuccess()
      } else {
        throw new Error("Échec de l'achat")
      }
    } catch (error: any) {
      console.error("Buy NFT error:", error)
      toast({
        title: "Erreur d'achat",
        description: error.message || "Une erreur s'est produite lors de l'achat du NFT.",
        variant: "destructive",
      })
    } finally {
      setIsBuying(false)
    }
  }

  return (
    <Card className="overflow-hidden transition-all hover:shadow-md">
      <CardHeader className="p-0">
        <div className="relative aspect-square w-full overflow-hidden">
          <Image
            src={nft.metadata.image || "/placeholder.svg"}
            alt={nft.metadata.name}
            fill
            className="object-cover transition-transform hover:scale-105"
          />
          {nft.forSale && nft.price && (
            <div className="absolute top-2 right-2">
              <Badge className="bg-green-500 hover:bg-green-600">
                <Tag className="h-3 w-3 mr-1" />
                {formatCurrency(nft.price)}
              </Badge>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <CardTitle className="text-lg mb-1 truncate">{nft.metadata.name}</CardTitle>
        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">{nft.metadata.description}</p>

        {nft.metadata.attributes && nft.metadata.attributes.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {nft.metadata.attributes.slice(0, 3).map((attr, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {attr.trait_type}: {attr.value}
              </Badge>
            ))}
            {nft.metadata.attributes.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{nft.metadata.attributes.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="p-4 pt-0 flex justify-between">
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Info className="h-4 w-4 mr-1" />
              Détails
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{nft.metadata.name}</DialogTitle>
              <DialogDescription>Collection: {nft.metadata.symbol}</DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative aspect-square w-full overflow-hidden rounded-lg">
                <Image
                  src={nft.metadata.image || "/placeholder.svg"}
                  alt={nft.metadata.name}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-1">Description</h4>
                  <p className="text-sm text-muted-foreground">{nft.metadata.description}</p>
                </div>

                {nft.metadata.attributes && nft.metadata.attributes.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">Attributs</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {nft.metadata.attributes.map((attr, index) => (
                        <div key={index} className="bg-muted p-2 rounded-md text-xs">
                          <span className="text-muted-foreground">{attr.trait_type}:</span>{" "}
                          <span className="font-medium">{attr.value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <h4 className="text-sm font-medium mb-1">Détails</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Mint Address:</span>
                      <span className="font-mono">
                        {nft.mint.substring(0, 8)}...{nft.mint.substring(nft.mint.length - 8)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Propriétaire:</span>
                      <span className="font-mono">
                        {nft.owner.substring(0, 8)}...{nft.owner.substring(nft.owner.length - 8)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Date de création:</span>
                      <span>{new Date(nft.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>

                {nft.metadata.external_url && (
                  <Button variant="outline" size="sm" className="w-full" asChild>
                    <a href={nft.metadata.external_url} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Voir sur le site externe
                    </a>
                  </Button>
                )}

                {nft.forSale && nft.price && (
                  <div className="pt-2">
                    <Button className="w-full" onClick={handleBuy} disabled={isBuying}>
                      <ShoppingCart className="h-4 w-4 mr-1" />
                      {isBuying ? "Achat en cours..." : `Acheter pour ${formatCurrency(nft.price)}`}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {nft.forSale && nft.price && (
          <Button size="sm" onClick={handleBuy} disabled={isBuying}>
            <ShoppingCart className="h-4 w-4 mr-1" />
            {isBuying ? "Achat..." : "Acheter"}
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

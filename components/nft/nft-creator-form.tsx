"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { Keypair } from "@solana/web3.js"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { createNFTWithMetadata, type NFTMetadata } from "@/lib/nft-service"
import { Loader2, Upload, Plus, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export function NFTCreatorForm() {
  const [name, setName] = useState("")
  const [symbol, setSymbol] = useState("")
  const [description, setDescription] = useState("")
  const [image, setImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [externalUrl, setExternalUrl] = useState("")
  const [animationUrl, setAnimationUrl] = useState("")
  const [attributes, setAttributes] = useState<{ trait_type: string; value: string }[]>([
    { trait_type: "Rarity", value: "Common" },
  ])
  const [newTraitType, setNewTraitType] = useState("")
  const [newTraitValue, setNewTraitValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const router = useRouter()
  const { toast } = useToast()
  const { publicKey, signTransaction } = useWallet()

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImage(file)

    const reader = new FileReader()
    reader.onloadend = () => {
      setImagePreview(reader.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleAddAttribute = () => {
    if (!newTraitType || !newTraitValue) return

    setAttributes([...attributes, { trait_type: newTraitType, value: newTraitValue }])
    setNewTraitType("")
    setNewTraitValue("")
  }

  const handleRemoveAttribute = (index: number) => {
    setAttributes(attributes.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!publicKey || !signTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour créer un NFT.",
        variant: "destructive",
      })
      return
    }

    if (!name || !symbol || !description || !image) {
      toast({
        title: "Informations manquantes",
        description: "Veuillez remplir tous les champs obligatoires.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      // Dans une implémentation réelle, nous utiliserions le keypair du portefeuille connecté
      // Ici, nous simulons avec un keypair temporaire
      const tempKeypair = Keypair.generate()

      // Préparer les métadonnées du NFT
      const metadata: NFTMetadata = {
        name,
        symbol,
        description,
        image: imagePreview || "", // Dans une implémentation réelle, nous téléchargerions l'image sur IPFS ou Arweave
        attributes,
        external_url: externalUrl || undefined,
        animation_url: animationUrl || undefined,
      }

      // Créer le NFT
      const nftData = await createNFTWithMetadata(tempKeypair, metadata)

      toast({
        title: "NFT créé avec succès",
        description: `Votre NFT "${name}" a été créé avec succès.`,
      })

      // Rediriger vers la galerie
      router.push("/nft-gallery")
    } catch (error: any) {
      console.error("Error creating NFT:", error)
      toast({
        title: "Erreur de création",
        description: error.message || "Une erreur s'est produite lors de la création du NFT.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Créer un nouveau NFT</CardTitle>
        <CardDescription>Remplissez le formulaire ci-dessous pour créer votre NFT sur Solana</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom du NFT *</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Mon Super NFT"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="symbol">Symbole *</Label>
                <Input
                  id="symbol"
                  value={symbol}
                  onChange={(e) => setSymbol(e.target.value)}
                  placeholder="SNFT"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Décrivez votre NFT..."
                rows={3}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="image">Image du NFT *</Label>
              <div className="flex items-center gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById("image-upload")?.click()}
                  className="w-full h-32 flex flex-col items-center justify-center border-dashed"
                >
                  {imagePreview ? (
                    <div className="relative w-full h-full">
                      <img
                        src={imagePreview || "/placeholder.svg"}
                        alt="Preview"
                        className="w-full h-full object-contain"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-1 right-1"
                        onClick={(e) => {
                          e.stopPropagation()
                          setImage(null)
                          setImagePreview(null)
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <>
                      <Upload className="h-8 w-8 mb-2 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">Cliquez pour télécharger une image</span>
                    </>
                  )}
                </Button>
                <input id="image-upload" type="file" accept="image/*" onChange={handleImageChange} className="hidden" />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Attributs</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {attributes.map((attr, index) => (
                  <Badge key={index} variant="outline" className="flex items-center gap-1">
                    <span>
                      {attr.trait_type}: {attr.value}
                    </span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 ml-1"
                      onClick={() => handleRemoveAttribute(index)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Type d'attribut"
                  value={newTraitType}
                  onChange={(e) => setNewTraitType(e.target.value)}
                  className="flex-1"
                />
                <Input
                  placeholder="Valeur"
                  value={newTraitValue}
                  onChange={(e) => setNewTraitValue(e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddAttribute}
                  disabled={!newTraitType || !newTraitValue}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="externalUrl">URL externe (optionnel)</Label>
                <Input
                  id="externalUrl"
                  value={externalUrl}
                  onChange={(e) => setExternalUrl(e.target.value)}
                  placeholder="https://monsite.com/nft"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="animationUrl">URL d'animation (optionnel)</Label>
                <Input
                  id="animationUrl"
                  value={animationUrl}
                  onChange={(e) => setAnimationUrl(e.target.value)}
                  placeholder="https://monsite.com/animation.mp4"
                />
              </div>
            </div>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => router.push("/nft-gallery")}>
          Annuler
        </Button>
        <Button onClick={handleSubmit} disabled={isLoading || !publicKey}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Création en cours...
            </>
          ) : (
            "Créer le NFT"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { getNFTsForSale, getUserNFTs, type NFTData } from "@/lib/nft-service"
import { NFTCard } from "./nft-card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useWallet } from "@solana/wallet-adapter-react"
import { Search, SlidersHorizontal, Plus } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from "next/navigation"

export function NFTGalleryGrid() {
  const [nfts, setNfts] = useState<NFTData[]>([])
  const [userNfts, setUserNfts] = useState<NFTData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("marketplace")
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("newest")
  const [showFilters, setShowFilters] = useState(false)
  const [filterRarity, setFilterRarity] = useState("all")
  const { publicKey } = useWallet()
  const router = useRouter()

  useEffect(() => {
    const fetchNFTs = async () => {
      setIsLoading(true)
      try {
        const nftsData = await getNFTsForSale()
        setNfts(nftsData)

        if (publicKey) {
          const userNftsData = await getUserNFTs(publicKey.toString())
          setUserNfts(userNftsData)
        }
      } catch (error) {
        console.error("Error fetching NFTs:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchNFTs()
  }, [publicKey])

  const refreshData = async () => {
    try {
      const nftsData = await getNFTsForSale()
      setNfts(nftsData)

      if (publicKey) {
        const userNftsData = await getUserNFTs(publicKey.toString())
        setUserNfts(userNftsData)
      }
    } catch (error) {
      console.error("Error refreshing NFT data:", error)
    }
  }

  // Filtrer les NFTs en fonction de l'onglet actif, de la recherche et des filtres
  const filteredNFTs = (activeTab === "marketplace" ? nfts : userNfts).filter((nft) => {
    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        nft.metadata.name.toLowerCase().includes(query) ||
        nft.metadata.description.toLowerCase().includes(query) ||
        nft.metadata.attributes.some(
          (attr) => attr.trait_type.toLowerCase().includes(query) || attr.value.toLowerCase().includes(query),
        )
      )
    }

    // Filtrer par rareté
    if (filterRarity !== "all") {
      const rarityAttr = nft.metadata.attributes.find((attr) => attr.trait_type.toLowerCase() === "rarity")
      if (!rarityAttr || rarityAttr.value.toLowerCase() !== filterRarity.toLowerCase()) {
        return false
      }
    }

    return true
  })

  // Trier les NFTs
  const sortedNFTs = [...filteredNFTs].sort((a, b) => {
    switch (sortBy) {
      case "newest":
        return b.createdAt - a.createdAt
      case "oldest":
        return a.createdAt - b.createdAt
      case "price_asc":
        return (a.price || 0) - (b.price || 0)
      case "price_desc":
        return (b.price || 0) - (a.price || 0)
      default:
        return 0
    }
  })

  // Extraire toutes les raretés uniques pour le filtre
  const rarities = [
    ...new Set(
      [...nfts, ...userNfts]
        .flatMap((nft) => nft.metadata.attributes)
        .filter((attr) => attr.trait_type.toLowerCase() === "rarity")
        .map((attr) => attr.value),
    ),
  ]

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between">
          <Skeleton className="h-10 w-[200px]" />
          <Skeleton className="h-10 w-[200px]" />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Skeleton key={i} className="h-[350px] w-full" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="marketplace">Marketplace</TabsTrigger>
            <TabsTrigger value="my-nfts" disabled={!publicKey}>
              Mes NFTs
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher..."
              className="pl-8 w-[200px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            variant={showFilters ? "default" : "outline"}
            size="icon"
            onClick={() => setShowFilters(!showFilters)}
          >
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
          {activeTab === "my-nfts" && (
            <Button onClick={() => router.push("/nft-gallery/create")}>
              <Plus className="h-4 w-4 mr-1" />
              Créer un NFT
            </Button>
          )}
        </div>
      </div>

      {showFilters && (
        <div className="flex flex-wrap gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="space-y-1">
            <label htmlFor="sort-by" className="text-sm font-medium">
              Trier par
            </label>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger id="sort-by" className="w-[180px]">
                <SelectValue placeholder="Trier par" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Plus récent</SelectItem>
                <SelectItem value="oldest">Plus ancien</SelectItem>
                <SelectItem value="price_asc">Prix (croissant)</SelectItem>
                <SelectItem value="price_desc">Prix (décroissant)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {rarities.length > 0 && (
            <div className="space-y-1">
              <label htmlFor="filter-rarity" className="text-sm font-medium">
                Rareté
              </label>
              <Select value={filterRarity} onValueChange={setFilterRarity}>
                <SelectTrigger id="filter-rarity" className="w-[180px]">
                  <SelectValue placeholder="Filtrer par rareté" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les raretés</SelectItem>
                  {rarities.map((rarity) => (
                    <SelectItem key={rarity} value={rarity}>
                      {rarity}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      )}

      {sortedNFTs.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            {activeTab === "marketplace" ? "Aucun NFT disponible sur le marketplace." : "Vous ne possédez aucun NFT."}
          </p>
          {activeTab === "my-nfts" && (
            <Button className="mt-4" onClick={() => router.push("/nft-gallery/create")}>
              <Plus className="h-4 w-4 mr-1" />
              Créer votre premier NFT
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {sortedNFTs.map((nft) => (
            <NFTCard key={nft.mint} nft={nft} onBuySuccess={refreshData} />
          ))}
        </div>
      )}
    </div>
  )
}

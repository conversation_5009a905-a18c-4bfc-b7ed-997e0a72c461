"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { ArrowUp, ArrowDown, Clock, BarChart3, Users } from "lucide-react"
import blockchainService from "@/lib/coingecko-service"
import type { CoinData } from "@/lib/coingecko-service"

export default function BlockchainNewTokens() {
  const [solanaNewTokens, setSolanaNewTokens] = useState<CoinData[]>([])
  const [bnbNewTokens, setBnbNewTokens] = useState<CoinData[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("solana")
  const [sortBy, setSortBy] = useState<"volume" | "holders">("volume")

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        // Charger les nouveaux tokens Solana
        const solanaNewTokensData = await blockchainService.getNewSolanaTokens(10)
        setSolanaNewTokens(solanaNewTokensData)

        // Charger les nouveaux tokens BNB
        const bnbNewTokensData = await blockchainService.getNewBnbTokens(10)
        setBnbNewTokens(bnbNewTokensData)
      } catch (error) {
        console.error("Error loading new blockchain tokens:", error)
      } finally {
        setLoading(false)
      }
    }

    if (typeof window !== "undefined") {
      loadData()
    }
  }, [])

  // Trier les tokens par volume ou nombre de holders
  const sortedSolanaNewTokens = [...solanaNewTokens].sort((a, b) =>
    sortBy === "volume" ? (b.total_volume || 0) - (a.total_volume || 0) : (b.holders || 0) - (a.holders || 0),
  )

  const sortedBnbNewTokens = [...bnbNewTokens].sort((a, b) =>
    sortBy === "volume" ? (b.total_volume || 0) - (a.total_volume || 0) : (b.holders || 0) - (a.holders || 0),
  )

  // Formater les prix selon leur valeur
  const formatPrice = (price: number) => {
    if (price < 0.00001) {
      return price.toExponential(2)
    }
    if (price < 0.001) {
      return price.toFixed(6)
    }
    if (price < 1) {
      return price.toFixed(4)
    }
    if (price < 10) {
      return price.toFixed(2)
    }
    return price.toLocaleString(undefined, { maximumFractionDigits: 2 })
  }

  // Formater les nombres avec des suffixes K, M, B
  const formatNumber = (num: number | undefined) => {
    if (num === undefined) return "N/A"
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(1) + "B"
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K"
    }
    return num.toString()
  }

  // Calculer l'âge du token en jours
  const getTokenAge = (dateString: string | undefined) => {
    if (!dateString) return "N/A"
    const createdDate = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - createdDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return `${diffDays} jour${diffDays > 1 ? "s" : ""}`
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Nouveaux Tokens</span>
          <div className="flex items-center gap-2">
            <Badge
              variant={sortBy === "volume" ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => setSortBy("volume")}
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              Volume
            </Badge>
            <Badge
              variant={sortBy === "holders" ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => setSortBy("holders")}
            >
              <Users className="h-3 w-3 mr-1" />
              Holders
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="solana" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="solana">Solana</TabsTrigger>
            <TabsTrigger value="bnb">BNB Chain</TabsTrigger>
          </TabsList>

          <TabsContent value="solana">
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {sortedSolanaNewTokens.length > 0 ? (
                  sortedSolanaNewTokens.map((token) => (
                    <div key={token.id} className="flex items-center justify-between border-b pb-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 mr-2 overflow-hidden rounded-full bg-gray-100">
                          <img
                            src={token.image || "/placeholder.svg"}
                            alt={token.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium flex items-center">
                            {token.name}
                            <Badge variant="secondary" className="ml-2 text-xs">
                              Nouveau
                            </Badge>
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center">
                            {token.symbol} •
                            <Clock className="h-3 w-3 mx-1" />
                            {getTokenAge(token.created_at)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${formatPrice(token.current_price)}</div>
                        <div className="flex items-center justify-end gap-2 text-xs">
                          <span
                            className={`flex items-center ${
                              token.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"
                            }`}
                          >
                            {token.price_change_percentage_24h >= 0 ? (
                              <ArrowUp className="h-3 w-3 mr-1" />
                            ) : (
                              <ArrowDown className="h-3 w-3 mr-1" />
                            )}
                            {Math.abs(token.price_change_percentage_24h).toFixed(2)}%
                          </span>
                          <span className="flex items-center text-muted-foreground">
                            <BarChart3 className="h-3 w-3 mr-1" />${formatNumber(token.total_volume)}
                          </span>
                          <span className="flex items-center text-muted-foreground">
                            <Users className="h-3 w-3 mr-1" />
                            {formatNumber(token.holders)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">Aucun nouveau token Solana trouvé</div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="bnb">
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {sortedBnbNewTokens.length > 0 ? (
                  sortedBnbNewTokens.map((token) => (
                    <div key={token.id} className="flex items-center justify-between border-b pb-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 mr-2 overflow-hidden rounded-full bg-gray-100">
                          <img
                            src={token.image || "/placeholder.svg"}
                            alt={token.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium flex items-center">
                            {token.name}
                            <Badge variant="secondary" className="ml-2 text-xs">
                              Nouveau
                            </Badge>
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center">
                            {token.symbol} •
                            <Clock className="h-3 w-3 mx-1" />
                            {getTokenAge(token.created_at)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${formatPrice(token.current_price)}</div>
                        <div className="flex items-center justify-end gap-2 text-xs">
                          <span
                            className={`flex items-center ${
                              token.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"
                            }`}
                          >
                            {token.price_change_percentage_24h >= 0 ? (
                              <ArrowUp className="h-3 w-3 mr-1" />
                            ) : (
                              <ArrowDown className="h-3 w-3 mr-1" />
                            )}
                            {Math.abs(token.price_change_percentage_24h).toFixed(2)}%
                          </span>
                          <span className="flex items-center text-muted-foreground">
                            <BarChart3 className="h-3 w-3 mr-1" />${formatNumber(token.total_volume)}
                          </span>
                          <span className="flex items-center text-muted-foreground">
                            <Users className="h-3 w-3 mr-1" />
                            {formatNumber(token.holders)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">Aucun nouveau token BNB Chain trouvé</div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

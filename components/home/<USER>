export default function Roadmap() {
  const roadmapItems = [
    {
      phase: "Phase 1",
      title: "Platform Launch",
      date: "Q2 2023",
      completed: true,
      items: [
        "Core platform development",
        "Token Factory module",
        "Basic market data",
        "Wallet integration",
        "Solana devnet deployment",
      ],
    },
    {
      phase: "Phase 2",
      title: "Enhanced Features",
      date: "Q4 2023",
      completed: true,
      items: [
        "Advanced token creation options",
        "Staking module implementation",
        "Market charts and analytics",
        "User dashboard improvements",
        "Security enhancements",
      ],
    },
    {
      phase: "Phase 3",
      title: "Governance & Expansion",
      date: "Q2 2024",
      completed: false,
      items: [
        "Governance module launch",
        "DAO voting system",
        "Cross-chain integration",
        "Mobile app development",
        "Advanced analytics",
      ],
    },
    {
      phase: "Phase 4",
      title: "Ecosystem Growth",
      date: "Q4 2024",
      completed: false,
      items: ["Mainnet migration", "Developer API", "Partner integrations", "Enterprise solutions", "Global expansion"],
    },
  ]

  return (
    <div className="relative">
      {/* Vertical line */}
      <div className="absolute left-[15px] md:left-1/2 top-0 bottom-0 w-0.5 bg-[#D4AF37]/30 -translate-x-1/2 z-0"></div>

      <div className="space-y-12">
        {roadmapItems.map((item, index) => (
          <div
            key={index}
            className={`relative z-10 flex flex-col md:flex-row ${index % 2 === 0 ? "md:flex-row-reverse" : ""}`}
          >
            {/* Timeline dot */}
            <div className="absolute left-0 md:left-1/2 top-0 w-8 h-8 bg-background border-4 border-[#D4AF37] rounded-full -translate-x-1/2 z-20"></div>

            {/* Content */}
            <div className="ml-12 md:ml-0 md:w-1/2 md:px-8">
              <div
                className={`p-6 bg-card rounded-lg border ${item.completed ? "border-[#D4AF37]/50" : "border-gray-800"}`}
              >
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <span className="text-sm font-medium text-[#D4AF37]">{item.phase}</span>
                    <h3 className="text-xl font-bold">{item.title}</h3>
                  </div>
                  <span
                    className={`px-3 py-1 rounded-full text-xs ${item.completed ? "bg-[#D4AF37]/20 text-[#D4AF37]" : "bg-gray-800 text-gray-400"}`}
                  >
                    {item.date}
                  </span>
                </div>
                <ul className="space-y-2">
                  {item.items.map((listItem, i) => (
                    <li key={i} className="flex items-start">
                      <span className={`mr-2 text-lg ${item.completed ? "text-[#D4AF37]" : "text-gray-500"}`}>•</span>
                      <span>{listItem}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Empty space for alignment */}
            <div className="hidden md:block md:w-1/2"></div>
          </div>
        ))}
      </div>
    </div>
  )
}

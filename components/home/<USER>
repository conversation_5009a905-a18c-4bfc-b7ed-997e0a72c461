"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowDown, ArrowUp, ExternalLink } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

interface Coin {
  id: string
  name: string
  symbol: string
  image: string
  current_price: number
  price_change_percentage_24h: number
  market_cap: number
  total_volume: number
}

export default function TopCoins() {
  const [coins, setCoins] = useState<Coin[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // In a real implementation, you would fetch this data from an API
    // For demonstration, we'll use mock data
    const mockData: Coin[] = [
      {
        id: "bitcoin",
        name: "Bitcoin",
        symbol: "BTC",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 65432.12,
        price_change_percentage_24h: 2.5,
        market_cap: 1234567890123,
        total_volume: 45678901234,
      },
      {
        id: "ethereum",
        name: "Ethereum",
        symbol: "ETH",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 3456.78,
        price_change_percentage_24h: -1.2,
        market_cap: 456789012345,
        total_volume: 23456789012,
      },
      {
        id: "tether",
        name: "Tether",
        symbol: "USDT",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 1.0,
        price_change_percentage_24h: 0.1,
        market_cap: 87654321098,
        total_volume: 65432109876,
      },
      {
        id: "bnb",
        name: "BNB",
        symbol: "BNB",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 567.89,
        price_change_percentage_24h: 0.8,
        market_cap: 76543210987,
        total_volume: 5432109876,
      },
      {
        id: "solana",
        name: "Solana",
        symbol: "SOL",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 145.67,
        price_change_percentage_24h: 5.8,
        market_cap: 56789012345,
        total_volume: 3456789012,
      },
      {
        id: "xrp",
        name: "XRP",
        symbol: "XRP",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.54,
        price_change_percentage_24h: -0.5,
        market_cap: 28765432109,
        total_volume: 1876543210,
      },
      {
        id: "usdc",
        name: "USD Coin",
        symbol: "USDC",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 1.0,
        price_change_percentage_24h: 0.05,
        market_cap: 32109876543,
        total_volume: 2109876543,
      },
      {
        id: "cardano",
        name: "Cardano",
        symbol: "ADA",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.56,
        price_change_percentage_24h: 1.3,
        market_cap: 19876543210,
        total_volume: 987654321,
      },
      {
        id: "dogecoin",
        name: "Dogecoin",
        symbol: "DOGE",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.12,
        price_change_percentage_24h: -2.7,
        market_cap: 16543210987,
        total_volume: 876543210,
      },
      {
        id: "polkadot",
        name: "Polkadot",
        symbol: "DOT",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 7.89,
        price_change_percentage_24h: 3.2,
        market_cap: 9876543210,
        total_volume: 765432109,
      },
    ]

    // Simulate API fetch delay
    if (typeof window !== "undefined") {
      setTimeout(() => {
        setCoins(mockData)
        setLoading(false)
      }, 1000)
    }
  }, [])

  if (loading) {
    return (
      <Card>
        <CardContent className="p-0">
          <div className="p-4">
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0 overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">#</TableHead>
              <TableHead>Name</TableHead>
              <TableHead className="text-right">Price</TableHead>
              <TableHead className="text-right">24h %</TableHead>
              <TableHead className="text-right">Market Cap</TableHead>
              <TableHead className="text-right">Volume (24h)</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {coins.map((coin, index) => (
              <TableRow key={coin.id}>
                <TableCell className="font-medium">{index + 1}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <img src={coin.image || "/placeholder.svg"} alt={coin.name} className="w-6 h-6" />
                    <span className="font-medium">{coin.name}</span>
                    <span className="text-muted-foreground">{coin.symbol.toUpperCase()}</span>
                  </div>
                </TableCell>
                <TableCell className="text-right font-medium">${coin.current_price.toLocaleString()}</TableCell>
                <TableCell
                  className={`text-right ${coin.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"}`}
                >
                  <div className="flex items-center justify-end">
                    {coin.price_change_percentage_24h >= 0 ? (
                      <ArrowUp className="h-3 w-3 mr-1" />
                    ) : (
                      <ArrowDown className="h-3 w-3 mr-1" />
                    )}
                    {Math.abs(coin.price_change_percentage_24h).toFixed(2)}%
                  </div>
                </TableCell>
                <TableCell className="text-right">${(coin.market_cap / 1000000000).toFixed(2)}B</TableCell>
                <TableCell className="text-right">${(coin.total_volume / 1000000000).toFixed(2)}B</TableCell>
                <TableCell>
                  <Link href={`/market?coin=${coin.id}`}>
                    <ExternalLink className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

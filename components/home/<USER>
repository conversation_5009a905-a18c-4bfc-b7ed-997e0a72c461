"use client"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { ArrowU<PERSON>, ArrowDown, ExternalLink, Trophy, Zap } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import blockchainService from "@/lib/blockchain-service"
import { formatNumber, formatPrice } from "@/lib/utils"
import type { CoinData } from "@/lib/coingecko-service"

interface MemecoinTickerProps {
  blockchain?: "solana" | "bnb" | "all"
  limit?: number
  isPremium?: boolean
  title?: string
  className?: string
}

export default function MemecoinTicker({
  blockchain = "all",
  limit = 20,
  isPremium = false,
  title = "Memecoins Trending",
  className = "",
}: MemecoinTickerProps) {
  const [memecoins, setMemecoins] = useState<CoinData[]>([])
  const [loading, setLoading] = useState(true)
  const tickerRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        let coins: CoinData[] = []

        if (blockchain === "solana" || blockchain === "all") {
          const solanaCoins = await blockchainService.detectSolanaMemecoins(limit)
          coins = [...coins, ...solanaCoins]
        }

        if (blockchain === "bnb" || blockchain === "all") {
          const bnbCoins = await blockchainService.detectBnbMemecoins(limit)
          coins = [...coins, ...bnbCoins]
        }

        // Trier par volume
        coins.sort((a, b) => (b.total_volume || 0) - (a.total_volume || 0))

        // Limiter le nombre de coins
        setMemecoins(coins.slice(0, limit))
      } catch (error) {
        console.error("Error loading memecoin ticker data:", error)
      } finally {
        setLoading(false)
      }
    }

    loadData()

    // Démarrer l'animation de défilement
    const startScrolling = () => {
      if (tickerRef.current) {
        const tickerWidth = tickerRef.current.scrollWidth
        const viewportWidth = tickerRef.current.offsetWidth

        if (tickerWidth > viewportWidth) {
          let scrollPosition = 0
          const scrollSpeed = 1 // pixels par frame

          const scroll = () => {
            if (tickerRef.current) {
              scrollPosition += scrollSpeed

              // Réinitialiser la position lorsqu'un élément est complètement sorti
              if (scrollPosition >= tickerWidth / 2) {
                scrollPosition = 0
              }

              tickerRef.current.scrollLeft = scrollPosition
            }
          }

          const intervalId = setInterval(scroll, 30)
          return () => clearInterval(intervalId)
        }
      }
    }

    const timeoutId = setTimeout(startScrolling, 1000) // Attendre que les données soient chargées

    return () => clearTimeout(timeoutId)
  }, [blockchain, limit])

  const navigateToDetails = (coinId: string) => {
    router.push(`/market?token=${coinId}`)
  }

  // Dupliquer les memecoins pour créer un effet de défilement continu
  const displayedMemecoins = [...memecoins, ...memecoins]

  return (
    <div className={`w-full ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          {isPremium ? (
            <Badge variant="premium" className="mr-2 bg-gradient-to-r from-amber-500 to-yellow-300 text-black">
              <Trophy className="h-3 w-3 mr-1" /> PREMIUM
            </Badge>
          ) : (
            <Badge variant="outline" className="mr-2">
              <Zap className="h-3 w-3 mr-1" /> LIVE
            </Badge>
          )}
          <h3 className="text-lg font-semibold">{title}</h3>
        </div>
        <Badge variant="outline" className="text-xs">
          {blockchain === "solana" ? "Solana" : blockchain === "bnb" ? "BNB Chain" : "Multi-chain"}
        </Badge>
      </div>

      <div
        className={`relative overflow-hidden border rounded-lg ${isPremium ? "border-amber-500/50 bg-amber-500/5" : "border-gray-800 bg-black/20"}`}
      >
        {loading ? (
          <div className="flex items-center justify-center h-14 px-4">
            <div className="animate-pulse flex space-x-4 w-full">
              <div className="h-4 bg-gray-700 rounded w-1/4"></div>
              <div className="h-4 bg-gray-700 rounded w-1/4"></div>
              <div className="h-4 bg-gray-700 rounded w-1/4"></div>
              <div className="h-4 bg-gray-700 rounded w-1/4"></div>
            </div>
          </div>
        ) : (
          <div className="relative overflow-hidden">
            <div
              ref={tickerRef}
              className="flex whitespace-nowrap py-3 px-2 overflow-x-auto scrollbar-hide"
              style={{ scrollBehavior: "smooth" }}
            >
              {displayedMemecoins.map((coin, index) => (
                <TooltipProvider key={`${coin.id}-${index}`}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={`inline-flex items-center mx-2 px-3 py-1.5 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-800 ${isPremium ? "hover:border-amber-500/50" : "hover:border-blue-500/50"}`}
                        onClick={() => navigateToDetails(coin.id)}
                      >
                        <div className="w-6 h-6 mr-2 overflow-hidden rounded-full bg-gray-800 flex-shrink-0">
                          <img
                            src={coin.image || "/placeholder.svg?height=24&width=24"}
                            alt={coin.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex flex-col">
                          <div className="flex items-center">
                            <span className="font-medium text-sm">{coin.symbol}</span>
                            {coin.is_new && (
                              <Badge variant="secondary" className="ml-1.5 text-[10px] px-1 py-0 h-4">
                                NEW
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center text-xs">
                            <span className="text-muted-foreground mr-1.5">${formatPrice(coin.current_price)}</span>
                            <span
                              className={`flex items-center ${
                                coin.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"
                              }`}
                            >
                              {coin.price_change_percentage_24h >= 0 ? (
                                <ArrowUp className="h-3 w-3 mr-0.5" />
                              ) : (
                                <ArrowDown className="h-3 w-3 mr-0.5" />
                              )}
                              {Math.abs(coin.price_change_percentage_24h).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        <div className="ml-3 pl-3 border-l border-gray-700 flex flex-col">
                          <div className="text-xs text-muted-foreground">Vol 24h</div>
                          <div className="text-sm font-medium">${formatNumber(coin.total_volume || 0)}</div>
                        </div>
                        {isPremium && (
                          <div className="ml-3 pl-3 border-l border-gray-700 flex flex-col">
                            <div className="text-xs text-muted-foreground">Holders</div>
                            <div className="text-sm font-medium">{formatNumber(coin.holders || 0)}</div>
                          </div>
                        )}
                        <ExternalLink className="ml-2 h-3 w-3 text-muted-foreground" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <div className="text-xs">
                        <p className="font-bold">{coin.name}</p>
                        <p>Blockchain: {coin.blockchain}</p>
                        <p>Market Cap: ${formatNumber(coin.market_cap || 0)}</p>
                        {coin.created_at && <p>Created: {new Date(coin.created_at).toLocaleDateString()}</p>}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
            </div>

            {/* Gradient overlay pour indiquer le défilement */}
            <div className="absolute top-0 left-0 h-full w-8 bg-gradient-to-r from-background to-transparent pointer-events-none"></div>
            <div className="absolute top-0 right-0 h-full w-8 bg-gradient-to-l from-background to-transparent pointer-events-none"></div>
          </div>
        )}
      </div>
    </div>
  )
}

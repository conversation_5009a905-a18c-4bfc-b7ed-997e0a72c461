"use client"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Arrow<PERSON><PERSON>, ArrowDown, ExternalLink, Zap, Sparkles } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Button } from "@/components/ui/button"
import { formatNumber, formatPrice } from "@/lib/utils"
import type { CoinData } from "@/lib/coingecko-service"
import { useTokenRegistry } from "@/lib/token-registry"

export default function UserTokensTicker() {
  const [loading, setLoading] = useState(true)
  const tickerRef = useRef<HTMLDivElement>(null)
  const router = useRouter()
  const tokens = useTokenRegistry((state) => state.tokens)

  // Convertir les tokens utilisateur en format CoinData
  const userTokens: CoinData[] = tokens.map((token, index) => ({
    id: token.address,
    address: token.address,
    name: token.name,
    symbol: token.symbol.toUpperCase(),
    image: token.logoURI || `/placeholder.svg?height=32&width=32`,
    current_price: Math.random() * 10, // Prix simulé
    price_change_percentage_24h: Math.random() * 40 - 20, // Changement de prix simulé
    market_cap: Math.random() * **********, // Cap. marché simulé
    total_volume: Math.random() * 10000000 + 100000, // Volume simulé
    circulating_supply: Math.random() * **********, // Offre simulée
    blockchain: "Solana",
    created_at: token.createdAt || new Date().toISOString(),
    is_new: true,
    is_recent: true,
    is_meme: token.tags?.includes("meme") || false,
    holders: Math.floor(Math.random() * 5000) + 100, // Holders simulés
    creator: token.creator || "Unknown",
  }))

  useEffect(() => {
    // Simuler le chargement
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    // Démarrer l'animation de défilement
    const startScrolling = () => {
      if (tickerRef.current && userTokens.length > 0) {
        const tickerWidth = tickerRef.current.scrollWidth
        const viewportWidth = tickerRef.current.offsetWidth

        if (tickerWidth > viewportWidth) {
          let scrollPosition = 0
          const scrollSpeed = 1 // pixels par frame

          const scroll = () => {
            if (tickerRef.current) {
              scrollPosition += scrollSpeed

              // Réinitialiser la position lorsqu'un élément est complètement sorti
              if (scrollPosition >= tickerWidth / 2) {
                scrollPosition = 0
              }

              tickerRef.current.scrollLeft = scrollPosition
            }
          }

          const intervalId = setInterval(scroll, 30)
          return () => clearInterval(intervalId)
        }
      }
    }

    const scrollTimer = setTimeout(startScrolling, 1500)

    return () => {
      clearTimeout(timer)
      clearTimeout(scrollTimer)
    }
  }, [userTokens.length])

  const navigateToDetails = (coinId: string) => {
    router.push(`/market?token=${coinId}`)
  }

  const navigateToTokenFactory = () => {
    router.push("/token-factory")
  }

  // Dupliquer les tokens pour créer un effet de défilement continu
  const displayedTokens = [...userTokens, ...userTokens]

  return (
    <div className="w-full mb-8">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <Badge variant="premium" className="mr-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white">
            <Sparkles className="h-3 w-3 mr-1" /> PREMIUM
          </Badge>
          <h3 className="text-lg font-semibold">Tokens Créés par la Communauté</h3>
        </div>
        <Button variant="outline" size="sm" onClick={navigateToTokenFactory} className="text-xs">
          <Zap className="h-3 w-3 mr-1" /> Créer un Token
        </Button>
      </div>

      <div className="relative overflow-hidden border rounded-lg border-purple-500/50 bg-purple-500/5">
        {loading ? (
          <div className="flex items-center justify-center h-14 px-4">
            <div className="animate-pulse flex space-x-4 w-full">
              <div className="h-4 bg-gray-700 rounded w-1/4"></div>
              <div className="h-4 bg-gray-700 rounded w-1/4"></div>
              <div className="h-4 bg-gray-700 rounded w-1/4"></div>
              <div className="h-4 bg-gray-700 rounded w-1/4"></div>
            </div>
          </div>
        ) : userTokens.length > 0 ? (
          <div className="relative overflow-hidden">
            <div
              ref={tickerRef}
              className="flex whitespace-nowrap py-3 px-2 overflow-x-auto scrollbar-hide"
              style={{ scrollBehavior: "smooth" }}
            >
              {displayedTokens.map((token, index) => (
                <TooltipProvider key={`${token.id}-${index}`}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className="inline-flex items-center mx-2 px-3 py-1.5 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-800 hover:border-purple-500/50"
                        onClick={() => navigateToDetails(token.id)}
                      >
                        <div className="w-6 h-6 mr-2 overflow-hidden rounded-full bg-gray-800 flex-shrink-0">
                          <img
                            src={token.image || "/placeholder.svg?height=24&width=24"}
                            alt={token.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex flex-col">
                          <div className="flex items-center">
                            <span className="font-medium text-sm">{token.symbol}</span>
                            <Badge variant="secondary" className="ml-1.5 text-[10px] px-1 py-0 h-4">
                              USER
                            </Badge>
                          </div>
                          <div className="flex items-center text-xs">
                            <span className="text-muted-foreground mr-1.5">${formatPrice(token.current_price)}</span>
                            <span
                              className={`flex items-center ${
                                token.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"
                              }`}
                            >
                              {token.price_change_percentage_24h >= 0 ? (
                                <ArrowUp className="h-3 w-3 mr-0.5" />
                              ) : (
                                <ArrowDown className="h-3 w-3 mr-0.5" />
                              )}
                              {Math.abs(token.price_change_percentage_24h).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        <div className="ml-3 pl-3 border-l border-gray-700 flex flex-col">
                          <div className="text-xs text-muted-foreground">Vol 24h</div>
                          <div className="text-sm font-medium">${formatNumber(token.total_volume || 0)}</div>
                        </div>
                        <div className="ml-3 pl-3 border-l border-gray-700 flex flex-col">
                          <div className="text-xs text-muted-foreground">Holders</div>
                          <div className="text-sm font-medium">{formatNumber(token.holders || 0)}</div>
                        </div>
                        <div className="ml-3 pl-3 border-l border-gray-700 flex flex-col">
                          <div className="text-xs text-muted-foreground">Créateur</div>
                          <div className="text-sm font-medium truncate max-w-[80px]">
                            {token.creator?.substring(0, 6)}...
                          </div>
                        </div>
                        <ExternalLink className="ml-2 h-3 w-3 text-muted-foreground" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <div className="text-xs">
                        <p className="font-bold">{token.name}</p>
                        <p>Blockchain: {token.blockchain}</p>
                        <p>Market Cap: ${formatNumber(token.market_cap || 0)}</p>
                        {token.created_at && <p>Created: {new Date(token.created_at).toLocaleDateString()}</p>}
                        <p>Creator: {token.creator || "Unknown"}</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
            </div>

            {/* Gradient overlay pour indiquer le défilement */}
            <div className="absolute top-0 left-0 h-full w-8 bg-gradient-to-r from-background to-transparent pointer-events-none"></div>
            <div className="absolute top-0 right-0 h-full w-8 bg-gradient-to-l from-background to-transparent pointer-events-none"></div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 px-4">
            <p className="text-muted-foreground mb-3">Aucun token créé par la communauté pour le moment</p>
            <Button variant="outline" size="sm" onClick={navigateToTokenFactory}>
              <Zap className="h-4 w-4 mr-2" /> Créer le premier token
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

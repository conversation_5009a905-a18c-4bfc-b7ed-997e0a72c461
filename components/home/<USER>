"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, ArrowRight, ArrowUp, ArrowDown, Star, StarOff, ExternalLink, Rocket, Calendar } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import type { CoinData } from "@/lib/coingecko-service"

interface MarketCarouselProps {
  coins?: CoinData[]
  title?: string
  onNavigate?: (id: string) => void
  onToggleFavorite?: (id: string) => void
  favorites?: string[]
  loading?: boolean
  blockchain?: string
  isNew?: boolean
  showCreationDate?: boolean
  autoScrollSpeed?: number
  className?: string
  cardClassName?: string
  showBlockchainBadge?: boolean
  showControls?: boolean
  itemsToShow?: number
}

export default function MarketCarousel({
  coins = [],
  title,
  onNavigate,
  onToggleFavorite,
  favorites = [],
  loading = false,
  blockchain,
  isNew = false,
  showCreationDate = false,
  autoScrollSpeed = 5000,
  className = "",
  cardClassName = "",
  showBlockchainBadge = false,
  showControls = true,
  itemsToShow = 4,
}: MarketCarouselProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)
  const [isPaused, setIsPaused] = useState(false)
  const [touchStartX, setTouchStartX] = useState(0)
  const [isGrabbing, setIsGrabbing] = useState(false)
  const [scrollPosition, setScrollPosition] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(0)
  const [visibleCoins, setVisibleCoins] = useState<CoinData[]>([])
  const animationFrameRef = useRef<number | null>(null)
  const scrollIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Calculer le nombre de pages
  const totalPages = Math.ceil((coins?.length || 0) / itemsToShow)

  // Mettre à jour les coins visibles lorsque la page change
  useEffect(() => {
    if (!coins || coins.length === 0) return

    const start = currentPage * itemsToShow
    const end = start + itemsToShow
    setVisibleCoins(coins.slice(start, end))
  }, [currentPage, coins, itemsToShow])

  // Vérifier si on peut défiler à gauche ou à droite
  const checkScrollButtons = useCallback(() => {
    if (!containerRef.current) return

    const { scrollLeft, scrollWidth, clientWidth } = containerRef.current
    setCanScrollLeft(scrollLeft > 0)
    setCanScrollRight(scrollLeft + clientWidth < scrollWidth - 10)
    setScrollPosition(scrollLeft)
  }, [])

  // Ajouter des écouteurs d'événements pour le redimensionnement
  useEffect(() => {
    checkScrollButtons()
    window.addEventListener("resize", checkScrollButtons)
    return () => window.removeEventListener("resize", checkScrollButtons)
  }, [checkScrollButtons, coins])

  // Défilement automatique professionnel
  useEffect(() => {
    if (
      !containerRef.current ||
      isPaused ||
      !autoScrollSpeed ||
      autoScrollSpeed <= 0 ||
      !coins ||
      coins.length <= itemsToShow
    )
      return

    // Nettoyer les intervalles précédents
    if (scrollIntervalRef.current) {
      clearInterval(scrollIntervalRef.current)
    }

    // Créer un nouvel intervalle pour le défilement automatique
    scrollIntervalRef.current = setInterval(() => {
      if (!containerRef.current) return

      // Si on est à la dernière page, revenir à la première
      if (currentPage >= totalPages - 1) {
        setCurrentPage(0)
      } else {
        // Sinon, passer à la page suivante
        setCurrentPage((prev) => prev + 1)
      }
    }, autoScrollSpeed)

    return () => {
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current)
      }
    }
  }, [isPaused, autoScrollSpeed, coins, currentPage, totalPages, itemsToShow])

  // Fonction pour aller à une page spécifique
  const goToPage = useCallback(
    (page: number) => {
      if (page < 0 || page >= totalPages) return
      setCurrentPage(page)
      setIsPaused(true)
      setTimeout(() => setIsPaused(false), 5000)
    },
    [totalPages],
  )

  // Fonction pour aller à la page suivante
  const nextPage = useCallback(() => {
    goToPage((currentPage + 1) % totalPages)
  }, [currentPage, goToPage, totalPages])

  // Fonction pour aller à la page précédente
  const prevPage = useCallback(() => {
    goToPage((currentPage - 1 + totalPages) % totalPages)
  }, [currentPage, goToPage, totalPages])

  // Gestionnaire d'événements pour mettre en pause le défilement automatique lors de l'interaction
  const handleMouseEnter = useCallback(() => {
    setIsPaused(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsPaused(false)
    setIsGrabbing(false)
  }, [])

  // Formatage des prix
  const formatPrice = useCallback((price: number) => {
    if (!price) return "N/A"
    if (price < 0.00001) {
      return price.toExponential(2)
    }
    if (price < 0.001) {
      return price.toFixed(6)
    }
    if (price < 1) {
      return price.toFixed(4)
    }
    if (price < 10) {
      return price.toFixed(2)
    }
    return price.toLocaleString(undefined, { maximumFractionDigits: 2 })
  }, [])

  // Formatage des grands nombres
  const formatNumber = useCallback((num: number, digits = 2) => {
    if (!num) return "N/A"
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(digits) + "B"
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(digits) + "M"
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(digits) + "K"
    }
    return num.toFixed(digits)
  }, [])

  // Formatage des dates avec vérification
  const formatDate = useCallback((dateString: string | undefined) => {
    if (!dateString) return "N/A"
    try {
      const date = new Date(dateString)
      // Vérifier si la date est valide
      if (isNaN(date.getTime())) return "N/A"

      return date.toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "short",
        day: "numeric",
      })
    } catch (e) {
      console.error("Error formatting date:", e)
      return "N/A"
    }
  }, [])

  // Vérifier si un token est récent (moins d'un mois) avec vérification
  const isRecentToken = useCallback((createdAt: string | undefined): boolean => {
    if (!createdAt) return false
    try {
      const date = new Date(createdAt)
      // Vérifier si la date est valide
      if (isNaN(date.getTime())) return false

      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
      return date > oneMonthAgo
    } catch (e) {
      console.error("Error checking if token is recent:", e)
      return false
    }
  }, [])

  // Afficher un état de chargement
  if (loading) {
    return (
      <div className={`relative ${className}`}>
        {title && <h3 className="text-xl font-bold mb-4">{title}</h3>}
        <div className="flex overflow-x-auto pb-4 hide-scrollbar">
          <div className="flex gap-4 px-1">
            {Array.from({ length: itemsToShow }).map((_, index) => (
              <Card key={index} className={`min-w-[300px] border-gray-800 ${cardClassName}`}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between gap-3 mb-4">
                    <div className="flex items-center gap-3">
                      <Skeleton className="w-10 h-10 rounded-full" />
                      <div>
                        <Skeleton className="h-5 w-24 mb-1" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    </div>
                    <Skeleton className="h-5 w-5 rounded-full" />
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                    <div>
                      <Skeleton className="h-4 w-16 mb-1" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                    <div>
                      <Skeleton className="h-4 w-16 mb-1" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                  </div>
                  <Skeleton className="h-4 w-32" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Afficher un message d'erreur ou un état vide
  if (error || !coins || coins.length === 0) {
    return (
      <div className={`relative ${className}`}>
        {title && <h3 className="text-xl font-bold mb-4">{title}</h3>}
        <Card className={`min-w-full border-gray-800 ${cardClassName}`}>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">{error || "Aucune donnée disponible pour le moment"}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {title && (
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold">{title}</h3>
          {showControls && totalPages > 1 && (
            <div className="flex items-center gap-2">
              <div className="flex gap-1">
                {Array.from({ length: totalPages }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToPage(index)}
                    className={`w-2 h-2 rounded-full ${currentPage === index ? "bg-[#D4AF37]" : "bg-gray-600"}`}
                    aria-label={`Page ${index + 1}`}
                  />
                ))}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={prevPage}
                  disabled={totalPages <= 1}
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span className="sr-only">Page précédente</span>
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={nextPage}
                  disabled={totalPages <= 1}
                >
                  <ArrowRight className="h-4 w-4" />
                  <span className="sr-only">Page suivante</span>
                </Button>
              </div>
            </div>
          )}
        </div>
      )}

      {blockchain && showBlockchainBadge && (
        <div className="mb-4">
          <Badge className="bg-[#D4AF37] text-black text-sm px-3 py-1">{blockchain}</Badge>
        </div>
      )}

      <div
        className="flex overflow-x-auto pb-4 hide-scrollbar"
        ref={containerRef}
        onScroll={checkScrollButtons}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{ scrollBehavior: "smooth" }}
      >
        <div className="flex gap-4 px-1">
          {visibleCoins.map((coin, index) => {
            // Vérifier si le token est réellement récent
            const isActuallyRecent = coin.created_at ? isRecentToken(coin.created_at) : false

            return (
              <Card
                key={`${coin.id}-${index}`}
                className={`min-w-[300px] border-gray-800 hover:border-[#D4AF37] transition-colors cursor-pointer ${cardClassName}`}
                onClick={() => onNavigate && onNavigate(coin.id)}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between gap-3 mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-800 flex items-center justify-center">
                        {coin.image ? (
                          <Image
                            src={coin.image || "/placeholder.svg"}
                            alt={coin.name}
                            width={40}
                            height={40}
                            className="object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.src = "/placeholder.svg?height=40&width=40"
                            }}
                          />
                        ) : (
                          <span className="text-xs text-gray-400">{coin.symbol?.substring(0, 3).toUpperCase()}</span>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-bold">{coin.name}</h3>
                          {isActuallyRecent && (
                            <Badge className="bg-green-500 text-white text-xs">
                              <Rocket className="h-3 w-3 mr-1" /> Nouveau
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-sm text-muted-foreground">{coin.symbol?.toUpperCase()}</p>
                          {showBlockchainBadge && coin.blockchain && (
                            <Badge variant="outline" className="text-xs px-1 py-0 h-4">
                              {coin.blockchain}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    {onToggleFavorite && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          onToggleFavorite(coin.id)
                        }}
                        className="text-gray-400 hover:text-[#D4AF37]"
                      >
                        {favorites.includes(coin.id) ? (
                          <Star className="h-5 w-5 fill-[#D4AF37] text-[#D4AF37]" />
                        ) : (
                          <StarOff className="h-5 w-5" />
                        )}
                      </button>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-smxt-sm mb-4">
                    <div>
                      <span className="text-muted-foreground">Prix:</span>{" "}
                      <div className="font-medium text-lg">${formatPrice(coin.current_price)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">24h:</span>{" "}
                      <div
                        className={`font-medium flex items-center ${
                          coin.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"
                        }`}
                      >
                        {coin.price_change_percentage_24h >= 0 ? (
                          <ArrowUp className="h-3 w-3 mr-1" />
                        ) : (
                          <ArrowDown className="h-3 w-3 mr-1" />
                        )}
                        {Math.abs(coin.price_change_percentage_24h || 0).toFixed(2)}%
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <div>
                      {(showCreationDate || isActuallyRecent) && coin.created_at ? (
                        <div className="flex items-center text-xs">
                          <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                          <span className="text-muted-foreground">Créé le:</span>{" "}
                          <span className="font-medium ml-1">{formatDate(coin.created_at)}</span>
                        </div>
                      ) : (
                        <>
                          <span className="text-muted-foreground">Cap. Marché:</span>{" "}
                          <span className="font-medium">${formatNumber(coin.market_cap)}</span>
                        </>
                      )}
                    </div>
                    <Link
                      href={`/market?token=${coin.id}`}
                      className="text-[#D4AF37] hover:text-[#B8941F] flex items-center gap-1 text-sm font-medium"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Détails <ExternalLink className="h-3 w-3" />
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {showControls && (
        <>
          {canScrollLeft && (
            <div className="absolute top-1/2 -left-4 -translate-y-1/2">
              <Button
                variant="outline"
                size="icon"
                className="rounded-full bg-background/80 backdrop-blur-sm"
                onClick={prevPage}
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Précédent</span>
              </Button>
            </div>
          )}

          {canScrollRight && (
            <div className="absolute top-1/2 -right-4 -translate-y-1/2">
              <Button
                variant="outline"
                size="icon"
                className="rounded-full bg-background/80 backdrop-blur-sm"
                onClick={nextPage}
              >
                <ArrowRight className="h-4 w-4" />
                <span className="sr-only">Suivant</span>
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowD<PERSON>, ArrowUp, ExternalLink, Rocket } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"

interface Coin {
  id: string
  name: string
  symbol: string
  image: string
  current_price: number
  price_change_percentage_24h: number
  market_cap: number
  total_volume: number
  isNew?: boolean
}

export default function MemeCoins() {
  const [coins, setCoins] = useState<Coin[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // In a real implementation, you would fetch this data from an API
    // For demonstration, we'll use mock data
    const mockData: Coin[] = [
      {
        id: "dogecoin",
        name: "<PERSON><PERSON><PERSON><PERSON>",
        symbol: "<PERSON>O<PERSON>",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.12,
        price_change_percentage_24h: -2.7,
        market_cap: 16543210987,
        total_volume: 876543210,
      },
      {
        id: "shiba-inu",
        name: "Shiba Inu",
        symbol: "SHIB",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.000023,
        price_change_percentage_24h: 1.5,
        market_cap: 13654321098,
        total_volume: 765432109,
      },
      {
        id: "bonk",
        name: "Bonk",
        symbol: "BONK",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.000012,
        price_change_percentage_24h: 15.8,
        market_cap: 654321098,
        total_volume: 87654321,
        isNew: true,
      },
      {
        id: "samoyedcoin",
        name: "Samoyedcoin",
        symbol: "SAMO",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.0145,
        price_change_percentage_24h: 8.3,
        market_cap: 543210987,
        total_volume: 76543210,
      },
      {
        id: "solana-doge",
        name: "Solana Doge",
        symbol: "SDOGE",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.0078,
        price_change_percentage_24h: 12.7,
        market_cap: 432109876,
        total_volume: 65432109,
      },
      {
        id: "catcoin",
        name: "Catcoin",
        symbol: "CATS",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.00034,
        price_change_percentage_24h: -5.2,
        market_cap: 321098765,
        total_volume: 54321098,
      },
      {
        id: "dogwifhat",
        name: "Dogwifhat",
        symbol: "WIF",
        image: "/placeholder.svg?height=32&width=32",
        current_price: 0.89,
        price_change_percentage_24h: 25.3,
        market_cap: 210987654,
        total_volume: 43210987,
        isNew: true,
      },
    ]

    // Simulate API fetch delay
    if (typeof window !== "undefined") {
      setTimeout(() => {
        setCoins(mockData)
        setLoading(false)
      }, 1000)
    }
  }, [])

  if (loading) {
    return (
      <Card>
        <CardContent className="p-0">
          <div className="p-4">
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-8 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0 overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">#</TableHead>
              <TableHead>Name</TableHead>
              <TableHead className="text-right">Price</TableHead>
              <TableHead className="text-right">24h %</TableHead>
              <TableHead className="text-right">Market Cap</TableHead>
              <TableHead className="text-right">Volume (24h)</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {coins.map((coin, index) => (
              <TableRow key={coin.id}>
                <TableCell className="font-medium">{index + 1}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <img src={coin.image || "/placeholder.svg"} alt={coin.name} className="w-6 h-6" />
                    <span className="font-medium">{coin.name}</span>
                    <span className="text-muted-foreground">{coin.symbol.toUpperCase()}</span>
                    {coin.isNew && (
                      <Badge className="bg-[#D4AF37] text-black">
                        <Rocket className="h-3 w-3 mr-1" /> New
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell className="text-right font-medium">${coin.current_price.toLocaleString()}</TableCell>
                <TableCell
                  className={`text-right ${coin.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"}`}
                >
                  <div className="flex items-center justify-end">
                    {coin.price_change_percentage_24h >= 0 ? (
                      <ArrowUp className="h-3 w-3 mr-1" />
                    ) : (
                      <ArrowDown className="h-3 w-3 mr-1" />
                    )}
                    {Math.abs(coin.price_change_percentage_24h).toFixed(2)}%
                  </div>
                </TableCell>
                <TableCell className="text-right">${(coin.market_cap / 1000000).toFixed(2)}M</TableCell>
                <TableCell className="text-right">${(coin.total_volume / 1000000).toFixed(2)}M</TableCell>
                <TableCell>
                  <Link href={`/market?coin=${coin.id}`}>
                    <ExternalLink className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

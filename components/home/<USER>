"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { ArrowUp, ArrowDown, Users, BarChart3 } from "lucide-react"
import blockchainService from "@/lib/blockchain-service"
import type { CoinData } from "@/lib/coingecko-service"

export default function BlockchainMemecoins() {
  const [solanaMemeCoins, setSolanaMemeCoins] = useState<CoinData[]>([])
  const [bnbMemeCoins, setBnbMemeCoins] = useState<CoinData[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("solana")
  const [sortBy, setSortBy] = useState<"volume" | "holders">("volume")

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        // Charger les memecoins Solana
        const solanaMemecoinsData = await blockchainService.detectSolanaMemecoins(10)
        setSolanaMemeCoins(solanaMemecoinsData)

        // Charger les memecoins BNB
        const bnbMemecoinsData = await blockchainService.detectBnbMemecoins(10)
        setBnbMemeCoins(bnbMemecoinsData)
      } catch (error) {
        console.error("Error loading blockchain memecoins:", error)
      } finally {
        setLoading(false)
      }
    }

    if (typeof window !== "undefined") {
      loadData()
    }
  }, [])

  // Trier les memecoins par volume ou nombre de holders
  const sortedSolanaMemecoins = [...solanaMemeCoins].sort((a, b) =>
    sortBy === "volume" ? (b.total_volume || 0) - (a.total_volume || 0) : (b.holders || 0) - (a.holders || 0),
  )

  const sortedBnbMemecoins = [...bnbMemeCoins].sort((a, b) =>
    sortBy === "volume" ? (b.total_volume || 0) - (a.total_volume || 0) : (b.holders || 0) - (a.holders || 0),
  )

  // Formater les prix selon leur valeur
  const formatPrice = (price: number) => {
    if (price < 0.00001) {
      return price.toExponential(2)
    }
    if (price < 0.001) {
      return price.toFixed(6)
    }
    if (price < 1) {
      return price.toFixed(4)
    }
    if (price < 10) {
      return price.toFixed(2)
    }
    return price.toLocaleString(undefined, { maximumFractionDigits: 2 })
  }

  // Formater les nombres avec des suffixes K, M, B
  const formatNumber = (num: number | undefined) => {
    if (num === undefined) return "N/A"
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(1) + "B"
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K"
    }
    return num.toString()
  }

  // Formater les dates
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return date.toLocaleDateString()
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Memecoins Blockchain</span>
          <div className="flex items-center gap-2">
            <Badge
              variant={sortBy === "volume" ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => setSortBy("volume")}
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              Volume
            </Badge>
            <Badge
              variant={sortBy === "holders" ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => setSortBy("holders")}
            >
              <Users className="h-3 w-3 mr-1" />
              Holders
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="solana" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="solana">Solana</TabsTrigger>
            <TabsTrigger value="bnb">BNB Chain</TabsTrigger>
          </TabsList>

          <TabsContent value="solana">
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {sortedSolanaMemecoins.length > 0 ? (
                  sortedSolanaMemecoins.map((coin) => (
                    <div key={coin.id} className="flex items-center justify-between border-b pb-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 mr-2 overflow-hidden rounded-full bg-gray-100">
                          <img
                            src={coin.image || "/placeholder.svg"}
                            alt={coin.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium">{coin.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {coin.symbol} • Créé le {formatDate(coin.created_at)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${formatPrice(coin.current_price)}</div>
                        <div className="flex items-center justify-end gap-2 text-xs">
                          <span
                            className={`flex items-center ${
                              coin.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"
                            }`}
                          >
                            {coin.price_change_percentage_24h >= 0 ? (
                              <ArrowUp className="h-3 w-3 mr-1" />
                            ) : (
                              <ArrowDown className="h-3 w-3 mr-1" />
                            )}
                            {Math.abs(coin.price_change_percentage_24h).toFixed(2)}%
                          </span>
                          <span className="flex items-center text-muted-foreground">
                            <BarChart3 className="h-3 w-3 mr-1" />${formatNumber(coin.total_volume)}
                          </span>
                          <span className="flex items-center text-muted-foreground">
                            <Users className="h-3 w-3 mr-1" />
                            {formatNumber(coin.holders || Math.floor(Math.random() * 10000))}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">Aucun memecoin Solana trouvé</div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="bnb">
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {sortedBnbMemecoins.length > 0 ? (
                  sortedBnbMemecoins.map((coin) => (
                    <div key={coin.id} className="flex items-center justify-between border-b pb-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 mr-2 overflow-hidden rounded-full bg-gray-100">
                          <img
                            src={coin.image || "/placeholder.svg"}
                            alt={coin.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium">{coin.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {coin.symbol} • Créé le {formatDate(coin.created_at)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${formatPrice(coin.current_price)}</div>
                        <div className="flex items-center justify-end gap-2 text-xs">
                          <span
                            className={`flex items-center ${
                              coin.price_change_percentage_24h >= 0 ? "text-green-500" : "text-red-500"
                            }`}
                          >
                            {coin.price_change_percentage_24h >= 0 ? (
                              <ArrowUp className="h-3 w-3 mr-1" />
                            ) : (
                              <ArrowDown className="h-3 w-3 mr-1" />
                            )}
                            {Math.abs(coin.price_change_percentage_24h).toFixed(2)}%
                          </span>
                          <span className="flex items-center text-muted-foreground">
                            <BarChart3 className="h-3 w-3 mr-1" />${formatNumber(coin.total_volume)}
                          </span>
                          <span className="flex items-center text-muted-foreground">
                            <Users className="h-3 w-3 mr-1" />
                            {formatNumber(coin.holders || Math.floor(Math.random() * 10000))}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">Aucun memecoin BNB Chain trouvé</div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

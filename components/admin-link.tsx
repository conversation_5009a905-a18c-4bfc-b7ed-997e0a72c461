"use client"

import type { ReactNode } from "react"
import Link from "next/link"
import { useAdmin } from "@/hooks/use-admin"

interface AdminLinkProps {
  href: string
  children: ReactNode
  className?: string
  onClick?: () => void
}

export function AdminLink({ href, children, className, onClick }: AdminLinkProps) {
  const { isAdmin, isLoading } = useAdmin()

  // Ne rien afficher pendant le chargement ou si l'utilisateur n'est pas admin
  if (isLoading || !isAdmin) {
    return null
  }

  return (
    <Link href={href} className={className} onClick={onClick}>
      {children}
    </Link>
  )
}

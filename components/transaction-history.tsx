"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"

interface Transaction {
  id: string
  type: "buy" | "sell" | "swap"
  amount: number
  tokenSymbol: string
  price: number
  timestamp: Date
  status: "completed" | "pending" | "failed"
  signature?: string
}

interface TransactionHistoryProps {
  tokenAddress?: string // Optional: filter by token
  walletAddress?: string // Optional: filter by wallet
  limit?: number
}

export default function TransactionHistory({ tokenAddress, walletAddress, limit = 10 }: TransactionHistoryProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchTransactions() {
      try {
        // In a real implementation, this would fetch from an API
        // For now, we'll use mock data
        const mockTransactions: Transaction[] = [
          {
            id: "1",
            type: "buy",
            amount: 100,
            tokenSymbol: "SOL",
            price: 0.05,
            timestamp: new Date(Date.now() - 1000 * 60 * 5),
            status: "completed",
            signature: "5UxV7...8Yz",
          },
          {
            id: "2",
            type: "sell",
            amount: 50,
            tokenSymbol: "MMGF",
            price: 0.02,
            timestamp: new Date(Date.now() - 1000 * 60 * 30),
            status: "completed",
            signature: "3Jk2L...9Pq",
          },
          {
            id: "3",
            type: "swap",
            amount: 200,
            tokenSymbol: "USDC",
            price: 1,
            timestamp: new Date(Date.now() - 1000 * 60 * 60),
            status: "pending",
            signature: "7Mn4R...2Ts",
          },
        ]

        setTransactions(mockTransactions)
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching transactions:", error)
        setIsLoading(false)
      }
    }

    fetchTransactions()
  }, [tokenAddress, walletAddress, limit])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500"
      case "pending":
        return "bg-yellow-500"
      case "failed":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "buy":
        return "bg-blue-500"
      case "sell":
        return "bg-purple-500"
      case "swap":
        return "bg-orange-500"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transaction History</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : transactions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">No transactions found</div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Time</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Signature</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((tx) => (
                  <TableRow key={tx.id}>
                    <TableCell>
                      <Badge className={getTypeColor(tx.type)}>{tx.type.toUpperCase()}</Badge>
                    </TableCell>
                    <TableCell>
                      {tx.amount} {tx.tokenSymbol}
                    </TableCell>
                    <TableCell>${tx.price.toFixed(4)}</TableCell>
                    <TableCell>{formatDistanceToNow(tx.timestamp, { addSuffix: true })}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(tx.status)}>{tx.status}</Badge>
                    </TableCell>
                    <TableCell>
                      <a
                        href={`https://explorer.solana.com/tx/${tx.signature}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        {tx.signature?.substring(0, 4)}...{tx.signature?.substring(tx.signature.length - 4)}
                      </a>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { InfoIcon as InfoCircle } from "lucide-react"

interface BondingCurveChartProps {
  initialSupply?: number
  maxSupply?: number
  initialPrice?: number
  curveType?: "linear" | "exponential" | "logarithmic" | "sigmoid"
  onCurveChange?: (params: {
    initialSupply: number
    maxSupply: number
    initialPrice: number
    curveType: string
  }) => void
}

export function BondingCurveChart({
  initialSupply = 1000000,
  maxSupply = 10000000,
  initialPrice = 0.0001,
  curveType = "linear",
  onCurveChange,
}: BondingCurveChartProps) {
  const [supply, setSupply] = useState(initialSupply)
  const [max, setMax] = useState(maxSupply)
  const [price, setPrice] = useState(initialPrice)
  const [curve, setCurve] = useState<"linear" | "exponential" | "logarithmic" | "sigmoid">(curveType)
  const [curveData, setCurveData] = useState<Array<{ supply: number; price: number; marketCap: number }>>([])
  const [simulationAmount, setSimulationAmount] = useState(10000)
  const [simulationResult, setSimulationResult] = useState({ price: 0, cost: 0 })

  // Calculer les points de la courbe
  useEffect(() => {
    const points = 100
    const data = []

    for (let i = 0; i <= points; i++) {
      const currentSupply = supply + (i * (max - supply)) / points
      let currentPrice

      switch (curve) {
        case "linear":
          currentPrice = price * (1 + (currentSupply - supply) / supply)
          break
        case "exponential":
          currentPrice = price * Math.pow(1.5, (currentSupply - supply) / supply)
          break
        case "logarithmic":
          currentPrice = price * (1 + Math.log(1 + (currentSupply - supply) / supply))
          break
        case "sigmoid":
          const midpoint = (max - supply) / 2 + supply
          const steepness = 10 / (max - supply)
          currentPrice = price * (1 + 1 / (1 + Math.exp(-steepness * (currentSupply - midpoint))))
          break
        default:
          currentPrice = price
      }

      data.push({
        supply: currentSupply,
        price: currentPrice,
        marketCap: currentSupply * currentPrice,
      })
    }

    setCurveData(data)

    // Notifier le parent des changements
    if (onCurveChange) {
      onCurveChange({
        initialSupply: supply,
        maxSupply: max,
        initialPrice: price,
        curveType: curve,
      })
    }

    // Mettre à jour la simulation
    simulatePurchase(simulationAmount)
  }, [supply, max, price, curve, onCurveChange])

  // Simuler un achat de tokens
  const simulatePurchase = (amount: number) => {
    if (amount <= 0 || amount > max - supply) {
      setSimulationResult({ price: 0, cost: 0 })
      return
    }

    let totalCost = 0
    let finalPrice = 0
    const smallIncrement = amount / 100 // Diviser l'achat en petits incréments pour une meilleure précision

    let currentSupply = supply

    for (let i = 0; i < 100; i++) {
      let currentPrice

      switch (curve) {
        case "linear":
          currentPrice = price * (1 + (currentSupply - supply) / supply)
          break
        case "exponential":
          currentPrice = price * Math.pow(1.5, (currentSupply - supply) / supply)
          break
        case "logarithmic":
          currentPrice = price * (1 + Math.log(1 + (currentSupply - supply) / supply))
          break
        case "sigmoid":
          const midpoint = (max - supply) / 2 + supply
          const steepness = 10 / (max - supply)
          currentPrice = price * (1 + 1 / (1 + Math.exp(-steepness * (currentSupply - midpoint))))
          break
        default:
          currentPrice = price
      }

      totalCost += currentPrice * smallIncrement
      currentSupply += smallIncrement
      finalPrice = currentPrice
    }

    setSimulationResult({ price: finalPrice, cost: totalCost })
  }

  // Formater les nombres pour l'affichage
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(2) + "M"
    } else if (num >= 1000) {
      return (num / 1000).toFixed(2) + "K"
    } else {
      return num.toFixed(2)
    }
  }

  // Formater les prix pour l'affichage
  const formatPrice = (num: number) => {
    if (num < 0.00001) {
      return num.toExponential(2)
    } else {
      return num.toFixed(6)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Courbe de Bonding</CardTitle>
        <CardDescription>
          Visualisez comment le prix de votre token évolue en fonction de l'offre en circulation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs defaultValue="chart" className="space-y-4">
          <TabsList>
            <TabsTrigger value="chart">Graphique</TabsTrigger>
            <TabsTrigger value="simulation">Simulation</TabsTrigger>
            <TabsTrigger value="parameters">Paramètres</TabsTrigger>
          </TabsList>

          <TabsContent value="chart" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={curveData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="supply"
                    tickFormatter={(value) => formatNumber(value)}
                    label={{ value: "Offre en circulation", position: "insideBottom", offset: -5 }}
                  />
                  <YAxis
                    dataKey="price"
                    tickFormatter={(value) => formatPrice(value)}
                    label={{ value: "Prix (SOL)", angle: -90, position: "insideLeft" }}
                  />
                  <Tooltip
                    formatter={(value: number) => [formatPrice(value) + " SOL", "Prix"]}
                    labelFormatter={(label) => `Offre: ${formatNumber(label)}`}
                  />
                  <Line
                    type="monotone"
                    dataKey="price"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">Prix initial</p>
                <p className="font-medium">{formatPrice(price)} SOL</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">Prix final estimé</p>
                <p className="font-medium">{formatPrice(curveData[curveData.length - 1]?.price || 0)} SOL</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">Cap. marché initiale</p>
                <p className="font-medium">{formatNumber(supply * price)} SOL</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">Cap. marché max</p>
                <p className="font-medium">{formatNumber((curveData[curveData.length - 1]?.price || 0) * max)} SOL</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="simulation" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="simulation-amount">Montant à acheter</Label>
                <div className="flex gap-2">
                  <Input
                    id="simulation-amount"
                    type="number"
                    value={simulationAmount}
                    onChange={(e) => {
                      const value = Number.parseInt(e.target.value)
                      setSimulationAmount(value)
                      simulatePurchase(value)
                    }}
                    min={1}
                    max={max - supply}
                  />
                  <span className="flex items-center text-sm text-muted-foreground">tokens</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <p className="text-sm text-muted-foreground">Prix final estimé</p>
                  <p className="text-xl font-bold">{formatPrice(simulationResult.price)} SOL</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <p className="text-sm text-muted-foreground">Coût total estimé</p>
                  <p className="text-xl font-bold">{formatPrice(simulationResult.cost)} SOL</p>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg flex gap-2">
                <InfoCircle className="h-5 w-5 text-blue-700 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-blue-700">
                    Cette simulation estime le coût d'achat de {simulationAmount.toLocaleString()} tokens en tenant
                    compte de l'augmentation progressive du prix selon la courbe de bonding.
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="parameters" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="curve-type">Type de courbe</Label>
                <select
                  id="curve-type"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={curve}
                  onChange={(e) => setCurve(e.target.value as any)}
                >
                  <option value="linear">Linéaire</option>
                  <option value="exponential">Exponentielle</option>
                  <option value="logarithmic">Logarithmique</option>
                  <option value="sigmoid">Sigmoïde</option>
                </select>
              </div>

              <div>
                <Label htmlFor="initial-supply">Offre initiale</Label>
                <div className="flex gap-2">
                  <Input
                    id="initial-supply"
                    type="number"
                    value={supply}
                    onChange={(e) => setSupply(Number.parseInt(e.target.value))}
                    min={1}
                    max={max - 1}
                  />
                  <span className="flex items-center text-sm text-muted-foreground">tokens</span>
                </div>
              </div>

              <div>
                <Label htmlFor="max-supply">Offre maximale</Label>
                <div className="flex gap-2">
                  <Input
                    id="max-supply"
                    type="number"
                    value={max}
                    onChange={(e) => setMax(Number.parseInt(e.target.value))}
                    min={supply + 1}
                  />
                  <span className="flex items-center text-sm text-muted-foreground">tokens</span>
                </div>
              </div>

              <div>
                <Label htmlFor="initial-price">Prix initial</Label>
                <div className="flex gap-2">
                  <Input
                    id="initial-price"
                    type="number"
                    value={price}
                    onChange={(e) => setPrice(Number.parseFloat(e.target.value))}
                    min={0.0000001}
                    step={0.0000001}
                  />
                  <span className="flex items-center text-sm text-muted-foreground">SOL</span>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

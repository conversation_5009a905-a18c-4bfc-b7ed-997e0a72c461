"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON>lider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Rocket, Info, AlertTriangle } from "lucide-react"

interface LaunchConfigurationProps {
  addLiquidity: boolean
  setAddLiquidity: (value: boolean) => void
  liquidityAmount: string
  setLiquidityAmount: (value: string) => void
  liquidityPercentage: number
  setLiquidityPercentage: (value: number) => void
  lockLiquidity: boolean
  setLockLiquidity: (value: boolean) => void
  lockDuration: number
  setLockDuration: (value: number) => void
  setupInitialTrade: boolean
  setSetupInitialTrade: (value: boolean) => void
}

export function LaunchConfiguration({
  addLiquidity,
  setAddLiquidity,
  liquidityAmount,
  setLiquidityAmount,
  liquidityPercentage,
  setLiquidityPercentage,
  lockLiquidity,
  setLockLiquidity,
  lockDuration,
  setLockDuration,
  setupInitialTrade,
  setSetupInitialTrade,
}: LaunchConfigurationProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Rocket className="h-5 w-5" />
            Configuration de lancement
          </CardTitle>
          <CardDescription>
            Configurez les options de lancement pour votre token, y compris la liquidité initiale
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="bg-amber-50 border-amber-200">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              Les options de lancement avancées sont disponibles uniquement sur Solana Mainnet. Sur Devnet, ces options
              sont simulées pour des fins de test.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="add-liquidity" className="text-base">
                  Ajouter de la liquidité initiale
                </Label>
                <p className="text-sm text-muted-foreground">
                  Crée automatiquement un pool de liquidité sur un DEX pour permettre l'échange de votre token
                </p>
              </div>
              <Switch id="add-liquidity" checked={addLiquidity} onCheckedChange={setAddLiquidity} />
            </div>

            {addLiquidity && (
              <div className="space-y-4 pl-4 border-l-2 border-primary/20">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="liquidity-amount" className="text-sm">
                      Montant de SOL à ajouter
                    </Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Ce montant de SOL sera utilisé pour créer le pool de liquidité initial. Plus le montant est
                            élevé, plus le prix initial du token sera stable.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Input
                    id="liquidity-amount"
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={liquidityAmount}
                    onChange={(e) => setLiquidityAmount(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Nous recommandons au moins 0.1 SOL pour une liquidité initiale suffisante
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="liquidity-percentage" className="text-sm">
                      Pourcentage de tokens à ajouter à la liquidité
                    </Label>
                    <span className="text-sm font-medium">{liquidityPercentage}%</span>
                  </div>
                  <Slider
                    id="liquidity-percentage"
                    value={[liquidityPercentage]}
                    min={1}
                    max={100}
                    step={1}
                    onValueChange={(value) => setLiquidityPercentage(value[0])}
                  />
                  <p className="text-xs text-muted-foreground">
                    Nous recommandons d'allouer au moins 50% de vos tokens à la liquidité initiale
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="lock-liquidity" className="text-sm">
                      Verrouiller la liquidité
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Verrouille la liquidité pour une période définie pour renforcer la confiance des investisseurs
                    </p>
                  </div>
                  <Switch id="lock-liquidity" checked={lockLiquidity} onCheckedChange={setLockLiquidity} />
                </div>

                {lockLiquidity && (
                  <div className="space-y-2 pl-4 border-l-2 border-primary/20">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="lock-duration" className="text-sm">
                        Durée de verrouillage (jours)
                      </Label>
                      <span className="text-sm font-medium">{lockDuration} jours</span>
                    </div>
                    <Slider
                      id="lock-duration"
                      value={[lockDuration]}
                      min={7}
                      max={365}
                      step={1}
                      onValueChange={(value) => setLockDuration(value[0])}
                    />
                    <p className="text-xs text-muted-foreground">
                      Une période de verrouillage plus longue inspire plus de confiance aux investisseurs
                    </p>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="setup-initial-trade" className="text-sm">
                      Configurer une transaction initiale
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Effectue une petite transaction pour initialiser le graphique de prix
                    </p>
                  </div>
                  <Switch id="setup-initial-trade" checked={setupInitialTrade} onCheckedChange={setSetupInitialTrade} />
                </div>
              </div>
            )}
          </div>

          <div className="p-4 bg-muted rounded-md">
            <h3 className="font-medium mb-2">Informations sur le lancement</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <Info className="h-4 w-4 text-muted-foreground mt-0.5" />
                <span>
                  Après la création du token, vous pourrez le lister sur des DEX comme Raydium, Orca ou Jupiter.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <Info className="h-4 w-4 text-muted-foreground mt-0.5" />
                <span>
                  Pour une meilleure visibilité, nous vous recommandons d'ajouter de la liquidité et de verrouiller
                  celle-ci pour au moins 90 jours.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <Info className="h-4 w-4 text-muted-foreground mt-0.5" />
                <span>
                  Le prix initial de votre token sera déterminé par le rapport entre le montant de SOL et le nombre de
                  tokens dans le pool de liquidité.
                </span>
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  Shield,
  Percent,
  Users,
  Ban,
  Wallet,
  FileText,
  Activity,
  Flame,
  Lock,
  RefreshCw,
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"

interface AdvancedTokenDetailsProps {
  tokenAddress: string
}

interface TokenData {
  name: string
  symbol: string
  decimals: number
  totalSupply: number
  circulatingSupply: number
  burnedSupply: number
  priceImpactReserve: number
  owner: string
  burnWallet: string
  priceImpactWallet: string
  createdAt: number
  fees: {
    baseFee: number
    additionalFee: number
    antiGainThreshold: number
    antiGainTax: number
  }
  transactionLimits: {
    maxTransactionSize: number
    maxWalletHolding: number
    maxPurchasesPerBlock: number
    maxSalesPerBlock: number
    cooldownPeriod: number
  }
  priceImpactConfig: {
    reservePercentage: number
    purchaseTrigger: number
    sellTrigger1: number
    sellTrigger2: number
    sellAmount1: number
    sellAmount2: number
    minimumInterval: number
  }
  blacklist: {
    count: number
    addresses: string[]
  }
  daoProposals: {
    active: number
    passed: number
    rejected: number
    executed: number
  }
  priceHistory: {
    timestamp: number
    price: number
  }[]
  transactions: {
    signature: string
    type: string
    amount: number
    fee: number
    timestamp: number
    from: string
    to: string
  }[]
}

export default function AdvancedTokenDetails({ tokenAddress }: AdvancedTokenDetailsProps) {
  const { publicKey, connected, signMessage } = useWallet()
  const { toast } = useToast()
  const { activeNetwork } = useNetwork()

  const [tokenData, setTokenData] = useState<TokenData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("overview")

  // États pour les actions
  const [transferAmount, setTransferAmount] = useState("")
  const [transferRecipient, setTransferRecipient] = useState("")
  const [blacklistAddress, setBlacklistAddress] = useState("")
  const [isTransferring, setIsTransferring] = useState(false)
  const [isBlacklisting, setIsBlacklisting] = useState(false)

  // Charger les données du token
  useEffect(() => {
    const fetchTokenData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Dans une implémentation réelle, nous ferions un appel API
        // Simuler un délai de chargement
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Données simulées pour la démo
        const mockData: TokenData = {
          name: "Advanced Demo Token",
          symbol: "ADT",
          decimals: 9,
          totalSupply: 1000000000,
          circulatingSupply: *********,
          burnedSupply: 50000000,
          priceImpactReserve: 50000000,
          owner: "8JYVFy3pYsPSpPRsqimYxggTHcKFQxq3zLybz19R5YMF",
          burnWallet: "BurnWalletAddress123456789012345678901234567890",
          priceImpactWallet: "PriceImpactWallet123456789012345678901234567890",
          createdAt: Date.now() - 86400000, // 1 jour avant
          fees: {
            baseFee: 1,
            additionalFee: 10,
            antiGainThreshold: 150,
            antiGainTax: 10,
          },
          transactionLimits: {
            maxTransactionSize: 10000000,
            maxWalletHolding: 50000000,
            maxPurchasesPerBlock: 3,
            maxSalesPerBlock: 2,
            cooldownPeriod: 60,
          },
          priceImpactConfig: {
            reservePercentage: 5,
            purchaseTrigger: 10,
            sellTrigger1: 60,
            sellTrigger2: 100,
            sellAmount1: 5,
            sellAmount2: 10,
            minimumInterval: 3600,
          },
          blacklist: {
            count: 2,
            addresses: [
              "BlacklistedAddress1234567890123456789012345678901",
              "BlacklistedAddress2234567890123456789012345678901",
            ],
          },
          daoProposals: {
            active: 1,
            passed: 2,
            rejected: 1,
            executed: 1,
          },
          priceHistory: Array.from({ length: 24 }, (_, i) => ({
            timestamp: Date.now() - (24 - i) * 3600000,
            price: 0.01 + Math.random() * 0.02,
          })),
          transactions: Array.from({ length: 10 }, (_, i) => ({
            signature: `signature${i}`,
            type: ["buy", "sell", "transfer"][Math.floor(Math.random() * 3)],
            amount: Math.floor(Math.random() * 1000000),
            fee: Math.floor(Math.random() * 1000),
            timestamp: Date.now() - Math.floor(Math.random() * 86400000),
            from: `wallet${i}`,
            to: `wallet${i + 1}`,
          })),
        }

        setTokenData(mockData)
      } catch (err: any) {
        console.error("Erreur lors du chargement des données du token:", err)
        setError(err.message || "Une erreur s'est produite lors du chargement des données du token")
      } finally {
        setIsLoading(false)
      }
    }

    fetchTokenData()
  }, [tokenAddress])

  // Transférer des tokens
  const handleTransfer = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet non connecté",
        description: "Veuillez connecter votre wallet pour transférer des tokens",
        variant: "destructive",
      })
      return
    }

    if (!transferAmount || !transferRecipient) {
      toast({
        title: "Données manquantes",
        description: "Veuillez saisir un montant et un destinataire",
        variant: "destructive",
      })
      return
    }

    try {
      setIsTransferring(true)

      // Dans une implémentation réelle, nous ferions un appel API
      // Simuler un délai de traitement
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Transfert réussi",
        description: `${transferAmount} ${tokenData?.symbol} ont été transférés à ${transferRecipient.substring(0, 8)}...`,
      })

      setTransferAmount("")
      setTransferRecipient("")
    } catch (err: any) {
      toast({
        title: "Erreur de transfert",
        description: err.message || "Une erreur s'est produite lors du transfert",
        variant: "destructive",
      })
    } finally {
      setIsTransferring(false)
    }
  }

  // Blacklister une adresse
  const handleBlacklist = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet non connecté",
        description: "Veuillez connecter votre wallet pour blacklister une adresse",
        variant: "destructive",
      })
      return
    }

    if (!blacklistAddress) {
      toast({
        title: "Adresse manquante",
        description: "Veuillez saisir une adresse à blacklister",
        variant: "destructive",
      })
      return
    }

    try {
      setIsBlacklisting(true)

      // Dans une implémentation réelle, nous ferions un appel API
      // Simuler un délai de traitement
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Adresse blacklistée",
        description: `L'adresse ${blacklistAddress.substring(0, 8)}... a été ajoutée à la blacklist`,
      })

      setBlacklistAddress("")
    } catch (err: any) {
      toast({
        title: "Erreur de blacklist",
        description: err.message || "Une erreur s'est produite lors de l'ajout à la blacklist",
        variant: "destructive",
      })
    } finally {
      setIsBlacklisting(false)
    }
  }

  // Créer une proposition DAO
  const handleCreateProposal = () => {
    // Rediriger vers la page de création de proposition
    window.location.href = `/dao/create-proposal?tokenAddress=${tokenAddress}`
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-12 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
        </div>
        <div className="mt-8">
          <Skeleton className="h-64" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Erreur</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!tokenData) {
    return (
      <Alert>
        <AlertTitle>Token non trouvé</AlertTitle>
        <AlertDescription>Le token avec l'adresse {tokenAddress} n'a pas été trouvé.</AlertDescription>
      </Alert>
    )
  }

  // Formater les nombres
  const formatNumber = (num: number) => {
    return num.toLocaleString("fr-FR")
  }

  // Formater les pourcentages
  const formatPercent = (num: number) => {
    return `${num}%`
  }

  // Formater les adresses
  const formatAddress = (address: string) => {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            {tokenData.name} <Badge variant="outline">{tokenData.symbol}</Badge>
          </h1>
          <p className="text-gray-500">
            Créé {formatDistanceToNow(new Date(tokenData.createdAt), { addSuffix: true, locale: fr })}
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(`https://explorer.solana.com/address/${tokenAddress}`, "_blank")}
          >
            Explorer
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(`/token-factory/advanced/edit/${tokenAddress}`, "_self")}
          >
            Modifier
          </Button>
          <Button variant="default" size="sm" onClick={handleCreateProposal}>
            Créer une proposition DAO
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Offre totale</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(tokenData.totalSupply)}</div>
            <div className="mt-2 text-xs text-gray-500">
              <div className="flex justify-between mb-1">
                <span>Circulation</span>
                <span>{formatPercent((tokenData.circulatingSupply / tokenData.totalSupply) * 100)}</span>
              </div>
              <Progress value={(tokenData.circulatingSupply / tokenData.totalSupply) * 100} className="h-1" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Tokens brûlés</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(tokenData.burnedSupply)}</div>
            <div className="mt-2 text-xs text-gray-500">
              <div className="flex justify-between mb-1">
                <span>% de l'offre totale</span>
                <span>{formatPercent((tokenData.burnedSupply / tokenData.totalSupply) * 100)}</span>
              </div>
              <Progress value={(tokenData.burnedSupply / tokenData.totalSupply) * 100} className="h-1" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Réserve d'impact prix</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(tokenData.priceImpactReserve)}</div>
            <div className="mt-2 text-xs text-gray-500">
              <div className="flex justify-between mb-1">
                <span>% de l'offre totale</span>
                <span>{formatPercent((tokenData.priceImpactReserve / tokenData.totalSupply) * 100)}</span>
              </div>
              <Progress value={(tokenData.priceImpactReserve / tokenData.totalSupply) * 100} className="h-1" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid grid-cols-2 md:grid-cols-5 mb-4">
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="dao">Gouvernance DAO</TabsTrigger>
          <TabsTrigger value="security">Sécurité</TabsTrigger>
          <TabsTrigger value="admin">Administration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Percent className="h-5 w-5" /> Mécanismes de frais
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium">Frais de base</h4>
                  <p className="text-sm text-gray-500">
                    {formatPercent(tokenData.fees.baseFee)} sur toutes les transactions
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Frais additionnels</h4>
                  <p className="text-sm text-gray-500">
                    {formatPercent(tokenData.fees.additionalFee)} sur les swaps uniquement
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Taxe anti-gain excessif</h4>
                  <p className="text-sm text-gray-500">
                    {formatPercent(tokenData.fees.antiGainTax)} au-delà de{" "}
                    {formatPercent(tokenData.fees.antiGainThreshold)} de gain
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" /> Limites de transaction
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium">Taille maximale de transaction</h4>
                  <p className="text-sm text-gray-500">
                    {formatNumber(tokenData.transactionLimits.maxTransactionSize)} tokens
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Détention maximale par wallet</h4>
                  <p className="text-sm text-gray-500">
                    {formatNumber(tokenData.transactionLimits.maxWalletHolding)} tokens
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Limites par bloc</h4>
                  <p className="text-sm text-gray-500">
                    Max {tokenData.transactionLimits.maxPurchasesPerBlock} achats, Max{" "}
                    {tokenData.transactionLimits.maxSalesPerBlock} ventes
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Période de refroidissement</h4>
                  <p className="text-sm text-gray-500">{tokenData.transactionLimits.cooldownPeriod} secondes</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" /> Impact sur les prix
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium">Réserve</h4>
                  <p className="text-sm text-gray-500">
                    {formatPercent(tokenData.priceImpactConfig.reservePercentage)} de l'offre totale
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Déclencheurs d'achat</h4>
                  <p className="text-sm text-gray-500">
                    Achat automatique à -{formatPercent(tokenData.priceImpactConfig.purchaseTrigger)} de baisse
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Déclencheurs de vente</h4>
                  <p className="text-sm text-gray-500">
                    Vente de {formatPercent(tokenData.priceImpactConfig.sellAmount1)} à +
                    {formatPercent(tokenData.priceImpactConfig.sellTrigger1)} de hausse
                  </p>
                  <p className="text-sm text-gray-500">
                    Vente de {formatPercent(tokenData.priceImpactConfig.sellAmount2)} à +
                    {formatPercent(tokenData.priceImpactConfig.sellTrigger2)} de hausse
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Intervalle minimum</h4>
                  <p className="text-sm text-gray-500">{tokenData.priceImpactConfig.minimumInterval / 60} minutes</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" /> Gouvernance DAO
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium">Propositions actives</h4>
                    <p className="text-2xl font-bold">{tokenData.daoProposals.active}</p>
                  </div>

                  <div>
                    <h4 className="font-medium">Propositions passées</h4>
                    <p className="text-2xl font-bold">{tokenData.daoProposals.passed}</p>
                  </div>

                  <div>
                    <h4 className="font-medium">Propositions rejetées</h4>
                    <p className="text-2xl font-bold">{tokenData.daoProposals.rejected}</p>
                  </div>

                  <div>
                    <h4 className="font-medium">Propositions exécutées</h4>
                    <p className="text-2xl font-bold">{tokenData.daoProposals.executed}</p>
                  </div>
                </div>

                <Button variant="outline" className="w-full" onClick={handleCreateProposal}>
                  Voir toutes les propositions
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Dernières transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-xs text-gray-500">
                      <th className="pb-2">Type</th>
                      <th className="pb-2">Montant</th>
                      <th className="pb-2">De</th>
                      <th className="pb-2">À</th>
                      <th className="pb-2">Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tokenData.transactions.slice(0, 5).map((tx, index) => (
                      <tr key={index} className="border-t border-gray-100">
                        <td className="py-3">
                          <Badge
                            variant={tx.type === "buy" ? "success" : tx.type === "sell" ? "destructive" : "secondary"}
                          >
                            {tx.type === "buy" ? "Achat" : tx.type === "sell" ? "Vente" : "Transfert"}
                          </Badge>
                        </td>
                        <td className="py-3">{formatNumber(tx.amount)}</td>
                        <td className="py-3">{formatAddress(tx.from)}</td>
                        <td className="py-3">{formatAddress(tx.to)}</td>
                        <td className="py-3 text-sm text-gray-500">
                          {formatDistanceToNow(new Date(tx.timestamp), { addSuffix: true, locale: fr })}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-4 text-center">
                <Button variant="link" onClick={() => setActiveTab("transactions")}>
                  Voir toutes les transactions
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Historique des transactions</CardTitle>
              <CardDescription>Toutes les transactions effectuées avec ce token</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-xs text-gray-500">
                      <th className="pb-2">Signature</th>
                      <th className="pb-2">Type</th>
                      <th className="pb-2">Montant</th>
                      <th className="pb-2">Frais</th>
                      <th className="pb-2">De</th>
                      <th className="pb-2">À</th>
                      <th className="pb-2">Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tokenData.transactions.map((tx, index) => (
                      <tr key={index} className="border-t border-gray-100">
                        <td className="py-3 text-xs text-blue-500">
                          <a
                            href={`https://explorer.solana.com/tx/${tx.signature}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {formatAddress(tx.signature)}
                          </a>
                        </td>
                        <td className="py-3">
                          <Badge
                            variant={tx.type === "buy" ? "success" : tx.type === "sell" ? "destructive" : "secondary"}
                          >
                            {tx.type === "buy" ? "Achat" : tx.type === "sell" ? "Vente" : "Transfert"}
                          </Badge>
                        </td>
                        <td className="py-3">{formatNumber(tx.amount)}</td>
                        <td className="py-3">{formatNumber(tx.fee)}</td>
                        <td className="py-3">{formatAddress(tx.from)}</td>
                        <td className="py-3">{formatAddress(tx.to)}</td>
                        <td className="py-3 text-sm text-gray-500">
                          {formatDistanceToNow(new Date(tx.timestamp), { addSuffix: true, locale: fr })}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Transférer des tokens</CardTitle>
              <CardDescription>Envoyez des tokens à une autre adresse</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="transferAmount">Montant</Label>
                  <Input
                    id="transferAmount"
                    type="number"
                    placeholder="Montant à transférer"
                    value={transferAmount}
                    onChange={(e) => setTransferAmount(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="transferRecipient">Destinataire</Label>
                  <Input
                    id="transferRecipient"
                    placeholder="Adresse du destinataire"
                    value={transferRecipient}
                    onChange={(e) => setTransferRecipient(e.target.value)}
                  />
                </div>
              </div>

              <Button className="mt-4 w-full" onClick={handleTransfer} disabled={isTransferring || !connected}>
                {isTransferring ? "Transfert en cours..." : "Transférer"}
              </Button>

              {!connected && (
                <p className="text-sm text-red-500 mt-2">Veuillez connecter votre wallet pour transférer des tokens</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dao" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Propositions actives</CardTitle>
              <CardDescription>Propositions en cours de vote</CardDescription>
            </CardHeader>
            <CardContent>
              {tokenData.daoProposals.active > 0 ? (
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">Modification des frais de base</h3>
                        <p className="text-sm text-gray-500">Proposition de réduction des frais de base de 1% à 0.8%</p>
                      </div>
                      <Badge>En cours</Badge>
                    </div>

                    <div className="mt-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Votes pour: 65%</span>
                        <span>Quorum: 8% / 10%</span>
                      </div>
                      <Progress value={65} className="h-2" />
                    </div>

                    <div className="mt-4 flex gap-2">
                      <Button variant="default" size="sm">
                        Voter pour
                      </Button>
                      <Button variant="outline" size="sm">
                        Voter contre
                      </Button>
                      <Button variant="link" size="sm">
                        Détails
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Aucune proposition active en ce moment</p>
                  <Button variant="outline" className="mt-4" onClick={handleCreateProposal}>
                    Créer une proposition
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Propositions passées</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{tokenData.daoProposals.passed}</div>
                <Button variant="link" className="p-0 h-auto mt-2">
                  Voir le détail
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Propositions rejetées</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{tokenData.daoProposals.rejected}</div>
                <Button variant="link" className="p-0 h-auto mt-2">
                  Voir le détail
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Propositions exécutées</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{tokenData.daoProposals.executed}</div>
                <Button variant="link" className="p-0 h-auto mt-2">
                  Voir le détail
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Créer une proposition</CardTitle>
              <CardDescription>Proposez des modifications aux paramètres du token</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                  <Percent className="h-6 w-6" />
                  <span>Modifier les frais</span>
                </Button>

                <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                  <Shield className="h-6 w-6" />
                  <span>Modifier les limites</span>
                </Button>

                <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                  <Ban className="h-6 w-6" />
                  <span>Débloquer une adresse</span>
                </Button>

                <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                  <BarChart3 className="h-6 w-6" />
                  <span>Impact sur les prix</span>
                </Button>

                <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                  <Wallet className="h-6 w-6" />
                  <span>Allocation de fonds</span>
                </Button>

                <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                  <FileText className="h-6 w-6" />
                  <span>Autre proposition</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Blacklist</CardTitle>
              <CardDescription>Adresses blacklistées qui ne peuvent pas interagir avec le token</CardDescription>
            </CardHeader>
            <CardContent>
              {tokenData.blacklist.count > 0 ? (
                <div className="space-y-4">
                  {tokenData.blacklist.addresses.map((address, index) => (
                    <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                      <div className="text-sm font-mono">{formatAddress(address)}</div>
                      <Button variant="outline" size="sm">
                        Débloquer
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center py-4 text-gray-500">Aucune adresse blacklistée</p>
              )}

              <div className="mt-6 space-y-4">
                <h3 className="font-medium">Ajouter une adresse à la blacklist</h3>
                <div className="flex gap-2">
                  <Input
                    placeholder="Adresse à blacklister"
                    value={blacklistAddress}
                    onChange={(e) => setBlacklistAddress(e.target.value)}
                  />
                  <Button onClick={handleBlacklist} disabled={isBlacklisting || !connected}>
                    {isBlacklisting ? "En cours..." : "Blacklister"}
                  </Button>
                </div>

                {!connected && (
                  <p className="text-sm text-red-500">Veuillez connecter votre wallet pour blacklister une adresse</p>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Flame className="h-5 w-5" /> Wallet de burn
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-3 bg-gray-50 rounded-lg font-mono text-sm break-all">{tokenData.burnWallet}</div>
                <p className="mt-2 text-sm text-gray-500">
                  Ce wallet est gelé et ne peut pas transférer de tokens. Il reçoit une partie des frais de transaction.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" /> Wallet d'impact sur les prix
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-3 bg-gray-50 rounded-lg font-mono text-sm break-all">
                  {tokenData.priceImpactWallet}
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  Ce wallet est utilisé pour les mécanismes automatiques d'achat et de vente afin de stabiliser le prix.
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Protections anti-bot</CardTitle>
              <CardDescription>Mécanismes de protection contre les bots et les attaques</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h3 className="font-medium">Protection anti-sandwich</h3>
                    <p className="text-sm text-gray-500">Détecte et bloque les attaques sandwich</p>
                  </div>
                  <Badge variant="outline" className="bg-green-50">
                    Activé
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h3 className="font-medium">Limites par bloc</h3>
                    <p className="text-sm text-gray-500">
                      Max {tokenData.transactionLimits.maxPurchasesPerBlock} achats et{" "}
                      {tokenData.transactionLimits.maxSalesPerBlock} ventes par bloc
                    </p>
                  </div>
                  <Badge variant="outline" className="bg-green-50">
                    Activé
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h3 className="font-medium">Période de refroidissement</h3>
                    <p className="text-sm text-gray-500">
                      {tokenData.transactionLimits.cooldownPeriod} secondes entre les transactions
                    </p>
                  </div>
                  <Badge variant="outline" className="bg-green-50">
                    Activé
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h3 className="font-medium">Détection de contrats</h3>
                    <p className="text-sm text-gray-500">Bloque les interactions avec des contrats non autorisés</p>
                  </div>
                  <Badge variant="outline" className="bg-green-50">
                    Activé
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="admin" className="space-y-6">
          <Alert className="bg-yellow-50 border-yellow-200">
            <AlertTitle className="flex items-center gap-2">
              <Lock className="h-4 w-4" /> Accès restreint
            </AlertTitle>
            <AlertDescription>
              Cette section est réservée au propriétaire du token. Connectez votre wallet pour accéder aux
              fonctionnalités d'administration.
            </AlertDescription>
          </Alert>

          {connected && publicKey?.toString() === tokenData.owner ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Paramètres du token</CardTitle>
                  <CardDescription>Modifier les paramètres du token</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                      <Percent className="h-6 w-6" />
                      <span>Modifier les frais</span>
                    </Button>

                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                      <Shield className="h-6 w-6" />
                      <span>Modifier les limites</span>
                    </Button>

                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                      <BarChart3 className="h-6 w-6" />
                      <span>Impact sur les prix</span>
                    </Button>

                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                      <Ban className="h-6 w-6" />
                      <span>Gérer la blacklist</span>
                    </Button>

                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                      <Users className="h-6 w-6" />
                      <span>Paramètres DAO</span>
                    </Button>

                    <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center gap-2">
                      <RefreshCw className="h-6 w-6" />
                      <span>Mettre à jour le logo</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Actions avancées</CardTitle>
                  <CardDescription>Actions réservées au propriétaire du token</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h3 className="font-medium">Transférer la propriété</h3>
                        <p className="text-sm text-gray-500">Transférer la propriété du token à une autre adresse</p>
                      </div>
                      <Button variant="outline">Transférer</Button>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h3 className="font-medium">Geler le token</h3>
                        <p className="text-sm text-gray-500">Suspendre temporairement toutes les transactions</p>
                      </div>
                      <Button variant="outline">Geler</Button>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h3 className="font-medium">Mint d'urgence</h3>
                        <p className="text-sm text-gray-500">Créer de nouveaux tokens en cas d'urgence</p>
                      </div>
                      <Button variant="outline">Mint</Button>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h3 className="font-medium text-red-500">Renoncer à la propriété</h3>
                        <p className="text-sm text-gray-500">
                          Renoncer définitivement à la propriété du token (irréversible)
                        </p>
                      </div>
                      <Button variant="destructive">Renoncer</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="text-center py-12">
              <Lock className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">Accès non autorisé</h3>
              <p className="text-gray-500 mb-6">
                Vous n'êtes pas le propriétaire de ce token. Seul le propriétaire peut accéder à cette section.
              </p>
              <Button variant="outline" onClick={() => window.location.reload()}>
                Actualiser
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Stepper, Step, StepTitle, StepDescription } from "@/components/ui/stepper"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"
import { BasicInfoForm } from "./form-steps/basic-info-form"
import { TokenomicsForm } from "./form-steps/tokenomics-form"
import { SecurityForm } from "./form-steps/security-form"
import { LaunchForm } from "./form-steps/launch-form"
import { MarketingForm } from "./form-steps/marketing-form"
import { TokenPreview } from "./token-preview"
import { TokenCreationProgress } from "./token-creation-progress"
import PaymentOptions from "./payment-options"
import tokenCreationServiceEnhanced from "@/lib/token-creation-service-enhanced"
import { AlertCircle, CheckCircle2, Info, Loader2, ArrowRight } from "lucide-react"
import type { Keypair } from "@solana/web3.js"

// Schéma de validation pour le formulaire complet
const tokenFormSchema = z.object({
  // Informations de base
  name: z.string().min(3, { message: "Le nom doit contenir au moins 3 caractères" }),
  symbol: z.string().min(2, { message: "Le symbole doit contenir au moins 2 caractères" }).max(10),
  description: z.string().optional(),
  decimals: z.number().int().min(0).max(9),
  initialSupply: z.number().positive(),
  maxSupply: z.number().positive(),
  logo: z.any().optional(),
  suffix: z.string().optional(),

  // Tokenomics
  distribution: z.array(
    z.object({
      label: z.string(),
      percentage: z.number().min(0).max(100),
      address: z.string().optional(),
    }),
  ),

  // Sécurité
  isMintable: z.boolean().default(true),
  isBurnable: z.boolean().default(true),
  isPausable: z.boolean().default(false),
  isTransferTaxable: z.boolean().default(false),
  transferTaxRate: z.number().min(0).max(10),
  maxTxAmount: z.number().min(0),
  maxWalletAmount: z.number().min(0),
  antiBot: z.boolean().default(false),
  antiDump: z.boolean().default(false),

  // Lancement
  addLiquidity: z.boolean().default(false),
  liquidityAmount: z.number().min(0),
  liquidityPercentage: z.number().min(0).max(100),
  lockLiquidity: z.boolean().default(false),
  lockDuration: z.number().min(0),
  setupInitialTrade: z.boolean().default(false),

  // Marketing
  website: z.string().url().optional().or(z.literal("")),
  twitter: z.string().url().optional().or(z.literal("")),
  telegram: z.string().url().optional().or(z.literal("")),
  discord: z.string().url().optional().or(z.literal("")),
})

type TokenFormValues = z.infer<typeof tokenFormSchema>

export function EnhancedTokenCreator() {
  const router = useRouter()
  const { publicKey, signTransaction, connected } = useWallet()
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()
  const [activeStep, setActiveStep] = useState(0)
  const [isCreating, setIsCreating] = useState(false)
  const [creationProgress, setCreationProgress] = useState(0)
  const [creationStep, setCreationStep] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [createdTokenAddress, setCreatedTokenAddress] = useState<string | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const [mintKeypair, setMintKeypair] = useState<Keypair | null>(null)
  const [paymentSignature, setPaymentSignature] = useState<string | null>(null)
  const [showPayment, setShowPayment] = useState(false)
  const [creationSteps, setCreationSteps] = useState([
    {
      id: "init",
      title: "Initialisation",
      description: "Préparation des paramètres du token",
      status: "pending" as const,
    },
    {
      id: "address",
      title: "Génération d'adresse",
      description: "Création de l'adresse du token",
      status: "pending" as const,
    },
    {
      id: "transaction",
      title: "Création du token",
      description: "Déploiement sur la blockchain",
      status: "pending" as const,
    },
    {
      id: "metadata",
      title: "Métadonnées",
      description: "Ajout des métadonnées du token",
      status: "pending" as const,
    },
    {
      id: "liquidity",
      title: "Liquidité",
      description: "Configuration de la liquidité",
      status: "pending" as const,
    },
  ])

  // Initialiser le formulaire avec des valeurs par défaut
  const form = useForm<TokenFormValues>({
    resolver: zodResolver(tokenFormSchema),
    defaultValues: {
      name: "",
      symbol: "",
      description: "",
      decimals: 9,
      initialSupply: 1000000000,
      maxSupply: 10000000000,
      suffix: "GF",
      distribution: [
        { label: "Équipe", percentage: 15 },
        { label: "Marketing", percentage: 10 },
        { label: "Développement", percentage: 15 },
        { label: "Réserve", percentage: 10 },
        { label: "Liquidité", percentage: 50 },
      ],
      isMintable: true,
      isBurnable: true,
      isPausable: false,
      isTransferTaxable: false,
      transferTaxRate: 0,
      maxTxAmount: 0,
      maxWalletAmount: 0,
      antiBot: false,
      antiDump: false,
      addLiquidity: false,
      liquidityAmount: 0.1,
      liquidityPercentage: 50,
      lockLiquidity: false,
      lockDuration: 180,
      setupInitialTrade: false,
      website: "",
      twitter: "",
      telegram: "",
      discord: "",
    },
  })

  // Vérifier le solde du portefeuille
  useEffect(() => {
    if (publicKey) {
      // Simuler la récupération du solde (à remplacer par un appel réel à la blockchain)
      setWalletBalance(1.5) // Exemple: 1.5 SOL
    }
  }, [publicKey])

  // Étapes du formulaire
  const steps = [
    {
      id: "basic",
      title: "Informations de base",
      description: "Nom, symbole et offre",
    },
    {
      id: "tokenomics",
      title: "Tokenomics",
      description: "Distribution et vesting",
    },
    {
      id: "security",
      title: "Sécurité",
      description: "Protections et limites",
    },
    {
      id: "launch",
      title: "Lancement",
      description: "Liquidité et listing",
    },
    {
      id: "marketing",
      title: "Marketing",
      description: "Présence en ligne",
    },
    {
      id: "payment",
      title: "Paiement",
      description: "Finaliser la création",
    },
  ]

  // Gérer le changement d'étape
  const nextStep = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1)
    }
  }

  const prevStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1)
    }
  }

  // Mettre à jour le statut d'une étape de création
  const updateCreationStatus = (
    stepId: string,
    status: "pending" | "processing" | "completed" | "failed" | "skipped",
  ) => {
    setCreationSteps((prevSteps) =>
      prevSteps.map((step) => (step.id === stepId ? { ...step, status: status as any } : step)),
    )
  }

  // Gérer le changement de logo
  const handleLogoChange = (file: File | null, preview: string | null) => {
    form.setValue("logo", file)
    setLogoPreview(preview)
  }

  // Gérer le paiement réussi
  const handlePaymentComplete = (signature: string, method: string) => {
    setPaymentSignature(signature)
    toast({
      title: "Paiement réussi",
      description: `Votre paiement a été traité avec succès via ${method.toUpperCase()}.`,
    })

    // Lancer la création du token
    handleCreateToken()
  }

  // Gérer l'erreur de paiement
  const handlePaymentError = (errorMessage: string) => {
    toast({
      title: "Erreur de paiement",
      description: errorMessage,
      variant: "destructive",
    })
  }

  // Préparer la création du token
  const handlePrepareToken = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour créer un token",
        variant: "destructive",
      })
      return
    }

    // Vérifier le solde du portefeuille
    const requiredBalance = 0.01 // Minimum SOL required
    if (walletBalance === null || walletBalance < requiredBalance) {
      toast({
        title: "Solde insuffisant",
        description: `Veuillez ajouter au moins ${requiredBalance} SOL à votre portefeuille pour couvrir les frais de transaction.`,
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setCreationProgress(0)
    setCreationStep("Préparation de la création du token...")
    setError(null)
    updateCreationStatus("init", "processing")

    try {
      const values = form.getValues()

      // Préparer les paramètres du token
      const tokenParams = {
        name: values.name,
        symbol: values.symbol,
        decimals: values.decimals,
        initialSupply: values.initialSupply,
        maxSupply: values.maxSupply,
        isMintable: values.isMintable,
        isBurnable: values.isBurnable,
        isPausable: values.isPausable,
        isTransferTaxable: values.isTransferTaxable,
        transferTaxRate: values.transferTaxRate,
        ownerAddress: publicKey.toString(),
        suffix: values.suffix,
        description: values.description,
        website: values.website,
        twitter: values.twitter,
        telegram: values.telegram,
        imageUrl: logoPreview || undefined,
      }

      // Préparer la création du token
      const result = await tokenCreationServiceEnhanced.prepareTokenCreation(tokenParams, (progress) => {
        setCreationProgress(progress.progress)
        setCreationStep(progress.message)

        if (progress.error) {
          setError(progress.error)
          updateCreationStatus("init", "failed")
        }
      })

      if (!result.success || !result.mintKeypair) {
        throw new Error(result.error || "Échec de la préparation du token")
      }

      // Stocker le keypair généré
      setMintKeypair(result.mintKeypair)

      updateCreationStatus("init", "completed")
      updateCreationStatus("address", "completed")

      // Afficher les options de paiement
      setShowPayment(true)
      setActiveStep(5) // Aller à l'étape de paiement
    } catch (err: any) {
      console.error("Erreur lors de la préparation du token:", err)
      setError(err.message || "Une erreur s'est produite lors de la préparation du token")
      updateCreationStatus("init", "failed")

      toast({
        title: "Échec de la préparation",
        description: err.message || "Une erreur s'est produite lors de la préparation du token",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  // Créer le token sur la blockchain
  const handleCreateToken = async () => {
    if (!mintKeypair || !paymentSignature || !publicKey) {
      toast({
        title: "Erreur de création",
        description: "Informations manquantes pour la création du token",
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setCreationProgress(0)
    setCreationStep("Création du token sur la blockchain...")
    setError(null)
    updateCreationStatus("transaction", "processing")

    try {
      const values = form.getValues()

      // Préparer les paramètres du token
      const tokenParams = {
        name: values.name,
        symbol: values.symbol,
        decimals: values.decimals,
        initialSupply: values.initialSupply,
        maxSupply: values.maxSupply,
        isMintable: values.isMintable,
        isBurnable: values.isBurnable,
        isPausable: values.isPausable,
        isTransferTaxable: values.isTransferTaxable,
        transferTaxRate: values.transferTaxRate,
        ownerAddress: publicKey.toString(),
        suffix: values.suffix,
        description: values.description,
        website: values.website,
        twitter: values.twitter,
        telegram: values.telegram,
        imageUrl: logoPreview || undefined,
      }

      // Créer le token
      const result = await tokenCreationServiceEnhanced.createToken(
        tokenParams,
        paymentSignature,
        mintKeypair,
        (progress) => {
          setCreationProgress(progress.progress)
          setCreationStep(progress.message)

          if (progress.error) {
            setError(progress.error)
            updateCreationStatus("transaction", "failed")
          }
        },
      )

      if (!result.success || !result.tokenAddress) {
        throw new Error(result.error || "Échec de la création du token")
      }

      // Stocker l'adresse du token créé
      setCreatedTokenAddress(result.tokenAddress)
      updateCreationStatus("transaction", "completed")

      // Ajouter les métadonnées
      updateCreationStatus("metadata", "processing")
      await tokenCreationServiceEnhanced.addTokenMetadata(
        result.tokenAddress,
        {
          name: values.name,
          symbol: values.symbol,
          description: values.description,
          image: logoPreview || undefined,
          externalUrl: values.website,
          attributes: [
            { trait_type: "Decimals", value: values.decimals.toString() },
            { trait_type: "Initial Supply", value: values.initialSupply.toString() },
            { trait_type: "Mintable", value: values.isMintable ? "Yes" : "No" },
            { trait_type: "Burnable", value: values.isBurnable ? "Yes" : "No" },
          ],
        },
        mintKeypair,
        (progress) => {
          setCreationProgress(progress.progress)
          setCreationStep(progress.message)

          if (progress.error) {
            setError(progress.error)
            updateCreationStatus("metadata", "failed")
          }
        },
      )

      updateCreationStatus("metadata", "completed")

      // Ajouter de la liquidité si demandé
      if (values.addLiquidity) {
        updateCreationStatus("liquidity", "processing")
        await tokenCreationServiceEnhanced.addLiquidity(
          result.tokenAddress,
          values.liquidityAmount,
          values.initialSupply * (values.liquidityPercentage / 100),
          mintKeypair,
          (progress) => {
            setCreationProgress(progress.progress)
            setCreationStep(progress.message)

            if (progress.error) {
              setError(progress.error)
              updateCreationStatus("liquidity", "failed")
            }
          },
        )

        updateCreationStatus("liquidity", "completed")
      } else {
        updateCreationStatus("liquidity", "skipped")
      }

      // Finalisation
      setCreationProgress(100)
      setCreationStep("Token créé avec succès!")
      setSuccess(true)

      toast({
        title: "Token créé avec succès",
        description: `Votre token ${values.name} (${values.symbol}) a été créé avec succès!`,
      })
    } catch (err: any) {
      console.error("Erreur lors de la création du token:", err)
      setError(err.message || "Une erreur s'est produite lors de la création du token")

      toast({
        title: "Échec de la création du token",
        description: err.message || "Une erreur s'est produite lors de la création du token",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  // Gérer la soumission du formulaire
  const onSubmit = async (data: TokenFormValues) => {
    if (activeStep < steps.length - 1) {
      nextStep()
    } else {
      await handlePrepareToken()
    }
  }

  // Si le token est en cours de création, afficher la progression
  if (isCreating) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Création du token en cours</CardTitle>
          <CardDescription>Veuillez patienter pendant que nous créons votre token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium">{creationStep}</h3>
            <Progress value={creationProgress} className="h-2 mt-2" />
          </div>

          <TokenCreationProgress steps={creationSteps} />

          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <h4 className="font-medium">Détails du token</h4>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Nom:</span> {form.getValues("name")}
              </div>
              <div>
                <span className="text-muted-foreground">Symbole:</span> {form.getValues("symbol")}
              </div>
              <div>
                <span className="text-muted-foreground">Décimales:</span> {form.getValues("decimals")}
              </div>
              <div>
                <span className="text-muted-foreground">Offre initiale:</span>{" "}
                {form.getValues("initialSupply").toLocaleString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Si le token a été créé avec succès, afficher les détails
  if (success && createdTokenAddress) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-6 w-6 text-green-500" />
            <CardTitle>Token créé avec succès</CardTitle>
          </div>
          <CardDescription>Votre token a été créé sur la blockchain {activeNetwork?.name || "Solana"}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Félicitations!</AlertTitle>
            <AlertDescription>
              Votre token {form.getValues("name")} ({form.getValues("symbol")}) a été créé avec succès. Vous pouvez
              maintenant le gérer et le promouvoir.
            </AlertDescription>
          </Alert>

          <TokenPreview
            name={form.getValues("name")}
            symbol={form.getValues("symbol")}
            logoUrl={logoPreview}
            address={createdTokenAddress}
            supply={form.getValues("initialSupply")}
            decimals={form.getValues("decimals")}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="default"
              onClick={() => {
                window.open(`https://explorer.solana.com/address/${createdTokenAddress}?cluster=devnet`, "_blank")
              }}
            >
              Voir sur l'explorateur
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                router.push("/token-factory/tokens")
              }}
            >
              Voir tous mes tokens
            </Button>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Prochaines étapes</h3>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <ArrowRight className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">Ajouter de la liquidité</p>
                  <p className="text-sm text-muted-foreground">
                    Créez un pool de liquidité sur un DEX pour permettre l'échange de votre token
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <ArrowRight className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">Promouvoir votre token</p>
                  <p className="text-sm text-muted-foreground">
                    Partagez votre token sur les réseaux sociaux et les communautés crypto
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <ArrowRight className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">Configurer votre tokenomics</p>
                  <p className="text-sm text-muted-foreground">
                    Définissez des règles de vesting et de distribution pour votre token
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button
            className="w-full"
            onClick={() => {
              form.reset()
              setLogoPreview(null)
              setSuccess(false)
              setCreatedTokenAddress(null)
              setActiveStep(0)
              setMintKeypair(null)
              setPaymentSignature(null)
              setShowPayment(false)
              setCreationSteps(creationSteps.map((step) => ({ ...step, status: "pending" })))
            }}
          >
            Créer un autre token
          </Button>
        </CardFooter>
      </Card>
    )
  }

  // Afficher le formulaire de création de token
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Créer un nouveau token</CardTitle>
        <CardDescription>
          Remplissez le formulaire ci-dessous pour créer votre propre token sur {activeNetwork?.name || "Solana"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Stepper currentStep={activeStep} className="mb-8">
            {steps.map((step, index) => (
              <Step
                key={index}
                onClick={() => {
                  // Ne pas permettre d'aller à l'étape de paiement directement
                  if (index === 5 && !showPayment) return
                  setActiveStep(index)
                }}
              >
                <StepTitle>{step.title}</StepTitle>
                <StepDescription>{step.description}</StepDescription>
              </Step>
            ))}
          </Stepper>

          <div className="mt-8">
            {activeStep === 0 && (
              <BasicInfoForm form={form} logoPreview={logoPreview} onLogoChange={handleLogoChange} />
            )}

            {activeStep === 1 && <TokenomicsForm form={form} />}

            {activeStep === 2 && <SecurityForm form={form} />}

            {activeStep === 3 && <LaunchForm form={form} />}

            {activeStep === 4 && <MarketingForm form={form} />}

            {activeStep === 5 && showPayment && (
              <PaymentOptions
                onPaymentComplete={handlePaymentComplete}
                onPaymentError={handlePaymentError}
                price={0.01} // Prix fixe pour la création de token
              />
            )}
          </div>

          <div className="flex justify-between pt-4">
            <Button type="button" variant="outline" onClick={prevStep} disabled={activeStep === 0}>
              Précédent
            </Button>
            {activeStep < steps.length - 1 ? (
              <Button type="button" onClick={nextStep}>
                Suivant
              </Button>
            ) : (
              !showPayment && (
                <Button type="submit" disabled={isCreating || !connected}>
                  {isCreating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Préparation en cours...
                    </>
                  ) : (
                    "Créer le token"
                  )}
                </Button>
              )
            )}
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <div className="text-sm text-muted-foreground">
          {connected ? (
            <>
              Connecté: {publicKey?.toString().slice(0, 6)}...{publicKey?.toString().slice(-4)}
              {walletBalance !== null && <> | Solde: {walletBalance.toFixed(4)} SOL</>}
            </>
          ) : (
            "Veuillez connecter votre portefeuille pour créer un token"
          )}
        </div>
        <div className="text-sm">
          Prix: <span className="font-medium">0.01 SOL</span>
        </div>
      </CardFooter>
    </Card>
  )
}

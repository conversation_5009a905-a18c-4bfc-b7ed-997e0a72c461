"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { formatCurrency, formatTimeAgo, truncateAddress } from "@/lib/utils"
import { useNetwork } from "@/contexts/network-context"
import { ArrowUpRight, ExternalLink, LineChart, Shield, Sparkles } from "lucide-react"

interface Token {
  id: string
  name: string
  symbol: string
  logo: string
  price: number
  change24h: number
  volume24h: number
  marketCap: number
  createdAt: string
  creator: string
  type: "standard" | "advanced" | "quantum" | "bonding" | "ai"
}

interface TokenListProps {
  limit?: number
  filter?: string
}

export function TokenList({ limit, filter }: TokenListProps) {
  const [tokens, setTokens] = useState<Token[]>([])
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const { activeNetwork } = useNetwork()

  useEffect(() => {
    // Simuler le chargement des données
    const fetchTokens = async () => {
      setLoading(true)

      // Simuler un délai d'API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Données fictives pour la démonstration
      const mockTokens: Token[] = [
        {
          id: "token1",
          name: "Solana Quantum",
          symbol: "SOLQ",
          logo: "/abstract-geometric-sq.png",
          price: 0.0458,
          change24h: 12.4,
          volume24h: 245890,
          marketCap: 4580000,
          createdAt: "2023-05-15T10:30:00Z",
          creator: "5tGTWBdvYP7UJGyCPgxSSApkn71Jx6SvRXVXMgBx1Pue",
          type: "quantum",
        },
        {
          id: "token2",
          name: "Global Finance",
          symbol: "GFIN",
          logo: "/abstract-geometric-fg.png",
          price: 0.1245,
          change24h: 8.2,
          volume24h: 189560,
          marketCap: 12450000,
          createdAt: "2023-06-20T14:45:00Z",
          creator: "8ZwzXYrQzEcYrFr8YAjnNbvGYuBtr9rBmNcmBMGFJQQF",
          type: "advanced",
        },
        {
          id: "token3",
          name: "Meme Token",
          symbol: "MEME",
          logo: "/mountain-terrain.png",
          price: 0.0078,
          change24h: -2.4,
          volume24h: 98760,
          marketCap: 780000,
          createdAt: "2023-07-05T09:15:00Z",
          creator: "3ZwzXYrQzEcYrFr8YAjnNbvGYuBtr9rBmNcmBMGFJQQF",
          type: "standard",
        },
        {
          id: "token4",
          name: "Bonding Curve",
          symbol: "BOND",
          logo: "/bc-landscape.png",
          price: 0.0356,
          change24h: 5.8,
          volume24h: 78450,
          marketCap: 3560000,
          createdAt: "2023-08-12T16:20:00Z",
          creator: "2ZwzXYrQzEcYrFr8YAjnNbvGYuBtr9rBmNcmBMGFJQQF",
          type: "bonding",
        },
        {
          id: "token5",
          name: "AI Generated Token",
          symbol: "AIGPT",
          logo: "/abstract-ai-network.png",
          price: 0.0892,
          change24h: 15.6,
          volume24h: 156780,
          marketCap: 8920000,
          createdAt: "2023-09-01T11:10:00Z",
          creator: "1ZwzXYrQzEcYrFr8YAjnNbvGYuBtr9rBmNcmBMGFJQQF",
          type: "ai",
        },
        {
          id: "token6",
          name: "Decentralized Finance",
          symbol: "DEFI",
          logo: "/abstract-df.png",
          price: 0.0567,
          change24h: 3.2,
          volume24h: 112340,
          marketCap: 5670000,
          createdAt: "2023-09-15T13:25:00Z",
          creator: "7ZwzXYrQzEcYrFr8YAjnNbvGYuBtr9rBmNcmBMGFJQQF",
          type: "advanced",
        },
      ]

      // Filtrer les tokens si nécessaire
      let filteredTokens = mockTokens
      if (filter) {
        filteredTokens = mockTokens.filter((token) => token.type === filter)
      }

      // Limiter le nombre de tokens si nécessaire
      if (limit) {
        filteredTokens = filteredTokens.slice(0, limit)
      }

      setTokens(filteredTokens)
      setLoading(false)
    }

    fetchTokens()
  }, [limit, filter, activeNetwork])

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "standard":
        return null
      case "advanced":
        return <Shield className="h-4 w-4 text-green-500" />
      case "quantum":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
            Quantum
          </Badge>
        )
      case "bonding":
        return <LineChart className="h-4 w-4 text-orange-500" />
      case "ai":
        return <Sparkles className="h-4 w-4 text-pink-500" />
      default:
        return null
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(limit || 4)].map((_, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-[120px] mb-2" />
                    <Skeleton className="h-3 w-[80px]" />
                  </div>
                </div>
                <div className="text-right">
                  <Skeleton className="h-4 w-[80px] mb-2" />
                  <Skeleton className="h-3 w-[60px]" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (tokens.length === 0) {
    return (
      <div className="text-center p-8">
        <p>Aucun token trouvé.</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {tokens.map((token) => (
        <Card key={token.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full overflow-hidden bg-muted flex items-center justify-center">
                  <img src={token.logo || "/placeholder.svg"} alt={token.name} className="w-full h-full object-cover" />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{token.name}</h3>
                    {getTypeIcon(token.type)}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>{token.symbol}</span>
                    <span>•</span>
                    <span>{formatTimeAgo(token.createdAt)}</span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium">{formatCurrency(token.price)}</div>
                <div className={`text-sm ${token.change24h >= 0 ? "text-green-500" : "text-red-500"}`}>
                  {token.change24h >= 0 ? "+" : ""}
                  {token.change24h}%
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mt-4 text-sm">
              <div>
                <div className="text-muted-foreground">Volume 24h</div>
                <div>{formatCurrency(token.volume24h)}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Market Cap</div>
                <div>{formatCurrency(token.marketCap)}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Créateur</div>
                <div className="flex items-center gap-1">
                  <span>{truncateAddress(token.creator)}</span>
                  <ExternalLink className="h-3 w-3" />
                </div>
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <Button
                variant="outline"
                size="sm"
                className="text-xs"
                onClick={() => router.push(`/token-factory/token/${token.id}`)}
              >
                Voir les détails
                <ArrowUpRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

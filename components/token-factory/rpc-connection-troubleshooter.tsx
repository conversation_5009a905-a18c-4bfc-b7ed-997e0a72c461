"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, RefreshCw } from "lucide-react"
import { checkRpcConnection, findBestRpcEndpoint, type RpcConnectionStatus } from "@/lib/rpc-connection-utils"
import { envConfig } from "@/lib/env-config"

export default function RpcConnectionTroubleshooter() {
  const [isChecking, setIsChecking] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<RpcConnectionStatus | null>(null)
  const [alternativeEndpoints, setAlternativeEndpoints] = useState<RpcConnectionStatus[]>([])
  const [bestEndpoint, setBestEndpoint] = useState<string | null>(null)

  const checkConnection = async () => {
    setIsChecking(true)
    try {
      // Check current endpoint
      const status = await checkRpcConnection(envConfig.SOLANA_RPC_URL)
      setConnectionStatus(status)

      // Find alternatives if current is not working
      if (!status.connected) {
        const best = await findBestRpcEndpoint()
        setBestEndpoint(best)

        // Check alternatives
        const alternatives: RpcConnectionStatus[] = []
        for (const endpoint of [
          "https://api.devnet.solana.com",
          "https://devnet.genesysgo.net",
          "https://devnet.helius-rpc.com/?api-key=15319106-2ae4-418b-a53a-3c6da6a4ed05",
        ]) {
          if (endpoint !== envConfig.SOLANA_RPC_URL) {
            const altStatus = await checkRpcConnection(endpoint)
            alternatives.push(altStatus)
          }
        }
        setAlternativeEndpoints(alternatives.filter((alt) => alt.connected))
      }
    } catch (error) {
      console.error("Error checking RPC connection:", error)
    } finally {
      setIsChecking(false)
    }
  }

  useEffect(() => {
    checkConnection()
  }, [])

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">RPC Connection Status</h3>
        <Button variant="outline" size="sm" onClick={checkConnection} disabled={isChecking}>
          {isChecking ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Checking...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Check Connection
            </>
          )}
        </Button>
      </div>

      {connectionStatus && (
        <Alert variant={connectionStatus.connected ? "default" : "destructive"}>
          {connectionStatus.connected ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
          <AlertTitle>{connectionStatus.connected ? "Connected" : "Connection Failed"}</AlertTitle>
          <AlertDescription>
            {connectionStatus.connected ? (
              <>
                Successfully connected to {connectionStatus.endpoint}
                {connectionStatus.latency && <> (Latency: {connectionStatus.latency}ms)</>}
              </>
            ) : (
              <>
                Failed to connect to {connectionStatus.endpoint}: {connectionStatus.error}
              </>
            )}
          </AlertDescription>
        </Alert>
      )}

      {!connectionStatus?.connected && alternativeEndpoints.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Working Alternatives:</h4>
          {alternativeEndpoints.map((alt, index) => (
            <Alert key={index} variant="default" className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle>{alt.endpoint}</AlertTitle>
              <AlertDescription>
                Latency: {alt.latency}ms
                <Button
                  variant="link"
                  size="sm"
                  className="ml-2 text-green-600"
                  onClick={() => {
                    navigator.clipboard.writeText(alt.endpoint)
                  }}
                >
                  Copy
                </Button>
              </AlertDescription>
            </Alert>
          ))}
          <p className="text-sm text-muted-foreground mt-2">
            To use an alternative endpoint, update your NEXT_PUBLIC_SOLANA_RPC_URL environment variable.
          </p>
        </div>
      )}

      {!connectionStatus?.connected && alternativeEndpoints.length === 0 && !isChecking && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Working RPC Endpoints Found</AlertTitle>
          <AlertDescription>
            Could not connect to any known Solana RPC endpoints. Please check your internet connection or try again
            later.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

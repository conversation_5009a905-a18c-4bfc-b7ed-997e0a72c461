"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Droplets, Lock, Info } from "lucide-react"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface LiquidityConfigurationProps {
  initialValues?: {
    initialLiquidity: number
    liquidityToken: string
    lockLiquidity: boolean
    lockPeriod: number
    autoLiquidityAddition: boolean
    autoLiquidityPercentage: number
  }
  onChange?: (values: any) => void
}

export function LiquidityConfiguration({ initialValues, onChange }: LiquidityConfigurationProps) {
  const [liquidity, setLiquidity] = useState({
    initialLiquidity: initialValues?.initialLiquidity || 1,
    liquidityToken: initialValues?.liquidityToken || "SOL",
    lockLiquidity: initialValues?.lockLiquidity || true,
    lockPeriod: initialValues?.lockPeriod || 180,
    autoLiquidityAddition: initialValues?.autoLiquidityAddition || false,
    autoLiquidityPercentage: initialValues?.autoLiquidityPercentage || 2,
  })

  const handleChange = (field: string, value: any) => {
    const updatedLiquidity = { ...liquidity, [field]: value }
    setLiquidity(updatedLiquidity)
    if (onChange) {
      onChange(updatedLiquidity)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Droplets className="h-5 w-5" />
          Configuration de la liquidité
        </CardTitle>
        <CardDescription>Définissez les paramètres de liquidité pour votre token</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="initialLiquidity">Liquidité initiale</Label>
            <div className="flex gap-2">
              <Input
                id="initialLiquidity"
                type="number"
                value={liquidity.initialLiquidity}
                onChange={(e) => handleChange("initialLiquidity", Number(e.target.value))}
                min={0.1}
                step={0.1}
              />
              <Select value={liquidity.liquidityToken} onValueChange={(value) => handleChange("liquidityToken", value)}>
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder="Token" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SOL">SOL</SelectItem>
                  <SelectItem value="USDC">USDC</SelectItem>
                  <SelectItem value="USDT">USDT</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <p className="text-xs text-muted-foreground">Montant de liquidité initiale à ajouter au pool de trading</p>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Label htmlFor="lockLiquidity">Verrouiller la liquidité</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[220px] text-xs">
                        Verrouille les tokens de liquidité pour une période définie, ce qui empêche le retrait de la
                        liquidité
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <p className="text-sm text-muted-foreground">Sécurise votre projet en verrouillant la liquidité</p>
            </div>
            <Switch
              id="lockLiquidity"
              checked={liquidity.lockLiquidity}
              onCheckedChange={(checked) => handleChange("lockLiquidity", checked)}
            />
          </div>

          {liquidity.lockLiquidity && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="lockPeriod">Période de verrouillage (jours)</Label>
                <span className="text-sm font-medium">{liquidity.lockPeriod} jours</span>
              </div>
              <Slider
                id="lockPeriod"
                min={30}
                max={365}
                step={30}
                value={[liquidity.lockPeriod]}
                onValueChange={(value) => handleChange("lockPeriod", value[0])}
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>30 jours</span>
                <span>180 jours</span>
                <span>365 jours</span>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Label htmlFor="autoLiquidityAddition">Ajout automatique de liquidité</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[220px] text-xs">
                        Ajoute automatiquement une partie des taxes collectées à la pool de liquidité
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <p className="text-sm text-muted-foreground">Augmente progressivement la liquidité du token</p>
            </div>
            <Switch
              id="autoLiquidityAddition"
              checked={liquidity.autoLiquidityAddition}
              onCheckedChange={(checked) => handleChange("autoLiquidityAddition", checked)}
            />
          </div>

          {liquidity.autoLiquidityAddition && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="autoLiquidityPercentage">Pourcentage des taxes (%)</Label>
                <span className="text-sm font-medium">{liquidity.autoLiquidityPercentage}%</span>
              </div>
              <Slider
                id="autoLiquidityPercentage"
                min={1}
                max={10}
                step={0.5}
                value={[liquidity.autoLiquidityPercentage]}
                onValueChange={(value) => handleChange("autoLiquidityPercentage", value[0])}
              />
              <p className="text-xs text-muted-foreground">
                Pourcentage des taxes qui sera automatiquement ajouté à la liquidité
              </p>
            </div>
          )}
        </div>

        <div className="p-4 bg-muted rounded-lg flex items-start">
          <Lock className="h-5 w-5 mr-3 text-muted-foreground shrink-0 mt-0.5" />
          <div>
            <p className="text-sm font-medium">Sécurité de la liquidité</p>
            <p className="text-xs text-muted-foreground mt-1">
              Le verrouillage de la liquidité est une pratique recommandée qui renforce la confiance des investisseurs.
              Les tokens de liquidité seront verrouillés dans un contrat sécurisé pendant la période spécifiée.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

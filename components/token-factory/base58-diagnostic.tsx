"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, Info } from "lucide-react"
import { isValidBase58, findInvalidBase58Char, sanitizeToBase58 } from "@/lib/input-sanitizer"

export default function Base58Diagnostic() {
  const [input, setInput] = useState("")
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean
    message: string
    sanitized?: string
    invalidChar?: string
    position?: number
  } | null>(null)

  const validateInput = () => {
    if (!input) {
      setValidationResult({
        isValid: false,
        message: "Veuillez entrer une valeur à valider",
      })
      return
    }

    const isValid = isValidBase58(input)

    if (isValid) {
      setValidationResult({
        isValid: true,
        message: "La valeur est valide en base58",
      })
    } else {
      const invalidChar = findInvalidBase58Char(input)
      const sanitized = sanitizeToBase58(input)

      setValidationResult({
        isValid: false,
        message: invalidChar
          ? `Caractère non-base58 '${invalidChar.char}' à la position ${invalidChar.position + 1}`
          : "La valeur contient des caractères non valides en base58",
        sanitized: sanitized,
        invalidChar: invalidChar?.char,
        position: invalidChar?.position,
      })
    }
  }

  const applySanitized = () => {
    if (validationResult?.sanitized) {
      setInput(validationResult.sanitized)
      setValidationResult({
        isValid: true,
        message: "Valeur corrigée appliquée",
      })
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Diagnostic Base58</CardTitle>
        <CardDescription>
          Vérifiez si votre texte est valide en base58 (utilisé pour les adresses et symboles Solana)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="base58Input">Texte à valider</Label>
          <Input
            id="base58Input"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Entrez le texte à valider"
          />
          <p className="text-xs text-muted-foreground">
            Base58 utilise uniquement: 123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz
          </p>
          <p className="text-xs text-muted-foreground">
            <strong>Caractères exclus:</strong> 0 (zéro), O (o majuscule), I (i majuscule), l (L minuscule)
          </p>
        </div>

        {validationResult && (
          <Alert variant={validationResult.isValid ? "default" : "destructive"}>
            {validationResult.isValid ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
            <AlertTitle>{validationResult.isValid ? "Valide" : "Non valide"}</AlertTitle>
            <AlertDescription>
              {validationResult.message}

              {validationResult.sanitized && (
                <div className="mt-2">
                  <p>Version corrigée:</p>
                  <code className="bg-muted p-1 rounded">{validationResult.sanitized}</code>
                  <Button variant="outline" size="sm" className="mt-2" onClick={applySanitized}>
                    Appliquer la correction
                  </Button>
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Pourquoi est-ce important?</AlertTitle>
          <AlertDescription>
            <p>
              Solana utilise l'encodage base58 pour les adresses et certaines données. Les caractères non-base58
              provoqueront l'erreur "Non-base58 character" lors de la création de tokens ou d'autres opérations.
            </p>
            <p className="mt-2">
              <strong>Erreurs courantes:</strong>
            </p>
            <ul className="list-disc pl-5 mt-1">
              <li>Utiliser "0" (zéro) au lieu de "o" (o minuscule)</li>
              <li>Utiliser "O" (O majuscule) au lieu de "o" (o minuscule)</li>
              <li>Utiliser "I" (I majuscule) au lieu de "i" (i minuscule)</li>
              <li>Utiliser "l" (L minuscule) au lieu de "1" (un)</li>
              <li>Inclure des espaces ou des caractères spéciaux</li>
            </ul>
          </AlertDescription>
        </Alert>
      </CardContent>
      <CardFooter>
        <Button onClick={validateInput} className="w-full">
          Valider
        </Button>
      </CardFooter>
    </Card>
  )
}

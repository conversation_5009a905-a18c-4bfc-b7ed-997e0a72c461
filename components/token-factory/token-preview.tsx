import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface TokenPreviewProps {
  name: string
  symbol: string
  logoUrl?: string | null
  description?: string
  address?: string
  supply?: number
  decimals?: number
}

export function TokenPreview({
  name,
  symbol,
  logoUrl,
  description,
  address,
  supply = 1000000000,
  decimals = 9,
}: TokenPreviewProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3">
        <div className="w-16 h-16 rounded-full overflow-hidden border flex items-center justify-center bg-muted">
          {logoUrl ? (
            <img src={logoUrl || "/placeholder.svg"} alt={name} className="w-full h-full object-cover" />
          ) : (
            <div className="text-2xl font-bold text-muted-foreground">{symbol.substring(0, 2)}</div>
          )}
        </div>
        <div>
          <h3 className="text-xl font-bold">{name}</h3>
          <div className="flex items-center gap-2">
            <Badge variant="outline">{symbol}</Badge>
            {address && (
              <span className="text-xs text-muted-foreground">
                {address.substring(0, 6)}...{address.substring(address.length - 4)}
              </span>
            )}
          </div>
        </div>
      </div>

      {description && (
        <>
          <Separator />
          <div>
            <h4 className="text-sm font-medium mb-1">Description</h4>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
        </>
      )}

      <Separator />

      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="text-sm font-medium">Offre totale</h4>
          <p className="text-sm">{supply.toLocaleString()}</p>
        </div>
        <div>
          <h4 className="text-sm font-medium">Décimales</h4>
          <p className="text-sm">{decimals}</p>
        </div>
      </div>

      <Card className="bg-muted/50">
        <CardContent className="p-3">
          <div className="text-xs text-muted-foreground">
            <p className="font-medium mb-1">Fonctionnalités de sécurité incluses:</p>
            <ul className="list-disc list-inside space-y-0.5">
              <li>Protection anti-bot</li>
              <li>Limites de transaction</li>
              <li>Mécanisme anti-dump</li>
              <li>Bonding curve automatique</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { Loader2 } from "lucide-react"

const tokenFormSchema = z.object({
  name: z.string().min(3, {
    message: "Le nom du token doit contenir au moins 3 caractères.",
  }),
  symbol: z
    .string()
    .min(2, {
      message: "Le symbole du token doit contenir au moins 2 caractères.",
    })
    .max(10, {
      message: "Le symbole du token ne doit pas dépasser 10 caractères.",
    }),
  decimals: z.number().int().min(0).max(18),
  totalSupply: z.number().positive({
    message: "L'offre totale doit être un nombre positif.",
  }),
  description: z.string().optional(),
  website: z
    .string()
    .url({
      message: "Veuillez entrer une URL valide.",
    })
    .optional()
    .or(z.literal("")),
  twitter: z
    .string()
    .url({
      message: "Veuillez entrer une URL valide.",
    })
    .optional()
    .or(z.literal("")),
  telegram: z
    .string()
    .url({
      message: "Veuillez entrer une URL valide.",
    })
    .optional()
    .or(z.literal("")),
  burnPercentage: z.number().min(0).max(10),
  liquidityPercentage: z.number().min(0).max(100),
  lockLiquidity: z.boolean(),
  lockDuration: z.number().min(1).max(365),
})

type TokenFormValues = z.infer<typeof tokenFormSchema>

const defaultValues: Partial<TokenFormValues> = {
  name: "",
  symbol: "",
  decimals: 9,
  totalSupply: 1000000000,
  description: "",
  website: "",
  twitter: "",
  telegram: "",
  burnPercentage: 0,
  liquidityPercentage: 50,
  lockLiquidity: true,
  lockDuration: 180,
}

export function TokenCreator() {
  const router = useRouter()
  const { toast } = useToast()
  const [isCreating, setIsCreating] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")

  const form = useForm<TokenFormValues>({
    resolver: zodResolver(tokenFormSchema),
    defaultValues,
    mode: "onChange",
  })

  async function onSubmit(data: TokenFormValues) {
    setIsCreating(true)

    try {
      // Dans une implémentation réelle, vous feriez un appel API ici
      console.log("Création du token avec les données:", data)
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Token créé avec succès",
        description: `Le token ${data.name} (${data.symbol}) a été créé avec succès.`,
      })

      // Rediriger vers la page de succès
      router.push("/token-factory/success")
    } catch (error) {
      console.error("Erreur lors de la création du token:", error)
      toast({
        title: "Erreur",
        description: "Une erreur s'est produite lors de la création du token. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Créer un nouveau token</CardTitle>
        <CardDescription>
          Configurez les paramètres de votre token et déployez-le sur la blockchain Solana.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="basic">Informations de base</TabsTrigger>
            <TabsTrigger value="advanced">Paramètres avancés</TabsTrigger>
            <TabsTrigger value="social">Informations sociales</TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <TabsContent value="basic" className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nom du token</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Solana Platform" {...field} />
                      </FormControl>
                      <FormDescription>Le nom complet de votre token.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="symbol"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Symbole du token</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: SOLP" {...field} />
                      </FormControl>
                      <FormDescription>Le symbole court de votre token (généralement 3-5 caractères).</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="decimals"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Décimales</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            max={18}
                            {...field}
                            onChange={(e) => field.onChange(Number.parseInt(e.target.value))}
                          />
                        </FormControl>
                        <FormDescription>Nombre de décimales (généralement 9 pour Solana).</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="totalSupply"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Offre totale</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            {...field}
                            onChange={(e) => field.onChange(Number.parseFloat(e.target.value))}
                          />
                        </FormControl>
                        <FormDescription>Nombre total de tokens à créer.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Décrivez votre token et son utilité..."
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>Une brève description de votre token et de son utilité.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="advanced" className="space-y-6">
                <FormField
                  control={form.control}
                  name="burnPercentage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pourcentage de burn ({field.value}%)</FormLabel>
                      <FormControl>
                        <Slider
                          min={0}
                          max={10}
                          step={0.1}
                          value={[field.value]}
                          onValueChange={(values) => field.onChange(values[0])}
                        />
                      </FormControl>
                      <FormDescription>Pourcentage de tokens à brûler lors des transactions (0-10%).</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="liquidityPercentage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pourcentage de liquidité ({field.value}%)</FormLabel>
                      <FormControl>
                        <Slider
                          min={0}
                          max={100}
                          step={1}
                          value={[field.value]}
                          onValueChange={(values) => field.onChange(values[0])}
                        />
                      </FormControl>
                      <FormDescription>
                        Pourcentage de l'offre totale à allouer à la liquidité initiale.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="lockLiquidity"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Verrouiller la liquidité</FormLabel>
                          <FormDescription>Verrouiller la liquidité pour une période définie.</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch("lockLiquidity") && (
                    <FormField
                      control={form.control}
                      name="lockDuration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Durée de verrouillage (jours)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={365}
                              {...field}
                              onChange={(e) => field.onChange(Number.parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>Durée de verrouillage de la liquidité en jours.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </TabsContent>

              <TabsContent value="social" className="space-y-6">
                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Site web</FormLabel>
                      <FormControl>
                        <Input placeholder="https://votre-site.com" {...field} />
                      </FormControl>
                      <FormDescription>URL du site web de votre projet.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="twitter"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Twitter</FormLabel>
                      <FormControl>
                        <Input placeholder="https://twitter.com/votre-compte" {...field} />
                      </FormControl>
                      <FormDescription>URL de votre compte Twitter.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="telegram"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telegram</FormLabel>
                      <FormControl>
                        <Input placeholder="https://t.me/votre-groupe" {...field} />
                      </FormControl>
                      <FormDescription>URL de votre groupe Telegram.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <div className="flex justify-between">
                {activeTab !== "basic" && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      if (activeTab === "advanced") setActiveTab("basic")
                      if (activeTab === "social") setActiveTab("advanced")
                    }}
                  >
                    Précédent
                  </Button>
                )}

                {activeTab !== "social" ? (
                  <Button
                    type="button"
                    onClick={() => {
                      if (activeTab === "basic") setActiveTab("advanced")
                      if (activeTab === "advanced") setActiveTab("social")
                    }}
                    className="ml-auto"
                  >
                    Suivant
                  </Button>
                ) : (
                  <Button type="submit" disabled={isCreating} className="ml-auto">
                    {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Créer le token
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <p className="text-sm text-muted-foreground">
          Des frais de création peuvent s'appliquer en fonction de la configuration du token.
        </p>
      </CardFooter>
    </Card>
  )
}

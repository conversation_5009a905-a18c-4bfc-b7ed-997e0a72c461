"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { useNetwork } from "@/contexts/network-context"
import { formatNumber, formatCurrency } from "@/lib/utils"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from "recharts"

interface TokenFactoryStats {
  totalTokens: number
  totalVolume: number
  activeUsers: number
  tokensThisMonth: number
  tokensByType: {
    standard: number
    advanced: number
    quantum: number
    bonding: number
    ai: number
  }
  volumeByDay: Array<{
    date: string
    volume: number
  }>
}

export function TokenFactoryStats() {
  const [stats, setStats] = useState<TokenFactoryStats | null>(null)
  const [loading, setLoading] = useState(true)
  const { activeNetwork } = useNetwork()

  useEffect(() => {
    // Simuler le chargement des données
    const fetchStats = async () => {
      setLoading(true)

      // Simuler un délai d'API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Données fictives pour la démonstration
      const mockStats: TokenFactoryStats = {
        totalTokens: 1245,
        totalVolume: 2800000,
        activeUsers: 5678,
        tokensThisMonth: 142,
        tokensByType: {
          standard: 580,
          advanced: 420,
          quantum: 150,
          bonding: 65,
          ai: 30,
        },
        volumeByDay: [
          { date: "Lun", volume: 120000 },
          { date: "Mar", volume: 180000 },
          { date: "Mer", volume: 150000 },
          { date: "Jeu", volume: 210000 },
          { date: "Ven", volume: 250000 },
          { date: "Sam", volume: 190000 },
          { date: "Dim", volume: 160000 },
        ],
      }

      setStats(mockStats)
      setLoading(false)
    }

    fetchStats()
  }, [activeNetwork])

  const pieData = stats
    ? [
        { name: "Standard", value: stats.tokensByType.standard, color: "#3b82f6" },
        { name: "Avancé", value: stats.tokensByType.advanced, color: "#10b981" },
        { name: "Quantum", value: stats.tokensByType.quantum, color: "#8b5cf6" },
        { name: "Bonding", value: stats.tokensByType.bonding, color: "#f97316" },
        { name: "AI", value: stats.tokensByType.ai, color: "#ec4899" },
      ]
    : []

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="h-4 w-[100px] mb-2" />
            <Skeleton className="h-8 w-[120px] mb-4" />
            <Skeleton className="h-3 w-[80px]" />
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="h-4 w-[100px] mb-2" />
            <Skeleton className="h-8 w-[120px] mb-4" />
            <Skeleton className="h-3 w-[80px]" />
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="h-4 w-[100px] mb-2" />
            <Skeleton className="h-8 w-[120px] mb-4" />
            <Skeleton className="h-3 w-[80px]" />
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="h-4 w-[100px] mb-2" />
            <Skeleton className="h-8 w-[120px] mb-4" />
            <Skeleton className="h-3 w-[80px]" />
          </CardContent>
        </Card>
        <Card className="md:col-span-2">
          <CardContent className="pt-6">
            <Skeleton className="h-4 w-[150px] mb-4" />
            <Skeleton className="h-[200px] w-full" />
          </CardContent>
        </Card>
        <Card className="md:col-span-2">
          <CardContent className="pt-6">
            <Skeleton className="h-4 w-[150px] mb-4" />
            <Skeleton className="h-[200px] w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="text-center p-8">
        <p>Impossible de charger les statistiques. Veuillez réessayer plus tard.</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-muted-foreground">Total des tokens créés</div>
            <div className="text-3xl font-bold mt-1">{formatNumber(stats.totalTokens)}</div>
            <div className="text-xs text-green-500 mt-1">+{stats.tokensThisMonth} ce mois-ci</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-muted-foreground">Volume total</div>
            <div className="text-3xl font-bold mt-1">{formatCurrency(stats.totalVolume)}</div>
            <div className="text-xs text-green-500 mt-1">+8% ce mois-ci</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-muted-foreground">Utilisateurs actifs</div>
            <div className="text-3xl font-bold mt-1">{formatNumber(stats.activeUsers)}</div>
            <div className="text-xs text-green-500 mt-1">+15% ce mois-ci</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-muted-foreground">Réseau actif</div>
            <div className="text-3xl font-bold mt-1">{activeNetwork?.name || "Solana"}</div>
            <div className="text-xs text-muted-foreground mt-1">{activeNetwork?.id || "mainnet"}</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium mb-4">Volume hebdomadaire</h3>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={stats.volumeByDay}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                  <Tooltip formatter={(value) => [`$${formatNumber(value as number)}`, "Volume"]} />
                  <Bar dataKey="volume" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium mb-4">Types de tokens</h3>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} tokens`, ""]} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-medium mb-4">Distribution par type de token</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm">Standard</span>
                <span className="text-sm text-muted-foreground">{stats.tokensByType.standard} tokens</span>
              </div>
              <Progress value={(stats.tokensByType.standard / stats.totalTokens) * 100} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm">Avancé</span>
                <span className="text-sm text-muted-foreground">{stats.tokensByType.advanced} tokens</span>
              </div>
              <Progress value={(stats.tokensByType.advanced / stats.totalTokens) * 100} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm">Quantum</span>
                <span className="text-sm text-muted-foreground">{stats.tokensByType.quantum} tokens</span>
              </div>
              <Progress value={(stats.tokensByType.quantum / stats.totalTokens) * 100} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm">Bonding Curve</span>
                <span className="text-sm text-muted-foreground">{stats.tokensByType.bonding} tokens</span>
              </div>
              <Progress value={(stats.tokensByType.bonding / stats.totalTokens) * 100} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm">AI Creator</span>
                <span className="text-sm text-muted-foreground">{stats.tokensByType.ai} tokens</span>
              </div>
              <Progress value={(stats.tokensByType.ai / stats.totalTokens) * 100} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

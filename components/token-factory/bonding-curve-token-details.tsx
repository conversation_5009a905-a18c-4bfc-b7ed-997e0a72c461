"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { BondingCurveChart } from "@/components/token-factory/bonding-curve-chart"
import { BondingCurveService, type BondingCurveParams } from "@/lib/bonding-curve-service"
import { ArrowUpRight, ArrowDownRight, RefreshCw, TrendingUp, Wallet } from "lucide-react"

interface TokenDetails {
  name: string
  symbol: string
  address: string
  initialPrice: number
  currentPrice: number
  initialSupply: number
  currentSupply: number
  maxSupply: number
  reserveRatio: number
  reserveBalance: number
  holders: number
  marketCap: number
}

interface BondingCurveTokenDetailsProps {
  tokenAddress: string
}

export function BondingCurveTokenDetails({ tokenAddress }: BondingCurveTokenDetailsProps) {
  const [token, setToken] = useState<TokenDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [buyAmount, setBuyAmount] = useState("")
  const [sellAmount, setSellAmount] = useState("")
  const [buyEstimate, setBuyEstimate] = useState({ cost: 0, newPrice: 0 })
  const [sellEstimate, setSellEstimate] = useState({ return: 0, newPrice: 0 })
  const [activeTab, setActiveTab] = useState("overview")

  // Simuler le chargement des détails du token
  useEffect(() => {
    const fetchTokenDetails = async () => {
      try {
        // Simuler une requête API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données fictives pour la démonstration
        const mockToken: TokenDetails = {
          name: "Global Finance Quantum",
          symbol: "GFQ",
          address: tokenAddress,
          initialPrice: 0.001,
          currentPrice: 0.00125,
          initialSupply: 1000000,
          currentSupply: 1200000,
          maxSupply: 10000000,
          reserveRatio: 0.2,
          reserveBalance: 300,
          holders: 42,
          marketCap: 1500,
        }

        setToken(mockToken)
      } catch (error) {
        console.error("Erreur lors du chargement des détails du token:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchTokenDetails()
  }, [tokenAddress])

  // Calculer l'estimation d'achat
  useEffect(() => {
    if (!token || !buyAmount || isNaN(Number.parseFloat(buyAmount))) {
      setBuyEstimate({ cost: 0, newPrice: 0 })
      return
    }

    const amount = Number.parseFloat(buyAmount)
    if (amount <= 0) {
      setBuyEstimate({ cost: 0, newPrice: 0 })
      return
    }

    const params: BondingCurveParams = {
      initialPrice: token.initialPrice,
      reserveRatio: token.reserveRatio,
      initialSupply: token.initialSupply,
      maxSupply: token.maxSupply,
      currentSupply: token.currentSupply,
      reserveBalance: token.reserveBalance,
    }

    try {
      const cost = BondingCurveService.calculatePurchaseCost(params, amount)
      const newSupply = token.currentSupply + amount
      const newPrice = BondingCurveService.calculatePriceAtSupply(params, newSupply)

      setBuyEstimate({
        cost,
        newPrice,
      })
    } catch (error) {
      console.error("Erreur lors du calcul de l'estimation d'achat:", error)
      setBuyEstimate({ cost: 0, newPrice: 0 })
    }
  }, [token, buyAmount])

  // Calculer l'estimation de vente
  useEffect(() => {
    if (!token || !sellAmount || isNaN(Number.parseFloat(sellAmount))) {
      setSellEstimate({ return: 0, newPrice: 0 })
      return
    }

    const amount = Number.parseFloat(sellAmount)
    if (amount <= 0 || amount > token.currentSupply) {
      setSellEstimate({ return: 0, newPrice: 0 })
      return
    }

    const params: BondingCurveParams = {
      initialPrice: token.initialPrice,
      reserveRatio: token.reserveRatio,
      initialSupply: token.initialSupply,
      maxSupply: token.maxSupply,
      currentSupply: token.currentSupply,
      reserveBalance: token.reserveBalance,
    }

    try {
      const returnAmount = BondingCurveService.calculateSaleReturn(params, amount)
      const newSupply = token.currentSupply - amount
      const newPrice = BondingCurveService.calculatePriceAtSupply(params, newSupply)

      setSellEstimate({
        return: returnAmount,
        newPrice,
      })
    } catch (error) {
      console.error("Erreur lors du calcul de l'estimation de vente:", error)
      setSellEstimate({ return: 0, newPrice: 0 })
    }
  }, [token, sellAmount])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (!token) {
    return (
      <div className="text-center p-8">
        <h2 className="text-xl font-semibold">Token non trouvé</h2>
        <p className="text-muted-foreground">
          Le token avec l'adresse {tokenAddress} n'a pas été trouvé ou n'est pas un token avec Bonding Curve.
        </p>
      </div>
    )
  }

  // Préparer les paramètres pour la courbe de liaison
  const bondingCurveParams: BondingCurveParams = {
    initialPrice: token.initialPrice,
    reserveRatio: token.reserveRatio,
    initialSupply: token.initialSupply,
    maxSupply: token.maxSupply,
    currentSupply: token.currentSupply,
    reserveBalance: token.reserveBalance,
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">
            {token.name} <span className="text-muted-foreground">({token.symbol})</span>
          </h1>
          <p className="text-sm text-muted-foreground break-all">{token.address}</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm font-medium flex items-center">
            <TrendingUp className="h-4 w-4 mr-1" />
            {token.currentPrice.toFixed(6)} SOL
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Courbe de liaison</CardTitle>
            <CardDescription>Visualisation de l'évolution du prix en fonction de l'offre</CardDescription>
          </CardHeader>
          <CardContent>
            <BondingCurveChart params={bondingCurveParams} />
          </CardContent>
        </Card>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Statistiques</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Prix actuel</p>
                  <p className="font-medium">{token.currentPrice.toFixed(6)} SOL</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Offre actuelle</p>
                  <p className="font-medium">{token.currentSupply.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Offre maximale</p>
                  <p className="font-medium">{token.maxSupply.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Capitalisation</p>
                  <p className="font-medium">{token.marketCap.toLocaleString()} SOL</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Réserve</p>
                  <p className="font-medium">{token.reserveBalance.toLocaleString()} SOL</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Détenteurs</p>
                  <p className="font-medium">{token.holders}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Acheter / Vendre</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="buy">
                <TabsList className="grid grid-cols-2 mb-4">
                  <TabsTrigger value="buy">Acheter</TabsTrigger>
                  <TabsTrigger value="sell">Vendre</TabsTrigger>
                </TabsList>

                <TabsContent value="buy" className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Montant à acheter</label>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        placeholder="0"
                        value={buyAmount}
                        onChange={(e) => setBuyAmount(e.target.value)}
                      />
                      <Button variant="outline" size="sm" className="whitespace-nowrap">
                        Max
                      </Button>
                    </div>
                  </div>

                  {Number.parseFloat(buyAmount) > 0 && (
                    <div className="p-3 bg-blue-50 rounded-md space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-700">Coût estimé:</span>
                        <span className="font-medium text-blue-800">{buyEstimate.cost.toFixed(6)} SOL</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-700">Nouveau prix:</span>
                        <span className="font-medium text-blue-800">{buyEstimate.newPrice.toFixed(6)} SOL</span>
                      </div>
                    </div>
                  )}

                  <Button className="w-full" disabled={!Number.parseFloat(buyAmount)}>
                    <ArrowUpRight className="h-4 w-4 mr-2" />
                    Acheter
                  </Button>
                </TabsContent>

                <TabsContent value="sell" className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Montant à vendre</label>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        placeholder="0"
                        value={sellAmount}
                        onChange={(e) => setSellAmount(e.target.value)}
                      />
                      <Button variant="outline" size="sm" className="whitespace-nowrap">
                        Max
                      </Button>
                    </div>
                  </div>

                  {Number.parseFloat(sellAmount) > 0 && (
                    <div className="p-3 bg-blue-50 rounded-md space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-700">Retour estimé:</span>
                        <span className="font-medium text-blue-800">{sellEstimate.return.toFixed(6)} SOL</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-700">Nouveau prix:</span>
                        <span className="font-medium text-blue-800">{sellEstimate.newPrice.toFixed(6)} SOL</span>
                      </div>
                    </div>
                  )}

                  <Button className="w-full" variant="destructive" disabled={!Number.parseFloat(sellAmount)}>
                    <ArrowDownRight className="h-4 w-4 mr-2" />
                    Vendre
                  </Button>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-8">
        <TabsList className="grid grid-cols-3 w-full max-w-md">
          <TabsTrigger value="overview">Aperçu</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="holders">Détenteurs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>À propos de {token.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>
                  {token.name} ({token.symbol}) est un token utilisant un mécanisme de courbe de liaison (Bonding Curve)
                  sur la blockchain Solana. Ce mécanisme détermine automatiquement le prix du token en fonction de
                  l'offre en circulation.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-medium">Comment fonctionne la courbe de liaison?</h3>
                    <p className="text-sm text-muted-foreground">
                      Lorsque quelqu'un achète des tokens, l'offre augmente et le prix monte selon la formule de la
                      courbe. À l'inverse, lorsque quelqu'un vend des tokens, l'offre diminue et le prix baisse.
                    </p>

                    <h3 className="font-medium">Avantages de ce mécanisme</h3>
                    <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
                      <li>Liquidité garantie à tout moment</li>
                      <li>Prix déterminé de manière transparente et prévisible</li>
                      <li>Récompense les premiers investisseurs</li>
                      <li>Résistance aux manipulations de marché</li>
                    </ul>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-medium">Paramètres de la courbe</h3>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="text-muted-foreground">Prix initial</p>
                        <p>{token.initialPrice.toFixed(6)} SOL</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Ratio de réserve</p>
                        <p>{(token.reserveRatio * 100).toFixed(0)}%</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Offre initiale</p>
                        <p>{token.initialSupply.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Offre maximale</p>
                        <p>{token.maxSupply.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Transactions récentes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Transactions fictives pour la démonstration */}
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {Math.random() > 0.5 ? (
                        <div className="p-2 bg-green-100 rounded-full">
                          <ArrowUpRight className="h-4 w-4 text-green-600" />
                        </div>
                      ) : (
                        <div className="p-2 bg-red-100 rounded-full">
                          <ArrowDownRight className="h-4 w-4 text-red-600" />
                        </div>
                      )}
                      <div>
                        <p className="font-medium">
                          {Math.random() > 0.5 ? "Achat" : "Vente"} de {Math.floor(Math.random() * 10000)} tokens
                        </p>
                        <p className="text-xs text-muted-foreground">Il y a {Math.floor(Math.random() * 60)} minutes</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{(Math.random() * 10).toFixed(3)} SOL</p>
                      <p className="text-xs text-muted-foreground">Prix: {(Math.random() * 0.01).toFixed(6)} SOL</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="holders" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Principaux détenteurs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Détenteurs fictifs pour la démonstration */}
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-full">
                        <Wallet className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">
                          {`${i}. `}
                          {`Wallet${i}`.padEnd(8, "x")}...{Math.random().toString(36).substring(2, 6)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Depuis {Math.floor(Math.random() * 30) + 1} jours
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {Math.floor(Math.random() * 100000).toLocaleString()} {token.symbol}
                      </p>
                      <p className="text-xs text-muted-foreground">{(Math.random() * 10).toFixed(2)}% de l'offre</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

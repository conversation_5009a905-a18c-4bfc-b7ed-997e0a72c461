"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Shield, Lock, AlertTriangle, Info } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface SecurityFeaturesConfigurationProps {
  initialValues?: {
    antiBot: boolean
    antiDump: boolean
    maxTransactionAmount: number
    maxWalletAmount: number
    tradingCooldown: number
    blacklist: boolean
    transferPause: boolean
    renounceOwnership: boolean
  }
  onChange?: (values: any) => void
}

export function SecurityFeaturesConfiguration({ initialValues, onChange }: SecurityFeaturesConfigurationProps) {
  const [security, setSecurity] = useState({
    antiBot: initialValues?.antiBot || false,
    antiDump: initialValues?.antiDump || false,
    maxTransactionAmount: initialValues?.maxTransactionAmount || 2,
    maxWalletAmount: initialValues?.maxWalletAmount || 3,
    tradingCooldown: initialValues?.tradingCooldown || 60,
    blacklist: initialValues?.blacklist || false,
    transferPause: initialValues?.transferPause || false,
    renounceOwnership: initialValues?.renounceOwnership || false,
  })

  const handleChange = (field: string, value: any) => {
    const updatedSecurity = { ...security, [field]: value }
    setSecurity(updatedSecurity)
    if (onChange) {
      onChange(updatedSecurity)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Protection contre les bots et les dumps
          </CardTitle>
          <CardDescription>Configurez les mécanismes de protection contre les manipulations</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="antiBot">Protection anti-bot</Label>
              <p className="text-xs text-muted-foreground">Empêche les bots d'acheter votre token lors du lancement</p>
            </div>
            <Switch
              id="antiBot"
              checked={security.antiBot}
              onCheckedChange={(checked) => handleChange("antiBot", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="antiDump">Protection anti-dump</Label>
              <p className="text-xs text-muted-foreground">
                Limite les ventes massives pour éviter les chutes de prix brutales
              </p>
            </div>
            <Switch
              id="antiDump"
              checked={security.antiDump}
              onCheckedChange={(checked) => handleChange("antiDump", checked)}
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="tradingCooldown">Période de cooldown (secondes)</Label>
              <div className="w-24">
                <Input
                  id="tradingCooldown"
                  type="number"
                  value={security.tradingCooldown}
                  onChange={(e) => handleChange("tradingCooldown", Number.parseInt(e.target.value) || 0)}
                  min={0}
                />
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Délai minimum (en secondes) entre deux transactions pour un même portefeuille
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Limites de transaction
          </CardTitle>
          <CardDescription>Définissez des limites pour protéger votre token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="maxTransactionAmount">
              Montant maximum par transaction ({security.maxTransactionAmount}%)
            </Label>
            <Slider
              id="maxTransactionAmount"
              min={0.1}
              max={10}
              step={0.1}
              value={[security.maxTransactionAmount]}
              onValueChange={(value) => handleChange("maxTransactionAmount", value[0])}
            />
            <p className="text-xs text-muted-foreground">
              Pourcentage maximum de l'offre totale pouvant être échangé en une seule transaction
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxWalletAmount">Montant maximum par portefeuille ({security.maxWalletAmount}%)</Label>
            <Slider
              id="maxWalletAmount"
              min={0.1}
              max={10}
              step={0.1}
              value={[security.maxWalletAmount]}
              onValueChange={(value) => handleChange("maxWalletAmount", value[0])}
            />
            <p className="text-xs text-muted-foreground">
              Pourcentage maximum de l'offre totale pouvant être détenu par un seul portefeuille
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="h-5 w-5" />
            Fonctions administratives
          </CardTitle>
          <CardDescription>Configurez les fonctions de contrôle du token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Label htmlFor="blacklist">Fonction de blacklist</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[220px] text-xs">
                        Permet de bloquer les adresses malveillantes pour les empêcher d'effectuer des transactions
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <p className="text-xs text-muted-foreground">Permet de bloquer les adresses malveillantes</p>
            </div>
            <Switch
              id="blacklist"
              checked={security.blacklist}
              onCheckedChange={(checked) => handleChange("blacklist", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Label htmlFor="transferPause">Pause des transferts</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[220px] text-xs">
                        Permet de suspendre temporairement tous les transferts en cas d'urgence
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <p className="text-xs text-muted-foreground">Permet de suspendre les transferts en cas d'urgence</p>
            </div>
            <Switch
              id="transferPause"
              checked={security.transferPause}
              onCheckedChange={(checked) => handleChange("transferPause", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center gap-2">
                <Label htmlFor="renounceOwnership">Renoncer à la propriété</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[220px] text-xs">
                        Renonce définitivement aux droits administratifs après le lancement
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <p className="text-xs text-muted-foreground">
                Renonce définitivement aux droits administratifs (irréversible)
              </p>
            </div>
            <Switch
              id="renounceOwnership"
              checked={security.renounceOwnership}
              onCheckedChange={(checked) => handleChange("renounceOwnership", checked)}
            />
          </div>

          {(security.blacklist || security.transferPause) && security.renounceOwnership && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Attention : Si vous renoncez à la propriété, vous ne pourrez plus utiliser les fonctions de blacklist ou
                de pause des transferts.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON>lider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, Info, AlertTriangle } from "lucide-react"

interface SecurityConfigurationProps {
  isMintable: boolean
  setIsMintable: (value: boolean) => void
  isBurnable: boolean
  setIsBurnable: (value: boolean) => void
  isPausable: boolean
  setIsPausable: (value: boolean) => void
  isTransferTaxable: boolean
  setIsTransferTaxable: (value: boolean) => void
  transferTaxRate: number
  setTransferTaxRate: (value: number) => void
  maxTxAmount: number
  setMaxTxAmount: (value: number) => void
  maxWalletAmount: number
  setMaxWalletAmount: (value: number) => void
  antiBot: boolean
  setAntiBot: (value: boolean) => void
  antiDump: boolean
  setAntiDump: (value: boolean) => void
  tokenSupply: string
}

export function SecurityConfiguration({
  isMintable,
  setIsMintable,
  isBurnable,
  setIsBurnable,
  isPausable,
  setIsPausable,
  isTransferTaxable,
  setIsTransferTaxable,
  transferTaxRate,
  setTransferTaxRate,
  maxTxAmount,
  setMaxTxAmount,
  maxWalletAmount,
  setMaxWalletAmount,
  antiBot,
  setAntiBot,
  antiDump,
  setAntiDump,
  tokenSupply,
}: SecurityConfigurationProps) {
  const totalSupply = Number(tokenSupply)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Fonctionnalités de sécurité
          </CardTitle>
          <CardDescription>
            Configurez les fonctionnalités de sécurité pour protéger votre token et vos investisseurs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="bg-amber-50 border-amber-200">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              Les fonctionnalités de sécurité avancées sont disponibles uniquement sur Solana Mainnet. Sur Devnet, ces
              options sont simulées pour des fins de test.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Fonctionnalités de base</h3>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="mintable" className="text-base">
                    Mintable
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Permet de créer de nouveaux tokens après le déploiement initial
                  </p>
                </div>
                <Switch id="mintable" checked={isMintable} onCheckedChange={setIsMintable} />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="burnable" className="text-base">
                    Burnable
                  </Label>
                  <p className="text-sm text-muted-foreground">Permet de détruire des tokens pour réduire l'offre</p>
                </div>
                <Switch id="burnable" checked={isBurnable} onCheckedChange={setIsBurnable} />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="pausable" className="text-base">
                    Pausable
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Permet de suspendre temporairement les transferts de tokens
                  </p>
                </div>
                <Switch id="pausable" checked={isPausable} onCheckedChange={setIsPausable} />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="taxable" className="text-base">
                    Taxe sur les transferts
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Prélève une taxe sur chaque transfert pour financer le développement ou le marketing
                  </p>
                </div>
                <Switch id="taxable" checked={isTransferTaxable} onCheckedChange={setIsTransferTaxable} />
              </div>

              {isTransferTaxable && (
                <div className="space-y-2 pl-4 border-l-2 border-primary/20">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="tax-rate" className="text-sm">
                      Taux de taxe
                    </Label>
                    <span className="text-sm font-medium">{transferTaxRate}%</span>
                  </div>
                  <Slider
                    id="tax-rate"
                    value={[transferTaxRate]}
                    min={0}
                    max={10}
                    step={0.1}
                    onValueChange={(value) => setTransferTaxRate(value[0])}
                  />
                  <p className="text-xs text-muted-foreground">
                    Un taux de taxe trop élevé peut décourager les transactions. Nous recommandons un maximum de 5%.
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Protections avancées</h3>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="flex items-center gap-1">
                    <Label htmlFor="anti-bot" className="text-base">
                      Protection anti-bot
                    </Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Empêche les bots d'acheter votre token lors du lancement en limitant le nombre de
                            transactions par bloc.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <p className="text-sm text-muted-foreground">Protège contre les bots de trading lors du lancement</p>
                </div>
                <Switch id="anti-bot" checked={antiBot} onCheckedChange={setAntiBot} />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <div className="flex items-center gap-1">
                    <Label htmlFor="anti-dump" className="text-base">
                      Protection anti-dump
                    </Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Limite la quantité de tokens qu'un utilisateur peut vendre en une seule fois pour éviter les
                            chutes de prix brutales.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Empêche les grandes ventes qui pourraient faire chuter le prix
                  </p>
                </div>
                <Switch id="anti-dump" checked={antiDump} onCheckedChange={setAntiDump} />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-1">
                  <Label htmlFor="max-tx" className="text-base">
                    Montant maximum par transaction
                  </Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Limite le nombre de tokens qui peuvent être transférés en une seule transaction. Entrez 0 pour
                          désactiver cette limite.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    id="max-tx"
                    type="number"
                    min="0"
                    max={totalSupply}
                    value={maxTxAmount}
                    onChange={(e) => setMaxTxAmount(Number(e.target.value))}
                    className="flex-1"
                  />
                  <div className="text-sm text-muted-foreground whitespace-nowrap">
                    {maxTxAmount > 0 ? `${((maxTxAmount / totalSupply) * 100).toFixed(2)}%` : "Illimité"}
                  </div>
                </div>
                {maxTxAmount > 0 && (
                  <p className="text-xs text-muted-foreground">
                    Limite par transaction: {maxTxAmount.toLocaleString()} tokens
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-1">
                  <Label htmlFor="max-wallet" className="text-base">
                    Montant maximum par portefeuille
                  </Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Limite le nombre de tokens qu'un portefeuille peut détenir. Entrez 0 pour désactiver cette
                          limite.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    id="max-wallet"
                    type="number"
                    min="0"
                    max={totalSupply}
                    value={maxWalletAmount}
                    onChange={(e) => setMaxWalletAmount(Number(e.target.value))}
                    className="flex-1"
                  />
                  <div className="text-sm text-muted-foreground whitespace-nowrap">
                    {maxWalletAmount > 0 ? `${((maxWalletAmount / totalSupply) * 100).toFixed(2)}%` : "Illimité"}
                  </div>
                </div>
                {maxWalletAmount > 0 && (
                  <p className="text-xs text-muted-foreground">
                    Limite par portefeuille: {maxWalletAmount.toLocaleString()} tokens
                  </p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

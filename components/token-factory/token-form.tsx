"use client"

import type React from "react"

import { useState, useEffect, useCallback } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { useWallet } from "@solana/wallet-adapter-react"
import { Connection, Keypair, Transaction, SystemProgram } from "@solana/web3.js"
import {
  createInitializeMintInstruction,
  getMinimumBalanceForRentExemptMint,
  getMint,
  TOKEN_PROGRAM_ID,
  MINT_SIZE,
  getOrCreateAssociatedTokenAccount,
  createMintToInstruction,
} from "@solana/spl-token"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"
import { isValidBase58, sanitizeToBase58 } from "@/lib/base58-sanitizer"
import { useRouter } from "next/navigation"
import { getNetworkById } from "@/lib/network-config"
import { AlertCircle, CheckCircle2, Info, Loader2, Zap, Lock, Share2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface TokenFormProps {
  onCreateToken?: (data: any) => void
  onError?: () => void
  networkId?: string
}

export function TokenForm({ onCreateToken, onError, networkId }: TokenFormProps) {
  const { publicKey, signTransaction, connected } = useWallet()
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState("9")
  const [tokenSupply, setTokenSupply] = useState("1000000000")
  const [maxSupply, setMaxSupply] = useState("10000000000")
  const [tokenDescription, setTokenDescription] = useState("")
  const [tokenWebsite, setTokenWebsite] = useState("")
  const [tokenTwitter, setTokenTwitter] = useState("")
  const [tokenTelegram, setTokenTelegram] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [errorDetails, setErrorDetails] = useState<any>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const { toast } = useToast()
  const [mintAddress, setMintAddress] = useState<string | null>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const { network, activeNetwork } = useNetwork()
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({})
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [creationProgress, setCreationProgress] = useState(0)
  const [creationStep, setCreationStep] = useState("")
  const router = useRouter()
  const [selectedNetwork, setSelectedNetwork] = useState(networkId || network)

  // Security options
  const [isMintable, setIsMintable] = useState(true)
  const [isBurnable, setIsBurnable] = useState(true)
  const [isPausable, setIsPausable] = useState(false)
  const [isTransferTaxable, setIsTransferTaxable] = useState(false)
  const [transferTaxRate, setTransferTaxRate] = useState(0)

  // Mettre à jour le réseau sélectionné lorsque le réseau global change
  useEffect(() => {
    if (network && !networkId) {
      setSelectedNetwork(network)
    }
  }, [network, networkId])

  // Check wallet balance
  useEffect(() => {
    const checkBalance = async () => {
      if (publicKey) {
        try {
          const networkConfig = getNetworkById(selectedNetwork)
          const rpcUrl =
            networkConfig?.rpcUrl || process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com"

          const connection = new Connection(rpcUrl, "confirmed")
          const balance = await connection.getBalance(publicKey)
          setWalletBalance(balance / 1e9) // Convert to SOL
        } catch (err: any) {
          console.error("Error checking wallet balance:", err)
          setWalletBalance(null)
          toast({
            title: "Erreur de solde du portefeuille",
            description: err.message || "Impossible de récupérer le solde du portefeuille.",
            variant: "destructive",
          })
        }
      } else {
        setWalletBalance(null)
      }
    }

    checkBalance()
  }, [publicKey, selectedNetwork, toast])

  // Validation des champs du formulaire
  const validateForm = useCallback(() => {
    const errors: { [key: string]: string } = {}

    // Validation du nom du token
    if (!tokenName.trim()) {
      errors.name = "Le nom du token est requis"
    } else if (tokenName.length > 50) {
      errors.name = "Le nom du token ne doit pas dépasser 50 caractères"
    }

    // Validation du symbole du token
    if (!tokenSymbol.trim()) {
      errors.symbol = "Le symbole du token est requis"
    } else if (tokenSymbol.length > 10) {
      errors.symbol = "Le symbole du token ne doit pas dépasser 10 caractères"
    } else {
      // Vérifier que le symbole est valide en base58
      if (!isValidBase58(tokenSymbol)) {
        errors.symbol =
          "Le symbole contient des caractères non valides. Utilisez uniquement des caractères alphanumériques."
      }
    }

    // Validation des décimales
    const decimals = Number.parseInt(tokenDecimals)
    if (isNaN(decimals) || decimals < 0 || decimals > 9) {
      errors.decimals = "Les décimales doivent être un nombre entre 0 et 9"
    }

    // Validation de l'offre initiale
    const supply = Number.parseFloat(tokenSupply)
    if (isNaN(supply) || supply <= 0) {
      errors.supply = "L'offre initiale doit être un nombre positif"
    }

    // Validation de l'offre maximale
    const max = Number.parseFloat(maxSupply)
    if (isNaN(max) || max < supply) {
      errors.maxSupply = "L'offre maximale doit être supérieure ou égale à l'offre initiale"
    }

    // Validation des URLs optionnelles
    if (tokenWebsite && !tokenWebsite.startsWith("http")) {
      errors.website = "L'URL du site web doit commencer par http:// ou https://"
    }

    if (tokenTwitter && !tokenTwitter.startsWith("http")) {
      errors.twitter = "L'URL Twitter doit commencer par http:// ou https://"
    }

    if (tokenTelegram && !tokenTelegram.startsWith("http")) {
      errors.telegram = "L'URL Telegram doit commencer par http:// ou https://"
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }, [tokenName, tokenSymbol, tokenDecimals, tokenSupply, maxSupply, tokenWebsite, tokenTwitter, tokenTelegram])

  // Sanitiser les entrées avant soumission
  const sanitizeInputs = useCallback(() => {
    // Sanitiser le symbole pour s'assurer qu'il est valide en base58
    const sanitizedSymbol = sanitizeToBase58(tokenSymbol)
    if (sanitizedSymbol !== tokenSymbol) {
      setTokenSymbol(sanitizedSymbol)
      toast({
        title: "Symbole corrigé",
        description: `Le symbole a été corrigé pour être compatible avec base58: ${sanitizedSymbol}`,
        variant: "warning",
      })
      return false // Indiquer que des modifications ont été apportées
    }
    return true // Aucune modification nécessaire
  }, [tokenSymbol, toast])

  const handleCreateToken = async () => {
    if (!validateForm()) {
      toast({
        title: "Erreur de validation",
        description: "Veuillez corriger les erreurs dans le formulaire avant de continuer.",
        variant: "destructive",
      })
      return
    }

    if (!publicKey || !signTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour créer un token.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)
    setCreationProgress(10)
    setCreationStep("Préparation de la création du token...")

    try {
      // Récupérer la connexion RPC pour le réseau sélectionné
      const networkConfig = getNetworkById(selectedNetwork)
      if (!networkConfig) {
        throw new Error(`Réseau invalide: ${selectedNetwork}`)
      }

      const rpcUrl = networkConfig.rpcUrl
      console.log(`Utilisation du RPC: ${rpcUrl} pour le réseau ${networkConfig.name}`)

      const connection = new Connection(rpcUrl, "confirmed")

      // Créer un nouveau keypair pour le mint
      const mintKeypair = Keypair.generate()
      console.log(`Mint keypair généré: ${mintKeypair.publicKey.toString()}`)

      setCreationProgress(20)
      setCreationStep("Création du compte de token...")

      // Calculer le coût de la création du mint
      const rentExemptMint = await getMinimumBalanceForRentExemptMint(connection)
      console.log(`Coût de la création du mint: ${rentExemptMint / 1e9} SOL`)

      // Créer une transaction pour initialiser le mint
      const transaction = new Transaction()

      // Ajouter l'instruction pour créer le compte du mint
      transaction.add(
        SystemProgram.createAccount({
          fromPubkey: publicKey,
          newAccountPubkey: mintKeypair.publicKey,
          space: MINT_SIZE,
          lamports: rentExemptMint,
          programId: TOKEN_PROGRAM_ID,
        }),
      )

      // Ajouter l'instruction pour initialiser le mint
      transaction.add(
        createInitializeMintInstruction(
          mintKeypair.publicKey,
          Number(tokenDecimals),
          publicKey,
          isMintable ? publicKey : null,
          TOKEN_PROGRAM_ID,
        ),
      )

      setCreationProgress(40)
      setCreationStep("Préparation de la transaction de mint...")

      // Récupérer le blockhash récent
      const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash("confirmed")
      transaction.recentBlockhash = blockhash
      transaction.feePayer = publicKey

      // Signer la transaction avec le keypair du mint
      transaction.partialSign(mintKeypair)

      setCreationProgress(50)
      setCreationStep("Signature de la transaction...")

      // Faire signer la transaction par le wallet
      const signedTransaction = await signTransaction(transaction)
      console.log("Transaction signée")

      setCreationProgress(60)
      setCreationStep("Envoi de la transaction...")

      // Envoyer la transaction
      const signature = await connection.sendRawTransaction(signedTransaction.serialize())
      console.log(`Transaction envoyée: ${signature}`)

      // Attendre la confirmation
      setCreationStep("Attente de la confirmation de la transaction...")
      const confirmation = await connection.confirmTransaction({
        signature,
        blockhash,
        lastValidBlockHeight,
      })

      if (confirmation.value.err) {
        throw new Error(`Erreur lors de la confirmation: ${JSON.stringify(confirmation.value.err)}`)
      }

      console.log("Transaction confirmée")
      setCreationProgress(70)
      setCreationStep("Création du compte de token associé...")

      // Créer un compte de token associé pour le propriétaire
      const tokenAccount = await getOrCreateAssociatedTokenAccount(
        connection,
        Keypair.generate(), // Keypair temporaire pour payer les frais
        mintKeypair.publicKey,
        publicKey,
      )

      setCreationProgress(80)
      setCreationStep("Mint des tokens initiaux...")

      // Créer une transaction pour minter les tokens initiaux
      const mintTransaction = new Transaction()
      mintTransaction.add(
        createMintToInstruction(
          mintKeypair.publicKey,
          tokenAccount.address,
          publicKey,
          BigInt(Number(tokenSupply) * Math.pow(10, Number(tokenDecimals))),
          [],
          TOKEN_PROGRAM_ID,
        ),
      )

      // Récupérer le blockhash récent
      const mintBlockhash = await connection.getLatestBlockhash("confirmed")
      mintTransaction.recentBlockhash = mintBlockhash.blockhash
      mintTransaction.feePayer = publicKey

      // Faire signer la transaction par le wallet
      const signedMintTransaction = await signTransaction(mintTransaction)

      // Envoyer la transaction
      const mintSignature = await connection.sendRawTransaction(signedMintTransaction.serialize())
      console.log(`Transaction de mint envoyée: ${mintSignature}`)

      // Attendre la confirmation
      setCreationStep("Attente de la confirmation du mint...")
      const mintConfirmation = await connection.confirmTransaction({
        signature: mintSignature,
        blockhash: mintBlockhash.blockhash,
        lastValidBlockHeight: mintBlockhash.lastValidBlockHeight,
      })

      if (mintConfirmation.value.err) {
        throw new Error(`Erreur lors de la confirmation du mint: ${JSON.stringify(mintConfirmation.value.err)}`)
      }

      console.log("Transaction de mint confirmée")
      setCreationProgress(100)
      setCreationStep("Token créé avec succès!")

      // Récupérer les informations du token
      const tokenInfo = await getMint(connection, mintKeypair.publicKey)
      console.log("Informations du token:", tokenInfo)

      setMintAddress(mintKeypair.publicKey.toString())
      setSuccess(`Token ${tokenName} (${tokenSymbol}) créé avec succès!`)

      // Appeler le callback onCreateToken si fourni
      if (onCreateToken) {
        onCreateToken({
          tokenName,
          tokenSymbol,
          tokenDecimals,
          tokenSupply,
          maxSupply,
          tokenDescription,
          tokenWebsite,
          tokenTwitter,
          tokenTelegram,
          mintAddress: mintKeypair.publicKey.toString(),
          logoUrl: logoPreview,
          securityFeatures: {
            isMintable,
            isBurnable,
            isPausable,
            isTransferTaxable,
            transferTaxRate,
          },
          network: selectedNetwork,
          networkName: networkConfig.name,
          signature,
          mintSignature,
        })
      }

      toast({
        title: "Token créé avec succès",
        description: `Votre token ${tokenName} (${tokenSymbol}) a été créé avec succès!`,
      })
    } catch (err: any) {
      console.error("Error creating token:", err)
      setError(err.message || "Une erreur s'est produite lors de la création du token")
      setErrorDetails(err)
      setCreationProgress(0)

      // Call the onError callback if provided
      if (onError) {
        onError()
      }

      toast({
        title: "Échec de la création du token",
        description: err.message || "Une erreur s'est produite lors de la création du token",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Sanitiser les entrées avant validation
    if (!sanitizeInputs()) {
      // Si des modifications ont été apportées, arrêter la soumission
      return
    }

    await handleCreateToken()
  }

  // Gestionnaires d'événements pour les champs de formulaire avec sanitisation
  const handleSymbolChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value

    // Option 2: Permettre la saisie mais valider en temps réel
    setTokenSymbol(value)

    if (value && !isValidBase58(value)) {
      setValidationErrors((prev) => ({
        ...prev,
        symbol: "Le symbole contient des caractères non valides",
      }))
    } else {
      // Effacer l'erreur si maintenant valide
      setValidationErrors((prev) => {
        const { symbol, ...rest } = prev
        return rest
      })
    }
  }

  // Si le token est en cours de création, afficher la progression
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Création du token en cours</CardTitle>
          <CardDescription>Veuillez patienter pendant que nous créons votre token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium">{creationStep}</h3>
          </div>
          <Progress value={creationProgress} className="h-2" />

          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <h4 className="font-medium">Détails du token</h4>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Nom:</span> {tokenName}
              </div>
              <div>
                <span className="text-muted-foreground">Symbole:</span> {tokenSymbol}
              </div>
              <div>
                <span className="text-muted-foreground">Décimales:</span> {tokenDecimals}
              </div>
              <div>
                <span className="text-muted-foreground">Offre initiale:</span> {Number(tokenSupply).toLocaleString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Si le token a été créé avec succès, afficher les détails
  if (success) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-6 w-6 text-green-500" />
            <CardTitle>Token créé avec succès</CardTitle>
          </div>
          <CardDescription>
            Votre token a été créé sur la blockchain {getNetworkById(selectedNetwork)?.name || "Solana"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Félicitations!</AlertTitle>
            <AlertDescription>
              Votre token {tokenName} ({tokenSymbol}) a été créé avec succès. Vous pouvez maintenant le gérer et le
              promouvoir.
            </AlertDescription>
          </Alert>

          <div className="flex items-center gap-4">
            <div className="w-20 h-20 rounded-full overflow-hidden border flex items-center justify-center bg-muted">
              {logoPreview ? (
                <img src={logoPreview || "/placeholder.svg"} alt={tokenName} className="w-full h-full object-cover" />
              ) : (
                <div className="text-2xl font-bold text-muted-foreground">
                  {tokenSymbol.substring(0, 2).toUpperCase()}
                </div>
              )}
            </div>
            <div>
              <h3 className="text-xl font-bold">{tokenName}</h3>
              <p className="text-muted-foreground">{tokenSymbol}</p>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{tokenDecimals} décimales</Badge>
                <Badge variant="outline">{Number(tokenSupply).toLocaleString()} tokens</Badge>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-muted-foreground">Adresse du token</Label>
            <div className="flex items-center justify-between bg-muted p-2 rounded-md">
              <code className="text-xs break-all">{mintAddress}</code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(mintAddress || "")
                  toast({
                    title: "Adresse copiée",
                    description: "L'adresse du token a été copiée dans le presse-papier.",
                  })
                }}
              >
                Copier
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="default"
              onClick={() => {
                const networkConfig = getNetworkById(selectedNetwork)
                const explorerUrl =
                  networkConfig?.environment === "mainnet"
                    ? `https://explorer.solana.com/address/${mintAddress}`
                    : `https://explorer.solana.com/address/${mintAddress}?cluster=devnet`
                window.open(explorerUrl, "_blank")
              }}
            >
              Voir sur l'explorateur
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                router.push("/token-factory/tokens")
              }}
            >
              Voir tous mes tokens
            </Button>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Prochaines étapes</h3>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <div className="bg-primary/10 p-1 rounded-full mt-0.5">
                  <Zap className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">Ajouter de la liquidité</p>
                  <p className="text-sm text-muted-foreground">
                    Créez un pool de liquidité sur un DEX pour permettre l'échange de votre token
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-primary/10 p-1 rounded-full mt-0.5">
                  <Lock className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">Verrouiller la liquidité</p>
                  <p className="text-sm text-muted-foreground">
                    Verrouillez votre liquidité pour renforcer la confiance des investisseurs
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-primary/10 p-1 rounded-full mt-0.5">
                  <Share2 className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="font-medium">Promouvoir votre token</p>
                  <p className="text-sm text-muted-foreground">
                    Partagez votre token sur les réseaux sociaux et les communautés crypto
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button
            className="w-full"
            onClick={() => {
              setSuccess(null)
              setTokenName("")
              setTokenSymbol("")
              setTokenDecimals("9")
              setTokenSupply("1000000000")
              setTokenDescription("")
              setTokenWebsite("")
              setTokenTwitter("")
              setTokenTelegram("")
              setLogoPreview(null)
            }}
          >
            Créer un autre token
          </Button>
        </CardFooter>
      </Card>
    )
  }

  // Afficher le formulaire de création de token
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Créer un nouveau token</CardTitle>
        <CardDescription>
          Remplissez le formulaire ci-dessous pour créer votre propre token sur{" "}
          {getNetworkById(selectedNetwork)?.name || "Solana"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
            {errorDetails && (
              <div className="mt-2 text-sm">
                <details>
                  <summary className="font-medium cursor-pointer">Détails techniques</summary>
                  <div className="mt-2 p-2 bg-destructive/10 rounded text-xs font-mono whitespace-pre-wrap">
                    {JSON.stringify(errorDetails, null, 2)}
                  </div>
                </details>
              </div>
            )}
          </Alert>
        )}

        {!connected && (
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Portefeuille non connecté</AlertTitle>
            <AlertDescription>
              Veuillez connecter votre portefeuille Solana pour créer un token. Cliquez sur le bouton "Connecter" en
              haut à droite.
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tokenName">Nom du token</Label>
              <Input
                id="tokenName"
                placeholder="ex: Mon Token"
                value={tokenName}
                onChange={(e) => setTokenName(e.target.value)}
                required
                className={validationErrors.name ? "border-red-500" : ""}
              />
              {validationErrors.name && <p className="text-xs text-red-500 mt-1">{validationErrors.name}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="tokenSymbol">Symbole du token</Label>
              <Input
                id="tokenSymbol"
                placeholder="ex: MTK"
                value={tokenSymbol}
                onChange={handleSymbolChange}
                required
                maxLength={10}
                className={validationErrors.symbol ? "border-red-500" : ""}
              />
              {validationErrors.symbol && <p className="text-xs text-red-500 mt-1">{validationErrors.symbol}</p>}
              <p className="text-xs text-muted-foreground">
                Utilisez uniquement des caractères alphanumériques (pas de O, I, l, 0)
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="tokenDecimals">Décimales</Label>
                <Input
                  id="tokenDecimals"
                  type="number"
                  min="0"
                  max="9"
                  value={tokenDecimals}
                  onChange={(e) => setTokenDecimals(e.target.value)}
                  required
                  className={validationErrors.decimals ? "border-red-500" : ""}
                />
                {validationErrors.decimals && <p className="text-xs text-red-500 mt-1">{validationErrors.decimals}</p>}
                <p className="text-xs text-muted-foreground">
                  Les décimales déterminent la divisibilité de votre token. La valeur recommandée est 9.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tokenSupply">Offre initiale</Label>
                <Input
                  id="tokenSupply"
                  type="number"
                  min="1"
                  value={tokenSupply}
                  onChange={(e) => setTokenSupply(e.target.value)}
                  required
                  className={validationErrors.supply ? "border-red-500" : ""}
                />
                {validationErrors.supply && <p className="text-xs text-red-500 mt-1">{validationErrors.supply}</p>}
                <p className="text-xs text-muted-foreground">
                  L'offre initiale est le nombre total de tokens qui seront créés.
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxSupply">Offre maximale</Label>
              <Input
                id="maxSupply"
                type="number"
                min={tokenSupply}
                value={maxSupply}
                onChange={(e) => setMaxSupply(e.target.value)}
                required
                className={validationErrors.maxSupply ? "border-red-500" : ""}
              />
              {validationErrors.maxSupply && <p className="text-xs text-red-500 mt-1">{validationErrors.maxSupply}</p>}
              <p className="text-xs text-muted-foreground">
                L'offre maximale est le nombre total de tokens qui pourront être créés à terme.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tokenDescription">Description (Optionnel)</Label>
              <Textarea
                id="tokenDescription"
                placeholder="Décrivez votre token..."
                value={tokenDescription}
                onChange={(e) => setTokenDescription(e.target.value)}
                rows={4}
              />
              <p className="text-xs text-muted-foreground">
                Une description claire aide les utilisateurs à comprendre l'utilité de votre token.
              </p>
            </div>

            <div className="space-y-2 mt-4">
              <Label htmlFor="networkSelect">Réseau</Label>
              <select
                id="networkSelect"
                className="w-full p-2 border rounded-md"
                value={selectedNetwork}
                onChange={(e) => setSelectedNetwork(e.target.value)}
              >
                <option value="solana-devnet">Solana Devnet</option>
                <option value="solana-mainnet">Solana Mainnet</option>
              </select>
              <p className="text-xs text-muted-foreground">
                {selectedNetwork.includes("mainnet")
                  ? "Attention: La création sur mainnet nécessite des SOL réels et des frais plus élevés."
                  : "Devnet est recommandé pour les tests. Les tokens créés n'ont pas de valeur réelle."}
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isMintable"
                  checked={isMintable}
                  onChange={(e) => setIsMintable(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="isMintable">Token mintable (possibilité de créer plus de tokens)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isBurnable"
                  checked={isBurnable}
                  onChange={(e) => setIsBurnable(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="isBurnable">Token burnable (possibilité de détruire des tokens)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPausable"
                  checked={isPausable}
                  onChange={(e) => setIsPausable(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="isPausable">Token pausable (possibilité de geler les transferts)</Label>
              </div>
            </div>
          </div>

          <div className="flex justify-between pt-4">
            <Button type="button" variant="outline" disabled={true}>
              Précédent
            </Button>
            <Button type="submit" disabled={isLoading || !connected}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Création en cours...
                </>
              ) : (
                "Créer le token"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <div className="text-sm text-muted-foreground">
          {connected ? (
            <>
              Connecté: {publicKey?.toString().slice(0, 6)}...{publicKey?.toString().slice(-4)}
              {walletBalance !== null && <> | Solde: {walletBalance.toFixed(4)} SOL</>}
            </>
          ) : (
            "Veuillez connecter votre portefeuille pour créer un token"
          )}
        </div>
      </CardFooter>
    </Card>
  )
}

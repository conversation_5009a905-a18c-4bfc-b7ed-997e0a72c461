"use client"

import type React from "react"

import { useState, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { ImageIcon, UploadIcon, XIcon } from "lucide-react"
import Image from "next/image"

interface TokenImageSelectorProps {
  logoPreview: string | null
  onChange: (file: File | null, preview: string | null) => void
}

export function TokenImageSelector({ logoPreview, onChange }: TokenImageSelectorProps) {
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Gérer le clic sur le bouton de téléchargement
  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  // Gérer le changement de fichier
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      validateAndProcessImage(file)
    }
  }

  // Gérer le glisser-déposer
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const file = e.dataTransfer.files?.[0]
    if (file) {
      validateAndProcessImage(file)
    }
  }

  // Valider et traiter l'image
  const validateAndProcessImage = (file: File) => {
    // Vérifier le type de fichier
    if (!file.type.startsWith("image/")) {
      alert("Veuillez sélectionner une image valide.")
      return
    }

    // Vérifier la taille du fichier (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      alert("L'image est trop volumineuse. La taille maximale est de 2MB.")
      return
    }

    // Créer une URL pour la prévisualisation
    const reader = new FileReader()
    reader.onload = (e) => {
      const preview = e.target?.result as string
      onChange(file, preview)
    }
    reader.readAsDataURL(file)
  }

  // Supprimer l'image
  const handleRemoveImage = () => {
    onChange(null, null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  // Sélectionner une image prédéfinie
  const selectPredefinedImage = (imageUrl: string) => {
    onChange(null, imageUrl)
  }

  // Images prédéfinies
  const predefinedImages = [
    "/digital-token.png",
    "/abstract-geometric-sq.png",
    "/abstract-geometric-fg.png",
    "/abstract-geometric-sm.png",
    "/abstract-tb.png",
    "/abstract-df.png",
  ]

  return (
    <div className="space-y-4">
      <Label className="text-base">Logo du token</Label>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/png,image/jpeg,image/gif,image/webp"
        className="hidden"
      />

      {logoPreview ? (
        <div className="relative">
          <div className="relative w-full h-48 rounded-lg overflow-hidden border">
            <Image
              src={logoPreview || "/placeholder.svg"}
              alt="Logo du token"
              fill
              className="object-contain"
              sizes="(max-width: 768px) 100vw, 300px"
            />
          </div>
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full"
            onClick={handleRemoveImage}
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <Card
          className={`border-dashed cursor-pointer ${
            isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/20"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleButtonClick}
        >
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <div className="rounded-full bg-primary/10 p-3 mb-4">
              <ImageIcon className="h-8 w-8 text-primary" />
            </div>
            <p className="text-sm font-medium mb-1">Glissez-déposez votre logo ici</p>
            <p className="text-xs text-muted-foreground mb-4">PNG, JPG ou GIF (max. 2MB)</p>
            <Button type="button" variant="outline" size="sm">
              <UploadIcon className="h-4 w-4 mr-2" />
              Parcourir
            </Button>
          </CardContent>
        </Card>
      )}

      <div>
        <p className="text-sm text-muted-foreground mb-2">Ou choisissez parmi nos modèles :</p>
        <div className="grid grid-cols-3 gap-2">
          {predefinedImages.map((image, index) => (
            <div
              key={index}
              className={`relative aspect-square rounded-md overflow-hidden border cursor-pointer hover:border-primary transition-colors ${
                logoPreview === image ? "border-primary ring-2 ring-primary/20" : "border-muted"
              }`}
              onClick={() => selectPredefinedImage(image)}
            >
              <Image
                src={image || "/placeholder.svg"}
                alt={`Modèle ${index + 1}`}
                fill
                className="object-cover"
                sizes="100px"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

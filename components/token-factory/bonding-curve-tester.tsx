"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, AlertCircle } from "lucide-react"

export default function BondingCurveTester() {
  const [tokenAddress, setTokenAddress] = useState("")
  const [initialSupply, setInitialSupply] = useState(1000000)
  const [maxSupply, setMaxSupply] = useState(10000000)
  const [solPriceUsd, setSolPriceUsd] = useState(100)
  const [dexListingThresholdUsd, setDexListingThresholdUsd] = useState(10000)
  const [reserveRatio, setReserveRatio] = useState(0.2)
  const [initialPrice, setInitialPrice] = useState(0.00001)

  const [solAmount, setSolAmount] = useState(0.1)
  const [currentSupply, setCurrentSupply] = useState(1000000)

  const [isInitializing, setIsInitializing] = useState(false)
  const [isSimulating, setIsSimulating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [initResult, setInitResult] = useState<any>(null)
  const [simulationResult, setSimulationResult] = useState<any>(null)

  const handleInitialize = async () => {
    setIsInitializing(true)
    setError(null)

    try {
      const response = await fetch("/api/token/initialize-curve", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tokenAddress,
          initialSupply,
          maxSupply,
          solPriceUsd,
          dexListingThresholdUsd,
          reserveRatio,
          initialPrice,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setInitResult(data)
        setCurrentSupply(initialSupply)
      } else {
        setError(data.error || "Failed to initialize bonding curve")
      }
    } catch (err: any) {
      setError(err.message || "An error occurred")
    } finally {
      setIsInitializing(false)
    }
  }

  const handleSimulate = async () => {
    setIsSimulating(true)
    setError(null)

    try {
      const response = await fetch("/api/token/simulate-purchase", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tokenAddress,
          currentSupply,
          amountInSol: solAmount,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setSimulationResult(data.simulation)
      } else {
        setError(data.error || "Failed to simulate purchase")
      }
    } catch (err: any) {
      setError(err.message || "An error occurred")
    } finally {
      setIsSimulating(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Bonding Curve Initializer</CardTitle>
          <CardDescription>Configure and initialize a bonding curve for testing</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="tokenAddress">Token Address</Label>
            <Input
              id="tokenAddress"
              value={tokenAddress}
              onChange={(e) => setTokenAddress(e.target.value)}
              placeholder="Enter token address"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="initialSupply">Initial Supply</Label>
              <Input
                id="initialSupply"
                type="number"
                value={initialSupply}
                onChange={(e) => setInitialSupply(Number(e.target.value))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxSupply">Max Supply</Label>
              <Input
                id="maxSupply"
                type="number"
                value={maxSupply}
                onChange={(e) => setMaxSupply(Number(e.target.value))}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="solPriceUsd">SOL Price (USD)</Label>
              <Input
                id="solPriceUsd"
                type="number"
                value={solPriceUsd}
                onChange={(e) => setSolPriceUsd(Number(e.target.value))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="dexListingThresholdUsd">DEX Listing Threshold (USD)</Label>
              <Input
                id="dexListingThresholdUsd"
                type="number"
                value={dexListingThresholdUsd}
                onChange={(e) => setDexListingThresholdUsd(Number(e.target.value))}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="reserveRatio">Reserve Ratio (0.1-0.9)</Label>
              <Input
                id="reserveRatio"
                type="number"
                min="0.1"
                max="0.9"
                step="0.1"
                value={reserveRatio}
                onChange={(e) => setReserveRatio(Number(e.target.value))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="initialPrice">Initial Price (SOL)</Label>
              <Input
                id="initialPrice"
                type="number"
                min="0.000001"
                step="0.000001"
                value={initialPrice}
                onChange={(e) => setInitialPrice(Number(e.target.value))}
              />
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={handleInitialize} disabled={isInitializing || !tokenAddress}>
            {isInitializing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
            {isInitializing ? "Initializing..." : "Initialize Bonding Curve"}
          </Button>
        </CardFooter>
      </Card>

      {initResult && (
        <Card>
          <CardHeader>
            <CardTitle>Simulation Tester</CardTitle>
            <CardDescription>Test the bonding curve with different parameters</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="currentSupply">Current Supply</Label>
                <Input
                  id="currentSupply"
                  type="number"
                  value={currentSupply}
                  onChange={(e) => setCurrentSupply(Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="solAmount">SOL Amount</Label>
                <Input
                  id="solAmount"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={solAmount}
                  onChange={(e) => setSolAmount(Number(e.target.value))}
                />
              </div>
            </div>

            {simulationResult && (
              <div className="rounded-md bg-muted p-4">
                <h3 className="font-medium mb-2">Simulation Results</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Tokens to Receive:</div>
                  <div className="font-mono">{simulationResult.tokensToReceive.toLocaleString()}</div>

                  <div>Price per Token:</div>
                  <div className="font-mono">{simulationResult.pricePerToken.toFixed(8)} SOL</div>

                  <div>New Supply:</div>
                  <div className="font-mono">{simulationResult.newSupply.toLocaleString()}</div>

                  <div>New Price:</div>
                  <div className="font-mono">{simulationResult.newPrice.toFixed(8)} SOL</div>

                  <div>Market Cap (USD):</div>
                  <div className="font-mono">${simulationResult.marketCapUsd.toLocaleString()}</div>

                  <div>DEX Listing Progress:</div>
                  <div className="font-mono">{simulationResult.dexListingProgress.toFixed(2)}%</div>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button onClick={handleSimulate} disabled={isSimulating}>
              {isSimulating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              {isSimulating ? "Simulating..." : "Simulate Purchase"}
            </Button>
          </CardFooter>
        </Card>
      )}

      {initResult && (
        <Card>
          <CardHeader>
            <CardTitle>Initial Simulations</CardTitle>
            <CardDescription>Results from initial bonding curve setup</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {initResult.simulations.map((sim: any, index: number) => (
                <div key={index} className="rounded-md bg-muted p-4">
                  <h3 className="font-medium mb-2">
                    Simulation {index + 1} ({index === 0 ? "0.1" : index === 1 ? "0.5" : "1.0"} SOL)
                  </h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>Tokens to Receive:</div>
                    <div className="font-mono">{sim.tokensToReceive.toLocaleString()}</div>

                    <div>Price per Token:</div>
                    <div className="font-mono">{sim.pricePerToken.toFixed(8)} SOL</div>

                    <div>New Supply:</div>
                    <div className="font-mono">{sim.newSupply.toLocaleString()}</div>

                    <div>New Price:</div>
                    <div className="font-mono">{sim.newPrice.toFixed(8)} SOL</div>

                    <div>Market Cap (USD):</div>
                    <div className="font-mono">${sim.marketCapUsd.toLocaleString()}</div>

                    <div>DEX Listing Progress:</div>
                    <div className="font-mono">{sim.dexListingProgress.toFixed(2)}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

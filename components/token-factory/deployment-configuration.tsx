"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Rocket, Settings, Shield, AlertTriangle, Clock } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface DeploymentConfigurationProps {
  initialValues?: {
    network: string
    deploymentType: string
    deploymentTime: "now" | "scheduled"
    scheduledTime?: string
    gasSettings: {
      priorityFee: number
      maxFee: number
      autoAdjust: boolean
    }
    verification: {
      verifyContract: boolean
      publishSource: boolean
    }
  }
  onChange?: (values: any) => void
}

export function DeploymentConfiguration({ initialValues, onChange }: DeploymentConfigurationProps) {
  const [network, setNetwork] = useState(initialValues?.network || "mainnet")
  const [deploymentType, setDeploymentType] = useState(initialValues?.deploymentType || "standard")
  const [deploymentTime, setDeploymentTime] = useState(initialValues?.deploymentTime || "now")
  const [scheduledTime, setScheduledTime] = useState(initialValues?.scheduledTime || "")
  const [gasSettings, setGasSettings] = useState({
    priorityFee: initialValues?.gasSettings?.priorityFee || 5,
    maxFee: initialValues?.gasSettings?.maxFee || 20,
    autoAdjust: initialValues?.gasSettings?.autoAdjust !== false,
  })
  const [verification, setVerification] = useState({
    verifyContract: initialValues?.verification?.verifyContract !== false,
    publishSource: initialValues?.verification?.publishSource !== false,
  })

  // Mettre à jour le parent lorsque les valeurs changent
  const updateValues = (newValues: any) => {
    if (onChange) {
      onChange({
        network,
        deploymentType,
        deploymentTime,
        scheduledTime,
        gasSettings,
        verification,
        ...newValues,
      })
    }
  }

  // Mettre à jour le réseau
  const updateNetwork = (value: string) => {
    setNetwork(value)
    updateValues({ network: value })
  }

  // Mettre à jour le type de déploiement
  const updateDeploymentType = (value: string) => {
    setDeploymentType(value)
    updateValues({ deploymentType: value })
  }

  // Mettre à jour le moment du déploiement
  const updateDeploymentTime = (value: "now" | "scheduled") => {
    setDeploymentTime(value)
    updateValues({ deploymentTime: value })
  }

  // Mettre à jour l'heure programmée
  const updateScheduledTime = (value: string) => {
    setScheduledTime(value)
    updateValues({ scheduledTime: value })
  }

  // Mettre à jour les paramètres de gaz
  const updateGasSettings = (field: keyof typeof gasSettings, value: any) => {
    const newGasSettings = { ...gasSettings, [field]: value }
    setGasSettings(newGasSettings)
    updateValues({ gasSettings: newGasSettings })
  }

  // Mettre à jour les paramètres de vérification
  const updateVerification = (field: keyof typeof verification, value: boolean) => {
    const newVerification = { ...verification, [field]: value }
    setVerification(newVerification)
    updateValues({ verification: newVerification })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Rocket className="h-5 w-5" />
          Configuration du déploiement
        </CardTitle>
        <CardDescription>Configurez les paramètres de déploiement de votre token</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="network">Réseau</Label>
            <Select value={network} onValueChange={updateNetwork}>
              <SelectTrigger id="network">
                <SelectValue placeholder="Sélectionnez un réseau" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mainnet">Solana Mainnet</SelectItem>
                <SelectItem value="devnet">Solana Devnet</SelectItem>
                <SelectItem value="testnet">Solana Testnet</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              {network === "mainnet"
                ? "Réseau principal Solana - Déploiement en production"
                : network === "devnet"
                  ? "Réseau de développement Solana - Pour les tests avec des SOL de test"
                  : "Réseau de test Solana - Pour les tests avancés"}
            </p>
          </div>

          <div>
            <Label htmlFor="deployment-type">Type de déploiement</Label>
            <Select value={deploymentType} onValueChange={updateDeploymentType}>
              <SelectTrigger id="deployment-type">
                <SelectValue placeholder="Sélectionnez un type de déploiement" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="optimized">Optimisé</SelectItem>
                <SelectItem value="premium">Premium</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              {deploymentType === "standard"
                ? "Déploiement standard - Équilibre entre coût et performance"
                : deploymentType === "optimized"
                  ? "Déploiement optimisé - Réduit les coûts de gaz"
                  : "Déploiement premium - Priorité maximale et vérification complète"}
            </p>
          </div>

          <div className="space-y-2">
            <Label>Moment du déploiement</Label>
            <RadioGroup value={deploymentTime} onValueChange={updateDeploymentTime as any}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="now" id="deploy-now" />
                <Label htmlFor="deploy-now" className="cursor-pointer">
                  Déployer maintenant
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="scheduled" id="deploy-scheduled" />
                <Label htmlFor="deploy-scheduled" className="cursor-pointer">
                  Programmer le déploiement
                </Label>
              </div>
            </RadioGroup>

            {deploymentTime === "scheduled" && (
              <div className="mt-2">
                <Label htmlFor="scheduled-time">Date et heure programmées</Label>
                <Input
                  id="scheduled-time"
                  type="datetime-local"
                  value={scheduledTime}
                  onChange={(e) => updateScheduledTime(e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Le token sera déployé automatiquement à la date et l'heure spécifiées
                </p>
              </div>
            )}
          </div>
        </div>

        <Tabs defaultValue="gas" className="space-y-4">
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="gas">Paramètres de gaz</TabsTrigger>
            <TabsTrigger value="verification">Vérification</TabsTrigger>
          </TabsList>

          <TabsContent value="gas" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-2">
                <Settings className="h-5 w-5 mt-0.5" />
                <div>
                  <h3 className="font-medium">Ajustement automatique du gaz</h3>
                  <p className="text-sm text-muted-foreground">
                    Ajuste automatiquement les frais de gaz en fonction des conditions du réseau
                  </p>
                </div>
              </div>
              <Switch
                checked={gasSettings.autoAdjust}
                onCheckedChange={(checked) => updateGasSettings("autoAdjust", checked)}
              />
            </div>

            {!gasSettings.autoAdjust && (
              <div className="space-y-4 pl-6 border-l-2 border-gray-100">
                <div>
                  <Label htmlFor="priority-fee">Frais de priorité (Lamports)</Label>
                  <Input
                    id="priority-fee"
                    type="number"
                    value={gasSettings.priorityFee}
                    onChange={(e) => updateGasSettings("priorityFee", Number(e.target.value))}
                    min={1}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Frais supplémentaires pour accélérer la transaction
                  </p>
                </div>

                <div>
                  <Label htmlFor="max-fee">Frais maximum (Lamports)</Label>
                  <Input
                    id="max-fee"
                    type="number"
                    value={gasSettings.maxFee}
                    onChange={(e) => updateGasSettings("maxFee", Number(e.target.value))}
                    min={gasSettings.priorityFee}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Montant maximum que vous êtes prêt à payer pour la transaction
                  </p>
                </div>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Des frais de gaz trop bas peuvent entraîner des retards dans le déploiement de votre token.
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </TabsContent>

          <TabsContent value="verification" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-2">
                <Shield className="h-5 w-5 mt-0.5" />
                <div>
                  <h3 className="font-medium">Vérifier le contrat</h3>
                  <p className="text-sm text-muted-foreground">
                    Vérifie le code source du contrat sur l'explorateur Solana
                  </p>
                </div>
              </div>
              <Switch
                checked={verification.verifyContract}
                onCheckedChange={(checked) => updateVerification("verifyContract", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-start gap-2">
                <Clock className="h-5 w-5 mt-0.5" />
                <div>
                  <h3 className="font-medium">Publier le code source</h3>
                  <p className="text-sm text-muted-foreground">
                    Publie le code source du contrat pour une transparence totale
                  </p>
                </div>
              </div>
              <Switch
                checked={verification.publishSource}
                onCheckedChange={(checked) => updateVerification("publishSource", checked)}
              />
            </div>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                La vérification du contrat est recommandée pour la transparence et la confiance des utilisateurs.
              </AlertDescription>
            </Alert>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

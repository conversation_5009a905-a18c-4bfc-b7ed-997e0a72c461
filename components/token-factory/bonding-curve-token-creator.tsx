"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { TokenImageSelector } from "@/components/token-factory/token-image-selector"
import { BondingCurveChart } from "@/components/token-factory/bonding-curve-chart"
import { TokenChatInterface } from "@/components/token-factory/token-chat-interface"
import { TokenCreationProgress } from "@/components/token-factory/token-creation-progress"
import type { BondingCurveParams } from "@/lib/bonding-curve-service"
import { ArrowLeft } from "lucide-react"

// Schéma de validation pour le formulaire
const tokenFormSchema = z.object({
  name: z.string().min(3, { message: "Le nom doit contenir au moins 3 caractères" }),
  symbol: z.string().min(2, { message: "Le symbole doit contenir au moins 2 caractères" }),
  description: z.string().optional(),
  initialPrice: z.coerce.number().positive({ message: "Le prix initial doit être positif" }),
  reserveRatio: z.coerce.number().min(0.1).max(1),
  initialSupply: z.coerce.number().int().positive(),
  maxSupply: z.coerce.number().int().positive(),
  taxRate: z.coerce.number().min(0).max(0.2),
  antiBot: z.boolean().default(true),
  antiDump: z.boolean().default(true),
  liquidityLock: z.coerce.number().int().min(0),
})

type TokenFormValues = z.infer<typeof tokenFormSchema>

export function BondingCurveTokenCreator() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("basic")
  const [tokenImage, setTokenImage] = useState<File | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [currentStep, setCurrentStep] = useState("init")

  // Étapes de création du token
  const creationSteps = [
    {
      id: "init",
      title: "Initialisation",
      description: "Préparation des paramètres du token",
      status: "completed" as const,
    },
    {
      id: "validation",
      title: "Validation",
      description: "Vérification des paramètres et de la courbe",
      status: "processing" as const,
      progress: 50,
    },
    {
      id: "deployment",
      title: "Déploiement",
      description: "Déploiement du contrat sur la blockchain",
      status: "pending" as const,
    },
    {
      id: "liquidity",
      title: "Liquidité",
      description: "Configuration de la liquidité initiale",
      status: "pending" as const,
    },
    {
      id: "verification",
      title: "Vérification",
      description: "Vérification du contrat sur l'explorateur",
      status: "pending" as const,
    },
  ]

  // Initialiser le formulaire avec des valeurs par défaut
  const form = useForm<TokenFormValues>({
    resolver: zodResolver(tokenFormSchema),
    defaultValues: {
      name: "",
      symbol: "",
      description: "",
      initialPrice: 0.001,
      reserveRatio: 0.2,
      initialSupply: 1000000,
      maxSupply: 10000000,
      taxRate: 0.05,
      antiBot: true,
      antiDump: true,
      liquidityLock: 30,
    },
  })

  // Obtenir les valeurs actuelles du formulaire pour la prévisualisation
  const formValues = form.watch()

  // Préparer les paramètres pour la courbe de liaison
  const bondingCurveParams: BondingCurveParams = {
    initialPrice: formValues.initialPrice,
    reserveRatio: formValues.reserveRatio,
    initialSupply: formValues.initialSupply,
    maxSupply: formValues.maxSupply,
    currentSupply: formValues.initialSupply,
    taxRate: formValues.taxRate,
    slope: 0,
    reserveBalance: formValues.initialPrice * formValues.initialSupply * formValues.reserveRatio,
  }

  // Gérer la soumission du formulaire
  const onSubmit = async (data: TokenFormValues) => {
    setIsCreating(true)
    setCurrentStep("validation")

    try {
      // Simuler le processus de création
      await new Promise((resolve) => setTimeout(resolve, 2000))
      setCurrentStep("deployment")

      await new Promise((resolve) => setTimeout(resolve, 3000))
      setCurrentStep("liquidity")

      await new Promise((resolve) => setTimeout(resolve, 2000))
      setCurrentStep("verification")

      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Rediriger vers la page de succès
      router.push("/token-factory/success")
    } catch (error) {
      console.error("Erreur lors de la création du token:", error)
      setIsCreating(false)
    }
  }

  // Gérer l'envoi de message dans l'interface de chat
  const handleSendMessage = async (message: string) => {
    console.log("Message envoyé:", message)
    // Ici, vous pourriez appeler une API pour traiter le message
    await new Promise((resolve) => setTimeout(resolve, 1000))
  }

  return (
    <div className="container py-8">
      {isCreating ? (
        <Card>
          <CardHeader>
            <CardTitle>Création du token en cours</CardTitle>
            <CardDescription>Veuillez patienter pendant que nous créons votre token</CardDescription>
          </CardHeader>
          <CardContent>
            <TokenCreationProgress steps={creationSteps} currentStepId={currentStep} />
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="flex items-center mb-6">
            <Button variant="ghost" size="icon" onClick={() => router.back()} className="mr-4">
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Créer un token avec Bonding Curve</h1>
              <p className="text-muted-foreground">
                Configurez les paramètres de votre token avec mécanisme de courbe de liaison
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid grid-cols-3">
                      <TabsTrigger value="basic">Informations de base</TabsTrigger>
                      <TabsTrigger value="curve">Courbe de liaison</TabsTrigger>
                      <TabsTrigger value="security">Sécurité</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-4 pt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Informations de base</CardTitle>
                          <CardDescription>Entrez les informations essentielles pour votre token</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Nom du token</FormLabel>
                                <FormControl>
                                  <Input placeholder="Ex: My Awesome Token" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Le nom complet de votre token, tel qu'il apparaîtra sur les exchanges
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="symbol"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Symbole du token</FormLabel>
                                <FormControl>
                                  <Input placeholder="Ex: MAT" {...field} maxLength={10} />
                                </FormControl>
                                <FormDescription>
                                  Le symbole court de votre token, généralement 3-5 caractères
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Description (optionnelle)</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Décrivez brièvement votre token et son utilité..."
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <TokenImageSelector
                            onImageSelect={(file, url) => {
                              setTokenImage(file)
                            }}
                          />
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="curve" className="space-y-4 pt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Paramètres de la courbe</CardTitle>
                          <CardDescription>
                            Configurez les paramètres qui déterminent comment le prix de votre token évolue
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={form.control}
                            name="initialPrice"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Prix initial (SOL)</FormLabel>
                                <FormControl>
                                  <Input type="number" step="0.000001" min="0.000001" {...field} />
                                </FormControl>
                                <FormDescription>Le prix de départ de votre token, en SOL</FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="reserveRatio"
                            render={({ field: { value, onChange, ...field } }) => (
                              <FormItem>
                                <FormLabel>Ratio de réserve: {(value * 100).toFixed(0)}%</FormLabel>
                                <FormControl>
                                  <Slider
                                    min={0.1}
                                    max={1}
                                    step={0.01}
                                    value={[value]}
                                    onValueChange={(vals) => onChange(vals[0])}
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Détermine la pente de la courbe. Une valeur plus basse rend la courbe plus abrupte.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name="initialSupply"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Offre initiale</FormLabel>
                                  <FormControl>
                                    <Input type="number" min="1" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="maxSupply"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Offre maximale</FormLabel>
                                  <FormControl>
                                    <Input type="number" min="1" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={form.control}
                            name="taxRate"
                            render={({ field: { value, onChange, ...field } }) => (
                              <FormItem>
                                <FormLabel>Taxe sur les transactions: {(value * 100).toFixed(1)}%</FormLabel>
                                <FormControl>
                                  <Slider
                                    min={0}
                                    max={0.2}
                                    step={0.005}
                                    value={[value]}
                                    onValueChange={(vals) => onChange(vals[0])}
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Taxe appliquée à chaque transaction, utilisée pour le développement et le marketing
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="security" className="space-y-4 pt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Paramètres de sécurité</CardTitle>
                          <CardDescription>
                            Configurez les protections pour votre token et vos investisseurs
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <FormField
                            control={form.control}
                            name="antiBot"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Protection anti-bot</FormLabel>
                                  <FormDescription>
                                    Empêche les bots de manipuler le marché lors du lancement
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="antiDump"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Protection anti-dump</FormLabel>
                                  <FormDescription>
                                    Limite les ventes massives qui pourraient faire chuter le prix
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="liquidityLock"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Verrouillage de la liquidité (jours)</FormLabel>
                                <FormControl>
                                  <Input type="number" min="0" {...field} />
                                </FormControl>
                                <FormDescription>
                                  Durée pendant laquelle la liquidité sera verrouillée. 0 = pas de verrouillage.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>

                  <div className="flex justify-between">
                    <Button type="button" variant="outline" onClick={() => router.back()}>
                      Annuler
                    </Button>
                    <Button type="submit">Créer le token</Button>
                  </div>
                </form>
              </Form>
            </div>

            <div className="space-y-6">
              <Card className="h-[400px]">
                <CardHeader>
                  <CardTitle>Prévisualisation de la courbe</CardTitle>
                </CardHeader>
                <CardContent>
                  <BondingCurveChart params={bondingCurveParams} />
                </CardContent>
              </Card>

              <Card className="h-[400px]">
                <CardHeader>
                  <CardTitle>Assistant</CardTitle>
                  <CardDescription>Posez vos questions sur la création de token avec Bonding Curve</CardDescription>
                </CardHeader>
                <CardContent className="h-[320px]">
                  <TokenChatInterface onSendMessage={handleSendMessage} />
                </CardContent>
              </Card>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

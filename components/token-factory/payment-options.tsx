"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useWallet } from "@solana/wallet-adapter-react"
import { Connection, Transaction } from "@solana/web3.js"
import { envConfig } from "@/lib/env-config"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, CreditCard, Wallet } from "lucide-react"

interface PaymentOptionsProps {
  price: number
  onPaymentComplete?: (signature: string) => void
}

export function PaymentOptions({ price, onPaymentComplete }: PaymentOptionsProps) {
  const { publicKey, signTransaction, sendTransaction } = useWallet()
  const [isProcessing, setIsProcessing] = useState(false)
  const { toast } = useToast()

  const handlePayment = async () => {
    if (!publicKey || !signTransaction || !sendTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour effectuer un paiement.",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    try {
      // Créer une transaction de paiement
      const response = await fetch("/api/token/create-payment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          payerAddress: publicKey.toString(),
          amount: price,
        }),
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || "Échec de la création de la transaction de paiement")
      }

      // Décoder la transaction
      const transaction = Transaction.from(Buffer.from(result.transaction, "base64"))

      // Signer la transaction
      const signedTransaction = await signTransaction(transaction)

      // Envoyer la transaction
      const connection = new Connection(envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com", "confirmed")
      const signature = await sendTransaction(signedTransaction, connection)

      // Attendre la confirmation
      await connection.confirmTransaction(signature, "confirmed")

      toast({
        title: "Paiement effectué",
        description: `Votre paiement de ${price} SOL a été effectué avec succès.`,
      })

      // Appeler le callback
      if (onPaymentComplete) {
        onPaymentComplete(signature)
      }
    } catch (error: any) {
      console.error("Erreur lors du paiement:", error)
      toast({
        title: "Échec du paiement",
        description: error.message || "Une erreur s'est produite lors du paiement.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSimulatedPayment = () => {
    // Pour le développement, simuler un paiement
    if (onPaymentComplete) {
      onPaymentComplete("simulated_payment")
    }

    toast({
      title: "Paiement simulé",
      description: "Un paiement simulé a été effectué pour le développement.",
    })
  }

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handlePayment}
        disabled={isProcessing || !publicKey}
        className="flex items-center gap-2"
      >
        {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Wallet className="h-4 w-4" />}
        Payer {price} SOL
      </Button>
      {process.env.NODE_ENV === "development" && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSimulatedPayment}
          className="flex items-center gap-2 text-xs text-muted-foreground"
        >
          <CreditCard className="h-3 w-3" />
          Simuler
        </Button>
      )}
    </div>
  )
}

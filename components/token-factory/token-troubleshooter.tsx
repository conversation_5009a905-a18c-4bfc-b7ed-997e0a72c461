"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle2, AlertCircle, RefreshCw, ArrowRight, Clock, Shield } from "lucide-react"
import { Connection } from "@solana/web3.js"
import { useWallet } from "@solana/wallet-adapter-react"
import { envConfig } from "@/lib/env-config"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"
import TokenSuffixService from "@/lib/token-suffix-service"
import { findBestRpcEndpoint, checkRpcConnection } from "@/lib/rpc-connection-utils"
import { Progress } from "@/components/ui/progress"

interface RpcStatus {
  connected: boolean
  endpoint: string
  latency?: number
  version?: string
  error?: string
}

export default function TokenTroubleshooter() {
  const { publicKey, connected } = useWallet()
  const [rpcStatus, setRpcStatus] = useState<RpcStatus | null>(null)
  const [isCheckingRpc, setIsCheckingRpc] = useState(false)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const [isCheckingBalance, setIsCheckingBalance] = useState(false)
  const [suffixStatus, setSuffixStatus] = useState<{ loaded: boolean; suffix: string | null; error: string | null }>({
    loaded: false,
    suffix: null,
    error: null,
  })
  const [isCheckingSuffix, setIsCheckingSuffix] = useState(false)
  const [keypairStatus, setKeypairStatus] = useState<{
    tested: boolean
    success: boolean
    error: string | null
    publicKey: string | null
    attempts: number
    inProgress: boolean
    progress: number
  }>({
    tested: false,
    success: false,
    error: null,
    publicKey: null,
    attempts: 0,
    inProgress: false,
    progress: 0,
  })
  const [isTestingKeypair, setIsTestingKeypair] = useState(false)
  const { toast } = useToast()
  const { activeNetwork } = useNetwork()

  // Check RPC connection
  const checkRpc = async () => {
    setIsCheckingRpc(true)
    try {
      const status = await checkRpcConnection(envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com")
      setRpcStatus(status)

      if (!status.connected) {
        // Try to find a better RPC endpoint
        const bestEndpoint = await findBestRpcEndpoint()
        if (bestEndpoint) {
          toast({
            title: "Meilleur point de terminaison RPC trouvé",
            description: `Nous avons trouvé un meilleur point de terminaison RPC: ${bestEndpoint}`,
          })

          // Check the best endpoint
          const bestStatus = await checkRpcConnection(bestEndpoint)
          setRpcStatus(bestStatus)
        }
      }
    } catch (error) {
      console.error("Error checking RPC:", error)
      setRpcStatus({
        connected: false,
        endpoint: envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com",
        error: "Erreur lors de la vérification de la connexion RPC",
      })
    } finally {
      setIsCheckingRpc(false)
    }
  }

  // Check wallet balance
  const checkBalance = async () => {
    if (!publicKey) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour vérifier votre solde.",
        variant: "destructive",
      })
      return
    }

    setIsCheckingBalance(true)
    try {
      const connection = new Connection(envConfig.SOLANA_RPC_URL || "https://api.devnet.solana.com", "confirmed")
      const balance = await connection.getBalance(publicKey)
      setWalletBalance(balance / 1e9) // Convert to SOL
    } catch (error) {
      console.error("Error checking balance:", error)
      setWalletBalance(null)
      toast({
        title: "Erreur de vérification du solde",
        description: "Impossible de récupérer le solde du portefeuille.",
        variant: "destructive",
      })
    } finally {
      setIsCheckingBalance(false)
    }
  }

  // Check suffix configuration
  const checkSuffix = async () => {
    setIsCheckingSuffix(true)
    try {
      const suffix = await TokenSuffixService.getSuffixForNetwork(activeNetwork?.id || "solana-devnet")
      setSuffixStatus({
        loaded: true,
        suffix,
        error: null,
      })
    } catch (error: any) {
      console.error("Error checking suffix:", error)
      setSuffixStatus({
        loaded: true,
        suffix: null,
        error: error.message || "Erreur lors de la vérification du suffixe",
      })
    } finally {
      setIsCheckingSuffix(false)
    }
  }

  // Test keypair generation
  const testKeypairGeneration = async () => {
    setIsTestingKeypair(true)
    setKeypairStatus((prev) => ({ ...prev, inProgress: true, progress: 0, attempts: 0 }))

    try {
      const suffix = suffixStatus.suffix || "GF"

      // Utiliser un nombre beaucoup plus élevé de tentatives
      const maxAttempts = 5000000

      // Utiliser la fonction getKeypairWithSuffix qui vérifie d'abord le cache
      const result = await TokenSuffixService.getKeypairWithSuffix(suffix)

      if (result.success && result.keypair) {
        setKeypairStatus({
          tested: true,
          success: true,
          error: null,
          publicKey: result.keypair.publicKey,
          attempts: result.attempts || 0,
          inProgress: false,
          progress: 100,
        })

        toast({
          title: "Génération de paire de clés réussie",
          description: `Adresse générée: ${result.keypair.publicKey.slice(0, 10)}...${result.keypair.publicKey.slice(-5)}`,
        })
      } else {
        setKeypairStatus({
          tested: true,
          success: false,
          error: result.error || "Échec de la génération de la paire de clés",
          publicKey: null,
          attempts: result.attempts || 0,
          inProgress: false,
          progress: 0,
        })
      }
    } catch (error: any) {
      console.error("Error testing keypair generation:", error)
      setKeypairStatus({
        tested: true,
        success: false,
        error: error.message || "Erreur lors du test de génération de la paire de clés",
        publicKey: null,
        attempts: 0,
        inProgress: false,
        progress: 0,
      })
    } finally {
      setIsTestingKeypair(false)
    }
  }

  // Run all checks
  const runAllChecks = async () => {
    await checkRpc()
    if (connected) {
      await checkBalance()
    }
    await checkSuffix()
    await testKeypairGeneration()
  }

  // Run initial checks on component mount
  useEffect(() => {
    runAllChecks()
  }, [])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Dépannage de la création de token</CardTitle>
          <CardDescription>
            Utilisez cet outil pour diagnostiquer et résoudre les problèmes de création de token
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* RPC Connection Status */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">État de la connexion RPC</h3>
              <Button variant="outline" size="sm" onClick={checkRpc} disabled={isCheckingRpc}>
                {isCheckingRpc ? <RefreshCw className="h-4 w-4 animate-spin" /> : "Vérifier la connexion"}
              </Button>
            </div>

            {rpcStatus && (
              <Alert variant={rpcStatus.connected ? "default" : "destructive"}>
                {rpcStatus.connected ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                <AlertTitle>{rpcStatus.connected ? "Connecté" : "Non connecté"}</AlertTitle>
                <AlertDescription>
                  {rpcStatus.connected
                    ? `Connexion réussie à ${rpcStatus.endpoint} (Latence: ${rpcStatus.latency}ms)`
                    : `Impossible de se connecter à ${rpcStatus.endpoint}: ${rpcStatus.error}`}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Wallet Status */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">État du portefeuille</h3>
              <Button variant="outline" size="sm" onClick={checkBalance} disabled={isCheckingBalance || !connected}>
                {isCheckingBalance ? <RefreshCw className="h-4 w-4 animate-spin" /> : "Vérifier le solde"}
              </Button>
            </div>

            <Alert variant={connected ? "default" : "destructive"}>
              {connected ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
              <AlertTitle>{connected ? "Portefeuille connecté" : "Portefeuille non connecté"}</AlertTitle>
              <AlertDescription>
                {connected
                  ? `Adresse: ${publicKey?.toString().slice(0, 6)}...${publicKey?.toString().slice(-4)}`
                  : "Veuillez connecter votre portefeuille pour continuer"}
              </AlertDescription>
            </Alert>

            {connected && walletBalance !== null && (
              <Alert variant={walletBalance >= 0.001 ? "default" : "destructive"}>
                {walletBalance >= 0.001 ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                <AlertTitle>Solde du portefeuille</AlertTitle>
                <AlertDescription>
                  {walletBalance.toFixed(6)} SOL
                  {walletBalance < 0.001 && " (Insuffisant pour créer un token, minimum requis: 0.001 SOL)"}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Suffix Configuration */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Configuration du suffixe</h3>
              <Button variant="outline" size="sm" onClick={checkSuffix} disabled={isCheckingSuffix}>
                {isCheckingSuffix ? <RefreshCw className="h-4 w-4 animate-spin" /> : "Vérifier le suffixe"}
              </Button>
            </div>

            {suffixStatus.loaded && (
              <Alert variant={suffixStatus.suffix ? "default" : "destructive"}>
                {suffixStatus.suffix ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                <AlertTitle>{suffixStatus.suffix ? "Suffixe configuré" : "Erreur de suffixe"}</AlertTitle>
                <AlertDescription>
                  {suffixStatus.suffix
                    ? `Les tokens créés auront une adresse se terminant par "${suffixStatus.suffix}"`
                    : suffixStatus.error || "Erreur lors de la récupération du suffixe"}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Keypair Generation Test */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Test de génération de paire de clés</h3>
              <Button variant="outline" size="sm" onClick={testKeypairGeneration} disabled={isTestingKeypair}>
                {isTestingKeypair ? <RefreshCw className="h-4 w-4 animate-spin" /> : "Tester la génération"}
              </Button>
            </div>

            {keypairStatus.inProgress && (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Génération en cours...</span>
                  <span>{keypairStatus.attempts.toLocaleString()} tentatives</span>
                </div>
                <Progress value={keypairStatus.progress} className="h-2" />
              </div>
            )}

            {keypairStatus.tested && !keypairStatus.inProgress && (
              <Alert variant={keypairStatus.success ? "default" : "destructive"}>
                {keypairStatus.success ? <CheckCircle2 className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                <AlertTitle>{keypairStatus.success ? "Génération réussie" : "Échec de la génération"}</AlertTitle>
                <AlertDescription>
                  {keypairStatus.success
                    ? `Paire de clés générée avec succès: ${keypairStatus.publicKey}`
                    : keypairStatus.error || "Erreur lors de la génération de la paire de clés"}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Common Issues and Solutions */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Problèmes courants et solutions</h3>

            <div className="space-y-4">
              <div className="flex items-start space-x-2">
                <Clock className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Temps de génération d'adresse</h4>
                  <p className="text-sm text-muted-foreground">
                    La génération d'une adresse avec un suffixe spécifique peut prendre du temps, surtout pour les
                    suffixes plus longs. Nous utilisons maintenant un système de cache qui pré-génère des adresses en
                    arrière-plan pour accélérer le processus.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <Shield className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Problèmes de signature</h4>
                  <p className="text-sm text-muted-foreground">
                    Si vous rencontrez des erreurs de signature comme "unknown signer", assurez-vous que votre
                    portefeuille est compatible avec la signature de transactions et que vous utilisez la dernière
                    version de votre extension de portefeuille.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Erreurs de transaction</h4>
                  <p className="text-sm text-muted-foreground">
                    Les erreurs de transaction peuvent être causées par des problèmes de réseau, des frais insuffisants,
                    ou des problèmes avec le programme de token. Essayez de réduire la complexité de votre token ou
                    attendez que le réseau soit moins congestionné.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={runAllChecks}>
            Exécuter tous les tests
          </Button>
          <Button onClick={() => (window.location.href = "/token-factory")}>
            Retourner à la création de token
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

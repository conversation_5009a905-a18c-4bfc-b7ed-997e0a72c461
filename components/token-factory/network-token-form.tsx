"use client"

import { useState, useEffect } from "react"
import { useNetwork } from "@/contexts/network-context"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import TokenForm from "@/components/token-factory/token-form"
import BnbTokenForm from "@/components/token-factory/bnb-token-form"
import type { NetworkType } from "@/lib/network-config"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

interface NetworkTokenFormProps {
  onCreateToken: (data: any) => void
}

export default function NetworkTokenForm({ onCreateToken }: NetworkTokenFormProps) {
  const { activeNetwork } = useNetwork()
  const [networkType, setNetworkType] = useState<NetworkType>("solana")
  const [error, setError] = useState<string | null>(null)

  // Update network type when active network changes
  useEffect(() => {
    if (activeNetwork) {
      setNetworkType(activeNetwork.type)
    }
  }, [activeNetwork])

  // Handle network change
  const handleNetworkChange = (value: string) => {
    setNetworkType(value as NetworkType)
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue={networkType} onValueChange={handleNetworkChange}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="solana">Solana Token</TabsTrigger>
          <TabsTrigger value="bnb">BNB Chain Token</TabsTrigger>
        </TabsList>
        <TabsContent value="solana">
          <TokenForm onCreateToken={onCreateToken} />
        </TabsContent>
        <TabsContent value="bnb">
          <BnbTokenForm onCreateToken={onCreateToken} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

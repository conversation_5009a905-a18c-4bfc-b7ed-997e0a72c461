"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2 } from "lucide-react"
import { grindSuffixAction } from "@/app/grind-suffix-action"
import { useWallet } from "@solana/wallet-adapter-react"
import { Connection, Keypair, Transaction } from "@solana/web3.js"
import { envConfig } from "@/lib/env-config"
import { useToast } from "@/components/ui/use-toast"
import {
  createInitializeMintInstruction,
  TOKEN_PROGRAM_ID,
  getOrCreateAssociatedTokenAccount,
  mintTo,
} from "@solana/spl-token"
import { useTokenRegistry } from "@/lib/token-registry"
import { useRouter } from "next/navigation"
// Ajouter l'import pour le service de suffixe
import TokenSuffixService from "@/lib/token-suffix-service"
import { useNetwork } from "@/contexts/network-context"
// Add the import at the top
import { diagnoseTokenCreationIssue } from "@/lib/token-creation-diagnostics"

interface BasicTokenFormProps {
  onCreateToken: (data: any) => void
}

export default function BasicTokenForm({ onCreateToken }: BasicTokenFormProps) {
  const { publicKey, signTransaction, sendTransaction } = useWallet()
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState("9")
  const [tokenSupply, setTokenSupply] = useState("**********")
  const [tokenDescription, setTokenDescription] = useState("")
  const [tokenWebsite, setTokenWebsite] = useState("")
  const [tokenTwitter, setTokenTwitter] = useState("")
  const [tokenTelegram, setTokenTelegram] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const { toast } = useToast()
  const [mintAddress, setMintAddress] = useState<string | null>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null) // Initialize walletBalance
  const addToken = useTokenRegistry((state) => state.addToken)
  const router = useRouter()
  // Dans le composant BasicTokenForm, ajouter :
  const { activeNetwork } = useNetwork()
  const [tokenSuffix, setTokenSuffix] = useState("")

  // Check if wallet balance is sufficient
  const getTokenPrice = () => {
    return 0.001 // Minimum SOL required
  }

  useEffect(() => {
    const checkBalance = async () => {
      if (publicKey) {
        try {
          const connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")
          const balance = await connection.getBalance(publicKey)
          setWalletBalance(balance / 1e9) // Convertir en SOL
        } catch (err: any) {
          console.error("Error checking wallet balance:", err)
          setWalletBalance(null)
          toast({
            title: "Wallet Balance Error",
            description: err.message || "Failed to retrieve wallet balance.",
            variant: "destructive",
          })
        }
      } else {
        setWalletBalance(null)
      }
    }

    checkBalance()
  }, [publicKey])

  // Ajouter cet useEffect après les autres useEffect
  useEffect(() => {
    const loadSuffix = async () => {
      try {
        const suffix = await TokenSuffixService.getSuffixForNetwork(activeNetwork.id)
        console.log(`Loaded suffix for network ${activeNetwork.id}: ${suffix}`)
        setTokenSuffix(suffix)
      } catch (error) {
        console.error("Error loading suffix:", error)
        setTokenSuffix("GF") // Default to GF as fallback
      }
    }

    loadSuffix()
  }, [activeNetwork])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    if (!publicKey || !signTransaction || !sendTransaction) {
      setError("Wallet not connected or missing features")
      toast({
        title: "Wallet Error",
        description: "Please connect your wallet and ensure it supports signing transactions.",
        variant: "destructive",
      })
      setIsLoading(false)
      return
    }

    // Check if wallet balance is sufficient
    const requiredBalance = getTokenPrice() // Minimum SOL required
    if (walletBalance === null || walletBalance < requiredBalance) {
      setError(`Insufficient SOL balance. Please add at least ${requiredBalance} SOL to your wallet.`)
      toast({
        title: "Insufficient Balance",
        description: `Please add at least ${requiredBalance} SOL to your wallet to cover transaction fees.`,
        variant: "destructive",
      })
      setIsLoading(false)
      return
    }

    try {
      // Ensure we're using the correct suffix
      const actualSuffix = tokenSuffix || "GF"
      console.log(`Using suffix for grinding: ${actualSuffix}`)

      // Call the grinding action with the suffix
      const grindingResult = await grindSuffixAction(actualSuffix, activeNetwork.id)

      if (!grindingResult || !grindingResult.success || !grindingResult.keypair) {
        setError(grindingResult?.error || "Failed to retrieve keypair for the configured suffix")
        toast({
          title: "Keypair Error",
          description: grindingResult?.error || "Failed to retrieve keypair for the configured suffix.",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      const mintKeypair = Keypair.fromSecretKey(Uint8Array.from(grindingResult.keypair.secret))

      // 2. Connect to Solana Devnet
      const connection = new Connection(envConfig.SOLANA_RPC_URL, "confirmed")

      // 3. Initialize the mint
      const initMintInstruction = createInitializeMintInstruction(
        mintKeypair.publicKey,
        Number(tokenDecimals),
        publicKey,
        publicKey,
        TOKEN_PROGRAM_ID,
      )

      // 4. Create a transaction
      const transaction = new Transaction()
      transaction.add(initMintInstruction)

      // 5. Get recent blockhash
      const { blockhash } = await connection.getLatestBlockhash("confirmed")
      transaction.recentBlockhash = blockhash

      // 6. Set the fee payer
      transaction.feePayer = publicKey

      // 7. Sign the transaction
      let signedTransaction
      try {
        console.log("Transaction before signing:", transaction)
        signedTransaction = await signTransaction(transaction)
        if (!signedTransaction) {
          throw new Error("Transaction signature failed")
        }
      } catch (signError: any) {
        console.error("Transaction signature failed:", signError)
        setError(`Transaction signature failed: ${signError.message}`)
        toast({
          title: "Transaction Signature Failed",
          description: signError.message || "Failed to sign the transaction.",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      // 8. Send the transaction
      let transactionId
      try {
        transactionId = await sendTransaction(signedTransaction, connection)
        console.log(`Transaction submitted: ${transactionId}`)
      } catch (sendError: any) {
        console.error("Transaction send failed:", sendError)
        console.error("Raw sendError object:", sendError)
        console.error("Decoded transaction:", transaction)
        console.error("Environment variables:", {
          SOLANA_RPC_URL: envConfig.SOLANA_RPC_URL,
          TOKEN_PROGRAM_ID: envConfig.TOKEN_PROGRAM_ID,
        })
        setError(`Transaction send failed: ${sendError.message}`)
        toast({
          title: "Transaction Send Failed",
          description: sendError.message || "Failed to send the transaction.",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      // 9. Get the token account of the creator address
      const tokenAccount = await getOrCreateAssociatedTokenAccount(
        connection,
        Keypair.fromSecretKey(Uint8Array.from(grindingResult.keypair.secret)), // Use the pre-generated keypair
        mintKeypair.publicKey,
        publicKey, // Use the connected wallet's public key
      )

      // 10. Mint the initial supply to the creator
      const initialAmount = Number(tokenSupply) * Math.pow(10, Number(tokenDecimals))
      const mintTx = await mintTo(
        connection,
        Keypair.fromSecretKey(Uint8Array.from(grindingResult.keypair.secret)), // Use the pre-generated keypair
        mintKeypair.publicKey,
        tokenAccount.address,
        publicKey, // Mint authority
        initialAmount,
      )

      const tokenData = {
        tokenName,
        tokenSymbol,
        tokenDecimals,
        tokenSupply,
        tokenDescription,
        tokenWebsite,
        tokenTwitter,
        tokenTelegram,
        mintAddress: mintKeypair.publicKey.toString(),
      }

      onCreateToken(tokenData)
      setSuccess(`Token ${tokenName} (${tokenSymbol}) created successfully!`)
    } catch (err: any) {
      console.error("Error creating token:", err)

      // Run diagnostics on the error
      const diagnostic = await diagnoseTokenCreationIssue(err)

      // Construct detailed error message
      let errorMessage = err.message || "Failed to create token. Please try again."
      if (diagnostic.issues.length > 0) {
        errorMessage += "\n\nPossible issues detected:\n• " + diagnostic.issues.join("\n• ")
      }
      if (diagnostic.suggestions.length > 0) {
        errorMessage += "\n\nSuggestions:\n• " + diagnostic.suggestions.join("\n• ")
      }

      setError(errorMessage)
      toast({
        title: "Token Creation Failed",
        description: "See the error details below for more information.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="tokenName">Token Name</Label>
        <Input
          id="tokenName"
          placeholder="e.g. My Token"
          value={tokenName}
          onChange={(e) => setTokenName(e.target.value)}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tokenSymbol">Token Symbol</Label>
        <Input
          id="tokenSymbol"
          placeholder="e.g. MTK"
          value={tokenSymbol}
          onChange={(e) => setTokenSymbol(e.target.value)}
          required
          maxLength={10}
        />
      </div>
      {/* Ajouter après la section "Token Symbol" dans le formulaire : */}
      <div className="mt-1 text-xs text-muted-foreground">
        Le symbole sera automatiquement suffixé avec "{tokenSuffix}"
      </div>

      <div className="space-y-2">
        <Label htmlFor="tokenDecimals">Decimals</Label>
        <Input
          id="tokenDecimals"
          type="number"
          min="0"
          max="9"
          value={tokenDecimals}
          onChange={(e) => setTokenDecimals(e.target.value)}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tokenSupply">Initial Supply</Label>
        <Input
          id="tokenSupply"
          type="number"
          min="1"
          value={tokenSupply}
          onChange={(e) => setTokenSupply(e.target.value)}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tokenDescription">Description (Optional)</Label>
        <Textarea
          id="tokenDescription"
          placeholder="Describe your token..."
          value={tokenDescription}
          onChange={(e) => setTokenDescription(e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tokenWebsite">Website (Optional)</Label>
        <Input
          id="tokenWebsite"
          placeholder="https://example.com"
          value={tokenWebsite}
          onChange={(e) => setTokenWebsite(e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tokenTwitter">Twitter (Optional)</Label>
        <Input
          id="tokenTwitter"
          placeholder="https://twitter.com/example"
          value={tokenTwitter}
          onChange={(e) => setTokenTwitter(e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tokenTelegram">Telegram (Optional)</Label>
        <Input
          id="tokenTelegram"
          placeholder="https://t.me/example"
          value={tokenTelegram}
          onChange={(e) => setTokenTelegram(e.target.value)}
        />
      </div>

      <Button type="submit" disabled={isLoading}>
        {isLoading ? "Creating Token..." : "Create Token"}
      </Button>
    </form>
  )
}

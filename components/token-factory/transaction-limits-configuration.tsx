"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowDownUp, Clock, Wallet, ShieldAlert, X, Plus } from "lucide-react"

interface TransactionLimitsConfigurationProps {
  initialValues?: {
    enabled: boolean
    maxTransactionAmount: number
    maxWalletAmount: number
    cooldowns: {
      enabled: boolean
      buySeconds: number
      sellSeconds: number
    }
    blacklist: {
      enabled: boolean
      addresses: string[]
    }
    whitelist: {
      enabled: boolean
      addresses: string[]
    }
  }
  onChange?: (values: any) => void
}

export function TransactionLimitsConfiguration({ initialValues, onChange }: TransactionLimitsConfigurationProps) {
  const [enabled, setEnabled] = useState(initialValues?.enabled || false)
  const [maxTransactionAmount, setMaxTransactionAmount] = useState(initialValues?.maxTransactionAmount || 1)
  const [maxWalletAmount, setMaxWalletAmount] = useState(initialValues?.maxWalletAmount || 2)
  const [cooldowns, setCooldowns] = useState({
    enabled: initialValues?.cooldowns?.enabled || false,
    buySeconds: initialValues?.cooldowns?.buySeconds || 0,
    sellSeconds: initialValues?.cooldowns?.sellSeconds || 0,
  })
  const [blacklist, setBlacklist] = useState({
    enabled: initialValues?.blacklist?.enabled || false,
    addresses: initialValues?.blacklist?.addresses || [],
  })
  const [whitelist, setWhitelist] = useState({
    enabled: initialValues?.whitelist?.enabled || false,
    addresses: initialValues?.whitelist?.addresses || [],
  })
  const [newBlacklistAddress, setNewBlacklistAddress] = useState("")
  const [newWhitelistAddress, setNewWhitelistAddress] = useState("")

  // Mettre à jour le parent lorsque les valeurs changent
  const updateValues = (newValues: any) => {
    if (onChange) {
      onChange({
        enabled,
        maxTransactionAmount,
        maxWalletAmount,
        cooldowns,
        blacklist,
        whitelist,
        ...newValues,
      })
    }
  }

  // Mettre à jour les cooldowns
  const updateCooldowns = (field: keyof typeof cooldowns, value: any) => {
    const newCooldowns = { ...cooldowns, [field]: value }
    setCooldowns(newCooldowns)
    updateValues({ cooldowns: newCooldowns })
  }

  // Ajouter une adresse à la blacklist
  const addToBlacklist = () => {
    if (!newBlacklistAddress || blacklist.addresses.includes(newBlacklistAddress)) return

    const newBlacklist = {
      ...blacklist,
      addresses: [...blacklist.addresses, newBlacklistAddress],
    }
    setBlacklist(newBlacklist)
    setNewBlacklistAddress("")
    updateValues({ blacklist: newBlacklist })
  }

  // Supprimer une adresse de la blacklist
  const removeFromBlacklist = (address: string) => {
    const newBlacklist = {
      ...blacklist,
      addresses: blacklist.addresses.filter((a) => a !== address),
    }
    setBlacklist(newBlacklist)
    updateValues({ blacklist: newBlacklist })
  }

  // Ajouter une adresse à la whitelist
  const addToWhitelist = () => {
    if (!newWhitelistAddress || whitelist.addresses.includes(newWhitelistAddress)) return

    const newWhitelist = {
      ...whitelist,
      addresses: [...whitelist.addresses, newWhitelistAddress],
    }
    setWhitelist(newWhitelist)
    setNewWhitelistAddress("")
    updateValues({ whitelist: newWhitelist })
  }

  // Supprimer une adresse de la whitelist
  const removeFromWhitelist = (address: string) => {
    const newWhitelist = {
      ...whitelist,
      addresses: whitelist.addresses.filter((a) => a !== address),
    }
    setWhitelist(newWhitelist)
    updateValues({ whitelist: newWhitelist })
  }

  // Formater les secondes en minutes et secondes
  const formatSeconds = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShieldAlert className="h-5 w-5" />
          Limites de transaction
        </CardTitle>
        <CardDescription>Définissez des limites pour les transactions et les portefeuilles</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">Activer les limites</h3>
            <p className="text-sm text-muted-foreground">
              Appliquer des limites sur les transactions et les portefeuilles
            </p>
          </div>
          <Switch
            checked={enabled}
            onCheckedChange={(checked) => {
              setEnabled(checked)
              updateValues({ enabled: checked })
            }}
          />
        </div>

        {enabled && (
          <Tabs defaultValue="limits" className="space-y-4">
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="limits">Limites</TabsTrigger>
              <TabsTrigger value="cooldowns">Délais</TabsTrigger>
              <TabsTrigger value="lists">Listes</TabsTrigger>
            </TabsList>

            <TabsContent value="limits" className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <Label htmlFor="max-transaction" className="flex items-center gap-2">
                    <ArrowDownUp className="h-4 w-4" /> Maximum par transaction
                  </Label>
                  <span className="text-sm">{maxTransactionAmount}% de l'offre</span>
                </div>
                <Slider
                  id="max-transaction"
                  value={[maxTransactionAmount]}
                  min={0.1}
                  max={5}
                  step={0.1}
                  onValueChange={(values) => {
                    setMaxTransactionAmount(values[0])
                    updateValues({ maxTransactionAmount: values[0] })
                  }}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Limite la quantité maximale de tokens par transaction
                </p>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <Label htmlFor="max-wallet" className="flex items-center gap-2">
                    <Wallet className="h-4 w-4" /> Maximum par portefeuille
                  </Label>
                  <span className="text-sm">{maxWalletAmount}% de l'offre</span>
                </div>
                <Slider
                  id="max-wallet"
                  value={[maxWalletAmount]}
                  min={0.1}
                  max={10}
                  step={0.1}
                  onValueChange={(values) => {
                    setMaxWalletAmount(values[0])
                    updateValues({ maxWalletAmount: values[0] })
                  }}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Limite la quantité maximale de tokens qu'un portefeuille peut détenir
                </p>
              </div>
            </TabsContent>

            <TabsContent value="cooldowns" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Activer les délais</h3>
                  <p className="text-sm text-muted-foreground">Imposer un délai entre les transactions</p>
                </div>
                <Switch
                  checked={cooldowns.enabled}
                  onCheckedChange={(checked) => updateCooldowns("enabled", checked)}
                />
              </div>

              {cooldowns.enabled && (
                <div className="space-y-4 pl-6 border-l-2 border-blue-100">
                  <div>
                    <div className="flex justify-between mb-1">
                      <Label htmlFor="buy-cooldown" className="flex items-center gap-2">
                        <Clock className="h-4 w-4" /> Délai entre achats
                      </Label>
                      <span className="text-sm">{formatSeconds(cooldowns.buySeconds)}</span>
                    </div>
                    <Slider
                      id="buy-cooldown"
                      value={[cooldowns.buySeconds]}
                      min={0}
                      max={300}
                      step={10}
                      onValueChange={(values) => updateCooldowns("buySeconds", values[0])}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Temps d'attente obligatoire entre deux achats pour un même portefeuille
                    </p>
                  </div>

                  <div>
                    <div className="flex justify-between mb-1">
                      <Label htmlFor="sell-cooldown" className="flex items-center gap-2">
                        <Clock className="h-4 w-4" /> Délai entre ventes
                      </Label>
                      <span className="text-sm">{formatSeconds(cooldowns.sellSeconds)}</span>
                    </div>
                    <Slider
                      id="sell-cooldown"
                      value={[cooldowns.sellSeconds]}
                      min={0}
                      max={300}
                      step={10}
                      onValueChange={(values) => updateCooldowns("sellSeconds", values[0])}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Temps d'attente obligatoire entre deux ventes pour un même portefeuille
                    </p>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="lists" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Blacklist</h3>
                    <p className="text-sm text-muted-foreground">
                      Bloquer certaines adresses pour les empêcher d'interagir avec le token
                    </p>
                  </div>
                  <Switch
                    checked={blacklist.enabled}
                    onCheckedChange={(checked) => {
                      const newBlacklist = { ...blacklist, enabled: checked }
                      setBlacklist(newBlacklist)
                      updateValues({ blacklist: newBlacklist })
                    }}
                  />
                </div>

                {blacklist.enabled && (
                  <div className="space-y-2 pl-6 border-l-2 border-red-100">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Adresse à blacklister"
                        value={newBlacklistAddress}
                        onChange={(e) => setNewBlacklistAddress(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault()
                            addToBlacklist()
                          }
                        }}
                      />
                      <Button variant="outline" onClick={addToBlacklist} className="shrink-0">
                        <Plus className="h-4 w-4 mr-2" />
                        Ajouter
                      </Button>
                    </div>

                    <div className="max-h-[150px] overflow-y-auto space-y-2">
                      {blacklist.addresses.map((address) => (
                        <div key={address} className="flex justify-between items-center p-2 bg-red-50 rounded-md">
                          <span className="text-sm truncate max-w-[80%]">{address}</span>
                          <Button variant="ghost" size="sm" onClick={() => removeFromBlacklist(address)}>
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {blacklist.addresses.length === 0 && (
                        <p className="text-sm text-muted-foreground">Aucune adresse blacklistée</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Whitelist</h3>
                    <p className="text-sm text-muted-foreground">
                      Autoriser uniquement certaines adresses à interagir avec le token
                    </p>
                  </div>
                  <Switch
                    checked={whitelist.enabled}
                    onCheckedChange={(checked) => {
                      const newWhitelist = { ...whitelist, enabled: checked }
                      setWhitelist(newWhitelist)
                      updateValues({ whitelist: newWhitelist })
                    }}
                  />
                </div>

                {whitelist.enabled && (
                  <div className="space-y-2 pl-6 border-l-2 border-green-100">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Adresse à whitelister"
                        value={newWhitelistAddress}
                        onChange={(e) => setNewWhitelistAddress(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault()
                            addToWhitelist()
                          }
                        }}
                      />
                      <Button variant="outline" onClick={addToWhitelist} className="shrink-0">
                        <Plus className="h-4 w-4 mr-2" />
                        Ajouter
                      </Button>
                    </div>

                    <div className="max-h-[150px] overflow-y-auto space-y-2">
                      {whitelist.addresses.map((address) => (
                        <div key={address} className="flex justify-between items-center p-2 bg-green-50 rounded-md">
                          <span className="text-sm truncate max-w-[80%]">{address}</span>
                          <Button variant="ghost" size="sm" onClick={() => removeFromWhitelist(address)}>
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {whitelist.addresses.length === 0 && (
                        <p className="text-sm text-muted-foreground">Aucune adresse whitelistée</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

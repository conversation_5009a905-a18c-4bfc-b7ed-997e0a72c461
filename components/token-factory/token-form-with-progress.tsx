"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { useNetwork } from "@/contexts/network-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { AlertCircle, CheckCircle2, Lock, Coins, BarChart3, ArrowR<PERSON> } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface TokenFormWithProgressProps {
  onCreateToken: (data: any) => void
}

// Token creation stages
type CreationStage =
  | "form"
  | "creating"
  | "created"
  | "preparing_dex"
  | "adding_liquidity"
  | "locking_liquidity"
  | "completed"

export default function TokenFormWithProgress({ onCreateToken }: TokenFormWithProgressProps) {
  const { publicKey, connected } = useWallet()
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()
  const router = useRouter()

  // Form state
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState(activeNetwork.type === "solana" ? "9" : "18")
  const [tokenSupply, setTokenSupply] = useState("1000000000")
  const [tokenDescription, setTokenDescription] = useState("")
  const [tokenWebsite, setTokenWebsite] = useState("")
  const [tokenTwitter, setTokenTwitter] = useState("")
  const [tokenTelegram, setTokenTelegram] = useState("")

  // Advanced options
  const [addLiquidity, setAddLiquidity] = useState(true)
  const [lockLiquidity, setLockLiquidity] = useState(true)
  const [lockDuration, setLockDuration] = useState(180) // days
  const [liquidityAmount, setLiquidityAmount] = useState("0.1") // in native currency (SOL/BNB)
  const [liquidityPercentage, setLiquidityPercentage] = useState(50) // % of tokens for liquidity
  const [antiBot, setAntiBot] = useState(false)
  const [maxTxAmount, setMaxTxAmount] = useState(1) // % of total supply
  const [maxWalletAmount, setMaxWalletAmount] = useState(2) // % of total supply

  // Creation state
  const [stage, setStage] = useState<CreationStage>("form")
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [tokenAddress, setTokenAddress] = useState<string | null>(null)
  const [pairAddress, setPairAddress] = useState<string | null>(null)
  const [lockAddress, setLockAddress] = useState<string | null>(null)
  const [tokenData, setTokenData] = useState<any>(null)
  const [step, setStep] = useState(1)

  // Wallet balance
  const [nativeBalance, setNativeBalance] = useState<number | null>(null)

  // Update decimals when network changes
  useEffect(() => {
    setTokenDecimals(activeNetwork.type === "solana" ? "9" : "18")
  }, [activeNetwork])

  // Progress bar animation
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (stage === "creating") {
      setProgress(10)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 30) {
            clearInterval(interval)
            return 30
          }
          return prev + 1
        })
      }, 100)
    } else if (stage === "created") {
      setProgress(40)
    } else if (stage === "preparing_dex") {
      setProgress(50)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 60) {
            clearInterval(interval)
            return 60
          }
          return prev + 1
        })
      }, 100)
    } else if (stage === "adding_liquidity") {
      setProgress(70)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(interval)
            return 80
          }
          return prev + 1
        })
      }, 100)
    } else if (stage === "locking_liquidity") {
      setProgress(90)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 95) {
            clearInterval(interval)
            return 95
          }
          return prev + 1
        })
      }, 100)
    } else if (stage === "completed") {
      setProgress(100)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [stage])

  // Calculate required balance
  const getRequiredBalance = () => {
    let required = 0

    // Base fee for token creation
    required += activeNetwork.type === "solana" ? 0.001 : 0.01

    // Liquidity amount if enabled
    if (addLiquidity) {
      required += Number.parseFloat(liquidityAmount)
    }

    return required
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!connected || !publicKey) {
      setError("Please connect your wallet")
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to create a token",
        variant: "destructive",
      })
      return
    }

    // Check if wallet has enough balance
    const requiredBalance = getRequiredBalance()
    if (nativeBalance === null || nativeBalance < requiredBalance) {
      setError(`Insufficient balance. You need at least ${requiredBalance} ${activeNetwork.nativeCurrency.symbol}`)
      toast({
        title: "Insufficient balance",
        description: `You need at least ${requiredBalance} ${activeNetwork.nativeCurrency.symbol} to create this token`,
        variant: "destructive",
      })
      return
    }

    // Start token creation process
    setStage("creating")

    try {
      // Simulate token creation
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // Set token address (in a real implementation, this would come from the API)
      const simulatedTokenAddress = `0x${Array.from({ length: 40 }, () => "0123456789abcdef"[Math.floor(Math.random() * 16)]).join("")}`
      setTokenAddress(simulatedTokenAddress)
      setStage("created")

      // If liquidity is enabled, simulate DEX listing
      if (addLiquidity) {
        setStage("preparing_dex")
        await new Promise((resolve) => setTimeout(resolve, 2000))

        setStage("adding_liquidity")
        await new Promise((resolve) => setTimeout(resolve, 3000))

        // Set pair address (in a real implementation, this would come from the API)
        const simulatedPairAddress = `0x${Array.from({ length: 40 }, () => "0123456789abcdef"[Math.floor(Math.random() * 16)]).join("")}`
        setPairAddress(simulatedPairAddress)

        // If liquidity locking is enabled, simulate locking
        if (lockLiquidity) {
          setStage("locking_liquidity")
          await new Promise((resolve) => setTimeout(resolve, 2000))

          // Set lock address (in a real implementation, this would come from the API)
          const simulatedLockAddress = `0x${Array.from({ length: 40 }, () => "0123456789abcdef"[Math.floor(Math.random() * 16)]).join("")}`
          setLockAddress(simulatedLockAddress)
        }
      }

      // Complete the process
      setStage("completed")

      // Notify parent component
      onCreateToken({
        tokenName,
        tokenSymbol,
        tokenDecimals,
        tokenSupply,
        tokenDescription,
        tokenWebsite,
        tokenTwitter,
        tokenTelegram,
        tokenAddress: simulatedTokenAddress,
        pairAddress: addLiquidity ? simulatedPairAddress : null,
        lockAddress: addLiquidity && lockLiquidity ? simulatedLockAddress : null,
        network: activeNetwork.id,
        networkType: activeNetwork.type,
        addedLiquidity: addLiquidity,
        lockedLiquidity: addLiquidity && lockLiquidity,
        lockDuration: addLiquidity && lockLiquidity ? lockDuration : null,
        antiBot,
        maxTxAmount: antiBot ? maxTxAmount : null,
        maxWalletAmount: antiBot ? maxWalletAmount : null,
      })

      toast({
        title: "Token created successfully",
        description: `Your token ${tokenName} (${tokenSymbol}) has been created`,
        variant: "default",
      })
    } catch (err: any) {
      console.error("Error creating token:", err)
      setError(err.message || "Failed to create token")
      setStage("form")

      toast({
        title: "Token creation failed",
        description: err.message || "Failed to create token",
        variant: "destructive",
      })
    }
  }

  const handleCreateToken = (data: any) => {
    setTokenData(data)
    setProgress(100)
    setStep(3) // Success step
  }

  const handleError = () => {
    setError(
      "Une erreur s'est produite lors de la création du token. Consultez la section de dépannage pour obtenir de l'aide.",
    )
    setStep(4) // Error step
  }

  const goToTroubleshoot = () => {
    router.push("/token-factory/troubleshoot")
  }

  // Render creation progress
  const renderProgress = () => {
    return (
      <div className="space-y-6">
        <Progress value={progress} className="h-2" />

        <div className="space-y-4">
          {/* Token Creation Step */}
          <div className="flex items-center gap-3">
            <div
              className={`rounded-full p-2 ${stage === "creating" ? "bg-yellow-500/20 text-yellow-500" : stage === "form" ? "bg-gray-500/20 text-gray-500" : "bg-green-500/20 text-green-500"}`}
            >
              <Coins className="h-5 w-5" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Creating Token</h3>
                {stage !== "form" && stage !== "creating" && <CheckCircle2 className="h-4 w-4 text-green-500" />}
              </div>
              {tokenAddress && (
                <p className="text-xs text-muted-foreground mt-1">
                  Token Address: {tokenAddress.substring(0, 8)}...{tokenAddress.substring(tokenAddress.length - 6)}
                </p>
              )}
            </div>
          </div>

          {/* DEX Listing Step */}
          {addLiquidity && (
            <div className="flex items-center gap-3">
              <div
                className={`rounded-full p-2 ${stage === "preparing_dex" || stage === "adding_liquidity" ? "bg-yellow-500/20 text-yellow-500" : stage === "form" || stage === "creating" || stage === "created" ? "bg-gray-500/20 text-gray-500" : "bg-green-500/20 text-green-500"}`}
              >
                <BarChart3 className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Adding Liquidity</h3>
                  {stage !== "form" &&
                    stage !== "creating" &&
                    stage !== "created" &&
                    stage !== "preparing_dex" &&
                    stage !== "adding_liquidity" && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                </div>
                {pairAddress && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Pair Address: {pairAddress.substring(0, 8)}...{pairAddress.substring(pairAddress.length - 6)}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Liquidity Locking Step */}
          {addLiquidity && lockLiquidity && (
            <div className="flex items-center gap-3">
              <div
                className={`rounded-full p-2 ${stage === "locking_liquidity" ? "bg-yellow-500/20 text-yellow-500" : stage === "form" || stage === "creating" || stage === "created" || stage === "preparing_dex" || stage === "adding_liquidity" ? "bg-gray-500/20 text-gray-500" : "bg-green-500/20 text-green-500"}`}
              >
                <Lock className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Locking Liquidity</h3>
                  {stage === "completed" && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                </div>
                {lockAddress && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Lock Address: {lockAddress.substring(0, 8)}...{lockAddress.substring(lockAddress.length - 6)}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {stage === "completed" && (
          <div className="pt-4">
            <Alert className="bg-green-50 text-green-800 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle>Success!</AlertTitle>
            </Alert>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Progress value={progress} className="w-full" />

      {step === 1 && (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Create Your Token</CardTitle>
            <CardDescription>Fill out the form below to create your own token.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {stage === "form" ? (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="tokenName">Token Name</Label>
                  <Input
                    id="tokenName"
                    value={tokenName}
                    onChange={(e) => setTokenName(e.target.value)}
                    placeholder="My Token"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="tokenSymbol">Token Symbol</Label>
                  <Input
                    id="tokenSymbol"
                    value={tokenSymbol}
                    onChange={(e) => setTokenSymbol(e.target.value)}
                    placeholder="MTK"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="tokenDecimals">Token Decimals</Label>
                  <Input
                    id="tokenDecimals"
                    type="number"
                    value={tokenDecimals}
                    onChange={(e) => setTokenDecimals(e.target.value)}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="tokenSupply">Token Supply</Label>
                  <Input
                    id="tokenSupply"
                    type="number"
                    value={tokenSupply}
                    onChange={(e) => setTokenSupply(e.target.value)}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="tokenDescription">Token Description</Label>
                  <Textarea
                    id="tokenDescription"
                    value={tokenDescription}
                    onChange={(e) => setTokenDescription(e.target.value)}
                    placeholder="A brief description of your token"
                  />
                </div>

                <Tabs defaultValue="general" className="w-full">
                  <TabsList>
                    <TabsTrigger value="general">General</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced</TabsTrigger>
                  </TabsList>
                  <TabsContent value="general" className="space-y-4">
                    <div className="grid gap-2">
                      <Label htmlFor="tokenWebsite">Website</Label>
                      <Input
                        id="tokenWebsite"
                        type="url"
                        value={tokenWebsite}
                        onChange={(e) => setTokenWebsite(e.target.value)}
                        placeholder="https://example.com"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="tokenTwitter">Twitter</Label>
                      <Input
                        id="tokenTwitter"
                        type="url"
                        value={tokenTwitter}
                        onChange={(e) => setTokenTwitter(e.target.value)}
                        placeholder="https://twitter.com/example"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="tokenTelegram">Telegram</Label>
                      <Input
                        id="tokenTelegram"
                        type="url"
                        value={tokenTelegram}
                        onChange={(e) => setTokenTelegram(e.target.value)}
                        placeholder="https://t.me/example"
                      />
                    </div>
                  </TabsContent>
                  <TabsContent value="advanced" className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="addLiquidity">Add Liquidity to DEX</Label>
                      <Switch id="addLiquidity" checked={addLiquidity} onCheckedChange={setAddLiquidity} />
                    </div>
                    {addLiquidity && (
                      <div className="space-y-2">
                        <div className="grid gap-2">
                          <Label htmlFor="liquidityAmount">
                            Liquidity Amount ({activeNetwork.nativeCurrency.symbol})
                          </Label>
                          <Input
                            id="liquidityAmount"
                            type="number"
                            value={liquidityAmount}
                            onChange={(e) => setLiquidityAmount(e.target.value)}
                            placeholder="0.1"
                          />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="liquidityPercentage">Liquidity Percentage (%)</Label>
                          <Slider
                            id="liquidityPercentage"
                            defaultValue={[liquidityPercentage]}
                            max={100}
                            step={1}
                            onValueChange={(value) => setLiquidityPercentage(value[0])}
                          />
                          <p className="text-sm text-muted-foreground">
                            {liquidityPercentage}% of tokens will be used for liquidity.
                          </p>
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="lockLiquidity">Lock Liquidity</Label>
                          <Switch id="lockLiquidity" checked={lockLiquidity} onCheckedChange={setLockLiquidity} />
                        </div>
                        {lockLiquidity && (
                          <div className="space-y-2">
                            <div className="grid gap-2">
                              <Label htmlFor="lockDuration">Lock Duration (Days)</Label>
                              <Input
                                id="lockDuration"
                                type="number"
                                value={lockDuration}
                                onChange={(e) => setLockDuration(e.target.value)}
                                placeholder="180"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <Label htmlFor="antiBot">Enable Anti-Bot Measures</Label>
                      <Switch id="antiBot" checked={antiBot} onCheckedChange={setAntiBot} />
                    </div>
                    {antiBot && (
                      <div className="space-y-2">
                        <div className="grid gap-2">
                          <Label htmlFor="maxTxAmount">Max Transaction Amount (%)</Label>
                          <Input
                            id="maxTxAmount"
                            type="number"
                            value={maxTxAmount}
                            onChange={(e) => setMaxTxAmount(e.target.value)}
                            placeholder="1"
                          />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor="maxWalletAmount">Max Wallet Amount (%)</Label>
                          <Input
                            id="maxWalletAmount"
                            type="number"
                            value={maxWalletAmount}
                            onChange={(e) => setMaxWalletAmount(e.target.value)}
                            placeholder="2"
                          />
                        </div>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>

                <Button type="submit">Create Token</Button>
              </form>
            ) : (
              renderProgress()
            )}
          </CardContent>
          {stage === "form" && (
            <CardFooter className="flex justify-end items-center">
              {connected ? (
                <div className="text-sm text-muted-foreground">
                  Your balance: {nativeBalance || 0} {activeNetwork.nativeCurrency.symbol}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">Connect your wallet to create a token</div>
              )}
            </CardFooter>
          )}
        </Card>
      )}

      {step === 3 && tokenData && (
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <CheckCircle2 className="h-6 w-6 text-green-500" />
              <CardTitle>Token created successfully!</CardTitle>
            </div>
            <CardDescription>Your token has been created successfully on the blockchain Solana.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">Token details</h3>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div className="text-sm font-medium">Name:</div>
                  <div className="text-sm">{tokenData.tokenName}</div>

                  <div className="text-sm font-medium">Symbol:</div>
                  <div className="text-sm">{tokenData.tokenSymbol}</div>

                  <div className="text-sm font-medium">Decimals:</div>
                  <div className="text-sm">{tokenData.tokenDecimals}</div>

                  <div className="text-sm font-medium">Initial supply:</div>
                  <div className="text-sm">{tokenData.tokenSupply}</div>

                  <div className="text-sm font-medium">Token address:</div>
                  <div className="text-sm break-all">{tokenData.mintAddress}</div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setStep(1)}>
              Create another token
            </Button>
            <Button onClick={() => router.push(`/token-factory/token/${tokenData.mintAddress}`)}>
              View token details
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      )}

      {step === 4 && (
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-6 w-6 text-red-500" />
              <CardTitle>Token creation error</CardTitle>
            </div>
            <CardDescription>
              An error occurred while creating your token. Please see the troubleshooting section for assistance.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setStep(1)}>
              Try again
            </Button>
            <Button onClick={goToTroubleshoot}>
              Troubleshoot
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}

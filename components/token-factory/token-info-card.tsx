"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Copy, Share2, ExternalLink } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface TokenInfoCardProps {
  name: string
  symbol: string
  address: string
  type: "standard" | "bonding" | "quantum"
  createdAt: string
  description?: string
  website?: string
  explorer?: string
}

export function TokenInfoCard({
  name,
  symbol,
  address,
  type,
  createdAt,
  description,
  website,
  explorer,
}: TokenInfoCardProps) {
  const { toast } = useToast()

  // Copier l'adresse du token dans le presse-papiers
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Adresse copiée",
      description: "L'adresse du token a été copiée dans le presse-papiers",
    })
  }

  // Partager le token sur les réseaux sociaux
  const shareToken = () => {
    const url = `${window.location.origin}/token-factory/token/${address}`
    const text = `Découvrez le token ${name} (${symbol}) sur la blockchain Solana!`

    if (navigator.share) {
      navigator
        .share({
          title: `Token ${name}`,
          text,
          url,
        })
        .catch(console.error)
    } else {
      // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
      window.open(
        `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`,
        "_blank",
      )
    }
  }

  // Obtenir la couleur du badge en fonction du type de token
  const getTokenTypeBadgeColor = (type: string) => {
    switch (type) {
      case "standard":
        return "bg-blue-100 text-blue-800"
      case "bonding":
        return "bg-purple-100 text-purple-800"
      case "quantum":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Obtenir le libellé du type de token
  const getTokenTypeLabel = (type: string) => {
    switch (type) {
      case "standard":
        return "Token Standard"
      case "bonding":
        return "Bonding Curve"
      case "quantum":
        return "Token Quantum"
      default:
        return type
    }
  }

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <div>
            <CardTitle className="flex items-center gap-2">
              {name}
              <Badge variant="outline" className={getTokenTypeBadgeColor(type)}>
                {getTokenTypeLabel(type)}
              </Badge>
            </CardTitle>
            <CardDescription>{symbol}</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => copyToClipboard(address)}>
              <Copy className="h-4 w-4 mr-2" />
              Copier
            </Button>
            <Button variant="outline" size="sm" onClick={shareToken}>
              <Share2 className="h-4 w-4 mr-2" />
              Partager
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="text-sm text-muted-foreground mb-1">Adresse du token</p>
          <div className="flex items-center">
            <code className="bg-muted p-2 rounded text-xs md:text-sm flex-1 overflow-x-auto">{address}</code>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Type</p>
            <p>{getTokenTypeLabel(type)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Créé le</p>
            <p>{formatDate(createdAt)}</p>
          </div>
        </div>

        {description && (
          <div>
            <p className="text-sm text-muted-foreground mb-1">Description</p>
            <p className="text-sm">{description}</p>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-2">
          {website && (
            <Button variant="outline" size="sm" asChild className="flex-1">
              <a href={website} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Site web
              </a>
            </Button>
          )}
          {explorer && (
            <Button variant="outline" size="sm" asChild className="flex-1">
              <a href={explorer} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Explorer
              </a>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

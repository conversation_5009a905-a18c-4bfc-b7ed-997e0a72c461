"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Share2, Globe, Twitter, MessageCircle } from "lucide-react"

interface MarketingConfigurationProps {
  tokenWebsite: string
  setTokenWebsite: (value: string) => void
  tokenTwitter: string
  setTokenTwitter: (value: string) => void
  tokenTelegram: string
  setTokenTelegram: (value: string) => void
  validationErrors: { [key: string]: string }
}

export function MarketingConfiguration({
  tokenWebsite,
  setTokenWebsite,
  tokenTwitter,
  setTokenTwitter,
  tokenTelegram,
  setTokenTelegram,
  validationErrors,
}: MarketingConfigurationProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Présence en ligne
          </CardTitle>
          <CardDescription>
            Configurez les liens vers votre site web et vos réseaux sociaux pour promouvoir votre token
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="website" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Site web (optionnel)
              </Label>
              <Input
                id="website"
                placeholder="https://montoken.com"
                value={tokenWebsite}
                onChange={(e) => setTokenWebsite(e.target.value)}
                className={validationErrors.website ? "border-red-500" : ""}
              />
              {validationErrors.website && <p className="text-xs text-red-500 mt-1">{validationErrors.website}</p>}
              <p className="text-xs text-muted-foreground">
                L'URL de votre site web doit commencer par http:// ou https://
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="twitter" className="flex items-center gap-2">
                <Twitter className="h-4 w-4" />
                Twitter (optionnel)
              </Label>
              <Input
                id="twitter"
                placeholder="https://twitter.com/montoken"
                value={tokenTwitter}
                onChange={(e) => setTokenTwitter(e.target.value)}
                className={validationErrors.twitter ? "border-red-500" : ""}
              />
              {validationErrors.twitter && <p className="text-xs text-red-500 mt-1">{validationErrors.twitter}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="telegram" className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4" />
                Telegram (optionnel)
              </Label>
              <Input
                id="telegram"
                placeholder="https://t.me/montoken"
                value={tokenTelegram}
                onChange={(e) => setTokenTelegram(e.target.value)}
                className={validationErrors.telegram ? "border-red-500" : ""}
              />
              {validationErrors.telegram && <p className="text-xs text-red-500 mt-1">{validationErrors.telegram}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="whitepaper" className="flex items-center gap-2">
                Whitepaper (optionnel)
              </Label>
              <Textarea
                id="whitepaper"
                placeholder="Décrivez votre projet, sa vision, sa feuille de route et ses objectifs..."
                rows={6}
              />
              <p className="text-xs text-muted-foreground">
                Un whitepaper bien rédigé peut aider à attirer des investisseurs et à établir la crédibilité de votre
                projet
              </p>
            </div>
          </div>

          <div className="p-4 bg-muted rounded-md">
            <h3 className="font-medium mb-2">Conseils marketing</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <Globe className="h-4 w-4 text-muted-foreground mt-0.5" />
                <span>
                  Créez un site web professionnel qui explique clairement l'utilité et les avantages de votre token.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <Twitter className="h-4 w-4 text-muted-foreground mt-0.5" />
                <span>
                  Utilisez Twitter pour partager des mises à jour régulières et interagir avec votre communauté.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <MessageCircle className="h-4 w-4 text-muted-foreground mt-0.5" />
                <span>
                  Créez un groupe Telegram ou Discord pour construire une communauté active autour de votre projet.
                </span>
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

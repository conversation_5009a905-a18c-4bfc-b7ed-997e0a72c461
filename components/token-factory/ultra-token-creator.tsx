"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CheckCircle2, AlertCircle, Loader2, Info, Copy, ExternalLink, Rocket } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"
import { UltraTokenService } from "@/lib/ultra-token-service"
import { diagnoseBase58Error, testRpcConnection } from "@/lib/token-diagnostics"

export default function UltraTokenCreator() {
  // Wallet connection
  const { publicKey, signTransaction, signAllTransactions, connected } = useWallet()
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()

  // Form state
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState("9")
  const [tokenSupply, setTokenSupply] = useState("1000000000")
  const [tokenDescription, setTokenDescription] = useState("")
  const [tokenWebsite, setTokenWebsite] = useState("")
  const [tokenTwitter, setTokenTwitter] = useState("")
  const [tokenTelegram, setTokenTelegram] = useState("")
  const [activeTab, setActiveTab] = useState("basic")

  // Creation state
  const [isCreating, setIsCreating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [createdToken, setCreatedToken] = useState<any>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({})
  const [logs, setLogs] = useState<string[]>([])
  const [diagnosticResults, setDiagnosticResults] = useState<any>(null)
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false)

  // Add log function
  const addLog = (message: string) => {
    console.log(message)
    setLogs((prev) => [...prev, `${new Date().toISOString().split("T")[1].split(".")[0]} - ${message}`])
  }

  // Check wallet balance
  useEffect(() => {
    const checkBalance = async () => {
      if (publicKey) {
        try {
          const tokenService = new UltraTokenService()
          const connection = tokenService["connection"]
          const balance = await connection.getBalance(publicKey)
          setWalletBalance(balance / 1e9) // Convert to SOL
        } catch (err: any) {
          console.error("Error checking wallet balance:", err)
          setWalletBalance(null)
        }
      } else {
        setWalletBalance(null)
      }
    }

    checkBalance()
  }, [publicKey])

  // Validate form
  const validateForm = () => {
    const errors: { [key: string]: string } = {}

    // Validate token name
    if (!tokenName.trim()) {
      errors.name = "Token name is required"
    }

    // Validate token symbol
    if (!tokenSymbol.trim()) {
      errors.symbol = "Token symbol is required"
    } else {
      // Check if symbol contains only alphanumeric characters
      const symbolRegex = /^[A-Za-z0-9]+$/
      if (!symbolRegex.test(tokenSymbol)) {
        errors.symbol = "Symbol must contain only letters and numbers"
      }
    }

    // Validate decimals
    const decimals = Number(tokenDecimals)
    if (isNaN(decimals) || decimals < 0 || decimals > 9) {
      errors.decimals = "Decimals must be between 0 and 9"
    }

    // Validate supply
    const supply = Number(tokenSupply)
    if (isNaN(supply) || supply <= 0) {
      errors.supply = "Supply must be a positive number"
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Run diagnostics
  const runDiagnostics = async () => {
    setIsRunningDiagnostics(true)
    setDiagnosticResults(null)
    setLogs([])

    addLog("Starting diagnostics...")

    try {
      // Test RPC connection
      addLog("Testing RPC connection...")
      const rpcResult = await testRpcConnection()
      addLog(rpcResult.success ? "✅ RPC connection successful" : "❌ RPC connection failed")

      // Test symbol for base58 validity
      addLog("Testing token symbol for base58 validity...")
      const symbolResult = await diagnoseBase58Error(tokenSymbol)
      addLog(symbolResult.success ? "✅ Token symbol is valid" : `❌ Token symbol issue: ${symbolResult.details}`)

      // Test wallet connection
      addLog("Testing wallet connection...")
      const walletConnected = connected && publicKey
      addLog(walletConnected ? "✅ Wallet connected" : "❌ Wallet not connected")

      // Collect results
      const results = {
        rpc: rpcResult,
        symbol: symbolResult,
        wallet: {
          success: !!walletConnected,
          details: walletConnected ? `Connected: ${publicKey?.toString().slice(0, 6)}...` : "Not connected",
        },
        timestamp: new Date().toISOString(),
      }

      setDiagnosticResults(results)
      addLog("Diagnostics completed")
    } catch (error: any) {
      addLog(`❌ Error during diagnostics: ${error.message}`)
      setDiagnosticResults({
        success: false,
        error: error.message,
      })
    } finally {
      setIsRunningDiagnostics(false)
    }
  }

  // Create token
  const createToken = async () => {
    if (!connected || !publicKey || !signTransaction) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to create a token",
        variant: "destructive",
      })
      return
    }

    // Validate form
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setProgress(0)
    setError(null)
    setCurrentStep("Initializing...")
    setLogs([])

    try {
      addLog("Starting token creation process")
      addLog(
        `Token details: Name=${tokenName}, Symbol=${tokenSymbol}, Decimals=${tokenDecimals}, Supply=${tokenSupply}`,
      )

      // Update progress
      setProgress(10)
      setCurrentStep("Preparing token creation...")

      // Create token service
      const tokenService = new UltraTokenService()

      // Update progress
      setProgress(30)
      setCurrentStep("Creating token on blockchain...")

      // Create the token
      const result = await tokenService.createToken(
        {
          name: tokenName,
          symbol: tokenSymbol,
          decimals: Number(tokenDecimals),
          initialSupply: Number(tokenSupply),
          ownerPublicKey: publicKey,
        },
        {
          publicKey,
          signTransaction,
          signAllTransactions,
        },
      )

      if (!result.success) {
        throw new Error(result.error || "Unknown error occurred")
      }

      addLog(`Token created with address: ${result.tokenAddress}`)
      addLog(`Token account: ${result.tokenAccountAddress}`)
      addLog(`Transaction ID: ${result.transactionId}`)

      // Update progress
      setProgress(100)
      setCurrentStep("Token created successfully!")

      // Set created token
      setCreatedToken({
        name: tokenName,
        symbol: tokenSymbol,
        decimals: Number(tokenDecimals),
        supply: tokenSupply,
        mintAddress: result.tokenAddress,
        tokenAccount: result.tokenAccountAddress,
        transactionId: result.transactionId,
      })

      toast({
        title: "Token Created Successfully",
        description: `Your token ${tokenName} (${tokenSymbol}) has been created`,
      })
    } catch (error: any) {
      console.error("Error creating token:", error)
      setError(error.message || "An error occurred while creating the token")
      addLog(`ERROR: ${error.message || "Unknown error"}`)

      // Run diagnostics on the error
      addLog("Running diagnostics on the error...")
      const diagnostics = await diagnoseBase58Error(tokenSymbol)
      addLog(`Diagnostics result: ${JSON.stringify(diagnostics)}`)
      setDiagnosticResults(diagnostics)

      toast({
        title: "Token Creation Failed",
        description: error.message || "An error occurred while creating the token",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "The text has been copied to your clipboard",
    })
  }

  // View on explorer
  const viewOnExplorer = (address: string) => {
    const explorerUrl = `https://explorer.solana.com/address/${address}?cluster=devnet`
    window.open(explorerUrl, "_blank")
  }

  // Launch token (simulated)
  const launchToken = () => {
    toast({
      title: "Token Launch Initiated",
      description: "Your token launch process has started",
    })
    // In a real implementation, this would start the token launch process
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Ultra Token Creator</CardTitle>
        <CardDescription>Create your own token on {activeNetwork.name} with advanced features</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={runDiagnostics}
              disabled={isRunningDiagnostics}
            >
              {isRunningDiagnostics ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running Diagnostics...
                </>
              ) : (
                "Run Diagnostics"
              )}
            </Button>
          </Alert>
        )}

        {diagnosticResults && (
          <Alert
            variant={diagnosticResults.success ? "default" : "destructive"}
            className="bg-amber-50 border-amber-200"
          >
            <Info className="h-4 w-4" />
            <AlertTitle>Diagnostic Results</AlertTitle>
            <AlertDescription>
              <div className="mt-2 space-y-2">
                {diagnosticResults.rpc && (
                  <div className="flex items-center">
                    <span className={`mr-2 ${diagnosticResults.rpc.success ? "text-green-500" : "text-red-500"}`}>
                      {diagnosticResults.rpc.success ? "✓" : "✗"}
                    </span>
                    <span>RPC Connection: {diagnosticResults.rpc.details}</span>
                  </div>
                )}

                {diagnosticResults.symbol && (
                  <div className="flex items-center">
                    <span className={`mr-2 ${diagnosticResults.symbol.success ? "text-green-500" : "text-red-500"}`}>
                      {diagnosticResults.symbol.success ? "✓" : "✗"}
                    </span>
                    <span>Token Symbol: {diagnosticResults.symbol.details}</span>
                  </div>
                )}

                {diagnosticResults.wallet && (
                  <div className="flex items-center">
                    <span className={`mr-2 ${diagnosticResults.wallet.success ? "text-green-500" : "text-red-500"}`}>
                      {diagnosticResults.wallet.success ? "✓" : "✗"}
                    </span>
                    <span>Wallet: {diagnosticResults.wallet.details}</span>
                  </div>
                )}

                {diagnosticResults.solution && (
                  <div className="mt-2 p-2 bg-amber-100 rounded-md">
                    <strong>Suggested Solution:</strong> {diagnosticResults.solution}
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {createdToken ? (
          <div className="space-y-4">
            <Alert variant="default" className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle>Token Created Successfully</AlertTitle>
              <AlertDescription>Your token has been created on the blockchain.</AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Token Details</h3>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => viewOnExplorer(createdToken.mintAddress)}>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View on Explorer
                  </Button>
                  <Button variant="default" size="sm" onClick={launchToken}>
                    <Rocket className="h-4 w-4 mr-2" />
                    Launch Token
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Token Name</Label>
                  <div className="font-medium">{createdToken.name}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Token Symbol</Label>
                  <div className="font-medium">{createdToken.symbol}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Decimals</Label>
                  <div className="font-medium">{createdToken.decimals}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Initial Supply</Label>
                  <div className="font-medium">{Number(createdToken.supply).toLocaleString()}</div>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Token Address</Label>
                <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                  <code className="text-xs break-all">{createdToken.mintAddress}</code>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.mintAddress)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Token Account</Label>
                <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                  <code className="text-xs break-all">{createdToken.tokenAccount}</code>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.tokenAccount)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Transaction ID</Label>
                <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                  <code className="text-xs break-all">{createdToken.transactionId}</code>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.transactionId)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button onClick={() => setCreatedToken(null)}>Create Another Token</Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {isCreating ? (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-medium">{currentStep}</h3>
                  <p className="text-sm text-muted-foreground">Please wait while we create your token...</p>
                </div>
                <Progress value={progress} className="h-2" />

                {/* Logs display */}
                <div className="mt-4">
                  <Label>Creation Logs</Label>
                  <div className="mt-2 p-2 bg-black text-green-400 font-mono text-xs h-40 overflow-y-auto rounded">
                    {logs.map((log, index) => (
                      <div key={index}>{log}</div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <>
                <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="basic">Basic Info</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced</TabsTrigger>
                    <TabsTrigger value="launch">Launch</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="tokenName">Token Name</Label>
                      <Input
                        id="tokenName"
                        value={tokenName}
                        onChange={(e) => setTokenName(e.target.value)}
                        placeholder="My Token"
                        required
                        className={validationErrors.name ? "border-red-500" : ""}
                      />
                      {validationErrors.name && <p className="text-xs text-red-500 mt-1">{validationErrors.name}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenSymbol">Token Symbol</Label>
                      <Input
                        id="tokenSymbol"
                        value={tokenSymbol}
                        onChange={(e) => setTokenSymbol(e.target.value)}
                        placeholder="MTK"
                        required
                        maxLength={10}
                        className={validationErrors.symbol ? "border-red-500" : ""}
                      />
                      {validationErrors.symbol && (
                        <p className="text-xs text-red-500 mt-1">{validationErrors.symbol}</p>
                      )}
                      <p className="text-xs text-muted-foreground">Use only letters and numbers (A-Z, a-z, 0-9)</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="tokenDecimals">Decimals</Label>
                        <Input
                          id="tokenDecimals"
                          type="number"
                          min="0"
                          max="9"
                          value={tokenDecimals}
                          onChange={(e) => setTokenDecimals(e.target.value)}
                          required
                          className={validationErrors.decimals ? "border-red-500" : ""}
                        />
                        {validationErrors.decimals && (
                          <p className="text-xs text-red-500 mt-1">{validationErrors.decimals}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="tokenSupply">Initial Supply</Label>
                        <Input
                          id="tokenSupply"
                          type="number"
                          min="1"
                          value={tokenSupply}
                          onChange={(e) => setTokenSupply(e.target.value)}
                          required
                          className={validationErrors.supply ? "border-red-500" : ""}
                        />
                        {validationErrors.supply && (
                          <p className="text-xs text-red-500 mt-1">{validationErrors.supply}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenDescription">Description (Optional)</Label>
                      <Textarea
                        id="tokenDescription"
                        value={tokenDescription}
                        onChange={(e) => setTokenDescription(e.target.value)}
                        placeholder="Describe your token..."
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="advanced" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="tokenWebsite">Website (Optional)</Label>
                      <Input
                        id="tokenWebsite"
                        value={tokenWebsite}
                        onChange={(e) => setTokenWebsite(e.target.value)}
                        placeholder="https://example.com"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenTwitter">Twitter (Optional)</Label>
                      <Input
                        id="tokenTwitter"
                        value={tokenTwitter}
                        onChange={(e) => setTokenTwitter(e.target.value)}
                        placeholder="https://twitter.com/example"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenTelegram">Telegram (Optional)</Label>
                      <Input
                        id="tokenTelegram"
                        value={tokenTelegram}
                        onChange={(e) => setTokenTelegram(e.target.value)}
                        placeholder="https://t.me/example"
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="launch" className="space-y-4 mt-4">
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Launch Settings</AlertTitle>
                      <AlertDescription>
                        Configure how your token will be launched. These settings will be applied after token creation.
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-2">
                      <Label>Initial Liquidity (Coming Soon)</Label>
                      <div className="p-4 border border-dashed rounded-md text-center text-muted-foreground">
                        Liquidity pool creation will be available after token creation
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Marketing (Coming Soon)</Label>
                      <div className="p-4 border border-dashed rounded-md text-center text-muted-foreground">
                        Marketing tools will be available after token creation
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Token Creation Fee</AlertTitle>
                  <AlertDescription>
                    Creating a token requires a small amount of SOL to cover network fees.
                    {walletBalance !== null && (
                      <div className="mt-2">
                        Your wallet balance: <span className="font-medium">{walletBalance.toFixed(6)} SOL</span>
                      </div>
                    )}
                  </AlertDescription>
                </Alert>

                <div className="flex gap-2">
                  <Button
                    type="button"
                    onClick={runDiagnostics}
                    variant="outline"
                    disabled={isRunningDiagnostics}
                    className="flex-1"
                  >
                    {isRunningDiagnostics ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Running Diagnostics...
                      </>
                    ) : (
                      "Run Diagnostics"
                    )}
                  </Button>

                  <Button type="button" onClick={createToken} disabled={isCreating || !connected} className="flex-1">
                    {isCreating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Token...
                      </>
                    ) : (
                      "Create Token"
                    )}
                  </Button>
                </div>
              </>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {!isCreating && !createdToken && (
          <div className="text-sm text-muted-foreground">
            {connected ? (
              <>
                Connected: {publicKey?.toString().slice(0, 6)}...{publicKey?.toString().slice(-4)}
                {walletBalance !== null && <> | Balance: {walletBalance.toFixed(4)} SOL</>}
              </>
            ) : (
              "Please connect your wallet to create a token"
            )}
          </div>
        )}
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { CalendarIcon, Clock, Plus, Trash2, AlertTriangle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface VestingSchedule {
  id: string
  name: string
  allocation: number
  recipient: string
  unlockType: "cliff" | "linear"
  startDate: Date
  duration: number
  durationUnit: "days" | "months" | "years"
  cliffDuration?: number
  cliffDurationUnit?: "days" | "months" | "years"
}

interface VestingConfigurationProps {
  initialValues?: {
    enabled: boolean
    schedules: VestingSchedule[]
  }
  onChange?: (values: any) => void
}

export function VestingConfiguration({ initialValues, onChange }: VestingConfigurationProps) {
  const [vesting, setVesting] = useState({
    enabled: initialValues?.enabled || false,
    schedules: initialValues?.schedules || [
      {
        id: "team",
        name: "Équipe",
        allocation: 20,
        recipient: "",
        unlockType: "linear" as const,
        startDate: new Date(),
        duration: 12,
        durationUnit: "months" as const,
        cliffDuration: 3,
        cliffDurationUnit: "months" as const,
      },
    ],
  })

  const handleToggleVesting = (checked: boolean) => {
    const updatedVesting = { ...vesting, enabled: checked }
    setVesting(updatedVesting)
    if (onChange) {
      onChange(updatedVesting)
    }
  }

  const handleAddSchedule = () => {
    const newId = `schedule-${Date.now()}`
    const newSchedule: VestingSchedule = {
      id: newId,
      name: "Nouveau groupe",
      allocation: 5,
      recipient: "",
      unlockType: "linear",
      startDate: new Date(),
      duration: 6,
      durationUnit: "months",
      cliffDuration: 1,
      cliffDurationUnit: "months",
    }

    const updatedVesting = {
      ...vesting,
      schedules: [...vesting.schedules, newSchedule],
    }

    setVesting(updatedVesting)
    if (onChange) {
      onChange(updatedVesting)
    }
  }

  const handleRemoveSchedule = (id: string) => {
    const updatedVesting = {
      ...vesting,
      schedules: vesting.schedules.filter((schedule) => schedule.id !== id),
    }

    setVesting(updatedVesting)
    if (onChange) {
      onChange(updatedVesting)
    }
  }

  const handleUpdateSchedule = (id: string, field: keyof VestingSchedule, value: any) => {
    const updatedVesting = {
      ...vesting,
      schedules: vesting.schedules.map((schedule) => {
        if (schedule.id === id) {
          return { ...schedule, [field]: value }
        }
        return schedule
      }),
    }

    setVesting(updatedVesting)
    if (onChange) {
      onChange(updatedVesting)
    }
  }

  // Calculer l'allocation totale
  const totalAllocation = vesting.schedules.reduce((sum, schedule) => sum + schedule.allocation, 0)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Configuration du vesting
        </CardTitle>
        <CardDescription>Définissez des périodes de blocage pour distribuer progressivement les tokens</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">Activer le vesting</h3>
            <p className="text-sm text-muted-foreground">
              Distribue progressivement les tokens selon des calendriers prédéfinis
            </p>
          </div>
          <Switch checked={vesting.enabled} onCheckedChange={handleToggleVesting} />
        </div>

        {vesting.enabled && (
          <>
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Calendriers de vesting</h3>
              <Button variant="outline" size="sm" onClick={handleAddSchedule} className="flex items-center gap-1">
                <Plus className="h-4 w-4" />
                Ajouter
              </Button>
            </div>

            <div className="space-y-6 max-h-[500px] overflow-y-auto pr-2">
              {vesting.schedules.map((schedule) => (
                <div key={schedule.id} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">{schedule.name}</h4>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveSchedule(schedule.id)}
                      disabled={vesting.schedules.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`schedule-name-${schedule.id}`}>Nom du groupe</Label>
                      <Input
                        id={`schedule-name-${schedule.id}`}
                        value={schedule.name}
                        onChange={(e) => handleUpdateSchedule(schedule.id, "name", e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor={`schedule-allocation-${schedule.id}`}>Allocation (%)</Label>
                      <Input
                        id={`schedule-allocation-${schedule.id}`}
                        type="number"
                        value={schedule.allocation}
                        onChange={(e) => handleUpdateSchedule(schedule.id, "allocation", Number(e.target.value))}
                        min={1}
                        max={100}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Pourcentage de l'offre totale alloué à ce groupe
                      </p>
                    </div>

                    <div>
                      <Label htmlFor={`schedule-recipient-${schedule.id}`}>Adresse du bénéficiaire</Label>
                      <Input
                        id={`schedule-recipient-${schedule.id}`}
                        value={schedule.recipient}
                        onChange={(e) => handleUpdateSchedule(schedule.id, "recipient", e.target.value)}
                        placeholder="Adresse du portefeuille qui recevra les tokens"
                      />
                    </div>

                    <div>
                      <Label htmlFor={`schedule-unlock-${schedule.id}`}>Type de déblocage</Label>
                      <Select
                        value={schedule.unlockType}
                        onValueChange={(value) =>
                          handleUpdateSchedule(schedule.id, "unlockType", value as "cliff" | "linear")
                        }
                      >
                        <SelectTrigger id={`schedule-unlock-${schedule.id}`}>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cliff">Cliff (déblocage unique)</SelectItem>
                          <SelectItem value="linear">Linéaire (déblocage progressif)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Date de début</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                            id={`schedule-date-${schedule.id}`}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {schedule.startDate ? (
                              format(schedule.startDate, "PPP", { locale: fr })
                            ) : (
                              <span>Sélectionner une date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={schedule.startDate}
                            onSelect={(date) => handleUpdateSchedule(schedule.id, "startDate", date || new Date())}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div>
                      <Label htmlFor={`schedule-duration-${schedule.id}`}>Durée totale</Label>
                      <div className="flex gap-2">
                        <Input
                          id={`schedule-duration-${schedule.id}`}
                          type="number"
                          value={schedule.duration}
                          onChange={(e) => handleUpdateSchedule(schedule.id, "duration", Number(e.target.value))}
                          min={1}
                        />
                        <Select
                          value={schedule.durationUnit}
                          onValueChange={(value) =>
                            handleUpdateSchedule(schedule.id, "durationUnit", value as "days" | "months" | "years")
                          }
                        >
                          <SelectTrigger className="w-[120px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="days">Jours</SelectItem>
                            <SelectItem value="months">Mois</SelectItem>
                            <SelectItem value="years">Années</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {schedule.unlockType === "linear" && (
                      <div>
                        <Label htmlFor={`schedule-cliff-${schedule.id}`}>Période de cliff</Label>
                        <div className="flex gap-2">
                          <Input
                            id={`schedule-cliff-${schedule.id}`}
                            type="number"
                            value={schedule.cliffDuration}
                            onChange={(e) => handleUpdateSchedule(schedule.id, "cliffDuration", Number(e.target.value))}
                            min={0}
                          />
                          <Select
                            value={schedule.cliffDurationUnit}
                            onValueChange={(value) =>
                              handleUpdateSchedule(
                                schedule.id,
                                "cliffDurationUnit",
                                value as "days" | "months" | "years",
                              )
                            }
                          >
                            <SelectTrigger className="w-[120px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="days">Jours</SelectItem>
                              <SelectItem value="months">Mois</SelectItem>
                              <SelectItem value="years">Années</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Période avant le début de la distribution des tokens
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {totalAllocation > 100 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  L'allocation totale ({totalAllocation}%) dépasse 100% de l'offre totale. Veuillez ajuster les
                  allocations.
                </AlertDescription>
              </Alert>
            )}

            {totalAllocation < 100 && (
              <Alert variant="warning">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  L'allocation totale ({totalAllocation}%) est inférieure à 100% de l'offre totale. Il reste{" "}
                  {100 - totalAllocation}% non alloués.
                </AlertDescription>
              </Alert>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}

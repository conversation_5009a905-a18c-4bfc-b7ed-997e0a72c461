"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useNetwork } from "@/contexts/network-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { AlertCircle, CheckCircle2, Lock, Coins, BarChart3, Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import TokenSuffixService from "@/lib/token-suffix-service"
import Web3 from "web3"
import type { AbiItem } from "web3-utils"
import { getBnbTokenFactoryAddress } from "@/lib/network-config"

// Token Factory ABI (simplified)
const tokenFactoryABI: AbiItem[] = [
  {
    inputs: [
      { internalType: "string", name: "name", type: "string" },
      { internalType: "string", name: "symbol", type: "string" },
      { internalType: "uint8", name: "decimals", type: "uint8" },
      { internalType: "uint256", name: "initialSupply", type: "uint256" },
      { internalType: "address", name: "owner", type: "address" },
      { internalType: "string", name: "targetSuffix", type: "string" },
      { internalType: "uint256", name: "maxAttempts", type: "uint256" },
    ],
    name: "createTokenWithSuffix",
    outputs: [{ internalType: "address", name: "", type: "address" }],
    stateMutability: "payable",
    type: "function",
  },
  {
    inputs: [
      { internalType: "address", name: "tokenAddress", type: "address" },
      { internalType: "address", name: "routerAddress", type: "address" },
      { internalType: "uint256", name: "bnbAmount", type: "uint256" },
      { internalType: "uint256", name: "tokenAmount", type: "uint256" },
      { internalType: "uint256", name: "lockTime", type: "uint256" },
    ],
    name: "addLiquidityAndLock",
    outputs: [{ internalType: "address", name: "", type: "address" }],
    stateMutability: "payable",
    type: "function",
  },
]

// Token creation stages
type CreationStage =
  | "form"
  | "grinding"
  | "creating"
  | "created"
  | "preparing_dex"
  | "adding_liquidity"
  | "locking_liquidity"
  | "completed"

interface BnbTokenFormProps {
  onCreateToken: (data: any) => void
}

export default function BnbTokenForm({ onCreateToken }: BnbTokenFormProps) {
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()

  // Form state
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState("18") // Default for BEP-20
  const [tokenSupply, setTokenSupply] = useState("1000000000")
  const [tokenDescription, setTokenDescription] = useState("")
  const [tokenWebsite, setTokenWebsite] = useState("")
  const [tokenTwitter, setTokenTwitter] = useState("")
  const [tokenTelegram, setTokenTelegram] = useState("")
  const [tokenSuffix, setTokenSuffix] = useState("")
  const [maxAttempts, setMaxAttempts] = useState(10000) // Default max attempts for grinding
  const [grindingAttempts, setGrindingAttempts] = useState(0)

  // Advanced options
  const [addLiquidity, setAddLiquidity] = useState(true)
  const [lockLiquidity, setLockLiquidity] = useState(true)
  const [lockDuration, setLockDuration] = useState(180) // days
  const [liquidityAmount, setLiquidityAmount] = useState("0.1") // in BNB
  const [liquidityPercentage, setLiquidityPercentage] = useState(50) // % of tokens for liquidity
  const [antiBot, setAntiBot] = useState(false)
  const [maxTxAmount, setMaxTxAmount] = useState(1) // % of total supply
  const [maxWalletAmount, setMaxWalletAmount] = useState(2) // % of total supply

  // Creation state
  const [stage, setStage] = useState<CreationStage>("form")
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [tokenAddress, setTokenAddress] = useState<string | null>(null)
  const [pairAddress, setPairAddress] = useState<string | null>(null)
  const [lockAddress, setLockAddress] = useState<string | null>(null)

  // Wallet state
  const [account, setAccount] = useState<string | null>(null)
  const [bnbBalance, setBnbBalance] = useState<string | null>(null)
  const [web3, setWeb3] = useState<Web3 | null>(null)

  // Load token suffix for the active network
  useEffect(() => {
    const loadSuffix = async () => {
      try {
        const suffix = await TokenSuffixService.getSuffixForNetwork(activeNetwork.id)
        setTokenSuffix(suffix)
      } catch (error) {
        console.error("Error loading suffix:", error)
        setTokenSuffix("BETAGF") // Default suffix
      }
    }

    loadSuffix()
  }, [activeNetwork])

  // Initialize Web3 and connect to wallet
  useEffect(() => {
    const initWeb3 = async () => {
      if (window.ethereum) {
        try {
          // Request account access
          const accounts = await window.ethereum.request({ method: "eth_requestAccounts" })
          setAccount(accounts[0])

          // Create Web3 instance
          const web3Instance = new Web3(window.ethereum)
          setWeb3(web3Instance)

          // Check if we need to switch networks
          const chainId = await web3Instance.eth.getChainId()
          const targetChainId = activeNetwork.chainId || 97 // Default to testnet

          if (chainId !== targetChainId) {
            try {
              await window.ethereum.request({
                method: "wallet_switchEthereumChain",
                params: [{ chainId: `0x${targetChainId.toString(16)}` }],
              })
            } catch (switchError: any) {
              // This error code indicates that the chain has not been added to MetaMask
              if (switchError.code === 4902) {
                await window.ethereum.request({
                  method: "wallet_addEthereumChain",
                  params: [
                    {
                      chainId: `0x${targetChainId.toString(16)}`,
                      chainName: activeNetwork.name,
                      nativeCurrency: {
                        name: activeNetwork.nativeCurrency.name,
                        symbol: activeNetwork.nativeCurrency.symbol,
                        decimals: activeNetwork.nativeCurrency.decimals,
                      },
                      rpcUrls: [activeNetwork.rpcUrl],
                      blockExplorerUrls: activeNetwork.blockExplorerUrls,
                    },
                  ],
                })
              }
            }
          }

          // Get BNB balance
          const balance = await web3Instance.eth.getBalance(accounts[0])
          setBnbBalance(web3Instance.utils.fromWei(balance, "ether"))
        } catch (error) {
          console.error("Error initializing Web3:", error)
          toast({
            title: "Wallet Connection Error",
            description: "Failed to connect to your wallet. Please make sure MetaMask is installed and unlocked.",
            variant: "destructive",
          })
        }
      } else {
        toast({
          title: "Web3 Not Available",
          description: "Please install MetaMask or another Web3 wallet to use this feature.",
          variant: "destructive",
        })
      }
    }

    if (activeNetwork.type === "bnb") {
      initWeb3()
    }

    // Listen for account changes
    if (window.ethereum) {
      window.ethereum.on("accountsChanged", (accounts: string[]) => {
        setAccount(accounts[0])
      })
    }

    return () => {
      // Clean up listeners
      if (window.ethereum) {
        window.ethereum.removeAllListeners("accountsChanged")
      }
    }
  }, [activeNetwork, toast])

  // Progress bar animation
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (stage === "grinding") {
      setProgress(5)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 20) {
            clearInterval(interval)
            return 20
          }
          return prev + 0.5
        })
      }, 100)
    } else if (stage === "creating") {
      setProgress(25)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 40) {
            clearInterval(interval)
            return 40
          }
          return prev + 1
        })
      }, 100)
    } else if (stage === "created") {
      setProgress(50)
    } else if (stage === "preparing_dex") {
      setProgress(60)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 70) {
            clearInterval(interval)
            return 70
          }
          return prev + 1
        })
      }, 100)
    } else if (stage === "adding_liquidity") {
      setProgress(80)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(interval)
            return 90
          }
          return prev + 1
        })
      }, 100)
    } else if (stage === "locking_liquidity") {
      setProgress(95)
      interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 98) {
            clearInterval(interval)
            return 98
          }
          return prev + 1
        })
      }, 100)
    } else if (stage === "completed") {
      setProgress(100)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [stage])

  // Calculate required balance
  const getRequiredBalance = () => {
    // Base fee increases with max attempts for grinding
    let required = 0.01 + (maxAttempts / 10000) * 0.05 // Base fee for token creation

    // Liquidity amount if enabled
    if (addLiquidity) {
      required += Number.parseFloat(liquidityAmount)
    }

    return required
  }

  // Estimate grinding time
  const estimateGrindingTime = () => {
    // Estimation based on suffix length and max attempts
    const base16Size = 16 // Hex characters
    const suffixLength = tokenSuffix.length
    const averageAttempts = Math.pow(base16Size, suffixLength)

    // Limit by max attempts
    const expectedAttempts = Math.min(averageAttempts, maxAttempts)

    // Estimate time based on attempts (very rough estimate)
    const attemptsPerSecond = 50 // Estimated contract attempts per second
    const seconds = expectedAttempts / attemptsPerSecond

    // Format time
    if (seconds < 60) {
      return `${Math.ceil(seconds)} seconds`
    } else if (seconds < 3600) {
      return `${Math.ceil(seconds / 60)} minutes`
    } else {
      return `${Math.ceil(seconds / 3600)} hours`
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!account || !web3) {
      setError("Please connect your wallet")
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to create a token",
        variant: "destructive",
      })
      return
    }

    // Check if wallet has enough balance
    const requiredBalance = getRequiredBalance()
    if (bnbBalance === null || Number(bnbBalance) < requiredBalance) {
      setError(`Insufficient balance. You need at least ${requiredBalance} BNB`)
      toast({
        title: "Insufficient balance",
        description: `You need at least ${requiredBalance} BNB to create this token`,
        variant: "destructive",
      })
      return
    }

    // Validate suffix
    if (!tokenSuffix) {
      setError("Token suffix is required")
      toast({
        title: "Missing suffix",
        description: "Please enter a token suffix",
        variant: "destructive",
      })
      return
    }

    // Start grinding process
    setStage("grinding")
    setGrindingAttempts(0)

    try {
      // Get token factory contract
      const factoryAddress = getBnbTokenFactoryAddress()
      const factory = new web3.eth.Contract(tokenFactoryABI, factoryAddress)

      // Calculate the initial supply with decimals
      const decimals = Number(tokenDecimals)
      const initialSupplyWithDecimals = web3.utils
        .toBN(tokenSupply)
        .mul(web3.utils.toBN(10).pow(web3.utils.toBN(decimals)))
        .toString()

      // Start token creation with suffix grinding
      setStage("creating")

      // Estimate gas for the transaction
      const gasEstimate = await factory.methods
        .createTokenWithSuffix(
          tokenName,
          tokenSymbol,
          decimals,
          initialSupplyWithDecimals,
          account,
          tokenSuffix,
          maxAttempts,
        )
        .estimateGas({
          from: account,
          value: web3.utils.toWei(requiredBalance.toString(), "ether"),
        })

      // Create the token with suffix grinding
      const result = await factory.methods
        .createTokenWithSuffix(
          tokenName,
          tokenSymbol,
          decimals,
          initialSupplyWithDecimals,
          account,
          tokenSuffix,
          maxAttempts,
        )
        .send({
          from: account,
          gas: Math.floor(gasEstimate * 1.2), // Add 20% buffer
          value: web3.utils.toWei(requiredBalance.toString(), "ether"),
        })

      // Get the token address and attempts from the event
      const tokenAddress = result.events.TokenCreated.returnValues.tokenAddress
      const attemptsUsed = result.events.TokenCreated.returnValues.attemptsUsed
      setTokenAddress(tokenAddress)
      setGrindingAttempts(Number(attemptsUsed))
      setStage("created")

      // If liquidity is enabled, add liquidity
      if (addLiquidity) {
        setStage("preparing_dex")
        await new Promise((resolve) => setTimeout(resolve, 2000))

        setStage("adding_liquidity")

        // Calculate token amount for liquidity
        const tokenAmount = web3.utils
          .toBN(initialSupplyWithDecimals)
          .mul(web3.utils.toBN(liquidityPercentage))
          .div(web3.utils.toBN(100))
          .toString()

        // Get PancakeSwap router address
        const routerAddress =
          process.env.NEXT_PUBLIC_PANCAKESWAP_ROUTER_ADDRESS || "******************************************" // Default to testnet router

        // Add liquidity
        const liquidityResult = await factory.methods
          .addLiquidityAndLock(
            tokenAddress,
            routerAddress,
            web3.utils.toWei(liquidityAmount, "ether"),
            tokenAmount,
            lockLiquidity ? lockDuration * 86400 : 0, // Convert days to seconds if locking
          )
          .send({
            from: account,
            value: web3.utils.toWei(liquidityAmount, "ether"),
            gas: 5000000, // High gas limit for complex operation
          })

        // Get the pair address from the event
        const pairAddress = liquidityResult.events.LiquidityAdded.returnValues.pairAddress
        setPairAddress(pairAddress)

        // If liquidity locking is enabled
        if (lockLiquidity) {
          setStage("locking_liquidity")
          await new Promise((resolve) => setTimeout(resolve, 2000))

          // Lock address would be returned from the liquidity locking event
          const lockAddress = liquidityResult.events.LiquidityLocked.returnValues.lockAddress
          setLockAddress(lockAddress)
        }
      }

      // Complete the process
      setStage("completed")

      // Notify parent component
      onCreateToken({
        tokenName,
        tokenSymbol,
        tokenDecimals,
        tokenSupply,
        tokenDescription,
        tokenWebsite,
        tokenTwitter,
        tokenTelegram,
        tokenAddress,
        tokenSuffix,
        grindingAttempts: attemptsUsed,
        pairAddress: addLiquidity ? pairAddress : null,
        lockAddress: addLiquidity && lockLiquidity ? lockAddress : null,
        network: activeNetwork.id,
        networkType: activeNetwork.type,
        addedLiquidity: addLiquidity,
        lockedLiquidity: addLiquidity && lockLiquidity,
        lockDuration: addLiquidity && lockLiquidity ? lockDuration : null,
        antiBot,
        maxTxAmount: antiBot ? maxTxAmount : null,
        maxWalletAmount: antiBot ? maxWalletAmount : null,
      })

      toast({
        title: "Token created successfully",
        description: `Your token ${tokenName} (${tokenSymbol}) has been created with address ending in ${tokenSuffix}`,
        variant: "default",
      })
    } catch (err: any) {
      console.error("Error creating token:", err)
      setError(err.message || "Failed to create token")
      setStage("form")

      toast({
        title: "Token creation failed",
        description: err.message || "Failed to create token",
        variant: "destructive",
      })
    }
  }

  // Render creation progress
  const renderProgress = () => {
    return (
      <div className="space-y-6">
        <Progress value={progress} className="h-2" />

        <div className="space-y-4">
          {/* Grinding Step */}
          <div className="flex items-center gap-3">
            <div
              className={`rounded-full p-2 ${stage === "grinding" ? "bg-yellow-500/20 text-yellow-500" : stage === "form" ? "bg-gray-500/20 text-gray-500" : "bg-green-500/20 text-green-500"}`}
            >
              {stage === "grinding" ? <Loader2 className="h-5 w-5 animate-spin" /> : <Coins className="h-5 w-5" />}
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Finding Address with Suffix "{tokenSuffix}"</h3>
                {stage !== "form" && stage !== "grinding" && <CheckCircle2 className="h-4 w-4 text-green-500" />}
              </div>
              {grindingAttempts > 0 && (
                <p className="text-xs text-muted-foreground mt-1">Found after {grindingAttempts} attempts</p>
              )}
            </div>
          </div>

          {/* Token Creation Step */}
          <div className="flex items-center gap-3">
            <div
              className={`rounded-full p-2 ${stage === "creating" ? "bg-yellow-500/20 text-yellow-500" : stage === "form" || stage === "grinding" ? "bg-gray-500/20 text-gray-500" : "bg-green-500/20 text-green-500"}`}
            >
              <Coins className="h-5 w-5" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Creating Token</h3>
                {stage !== "form" && stage !== "grinding" && stage !== "creating" && (
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                )}
              </div>
              {tokenAddress && (
                <p className="text-xs text-muted-foreground mt-1">
                  Token Address: {tokenAddress.substring(0, 8)}...
                  {tokenAddress.substring(tokenAddress.length - tokenSuffix.length)}
                </p>
              )}
            </div>
          </div>

          {/* DEX Listing Step */}
          {addLiquidity && (
            <div className="flex items-center gap-3">
              <div
                className={`rounded-full p-2 ${stage === "preparing_dex" || stage === "adding_liquidity" ? "bg-yellow-500/20 text-yellow-500" : stage === "form" || stage === "grinding" || stage === "creating" || stage === "created" ? "bg-gray-500/20 text-gray-500" : "bg-green-500/20 text-green-500"}`}
              >
                <BarChart3 className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Adding Liquidity</h3>
                  {stage !== "form" &&
                    stage !== "grinding" &&
                    stage !== "creating" &&
                    stage !== "created" &&
                    stage !== "preparing_dex" &&
                    stage !== "adding_liquidity" && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                </div>
                {pairAddress && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Pair Address: {pairAddress.substring(0, 8)}...{pairAddress.substring(pairAddress.length - 6)}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Liquidity Locking Step */}
          {addLiquidity && lockLiquidity && (
            <div className="flex items-center gap-3">
              <div
                className={`rounded-full p-2 ${stage === "locking_liquidity" ? "bg-yellow-500/20 text-yellow-500" : stage === "form" || stage === "grinding" || stage === "creating" || stage === "created" || stage === "preparing_dex" || stage === "adding_liquidity" ? "bg-gray-500/20 text-gray-500" : "bg-green-500/20 text-green-500"}`}
              >
                <Lock className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Locking Liquidity</h3>
                  {stage === "completed" && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                </div>
                {lockAddress && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Lock Address: {lockAddress.substring(0, 8)}...{lockAddress.substring(lockAddress.length - 6)}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {stage === "completed" && (
          <div className="pt-4">
            <Alert className="bg-green-50 text-green-800 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle>Success!</AlertTitle>
              <AlertDescription>
                Your token has been created successfully with address ending in {tokenSuffix}.
              </AlertDescription>
            </Alert>
          </div>
        )}
      </div>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create BEP-20 Token with Custom Address Suffix</CardTitle>
        <CardDescription>
          Fill out the form below to create your own BEP-20 token on {activeNetwork.name} with an address ending in your
          chosen suffix.
        </CardDescription>
      </CardHeader>
      <CardContent className="grid gap-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {stage === "form" ? (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="tokenName">Token Name</Label>
              <Input
                id="tokenName"
                value={tokenName}
                onChange={(e) => setTokenName(e.target.value)}
                placeholder="My Token"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="tokenSymbol">Token Symbol</Label>
              <Input
                id="tokenSymbol"
                value={tokenSymbol}
                onChange={(e) => setTokenSymbol(e.target.value)}
                placeholder="MTK"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="tokenDecimals">Token Decimals</Label>
              <Input
                id="tokenDecimals"
                type="number"
                value={tokenDecimals}
                onChange={(e) => setTokenDecimals(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="tokenSupply">Token Supply</Label>
              <Input
                id="tokenSupply"
                type="number"
                value={tokenSupply}
                onChange={(e) => setTokenSupply(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="tokenSuffix">Address Suffix</Label>
              <Input
                id="tokenSuffix"
                value={tokenSuffix}
                onChange={(e) => setTokenSuffix(e.target.value.toUpperCase())}
                placeholder="GF"
                required
              />
              <p className="text-xs text-muted-foreground">
                Your token address will end with this suffix. Default: {tokenSuffix || "GF"}
              </p>
              <p className="text-xs text-muted-foreground">Estimated time to find address: {estimateGrindingTime()}</p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="maxAttempts">Max Grinding Attempts</Label>
              <Input
                id="maxAttempts"
                type="number"
                value={maxAttempts}
                onChange={(e) => setMaxAttempts(Number.parseInt(e.target.value))}
                required
              />
              <p className="text-xs text-muted-foreground">
                Maximum number of attempts to find an address with the desired suffix. Higher values increase the chance
                of success but cost more gas.
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="tokenDescription">Token Description</Label>
              <Textarea
                id="tokenDescription"
                value={tokenDescription}
                onChange={(e) => setTokenDescription(e.target.value)}
                placeholder="A brief description of your token"
              />
            </div>

            <Tabs defaultValue="general" className="w-full">
              <TabsList>
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>
              <TabsContent value="general" className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="tokenWebsite">Website</Label>
                  <Input
                    id="tokenWebsite"
                    type="url"
                    value={tokenWebsite}
                    onChange={(e) => setTokenWebsite(e.target.value)}
                    placeholder="https://example.com"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="tokenTwitter">Twitter</Label>
                  <Input
                    id="tokenTwitter"
                    type="url"
                    value={tokenTwitter}
                    onChange={(e) => setTokenTwitter(e.target.value)}
                    placeholder="https://twitter.com/example"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="tokenTelegram">Telegram</Label>
                  <Input
                    id="tokenTelegram"
                    type="url"
                    value={tokenTelegram}
                    onChange={(e) => setTokenTelegram(e.target.value)}
                    placeholder="https://t.me/example"
                  />
                </div>
              </TabsContent>
              <TabsContent value="advanced" className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="addLiquidity">Add Liquidity to PancakeSwap</Label>
                  <Switch id="addLiquidity" checked={addLiquidity} onCheckedChange={setAddLiquidity} />
                </div>
                {addLiquidity && (
                  <div className="space-y-2">
                    <div className="grid gap-2">
                      <Label htmlFor="liquidityAmount">Liquidity Amount (BNB)</Label>
                      <Input
                        id="liquidityAmount"
                        type="number"
                        value={liquidityAmount}
                        onChange={(e) => setLiquidityAmount(e.target.value)}
                        placeholder="0.1"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="liquidityPercentage">Liquidity Percentage (%)</Label>
                      <Slider
                        id="liquidityPercentage"
                        defaultValue={[liquidityPercentage]}
                        max={100}
                        step={1}
                        onValueChange={(value) => setLiquidityPercentage(value[0])}
                      />
                      <p className="text-sm text-muted-foreground">
                        {liquidityPercentage}% of tokens will be used for liquidity.
                      </p>
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="lockLiquidity">Lock Liquidity</Label>
                      <Switch id="lockLiquidity" checked={lockLiquidity} onCheckedChange={setLockLiquidity} />
                    </div>
                    {lockLiquidity && (
                      <div className="space-y-2">
                        <div className="grid gap-2">
                          <Label htmlFor="lockDuration">Lock Duration (Days)</Label>
                          <Input
                            id="lockDuration"
                            type="number"
                            value={lockDuration}
                            onChange={(e) => setLockDuration(Number.parseInt(e.target.value))}
                            placeholder="180"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <Label htmlFor="antiBot">Enable Anti-Bot Measures</Label>
                  <Switch id="antiBot" checked={antiBot} onCheckedChange={setAntiBot} />
                </div>
                {antiBot && (
                  <div className="space-y-2">
                    <div className="grid gap-2">
                      <Label htmlFor="maxTxAmount">Max Transaction Amount (%)</Label>
                      <Input
                        id="maxTxAmount"
                        type="number"
                        value={maxTxAmount}
                        onChange={(e) => setMaxTxAmount(Number.parseFloat(e.target.value))}
                        placeholder="1"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="maxWalletAmount">Max Wallet Amount (%)</Label>
                      <Input
                        id="maxWalletAmount"
                        type="number"
                        value={maxWalletAmount}
                        onChange={(e) => setMaxWalletAmount(Number.parseFloat(e.target.value))}
                        placeholder="2"
                      />
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>

            <Button type="submit" disabled={!account || !web3}>
              {!account ? "Connect Wallet" : "Create Token"}
            </Button>
          </form>
        ) : (
          renderProgress()
        )}
      </CardContent>
      {stage === "form" && (
        <CardFooter className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            {account ? (
              <>
                Connected: {account.substring(0, 6)}...{account.substring(account.length - 4)}
              </>
            ) : (
              "Please connect your wallet to create a token"
            )}
          </div>
          <div className="text-sm text-muted-foreground">
            {bnbBalance ? <>Balance: {Number.parseFloat(bnbBalance).toFixed(4)} BNB</> : ""}
          </div>
        </CardFooter>
      )}
    </Card>
  )
}

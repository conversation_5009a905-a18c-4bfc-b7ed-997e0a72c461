"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Bot, User, RefreshCw } from "lucide-react"

interface Message {
  id: string
  content: string
  sender: "user" | "assistant"
  timestamp: Date
}

interface TokenChatInterfaceProps {
  tokenName?: string
  onSuggestionSelected?: (suggestion: string) => void
}

export function TokenChatInterface({ tokenName, onSuggestionSelected }: TokenChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [loading, setLoading] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // Initialiser le chat avec un message de bienvenue
  useEffect(() => {
    const welcomeMessage = {
      id: "welcome",
      content: `Bienvenue dans l'assistant de création de token${
        tokenName ? ` pour ${tokenName}` : ""
      }. Comment puis-je vous aider aujourd'hui ?`,
      sender: "assistant" as const,
      timestamp: new Date(),
    }

    setMessages([welcomeMessage])
  }, [tokenName])

  // Faire défiler automatiquement vers le bas lorsque de nouveaux messages sont ajoutés
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  // Simuler une réponse de l'assistant
  const simulateAssistantResponse = async (userMessage: string) => {
    setLoading(true)

    // Attendre un délai aléatoire pour simuler le traitement
    await new Promise((resolve) => setTimeout(resolve, 1000 + Math.random() * 2000))

    // Générer une réponse en fonction du message de l'utilisateur
    let response = ""

    if (userMessage.toLowerCase().includes("tokenomics")) {
      response =
        "Les tokenomics sont la distribution et l'économie de votre token. Vous pouvez définir l'offre totale, la distribution initiale, et les mécanismes comme le burning ou le staking. Que souhaitez-vous configurer spécifiquement ?"
    } else if (userMessage.toLowerCase().includes("supply") || userMessage.toLowerCase().includes("offre")) {
      response =
        "L'offre totale détermine combien de tokens existeront. Pour un memecoin, une offre élevée (ex: 1 milliard) est courante. Pour un token utilitaire, une offre plus modérée peut être préférable. Quelle offre envisagez-vous ?"
    } else if (userMessage.toLowerCase().includes("liquidity") || userMessage.toLowerCase().includes("liquidité")) {
      response =
        "La liquidité est essentielle pour que votre token puisse être échangé facilement. Nous recommandons d'allouer au moins 30% de l'offre totale à la liquidité initiale. Souhaitez-vous configurer un pool de liquidité automatiquement ?"
    } else if (userMessage.toLowerCase().includes("marketing")) {
      response =
        "Une stratégie marketing est cruciale pour le succès de votre token. Vous pouvez réserver une partie de l'offre pour les campagnes marketing, les airdrops, et les partenariats. Combien souhaitez-vous allouer au marketing ?"
    } else if (userMessage.toLowerCase().includes("tax") || userMessage.toLowerCase().includes("taxe")) {
      response =
        "Les taxes de transaction peuvent financer le développement continu et le marketing. Une taxe de 2-5% est courante. Souhaitez-vous implémenter des taxes sur les transactions ?"
    } else if (userMessage.toLowerCase().includes("launch") || userMessage.toLowerCase().includes("lancement")) {
      response =
        "Pour le lancement, vous pouvez choisir entre un listing direct sur DEX, une prévente, ou un lancement via bonding curve. Chaque méthode a ses avantages. Quelle méthode préférez-vous ?"
    } else {
      response =
        "Je suis là pour vous aider à créer votre token. Vous pouvez me poser des questions sur les tokenomics, l'offre, la liquidité, le marketing, les taxes, ou le lancement. Que souhaitez-vous savoir ?"
    }

    // Ajouter la réponse de l'assistant
    const assistantMessage: Message = {
      id: `assistant-${Date.now()}`,
      content: response,
      sender: "assistant",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, assistantMessage])
    setLoading(false)
  }

  // Gérer l'envoi d'un message
  const handleSendMessage = async () => {
    if (!input.trim()) return

    // Ajouter le message de l'utilisateur
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: input,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")

    // Simuler la réponse de l'assistant
    await simulateAssistantResponse(input)
  }

  // Gérer la soumission du formulaire
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSendMessage()
  }

  // Suggestions rapides pour l'utilisateur
  const suggestions = [
    "Comment configurer les tokenomics ?",
    "Quelle offre totale recommandez-vous ?",
    "Comment ajouter de la liquidité ?",
    "Quelles taxes de transaction configurer ?",
    "Comment optimiser le lancement ?",
  ]

  return (
    <Card className="flex flex-col h-[600px]">
      <CardHeader>
        <CardTitle>Assistant de création</CardTitle>
        <CardDescription>Posez vos questions sur la création de token</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-[400px] p-4" ref={scrollAreaRef}>
          <div className="space-y-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}>
                <div className="flex items-start gap-3 max-w-[80%]">
                  {message.sender === "assistant" && (
                    <Avatar>
                      <AvatarFallback>
                        <Bot className="h-5 w-5" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                  <div
                    className={`p-3 rounded-lg ${
                      message.sender === "user"
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                    <p className="text-xs opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                    </p>
                  </div>
                  {message.sender === "user" && (
                    <Avatar>
                      <AvatarFallback>
                        <User className="h-5 w-5" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              </div>
            ))}
            {loading && (
              <div className="flex justify-start">
                <div className="flex items-start gap-3 max-w-[80%]">
                  <Avatar>
                    <AvatarFallback>
                      <Bot className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="p-3 rounded-lg bg-muted">
                    <RefreshCw className="h-5 w-5 animate-spin text-muted-foreground" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
      <CardFooter className="flex-col gap-4">
        <div className="flex flex-wrap gap-2">
          {suggestions.map((suggestion) => (
            <Button
              key={suggestion}
              variant="outline"
              size="sm"
              onClick={() => {
                setInput(suggestion)
                if (onSuggestionSelected) {
                  onSuggestionSelected(suggestion)
                }
              }}
            >
              {suggestion}
            </Button>
          ))}
        </div>
        <form onSubmit={handleSubmit} className="flex w-full gap-2">
          <Input
            placeholder="Tapez votre message..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            disabled={loading}
          />
          <Button type="submit" disabled={!input.trim() || loading}>
            <Send className="h-4 w-4" />
            <span className="sr-only">Envoyer</span>
          </Button>
        </form>
      </CardFooter>
    </Card>
  )
}

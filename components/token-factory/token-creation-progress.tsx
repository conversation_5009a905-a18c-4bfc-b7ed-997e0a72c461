"use client"

import { <PERSON><PERSON>ir<PERSON>, Circle, <PERSON>, Loader2 } from "lucide-react"

interface CreationStep {
  id: string
  title: string
  description: string
  status: "pending" | "processing" | "completed" | "failed" | "skipped"
}

interface TokenCreationProgressProps {
  steps: CreationStep[]
}

export function TokenCreationProgress({ steps }: TokenCreationProgressProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "processing":
        return <Loader2 className="h-5 w-5 text-primary animate-spin" />
      case "failed":
        return <Circle className="h-5 w-5 text-red-500" />
      case "skipped":
        return <Clock className="h-5 w-5 text-amber-500" />
      default:
        return <Circle className="h-5 w-5 text-muted-foreground" />
    }
  }

  const getStatusClass = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-500"
      case "processing":
        return "text-primary"
      case "failed":
        return "text-red-500"
      case "skipped":
        return "text-amber-500"
      default:
        return "text-muted-foreground"
    }
  }

  return (
    <div className="space-y-4">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-start gap-3">
          <div className="mt-0.5">{getStatusIcon(step.status)}</div>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <h4 className={`font-medium ${getStatusClass(step.status)}`}>{step.title}</h4>
              <div className="text-xs font-medium">
                {step.status === "completed" && <span className="text-green-500">Terminé</span>}
                {step.status === "processing" && <span className="text-primary">En cours...</span>}
                {step.status === "failed" && <span className="text-red-500">Échec</span>}
                {step.status === "skipped" && <span className="text-amber-500">Ignoré</span>}
                {step.status === "pending" && <span className="text-muted-foreground">En attente</span>}
              </div>
            </div>
            <p className="text-sm text-muted-foreground">{step.description}</p>
          </div>
        </div>
      ))}
    </div>
  )
}

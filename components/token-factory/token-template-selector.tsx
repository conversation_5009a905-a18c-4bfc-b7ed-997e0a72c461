"use client"

import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { Check, X } from "lucide-react"

interface TokenTemplate {
  id: string
  name: string
  description: string
  symbol: string
  decimals: number
  initialSupply: number
  maxSupply: number
  logo: string
}

interface TokenTemplateSelectorProps {
  templates: TokenTemplate[]
  onSelect: (templateId: string) => void
  onCancel: () => void
}

export function TokenTemplateSelector({ templates, onSelect, onCancel }: TokenTemplateSelectorProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Choisir un template</h3>
        <Button variant="ghost" size="icon" onClick={onCancel}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => (
          <Card key={template.id} className="overflow-hidden hover:border-primary transition-colors">
            <div className="relative h-32 bg-muted">
              <Image
                src={template.logo || "/placeholder.svg"}
                alt={template.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 300px"
              />
            </div>
            <CardContent className="p-4">
              <h4 className="font-medium">{template.name}</h4>
              <p className="text-sm text-muted-foreground line-clamp-2 mt-1">{template.description}</p>
              <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-3 text-xs">
                <div>
                  <span className="text-muted-foreground">Symbole:</span> {template.symbol}
                </div>
                <div>
                  <span className="text-muted-foreground">Décimales:</span> {template.decimals}
                </div>
                <div>
                  <span className="text-muted-foreground">Offre initiale:</span>{" "}
                  {template.initialSupply.toLocaleString()}
                </div>
                <div>
                  <span className="text-muted-foreground">Offre max:</span> {template.maxSupply.toLocaleString()}
                </div>
              </div>
            </CardContent>
            <CardFooter className="p-4 pt-0">
              <Button className="w-full" onClick={() => onSelect(template.id)}>
                <Check className="h-4 w-4 mr-2" />
                Utiliser ce template
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}

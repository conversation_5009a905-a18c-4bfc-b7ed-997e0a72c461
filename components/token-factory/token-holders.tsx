"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Wallet, RefreshCw } from "lucide-react"

interface Holder {
  id: string
  address: string
  balance: number
  percentage: number
  since: string
}

interface TokenHoldersProps {
  tokenAddress: string
  totalSupply: number
}

export function TokenHolders({ tokenAddress, totalSupply }: TokenHoldersProps) {
  const [holders, setHolders] = useState<Holder[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // Simuler le chargement des détenteurs
  useEffect(() => {
    const fetchHolders = async () => {
      try {
        setLoading(true)
        // Simuler une requête API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Générer des données fictives pour la démonstration
        const mockHolders: Holder[] = Array.from({ length: 10 }, (_, i) => {
          const balance = Math.floor(Math.random() * 100000) + 1000
          const percentage = (balance / totalSupply) * 100
          const since = new Date(Date.now() - Math.random() * 86400000 * 30).toISOString()
          const address = `Wallet${Math.floor(Math.random() * 1000)}...${Math.random().toString(36).substring(2, 6)}`

          return {
            id: `holder-${page}-${i}`,
            address,
            balance,
            percentage,
            since,
          }
        })

        // Trier par solde décroissant
        mockHolders.sort((a, b) => b.balance - a.balance)

        if (page === 1) {
          setHolders(mockHolders)
        } else {
          setHolders((prev) => [...prev, ...mockHolders])
        }

        // Simuler la fin des données après 2 pages
        setHasMore(page < 2)
      } catch (error) {
        console.error("Erreur lors du chargement des détenteurs:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchHolders()
  }, [tokenAddress, totalSupply, page])

  // Charger plus de détenteurs
  const loadMore = () => {
    if (!loading && hasMore) {
      setPage((prev) => prev + 1)
    }
  }

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Détenteurs du token</CardTitle>
        <CardDescription>Liste des adresses détenant ce token</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {holders.length === 0 && !loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Aucun détenteur trouvé pour ce token</p>
            </div>
          ) : (
            <div className="space-y-4">
              {holders.map((holder) => (
                <div key={holder.id} className="p-4 border rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <Wallet className="h-4 w-4 text-blue-700" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{holder.address}</p>
                      <p className="text-sm text-muted-foreground">Depuis le {formatDate(holder.since)}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{holder.balance.toLocaleString()} tokens</p>
                      <p className="text-sm text-muted-foreground">{holder.percentage.toFixed(2)}% de l'offre</p>
                    </div>
                  </div>
                  <Progress value={holder.percentage} className="h-1.5" />
                </div>
              ))}
            </div>
          )}

          {loading && (
            <div className="flex justify-center py-4">
              <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          )}
        </div>
      </CardContent>
      {hasMore && !loading && (
        <CardFooter>
          <Button variant="outline" className="w-full" onClick={loadMore}>
            Charger plus de détenteurs
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}

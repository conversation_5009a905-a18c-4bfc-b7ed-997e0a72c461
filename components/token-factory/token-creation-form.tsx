"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { useForm, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Stepper, Step, StepTitle, StepDescription } from "@/components/ui/stepper"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"
import { BasicInfoForm } from "./form-steps/basic-info-form"
import { TokenomicsForm } from "./form-steps/tokenomics-form"
import { SecurityForm } from "./form-steps/security-form"
import { LaunchForm } from "./form-steps/launch-form"
import { MarketingForm } from "./form-steps/marketing-form"
import { TokenPreview } from "./token-preview"
import { TokenCreationProgress } from "./token-creation-progress"
import { AlertCircle, CheckCircle2, Info, Loader2, ArrowRight } from "lucide-react"

// Schéma de validation pour le formulaire complet
const tokenFormSchema = z.object({
  // Informations de base
  name: z.string().min(3, { message: "Le nom doit contenir au moins 3 caractères" }),
  symbol: z.string().min(2, { message: "Le symbole doit contenir au moins 2 caractères" }).max(10),
  description: z.string().optional(),
  decimals: z.number().int().min(0).max(9),
  initialSupply: z.number().positive(),
  maxSupply: z.number().positive(),
  logo: z.any().optional(),

  // Tokenomics
  distribution: z.array(
    z.object({
      label: z.string(),
      percentage: z.number().min(0).max(100),
      address: z.string().optional(),
    }),
  ),

  // Sécurité
  isMintable: z.boolean().default(true),
  isBurnable: z.boolean().default(true),
  isPausable: z.boolean().default(false),
  isTransferTaxable: z.boolean().default(false),
  transferTaxRate: z.number().min(0).max(10),
  maxTxAmount: z.number().min(0),
  maxWalletAmount: z.number().min(0),
  antiBot: z.boolean().default(false),
  antiDump: z.boolean().default(false),

  // Lancement
  addLiquidity: z.boolean().default(false),
  liquidityAmount: z.number().min(0),
  liquidityPercentage: z.number().min(0).max(100),
  lockLiquidity: z.boolean().default(false),
  lockDuration: z.number().min(0),
  setupInitialTrade: z.boolean().default(false),

  // Marketing
  website: z.string().url().optional().or(z.literal("")),
  twitter: z.string().url().optional().or(z.literal("")),
  telegram: z.string().url().optional().or(z.literal("")),
  discord: z.string().url().optional().or(z.literal("")),
})

type TokenFormValues = z.infer<typeof tokenFormSchema>

export function TokenCreationForm() {
  const router = useRouter()
  const { publicKey, signTransaction, connected } = useWallet()
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()
  const [activeStep, setActiveStep] = useState(0)
  const [isCreating, setIsCreating] = useState(false)
  const [creationProgress, setCreationProgress] = useState(0)
  const [creationStep, setCreationStep] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [createdTokenAddress, setCreatedTokenAddress] = useState<string | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const [creationSteps, setCreationSteps] = useState([
    {
      id: "init",
      title: "Initialisation",
      description: "Préparation des paramètres du token",
      status: "pending" as const,
    },
    {
      id: "address",
      title: "Génération d'adresse",
      description: "Création de l'adresse du token",
      status: "pending" as const,
    },
    {
      id: "transaction",
      title: "Création du token",
      description: "Déploiement sur la blockchain",
      status: "pending" as const,
    },
    {
      id: "metadata",
      title: "Métadonnées",
      description: "Ajout des métadonnées du token",
      status: "pending" as const,
    },
    {
      id: "liquidity",
      title: "Liquidité",
      description: "Configuration de la liquidité",
      status: "pending" as const,
    },
  ])

  // Initialiser le formulaire avec des valeurs par défaut
  const methods = useForm<TokenFormValues>({
    resolver: zodResolver(tokenFormSchema),
    defaultValues: {
      name: "",
      symbol: "",
      description: "",
      decimals: 9,
      initialSupply: 1000000000,
      maxSupply: 10000000000,
      distribution: [
        { label: "Équipe", percentage: 15 },
        { label: "Marketing", percentage: 10 },
        { label: "Développement", percentage: 15 },
        { label: "Réserve", percentage: 10 },
        { label: "Liquidité", percentage: 50 },
      ],
      isMintable: true,
      isBurnable: true,
      isPausable: false,
      isTransferTaxable: false,
      transferTaxRate: 0,
      maxTxAmount: 0,
      maxWalletAmount: 0,
      antiBot: false,
      antiDump: false,
      addLiquidity: false,
      liquidityAmount: 0.1,
      liquidityPercentage: 50,
      lockLiquidity: false,
      lockDuration: 180,
      setupInitialTrade: false,
      website: "",
      twitter: "",
      telegram: "",
      discord: "",
    },
  })

  // Vérifier le solde du portefeuille
  useEffect(() => {
    if (publicKey) {
      // Simuler la récupération du solde (à remplacer par un appel réel à la blockchain)
      setWalletBalance(1.5) // Exemple: 1.5 SOL
    }
  }, [publicKey])

  // Étapes du formulaire
  const steps = [
    {
      id: "basic",
      title: "Informations de base",
      description: "Nom, symbole et offre",
    },
    {
      id: "tokenomics",
      title: "Tokenomics",
      description: "Distribution et vesting",
    },
    {
      id: "security",
      title: "Sécurité",
      description: "Protections et limites",
    },
    {
      id: "launch",
      title: "Lancement",
      description: "Liquidité et listing",
    },
    {
      id: "marketing",
      title: "Marketing",
      description: "Présence en ligne",
    },
  ]

  // Gérer le changement d'étape
  const nextStep = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1)
    }
  }

  const prevStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1)
    }
  }

  // Gérer la soumission du formulaire
  const onSubmit = async (data: TokenFormValues) => {
    if (!connected || !publicKey || !signTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour créer un token",
        variant: "destructive",
      })
      return
    }

    // Vérifier le solde du portefeuille
    const requiredBalance = 0.01 // Minimum SOL required
    if (walletBalance === null || walletBalance < requiredBalance) {
      toast({
        title: "Solde insuffisant",
        description: `Veuillez ajouter au moins ${requiredBalance} SOL à votre portefeuille pour couvrir les frais de transaction.`,
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setCreationProgress(0)
    setCreationStep("Initialisation de la création du token...")
    setError(null)

    try {
      // Simuler le processus de création du token
      // Étape 1: Initialisation
      setCreationProgress(10)
      setCreationStep("Initialisation...")
      updateCreationStatus("init", "processing")
      await new Promise((resolve) => setTimeout(resolve, 1500))
      updateCreationStatus("init", "completed")

      // Étape 2: Génération d'adresse
      setCreationProgress(30)
      setCreationStep("Génération de l'adresse du token...")
      updateCreationStatus("address", "processing")
      await new Promise((resolve) => setTimeout(resolve, 2000))
      updateCreationStatus("address", "completed")

      // Étape 3: Création du token
      setCreationProgress(50)
      setCreationStep("Déploiement du token sur la blockchain...")
      updateCreationStatus("transaction", "processing")
      await new Promise((resolve) => setTimeout(resolve, 3000))
      updateCreationStatus("transaction", "completed")

      // Étape 4: Métadonnées
      setCreationProgress(70)
      setCreationStep("Ajout des métadonnées du token...")
      updateCreationStatus("metadata", "processing")
      await new Promise((resolve) => setTimeout(resolve, 1500))
      updateCreationStatus("metadata", "completed")

      // Étape 5: Liquidité (si applicable)
      if (data.addLiquidity) {
        setCreationProgress(90)
        setCreationStep("Configuration de la liquidité...")
        updateCreationStatus("liquidity", "processing")
        await new Promise((resolve) => setTimeout(resolve, 2000))
        updateCreationStatus("liquidity", "completed")
      } else {
        updateCreationStatus("liquidity", "skipped")
      }

      // Finalisation
      setCreationProgress(100)
      setCreationStep("Token créé avec succès!")

      // Simuler une adresse de token créée
      const mockTokenAddress = "TokenAddressXYZ123456789abcdef"
      setCreatedTokenAddress(mockTokenAddress)
      setSuccess(true)

      toast({
        title: "Token créé avec succès",
        description: `Votre token ${data.name} (${data.symbol}) a été créé avec succès!`,
      })
    } catch (err: any) {
      console.error("Erreur lors de la création du token:", err)
      setError(err.message || "Une erreur s'est produite lors de la création du token")
      toast({
        title: "Échec de la création du token",
        description: err.message || "Une erreur s'est produite lors de la création du token",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  // Mettre à jour le statut d'une étape de création
  const updateCreationStatus = (
    stepId: string,
    status: "pending" | "processing" | "completed" | "failed" | "skipped",
  ) => {
    setCreationSteps((prevSteps) =>
      prevSteps.map((step) => (step.id === stepId ? { ...step, status: status as any } : step)),
    )
  }

  // Gérer le changement de logo
  const handleLogoChange = (file: File | null, preview: string | null) => {
    methods.setValue("logo", file)
    setLogoPreview(preview)
  }

  // Si le token est en cours de création, afficher la progression
  if (isCreating) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Création du token en cours</CardTitle>
          <CardDescription>Veuillez patienter pendant que nous créons votre token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium">{creationStep}</h3>
            <Progress value={creationProgress} className="h-2 mt-2" />
          </div>

          <TokenCreationProgress steps={creationSteps} />

          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <h4 className="font-medium">Détails du token</h4>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Nom:</span> {methods.getValues("name")}
              </div>
              <div>
                <span className="text-muted-foreground">Symbole:</span> {methods.getValues("symbol")}
              </div>
              <div>
                <span className="text-muted-foreground">Décimales:</span> {methods.getValues("decimals")}
              </div>
              <div>
                <span className="text-muted-foreground">Offre initiale:</span>{" "}
                {methods.getValues("initialSupply").toLocaleString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Si le token a été créé avec succès, afficher les détails
  if (success && createdTokenAddress) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-6 w-6 text-green-500" />
            <CardTitle>Token créé avec succès</CardTitle>
          </div>
          <CardDescription>Votre token a été créé sur la blockchain {activeNetwork?.name || "Solana"}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Félicitations!</AlertTitle>
            <AlertDescription>
              Votre token {methods.getValues("name")} ({methods.getValues("symbol")}) a été créé avec succès. Vous
              pouvez maintenant le gérer et le promouvoir.
            </AlertDescription>
          </Alert>

          <TokenPreview
            name={methods.getValues("name")}
            symbol={methods.getValues("symbol")}
            logoUrl={logoPreview}
            address={createdTokenAddress}
            supply={methods.getValues("initialSupply")}
            decimals={methods.getValues("decimals")}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="default"
              onClick={() => {
                window.open(`https://explorer.solana.com/address/${createdTokenAddress}?cluster=devnet`, "_blank")
              }}
            >
              Voir sur l'explorateur
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                router.push("/token-factory/tokens")
              }}
            >
              Voir tous mes tokens
            </Button>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Prochaines étapes</h3>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <ArrowRight className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">Ajouter de la liquidité</p>
                  <p className="text-sm text-muted-foreground">
                    Créez un pool de liquidité sur un DEX pour permettre l'échange de votre token
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <ArrowRight className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">Promouvoir votre token</p>
                  <p className="text-sm text-muted-foreground">
                    Partagez votre token sur les réseaux sociaux et les communautés crypto
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <ArrowRight className="h-5 w-5 text-primary mt-0.5" />
                <div>
                  <p className="font-medium">Configurer votre tokenomics</p>
                  <p className="text-sm text-muted-foreground">
                    Définissez des règles de vesting et de distribution pour votre token
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button
            className="w-full"
            onClick={() => {
              methods.reset()
              setLogoPreview(null)
              setSuccess(false)
              setCreatedTokenAddress(null)
              setActiveStep(0)
            }}
          >
            Créer un autre token
          </Button>
        </CardFooter>
      </Card>
    )
  }

  // Afficher le formulaire de création de token
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Créer un nouveau token</CardTitle>
        <CardDescription>
          Remplissez le formulaire ci-dessous pour créer votre propre token sur {activeNetwork?.name || "Solana"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
            <Stepper currentStep={activeStep} className="mb-8">
              {steps.map((step, index) => (
                <Step key={index} onClick={() => setActiveStep(index)}>
                  <StepTitle>{step.title}</StepTitle>
                  <StepDescription>{step.description}</StepDescription>
                </Step>
              ))}
            </Stepper>

            <div className="mt-8">
              {activeStep === 0 && <BasicInfoForm logoPreview={logoPreview} onLogoChange={handleLogoChange} />}

              {activeStep === 1 && <TokenomicsForm />}

              {activeStep === 2 && <SecurityForm />}

              {activeStep === 3 && <LaunchForm />}

              {activeStep === 4 && <MarketingForm />}
            </div>

            <div className="flex justify-between pt-4">
              <Button type="button" variant="outline" onClick={prevStep} disabled={activeStep === 0}>
                Précédent
              </Button>
              {activeStep < steps.length - 1 ? (
                <Button type="button" onClick={nextStep}>
                  Suivant
                </Button>
              ) : (
                <Button type="submit" disabled={isCreating || !connected}>
                  {isCreating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Création en cours...
                    </>
                  ) : (
                    "Créer le token"
                  )}
                </Button>
              )}
            </div>
          </form>
        </FormProvider>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <div className="text-sm text-muted-foreground">
          {connected ? (
            <>
              Connecté: {publicKey?.toString().slice(0, 6)}...{publicKey?.toString().slice(-4)}
              {walletBalance !== null && <> | Solde: {walletBalance.toFixed(4)} SOL</>}
            </>
          ) : (
            "Veuillez connecter votre portefeuille pour créer un token"
          )}
        </div>
        <div className="text-sm">
          Prix: <span className="font-medium">0.01 SOL</span>
        </div>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ImagePlus, Check } from "lucide-react"
import Image from "next/image"

// Catégories d'images prédéfinies
const IMAGE_CATEGORIES = [
  { id: "crypto", name: "Crypto" },
  { id: "abstract", name: "Abstract" },
  { id: "animals", name: "Animals" },
  { id: "space", name: "Space" },
  { id: "tech", name: "Technology" },
]

// Images prédéfinies par catégorie
const PREDEFINED_IMAGES = {
  crypto: [
    { id: "crypto1", url: "/digital-token.png", alt: "Digital Token" },
    { id: "crypto2", url: "/bitcoin-gold-concept.png", alt: "Bitcoin Gold" },
    { id: "crypto3", url: "/placeholder.svg?key=2gbef", alt: "Ethereum Token" },
    { id: "crypto4", url: "/crypto-coin.png", alt: "Crypto Coin" },
    { id: "crypto5", url: "/blockchain-token.png", alt: "Blockchain Token" },
  ],
  abstract: [
    { id: "abstract1", url: "/abstract-geometric-shapes.png", alt: "Abstract Geometric" },
    { id: "abstract2", url: "/abstract-geometric-sm.png", alt: "Abstract Geometric Small" },
    { id: "abstract3", url: "/abstract-tb.png", alt: "Abstract TB" },
    { id: "abstract4", url: "/abstract-waves.png", alt: "Abstract Waves" },
    { id: "abstract5", url: "/abstract-circles.png", alt: "Abstract Circles" },
  ],
  animals: [
    { id: "animal1", url: "/lion-token.png", alt: "Lion Token" },
    { id: "animal2", url: "/placeholder.svg?height=200&width=200&query=wolf token", alt: "Wolf Token" },
    { id: "animal3", url: "/placeholder.svg?height=200&width=200&query=eagle token", alt: "Eagle Token" },
    { id: "animal4", url: "/placeholder.svg?height=200&width=200&query=bull token", alt: "Bull Token" },
    { id: "animal5", url: "/placeholder.svg?height=200&width=200&query=bear token", alt: "Bear Token" },
  ],
  space: [
    { id: "space1", url: "/placeholder.svg?height=200&width=200&query=planet token", alt: "Planet Token" },
    { id: "space2", url: "/placeholder.svg?height=200&width=200&query=galaxy token", alt: "Galaxy Token" },
    { id: "space3", url: "/placeholder.svg?height=200&width=200&query=star token", alt: "Star Token" },
    { id: "space4", url: "/placeholder.svg?height=200&width=200&query=moon token", alt: "Moon Token" },
    { id: "space5", url: "/placeholder.svg?height=200&width=200&query=rocket token", alt: "Rocket Token" },
  ],
  tech: [
    { id: "tech1", url: "/placeholder.svg?height=200&width=200&query=circuit token", alt: "Circuit Token" },
    { id: "tech2", url: "/placeholder.svg?height=200&width=200&query=robot token", alt: "Robot Token" },
    { id: "tech3", url: "/placeholder.svg?height=200&width=200&query=ai token", alt: "AI Token" },
    { id: "tech4", url: "/placeholder.svg?height=200&width=200&query=digital network", alt: "Digital Network" },
    { id: "tech5", url: "/placeholder.svg?height=200&width=200&query=tech gear", alt: "Tech Gear" },
  ],
}

interface TokenImageGalleryProps {
  onSelectImage: (imageUrl: string) => void
  selectedImage?: string | null
}

export default function TokenImageGallery({ onSelectImage, selectedImage }: TokenImageGalleryProps) {
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("crypto")
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null)

  // Trouver l'ID de l'image sélectionnée au chargement
  useEffect(() => {
    if (selectedImage) {
      for (const category in PREDEFINED_IMAGES) {
        const foundImage = PREDEFINED_IMAGES[category as keyof typeof PREDEFINED_IMAGES].find(
          (img) => img.url === selectedImage,
        )
        if (foundImage) {
          setSelectedImageId(foundImage.id)
          break
        }
      }
    }
  }, [selectedImage])

  const handleSelectImage = (imageUrl: string, imageId: string) => {
    onSelectImage(imageUrl)
    setSelectedImageId(imageId)
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full flex items-center justify-center gap-2">
          <ImagePlus className="h-4 w-4" />
          Choisir une image prédéfinie
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Galerie d'images</DialogTitle>
        </DialogHeader>
        <Tabs defaultValue="crypto" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 mb-4">
            {IMAGE_CATEGORIES.map((category) => (
              <TabsTrigger key={category.id} value={category.id}>
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>
          {IMAGE_CATEGORIES.map((category) => (
            <TabsContent key={category.id} value={category.id}>
              <ScrollArea className="h-[300px] p-2">
                <div className="grid grid-cols-3 gap-4">
                  {PREDEFINED_IMAGES[category.id as keyof typeof PREDEFINED_IMAGES].map((image) => (
                    <div
                      key={image.id}
                      className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                        selectedImageId === image.id
                          ? "border-primary ring-2 ring-primary/20"
                          : "border-transparent hover:border-gray-300"
                      }`}
                      onClick={() => handleSelectImage(image.url, image.id)}
                    >
                      <div className="relative h-24 w-full">
                        <Image src={image.url || "/placeholder.svg"} alt={image.alt} fill className="object-cover" />
                      </div>
                      {selectedImageId === image.id && (
                        <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                          <Check className="h-3 w-3" />
                        </div>
                      )}
                      <div className="p-2 text-center text-xs truncate">{image.alt}</div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          ))}
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

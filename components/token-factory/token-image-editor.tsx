"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Edit, Save, RotateCw } from "lucide-react"

interface TokenImageEditorProps {
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

export default function TokenImageEditor({ imageUrl, onSave }: TokenImageEditorProps) {
  const [open, setOpen] = useState(false)
  const [scale, setScale] = useState(1)
  const [rotation, setRotation] = useState(0)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement | null>(null)

  useEffect(() => {
    if (open && imageUrl) {
      const img = new Image()
      img.crossOrigin = "anonymous"
      img.src = imageUrl
      img.onload = () => {
        imageRef.current = img
        drawImage()
      }
    }
  }, [open, imageUrl])

  const drawImage = () => {
    if (!canvasRef.current || !imageRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Save context
    ctx.save()

    // Move to center of canvas
    ctx.translate(canvas.width / 2, canvas.height / 2)

    // Rotate
    ctx.rotate((rotation * Math.PI) / 180)

    // Scale
    ctx.scale(scale, scale)

    // Draw image centered
    ctx.drawImage(
      imageRef.current,
      -imageRef.current.width / 2,
      -imageRef.current.height / 2,
      imageRef.current.width,
      imageRef.current.height,
    )

    // Restore context
    ctx.restore()
  }

  useEffect(() => {
    if (open) {
      drawImage()
    }
  }, [scale, rotation, open])

  const handleSave = () => {
    if (!canvasRef.current) return

    try {
      const editedImageUrl = canvasRef.current.toDataURL("image/png")
      onSave(editedImageUrl)
      setOpen(false)
    } catch (error) {
      console.error("Error saving edited image:", error)
    }
  }

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-1">
          <Edit className="h-4 w-4" />
          Éditer
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Éditer l'image</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center gap-4">
          <div className="border rounded-lg overflow-hidden bg-gray-100 w-[300px] h-[300px] flex items-center justify-center">
            <canvas ref={canvasRef} width={300} height={300} className="max-w-full max-h-full" />
          </div>

          <div className="w-full space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Zoom</span>
                <span className="text-sm text-muted-foreground">{Math.round(scale * 100)}%</span>
              </div>
              <Slider
                value={[scale * 100]}
                min={50}
                max={200}
                step={5}
                onValueChange={(value) => setScale(value[0] / 100)}
              />
            </div>

            <div className="flex justify-between gap-2">
              <Button variant="outline" onClick={handleRotate} className="flex-1">
                <RotateCw className="h-4 w-4 mr-2" />
                Rotation
              </Button>
              <Button onClick={handleSave} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Enregistrer
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

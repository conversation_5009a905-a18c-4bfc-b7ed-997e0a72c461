"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from "recharts"
import { ChartContainer } from "@/components/ui/chart"
import { Percent, Wallet, AlertTriangle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface TaxConfigurationProps {
  initialValues?: {
    enabled: boolean
    buyTax: {
      total: number
      liquidity: number
      marketing: number
      development: number
      burn: number
    }
    sellTax: {
      total: number
      liquidity: number
      marketing: number
      development: number
      burn: number
    }
    marketingWallet?: string
    developmentWallet?: string
  }
  onChange?: (values: any) => void
}

export function TaxConfiguration({ initialValues, onChange }: TaxConfigurationProps) {
  const [enabled, setEnabled] = useState(initialValues?.enabled || false)
  const [buyTax, setBuyTax] = useState({
    total: initialValues?.buyTax?.total || 5,
    liquidity: initialValues?.buyTax?.liquidity || 2,
    marketing: initialValues?.buyTax?.marketing || 2,
    development: initialValues?.buyTax?.development || 1,
    burn: initialValues?.buyTax?.burn || 0,
  })
  const [sellTax, setSellTax] = useState({
    total: initialValues?.sellTax?.total || 5,
    liquidity: initialValues?.sellTax?.liquidity || 2,
    marketing: initialValues?.sellTax?.marketing || 2,
    development: initialValues?.sellTax?.development || 1,
    burn: initialValues?.sellTax?.burn || 0,
  })
  const [marketingWallet, setMarketingWallet] = useState(initialValues?.marketingWallet || "")
  const [developmentWallet, setDevelopmentWallet] = useState(initialValues?.developmentWallet || "")

  // Mettre à jour le parent lorsque les valeurs changent
  const updateValues = (newValues: any) => {
    if (onChange) {
      onChange({
        enabled,
        buyTax,
        sellTax,
        marketingWallet,
        developmentWallet,
        ...newValues,
      })
    }
  }

  // Mettre à jour les taxes d'achat
  const updateBuyTax = (field: keyof typeof buyTax, value: number) => {
    const newBuyTax = { ...buyTax, [field]: value }
    setBuyTax(newBuyTax)
    updateValues({ buyTax: newBuyTax })
  }

  // Mettre à jour les taxes de vente
  const updateSellTax = (field: keyof typeof sellTax, value: number) => {
    const newSellTax = { ...sellTax, [field]: value }
    setSellTax(newSellTax)
    updateValues({ sellTax: newSellTax })
  }

  // Vérifier si la répartition des taxes est valide
  const isBuyTaxDistributionValid =
    buyTax.liquidity + buyTax.marketing + buyTax.development + buyTax.burn === buyTax.total
  const isSellTaxDistributionValid =
    sellTax.liquidity + sellTax.marketing + sellTax.development + sellTax.burn === sellTax.total

  // Préparer les données pour les graphiques
  const buyTaxData = [
    { name: "Liquidité", value: buyTax.liquidity, color: "#3b82f6" },
    { name: "Marketing", value: buyTax.marketing, color: "#10b981" },
    { name: "Développement", value: buyTax.development, color: "#f97316" },
    { name: "Burn", value: buyTax.burn, color: "#ef4444" },
  ].filter((item) => item.value > 0)

  const sellTaxData = [
    { name: "Liquidité", value: sellTax.liquidity, color: "#3b82f6" },
    { name: "Marketing", value: sellTax.marketing, color: "#10b981" },
    { name: "Développement", value: sellTax.development, color: "#f97316" },
    { name: "Burn", value: sellTax.burn, color: "#ef4444" },
  ].filter((item) => item.value > 0)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Percent className="h-5 w-5" />
          Configuration des taxes
        </CardTitle>
        <CardDescription>Définissez les taxes appliquées aux transactions et leur répartition</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">Activer les taxes</h3>
            <p className="text-sm text-muted-foreground">
              Appliquer des taxes sur les transactions pour financer le développement et le marketing
            </p>
          </div>
          <Switch
            checked={enabled}
            onCheckedChange={(checked) => {
              setEnabled(checked)
              updateValues({ enabled: checked })
            }}
          />
        </div>

        {enabled && (
          <Tabs defaultValue="buy" className="space-y-4">
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="buy">Taxes d'achat</TabsTrigger>
              <TabsTrigger value="sell">Taxes de vente</TabsTrigger>
            </TabsList>

            <TabsContent value="buy" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <Label htmlFor="buy-tax-total">Taxe totale d'achat</Label>
                      <span className="text-sm">{buyTax.total}%</span>
                    </div>
                    <Slider
                      id="buy-tax-total"
                      value={[buyTax.total]}
                      min={0}
                      max={10}
                      step={0.5}
                      onValueChange={(values) => updateBuyTax("total", values[0])}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Pourcentage total prélevé sur chaque achat de token
                    </p>
                  </div>

                  <div className="space-y-3 pl-6 border-l-2 border-blue-100">
                    <h4 className="text-sm font-medium">Répartition de la taxe d'achat</h4>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Label htmlFor="buy-tax-liquidity">Liquidité</Label>
                        <span className="text-sm">{buyTax.liquidity}%</span>
                      </div>
                      <Slider
                        id="buy-tax-liquidity"
                        value={[buyTax.liquidity]}
                        min={0}
                        max={buyTax.total}
                        step={0.5}
                        onValueChange={(values) => updateBuyTax("liquidity", values[0])}
                      />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Label htmlFor="buy-tax-marketing">Marketing</Label>
                        <span className="text-sm">{buyTax.marketing}%</span>
                      </div>
                      <Slider
                        id="buy-tax-marketing"
                        value={[buyTax.marketing]}
                        min={0}
                        max={buyTax.total}
                        step={0.5}
                        onValueChange={(values) => updateBuyTax("marketing", values[0])}
                      />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Label htmlFor="buy-tax-development">Développement</Label>
                        <span className="text-sm">{buyTax.development}%</span>
                      </div>
                      <Slider
                        id="buy-tax-development"
                        value={[buyTax.development]}
                        min={0}
                        max={buyTax.total}
                        step={0.5}
                        onValueChange={(values) => updateBuyTax("development", values[0])}
                      />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Label htmlFor="buy-tax-burn">Burn</Label>
                        <span className="text-sm">{buyTax.burn}%</span>
                      </div>
                      <Slider
                        id="buy-tax-burn"
                        value={[buyTax.burn]}
                        min={0}
                        max={buyTax.total}
                        step={0.5}
                        onValueChange={(values) => updateBuyTax("burn", values[0])}
                      />
                    </div>

                    {!isBuyTaxDistributionValid && (
                      <Alert variant="warning">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          La somme des répartitions (
                          {buyTax.liquidity + buyTax.marketing + buyTax.development + buyTax.burn}%) doit être égale à
                          la taxe totale ({buyTax.total}%)
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>

                <div className="flex flex-col items-center justify-center">
                  <ChartContainer className="h-[200px] w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={buyTaxData}
                          dataKey="value"
                          nameKey="name"
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          label={({ name, value }) => `${name}: ${value}%`}
                        >
                          {buyTaxData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => `${value}%`} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="sell" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <Label htmlFor="sell-tax-total">Taxe totale de vente</Label>
                      <span className="text-sm">{sellTax.total}%</span>
                    </div>
                    <Slider
                      id="sell-tax-total"
                      value={[sellTax.total]}
                      min={0}
                      max={10}
                      step={0.5}
                      onValueChange={(values) => updateSellTax("total", values[0])}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Pourcentage total prélevé sur chaque vente de token
                    </p>
                  </div>

                  <div className="space-y-3 pl-6 border-l-2 border-blue-100">
                    <h4 className="text-sm font-medium">Répartition de la taxe de vente</h4>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Label htmlFor="sell-tax-liquidity">Liquidité</Label>
                        <span className="text-sm">{sellTax.liquidity}%</span>
                      </div>
                      <Slider
                        id="sell-tax-liquidity"
                        value={[sellTax.liquidity]}
                        min={0}
                        max={sellTax.total}
                        step={0.5}
                        onValueChange={(values) => updateSellTax("liquidity", values[0])}
                      />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Label htmlFor="sell-tax-marketing">Marketing</Label>
                        <span className="text-sm">{sellTax.marketing}%</span>
                      </div>
                      <Slider
                        id="sell-tax-marketing"
                        value={[sellTax.marketing]}
                        min={0}
                        max={sellTax.total}
                        step={0.5}
                        onValueChange={(values) => updateSellTax("marketing", values[0])}
                      />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Label htmlFor="sell-tax-development">Développement</Label>
                        <span className="text-sm">{sellTax.development}%</span>
                      </div>
                      <Slider
                        id="sell-tax-development"
                        value={[sellTax.development]}
                        min={0}
                        max={sellTax.total}
                        step={0.5}
                        onValueChange={(values) => updateSellTax("development", values[0])}
                      />
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <Label htmlFor="sell-tax-burn">Burn</Label>
                        <span className="text-sm">{sellTax.burn}%</span>
                      </div>
                      <Slider
                        id="sell-tax-burn"
                        value={[sellTax.burn]}
                        min={0}
                        max={sellTax.total}
                        step={0.5}
                        onValueChange={(values) => updateSellTax("burn", values[0])}
                      />
                    </div>

                    {!isSellTaxDistributionValid && (
                      <Alert variant="warning">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          La somme des répartitions (
                          {sellTax.liquidity + sellTax.marketing + sellTax.development + sellTax.burn}%) doit être égale
                          à la taxe totale ({sellTax.total}%)
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>

                <div className="flex flex-col items-center justify-center">
                  <ChartContainer className="h-[200px] w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={sellTaxData}
                          dataKey="value"
                          nameKey="name"
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          label={({ name, value }) => `${name}: ${value}%`}
                        >
                          {sellTaxData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => `${value}%`} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}

        {enabled && (
          <div className="space-y-4 pt-4 border-t">
            <h3 className="font-medium">Adresses de destination</h3>

            <div>
              <Label htmlFor="marketing-wallet" className="flex items-center gap-2">
                <Wallet className="h-4 w-4" /> Portefeuille marketing
              </Label>
              <Input
                id="marketing-wallet"
                placeholder="Adresse du portefeuille marketing"
                value={marketingWallet}
                onChange={(e) => {
                  setMarketingWallet(e.target.value)
                  updateValues({ marketingWallet: e.target.value })
                }}
              />
              <p className="text-xs text-muted-foreground mt-1">Adresse qui recevra les taxes allouées au marketing</p>
            </div>

            <div>
              <Label htmlFor="development-wallet" className="flex items-center gap-2">
                <Wallet className="h-4 w-4" /> Portefeuille développement
              </Label>
              <Input
                id="development-wallet"
                placeholder="Adresse du portefeuille développement"
                value={developmentWallet}
                onChange={(e) => {
                  setDevelopmentWallet(e.target.value)
                  updateValues({ developmentWallet: e.target.value })
                }}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Adresse qui recevra les taxes allouées au développement
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

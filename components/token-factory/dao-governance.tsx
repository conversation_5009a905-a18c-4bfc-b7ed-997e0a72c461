"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { Users, FileText, Percent, Shield, Ban, BarChart3, Wallet, Clock } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"

interface DaoGovernanceProps {
  tokenAddress: string
}

interface Proposal {
  id: string
  title: string
  description: string
  proposer: string
  createdAt: number
  endTime: number
  status: "active" | "passed" | "rejected" | "executed"
  votesFor: number
  votesAgainst: number
  quorum: number
  type: "fees" | "limits" | "unblock" | "priceImpact" | "funds" | "other"
  changes?: any
}

export default function DaoGovernance({ tokenAddress }: DaoGovernanceProps) {
  const { publicKey, connected, signMessage } = useWallet()
  const { toast } = useToast()

  const [proposals, setProposals] = useState<Proposal[]>([])
  const [activeProposal, setActiveProposal] = useState<Proposal | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("active")

  // États pour la création de proposition
  const [proposalTitle, setProposalTitle] = useState("")
  const [proposalDescription, setProposalDescription] = useState("")
  const [proposalType, setProposalType] = useState<string>("fees")
  const [isCreatingProposal, setIsCreatingProposal] = useState(false)

  // Charger les propositions
  useEffect(() => {
    const fetchProposals = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Dans une implémentation réelle, nous ferions un appel API
        // Simuler un délai de chargement
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Données simulées pour la démo
        const mockProposals: Proposal[] = [
          {
            id: "prop1",
            title: "Réduction des frais de base",
            description: "Proposition de réduction des frais de base de 1% à 0.8% pour stimuler les transactions",
            proposer: "8JYVFy3pYsPSpPRsqimYxggTHcKFQxq3zLybz19R5YMF",
            createdAt: Date.now() - 86400000 * 2, // 2 jours avant
            endTime: Date.now() + 86400000, // 1 jour après
            status: "active",
            votesFor: 650000000,
            votesAgainst: 150000000,
            quorum: 100000000,
            type: "fees",
            changes: {
              baseFee: 0.8,
            },
          },
          {
            id: "prop2",
            title: "Déblocage d'une adresse blacklistée",
            description:
              "Proposition de déblocage de l'adresse 8JYVFy3pYsPSpPRsqimYxggTHcKFQxq3zLybz19R5YMF qui a été blacklistée par erreur",
            proposer: "DifferentAddress123456789012345678901234567890",
            createdAt: Date.now() - 86400000 * 5, // 5 jours avant
            endTime: Date.now() - 86400000 * 2, // 2 jours avant
            status: "passed",
            votesFor: 750000000,
            votesAgainst: 50000000,
            quorum: 100000000,
            type: "unblock",
            changes: {
              addressToUnblock: "8JYVFy3pYsPSpPRsqimYxggTHcKFQxq3zLybz19R5YMF",
            },
          },
          {
            id: "prop3",
            title: "Augmentation des limites de transaction",
            description:
              "Proposition d'augmentation des limites de transaction pour permettre des transactions plus importantes",
            proposer: "AnotherAddress123456789012345678901234567890",
            createdAt: Date.now() - 86400000 * 10, // 10 jours avant
            endTime: Date.now() - 86400000 * 7, // 7 jours avant
            status: "rejected",
            votesFor: 300000000,
            votesAgainst: 500000000,
            quorum: 100000000,
            type: "limits",
            changes: {
              maxTransactionSize: 20000000,
              maxWalletHolding: 100000000,
            },
          },
          {
            id: "prop4",
            title: "Allocation de fonds pour le marketing",
            description: "Proposition d'allocation de 10 millions de tokens pour les efforts de marketing",
            proposer: "YetAnotherAddress123456789012345678901234567890",
            createdAt: Date.now() - 86400000 * 15, // 15 jours avant
            endTime: Date.now() - 86400000 * 12, // 12 jours avant
            status: "executed",
            votesFor: 800000000,
            votesAgainst: 100000000,
            quorum: 100000000,
            type: "funds",
            changes: {
              amount: 10000000,
              recipient: "MarketingWallet123456789012345678901234567890",
            },
          },
        ]

        setProposals(mockProposals)
      } catch (err: any) {
        console.error("Erreur lors du chargement des propositions:", err)
        setError(err.message || "Une erreur s'est produite lors du chargement des propositions")
      } finally {
        setIsLoading(false)
      }
    }

    fetchProposals()
  }, [tokenAddress])

  // Voter pour une proposition
  const handleVote = async (proposalId: string, voteFor: boolean) => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet non connecté",
        description: "Veuillez connecter votre wallet pour voter",
        variant: "destructive",
      })
      return
    }

    try {
      // Dans une implémentation réelle, nous ferions un appel API
      // Simuler un délai de traitement
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Vote enregistré",
        description: `Vous avez voté ${voteFor ? "pour" : "contre"} la proposition`,
      })

      // Mettre à jour l'état local
      setProposals(
        proposals.map((p) => {
          if (p.id === proposalId) {
            return {
              ...p,
              votesFor: voteFor ? p.votesFor + 1000000 : p.votesFor,
              votesAgainst: !voteFor ? p.votesAgainst + 1000000 : p.votesAgainst,
            }
          }
          return p
        }),
      )
    } catch (err: any) {
      toast({
        title: "Erreur de vote",
        description: err.message || "Une erreur s'est produite lors du vote",
        variant: "destructive",
      })
    }
  }

  // Créer une proposition
  const handleCreateProposal = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet non connecté",
        description: "Veuillez connecter votre wallet pour créer une proposition",
        variant: "destructive",
      })
      return
    }

    if (!proposalTitle || !proposalDescription || !proposalType) {
      toast({
        title: "Données manquantes",
        description: "Veuillez remplir tous les champs",
        variant: "destructive",
      })
      return
    }

    try {
      setIsCreatingProposal(true)

      // Dans une implémentation réelle, nous ferions un appel API
      // Simuler un délai de traitement
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Créer une nouvelle proposition simulée
      const newProposal: Proposal = {
        id: `prop${Date.now()}`,
        title: proposalTitle,
        description: proposalDescription,
        proposer: publicKey.toString(),
        createdAt: Date.now(),
        endTime: Date.now() + 86400000 * 3, // 3 jours après
        status: "active",
        votesFor: 0,
        votesAgainst: 0,
        quorum: 100000000,
        type: proposalType as any,
        changes: {},
      }

      // Mettre à jour l'état local
      setProposals([newProposal, ...proposals])

      toast({
        title: "Proposition créée",
        description: "Votre proposition a été créée avec succès",
      })

      // Réinitialiser le formulaire
      setProposalTitle("")
      setProposalDescription("")
      setProposalType("fees")

      // Passer à l'onglet des propositions actives
      setActiveTab("active")
    } catch (err: any) {
      toast({
        title: "Erreur de création",
        description: err.message || "Une erreur s'est produite lors de la création de la proposition",
        variant: "destructive",
      })
    } finally {
      setIsCreatingProposal(false)
    }
  }

  // Filtrer les propositions par statut
  const filteredProposals = proposals.filter((p) => {
    if (activeTab === "active") return p.status === "active"
    if (activeTab === "passed") return p.status === "passed"
    if (activeTab === "rejected") return p.status === "rejected"
    if (activeTab === "executed") return p.status === "executed"
    return true
  })

  // Afficher les détails d'une proposition
  const handleViewProposal = (proposal: Proposal) => {
    setActiveProposal(proposal)
  }

  // Fermer les détails d'une proposition
  const handleCloseProposalDetails = () => {
    setActiveProposal(null)
  }

  // Obtenir l'icône pour le type de proposition
  const getProposalTypeIcon = (type: string) => {
    switch (type) {
      case "fees":
        return <Percent className="h-5 w-5" />
      case "limits":
        return <Shield className="h-5 w-5" />
      case "unblock":
        return <Ban className="h-5 w-5" />
      case "priceImpact":
        return <BarChart3 className="h-5 w-5" />
      case "funds":
        return <Wallet className="h-5 w-5" />
      default:
        return <FileText className="h-5 w-5" />
    }
  }

  // Obtenir le libellé pour le type de proposition
  const getProposalTypeLabel = (type: string) => {
    switch (type) {
      case "fees":
        return "Modification des frais"
      case "limits":
        return "Modification des limites"
      case "unblock":
        return "Déblocage d'adresse"
      case "priceImpact":
        return "Impact sur les prix"
      case "funds":
        return "Allocation de fonds"
      default:
        return "Autre"
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-12 w-3/4" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Erreur</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  // Afficher les détails d'une proposition
  if (activeProposal) {
    const totalVotes = activeProposal.votesFor + activeProposal.votesAgainst
    const forPercentage = totalVotes > 0 ? (activeProposal.votesFor / totalVotes) * 100 : 0
    const againstPercentage = totalVotes > 0 ? (activeProposal.votesAgainst / totalVotes) * 100 : 0
    const quorumPercentage = totalVotes > 0 ? (totalVotes / activeProposal.quorum) * 100 : 0

    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center gap-2">
                {getProposalTypeIcon(activeProposal.type)} {activeProposal.title}
              </CardTitle>
              <CardDescription>
                {getProposalTypeLabel(activeProposal.type)} • Créée{" "}
                {formatDistanceToNow(new Date(activeProposal.createdAt), { addSuffix: true, locale: fr })}
              </CardDescription>
            </div>
            <Badge
              variant={
                activeProposal.status === "active"
                  ? "default"
                  : activeProposal.status === "passed"
                    ? "success"
                    : activeProposal.status === "rejected"
                      ? "destructive"
                      : "outline"
              }
            >
              {activeProposal.status === "active"
                ? "En cours"
                : activeProposal.status === "passed"
                  ? "Adoptée"
                  : activeProposal.status === "rejected"
                    ? "Rejetée"
                    : "Exécutée"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="font-medium mb-2">Description</h3>
            <p className="text-gray-700">{activeProposal.description}</p>
          </div>

          <div>
            <h3 className="font-medium mb-2">Détails du vote</h3>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Pour: {forPercentage.toFixed(1)}%</span>
                  <span>{(activeProposal.votesFor / 1000000).toFixed(1)}M votes</span>
                </div>
                <Progress value={forPercentage} className="h-2 bg-gray-100" />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Contre: {againstPercentage.toFixed(1)}%</span>
                  <span>{(activeProposal.votesAgainst / 1000000).toFixed(1)}M votes</span>
                </div>
                <Progress value={againstPercentage} className="h-2 bg-gray-100" />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Quorum: {quorumPercentage.toFixed(1)}%</span>
                  <span>
                    {(totalVotes / 1000000).toFixed(1)}M / {(activeProposal.quorum / 1000000).toFixed(1)}M
                  </span>
                </div>
                <Progress value={Math.min(quorumPercentage, 100)} className="h-2 bg-gray-100" />
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Calendrier</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Début du vote</p>
                <p>{new Date(activeProposal.createdAt).toLocaleDateString()}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Fin du vote</p>
                <p>{new Date(activeProposal.endTime).toLocaleDateString()}</p>
              </div>
            </div>
          </div>

          {activeProposal.status === "active" && (
            <div>
              <h3 className="font-medium mb-2">Voter</h3>
              <div className="flex gap-4">
                <Button
                  variant="default"
                  className="flex-1"
                  onClick={() => handleVote(activeProposal.id, true)}
                  disabled={!connected}
                >
                  Voter pour
                </Button>

                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => handleVote(activeProposal.id, false)}
                  disabled={!connected}
                >
                  Voter contre
                </Button>
              </div>

              {!connected && <p className="text-sm text-red-500 mt-2">Veuillez connecter votre wallet pour voter</p>}
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button variant="ghost" onClick={handleCloseProposalDetails}>
            Retour à la liste
          </Button>
        </CardFooter>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" /> Gouvernance DAO
          </h1>
          <p className="text-gray-500">Participez aux décisions importantes concernant le token</p>
        </div>
      </div>

      <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 md:grid-cols-5 mb-4">
          <TabsTrigger value="active">Actives</TabsTrigger>
          <TabsTrigger value="passed">Adoptées</TabsTrigger>
          <TabsTrigger value="rejected">Rejetées</TabsTrigger>
          <TabsTrigger value="executed">Exécutées</TabsTrigger>
          <TabsTrigger value="create">Créer</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          {filteredProposals.length > 0 ? (
            filteredProposals.map((proposal) => (
              <Card
                key={proposal.id}
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleViewProposal(proposal)}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg flex items-center gap-2">
                      {getProposalTypeIcon(proposal.type)} {proposal.title}
                    </CardTitle>
                    <Badge>En cours</Badge>
                  </div>
                  <CardDescription>
                    {getProposalTypeLabel(proposal.type)} • Créée{" "}
                    {formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true, locale: fr })}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 line-clamp-2">{proposal.description}</p>

                  <div className="mt-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>
                        Votes pour:{" "}
                        {((proposal.votesFor / (proposal.votesFor + proposal.votesAgainst)) * 100).toFixed(1)}%
                      </span>
                      <span>
                        Quorum: {(((proposal.votesFor + proposal.votesAgainst) / proposal.quorum) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress
                      value={(proposal.votesFor / (proposal.votesFor + proposal.votesAgainst)) * 100}
                      className="h-2"
                    />
                  </div>

                  <div className="mt-4 flex items-center gap-2 text-sm text-gray-500">
                    <Clock className="h-4 w-4" />
                    <span>Fin {formatDistanceToNow(new Date(proposal.endTime), { addSuffix: true, locale: fr })}</span>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">Aucune proposition active</h3>
              <p className="text-gray-500 mb-6">Il n'y a actuellement aucune proposition active pour ce token.</p>
              <Button variant="outline" onClick={() => setActiveTab("create")}>
                Créer une proposition
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="passed" className="space-y-4">
          {filteredProposals.length > 0 ? (
            filteredProposals.map((proposal) => (
              <Card
                key={proposal.id}
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleViewProposal(proposal)}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg flex items-center gap-2">
                      {getProposalTypeIcon(proposal.type)} {proposal.title}
                    </CardTitle>
                    <Badge variant="success">Adoptée</Badge>
                  </div>
                  <CardDescription>
                    {getProposalTypeLabel(proposal.type)} • Créée{" "}
                    {formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true, locale: fr })}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 line-clamp-2">{proposal.description}</p>

                  <div className="mt-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>
                        Votes pour:{" "}
                        {((proposal.votesFor / (proposal.votesFor + proposal.votesAgainst)) * 100).toFixed(1)}%
                      </span>
                      <span>
                        Quorum: {(((proposal.votesFor + proposal.votesAgainst) / proposal.quorum) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress
                      value={(proposal.votesFor / (proposal.votesFor + proposal.votesAgainst)) * 100}
                      className="h-2"
                    />
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">Aucune proposition adoptée</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="rejected" className="space-y-4">
          {filteredProposals.length > 0 ? (
            filteredProposals.map((proposal) => (
              <Card
                key={proposal.id}
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleViewProposal(proposal)}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg flex items-center gap-2">
                      {getProposalTypeIcon(proposal.type)} {proposal.title}
                    </CardTitle>
                    <Badge variant="destructive">Rejetée</Badge>
                  </div>
                  <CardDescription>
                    {getProposalTypeLabel(proposal.type)} • Créée{" "}
                    {formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true, locale: fr })}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 line-clamp-2">{proposal.description}</p>

                  <div className="mt-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>
                        Votes pour:{" "}
                        {((proposal.votesFor / (proposal.votesFor + proposal.votesAgainst)) * 100).toFixed(1)}%
                      </span>
                      <span>
                        Quorum: {(((proposal.votesFor + proposal.votesAgainst) / proposal.quorum) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress
                      value={(proposal.votesFor / (proposal.votesFor + proposal.votesAgainst)) * 100}
                      className="h-2"
                    />
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">Aucune proposition rejetée</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="executed" className="space-y-4">
          {filteredProposals.length > 0 ? (
            filteredProposals.map((proposal) => (
              <Card
                key={proposal.id}
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleViewProposal(proposal)}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg flex items-center gap-2">
                      {getProposalTypeIcon(proposal.type)} {proposal.title}
                    </CardTitle>
                    <Badge variant="outline">Exécutée</Badge>
                  </div>
                  <CardDescription>
                    {getProposalTypeLabel(proposal.type)} • Créée{" "}
                    {formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true, locale: fr })}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 line-clamp-2">{proposal.description}</p>

                  <div className="mt-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>
                        Votes pour:{" "}
                        {((proposal.votesFor / (proposal.votesFor + proposal.votesAgainst)) * 100).toFixed(1)}%
                      </span>
                      <span>
                        Quorum: {(((proposal.votesFor + proposal.votesAgainst) / proposal.quorum) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress
                      value={(proposal.votesFor / (proposal.votesFor + proposal.votesAgainst)) * 100}
                      className="h-2"
                    />
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">Aucune proposition exécutée</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="create" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Créer une nouvelle proposition</CardTitle>
              <CardDescription>Proposez des modifications aux paramètres du token ou d'autres actions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="proposalTitle">Titre de la proposition</Label>
                <Input
                  id="proposalTitle"
                  placeholder="Ex: Réduction des frais de base"
                  value={proposalTitle}
                  onChange={(e) => setProposalTitle(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="proposalType">Type de proposition</Label>
                <select
                  id="proposalType"
                  className="w-full p-2 border rounded-md"
                  value={proposalType}
                  onChange={(e) => setProposalType(e.target.value)}
                >
                  <option value="fees">Modification des frais</option>
                  <option value="limits">Modification des limites</option>
                  <option value="unblock">Déblocage d'adresse</option>
                  <option value="priceImpact">Impact sur les prix</option>
                  <option value="funds">Allocation de fonds</option>
                  <option value="other">Autre</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="proposalDescription">Description détaillée</Label>
                <Textarea
                  id="proposalDescription"
                  placeholder="Décrivez votre proposition en détail..."
                  rows={5}
                  value={proposalDescription}
                  onChange={(e) => setProposalDescription(e.target.value)}
                />
              </div>

              {!connected && (
                <Alert variant="destructive">
                  <AlertTitle>Wallet non connecté</AlertTitle>
                  <AlertDescription>Veuillez connecter votre wallet pour créer une proposition</AlertDescription>
                </Alert>
              )}
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                onClick={handleCreateProposal}
                disabled={isCreatingProposal || !connected || !proposalTitle || !proposalDescription}
              >
                {isCreatingProposal ? "Création en cours..." : "Créer la proposition"}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Informations sur la gouvernance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium mb-2">Quorum</h3>
                  <p className="text-2xl font-bold">10%</p>
                  <p className="text-sm text-gray-500">
                    Pourcentage de l'offre totale qui doit voter pour qu'une proposition soit valide
                  </p>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium mb-2">Seuil d'adoption</h3>
                  <p className="text-2xl font-bold">50%</p>
                  <p className="text-sm text-gray-500">
                    Pourcentage de votes "pour" nécessaire pour qu'une proposition soit adoptée
                  </p>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium mb-2">Durée des votes</h3>
                  <p className="text-2xl font-bold">3 jours</p>
                  <p className="text-sm text-gray-500">Durée pendant laquelle une proposition peut être votée</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

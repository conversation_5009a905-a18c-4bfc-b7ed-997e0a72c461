"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Stepper, Step } from "@/components/ui/stepper"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { useNetwork } from "@/contexts/network-context"
import TokenImageSelector from "./token-image-selector"
import TokenNamePreview from "./token-name-preview"
import { isValidBase58, sanitizeToBase58, findInvalidBase58Char } from "@/lib/input-sanitizer"
import { realTokenService } from "@/lib/real-token-service"
import { AlertCircle, CheckCircle2, Loader2, Shield, Rocket, Coins, Lock, Info } from "lucide-react"

interface RealTokenFormProps {
  adminKey: string
}

export default function RealTokenForm({ adminKey }: RealTokenFormProps) {
  const { publicKey, connected } = useWallet()
  const { toast } = useToast()
  const router = useRouter()
  const { activeNetwork } = useNetwork()

  // États du formulaire
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState("9")
  const [tokenSupply, setTokenSupply] = useState("1000000000")
  const [maxSupply, setMaxSupply] = useState("10000000000")
  const [tokenDescription, setTokenDescription] = useState("")
  const [logoUrl, setLogoUrl] = useState<string | null>(null)

  // États des fonctionnalités
  const [isMintable, setIsMintable] = useState(true)
  const [isBurnable, setIsBurnable] = useState(true)
  const [isFreezable, setIsFreezable] = useState(false)
  const [isPausable, setIsPausable] = useState(false)
  const [isTransferTaxable, setIsTransferTaxable] = useState(false)
  const [transferTaxRate, setTransferTaxRate] = useState(0)
  const [maxTxAmount, setMaxTxAmount] = useState(0)
  const [maxWalletAmount, setMaxWalletAmount] = useState(0)
  const [antiBot, setAntiBot] = useState(false)
  const [antiDump, setAntiDump] = useState(false)

  // États de l'interface
  const [activeStep, setActiveStep] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<boolean>(false)
  const [creationProgress, setCreationProgress] = useState(0)
  const [creationStep, setCreationStep] = useState("")
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({})
  const [estimatedFees, setEstimatedFees] = useState<{ fees: { [key: string]: number }; total: number } | null>(null)
  const [mintAddress, setMintAddress] = useState<string | null>(null)
  const [tokenAccount, setTokenAccount] = useState<string | null>(null)
  const [signature, setSignature] = useState<string | null>(null)

  // Étapes du formulaire
  const steps = [
    { title: "Informations de base", description: "Nom, symbole et offre", icon: <Coins className="h-5 w-5" /> },
    { title: "Fonctionnalités", description: "Mintable, Freezable, etc.", icon: <Shield className="h-5 w-5" /> },
    { title: "Sécurité", description: "Limites et protections", icon: <Lock className="h-5 w-5" /> },
    { title: "Confirmation", description: "Vérifier et créer", icon: <Rocket className="h-5 w-5" /> },
  ]

  // Estimer les frais de création
  useEffect(() => {
    const estimateFees = async () => {
      try {
        const fees = await realTokenService.estimateTokenCreationFees(activeNetwork?.id)
        setEstimatedFees(fees)
      } catch (error) {
        console.error("Erreur lors de l'estimation des frais:", error)
      }
    }

    estimateFees()
  }, [activeNetwork])

  // Validation du formulaire
  const validateForm = () => {
    const errors: { [key: string]: string } = {}

    // Validation du nom
    if (!tokenName.trim()) {
      errors.name = "Le nom du token est requis"
    } else if (tokenName.length > 50) {
      errors.name = "Le nom du token ne doit pas dépasser 50 caractères"
    }

    // Validation du symbole
    if (!tokenSymbol.trim()) {
      errors.symbol = "Le symbole du token est requis"
    } else if (tokenSymbol.length > 10) {
      errors.symbol = "Le symbole du token ne doit pas dépasser 10 caractères"
    } else if (!isValidBase58(tokenSymbol)) {
      const invalidChar = findInvalidBase58Char(tokenSymbol)
      errors.symbol = invalidChar
        ? `Caractère non valide '${invalidChar.char}' à la position ${invalidChar.position + 1}`
        : "Le symbole contient des caractères non valides"
    }

    // Validation des décimales
    const decimals = Number.parseInt(tokenDecimals)
    if (isNaN(decimals) || decimals < 0 || decimals > 9) {
      errors.decimals = "Les décimales doivent être un nombre entre 0 et 9"
    }

    // Validation de l'offre
    const supply = Number.parseFloat(tokenSupply)
    if (isNaN(supply) || supply <= 0) {
      errors.supply = "L'offre initiale doit être un nombre positif"
    }

    // Validation de l'offre maximale
    const max = Number.parseFloat(maxSupply)
    if (isNaN(max) || max < supply) {
      errors.maxSupply = "L'offre maximale doit être supérieure ou égale à l'offre initiale"
    }

    // Validation du taux de taxe
    if (isTransferTaxable && (transferTaxRate < 0 || transferTaxRate > 10)) {
      errors.transferTaxRate = "Le taux de taxe doit être entre 0 et 10%"
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Gestionnaire de soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      toast({
        title: "Erreur de validation",
        description: "Veuillez corriger les erreurs dans le formulaire avant de continuer.",
        variant: "destructive",
      })
      return
    }

    if (!publicKey) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour créer un token.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)
    setCreationProgress(10)
    setCreationStep("Préparation de la création du token...")

    try {
      // Sanitiser le symbole
      const sanitizedSymbol = sanitizeToBase58(tokenSymbol)
      if (sanitizedSymbol !== tokenSymbol) {
        setTokenSymbol(sanitizedSymbol)
        toast({
          title: "Symbole corrigé",
          description: `Le symbole a été corrigé pour être compatible avec base58: ${sanitizedSymbol}`,
          variant: "warning",
        })
      }

      setCreationProgress(20)
      setCreationStep("Envoi de la requête de création...")

      // Préparer les données pour l'API
      const tokenData = {
        name: tokenName,
        symbol: sanitizedSymbol,
        decimals: Number.parseInt(tokenDecimals),
        initialSupply: Number.parseFloat(tokenSupply),
        maxSupply: Number.parseFloat(maxSupply),
        ownerAddress: publicKey.toString(),
        adminKey,
        metadata: {
          name: tokenName,
          symbol: sanitizedSymbol,
          description: tokenDescription,
          image: logoUrl || "",
        },
        isMintable,
        isBurnable,
        isFreezable,
        isPausable,
        isTransferTaxable,
        transferTaxRate: isTransferTaxable ? transferTaxRate : 0,
        maxTxAmount: maxTxAmount > 0 ? maxTxAmount : undefined,
        maxWalletAmount: maxWalletAmount > 0 ? maxWalletAmount : undefined,
        antiBot,
        antiDump,
        networkId: activeNetwork?.id || "solana-devnet",
      }

      setCreationProgress(40)
      setCreationStep("Création du token sur la blockchain...")

      // Appeler l'API pour créer le token
      const response = await fetch("/api/token/create-real", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(tokenData),
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || "Échec de la création du token")
      }

      setCreationProgress(100)
      setCreationStep("Token créé avec succès!")

      // Stocker les informations du token créé
      setMintAddress(result.mintAddress)
      setTokenAccount(result.tokenAccount)
      setSignature(result.signature)
      setSuccess(true)

      toast({
        title: "Token créé avec succès",
        description: `Votre token ${tokenName} (${sanitizedSymbol}) a été créé avec succès!`,
      })

      // Rediriger vers la page de succès après un court délai
      setTimeout(() => {
        router.push(
          `/token-factory/success?address=${result.mintAddress}&name=${encodeURIComponent(tokenName)}&symbol=${encodeURIComponent(sanitizedSymbol)}&decimals=${tokenDecimals}&supply=${tokenSupply}&network=${activeNetwork?.id === "solana-mainnet" ? "mainnet" : "devnet"}`,
        )
      }, 2000)
    } catch (err: any) {
      console.error("Erreur lors de la création du token:", err)
      setError(err.message || "Une erreur s'est produite lors de la création du token")
      setCreationProgress(0)

      toast({
        title: "Échec de la création du token",
        description: err.message || "Une erreur s'est produite lors de la création du token",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Navigation entre les étapes
  const nextStep = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1)
    }
  }

  const prevStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1)
    }
  }

  // Si le token est en cours de création, afficher la progression
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Création du token en cours</CardTitle>
          <CardDescription>Veuillez patienter pendant que nous créons votre token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium">{creationStep}</h3>
          </div>
          <Progress value={creationProgress} className="h-2" />

          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <h4 className="font-medium">Détails du token</h4>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Nom:</span> {tokenName}
              </div>
              <div>
                <span className="text-muted-foreground">Symbole:</span> {tokenSymbol}
              </div>
              <div>
                <span className="text-muted-foreground">Décimales:</span> {tokenDecimals}
              </div>
              <div>
                <span className="text-muted-foreground">Offre initiale:</span> {Number(tokenSupply).toLocaleString()}
              </div>
              <div>
                <span className="text-muted-foreground">Mintable:</span> {isMintable ? "Oui" : "Non"}
              </div>
              <div>
                <span className="text-muted-foreground">Freezable:</span> {isFreezable ? "Oui" : "Non"}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Si le token a été créé avec succès, afficher les détails
  if (success) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-6 w-6 text-green-500" />
            <CardTitle>Token créé avec succès</CardTitle>
          </div>
          <CardDescription>Votre token a été créé sur la blockchain {activeNetwork?.name || "Solana"}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Félicitations!</AlertTitle>
            <AlertDescription>
              Votre token {tokenName} ({tokenSymbol}) a été créé avec succès. Vous allez être redirigé vers la page de
              détails.
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <Label className="text-muted-foreground">Adresse du token</Label>
            <div className="flex items-center justify-between bg-muted p-2 rounded-md">
              <code className="text-xs break-all">{mintAddress}</code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(mintAddress || "")
                  toast({
                    title: "Adresse copiée",
                    description: "L'adresse du token a été copiée dans le presse-papier.",
                  })
                }}
              >
                Copier
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-muted-foreground">Signature de transaction</Label>
            <div className="flex items-center justify-between bg-muted p-2 rounded-md">
              <code className="text-xs break-all">{signature}</code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(signature || "")
                  toast({
                    title: "Signature copiée",
                    description: "La signature de transaction a été copiée dans le presse-papier.",
                  })
                }}
              >
                Copier
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="default"
              onClick={() => {
                const explorerUrl = activeNetwork?.id?.includes("mainnet")
                  ? `https://explorer.solana.com/address/${mintAddress}`
                  : `https://explorer.solana.com/address/${mintAddress}?cluster=devnet`
                window.open(explorerUrl, "_blank")
              }}
            >
              Voir sur l'explorateur
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                router.push("/token-factory/tokens")
              }}
            >
              Voir tous mes tokens
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Afficher le formulaire de création de token
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Créer un token réel</CardTitle>
        <CardDescription>
          Créez un token SPL sur la blockchain {activeNetwork?.name || "Solana"} avec des transactions réelles
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <Stepper activeStep={activeStep} orientation="horizontal">
            {steps.map((step, index) => (
              <Step key={index} onClick={() => setActiveStep(index)}>
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">{step.icon}</div>
                <div className="ml-3">
                  <p className="font-medium">{step.title}</p>
                  <p className="text-sm text-muted-foreground">{step.description}</p>
                </div>
              </Step>
            ))}
          </Stepper>

          <div className="mt-8">
            {activeStep === 0 && (
              <div className="space-y-6">
                <div className="mb-8">
                  <h3 className="text-lg font-medium mb-3">Aperçu du token</h3>
                  <TokenNamePreview name={tokenName} symbol={tokenSymbol} logoUrl={logoUrl} />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenName">Nom du token</Label>
                  <Input
                    id="tokenName"
                    placeholder="ex: Mon Token"
                    value={tokenName}
                    onChange={(e) => setTokenName(e.target.value)}
                    required
                    className={validationErrors.name ? "border-red-500" : ""}
                  />
                  {validationErrors.name && <p className="text-xs text-red-500 mt-1">{validationErrors.name}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenSymbol">Symbole du token</Label>
                  <Input
                    id="tokenSymbol"
                    placeholder="ex: MTK"
                    value={tokenSymbol}
                    onChange={(e) => setTokenSymbol(e.target.value)}
                    required
                    maxLength={10}
                    className={validationErrors.symbol ? "border-red-500" : ""}
                  />
                  {validationErrors.symbol && <p className="text-xs text-red-500 mt-1">{validationErrors.symbol}</p>}
                  <p className="text-xs text-muted-foreground">
                    Utilisez uniquement des caractères alphanumériques (pas de O, I, l, 0)
                  </p>
                </div>

                <TokenImageSelector logoUrl={logoUrl} onImageChange={setLogoUrl} />

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="tokenDecimals">Décimales</Label>
                    <Input
                      id="tokenDecimals"
                      type="number"
                      min="0"
                      max="9"
                      value={tokenDecimals}
                      onChange={(e) => setTokenDecimals(e.target.value)}
                      required
                      className={validationErrors.decimals ? "border-red-500" : ""}
                    />
                    {validationErrors.decimals && (
                      <p className="text-xs text-red-500 mt-1">{validationErrors.decimals}</p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Les décimales déterminent la divisibilité de votre token. La valeur recommandée est 9.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tokenSupply">Offre initiale</Label>
                    <Input
                      id="tokenSupply"
                      type="number"
                      min="1"
                      value={tokenSupply}
                      onChange={(e) => setTokenSupply(e.target.value)}
                      required
                      className={validationErrors.supply ? "border-red-500" : ""}
                    />
                    {validationErrors.supply && <p className="text-xs text-red-500 mt-1">{validationErrors.supply}</p>}
                    <p className="text-xs text-muted-foreground">
                      L'offre initiale est le nombre total de tokens qui seront créés.
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxSupply">Offre maximale</Label>
                  <Input
                    id="maxSupply"
                    type="number"
                    min={tokenSupply}
                    value={maxSupply}
                    onChange={(e) => setMaxSupply(e.target.value)}
                    required
                    className={validationErrors.maxSupply ? "border-red-500" : ""}
                  />
                  {validationErrors.maxSupply && (
                    <p className="text-xs text-red-500 mt-1">{validationErrors.maxSupply}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    L'offre maximale est le nombre total de tokens qui pourront être créés à terme.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenDescription">Description (Optionnel)</Label>
                  <Textarea
                    id="tokenDescription"
                    placeholder="Décrivez votre token..."
                    value={tokenDescription}
                    onChange={(e) => setTokenDescription(e.target.value)}
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground">
                    Une description claire aide les utilisateurs à comprendre l'utilité de votre token.
                  </p>
                </div>
              </div>
            )}

            {activeStep === 1 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Fonctionnalités du token</h3>
                <p className="text-sm text-muted-foreground">
                  Configurez les fonctionnalités de base de votre token. Ces options déterminent comment votre token
                  peut être utilisé et géré.
                </p>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isMintable">Mintable</Label>
                      <p className="text-sm text-muted-foreground">
                        Permet de créer de nouveaux tokens après le lancement
                      </p>
                    </div>
                    <Switch id="isMintable" checked={isMintable} onCheckedChange={setIsMintable} />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isBurnable">Burnable</Label>
                      <p className="text-sm text-muted-foreground">
                        Permet de détruire des tokens pour réduire l'offre
                      </p>
                    </div>
                    <Switch id="isBurnable" checked={isBurnable} onCheckedChange={setIsBurnable} />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isFreezable">Freezable</Label>
                      <p className="text-sm text-muted-foreground">
                        Permet de geler les comptes de token des utilisateurs
                      </p>
                    </div>
                    <Switch id="isFreezable" checked={isFreezable} onCheckedChange={setIsFreezable} />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isPausable">Pausable</Label>
                      <p className="text-sm text-muted-foreground">
                        Permet de suspendre temporairement toutes les transactions
                      </p>
                    </div>
                    <Switch id="isPausable" checked={isPausable} onCheckedChange={setIsPausable} />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isTransferTaxable">Taxe de transfert</Label>
                      <p className="text-sm text-muted-foreground">Applique une taxe sur chaque transaction</p>
                    </div>
                    <Switch id="isTransferTaxable" checked={isTransferTaxable} onCheckedChange={setIsTransferTaxable} />
                  </div>

                  {isTransferTaxable && (
                    <div className="space-y-2 pl-4 border-l-2 border-muted">
                      <div className="flex justify-between">
                        <Label htmlFor="transferTaxRate">Taux de taxe: {transferTaxRate}%</Label>
                      </div>
                      <Slider
                        id="transferTaxRate"
                        min={0}
                        max={10}
                        step={0.5}
                        value={[transferTaxRate]}
                        onValueChange={(value) => setTransferTaxRate(value[0])}
                      />
                      {validationErrors.transferTaxRate && (
                        <p className="text-xs text-red-500 mt-1">{validationErrors.transferTaxRate}</p>
                      )}
                    </div>
                  )}
                </div>

                <Alert className="bg-amber-50 border-amber-200">
                  <Info className="h-4 w-4 text-amber-600" />
                  <AlertTitle className="text-amber-800">Important</AlertTitle>
                  <AlertDescription className="text-amber-700">
                    Les fonctionnalités choisies ici sont permanentes et ne pourront pas être modifiées après la
                    création du token.
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {activeStep === 2 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Sécurité et limites</h3>
                <p className="text-sm text-muted-foreground">
                  Configurez les mesures de sécurité et les limites de votre token pour protéger les investisseurs et
                  prévenir les abus.
                </p>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="antiBot">Protection anti-bot</Label>
                      <p className="text-sm text-muted-foreground">
                        Empêche les bots de manipuler le marché au lancement
                      </p>
                    </div>
                    <Switch id="antiBot" checked={antiBot} onCheckedChange={setAntiBot} />
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="antiDump">Protection anti-dump</Label>
                      <p className="text-sm text-muted-foreground">
                        Limite les ventes massives pour stabiliser le prix
                      </p>
                    </div>
                    <Switch id="antiDump" checked={antiDump} onCheckedChange={setAntiDump} />
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label htmlFor="maxTxAmount">Montant maximum par transaction (0 = illimité)</Label>
                    <Input
                      id="maxTxAmount"
                      type="number"
                      min="0"
                      value={maxTxAmount}
                      onChange={(e) => setMaxTxAmount(Number(e.target.value))}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Limite le nombre de tokens pouvant être transférés en une seule transaction.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxWalletAmount">Montant maximum par portefeuille (0 = illimité)</Label>
                    <Input
                      id="maxWalletAmount"
                      type="number"
                      min="0"
                      value={maxWalletAmount}
                      onChange={(e) => setMaxWalletAmount(Number(e.target.value))}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Limite le nombre de tokens qu'un portefeuille peut détenir.
                    </p>
                  </div>
                </div>

                <Alert className="bg-blue-50 border-blue-200">
                  <Info className="h-4 w-4 text-blue-600" />
                  <AlertTitle className="text-blue-800">Conseil de sécurité</AlertTitle>
                  <AlertDescription className="text-blue-700">
                    L'activation des protections anti-bot et anti-dump est recommandée pour les nouveaux tokens afin de
                    prévenir les manipulations de marché.
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {activeStep === 3 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Confirmation et création</h3>
                <p className="text-sm text-muted-foreground">
                  Vérifiez les détails de votre token avant de le créer sur la blockchain.
                </p>

                <div className="bg-muted p-4 rounded-lg space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-xl font-bold">
                      {tokenSymbol.substring(0, 2)}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">{tokenName}</h3>
                      <p className="text-sm text-muted-foreground">{tokenSymbol}</p>
                      <div className="flex flex-wrap gap-2 mt-2">
                        <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                          {tokenDecimals} décimales
                        </span>
                        <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                          {Number(tokenSupply).toLocaleString()} tokens
                        </span>
                        {isMintable && (
                          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Mintable</span>
                        )}
                        {isFreezable && (
                          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Freezable</span>
                        )}
                        {isPausable && (
                          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Pausable</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Fonctionnalités</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">Mintable:</span> {isMintable ? "Oui" : "Non"}
                      </div>
                      <div>
                        <span className="text-muted-foreground">Burnable:</span> {isBurnable ? "Oui" : "Non"}
                      </div>
                      <div>
                        <span className="text-muted-foreground">Freezable:</span> {isFreezable ? "Oui" : "Non"}
                      </div>
                      <div>
                        <span className="text-muted-foreground">Pausable:</span> {isPausable ? "Oui" : "Non"}
                      </div>
                      <div>
                        <span className="text-muted-foreground">Taxe de transfert:</span>{" "}
                        {isTransferTaxable ? `${transferTaxRate}%` : "Non"}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Sécurité</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">Anti-bot:</span> {antiBot ? "Activé" : "Désactivé"}
                      </div>
                      <div>
                        <span className="text-muted-foreground">Anti-dump:</span> {antiDump ? "Activé" : "Désactivé"}
                      </div>
                      <div>
                        <span className="text-muted-foreground">Max par transaction:</span>{" "}
                        {maxTxAmount > 0 ? maxTxAmount.toLocaleString() : "Illimité"}
                      </div>
                      <div>
                        <span className="text-muted-foreground">Max par portefeuille:</span>{" "}
                        {maxWalletAmount > 0 ? maxWalletAmount.toLocaleString() : "Illimité"}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Frais estimés</h4>
                    {estimatedFees ? (
                      <div className="space-y-1 text-sm">
                        {Object.entries(estimatedFees.fees).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-muted-foreground">{key}:</span>
                            <span>{value.toFixed(6)} SOL</span>
                          </div>
                        ))}
                        <Separator />
                        <div className="flex justify-between font-medium">
                          <span>Total:</span>
                          <span>{estimatedFees.total.toFixed(6)} SOL</span>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Chargement des frais estimés...</p>
                    )}
                  </div>
                </div>

                <Alert className="bg-amber-50 border-amber-200">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <AlertTitle className="text-amber-800">Important</AlertTitle>
                  <AlertDescription className="text-amber-700">
                    La création d'un token est une opération irréversible. Assurez-vous que tous les détails sont
                    corrects avant de continuer.
                  </AlertDescription>
                </Alert>

                {!connected && (
                  <div className="flex flex-col items-center gap-4 p-4 bg-muted rounded-lg">
                    <p className="text-center">Veuillez connecter votre portefeuille pour créer un token</p>
                    <WalletMultiButton />
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="flex justify-between pt-4">
            <Button type="button" variant="outline" onClick={prevStep} disabled={activeStep === 0 || isLoading}>
              Précédent
            </Button>
            {activeStep < steps.length - 1 ? (
              <Button type="button" onClick={nextStep}>
                Suivant
              </Button>
            ) : (
              <Button type="submit" disabled={isLoading || !connected}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Création en cours...
                  </>
                ) : (
                  "Créer le token"
                )}
              </Button>
            )}
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <div className="text-sm text-muted-foreground">
          {connected ? (
            <>
              Connecté: {publicKey?.toString().slice(0, 6)}...{publicKey?.toString().slice(-4)}
            </>
          ) : (
            "Veuillez connecter votre portefeuille pour créer un token"
          )}
        </div>
        <div className="text-sm font-medium">Réseau: {activeNetwork?.name || "Solana Devnet"}</div>
      </CardFooter>
    </Card>
  )
}

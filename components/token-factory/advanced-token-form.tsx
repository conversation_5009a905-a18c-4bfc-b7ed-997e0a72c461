"use client"

import { useState } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { useNetwork } from "@/contexts/network-context"
import { isValidBase58, findInvalidBase58Char } from "@/lib/input-sanitizer"
import { Shield, Rocket, Coins, Percent, BarChart3, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface AdvancedTokenFormProps {
  adminKey: string
}

export default function AdvancedTokenForm({ adminKey }: AdvancedTokenFormProps) {
  const { publicKey, connected, signMessage } = useWallet()
  const { toast } = useToast()
  const router = useRouter()
  const { activeNetwork } = useNetwork()

  // États du formulaire
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState("9")
  const [tokenSupply, setTokenSupply] = useState("1000000000")
  const [tokenDescription, setTokenDescription] = useState("")
  const [logoUrl, setLogoUrl] = useState<string | null>(null)

  // États des frais
  const [baseFee, setBaseFee] = useState(1) // 1%
  const [platformFeeShare, setPlatformFeeShare] = useState(0.25) // 0.25% du 1%
  const [burnFeeShare, setBurnFeeShare] = useState(0.25) // 0.25% du 1%
  const [priceImpactFeeShare, setPriceImpactFeeShare] = useState(0.5) // 0.5% du 1%

  const [additionalFee, setAdditionalFee] = useState(10) // 10%
  const [additionalPlatformShare, setAdditionalPlatformShare] = useState(1) // 1% du 10%
  const [additionalBurnShare, setAdditionalBurnShare] = useState(2.5) // 2.5% du 10%
  const [additionalPriceImpactShare, setAdditionalPriceImpactShare] = useState(4) // 4% du 10%
  const [additionalCreatorShare, setAdditionalCreatorShare] = useState(2.5) // 2.5% du 10%

  const [antiGainThreshold, setAntiGainThreshold] = useState(150) // 150%
  const [antiGainTax, setAntiGainTax] = useState(10) // 10%

  // États des limites de transaction
  const [maxTransactionSize, setMaxTransactionSize] = useState(10000000) // 10M tokens
  const [maxWalletHolding, setMaxWalletHolding] = useState(50000000) // 50M tokens
  const [maxPurchasesPerBlock, setMaxPurchasesPerBlock] = useState(3)
  const [maxSalesPerBlock, setMaxSalesPerBlock] = useState(2)
  const [cooldownPeriod, setCooldownPeriod] = useState(60) // 60 secondes

  // États du mécanisme d'impact sur les prix
  const [reservePercentage, setReservePercentage] = useState(5) // 5% de l'offre totale
  const [purchaseTrigger, setPurchaseTrigger] = useState(10) // 10% de baisse
  const [sellTrigger1, setSellTrigger1] = useState(60) // 60% de hausse
  const [sellTrigger2, setSellTrigger2] = useState(100) // 100% de hausse
  const [sellAmount1, setSellAmount1] = useState(5) // 5% de la réserve
  const [sellAmount2, setSellAmount2] = useState(10) // 10% de la réserve
  const [minimumInterval, setMinimumInterval] = useState(3600) // 1 heure

  // États de l'interface
  const [activeStep, setActiveStep] = useState(0)
  const [activeTab, setActiveTab] = useState("basic")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<boolean>(false)
  const [creationProgress, setCreationProgress] = useState(0)
  const [creationStep, setCreationStep] = useState("")
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({})
  const [mintAddress, setMintAddress] = useState<string | null>(null)
  const [ownerTokenAccount, setOwnerTokenAccount] = useState<string | null>(null)
  const [burnWallet, setBurnWallet] = useState<string | null>(null)
  const [priceImpactWallet, setPriceImpactWallet] = useState<string | null>(null)
  const [signature, setSignature] = useState<string | null>(null)

  // Étapes du formulaire
  const steps = [
    { title: "Informations de base", description: "Nom, symbole et offre", icon: <Coins className="h-5 w-5" /> },
    { title: "Mécanismes de frais", description: "Frais et taxes", icon: <Percent className="h-5 w-5" /> },
    { title: "Limites de transaction", description: "Protections avancées", icon: <Shield className="h-5 w-5" /> },
    { title: "Impact sur les prix", description: "Stabilisation du marché", icon: <BarChart3 className="h-5 w-5" /> },
    { title: "Gouvernance DAO", description: "Paramètres DAO", icon: <Users className="h-5 w-5" /> },
    { title: "Confirmation", description: "Révision et création", icon: <Rocket className="h-5 w-5" /> },
  ]

  // Validation du formulaire
  const validateForm = () => {
    const errors: { [key: string]: string } = {}

    // Validation du nom
    if (!tokenName.trim()) {
      errors.name = "Le nom du token est requis"
    } else if (tokenName.length > 50) {
      errors.name = "Le nom du token ne doit pas dépasser 50 caractères"
    }

    // Validation du symbole
    if (!tokenSymbol.trim()) {
      errors.symbol = "Le symbole du token est requis"
    } else if (tokenSymbol.length > 10) {
      errors.symbol = "Le symbole du token ne doit pas dépasser 10 caractères"
    } else if (!isValidBase58(tokenSymbol)) {
      const invalidChar = findInvalidBase58Char(tokenSymbol)
      errors.symbol = invalidChar
        ? `Caractère non valide '${invalidChar.char}' à la position ${invalidChar.position + 1}`
        : "Le symbole contient des caractères non valides"
    }

    // Validation des décimales
    const decimals = Number.parseInt(tokenDecimals)
    if (isNaN(decimals) || decimals < 0 || decimals > 9) {
      errors.decimals = "Les décimales doivent être un nombre entre 0 et 9"
    }

    // Validation de l'offre
    const supply = Number.parseFloat(tokenSupply)
    if (isNaN(supply) || supply <= 0) {
      errors.supply = "L'offre initiale doit être un nombre positif"
    }

    // Validation des frais
    if (baseFee < 0 || baseFee > 100) {
      errors.baseFee = "Les frais de base doivent être entre 0 et 100%"
    }

    if (Math.abs(platformFeeShare + burnFeeShare + priceImpactFeeShare - 1) > 0.01) {
      errors.feeShares = "La somme des parts de frais de base doit être égale à 100%"
    }

    if (additionalFee < 0 || additionalFee > 100) {
      errors.additionalFee = "Les frais additionnels doivent être entre 0 et 100%"
    }

    const totalAdditionalShares =
      additionalPlatformShare + additionalBurnShare + additionalPriceImpactShare + additionalCreatorShare

    if (Math.abs(totalAdditionalShares - 10) > 0.01) {
      errors.additionalFeeShares = "La somme des parts de frais additionnels doit être égale à 10%"
    }

    if (antiGainThreshold < 0 || antiGainThreshold > 1000) {
      errors.antiGainThreshold = "Le seuil de gain excessif doit être entre 0 et 1000%"
    }

    if (antiGainTax < 0 || antiGainTax > 100) {
      errors.antiGainTax = "La taxe sur les gains excessifs doit être entre 0 et 100%"
    }

    // Validation des limites de transaction
    if (maxTransactionSize <= 0) {
      errors.maxTransactionSize = "La taille maximale de transaction doit être positive"
    }

    if (maxWalletHolding <= 0) {
      errors.maxWalletHolding = "La détention maximale par wallet doit être positive"
    }

    if (maxPurchasesPerBlock <= 0) {
      errors.maxPurchasesPerBlock = "Le nombre maximal d'achats par bloc doit être positif"
    }

    if (maxSalesPerBlock <= 0) {
      errors.maxSalesPerBlock = "Le nombre maximal de ventes par bloc doit être positif"
    }

    if (cooldownPeriod < 0) {
      errors.cooldownPeriod = "La période de refroidissement doit être positive ou nulle"
    }

    // Validation du mécanisme d'impact sur les prix
    if (reservePercentage <= 0 || reservePercentage > 100) {
      errors.reservePercentage = "Le pourcentage de réserve doit être entre 0 et 100%"
    }

    if (purchaseTrigger <= 0) {
      errors.purchaseTrigger = "Le déclencheur d'achat doit être positif"
    }

    if (sellTrigger1 <= 0) {
      errors.sellTrigger1 = "Le déclencheur de vente 1 doit être positif"
    }

    if (sellTrigger2 <= sellTrigger1) {
      errors.sellTrigger2 = "Le déclencheur de vente 2 doit être supérieur au déclencheur 1"
    }

    if (sellAmount1 <= 0 || sellAmount1 > 100) {
      errors.sellAmount1 = "Le montant de vente 1 doit être entre 0 et 100%"
    }

    if (sellAmount2 <= 0 || sellAmount2 > 100) {
      errors.sellAmount2 = "Le montant de vente 2 doit être entre 0 et 100%"
    }

    if (minimumInterval <= 0) {
      errors.minimumInterval = "L'intervalle minimum doit être positif"
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Gestion de la navigation entre les étapes
  const goToNextStep = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1)
    }
  }

  const goToPreviousStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1)
    }
  }

  // Création du token
  const createToken = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet non connecté",
        description: "Veuillez connecter votre wallet pour créer un token",
        variant: "destructive",
      })
      return
    }

    if (!validateForm()) {
      toast({
        title: "Formulaire invalide",
        description: "Veuillez corriger les erreurs dans le formulaire",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)
      setError(null)
      setCreationProgress(10)
      setCreationStep("Préparation des données...")

      // Préparer les données pour l'API
      const tokenData = {
        name: tokenName,
        symbol: tokenSymbol,
        decimals: Number.parseInt(tokenDecimals),
        initialSupply: Number.parseFloat(tokenSupply),
        ownerAddress: publicKey.toString(),
        adminKey,
        description: tokenDescription,
        imageUrl: logoUrl,

        fees: {
          baseFee,
          baseFeeDistribution: {
            platform: platformFeeShare,
            burn: burnFeeShare,
            priceImpact: priceImpactFeeShare,
            creator: 0,
          },
          additionalFee,
          additionalFeeDistribution: {
            platform: additionalPlatformShare,
            burn: additionalBurnShare,
            priceImpact: additionalPriceImpactShare,
            creator: additionalCreatorShare,
          },
          antiGainThreshold,
          antiGainTax,
        },

        transactionLimits: {
          maxTransactionSize,
          maxWalletHolding,
          maxPurchasesPerBlock,
          maxSalesPerBlock,
          cooldownPeriod,
        },

        priceImpactConfig: {
          reservePercentage,
          purchaseTrigger,
          sellTrigger1,
          sellTrigger2,
          sellAmount1,
          sellAmount2,
          minimumInterval,
        },

        networkId: activeNetwork.id,
      }

      setCreationProgress(30)
      setCreationStep("Création du token sur la blockchain...")

      // Appeler l'API pour créer le token
      const response = await fetch("/api/token/create-advanced", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(tokenData),
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || "Échec de la création du token")
      }

      setCreationProgress(80)
      setCreationStep("Finalisation...")

      // Stocker les résultats
      setMintAddress(result.mintAddress)
      setOwnerTokenAccount(result.ownerTokenAccount)
      setBurnWallet(result.burnWallet)
      setPriceImpactWallet(result.priceImpactWallet)
      setSignature(result.signature)

      setCreationProgress(100)
      setCreationStep("Token créé avec succès!")
      setSuccess(true)

      toast({
        title: "Token créé avec succès",
        description: `Votre token ${tokenName} (${tokenSymbol}) a été créé avec succès`,
      })

      // Rediriger vers la page de détails du token après 2 secondes
      setTimeout(() => {
        router.push(`/token-factory/advanced/${result.mintAddress}`)
      }, 2000)
    } catch (err: any) {
      setError(err.message || "Une erreur s'est produite lors de la création du token")
      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors de la création du token",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Rendu des étapes du formulaire
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tokenName">Nom du token</Label>
              <Input
                id="tokenName"
                placeholder="Ex: My Advanced Token"
                value={tokenName}
                onChange={(e) => setTokenName(e.target.value)}
                className={validationErrors.name ? "border-red-500" : ""}
              />
              {validationErrors.name && <p className="text-sm text-red-500">{validationErrors.name}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="tokenSymbol">Symbole du token</Label>
              <Input
                id="tokenSymbol"
                placeholder="Ex: MAT"
                value={tokenSymbol}
                onChange={(e) => setTokenSymbol(e.target.value.toUpperCase())}
                className={validationErrors.symbol ? "border-red-500" : ""}
              />
              {validationErrors.symbol && <p className="text-sm text-red-500">{validationErrors.symbol}</p>}
              <p className="text-xs text-gray-500">Le symbole doit être en caractères Base58 (A-Z, a-z, 0-9)</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tokenDecimals">Décimales</Label>
              <Input
                id="tokenDecimals"
                type="number"
                min="0"
                max="9"
                value={tokenDecimals}
                onChange={(e) => setTokenDecimals(e.target.value)}
                className={validationErrors.decimals ? "border-red-500" : ""}
              />
              {validationErrors.decimals && <p className="text-sm text-red-500">{validationErrors.decimals}</p>}
              <p className="text-xs text-gray-500">
                Nombre de décimales pour le token (0-9). La valeur standard est 9.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tokenSupply">Offre initiale</Label>
              <Input
                id="tokenSupply"
                type="number"
                min="1"
                value={tokenSupply}
                onChange={(e) => setTokenSupply(e.target.value)}
                className={validationErrors.supply ? "border-red-500" : ""}
              />
              {validationErrors.supply && <p className="text-sm text-red-500">{validationErrors.supply}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="tokenDescription">Description (optionnelle)</Label>
              <Textarea
                id="tokenDescription"
                placeholder="Description de votre token..."
                value={tokenDescription}
                onChange={(e) => setTokenDescription(e.target.value)}
                rows={3}
              />
            </div>
          </div>
        )

      case 1:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Frais de base (sur toutes les transactions)</h3>

              <div className="space-y-2">
                <Label>Frais de base: {baseFee}%</Label>
                <Slider value={[baseFee]} min={0} max={10} step={0.1} onValueChange={(value) => setBaseFee(value[0])} />
                {validationErrors.baseFee && <p className="text-sm text-red-500">{validationErrors.baseFee}</p>}
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Part plateforme: {(platformFeeShare * 100).toFixed(1)}%</Label>
                  <Slider
                    value={[platformFeeShare * 100]}
                    min={0}
                    max={100}
                    step={1}
                    onValueChange={(value) => setPlatformFeeShare(value[0] / 100)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Part burn: {(burnFeeShare * 100).toFixed(1)}%</Label>
                  <Slider
                    value={[burnFeeShare * 100]}
                    min={0}
                    max={100}
                    step={1}
                    onValueChange={(value) => setBurnFeeShare(value[0] / 100)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Part impact prix: {(priceImpactFeeShare * 100).toFixed(1)}%</Label>
                  <Slider
                    value={[priceImpactFeeShare * 100]}
                    min={0}
                    max={100}
                    step={1}
                    onValueChange={(value) => setPriceImpactFeeShare(value[0] / 100)}
                  />
                </div>
              </div>

              {validationErrors.feeShares && <p className="text-sm text-red-500">{validationErrors.feeShares}</p>}

              <p className="text-xs text-gray-500">
                Total: {((platformFeeShare + burnFeeShare + priceImpactFeeShare) * 100).toFixed(1)}% (doit être égal à
                100%)
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Frais additionnels (sur les swaps uniquement)</h3>

              <div className="space-y-2">
                <Label>Frais additionnels: {additionalFee}%</Label>
                <Slider
                  value={[additionalFee]}
                  min={0}
                  max={20}
                  step={0.5}
                  onValueChange={(value) => setAdditionalFee(value[0])}
                />
                {validationErrors.additionalFee && (
                  <p className="text-sm text-red-500">{validationErrors.additionalFee}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Part plateforme: {additionalPlatformShare}%</Label>
                  <Slider
                    value={[additionalPlatformShare]}
                    min={0}
                    max={10}
                    step={0.1}
                    onValueChange={(value) => setAdditionalPlatformShare(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Part burn: {additionalBurnShare}%</Label>
                  <Slider
                    value={[additionalBurnShare]}
                    min={0}
                    max={10}
                    step={0.1}
                    onValueChange={(value) => setAdditionalBurnShare(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Part impact prix: {additionalPriceImpactShare}%</Label>
                  <Slider
                    value={[additionalPriceImpactShare]}
                    min={0}
                    max={10}
                    step={0.1}
                    onValueChange={(value) => setAdditionalPriceImpactShare(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Part créateur: {additionalCreatorShare}%</Label>
                  <Slider
                    value={[additionalCreatorShare]}
                    min={0}
                    max={10}
                    step={0.1}
                    onValueChange={(value) => setAdditionalCreatorShare(value[0])}
                  />
                </div>
              </div>

              {validationErrors.additionalFeeShares && (
                <p className="text-sm text-red-500">{validationErrors.additionalFeeShares}</p>
              )}

              <p className="text-xs text-gray-500">
                Total:{" "}
                {(
                  additionalPlatformShare +
                  additionalBurnShare +
                  additionalPriceImpactShare +
                  additionalCreatorShare
                ).toFixed(1)}
                % (doit être égal à 10%)
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Taxe anti-gain excessif</h3>

              <div className="space-y-2">
                <Label>Seuil de gain: {antiGainThreshold}%</Label>
                <Slider
                  value={[antiGainThreshold]}
                  min={50}
                  max={500}
                  step={10}
                  onValueChange={(value) => setAntiGainThreshold(value[0])}
                />
                {validationErrors.antiGainThreshold && (
                  <p className="text-sm text-red-500">{validationErrors.antiGainThreshold}</p>
                )}
                <p className="text-xs text-gray-500">
                  Seuil à partir duquel la taxe sur les gains excessifs s'applique
                </p>
              </div>

              <div className="space-y-2">
                <Label>Taxe sur les gains: {antiGainTax}%</Label>
                <Slider
                  value={[antiGainTax]}
                  min={0}
                  max={50}
                  step={1}
                  onValueChange={(value) => setAntiGainTax(value[0])}
                />
                {validationErrors.antiGainTax && <p className="text-sm text-red-500">{validationErrors.antiGainTax}</p>}
                <p className="text-xs text-gray-500">Pourcentage du profit qui sera taxé au-delà du seuil</p>
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Limites de transaction</h3>

              <div className="space-y-2">
                <Label htmlFor="maxTransactionSize">Taille maximale de transaction</Label>
                <Input
                  id="maxTransactionSize"
                  type="number"
                  min="1"
                  value={maxTransactionSize}
                  onChange={(e) => setMaxTransactionSize(Number(e.target.value))}
                  className={validationErrors.maxTransactionSize ? "border-red-500" : ""}
                />
                {validationErrors.maxTransactionSize && (
                  <p className="text-sm text-red-500">{validationErrors.maxTransactionSize}</p>
                )}
                <p className="text-xs text-gray-500">Nombre maximum de tokens par transaction</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxWalletHolding">Détention maximale par wallet</Label>
                <Input
                  id="maxWalletHolding"
                  type="number"
                  min="1"
                  value={maxWalletHolding}
                  onChange={(e) => setMaxWalletHolding(Number(e.target.value))}
                  className={validationErrors.maxWalletHolding ? "border-red-500" : ""}
                />
                {validationErrors.maxWalletHolding && (
                  <p className="text-sm text-red-500">{validationErrors.maxWalletHolding}</p>
                )}
                <p className="text-xs text-gray-500">Nombre maximum de tokens qu'un wallet peut détenir</p>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Protection anti-bot</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="maxPurchasesPerBlock">Achats max par bloc</Label>
                  <Input
                    id="maxPurchasesPerBlock"
                    type="number"
                    min="1"
                    value={maxPurchasesPerBlock}
                    onChange={(e) => setMaxPurchasesPerBlock(Number(e.target.value))}
                    className={validationErrors.maxPurchasesPerBlock ? "border-red-500" : ""}
                  />
                  {validationErrors.maxPurchasesPerBlock && (
                    <p className="text-sm text-red-500">{validationErrors.maxPurchasesPerBlock}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxSalesPerBlock">Ventes max par bloc</Label>
                  <Input
                    id="maxSalesPerBlock"
                    type="number"
                    min="1"
                    value={maxSalesPerBlock}
                    onChange={(e) => setMaxSalesPerBlock(Number(e.target.value))}
                    className={validationErrors.maxSalesPerBlock ? "border-red-500" : ""}
                  />
                  {validationErrors.maxSalesPerBlock && (
                    <p className="text-sm text-red-500">{validationErrors.maxSalesPerBlock}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cooldownPeriod">Période de refroidissement (secondes)</Label>
                <Input
                  id="cooldownPeriod"
                  type="number"
                  min="0"
                  value={cooldownPeriod}
                  onChange={(e) => setCooldownPeriod(Number(e.target.value))}
                  className={validationErrors.cooldownPeriod ? "border-red-500" : ""}
                />
                {validationErrors.cooldownPeriod && (
                  <p className="text-sm text-red-500">{validationErrors.cooldownPeriod}</p>
                )}
                <p className="text-xs text-gray-500">
                  Temps minimum entre deux transactions du même wallet (en secondes)
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch id="antiSandwich" defaultChecked />
                <Label htmlFor="antiSandwich">Protection anti-sandwich</Label>
              </div>
              <p className="text-xs text-gray-500">
                Protège contre les attaques sandwich en détectant les patterns suspects
              </p>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Configuration de l'impact sur les prix</h3>

              <div className="space-y-2">
                <Label>Pourcentage de réserve: {reservePercentage}%</Label>
                <Slider
                  value={[reservePercentage]}
                  min={1}
                  max={20}
                  step={1}
                  onValueChange={(value) => setReservePercentage(value[0])}
                />
                {validationErrors.reservePercentage && (
                  <p className="text-sm text-red-500">{validationErrors.reservePercentage}</p>
                )}
                <p className="text-xs text-gray-500">
                  Pourcentage de l'offre totale à réserver pour la stabilisation des prix
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Déclencheurs d'achat</h3>

              <div className="space-y-2">
                <Label>Déclencheur d'achat: {purchaseTrigger}% de baisse</Label>
                <Slider
                  value={[purchaseTrigger]}
                  min={1}
                  max={30}
                  step={1}
                  onValueChange={(value) => setPurchaseTrigger(value[0])}
                />
                {validationErrors.purchaseTrigger && (
                  <p className="text-sm text-red-500">{validationErrors.purchaseTrigger}</p>
                )}
                <p className="text-xs text-gray-500">
                  Pourcentage de baisse de prix qui déclenche un achat automatique
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Déclencheurs de vente</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Déclencheur 1: {sellTrigger1}% de hausse</Label>
                  <Slider
                    value={[sellTrigger1]}
                    min={10}
                    max={200}
                    step={5}
                    onValueChange={(value) => setSellTrigger1(value[0])}
                  />
                  {validationErrors.sellTrigger1 && (
                    <p className="text-sm text-red-500">{validationErrors.sellTrigger1}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Montant 1: {sellAmount1}% de la réserve</Label>
                  <Slider
                    value={[sellAmount1]}
                    min={1}
                    max={50}
                    step={1}
                    onValueChange={(value) => setSellAmount1(value[0])}
                  />
                  {validationErrors.sellAmount1 && (
                    <p className="text-sm text-red-500">{validationErrors.sellAmount1}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Déclencheur 2: {sellTrigger2}% de hausse</Label>
                  <Slider
                    value={[sellTrigger2]}
                    min={sellTrigger1 + 10}
                    max={500}
                    step={10}
                    onValueChange={(value) => setSellTrigger2(value[0])}
                  />
                  {validationErrors.sellTrigger2 && (
                    <p className="text-sm text-red-500">{validationErrors.sellTrigger2}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Montant 2: {sellAmount2}% de la réserve</Label>
                  <Slider
                    value={[sellAmount2]}
                    min={1}
                    max={50}
                    step={1}
                    onValueChange={(value) => setSellAmount2(value[0])}
                  />
                  {validationErrors.sellAmount2 && (
                    <p className="text-sm text-red-500">{validationErrors.sellAmount2}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Intervalle minimum: {(minimumInterval / 60).toFixed(0)} minutes</Label>
              <Slider
                value={[minimumInterval / 60]}
                min={1}
                max={120}
                step={1}
                onValueChange={(value) => setMinimumInterval(value[0] * 60)}
              />
              {validationErrors.minimumInterval && (
                <p className="text-sm text-red-500">{validationErrors.minimumInterval}</p>
              )}
              <p className="text-xs text-gray-500">Temps minimum entre deux interventions automatiques (en minutes)</p>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Paramètres de gouvernance DAO</h3>

              <div className="space-y-2">
                <Label>Quorum pour les propositions: 10%</Label>
                <Slider value={[10]} min={1} max={50} step={1} disabled />
                <p className="text-xs text-gray-500">
                  Pourcentage de l'offre totale qui doit voter pour qu'une proposition soit valide
                </p>
              </div>

              <div className="space-y-2">
                <Label>Seuil d'adoption: 50%</Label>
                <Slider value={[50]} min={50} max={90} step={1} disabled />
                <p className="text-xs text-gray-500">
                  Pourcentage de votes "pour" nécessaire pour qu'une proposition soit adoptée
                </p>
              </div>

              <div className="space-y-2">
                <Label>Durée des votes: 3 jours</Label>
                <Slider value={[3]} min={1} max={14} step={1} disabled />
                <p className="text-xs text-gray-500">Durée pendant laquelle une proposition peut être votée</p>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Types de propositions</h3>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch id="unblockProposals" defaultChecked disabled />
                  <Label htmlFor="unblockProposals">Déblocage de wallets blacklistés</Label>
                </div>
                <p className="text-xs text-gray-500">
                  Permet de créer des propositions pour débloquer des wallets blacklistés
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch id="parameterProposals" defaultChecked disabled />
                  <Label htmlFor="parameterProposals">Modification des paramètres</Label>
                </div>
                <p className="text-xs text-gray-500">
                  Permet de créer des propositions pour modifier les paramètres du token
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch id="fundProposals" defaultChecked disabled />
                  <Label htmlFor="fundProposals">Allocation de fonds</Label>
                </div>
                <p className="text-xs text-gray-500">
                  Permet de créer des propositions pour allouer des fonds du trésor
                </p>
              </div>
            </div>

            <Alert>
              <AlertTitle>Note importante</AlertTitle>
              <AlertDescription>
                Le DAO sera déployé automatiquement avec les paramètres par défaut. Ces paramètres pourront être
                modifiés ultérieurement par des propositions de gouvernance.
              </AlertDescription>
            </Alert>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">Récapitulatif du token</h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium">Nom</p>
                <p>{tokenName}</p>
              </div>

              <div>
                <p className="text-sm font-medium">Symbole</p>
                <p>{tokenSymbol}</p>
              </div>

              <div>
                <p className="text-sm font-medium">Décimales</p>
                <p>{tokenDecimals}</p>
              </div>

              <div>
                <p className="text-sm font-medium">Offre initiale</p>
                <p>{Number(tokenSupply).toLocaleString()}</p>
              </div>
            </div>

            <div>
              <p className="text-sm font-medium">Frais de base</p>
              <p>
                {baseFee}% (Plateforme: {(platformFeeShare * 100).toFixed(1)}%, Burn: {(burnFeeShare * 100).toFixed(1)}
                %, Impact prix: {(priceImpactFeeShare * 100).toFixed(1)}%)
              </p>
            </div>

            <div>
              <p className="text-sm font-medium">Frais additionnels</p>
              <p>
                {additionalFee}% (Plateforme: {additionalPlatformShare}%, Burn: {additionalBurnShare}%, Impact prix:{" "}
                {additionalPriceImpactShare}%, Créateur: {additionalCreatorShare}%)
              </p>
            </div>

            <div>
              <p className="text-sm font-medium">Taxe anti-gain excessif</p>
              <p>
                {antiGainTax}% au-delà de {antiGainThreshold}% de gain
              </p>
            </div>

            <div>
              <p className="text-sm font-medium">Limites de transaction</p>
              <p>
                Max par tx: {maxTransactionSize.toLocaleString()}, Max par wallet: {maxWalletHolding.toLocaleString()}
              </p>
              <p>
                Max achats/bloc: {maxPurchasesPerBlock}, Max ventes/bloc: {maxSalesPerBlock}
              </p>
              <p>Cooldown: {cooldownPeriod} secondes</p>
            </div>

            <div>
              <p className="text-sm font-medium">Impact sur les prix</p>
              <p>
                Réserve: {reservePercentage}%, Achat à -{purchaseTrigger}%, Vente à +{sellTrigger1}% et +{sellTrigger2}%
              </p>
            </div>

            <div>
              <p className="text-sm font-medium">Réseau</p>
              <p>{activeNetwork.name}</p>
            </div>

            <div>
              <p className="text-sm font-medium">Propriétaire</p>
              <p className="text-xs break-all">{publicKey?.toString()}</p>
            </div>

            {!connected && (
              <Alert variant="destructive">
                <AlertTitle>Wallet non connecté</AlertTitle>
                <AlertDescription>Veuillez connecter votre wallet pour créer le token.</AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertTitle>Erreur</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && mintAddress && (
              <Alert variant="default" className="bg-green-50 border-green-200">
                <AlertTitle>Token créé avec succès!</AlertTitle>
                <AlertDescription>
                  <p>Adresse du mint: {mintAddress}</p>
                  <p>Vous allez être redirigé vers la page de détails du token...</p>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Créer un token avancé</CardTitle>
        <CardDescription>
          Créez un token avec des fonctionnalités avancées: frais, taxes, protections anti-bot, impact sur les prix et
          gouvernance DAO.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="mb-8">
          <div className="flex justify-between mb-2">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`flex flex-col items-center ${index <= activeStep ? "text-primary" : "text-gray-400"}`}
                style={{ width: `${100 / steps.length}%` }}
              >
                <div
                  className={`rounded-full p-2 mb-2 ${index <= activeStep ? "bg-primary text-white" : "bg-gray-200"}`}
                >
                  {step.icon}
                </div>
                <div className="text-xs text-center">{step.title}</div>
              </div>
            ))}
          </div>

          <Progress value={(activeStep / (steps.length - 1)) * 100} className="h-1" />
        </div>

        {renderStepContent()}

        {isLoading && (
          <div className="mt-6 space-y-4">
            <Progress value={creationProgress} className="h-2" />
            <p className="text-center text-sm">{creationStep}</p>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={goToPreviousStep} disabled={activeStep === 0 || isLoading}>
          Précédent
        </Button>

        {activeStep < steps.length - 1 ? (
          <Button onClick={goToNextStep} disabled={isLoading}>
            Suivant
          </Button>
        ) : (
          <Button onClick={createToken} disabled={!connected || isLoading || success}>
            {isLoading ? "Création en cours..." : "Créer le token"}
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

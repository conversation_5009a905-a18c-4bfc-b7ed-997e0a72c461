"use client"

import { useState } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"
import { AlertCircle, CheckCircle2, Loader2, Lock, Unlock, Search, RefreshCw } from "lucide-react"

interface TokenFreezeManagerProps {
  adminKey: string
}

export default function TokenFreezeManager({ adminKey }: TokenFreezeManagerProps) {
  const { publicKey, connected } = useWallet()
  const { toast } = useToast()
  const { activeNetwork } = useNetwork()

  // États du formulaire
  const [mintAddress, setMintAddress] = useState("")
  const [targetAddress, setTargetAddress] = useState("")
  const [isFrozen, setIsFrozen] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isChecking, setIsChecking] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("freeze")

  // Vérifier si un compte est gelé
  const checkFrozenStatus = async () => {
    if (!mintAddress || !targetAddress) {
      toast({
        title: "Champs requis",
        description: "Veuillez saisir l'adresse du token et l'adresse cible",
        variant: "destructive",
      })
      return
    }

    setIsChecking(true)
    setError(null)
    setIsFrozen(null)

    try {
      const response = await fetch(
        `/api/token/check-frozen?mintAddress=${mintAddress}&targetAddress=${targetAddress}&networkId=${activeNetwork?.id || "solana-devnet"}`,
      )
      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || "Échec de la vérification du statut de gel")
      }

      setIsFrozen(result.isFrozen)

      toast({
        title: "Statut vérifié",
        description: `Le compte est ${result.isFrozen ? "gelé" : "non gelé"}`,
      })
    } catch (err: any) {
      console.error("Erreur lors de la vérification du statut de gel:", err)
      setError(err.message || "Une erreur s'est produite lors de la vérification du statut de gel")

      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors de la vérification du statut de gel",
        variant: "destructive",
      })
    } finally {
      setIsChecking(false)
    }
  }

  // Geler un compte
  const freezeAccount = async () => {
    if (!mintAddress || !targetAddress) {
      toast({
        title: "Champs requis",
        description: "Veuillez saisir l'adresse du token et l'adresse cible",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/token/freeze", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          mintAddress,
          targetAddress,
          adminKey,
          networkId: activeNetwork?.id || "solana-devnet",
        }),
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || "Échec du gel du compte")
      }

      setSuccess(`Compte gelé avec succès. Signature: ${result.signature}`)
      setIsFrozen(true)

      toast({
        title: "Compte gelé",
        description: "Le compte a été gelé avec succès",
      })
    } catch (err: any) {
      console.error("Erreur lors du gel du compte:", err)
      setError(err.message || "Une erreur s'est produite lors du gel du compte")

      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors du gel du compte",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Dégeler un compte
  const thawAccount = async () => {
    if (!mintAddress || !targetAddress) {
      toast({
        title: "Champs requis",
        description: "Veuillez saisir l'adresse du token et l'adresse cible",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch("/api/token/thaw", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          mintAddress,
          targetAddress,
          adminKey,
          networkId: activeNetwork?.id || "solana-devnet",
        }),
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || "Échec du dégel du compte")
      }

      setSuccess(`Compte dégelé avec succès. Signature: ${result.signature}`)
      setIsFrozen(false)

      toast({
        title: "Compte dégelé",
        description: "Le compte a été dégelé avec succès",
      })
    } catch (err: any) {
      console.error("Erreur lors du dégel du compte:", err)
      setError(err.message || "Une erreur s'est produite lors du dégel du compte")

      toast({
        title: "Erreur",
        description: err.message || "Une erreur s'est produite lors du dégel du compte",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Gestionnaire de gel de tokens</CardTitle>
        <CardDescription>Gérez les comptes gelés pour vos tokens sur {activeNetwork?.name || "Solana"}</CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-6 bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Succès</AlertTitle>
            <AlertDescription className="text-green-700">{success}</AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="freeze">Geler</TabsTrigger>
            <TabsTrigger value="thaw">Dégeler</TabsTrigger>
            <TabsTrigger value="check">Vérifier</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="mintAddress">Adresse du token (Mint)</Label>
            <Input
              id="mintAddress"
              placeholder="ex: 7KVexUZJ9Gx6hZuMXHaU7XT6xyM3JwZXH1qArjPurQHJ"
              value={mintAddress}
              onChange={(e) => setMintAddress(e.target.value)}
              required
            />
            <p className="text-xs text-muted-foreground">L'adresse du token que vous souhaitez gérer</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="targetAddress">Adresse cible</Label>
            <Input
              id="targetAddress"
              placeholder="ex: 7KVexUZJ9Gx6hZuMXHaU7XT6xyM3JwZXH1qArjPurQHJ"
              value={targetAddress}
              onChange={(e) => setTargetAddress(e.target.value)}
              required
            />
            <p className="text-xs text-muted-foreground">
              L'adresse du portefeuille dont vous souhaitez geler/dégeler les tokens
            </p>
          </div>

          {isFrozen !== null && (
            <Alert className={isFrozen ? "bg-blue-50 border-blue-200" : "bg-green-50 border-green-200"}>
              {isFrozen ? <Lock className="h-4 w-4 text-blue-600" /> : <Unlock className="h-4 w-4 text-green-600" />}
              <AlertTitle className={isFrozen ? "text-blue-800" : "text-green-800"}>
                {isFrozen ? "Compte gelé" : "Compte non gelé"}
              </AlertTitle>
              <AlertDescription className={isFrozen ? "text-blue-700" : "text-green-700"}>
                {isFrozen
                  ? "Ce compte est actuellement gelé. Les tokens ne peuvent pas être transférés."
                  : "Ce compte n'est pas gelé. Les tokens peuvent être transférés normalement."}
              </AlertDescription>
            </Alert>
          )}

          {activeTab === "freeze" && (
            <Button onClick={freezeAccount} disabled={isLoading || !mintAddress || !targetAddress} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Gel en cours...
                </>
              ) : (
                <>
                  <Lock className="mr-2 h-4 w-4" />
                  Geler le compte
                </>
              )}
            </Button>
          )}

          {activeTab === "thaw" && (
            <Button onClick={thawAccount} disabled={isLoading || !mintAddress || !targetAddress} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Dégel en cours...
                </>
              ) : (
                <>
                  <Unlock className="mr-2 h-4 w-4" />
                  Dégeler le compte
                </>
              )}
            </Button>
          )}

          {activeTab === "check" && (
            <Button
              onClick={checkFrozenStatus}
              disabled={isChecking || !mintAddress || !targetAddress}
              className="w-full"
            >
              {isChecking ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Vérification en cours...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Vérifier le statut
                </>
              )}
            </Button>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <div className="text-sm text-muted-foreground">Réseau: {activeNetwork?.name || "Solana Devnet"}</div>
        <Button variant="outline" size="sm" onClick={checkFrozenStatus} disabled={isChecking}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Actualiser
        </Button>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { <PERSON>lider } from "@/components/ui/slider"
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from "recharts"
import { Plus, Trash2, PieChartIcon } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface TokenomicsConfigurationProps {
  tokenSupply: string
  distribution: Array<{ label: string; percentage: number; address?: string }>
  onDistributionChange: (distribution: Array<{ label: string; percentage: number; address?: string }>) => void
}

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d", "#ffc658", "#8dd1e1"]

export function TokenomicsConfiguration({
  tokenSupply,
  distribution,
  onDistributionChange,
}: TokenomicsConfigurationProps) {
  const [newCategoryLabel, setNewCategoryLabel] = useState("")
  const [newCategoryPercentage, setNewCategoryPercentage] = useState(5)
  const [newCategoryAddress, setNewCategoryAddress] = useState("")
  const [error, setError] = useState<string | null>(null)

  const totalPercentage = distribution.reduce((sum, item) => sum + item.percentage, 0)
  const remaining = 100 - totalPercentage

  const handleAddCategory = () => {
    if (!newCategoryLabel.trim()) {
      setError("Veuillez entrer un nom pour la catégorie")
      return
    }

    if (newCategoryPercentage <= 0) {
      setError("Le pourcentage doit être supérieur à 0")
      return
    }

    if (totalPercentage + newCategoryPercentage > 100) {
      setError(`Le total ne peut pas dépasser 100%. Vous pouvez ajouter jusqu'à ${remaining}%`)
      return
    }

    const newDistribution = [
      ...distribution,
      {
        label: newCategoryLabel,
        percentage: newCategoryPercentage,
        address: newCategoryAddress || undefined,
      },
    ]

    onDistributionChange(newDistribution)
    setNewCategoryLabel("")
    setNewCategoryPercentage(5)
    setNewCategoryAddress("")
    setError(null)
  }

  const handleRemoveCategory = (index: number) => {
    const newDistribution = [...distribution]
    newDistribution.splice(index, 1)
    onDistributionChange(newDistribution)
  }

  const handleUpdatePercentage = (index: number, percentage: number) => {
    const newDistribution = [...distribution]
    const oldPercentage = newDistribution[index].percentage
    const diff = percentage - oldPercentage

    if (totalPercentage + diff > 100) {
      setError(`Le total ne peut pas dépasser 100%`)
      return
    }

    newDistribution[index].percentage = percentage
    onDistributionChange(newDistribution)
    setError(null)
  }

  const handleUpdateAddress = (index: number, address: string) => {
    const newDistribution = [...distribution]
    newDistribution[index].address = address || undefined
    onDistributionChange(newDistribution)
  }

  // Préparer les données pour le graphique
  const chartData = distribution.map((item) => ({
    name: item.label,
    value: item.percentage,
  }))

  // Ajouter une entrée pour le pourcentage restant si nécessaire
  if (remaining > 0) {
    chartData.push({
      name: "Non alloué",
      value: remaining,
    })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Distribution des tokens</CardTitle>
          <CardDescription>
            Définissez comment les tokens seront distribués entre les différentes catégories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Catégories de distribution</h3>
                <div className="text-sm">
                  Total:{" "}
                  <span className={totalPercentage > 100 ? "text-red-500 font-bold" : "font-bold"}>
                    {totalPercentage}%
                  </span>
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                {distribution.map((item, index) => (
                  <div key={index} className="space-y-2 p-3 border rounded-md">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: COLORS[index % COLORS.length] }}
                        ></div>
                        <span className="font-medium">{item.label}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveCategory(index)}
                        className="h-7 w-7 p-0"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex items-center gap-2">
                      <Slider
                        value={[item.percentage]}
                        min={1}
                        max={100}
                        step={1}
                        onValueChange={(value) => handleUpdatePercentage(index, value[0])}
                        className="flex-1"
                      />
                      <div className="w-12 text-center font-medium">{item.percentage}%</div>
                    </div>

                    <div className="space-y-1">
                      <Label htmlFor={`address-${index}`} className="text-xs">
                        Adresse de destination (optionnel)
                      </Label>
                      <Input
                        id={`address-${index}`}
                        placeholder="Adresse du portefeuille"
                        value={item.address || ""}
                        onChange={(e) => handleUpdateAddress(index, e.target.value)}
                        className="h-8 text-xs"
                      />
                    </div>

                    <div className="text-xs text-muted-foreground">
                      {Number(tokenSupply).toLocaleString()} × {item.percentage}% ={" "}
                      {Math.floor((Number(tokenSupply) * item.percentage) / 100).toLocaleString()} tokens
                    </div>
                  </div>
                ))}
              </div>

              <div className="space-y-2 p-3 border rounded-md border-dashed">
                <div className="space-y-2">
                  <Label htmlFor="new-category" className="text-sm">
                    Nouvelle catégorie
                  </Label>
                  <Input
                    id="new-category"
                    placeholder="Nom de la catégorie"
                    value={newCategoryLabel}
                    onChange={(e) => setNewCategoryLabel(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="new-percentage" className="text-sm">
                      Pourcentage
                    </Label>
                    <span className="text-sm font-medium">{newCategoryPercentage}%</span>
                  </div>
                  <Slider
                    id="new-percentage"
                    value={[newCategoryPercentage]}
                    min={1}
                    max={Math.min(100, remaining > 0 ? remaining : 100)}
                    step={1}
                    onValueChange={(value) => setNewCategoryPercentage(value[0])}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new-address" className="text-sm">
                    Adresse de destination (optionnel)
                  </Label>
                  <Input
                    id="new-address"
                    placeholder="Adresse du portefeuille"
                    value={newCategoryAddress}
                    onChange={(e) => setNewCategoryAddress(e.target.value)}
                  />
                </div>

                <Button
                  onClick={handleAddCategory}
                  className="w-full mt-2"
                  disabled={!newCategoryLabel.trim() || newCategoryPercentage <= 0 || totalPercentage >= 100}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter une catégorie
                </Button>
              </div>
            </div>

            <div className="flex flex-col items-center justify-center h-full">
              {chartData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex flex-col items-center justify-center text-muted-foreground">
                  <PieChartIcon className="h-16 w-16 mb-2" />
                  <p>Ajoutez des catégories pour voir le graphique</p>
                </div>
              )}

              <div className="mt-4 text-center">
                <h3 className="font-medium">Offre totale: {Number(tokenSupply).toLocaleString()} tokens</h3>
                <p className="text-sm text-muted-foreground">
                  {remaining > 0
                    ? `${remaining}% non alloués (${Math.floor(
                        (Number(tokenSupply) * remaining) / 100,
                      ).toLocaleString()} tokens)`
                    : "100% alloués"}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

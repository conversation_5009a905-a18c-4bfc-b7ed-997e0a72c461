"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useNetwork } from "@/contexts/network-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  CheckCircle2,
  <PERSON>ertCircle,
  Loader2,
  <PERSON><PERSON>,
  <PERSON>R<PERSON>,
  <PERSON><PERSON>,
  ExternalLink,
  Upload,
  ImageIcon,
  Droplet,
  Lock,
  Rocket,
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import TokenPaymentService from "@/lib/token-payment-service"
import TokenSuffixService from "@/lib/token-suffix-service"
import TokenMetadataService from "@/lib/token-metadata-service"

export default function AdvancedTokenCreator() {
  // Wallet connection
  const { publicKey, signTransaction, connected } = useWallet()
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()

  // Form state
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState("9")
  const [tokenSupply, setTokenSupply] = useState("1000000000")
  const [maxSupply, setMaxSupply] = useState("10000000000")
  const [tokenDescription, setTokenDescription] = useState("")
  const [tokenWebsite, setTokenWebsite] = useState("")
  const [tokenTwitter, setTokenTwitter] = useState("")
  const [tokenTelegram, setTokenTelegram] = useState("")
  const [isMintable, setIsMintable] = useState(true)
  const [isBurnable, setIsBurnable] = useState(true)
  const [isPausable, setIsPausable] = useState(false)
  const [isTransferTaxable, setIsTransferTaxable] = useState(false)
  const [transferTaxRate, setTransferTaxRate] = useState(0)
  const [activeTab, setActiveTab] = useState("basic")

  // Métadonnées avancées
  const [tokenImage, setTokenImage] = useState<File | null>(null)
  const [tokenImagePreview, setTokenImagePreview] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Liquidité
  const [addLiquidity, setAddLiquidity] = useState(false)
  const [liquidityAmount, setLiquidityAmount] = useState("0.1")
  const [liquidityPercentage, setLiquidityPercentage] = useState(50)
  const [lockLiquidity, setLockLiquidity] = useState(false)
  const [lockDuration, setLockDuration] = useState(180)
  const [setupInitialTrade, setSetupInitialTrade] = useState(false)

  // Creation state
  const [isCreating, setIsCreating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [createdToken, setCreatedToken] = useState<any>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const [tokenSuffix, setTokenSuffix] = useState("GF")
  const [creationFee, setCreationFee] = useState(0.01)
  const [generatedAddress, setGeneratedAddress] = useState<string | null>(null)

  // Load token suffix, creation fee, and check wallet balance
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load suffix
        const suffix = await TokenSuffixService.getSuffixForNetwork(activeNetwork.id)
        console.log(`Loaded suffix for network ${activeNetwork.id}: ${suffix}`)
        setTokenSuffix(suffix)

        // Get token creation fee
        const fee = TokenPaymentService.getTokenCreationFee()
        setCreationFee(fee)

        // Check wallet balance if connected
        if (publicKey) {
          const balanceCheck = await TokenPaymentService.checkBalance(publicKey.toString())
          setWalletBalance(balanceCheck.balance)
        }
      } catch (error) {
        console.error("Error loading initial data:", error)
      }
    }

    loadInitialData()
  }, [publicKey, activeNetwork.id])

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setTokenImage(file)

      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setTokenImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Handle token creation
  const createToken = async () => {
    if (!connected || !publicKey || !signTransaction) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to create a token",
        variant: "destructive",
      })
      return
    }

    // Validate form
    if (!tokenName || !tokenSymbol || !tokenDecimals || !tokenSupply) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    // Check wallet balance
    const totalFee = creationFee + (addLiquidity ? Number(liquidityAmount) : 0)
    if (walletBalance === null || walletBalance < totalFee) {
      toast({
        title: "Insufficient balance",
        description: `You need at least ${totalFee} SOL to create a token${addLiquidity ? " with liquidity" : ""}`,
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setProgress(0)
    setError(null)
    setCurrentStep("Initializing...")

    try {
      // Étape 1: Générer l'adresse du token (automatiquement)
      setProgress(10)
      setCurrentStep("Generating token address...")

      const result = await TokenSuffixService.getKeypairWithSuffix(tokenSuffix)
      if (!result.success || !result.keypair) {
        throw new Error(result.error || "Failed to generate token address")
      }

      const tokenAddress = result.keypair.publicKey
      setGeneratedAddress(tokenAddress)

      // Étape 2: Créer une transaction de paiement
      setProgress(20)
      setCurrentStep("Processing payment...")

      const { transaction } = await TokenPaymentService.createPaymentTransaction(publicKey.toString())
      const signedTransaction = await signTransaction(transaction)
      const paymentResult = await TokenPaymentService.processPayment(signedTransaction)

      if (!paymentResult.success) {
        throw new Error(paymentResult.error || "Payment failed")
      }

      // Étape 3: Télécharger l'image si fournie
      setProgress(30)
      setCurrentStep("Uploading token image...")

      let imageUri = null
      if (tokenImage) {
        imageUri = await TokenMetadataService.uploadImage(tokenImage)
        if (!imageUri) {
          console.warn("Failed to upload token image, continuing without image")
        }
      }

      // Étape 4: Créer le token
      setProgress(40)
      setCurrentStep("Creating token on blockchain...")

      const tokenResult = await fetch("/api/token/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: tokenName,
          symbol: tokenSymbol,
          decimals: Number(tokenDecimals),
          initialSupply: tokenSupply,
          maxSupply,
          isMintable,
          isBurnable,
          isPausable,
          isTransferTaxable,
          transferTaxRate: isTransferTaxable ? transferTaxRate : 0,
          suffix: tokenSuffix,
          network: activeNetwork.id,
          ownerAddress: publicKey.toString(),
          description: tokenDescription,
          website: tokenWebsite,
          twitter: tokenTwitter,
          telegram: tokenTelegram,
          paymentSignature: paymentResult.signature,
          tokenAddress,
        }),
      })

      if (!tokenResult.ok) {
        const errorData = await tokenResult.json()
        throw new Error(errorData.error || "Failed to create token")
      }

      const tokenData = await tokenResult.json()

      // Étape 5: Créer les métadonnées
      setProgress(60)
      setCurrentStep("Creating token metadata...")

      const metadata = {
        name: tokenName,
        symbol: tokenSymbol,
        description: tokenDescription || `${tokenName} token`,
        image: imageUri || undefined,
        externalUrl: tokenWebsite || undefined,
        attributes: [
          { trait_type: "Decimals", value: tokenDecimals },
          { trait_type: "Initial Supply", value: tokenSupply },
        ],
      }

      const metadataResult = await TokenMetadataService.createMetadata(
        tokenData.tokenAddress,
        metadata,
        publicKey.toString(),
      )

      if (!metadataResult.success) {
        console.warn("Metadata creation failed:", metadataResult.error)
        // Continue anyway, metadata is not critical
      }

      // Étape 6: Ajouter de la liquidité si demandé
      let pairAddress = null
      let lockId = null

      if (addLiquidity) {
        setProgress(70)
        setCurrentStep("Adding liquidity to DEX...")

        const tokenAmount = (Number(tokenSupply) * liquidityPercentage) / 100
        const liquidityResult = await fetch("/api/token/add-liquidity", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            tokenAddress: tokenData.tokenAddress,
            ownerAddress: publicKey.toString(),
            liquidityAmount: Number(liquidityAmount),
            tokenAmount,
          }),
        })

        if (!liquidityResult.ok) {
          const errorData = await liquidityResult.json()
          console.warn("Liquidity addition failed:", errorData.error)
          // Continue anyway, liquidity is not critical
        } else {
          const liquidityData = await liquidityResult.json()
          pairAddress = liquidityData.pairAddress

          // Étape 7: Verrouiller la liquidité si demandé
          if (lockLiquidity && pairAddress) {
            setProgress(80)
            setCurrentStep("Locking liquidity...")

            const lockResult = await fetch("/api/token/lock-liquidity", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                pairAddress,
                ownerAddress: publicKey.toString(),
                lockDuration,
              }),
            })

            if (!lockResult.ok) {
              const errorData = await lockResult.json()
              console.warn("Liquidity locking failed:", errorData.error)
              // Continue anyway, locking is not critical
            } else {
              const lockData = await lockResult.json()
              lockId = lockData.lockId
            }
          }
        }
      }

      // Étape 8: Configurer le premier achat si demandé
      if (setupInitialTrade) {
        setProgress(90)
        setCurrentStep("Setting up initial trade...")

        const tradeResult = await fetch("/api/token/setup-initial-trade", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            tokenAddress: tokenData.tokenAddress,
            ownerAddress: publicKey.toString(),
          }),
        })

        if (!tradeResult.ok) {
          const errorData = await tradeResult.json()
          console.warn("Initial trade setup failed:", errorData.error)
          // Continue anyway, initial trade is not critical
        }
      }

      // Étape 9: Finaliser
      setProgress(100)
      setCurrentStep("Token created successfully!")

      // Set created token with all data
      setCreatedToken({
        ...tokenData.tokenData,
        mintAddress: tokenData.tokenAddress,
        transactionId: tokenData.transactionId,
        imageUri,
        metadataUri: metadataResult.success ? metadataResult.uri : null,
        pairAddress,
        lockId,
        addedLiquidity: !!pairAddress,
        lockedLiquidity: !!lockId,
        lockDuration: lockId ? lockDuration : null,
      })

      toast({
        title: "Token created successfully",
        description: `Your token ${tokenName} (${tokenSymbol}) has been created`,
      })
    } catch (error: any) {
      console.error("Error creating token:", error)
      setError(error.message || "An error occurred while creating the token")

      toast({
        title: "Token creation failed",
        description: error.message || "An error occurred while creating the token",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "The text has been copied to your clipboard",
    })
  }

  // View on explorer
  const viewOnExplorer = (address: string) => {
    const explorerUrl = `https://explorer.solana.com/address/${address}?cluster=devnet`
    window.open(explorerUrl, "_blank")
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create Your Token</CardTitle>
        <CardDescription>Fill out the form to create your own token on {activeNetwork.name}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Token Creation Fee: {creationFee} SOL</AlertTitle>
          <AlertDescription>
            Your token address will end with the suffix "{tokenSuffix}" for easy identification.
            {walletBalance !== null && (
              <div className="mt-2">
                Your wallet balance: <span className="font-medium">{walletBalance.toFixed(6)} SOL</span>
              </div>
            )}
          </AlertDescription>
        </Alert>

        {createdToken ? (
          <div className="space-y-4">
            <Alert variant="default" className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle>Token Created Successfully</AlertTitle>
              <AlertDescription>Your token has been created on the blockchain.</AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Token Details</h3>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => viewOnExplorer(createdToken.mintAddress)}>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View on Explorer
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Token Name</Label>
                  <div className="font-medium">{createdToken.name}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Token Symbol</Label>
                  <div className="font-medium">{createdToken.symbol}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Decimals</Label>
                  <div className="font-medium">{createdToken.decimals}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Initial Supply</Label>
                  <div className="font-medium">{createdToken.initialSupply.toLocaleString()}</div>
                </div>
              </div>

              {createdToken.imageUri && (
                <div className="flex justify-center py-4">
                  <div className="w-32 h-32 rounded-full overflow-hidden border">
                    <img
                      src={createdToken.imageUri || "/placeholder.svg"}
                      alt={createdToken.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label className="text-muted-foreground">Token Address</Label>
                <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                  <code className="text-xs break-all">{createdToken.mintAddress}</code>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.mintAddress)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Transaction ID</Label>
                <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                  <code className="text-xs break-all">{createdToken.transactionId}</code>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.transactionId)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Features</Label>
                <div className="flex flex-wrap gap-2">
                  {createdToken.isMintable && <Badge variant="outline">Mintable</Badge>}
                  {createdToken.isBurnable && <Badge variant="outline">Burnable</Badge>}
                  {createdToken.isPausable && <Badge variant="outline">Pausable</Badge>}
                  {createdToken.isTransferTaxable && (
                    <Badge variant="outline">Transfer Tax ({createdToken.transferTaxRate}%)</Badge>
                  )}
                  {createdToken.addedLiquidity && <Badge variant="outline">Liquidity Added</Badge>}
                  {createdToken.lockedLiquidity && (
                    <Badge variant="outline">Liquidity Locked ({createdToken.lockDuration} days)</Badge>
                  )}
                </div>
              </div>

              {createdToken.pairAddress && (
                <div className="space-y-2">
                  <Label className="text-muted-foreground">Liquidity Pair Address</Label>
                  <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                    <code className="text-xs break-all">{createdToken.pairAddress}</code>
                    <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.pairAddress)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label className="text-muted-foreground">Next Steps</Label>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <ArrowRight className="h-4 w-4 text-green-600" />
                    <span>Add more liquidity to your token on DEX</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ArrowRight className="h-4 w-4 text-green-600" />
                    <span>Create a website for your token</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ArrowRight className="h-4 w-4 text-green-600" />
                    <span>Promote your token on social media</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button onClick={() => setCreatedToken(null)}>Create Another Token</Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {isCreating ? (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-medium">{currentStep}</h3>
                  <p className="text-sm text-muted-foreground">Please wait while we create your token...</p>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            ) : (
              <>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-3 mb-4">
                    <TabsTrigger value="basic">Basic Information</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced Features</TabsTrigger>
                    <TabsTrigger value="liquidity">Liquidity & DEX</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid gap-2">
                      <Label htmlFor="tokenName">Token Name</Label>
                      <Input
                        id="tokenName"
                        value={tokenName}
                        onChange={(e) => setTokenName(e.target.value)}
                        placeholder="My Token"
                        required
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="tokenSymbol">Token Symbol</Label>
                      <Input
                        id="tokenSymbol"
                        value={tokenSymbol}
                        onChange={(e) => setTokenSymbol(e.target.value)}
                        placeholder="MTK"
                        required
                      />
                      <p className="text-xs text-muted-foreground">
                        Your token address will end with the suffix "{tokenSuffix}"
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="tokenDecimals">Decimals</Label>
                        <Input
                          id="tokenDecimals"
                          type="number"
                          value={tokenDecimals}
                          onChange={(e) => setTokenDecimals(e.target.value)}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="tokenSupply">Initial Supply</Label>
                        <Input
                          id="tokenSupply"
                          type="number"
                          value={tokenSupply}
                          onChange={(e) => setTokenSupply(e.target.value)}
                          min="1"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxSupply">Maximum Supply</Label>
                      <Input
                        id="maxSupply"
                        type="number"
                        value={maxSupply}
                        onChange={(e) => setMaxSupply(e.target.value)}
                        min={tokenSupply}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenDescription">Description (Optional)</Label>
                      <Textarea
                        id="tokenDescription"
                        value={tokenDescription}
                        onChange={(e) => setTokenDescription(e.target.value)}
                        placeholder="Describe your token..."
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenImage">Token Image (Optional)</Label>
                      <div className="flex items-center gap-4">
                        <div
                          className="w-24 h-24 border rounded-lg flex items-center justify-center cursor-pointer overflow-hidden"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          {tokenImagePreview ? (
                            <img
                              src={tokenImagePreview || "/placeholder.svg"}
                              alt="Token"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <ImageIcon className="h-8 w-8 text-muted-foreground" />
                          )}
                        </div>
                        <div className="flex-1">
                          <Input
                            id="tokenImage"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            ref={fileInputRef}
                            onChange={handleImageUpload}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                            className="w-full"
                          >
                            <Upload className="mr-2 h-4 w-4" />
                            Upload Image
                          </Button>
                          <p className="text-xs text-muted-foreground mt-2">
                            Recommended: 512x512px PNG or JPG. Max 5MB.
                          </p>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="advanced" className="space-y-4">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isMintable"
                          checked={isMintable}
                          onCheckedChange={(checked) => setIsMintable(!!checked)}
                        />
                        <Label htmlFor="isMintable">Mintable (allows creating more tokens later)</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isBurnable"
                          checked={isBurnable}
                          onCheckedChange={(checked) => setIsBurnable(!!checked)}
                        />
                        <Label htmlFor="isBurnable">Burnable (allows destroying tokens)</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isPausable"
                          checked={isPausable}
                          onCheckedChange={(checked) => setIsPausable(!!checked)}
                        />
                        <Label htmlFor="isPausable">Pausable (allows freezing all transfers)</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isTransferTaxable"
                          checked={isTransferTaxable}
                          onCheckedChange={(checked) => setIsTransferTaxable(!!checked)}
                        />
                        <Label htmlFor="isTransferTaxable">Transfer Tax (fee on each transfer)</Label>
                      </div>

                      {isTransferTaxable && (
                        <div className="pl-6 space-y-2">
                          <Label htmlFor="transferTaxRate">Tax Rate (%)</Label>
                          <Input
                            id="transferTaxRate"
                            value={transferTaxRate}
                            onChange={(e) => setTransferTaxRate(Number.parseFloat(e.target.value))}
                            type="number"
                            min="0"
                            max="10"
                            step="0.1"
                          />
                        </div>
                      )}
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <Label htmlFor="tokenWebsite">Website (Optional)</Label>
                      <Input
                        id="tokenWebsite"
                        value={tokenWebsite}
                        onChange={(e) => setTokenWebsite(e.target.value)}
                        placeholder="https://example.com"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenTwitter">Twitter (Optional)</Label>
                      <Input
                        id="tokenTwitter"
                        value={tokenTwitter}
                        onChange={(e) => setTokenTwitter(e.target.value)}
                        placeholder="https://twitter.com/example"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenTelegram">Telegram (Optional)</Label>
                      <Input
                        id="tokenTelegram"
                        value={tokenTelegram}
                        onChange={(e) => setTokenTelegram(e.target.value)}
                        placeholder="https://t.me/example"
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="liquidity" className="space-y-4">
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>DEX Integration</AlertTitle>
                      <AlertDescription>
                        Adding liquidity and setting up initial trades helps your token get discovered on DEXs.
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="addLiquidity"
                          checked={addLiquidity}
                          onCheckedChange={(checked) => setAddLiquidity(!!checked)}
                        />
                        <Label htmlFor="addLiquidity">Add Initial Liquidity</Label>
                      </div>

                      {addLiquidity && (
                        <div className="pl-6 space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="liquidityAmount">
                              Liquidity Amount ({activeNetwork.nativeCurrency.symbol})
                            </Label>
                            <Input
                              id="liquidityAmount"
                              type="number"
                              value={liquidityAmount}
                              onChange={(e) => setLiquidityAmount(e.target.value)}
                              min="0.01"
                              step="0.01"
                            />
                            <p className="text-xs text-muted-foreground">
                              This amount will be deducted from your wallet to create the liquidity pool.
                            </p>
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <Label htmlFor="liquidityPercentage">Token Percentage for Liquidity</Label>
                              <span className="text-sm">{liquidityPercentage}%</span>
                            </div>
                            <Slider
                              id="liquidityPercentage"
                              defaultValue={[liquidityPercentage]}
                              max={100}
                              step={1}
                              onValueChange={(value) => setLiquidityPercentage(value[0])}
                            />
                            <p className="text-xs text-muted-foreground">
                              {liquidityPercentage}% of your tokens will be used for the liquidity pool.
                            </p>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Switch
                              id="lockLiquidity"
                              checked={lockLiquidity}
                              onCheckedChange={(checked) => setLockLiquidity(!!checked)}
                            />
                            <Label htmlFor="lockLiquidity">Lock Liquidity</Label>
                          </div>

                          {lockLiquidity && (
                            <div className="pl-6 space-y-2">
                              <Label htmlFor="lockDuration">Lock Duration (Days)</Label>
                              <Input
                                id="lockDuration"
                                type="number"
                                value={lockDuration}
                                onChange={(e) => setLockDuration(Number.parseInt(e.target.value))}
                                min="30"
                                max="365"
                              />
                              <p className="text-xs text-muted-foreground">
                                Locking liquidity builds trust with your community.
                              </p>
                            </div>
                          )}
                        </div>
                      )}

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="setupInitialTrade"
                          checked={setupInitialTrade}
                          onCheckedChange={(checked) => setSetupInitialTrade(!!checked)}
                        />
                        <Label htmlFor="setupInitialTrade">Setup Initial Trade</Label>
                      </div>

                      {setupInitialTrade && (
                        <div className="pl-6">
                          <p className="text-xs text-muted-foreground">
                            We'll execute a small trade to help your token appear in DEX listings.
                          </p>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-muted-foreground">
                    {addLiquidity && (
                      <div className="flex items-center gap-2">
                        <Droplet className="h-4 w-4" />
                        <span>
                          Liquidity: {liquidityAmount} {activeNetwork.nativeCurrency.symbol}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {addLiquidity && lockLiquidity && (
                      <div className="flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        <span>Locked for {lockDuration} days</span>
                      </div>
                    )}
                  </div>
                </div>

                <Button type="button" onClick={createToken} disabled={isCreating || !connected} className="w-full mt-4">
                  {isCreating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Token...
                    </>
                  ) : (
                    <>
                      <Rocket className="mr-2 h-4 w-4" />
                      Create Token
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {!isCreating && !createdToken && (
          <div className="text-sm text-muted-foreground">
            {connected ? (
              <>
                Connected: {publicKey?.toString().slice(0, 6)}...{publicKey?.toString().slice(-4)}
                {walletBalance !== null && <> | Balance: {walletBalance.toFixed(4)} SOL</>}
              </>
            ) : (
              "Please connect your wallet to create a token"
            )}
          </div>
        )}
      </CardFooter>
    </Card>
  )
}

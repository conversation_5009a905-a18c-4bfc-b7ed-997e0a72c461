"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { <PERSON>lider } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Flame, Sparkles, Zap, Lock, BarChart3, AlertTriangle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface AdvancedFeaturesConfigurationProps {
  initialValues?: {
    autoBurn: {
      enabled: boolean
      percentage: number
      interval: number
    }
    autoLiquidity: {
      enabled: boolean
      percentage: number
      interval: number
    }
    rebase: {
      enabled: boolean
      targetPrice: number
      interval: number
    }
    antiBot: {
      enabled: boolean
      duration: number
    }
  }
  onChange?: (values: any) => void
}

export function AdvancedFeaturesConfiguration({ initialValues, onChange }: AdvancedFeaturesConfigurationProps) {
  const [autoBurn, setAutoBurn] = useState({
    enabled: initialValues?.autoBurn?.enabled || false,
    percentage: initialValues?.autoBurn?.percentage || 1,
    interval: initialValues?.autoBurn?.interval || 24,
  })
  const [autoLiquidity, setAutoLiquidity] = useState({
    enabled: initialValues?.autoLiquidity?.enabled || false,
    percentage: initialValues?.autoLiquidity?.percentage || 1,
    interval: initialValues?.autoLiquidity?.interval || 24,
  })
  const [rebase, setRebase] = useState({
    enabled: initialValues?.rebase?.enabled || false,
    targetPrice: initialValues?.rebase?.targetPrice || 0.01,
    interval: initialValues?.rebase?.interval || 24,
  })
  const [antiBot, setAntiBot] = useState({
    enabled: initialValues?.antiBot?.enabled || false,
    duration: initialValues?.antiBot?.duration || 60,
  })

  // Mettre à jour le parent lorsque les valeurs changent
  const updateValues = (newValues: any) => {
    if (onChange) {
      onChange({
        autoBurn,
        autoLiquidity,
        rebase,
        antiBot,
        ...newValues,
      })
    }
  }

  // Mettre à jour l'auto-burn
  const updateAutoBurn = (field: keyof typeof autoBurn, value: any) => {
    const newAutoBurn = { ...autoBurn, [field]: value }
    setAutoBurn(newAutoBurn)
    updateValues({ autoBurn: newAutoBurn })
  }

  // Mettre à jour l'auto-liquidité
  const updateAutoLiquidity = (field: keyof typeof autoLiquidity, value: any) => {
    const newAutoLiquidity = { ...autoLiquidity, [field]: value }
    setAutoLiquidity(newAutoLiquidity)
    updateValues({ autoLiquidity: newAutoLiquidity })
  }

  // Mettre à jour le rebase
  const updateRebase = (field: keyof typeof rebase, value: any) => {
    const newRebase = { ...rebase, [field]: value }
    setRebase(newRebase)
    updateValues({ rebase: newRebase })
  }

  // Mettre à jour l'anti-bot
  const updateAntiBot = (field: keyof typeof antiBot, value: any) => {
    const newAntiBot = { ...antiBot, [field]: value }
    setAntiBot(newAntiBot)
    updateValues({ antiBot: newAntiBot })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5" />
          Fonctionnalités avancées
        </CardTitle>
        <CardDescription>Configurez des mécanismes avancés pour votre token</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Ces fonctionnalités avancées peuvent augmenter la complexité et le coût de déploiement de votre token.
            Assurez-vous de bien comprendre leur fonctionnement avant de les activer.
          </AlertDescription>
        </Alert>

        <Tabs defaultValue="burn" className="space-y-4">
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="burn">Auto-Burn</TabsTrigger>
            <TabsTrigger value="liquidity">Auto-Liquidité</TabsTrigger>
            <TabsTrigger value="rebase">Rebase</TabsTrigger>
            <TabsTrigger value="antibot">Anti-Bot</TabsTrigger>
          </TabsList>

          <TabsContent value="burn" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-2">
                <Flame className="h-5 w-5 mt-0.5 text-orange-500" />
                <div>
                  <h3 className="font-medium">Auto-Burn</h3>
                  <p className="text-sm text-muted-foreground">
                    Brûle automatiquement une partie des tokens à intervalles réguliers
                  </p>
                </div>
              </div>
              <Switch checked={autoBurn.enabled} onCheckedChange={(checked) => updateAutoBurn("enabled", checked)} />
            </div>

            {autoBurn.enabled && (
              <div className="space-y-4 pl-6 border-l-2 border-orange-100">
                <div>
                  <div className="flex justify-between mb-1">
                    <Label htmlFor="burn-percentage">Pourcentage à brûler</Label>
                    <span className="text-sm">{autoBurn.percentage}% de l'offre circulante</span>
                  </div>
                  <Slider
                    id="burn-percentage"
                    value={[autoBurn.percentage]}
                    min={0.1}
                    max={5}
                    step={0.1}
                    onValueChange={(values) => updateAutoBurn("percentage", values[0])}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Pourcentage de l'offre circulante à brûler à chaque intervalle
                  </p>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <Label htmlFor="burn-interval">Intervalle de temps</Label>
                    <span className="text-sm">{autoBurn.interval} heures</span>
                  </div>
                  <Slider
                    id="burn-interval"
                    value={[autoBurn.interval]}
                    min={1}
                    max={168}
                    step={1}
                    onValueChange={(values) => updateAutoBurn("interval", values[0])}
                  />
                  <p className="text-xs text-muted-foreground mt-1">Fréquence à laquelle les tokens seront brûlés</p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="liquidity" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-2">
                <Lock className="h-5 w-5 mt-0.5 text-blue-500" />
                <div>
                  <h3 className="font-medium">Auto-Liquidité</h3>
                  <p className="text-sm text-muted-foreground">
                    Ajoute automatiquement de la liquidité au pool à intervalles réguliers
                  </p>
                </div>
              </div>
              <Switch
                checked={autoLiquidity.enabled}
                onCheckedChange={(checked) => updateAutoLiquidity("enabled", checked)}
              />
            </div>

            {autoLiquidity.enabled && (
              <div className="space-y-4 pl-6 border-l-2 border-blue-100">
                <div>
                  <div className="flex justify-between mb-1">
                    <Label htmlFor="liquidity-percentage">Pourcentage à ajouter</Label>
                    <span className="text-sm">{autoLiquidity.percentage}% des taxes collectées</span>
                  </div>
                  <Slider
                    id="liquidity-percentage"
                    value={[autoLiquidity.percentage]}
                    min={1}
                    max={100}
                    step={1}
                    onValueChange={(values) => updateAutoLiquidity("percentage", values[0])}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Pourcentage des taxes collectées à ajouter à la liquidité
                  </p>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <Label htmlFor="liquidity-interval">Intervalle de temps</Label>
                    <span className="text-sm">{autoLiquidity.interval} heures</span>
                  </div>
                  <Slider
                    id="liquidity-interval"
                    value={[autoLiquidity.interval]}
                    min={1}
                    max={168}
                    step={1}
                    onValueChange={(values) => updateAutoLiquidity("interval", values[0])}
                  />
                  <p className="text-xs text-muted-foreground mt-1">Fréquence à laquelle la liquidité sera ajoutée</p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="rebase" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-2">
                <BarChart3 className="h-5 w-5 mt-0.5 text-purple-500" />
                <div>
                  <h3 className="font-medium">Rebase</h3>
                  <p className="text-sm text-muted-foreground">
                    Ajuste automatiquement l'offre pour maintenir un prix cible
                  </p>
                </div>
              </div>
              <Switch checked={rebase.enabled} onCheckedChange={(checked) => updateRebase("enabled", checked)} />
            </div>

            {rebase.enabled && (
              <div className="space-y-4 pl-6 border-l-2 border-purple-100">
                <div>
                  <Label htmlFor="rebase-target">Prix cible (SOL)</Label>
                  <Input
                    id="rebase-target"
                    type="number"
                    value={rebase.targetPrice}
                    onChange={(e) => updateRebase("targetPrice", Number(e.target.value))}
                    min={0.000001}
                    step={0.000001}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Prix cible que le mécanisme de rebase essaiera de maintenir
                  </p>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <Label htmlFor="rebase-interval">Intervalle de temps</Label>
                    <span className="text-sm">{rebase.interval} heures</span>
                  </div>
                  <Slider
                    id="rebase-interval"
                    value={[rebase.interval]}
                    min={1}
                    max={168}
                    step={1}
                    onValueChange={(values) => updateRebase("interval", values[0])}
                  />
                  <p className="text-xs text-muted-foreground mt-1">Fréquence à laquelle le rebase sera effectué</p>
                </div>

                <Alert variant="warning">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Le rebase est une fonctionnalité complexe qui peut être difficile à comprendre pour les
                    utilisateurs. Assurez-vous de bien communiquer son fonctionnement.
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </TabsContent>

          <TabsContent value="antibot" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-2">
                <Zap className="h-5 w-5 mt-0.5 text-yellow-500" />
                <div>
                  <h3 className="font-medium">Protection Anti-Bot</h3>
                  <p className="text-sm text-muted-foreground">Protège le lancement contre les bots de trading</p>
                </div>
              </div>
              <Switch checked={antiBot.enabled} onCheckedChange={(checked) => updateAntiBot("enabled", checked)} />
            </div>

            {antiBot.enabled && (
              <div className="space-y-4 pl-6 border-l-2 border-yellow-100">
                <div>
                  <div className="flex justify-between mb-1">
                    <Label htmlFor="antibot-duration">Durée de protection</Label>
                    <span className="text-sm">{antiBot.duration} minutes</span>
                  </div>
                  <Slider
                    id="antibot-duration"
                    value={[antiBot.duration]}
                    min={1}
                    max={180}
                    step={1}
                    onValueChange={(values) => updateAntiBot("duration", values[0])}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Durée pendant laquelle la protection anti-bot sera active après le lancement
                  </p>
                </div>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    La protection anti-bot limite les transactions pendant la période spécifiée pour empêcher les bots
                    d'acheter de grandes quantités de tokens au lancement.
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

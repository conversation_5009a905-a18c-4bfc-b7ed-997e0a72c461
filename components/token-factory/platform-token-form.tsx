"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useWallet } from "@solana/wallet-adapter-react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Stepper, Step, StepTitle, StepDescription } from "@/components/ui/stepper"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, <PERSON><PERSON><PERSON>ircle, Check<PERSON>ircle2, Info } from "lucide-react"
import { TokenImageSelector } from "./token-image-selector"
import { TokenPreview } from "./token-preview"

// Schéma de validation pour le formulaire
const tokenFormSchema = z.object({
  // Informations de base
  name: z.string().min(3, { message: "Le nom doit contenir au moins 3 caractères" }),
  symbol: z.string().min(2, { message: "Le symbole doit contenir au moins 2 caractères" }).max(10),
  description: z.string().optional(),
  decimals: z.number().int().min(0).max(9),
  initialSupply: z.number().positive(),
  maxSupply: z.number().positive(),
  logo: z.any().optional(),
  suffix: z.string().optional(),

  // Distribution
  teamPercentage: z.number().min(0).max(100),
  marketingPercentage: z.number().min(0).max(100),
  developmentPercentage: z.number().min(0).max(100),
  reservePercentage: z.number().min(0).max(100),
  liquidityPercentage: z.number().min(0).max(100),

  // Sécurité
  antiBot: z.boolean().default(true),
  antiDump: z.boolean().default(true),
  maxTxPercentage: z.number().min(0).max(100),
  maxWalletPercentage: z.number().min(0).max(100),
  tradingDelay: z.number().min(0),

  // Lancement
  addLiquidity: z.boolean().default(false),
  liquidityAmount: z.number().min(0),
  lockLiquidity: z.boolean().default(false),
  lockDuration: z.number().min(0),

  // Marketing
  website: z.string().url().optional().or(z.literal("")),
  twitter: z.string().url().optional().or(z.literal("")),
  telegram: z.string().url().optional().or(z.literal("")),
  discord: z.string().url().optional().or(z.literal("")),
})

type TokenFormValues = z.infer<typeof tokenFormSchema>

export function PlatformTokenForm() {
  const router = useRouter()
  const { publicKey, signTransaction, connected } = useWallet()
  const { toast } = useToast()
  const [activeStep, setActiveStep] = useState(0)
  const [isCreating, setIsCreating] = useState(false)
  const [creationProgress, setCreationProgress] = useState(0)
  const [creationStep, setCreationStep] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [createdTokenAddress, setCreatedTokenAddress] = useState<string | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const [paymentSignature, setPaymentSignature] = useState<string | null>(null)

  // Initialiser le formulaire avec des valeurs par défaut
  const form = useForm<TokenFormValues>({
    resolver: zodResolver(tokenFormSchema),
    defaultValues: {
      name: "",
      symbol: "",
      description: "",
      decimals: 9,
      initialSupply: 1000000000,
      maxSupply: 10000000000,
      suffix: "GFMM",
      teamPercentage: 15,
      marketingPercentage: 10,
      developmentPercentage: 15,
      reservePercentage: 10,
      liquidityPercentage: 50,
      antiBot: true,
      antiDump: true,
      maxTxPercentage: 1,
      maxWalletPercentage: 3,
      tradingDelay: 0,
      addLiquidity: false,
      liquidityAmount: 0.1,
      lockLiquidity: false,
      lockDuration: 180,
      website: "",
      twitter: "",
      telegram: "",
      discord: "",
    },
  })

  // Étapes du formulaire
  const steps = [
    {
      id: "basic",
      title: "Informations de base",
      description: "Nom, symbole et offre",
    },
    {
      id: "distribution",
      title: "Distribution",
      description: "Répartition des tokens",
    },
    {
      id: "security",
      title: "Sécurité",
      description: "Protections et limites",
    },
    {
      id: "launch",
      title: "Lancement",
      description: "Liquidité et listing",
    },
    {
      id: "marketing",
      title: "Marketing",
      description: "Présence en ligne",
    },
  ]

  // Gérer le changement d'étape
  const nextStep = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1)
    }
  }

  const prevStep = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1)
    }
  }

  // Gérer le changement de logo
  const handleLogoChange = (file: File | null, preview: string | null) => {
    form.setValue("logo", file)
    setLogoPreview(preview)
  }

  // Créer le token
  const handleCreateToken = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour créer un token",
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setCreationProgress(0)
    setCreationStep("Préparation de la création du token...")
    setError(null)

    try {
      const values = form.getValues()

      // Simuler un paiement
      setPaymentSignature("simulated_payment_signature")

      // Préparer les données pour l'API
      const tokenData = {
        name: values.name,
        symbol: values.symbol,
        decimals: values.decimals,
        initialSupply: values.initialSupply,
        maxSupply: values.maxSupply,
        description: values.description,
        website: values.website,
        twitter: values.twitter,
        telegram: values.telegram,
        discord: values.discord,
        imageUrl: logoPreview,
        suffix: values.suffix,
        ownerAddress: publicKey.toString(),
        paymentSignature: "simulated_payment_signature",
        distribution: {
          team: values.teamPercentage,
          marketing: values.marketingPercentage,
          development: values.developmentPercentage,
          reserve: values.reservePercentage,
          liquidity: values.liquidityPercentage,
        },
        securityOptions: {
          antiBot: values.antiBot,
          antiDump: values.antiDump,
          maxTxPercentage: values.maxTxPercentage,
          maxWalletPercentage: values.maxWalletPercentage,
          tradingDelay: values.tradingDelay,
        },
        launchOptions: {
          addLiquidity: values.addLiquidity,
          liquidityAmount: values.liquidityAmount,
          liquidityPercentage: values.liquidityPercentage,
          lockLiquidity: values.lockLiquidity,
          lockDuration: values.lockDuration,
        },
      }

      // Simuler la progression de la création
      const interval = setInterval(() => {
        setCreationProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval)
            return 100
          }
          return prev + 5
        })

        if (creationProgress < 20) {
          setCreationStep("Génération de l'adresse avec suffixe...")
        } else if (creationProgress < 40) {
          setCreationStep("Création du token sur la blockchain...")
        } else if (creationProgress < 60) {
          setCreationStep("Configuration des mécanismes de sécurité...")
        } else if (creationProgress < 80) {
          setCreationStep("Configuration de la distribution et du vesting...")
        } else {
          setCreationStep("Finalisation de la création du token...")
        }
      }, 200)

      // Simuler un appel API
      setTimeout(() => {
        clearInterval(interval)
        setCreationProgress(100)
        setCreationStep("Token créé avec succès!")
        setSuccess(true)
        setCreatedTokenAddress(`${publicKey.toString().slice(0, 8)}...${values.suffix}`)

        toast({
          title: "Token créé avec succès",
          description: `Votre token ${values.name} (${values.symbol}${values.suffix}) a été créé avec succès!`,
        })

        setIsCreating(false)
      }, 5000)
    } catch (err: any) {
      console.error("Erreur lors de la création du token:", err)
      setError(err.message || "Une erreur s'est produite lors de la création du token")
      setIsCreating(false)

      toast({
        title: "Échec de la création du token",
        description: err.message || "Une erreur s'est produite lors de la création du token",
        variant: "destructive",
      })
    }
  }

  // Gérer la soumission du formulaire
  const onSubmit = async (data: TokenFormValues) => {
    // Vérifier que la somme des pourcentages de distribution est égale à 100%
    const totalPercentage =
      data.teamPercentage +
      data.marketingPercentage +
      data.developmentPercentage +
      data.reservePercentage +
      data.liquidityPercentage

    if (totalPercentage !== 100) {
      toast({
        title: "Erreur de distribution",
        description: `La somme des pourcentages de distribution doit être égale à 100% (actuellement ${totalPercentage}%)`,
        variant: "destructive",
      })
      return
    }

    await handleCreateToken()
  }

  // Si le token est en cours de création, afficher la progression
  if (isCreating) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Création du token en cours</CardTitle>
          <CardDescription>Veuillez patienter pendant que nous créons votre token</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium">{creationStep}</h3>
            <Progress value={creationProgress} className="h-2 mt-2" />
          </div>

          <div className="bg-muted p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <h4 className="font-medium">Détails du token</h4>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Nom:</span> {form.getValues("name")}
              </div>
              <div>
                <span className="text-muted-foreground">Symbole:</span> {form.getValues("symbol")}
                {form.getValues("suffix")}
              </div>
              <div>
                <span className="text-muted-foreground">Décimales:</span> {form.getValues("decimals")}
              </div>
              <div>
                <span className="text-muted-foreground">Offre initiale:</span>{" "}
                {form.getValues("initialSupply").toLocaleString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Si le token a été créé avec succès, afficher les détails
  if (success && createdTokenAddress) {
    return (
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-6 w-6 text-green-500" />
            <CardTitle>Token créé avec succès</CardTitle>
          </div>
          <CardDescription>Votre token a été créé sur la blockchain Solana</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle>Félicitations!</AlertTitle>
            <AlertDescription>
              Votre token {form.getValues("name")} ({form.getValues("symbol")}
              {form.getValues("suffix")}) a été créé avec succès. Vous pouvez maintenant le gérer et le promouvoir.
            </AlertDescription>
          </Alert>

          <TokenPreview
            name={form.getValues("name")}
            symbol={form.getValues("symbol") + form.getValues("suffix")}
            logoUrl={logoPreview}
            address={createdTokenAddress}
            supply={form.getValues("initialSupply")}
            decimals={form.getValues("decimals")}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="default"
              onClick={() => {
                window.open(`https://explorer.solana.com/address/${createdTokenAddress}?cluster=devnet`, "_blank")
              }}
            >
              Voir sur l'explorateur
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                router.push("/token-factory/tokens")
              }}
            >
              Voir tous mes tokens
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <Button
            className="w-full"
            onClick={() => {
              form.reset()
              setLogoPreview(null)
              setSuccess(false)
              setCreatedTokenAddress(null)
              setActiveStep(0)
              setPaymentSignature(null)
            }}
          >
            Créer un autre token
          </Button>
        </CardFooter>
      </Card>
    )
  }

  // Afficher le formulaire de création de token
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Créer un token avancé</CardTitle>
        <CardDescription>Configurez tous les aspects de votre token avec notre plateforme avancée</CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erreur</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Stepper currentStep={activeStep} className="mb-8">
            {steps.map((step, index) => (
              <Step key={index} onClick={() => setActiveStep(index)}>
                <StepTitle>{step.title}</StepTitle>
                <StepDescription>{step.description}</StepDescription>
              </Step>
            ))}
          </Stepper>

          <div className="mt-8">
            {/* Étape 1: Informations de base */}
            {activeStep === 0 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nom du token</Label>
                    <Input
                      id="name"
                      placeholder="Mon Super Token"
                      {...form.register("name")}
                      error={form.formState.errors.name?.message}
                    />
                    {form.formState.errors.name && (
                      <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="symbol">Symbole du token</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="symbol"
                        placeholder="MST"
                        {...form.register("symbol")}
                        error={form.formState.errors.symbol?.message}
                      />
                      <div className="flex-shrink-0 w-24">
                        <Input
                          id="suffix"
                          placeholder="GFMM"
                          {...form.register("suffix")}
                          error={form.formState.errors.suffix?.message}
                        />
                      </div>
                    </div>
                    {form.formState.errors.symbol && (
                      <p className="text-sm text-red-500">{form.formState.errors.symbol.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Une description de votre token et de son utilité..."
                    {...form.register("description")}
                    error={form.formState.errors.description?.message}
                  />
                  {form.formState.errors.description && (
                    <p className="text-sm text-red-500">{form.formState.errors.description.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="decimals">Décimales</Label>
                    <Input
                      id="decimals"
                      type="number"
                      min="0"
                      max="9"
                      {...form.register("decimals", { valueAsNumber: true })}
                      error={form.formState.errors.decimals?.message}
                    />
                    {form.formState.errors.decimals && (
                      <p className="text-sm text-red-500">{form.formState.errors.decimals.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="initialSupply">Offre initiale</Label>
                    <Input
                      id="initialSupply"
                      type="number"
                      min="1"
                      {...form.register("initialSupply", { valueAsNumber: true })}
                      error={form.formState.errors.initialSupply?.message}
                    />
                    {form.formState.errors.initialSupply && (
                      <p className="text-sm text-red-500">{form.formState.errors.initialSupply.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxSupply">Offre maximale</Label>
                    <Input
                      id="maxSupply"
                      type="number"
                      min="1"
                      {...form.register("maxSupply", { valueAsNumber: true })}
                      error={form.formState.errors.maxSupply?.message}
                    />
                    {form.formState.errors.maxSupply && (
                      <p className="text-sm text-red-500">{form.formState.errors.maxSupply.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Logo du token</Label>
                  <TokenImageSelector onImageChange={handleLogoChange} previewUrl={logoPreview} />
                </div>
              </div>
            )}

            {/* Étape 2: Distribution */}
            {activeStep === 1 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label>Distribution des tokens</Label>
                  <p className="text-sm text-muted-foreground">
                    Définissez comment les tokens seront distribués initialement. La somme doit être égale à 100%.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="teamPercentage">Équipe</Label>
                      <span className="text-sm">{form.watch("teamPercentage")}%</span>
                    </div>
                    <Slider
                      id="teamPercentage"
                      min={0}
                      max={100}
                      step={1}
                      value={[form.watch("teamPercentage")]}
                      onValueChange={(value) => form.setValue("teamPercentage", value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="marketingPercentage">Marketing</Label>
                      <span className="text-sm">{form.watch("marketingPercentage")}%</span>
                    </div>
                    <Slider
                      id="marketingPercentage"
                      min={0}
                      max={100}
                      step={1}
                      value={[form.watch("marketingPercentage")]}
                      onValueChange={(value) => form.setValue("marketingPercentage", value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="developmentPercentage">Développement</Label>
                      <span className="text-sm">{form.watch("developmentPercentage")}%</span>
                    </div>
                    <Slider
                      id="developmentPercentage"
                      min={0}
                      max={100}
                      step={1}
                      value={[form.watch("developmentPercentage")]}
                      onValueChange={(value) => form.setValue("developmentPercentage", value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="reservePercentage">Réserve</Label>
                      <span className="text-sm">{form.watch("reservePercentage")}%</span>
                    </div>
                    <Slider
                      id="reservePercentage"
                      min={0}
                      max={100}
                      step={1}
                      value={[form.watch("reservePercentage")]}
                      onValueChange={(value) => form.setValue("reservePercentage", value[0])}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="liquidityPercentage">Liquidité</Label>
                      <span className="text-sm">{form.watch("liquidityPercentage")}%</span>
                    </div>
                    <Slider
                      id="liquidityPercentage"
                      min={0}
                      max={100}
                      step={1}
                      value={[form.watch("liquidityPercentage")]}
                      onValueChange={(value) => form.setValue("liquidityPercentage", value[0])}
                    />
                  </div>
                </div>

                <div className="flex justify-between items-center p-4 bg-muted rounded-lg">
                  <span>Total</span>
                  <span className="font-bold">
                    {form.watch("teamPercentage") +
                      form.watch("marketingPercentage") +
                      form.watch("developmentPercentage") +
                      form.watch("reservePercentage") +
                      form.watch("liquidityPercentage")}
                    %
                  </span>
                </div>
              </div>
            )}

            {/* Étape 3: Sécurité */}
            {activeStep === 2 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label>Mécanismes de sécurité</Label>
                  <p className="text-sm text-muted-foreground">
                    Configurez les protections et les limites pour votre token.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="antiBot">Protection anti-bot</Label>
                      <p className="text-sm text-muted-foreground">
                        Limite le nombre de transactions par bloc pour éviter les bots
                      </p>
                    </div>
                    <Switch
                      id="antiBot"
                      checked={form.watch("antiBot")}
                      onCheckedChange={(value) => form.setValue("antiBot", value)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="antiDump">Protection anti-dump</Label>
                      <p className="text-sm text-muted-foreground">
                        Applique une taxe supplémentaire sur les ventes importantes pour décourager les dumps
                      </p>
                    </div>
                    <Switch
                      id="antiDump"
                      checked={form.watch("antiDump")}
                      onCheckedChange={(value) => form.setValue("antiDump", value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="maxTxPercentage">Limite de transaction maximale</Label>
                      <span className="text-sm">{form.watch("maxTxPercentage")}% de l'offre totale</span>
                    </div>
                    <Slider
                      id="maxTxPercentage"
                      min={0}
                      max={10}
                      step={0.1}
                      value={[form.watch("maxTxPercentage")]}
                      onValueChange={(value) => form.setValue("maxTxPercentage", value[0])}
                    />
                    <p className="text-sm text-muted-foreground">Limite la taille maximale d'une transaction unique</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="maxWalletPercentage">Limite de wallet maximale</Label>
                      <span className="text-sm">{form.watch("maxWalletPercentage")}% de l'offre totale</span>
                    </div>
                    <Slider
                      id="maxWalletPercentage"
                      min={0}
                      max={10}
                      step={0.1}
                      value={[form.watch("maxWalletPercentage")]}
                      onValueChange={(value) => form.setValue("maxWalletPercentage", value[0])}
                    />
                    <p className="text-sm text-muted-foreground">
                      Limite la quantité maximale de tokens qu'un wallet peut détenir
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tradingDelay">Délai avant trading (secondes)</Label>
                    <Input
                      id="tradingDelay"
                      type="number"
                      min="0"
                      {...form.register("tradingDelay", { valueAsNumber: true })}
                      error={form.formState.errors.tradingDelay?.message}
                    />
                    <p className="text-sm text-muted-foreground">
                      Délai avant que le trading ne soit activé après la création du token (0 = pas de délai)
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Étape 4: Lancement */}
            {activeStep === 3 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label>Options de lancement</Label>
                  <p className="text-sm text-muted-foreground">
                    Configurez les options de liquidité et de listing pour votre token.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="addLiquidity">Ajouter de la liquidité</Label>
                      <p className="text-sm text-muted-foreground">
                        Créer automatiquement un pool de liquidité sur un DEX
                      </p>
                    </div>
                    <Switch
                      id="addLiquidity"
                      checked={form.watch("addLiquidity")}
                      onCheckedChange={(value) => form.setValue("addLiquidity", value)}
                    />
                  </div>

                  {form.watch("addLiquidity") && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="liquidityAmount">Montant de liquidité (SOL)</Label>
                        <Input
                          id="liquidityAmount"
                          type="number"
                          min="0.01"
                          step="0.01"
                          {...form.register("liquidityAmount", { valueAsNumber: true })}
                          error={form.formState.errors.liquidityAmount?.message}
                        />
                        <p className="text-sm text-muted-foreground">Montant de SOL à ajouter au pool de liquidité</p>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="lockLiquidity">Verrouiller la liquidité</Label>
                          <p className="text-sm text-muted-foreground">
                            Verrouiller la liquidité pour une période définie
                          </p>
                        </div>
                        <Switch
                          id="lockLiquidity"
                          checked={form.watch("lockLiquidity")}
                          onCheckedChange={(value) => form.setValue("lockLiquidity", value)}
                        />
                      </div>

                      {form.watch("lockLiquidity") && (
                        <div className="space-y-2">
                          <Label htmlFor="lockDuration">Durée de verrouillage (jours)</Label>
                          <Input
                            id="lockDuration"
                            type="number"
                            min="1"
                            {...form.register("lockDuration", { valueAsNumber: true })}
                            error={form.formState.errors.lockDuration?.message}
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Étape 5: Marketing */}
            {activeStep === 4 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label>Informations marketing</Label>
                  <p className="text-sm text-muted-foreground">
                    Ajoutez des liens vers vos plateformes de médias sociaux et votre site web.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="website">Site web</Label>
                    <Input
                      id="website"
                      placeholder="https://montoken.com"
                      {...form.register("website")}
                      error={form.formState.errors.website?.message}
                    />
                    {form.formState.errors.website && (
                      <p className="text-sm text-red-500">{form.formState.errors.website.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input
                      id="twitter"
                      placeholder="https://twitter.com/montoken"
                      {...form.register("twitter")}
                      error={form.formState.errors.twitter?.message}
                    />
                    {form.formState.errors.twitter && (
                      <p className="text-sm text-red-500">{form.formState.errors.twitter.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="telegram">Telegram</Label>
                    <Input
                      id="telegram"
                      placeholder="https://t.me/montoken"
                      {...form.register("telegram")}
                      error={form.formState.errors.telegram?.message}
                    />
                    {form.formState.errors.telegram && (
                      <p className="text-sm text-red-500">{form.formState.errors.telegram.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="discord">Discord</Label>
                    <Input
                      id="discord"
                      placeholder="https://discord.gg/montoken"
                      {...form.register("discord")}
                      error={form.formState.errors.discord?.message}
                    />
                    {form.formState.errors.discord && (
                      <p className="text-sm text-red-500">{form.formState.errors.discord.message}</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-between pt-4">
            <Button type="button" variant="outline" onClick={prevStep} disabled={activeStep === 0}>
              Précédent
            </Button>
            {activeStep < steps.length - 1 ? (
              <Button type="button" onClick={nextStep}>
                Suivant
              </Button>
            ) : (
              <Button type="submit" disabled={isCreating || !connected}>
                {isCreating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Création en cours...
                  </>
                ) : (
                  "Créer le token"
                )}
              </Button>
            )}
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <div className="text-sm text-muted-foreground">
          {connected ? (
            <>
              Connecté: {publicKey?.toString().slice(0, 6)}...{publicKey?.toString().slice(-4)}
              {walletBalance !== null && <> | Solde: {walletBalance.toFixed(4)} SOL</>}
            </>
          ) : (
            "Veuillez connecter votre portefeuille pour créer un token"
          )}
        </div>
        <div className="text-sm">
          Prix: <span className="font-medium">0.1 SOL</span>
        </div>
      </CardFooter>
    </Card>
  )
}

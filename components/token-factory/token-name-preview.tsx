"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import { Sparkles } from "lucide-react"

interface TokenNamePreviewProps {
  name: string
  symbol: string
  logoUrl: string | null
  suffix?: string
}

export function TokenNamePreview({ name, symbol, logoUrl, suffix = "SOL" }: TokenNamePreviewProps) {
  // Formater le nom et le symbole
  const formattedName = name || "Nom du Token"
  const formattedSymbol = symbol ? `${symbol}${suffix}` : `TKN${suffix}`

  // Générer une couleur aléatoire pour le logo par défaut
  const getRandomColor = () => {
    const colors = [
      "from-blue-500 to-purple-600",
      "from-green-500 to-emerald-600",
      "from-orange-500 to-amber-600",
      "from-pink-500 to-rose-600",
      "from-cyan-500 to-blue-600",
    ]
    return colors[Math.floor(Math.random() * colors.length)]
  }

  const logoColor = getRandomColor()

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div className="relative h-16 w-16 rounded-full overflow-hidden flex-shrink-0">
            {logoUrl ? (
              <Image
                src={logoUrl || "/placeholder.svg"}
                alt={formattedName}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 64px, 64px"
              />
            ) : (
              <div className={`h-full w-full bg-gradient-to-br ${logoColor} flex items-center justify-center`}>
                <span className="text-white font-bold text-xl">{formattedSymbol.substring(0, 2)}</span>
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 flex-wrap">
              <h3 className="font-bold text-lg truncate">{formattedName}</h3>
              <Badge variant="outline" className="font-medium">
                {formattedSymbol}
              </Badge>
            </div>
            <div className="flex items-center gap-1 mt-1 text-sm text-muted-foreground">
              <Sparkles className="h-3.5 w-3.5" />
              <span>Aperçu du token</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

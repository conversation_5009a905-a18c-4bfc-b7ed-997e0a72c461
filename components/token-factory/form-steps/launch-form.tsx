"use client"

import { useFormContext } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { InfoIcon as InfoCircle, Rocket, BarChart3 } from "lucide-react"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export function LaunchForm() {
  const form = useFormContext()

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Rocket className="h-5 w-5 text-primary" />
            Options de lancement
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-start space-x-4">
            <Switch
              id="addLiquidity"
              checked={form.watch("addLiquidity")}
              onCheckedChange={(checked) => form.setValue("addLiquidity", checked)}
            />
            <div className="space-y-1">
              <Label htmlFor="addLiquidity" className="flex items-center gap-1">
                Ajouter de la liquidité
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Crée automatiquement un pool de liquidité sur un DEX pour permettre l'échange de votre token.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <p className="text-sm text-muted-foreground">
                Crée un pool de liquidité sur un DEX pour permettre l'échange
              </p>
            </div>
          </div>

          {form.watch("addLiquidity") && (
            <div className="space-y-4 pl-8">
              <div>
                <Label htmlFor="liquidityAmount" className="flex items-center gap-1">
                  Montant de SOL à ajouter
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <InfoCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Le montant de SOL que vous souhaitez ajouter au pool de liquidité. Plus ce montant est élevé,
                          plus le prix initial de votre token sera stable.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex-1">
                    <Slider
                      id="liquidityAmount"
                      value={[form.watch("liquidityAmount")]}
                      min={0.01}
                      max={10}
                      step={0.01}
                      onValueChange={(value) => form.setValue("liquidityAmount", value[0])}
                    />
                  </div>
                  <div className="w-24 flex items-center gap-1">
                    <Input
                      type="number"
                      value={form.watch("liquidityAmount")}
                      onChange={(e) => form.setValue("liquidityAmount", Number.parseFloat(e.target.value))}
                      min={0.01}
                      step={0.01}
                    />
                    <span className="text-sm font-medium">SOL</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Recommandé: au moins 0.1 SOL pour une liquidité initiale
                </p>
              </div>

              <div>
                <Label htmlFor="liquidityPercentage" className="flex items-center gap-1">
                  Pourcentage de tokens à ajouter
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <InfoCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Le pourcentage de l'offre totale de tokens à ajouter au pool de liquidité.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex-1">
                    <Slider
                      id="liquidityPercentage"
                      value={[form.watch("liquidityPercentage")]}
                      min={1}
                      max={100}
                      step={1}
                      onValueChange={(value) => form.setValue("liquidityPercentage", value[0])}
                    />
                  </div>
                  <div className="w-24 flex items-center gap-1">
                    <Input
                      type="number"
                      value={form.watch("liquidityPercentage")}
                      onChange={(e) => form.setValue("liquidityPercentage", Number.parseInt(e.target.value))}
                      min={1}
                      max={100}
                    />
                    <span className="text-sm font-medium">%</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Recommandé: 50-70% pour une bonne liquidité initiale
                </p>
              </div>

              <div className="flex items-start space-x-4">
                <Switch
                  id="lockLiquidity"
                  checked={form.watch("lockLiquidity")}
                  onCheckedChange={(checked) => form.setValue("lockLiquidity", checked)}
                />
                <div className="space-y-1">
                  <Label htmlFor="lockLiquidity" className="flex items-center gap-1">
                    Verrouiller la liquidité
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger type="button">
                          <InfoCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Verrouille les tokens de liquidité pour une période définie, ce qui empêche le retrait de la
                            liquidité et rassure les investisseurs.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Verrouille les tokens de liquidité pour une période définie
                  </p>
                </div>
              </div>

              {form.watch("lockLiquidity") && (
                <div>
                  <Label htmlFor="lockDuration" className="flex items-center gap-1">
                    Durée de verrouillage (jours)
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger type="button">
                          <InfoCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            La durée pendant laquelle la liquidité sera verrouillée. Une durée plus longue inspire
                            davantage confiance aux investisseurs.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </Label>
                  <div className="flex items-center gap-4 mt-2">
                    <div className="flex-1">
                      <Slider
                        id="lockDuration"
                        value={[form.watch("lockDuration")]}
                        min={1}
                        max={365}
                        step={1}
                        onValueChange={(value) => form.setValue("lockDuration", value[0])}
                      />
                    </div>
                    <div className="w-24 flex items-center gap-1">
                      <Input
                        type="number"
                        value={form.watch("lockDuration")}
                        onChange={(e) => form.setValue("lockDuration", Number.parseInt(e.target.value))}
                        min={1}
                        max={365}
                      />
                      <span className="text-sm font-medium">jours</span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Recommandé: au moins 180 jours pour inspirer confiance
                  </p>
                </div>
              )}
            </div>
          )}

          <div className="flex items-start space-x-4 pt-2">
            <Switch
              id="setupInitialTrade"
              checked={form.watch("setupInitialTrade")}
              onCheckedChange={(checked) => form.setValue("setupInitialTrade", checked)}
            />
            <div className="space-y-1">
              <Label htmlFor="setupInitialTrade" className="flex items-center gap-1">
                Configurer un échange initial
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Effectue automatiquement une première transaction d'achat pour initialiser le graphique de prix
                        et créer un historique de trading.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <p className="text-sm text-muted-foreground">
                Effectue une première transaction pour initialiser le graphique de prix
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            Estimation des coûts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm">Création du token</span>
              <span className="font-medium">0.01 SOL</span>
            </div>
            {form.watch("addLiquidity") && (
              <div className="flex justify-between items-center">
                <span className="text-sm">Ajout de liquidité</span>
                <span className="font-medium">{form.watch("liquidityAmount")} SOL</span>
              </div>
            )}
            {form.watch("lockLiquidity") && (
              <div className="flex justify-between items-center">
                <span className="text-sm">Verrouillage de liquidité</span>
                <span className="font-medium">0.005 SOL</span>
              </div>
            )}
            {form.watch("setupInitialTrade") && (
              <div className="flex justify-between items-center">
                <span className="text-sm">Échange initial</span>
                <span className="font-medium">0.002 SOL</span>
              </div>
            )}
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between items-center font-medium">
                <span>Total estimé</span>
                <span>
                  {(
                    0.01 +
                    (form.watch("addLiquidity") ? form.watch("liquidityAmount") : 0) +
                    (form.watch("lockLiquidity") ? 0.005 : 0) +
                    (form.watch("setupInitialTrade") ? 0.002 : 0)
                  ).toFixed(3)}{" "}
                  SOL
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useFormContext } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { InfoIcon as InfoCircle, Globe, Twitter, MessageCircle, MessagesSquare } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface MarketingFormProps {
  logoPreview?: string | null
  onLogoChange?: (file: File | null, preview: string | null) => void
}

export function MarketingForm({ logoPreview, onLogoChange }: MarketingFormProps) {
  // Utiliser useFormContext pour accéder aux méthodes du formulaire
  const { register, watch, setValue } = useFormContext()

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Globe className="h-5 w-5 text-primary" />
            Présence en ligne
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="website" className="flex items-center gap-1">
                Site web
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        L'URL de votre site web officiel. Cela aide à établir la crédibilité de votre projet.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <div className="flex items-center mt-2">
                <div className="w-10 flex items-center justify-center">
                  <Globe className="h-5 w-5 text-muted-foreground" />
                </div>
                <Input id="website" placeholder="https://montoken.com" {...register("website")} />
              </div>
            </div>

            <div>
              <Label htmlFor="twitter" className="flex items-center gap-1">
                Twitter
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        L'URL de votre compte Twitter. Un canal important pour les annonces et la communauté.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <div className="flex items-center mt-2">
                <div className="w-10 flex items-center justify-center">
                  <Twitter className="h-5 w-5 text-muted-foreground" />
                </div>
                <Input id="twitter" placeholder="https://twitter.com/montoken" {...register("twitter")} />
              </div>
            </div>

            <div>
              <Label htmlFor="telegram" className="flex items-center gap-1">
                Telegram
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        L'URL de votre groupe Telegram. Un canal populaire pour les discussions communautaires.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <div className="flex items-center mt-2">
                <div className="w-10 flex items-center justify-center">
                  <MessageCircle className="h-5 w-5 text-muted-foreground" />
                </div>
                <Input id="telegram" placeholder="https://t.me/montoken" {...register("telegram")} />
              </div>
            </div>

            <div>
              <Label htmlFor="discord" className="flex items-center gap-1">
                Discord
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        L'URL de votre serveur Discord. Idéal pour construire une communauté engagée.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <div className="flex items-center mt-2">
                <div className="w-10 flex items-center justify-center">
                  <MessagesSquare className="h-5 w-5 text-muted-foreground" />
                </div>
                <Input id="discord" placeholder="https://discord.gg/montoken" {...register("discord")} />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            <h3 className="font-medium">Conseils pour promouvoir votre token</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <div className="rounded-full bg-primary/10 p-1 mt-0.5">
                  <Globe className="h-3 w-3 text-primary" />
                </div>
                <span>
                  <strong>Site web professionnel</strong> - Créez un site web clair qui explique votre projet, sa
                  feuille de route et son équipe.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <div className="rounded-full bg-primary/10 p-1 mt-0.5">
                  <Twitter className="h-3 w-3 text-primary" />
                </div>
                <span>
                  <strong>Présence sur les réseaux sociaux</strong> - Maintenez une présence active sur Twitter,
                  Telegram et Discord pour engager votre communauté.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <div className="rounded-full bg-primary/10 p-1 mt-0.5">
                  <MessageCircle className="h-3 w-3 text-primary" />
                </div>
                <span>
                  <strong>Communauté engagée</strong> - Interagissez régulièrement avec votre communauté et répondez à
                  leurs questions.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <div className="rounded-full bg-primary/10 p-1 mt-0.5">
                  <MessagesSquare className="h-3 w-3 text-primary" />
                </div>
                <span>
                  <strong>Transparence</strong> - Soyez transparent sur les développements, les défis et les succès de
                  votre projet.
                </span>
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

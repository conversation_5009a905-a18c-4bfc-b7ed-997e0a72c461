"use client"

import { useFormContext } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/slider"
import { InfoIcon as InfoCircle, ShieldCheck, Lock, Ban, Percent } from "lucide-react"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export function SecurityForm() {
  const form = useFormContext()

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <ShieldCheck className="h-5 w-5 text-primary" />
            Fonctionnalités de base
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start space-x-4">
              <Switch
                id="isMintable"
                checked={form.watch("isMintable")}
                onCheckedChange={(checked) => form.setValue("isMintable", checked)}
              />
              <div className="space-y-1">
                <Label htmlFor="isMintable" className="flex items-center gap-1">
                  Mintable
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <InfoCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Permet de créer de nouveaux tokens après le déploiement initial. Utile pour les modèles
                          inflationnistes ou les récompenses futures.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <p className="text-sm text-muted-foreground">Permet de créer de nouveaux tokens après le déploiement</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <Switch
                id="isBurnable"
                checked={form.watch("isBurnable")}
                onCheckedChange={(checked) => form.setValue("isBurnable", checked)}
              />
              <div className="space-y-1">
                <Label htmlFor="isBurnable" className="flex items-center gap-1">
                  Burnable
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <InfoCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Permet de détruire des tokens, réduisant ainsi l'offre totale. Utile pour les mécanismes
                          déflationnistes.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <p className="text-sm text-muted-foreground">Permet de détruire des tokens (réduire l'offre)</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <Switch
                id="isPausable"
                checked={form.watch("isPausable")}
                onCheckedChange={(checked) => form.setValue("isPausable", checked)}
              />
              <div className="space-y-1">
                <Label htmlFor="isPausable" className="flex items-center gap-1">
                  Pausable
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <InfoCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Permet de suspendre temporairement les transferts de tokens. Utile en cas d'urgence ou de
                          maintenance.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <p className="text-sm text-muted-foreground">Permet de suspendre temporairement les transferts</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <Switch
                id="isTransferTaxable"
                checked={form.watch("isTransferTaxable")}
                onCheckedChange={(checked) => form.setValue("isTransferTaxable", checked)}
              />
              <div className="space-y-1">
                <Label htmlFor="isTransferTaxable" className="flex items-center gap-1">
                  Taxe de transfert
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <InfoCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Applique une taxe sur chaque transfert de tokens. Utile pour financer le développement ou
                          récompenser les détenteurs.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <p className="text-sm text-muted-foreground">Applique une taxe sur chaque transfert de tokens</p>
              </div>
            </div>
          </div>

          {form.watch("isTransferTaxable") && (
            <div className="pt-2">
              <Label htmlFor="transferTaxRate" className="flex items-center gap-1">
                Taux de taxe de transfert
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Le pourcentage de tokens qui sera prélevé sur chaque transfert. Une valeur trop élevée peut
                        décourager les transactions.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex-1">
                  <Slider
                    id="transferTaxRate"
                    value={[form.watch("transferTaxRate")]}
                    min={0}
                    max={10}
                    step={0.1}
                    onValueChange={(value) => form.setValue("transferTaxRate", value[0])}
                  />
                </div>
                <div className="w-20 flex items-center gap-1">
                  <Input
                    type="number"
                    value={form.watch("transferTaxRate")}
                    onChange={(e) => form.setValue("transferTaxRate", Number.parseFloat(e.target.value))}
                    min={0}
                    max={10}
                    step={0.1}
                  />
                  <Percent className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Recommandé: 1-5%. Des taux plus élevés peuvent décourager les transactions.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Lock className="h-5 w-5 text-primary" />
            Protections avancées
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start space-x-4">
              <Switch
                id="antiBot"
                checked={form.watch("antiBot")}
                onCheckedChange={(checked) => form.setValue("antiBot", checked)}
              />
              <div className="space-y-1">
                <Label htmlFor="antiBot" className="flex items-center gap-1">
                  Protection anti-bot
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <InfoCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Empêche les bots d'effectuer des transactions rapides lors du lancement. Protège contre les
                          attaques de front-running.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <p className="text-sm text-muted-foreground">Empêche les bots d'effectuer des transactions rapides</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <Switch
                id="antiDump"
                checked={form.watch("antiDump")}
                onCheckedChange={(checked) => form.setValue("antiDump", checked)}
              />
              <div className="space-y-1">
                <Label htmlFor="antiDump" className="flex items-center gap-1">
                  Protection anti-dump
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <InfoCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Limite la quantité de tokens qui peut être vendue en une seule fois. Protège contre les ventes
                          massives qui font chuter le prix.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Limite la quantité de tokens qui peut être vendue à la fois
                </p>
              </div>
            </div>
          </div>

          <div className="pt-2 space-y-4">
            <div>
              <Label htmlFor="maxTxAmount" className="flex items-center gap-1">
                Montant maximum par transaction
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Le nombre maximum de tokens qui peut être transféré en une seule transaction. 0 signifie aucune
                        limite.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex-1">
                  <Slider
                    id="maxTxAmount"
                    value={[form.watch("maxTxAmount")]}
                    min={0}
                    max={form.watch("initialSupply")}
                    step={form.watch("initialSupply") / 100}
                    onValueChange={(value) => form.setValue("maxTxAmount", value[0])}
                    disabled={!form.watch("antiDump")}
                  />
                </div>
                <div className="w-32">
                  <Input
                    type="number"
                    value={form.watch("maxTxAmount")}
                    onChange={(e) => form.setValue("maxTxAmount", Number.parseInt(e.target.value))}
                    min={0}
                    max={form.watch("initialSupply")}
                    disabled={!form.watch("antiDump")}
                  />
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                0 = aucune limite. Recommandé: 1-2% de l'offre totale.
              </p>
            </div>

            <div>
              <Label htmlFor="maxWalletAmount" className="flex items-center gap-1">
                Montant maximum par portefeuille
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger type="button">
                      <InfoCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Le nombre maximum de tokens qu'un portefeuille peut détenir. 0 signifie aucune limite.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex-1">
                  <Slider
                    id="maxWalletAmount"
                    value={[form.watch("maxWalletAmount")]}
                    min={0}
                    max={form.watch("initialSupply")}
                    step={form.watch("initialSupply") / 100}
                    onValueChange={(value) => form.setValue("maxWalletAmount", value[0])}
                    disabled={!form.watch("antiDump")}
                  />
                </div>
                <div className="w-32">
                  <Input
                    type="number"
                    value={form.watch("maxWalletAmount")}
                    onChange={(e) => form.setValue("maxWalletAmount", Number.parseInt(e.target.value))}
                    min={0}
                    max={form.watch("initialSupply")}
                    disabled={!form.watch("antiDump")}
                  />
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                0 = aucune limite. Recommandé: 2-5% de l'offre totale.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-amber-50 border-amber-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-2">
            <Ban className="h-5 w-5 text-amber-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-800">Avertissement de sécurité</h3>
              <p className="text-sm text-amber-700 mt-1">
                Les fonctionnalités de sécurité avancées peuvent augmenter les frais de gaz et la complexité du contrat.
                Assurez-vous de comprendre les implications avant d'activer ces fonctionnalités.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

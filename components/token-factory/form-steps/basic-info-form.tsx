"use client"

import { useState } from "react"
import { useFormContext } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form"
import { Slider } from "@/components/ui/slider"
import { TokenImageSelector } from "../token-image-selector"
import { TokenNamePreview } from "../token-name-preview"
import { TokenTemplateSelector } from "../token-template-selector"
import { Card, CardContent } from "@/components/ui/card"
import { InfoIcon as InfoCircle } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface BasicInfoFormProps {
  logoPreview: string | null
  onLogoChange: (file: File | null, preview: string | null) => void
}

export function BasicInfoForm({ logoPreview, onLogoChange }: BasicInfoFormProps) {
  const [showTemplates, setShowTemplates] = useState(false)
  const form = useFormContext()

  // Templates prédéfinis pour les tokens
  const templates = [
    {
      id: "meme",
      name: "Memecoin",
      description: "Un token meme amusant et viral",
      symbol: "MEME",
      decimals: 9,
      initialSupply: **********,
      maxSupply: **********,
      logo: "/funny-memecoin-logo.png",
    },
    {
      id: "defi",
      name: "DeFi Token",
      description: "Un token pour les applications de finance décentralisée",
      symbol: "DEFI",
      decimals: 18,
      initialSupply: 100000000,
      maxSupply: **********,
      logo: "/defi-finance-logo-blue.png",
    },
    {
      id: "game",
      name: "Game Token",
      description: "Un token pour les jeux blockchain",
      symbol: "GAME",
      decimals: 6,
      initialSupply: 10000000,
      maxSupply: 100000000,
      logo: "/colorful-game-token-logo.png",
    },
    {
      id: "nft",
      name: "NFT Project",
      description: "Un token pour un projet NFT",
      symbol: "NFT",
      decimals: 0,
      initialSupply: 10000,
      maxSupply: 10000,
      logo: "/abstract-nft-logo.png",
    },
    {
      id: "dao",
      name: "DAO Governance",
      description: "Un token de gouvernance pour une DAO",
      symbol: "GOV",
      decimals: 18,
      initialSupply: 1000000,
      maxSupply: 10000000,
      logo: "/dao-governance-logo.png",
    },
  ]

  // Appliquer un template
  const applyTemplate = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId)
    if (template) {
      form.setValue("name", template.name)
      form.setValue("symbol", template.symbol)
      form.setValue("description", template.description)
      form.setValue("decimals", template.decimals)
      form.setValue("initialSupply", template.initialSupply)
      form.setValue("maxSupply", template.maxSupply)
      // Simuler un changement de logo
      onLogoChange(null, template.logo)
    }
    setShowTemplates(false)
  }

  return (
    <div className="space-y-6">
      {showTemplates ? (
        <TokenTemplateSelector
          templates={templates}
          onSelect={applyTemplate}
          onCancel={() => setShowTemplates(false)}
        />
      ) : (
        <div className="flex justify-end">
          <button type="button" className="text-sm text-primary hover:underline" onClick={() => setShowTemplates(true)}>
            Utiliser un template
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom du token</FormLabel>
                <FormControl>
                  <Input placeholder="Ex: My Awesome Token" {...field} />
                </FormControl>
                <FormDescription>Le nom complet de votre token (3-50 caractères)</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="symbol"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Symbole</FormLabel>
                <FormControl>
                  <Input placeholder="Ex: MAT" {...field} maxLength={10} />
                </FormControl>
                <FormDescription>Le symbole court de votre token (2-10 caractères)</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea placeholder="Décrivez votre token..." {...field} />
                </FormControl>
                <FormDescription>Une brève description de votre token et de son utilité</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="decimals"
              render={({ field: { onChange, value, ...rest } }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Décimales
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger type="button">
                          <InfoCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Le nombre de décimales détermine la divisibilité de votre token. 9 est la valeur standard
                            pour Solana.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={0}
                      max={9}
                      onChange={(e) => onChange(Number.parseInt(e.target.value))}
                      value={value}
                      {...rest}
                    />
                  </FormControl>
                  <FormDescription>Généralement 9 pour Solana (0-9)</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="space-y-6">
          <TokenImageSelector logoPreview={logoPreview} onChange={(file, preview) => onLogoChange(file, preview)} />

          <TokenNamePreview
            name={form.watch("name") || "Nom du Token"}
            symbol={form.watch("symbol") || "SYM"}
            logoUrl={logoPreview}
          />
        </div>
      </div>

      <Card>
        <CardContent className="p-4 space-y-4">
          <div>
            <Label htmlFor="initialSupply" className="flex items-center gap-1">
              Offre initiale
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger type="button">
                    <InfoCircle className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">
                      Le nombre total de tokens qui seront créés initialement. Cette valeur peut être augmentée plus
                      tard si le token est mintable.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </Label>
            <div className="flex items-center gap-4 mt-2">
              <div className="flex-1">
                <Slider
                  value={[form.watch("initialSupply")]}
                  min={1000}
                  max={**********0}
                  step={1000}
                  onValueChange={(value) => form.setValue("initialSupply", value[0])}
                />
              </div>
              <div className="w-32">
                <Input
                  id="initialSupply"
                  type="number"
                  value={form.watch("initialSupply")}
                  onChange={(e) => form.setValue("initialSupply", Number.parseInt(e.target.value))}
                  min={1000}
                />
              </div>
            </div>
            <p className="text-sm text-muted-foreground mt-1">Valeur recommandée: 1 milliard (1,000,000,000)</p>
          </div>

          <div>
            <Label htmlFor="maxSupply" className="flex items-center gap-1">
              Offre maximale
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger type="button">
                    <InfoCircle className="h-4 w-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">
                      Le nombre maximum de tokens qui pourront exister. Doit être supérieur ou égal à l'offre initiale.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </Label>
            <div className="flex items-center gap-4 mt-2">
              <div className="flex-1">
                <Slider
                  value={[form.watch("maxSupply")]}
                  min={form.watch("initialSupply")}
                  max={**********00}
                  step={1000}
                  onValueChange={(value) => form.setValue("maxSupply", value[0])}
                />
              </div>
              <div className="w-32">
                <Input
                  id="maxSupply"
                  type="number"
                  value={form.watch("maxSupply")}
                  onChange={(e) => form.setValue("maxSupply", Number.parseInt(e.target.value))}
                  min={form.watch("initialSupply")}
                />
              </div>
            </div>
            <p className="text-sm text-muted-foreground mt-1">Doit être supérieur ou égal à l'offre initiale</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

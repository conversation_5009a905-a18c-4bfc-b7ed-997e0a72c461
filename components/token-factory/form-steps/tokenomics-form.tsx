"use client"

import { useState } from "react"
import { useFormContext } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>lider } from "@/components/ui/slider"
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON> as RechartsTooltip } from "recharts"
import { Plus, Trash2, InfoIcon as InfoCircle } from "lucide-react"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export function TokenomicsForm() {
  const [newAllocationLabel, setNewAllocationLabel] = useState("")
  const [newAllocationPercentage, setNewAllocationPercentage] = useState(5)
  const form = useFormContext()

  const distribution = form.watch("distribution") || []
  const totalPercentage = distribution.reduce((sum: number, item: any) => sum + item.percentage, 0)

  // Couleurs pour le graphique
  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884D8",
    "#82CA9D",
    "#FF6B6B",
    "#6A7FDB",
    "#F7C59F",
    "#9CAFB7",
  ]

  // Ajouter une nouvelle allocation
  const addAllocation = () => {
    if (!newAllocationLabel.trim()) return
    if (totalPercentage + newAllocationPercentage > 100) return

    const newDistribution = [
      ...distribution,
      {
        label: newAllocationLabel,
        percentage: newAllocationPercentage,
        address: "",
      },
    ]

    form.setValue("distribution", newDistribution)
    setNewAllocationLabel("")
    setNewAllocationPercentage(5)
  }

  // Supprimer une allocation
  const removeAllocation = (index: number) => {
    const newDistribution = [...distribution]
    newDistribution.splice(index, 1)
    form.setValue("distribution", newDistribution)
  }

  // Mettre à jour le pourcentage d'une allocation
  const updatePercentage = (index: number, percentage: number) => {
    const newDistribution = [...distribution]
    newDistribution[index].percentage = percentage
    form.setValue("distribution", newDistribution)
  }

  // Mettre à jour l'adresse d'une allocation
  const updateAddress = (index: number, address: string) => {
    const newDistribution = [...distribution]
    newDistribution[index].address = address
    form.setValue("distribution", newDistribution)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-1">
            Distribution des tokens
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger type="button">
                  <InfoCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    Définissez comment l'offre initiale de tokens sera distribuée entre différentes allocations.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              {distribution.map((item: any, index: number) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">{item.label}</Label>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{item.percentage}%</span>
                      <Button type="button" variant="ghost" size="icon" onClick={() => removeAllocation(index)}>
                        <Trash2 className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <Slider
                        value={[item.percentage]}
                        min={1}
                        max={100}
                        step={1}
                        onValueChange={(value) => updatePercentage(index, value[0])}
                      />
                    </div>
                    <div className="w-16">
                      <Input
                        type="number"
                        value={item.percentage}
                        onChange={(e) => updatePercentage(index, Number.parseInt(e.target.value))}
                        min={1}
                        max={100}
                      />
                    </div>
                  </div>
                  <div>
                    <Input
                      placeholder="Adresse du destinataire (optionnel)"
                      value={item.address}
                      onChange={(e) => updateAddress(index, e.target.value)}
                    />
                  </div>
                </div>
              ))}

              <div className="pt-4">
                <div className="flex items-center justify-between mb-2">
                  <Label>Nouvelle allocation</Label>
                  <span className="text-sm font-medium">{newAllocationPercentage}%</span>
                </div>
                <Input
                  placeholder="Nom de l'allocation (ex: Marketing)"
                  value={newAllocationLabel}
                  onChange={(e) => setNewAllocationLabel(e.target.value)}
                  className="mb-2"
                />
                <div className="flex items-center gap-4 mb-2">
                  <div className="flex-1">
                    <Slider
                      value={[newAllocationPercentage]}
                      min={1}
                      max={100 - totalPercentage > 0 ? 100 - totalPercentage : 1}
                      step={1}
                      onValueChange={(value) => setNewAllocationPercentage(value[0])}
                    />
                  </div>
                  <div className="w-16">
                    <Input
                      type="number"
                      value={newAllocationPercentage}
                      onChange={(e) => setNewAllocationPercentage(Number.parseInt(e.target.value))}
                      min={1}
                      max={100 - totalPercentage > 0 ? 100 - totalPercentage : 1}
                    />
                  </div>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={addAllocation}
                  disabled={!newAllocationLabel.trim() || totalPercentage >= 100}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter
                </Button>
              </div>
            </div>

            <div className="flex flex-col items-center justify-center">
              <div className="h-64 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={distribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="percentage"
                      nameKey="label"
                    >
                      {distribution.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip
                      formatter={(value: any, name: any) => [`${value}%`, name]}
                      contentStyle={{ backgroundColor: "rgba(255, 255, 255, 0.9)", borderRadius: "4px" }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              <div className="text-center mt-4">
                <div className="text-sm font-medium">Total alloué: {totalPercentage}%</div>
                <div className="text-sm text-muted-foreground">
                  {totalPercentage < 100 ? `Reste à allouer: ${100 - totalPercentage}%` : "Allocation complète"}
                </div>
              </div>

              <div className="mt-4 grid grid-cols-2 gap-2">
                {distribution.map((item: any, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-xs">{item.label}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

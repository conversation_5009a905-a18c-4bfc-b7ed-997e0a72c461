"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowUpRight, ArrowDownRight, ArrowLeftRight, ExternalLink, RefreshCw } from "lucide-react"

interface Transaction {
  id: string
  type: "buy" | "sell" | "transfer"
  amount: number
  price: number
  total: number
  timestamp: string
  from: string
  to: string
  hash: string
}

interface TokenTransactionsProps {
  tokenAddress: string
}

export function TokenTransactions({ tokenAddress }: TokenTransactionsProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // Simuler le chargement des transactions
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true)
        // Simuler une requête API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Générer des données fictives pour la démonstration
        const mockTransactions: Transaction[] = Array.from({ length: 10 }, (_, i) => {
          const type = ["buy", "sell", "transfer"][Math.floor(Math.random() * 3)] as "buy" | "sell" | "transfer"
          const amount = Math.floor(Math.random() * 10000) + 100
          const price = Math.random() * 0.01
          const total = amount * price
          const timestamp = new Date(Date.now() - Math.random() * 86400000 * 7).toISOString()
          const from = `Wallet${Math.floor(Math.random() * 1000)}...${Math.random().toString(36).substring(2, 6)}`
          const to = `Wallet${Math.floor(Math.random() * 1000)}...${Math.random().toString(36).substring(2, 6)}`
          const hash = `${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 6)}`

          return {
            id: `tx-${page}-${i}`,
            type,
            amount,
            price,
            total,
            timestamp,
            from,
            to,
            hash,
          }
        })

        if (page === 1) {
          setTransactions(mockTransactions)
        } else {
          setTransactions((prev) => [...prev, ...mockTransactions])
        }

        // Simuler la fin des données après 3 pages
        setHasMore(page < 3)
      } catch (error) {
        console.error("Erreur lors du chargement des transactions:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchTransactions()
  }, [tokenAddress, page])

  // Charger plus de transactions
  const loadMore = () => {
    if (!loading && hasMore) {
      setPage((prev) => prev + 1)
    }
  }

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Obtenir la couleur du badge en fonction du type de transaction
  const getTransactionBadgeColor = (type: string) => {
    switch (type) {
      case "buy":
        return "bg-green-100 text-green-800"
      case "sell":
        return "bg-red-100 text-red-800"
      case "transfer":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Obtenir l'icône en fonction du type de transaction
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "buy":
        return <ArrowUpRight className="h-4 w-4" />
      case "sell":
        return <ArrowDownRight className="h-4 w-4" />
      case "transfer":
        return <ArrowLeftRight className="h-4 w-4" />
      default:
        return null
    }
  }

  // Obtenir le libellé en fonction du type de transaction
  const getTransactionLabel = (type: string) => {
    switch (type) {
      case "buy":
        return "Achat"
      case "sell":
        return "Vente"
      case "transfer":
        return "Transfert"
      default:
        return type
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transactions récentes</CardTitle>
        <CardDescription>Historique des transactions pour ce token</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.length === 0 && !loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Aucune transaction trouvée pour ce token</p>
            </div>
          ) : (
            <div className="space-y-4">
              {transactions.map((tx) => (
                <div key={tx.id} className="p-4 border rounded-lg">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div className="flex items-center gap-3">
                      <div
                        className={`p-2 rounded-full ${
                          tx.type === "buy" ? "bg-green-100" : tx.type === "sell" ? "bg-red-100" : "bg-blue-100"
                        }`}
                      >
                        {getTransactionIcon(tx.type)}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={getTransactionBadgeColor(tx.type)}>
                            {getTransactionLabel(tx.type)}
                          </Badge>
                          <span className="text-sm text-muted-foreground">{formatDate(tx.timestamp)}</span>
                        </div>
                        <p className="font-medium mt-1">
                          {tx.amount.toLocaleString()} tokens à {tx.price.toFixed(6)} SOL
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{tx.total.toFixed(4)} SOL</p>
                      <a
                        href={`https://explorer.solana.com/tx/${tx.hash}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline flex items-center justify-end gap-1 mt-1"
                      >
                        {tx.hash}
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>
                  <div className="mt-2 text-sm text-muted-foreground">
                    <span>De: {tx.from}</span>
                    <span className="mx-2">→</span>
                    <span>À: {tx.to}</span>
                  </div>
                </div>
              ))}
            </div>
          )}

          {loading && (
            <div className="flex justify-center py-4">
              <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          )}
        </div>
      </CardContent>
      {hasMore && !loading && (
        <CardFooter>
          <Button variant="outline" className="w-full" onClick={loadMore}>
            Charger plus de transactions
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}

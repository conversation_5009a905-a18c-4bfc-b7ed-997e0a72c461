"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { RefreshCw } from "lucide-react"

interface PriceData {
  date: string
  price: number
  volume: number
  marketCap: number
}

interface TokenStatisticsProps {
  tokenAddress: string
}

export function TokenStatistics({ tokenAddress }: TokenStatisticsProps) {
  const [priceData, setPriceData] = useState<PriceData[]>([])
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState<"24h" | "7d" | "30d" | "all">("7d")

  // Simuler le chargement des données de prix
  useEffect(() => {
    const fetchPriceData = async () => {
      try {
        setLoading(true)
        // Simuler une requête API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Déterminer le nombre de points de données en fonction du timeframe
        let days = 7
        switch (timeframe) {
          case "24h":
            days = 1
            break
          case "7d":
            days = 7
            break
          case "30d":
            days = 30
            break
          case "all":
            days = 90
            break
        }

        // Générer des données fictives pour la démonstration
        const mockPriceData: PriceData[] = []
        const now = new Date()
        let basePrice = 0.001
        let baseVolume = 1000
        let baseMarketCap = 10000

        for (let i = days; i >= 0; i--) {
          const date = new Date(now)
          date.setDate(date.getDate() - i)

          // Simuler des variations de prix
          const randomFactor = 1 + (Math.random() * 0.2 - 0.1) // -10% à +10%
          basePrice *= randomFactor
          baseVolume *= 1 + (Math.random() * 0.4 - 0.2) // -20% à +20%
          baseMarketCap = basePrice * 10000000 // Simuler une offre fixe

          mockPriceData.push({
            date: date.toISOString(),
            price: basePrice,
            volume: baseVolume,
            marketCap: baseMarketCap,
          })
        }

        setPriceData(mockPriceData)
      } catch (error) {
        console.error("Erreur lors du chargement des données de prix:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchPriceData()
  }, [tokenAddress, timeframe])

  // Formater les dates pour l'affichage
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    if (timeframe === "24h") {
      return date.toLocaleTimeString("fr-FR", { hour: "2-digit", minute: "2-digit" })
    }
    return date.toLocaleDateString("fr-FR", { day: "2-digit", month: "2-digit" })
  }

  // Calculer les variations de prix
  const calculatePriceChange = () => {
    if (priceData.length < 2) return { change: 0, percentage: 0 }

    const firstPrice = priceData[0].price
    const lastPrice = priceData[priceData.length - 1].price
    const change = lastPrice - firstPrice
    const percentage = (change / firstPrice) * 100

    return { change, percentage }
  }

  const priceChange = calculatePriceChange()

  return (
    <Card>
      <CardHeader>
        <CardTitle>Statistiques du token</CardTitle>
        <CardDescription>Évolution du prix et des métriques clés</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-12">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Prix actuel</p>
                <p className="text-3xl font-bold">
                  {priceData.length > 0 ? priceData[priceData.length - 1].price.toFixed(6) : "0.000000"} SOL
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Variation ({timeframe})</p>
                <p className={`text-xl font-bold ${priceChange.percentage >= 0 ? "text-green-600" : "text-red-600"}`}>
                  {priceChange.percentage >= 0 ? "+" : ""}
                  {priceChange.percentage.toFixed(2)}%
                </p>
              </div>
            </div>

            <Tabs defaultValue="price" className="space-y-4">
              <TabsList>
                <TabsTrigger value="price">Prix</TabsTrigger>
                <TabsTrigger value="volume">Volume</TabsTrigger>
                <TabsTrigger value="marketCap">Cap. boursière</TabsTrigger>
              </TabsList>

              <div className="flex justify-end space-x-2">
                <Button
                  variant={timeframe === "24h" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeframe("24h")}
                >
                  24h
                </Button>
                <Button
                  variant={timeframe === "7d" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeframe("7d")}
                >
                  7j
                </Button>
                <Button
                  variant={timeframe === "30d" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeframe("30d")}
                >
                  30j
                </Button>
                <Button
                  variant={timeframe === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeframe("all")}
                >
                  Tout
                </Button>
              </div>

              <TabsContent value="price" className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={priceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={formatDate} />
                    <YAxis domain={["auto", "auto"]} tickFormatter={(value) => value.toFixed(6)} width={80} />
                    <Tooltip
                      formatter={(value: number) => [value.toFixed(6) + " SOL", "Prix"]}
                      labelFormatter={(label) => `Date: ${formatDate(label)}`}
                    />
                    <Line
                      type="monotone"
                      dataKey="price"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </TabsContent>

              <TabsContent value="volume" className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={priceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={formatDate} />
                    <YAxis domain={["auto", "auto"]} tickFormatter={(value) => value.toLocaleString()} width={80} />
                    <Tooltip
                      formatter={(value: number) => [value.toLocaleString() + " SOL", "Volume"]}
                      labelFormatter={(label) => `Date: ${formatDate(label)}`}
                    />
                    <Line
                      type="monotone"
                      dataKey="volume"
                      stroke="#a855f7"
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </TabsContent>

              <TabsContent value="marketCap" className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={priceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={formatDate} />
                    <YAxis
                      domain={["auto", "auto"]}
                      tickFormatter={(value) => (value / 1000).toLocaleString() + "k"}
                      width={80}
                    />
                    <Tooltip
                      formatter={(value: number) => [value.toLocaleString() + " SOL", "Cap. boursière"]}
                      labelFormatter={(label) => `Date: ${formatDate(label)}`}
                    />
                    <Line
                      type="monotone"
                      dataKey="marketCap"
                      stroke="#22c55e"
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </TabsContent>
            </Tabs>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">Prix max ({timeframe})</p>
                <p className="font-medium">{Math.max(...priceData.map((d) => d.price)).toFixed(6)} SOL</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">Prix min ({timeframe})</p>
                <p className="font-medium">{Math.min(...priceData.map((d) => d.price)).toFixed(6)} SOL</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">Volume total ({timeframe})</p>
                <p className="font-medium">{priceData.reduce((sum, d) => sum + d.volume, 0).toLocaleString()} SOL</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">Cap. boursière actuelle</p>
                <p className="font-medium">
                  {priceData.length > 0 ? priceData[priceData.length - 1].marketCap.toLocaleString() : "0"} SOL
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

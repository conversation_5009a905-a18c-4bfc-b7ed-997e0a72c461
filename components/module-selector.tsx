"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, Coins, LineChart, Lock, Settings, ShieldCheck, Vote, Rocket } from "lucide-react"

interface ModuleSelectorProps {
  activeModule: string
  setActiveModule: (module: string) => void
}

export default function ModuleSelector({ activeModule, setActiveModule }: ModuleSelectorProps) {
  const modules = [
    { id: "core", name: "Core (GF-beta)", icon: <ShieldCheck className="h-4 w-4" /> },
    { id: "token-factory", name: "Token Factory", icon: <Coins className="h-4 w-4" /> },
    { id: "memecoin-launchpad", name: "Memecoin Launchpad", icon: <Rocket className="h-4 w-4" /> },
    { id: "market", name: "Market", icon: <LineChart className="h-4 w-4" /> },
    { id: "staking", name: "Staking", icon: <Lock className="h-4 w-4" /> },
    { id: "governance", name: "Governance", icon: <Vote className="h-4 w-4" /> },
    { id: "analytics", name: "Analytics", icon: <BarChart3 className="h-4 w-4" /> },
    { id: "settings", name: "Settings", icon: <Settings className="h-4 w-4" /> },
  ]

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Modules</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-1 px-1">
          {modules.map((module) => (
            <Button
              key={module.id}
              variant={activeModule === module.id ? "secondary" : "ghost"}
              className={`w-full justify-start ${activeModule === module.id ? "bg-secondary" : ""}`}
              onClick={() => setActiveModule(module.id)}
            >
              {module.icon}
              <span className="ml-2">{module.name}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

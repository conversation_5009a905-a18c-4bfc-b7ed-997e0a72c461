"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ArrowRightLeft, TrendingUp, TrendingDown } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface TokenTransactionProps {
  tokenId: string
  tokenSymbol: string
  tokenName: string
  tokenPrice: number
  tokenBalance?: number
  solBalance?: number
  onTransactionComplete?: () => void
}

export default function TokenTransaction({
  tokenId,
  tokenSymbol,
  tokenName,
  tokenPrice,
  tokenBalance = 0,
  solBalance = 0,
  onTransactionComplete,
}: TokenTransactionProps) {
  const [activeTab, setActiveTab] = useState<string>("buy")
  const [buyA<PERSON>, setBuyAmount] = useState<string>("")
  const [sellAmount, setSellAmount] = useState<string>("")
  const [isProcessing, setIsProcessing] = useState<boolean>(false)
  const { toast } = useToast()

  // Calculer la valeur en SOL ou en tokens
  const calculateBuyValue = () => {
    const amount = Number.parseFloat(buyAmount) || 0
    return (amount * tokenPrice).toFixed(6)
  }

  const calculateSellValue = () => {
    const amount = Number.parseFloat(sellAmount) || 0
    return (amount * tokenPrice).toFixed(6)
  }

  // Définir les montants maximum
  const setMaxBuy = () => {
    if (solBalance && tokenPrice > 0) {
      // Laisser un peu de SOL pour les frais de transaction
      const maxAmount = Math.max(0, (solBalance - 0.01) / tokenPrice)
      setBuyAmount(maxAmount.toFixed(6))
    }
  }

  const setMaxSell = () => {
    if (tokenBalance > 0) {
      setSellAmount(tokenBalance.toString())
    }
  }

  // Gérer l'achat de tokens
  const handleBuy = async () => {
    if (!buyAmount || Number.parseFloat(buyAmount) <= 0) {
      toast({
        title: "Montant invalide",
        description: "Veuillez entrer un montant valide à acheter",
        variant: "destructive",
      })
      return
    }

    const buyValue = Number.parseFloat(calculateBuyValue())
    if (buyValue > solBalance) {
      toast({
        title: "Solde insuffisant",
        description: "Vous n'avez pas assez de SOL pour effectuer cet achat",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    try {
      // Simuler une transaction
      await new Promise((resolve) => setTimeout(resolve, 1500))

      toast({
        title: "Achat réussi",
        description: `Vous avez acheté ${buyAmount} ${tokenSymbol} avec succès`,
      })

      setBuyAmount("")
      if (onTransactionComplete) {
        onTransactionComplete()
      }
    } catch (error) {
      console.error("Erreur de transaction:", error)
      toast({
        title: "Erreur de transaction",
        description: "Une erreur inattendue s'est produite",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Gérer la vente de tokens
  const handleSell = async () => {
    if (!sellAmount || Number.parseFloat(sellAmount) <= 0) {
      toast({
        title: "Montant invalide",
        description: "Veuillez entrer un montant valide à vendre",
        variant: "destructive",
      })
      return
    }

    if (Number.parseFloat(sellAmount) > tokenBalance) {
      toast({
        title: "Solde insuffisant",
        description: `Vous n'avez pas assez de ${tokenSymbol} pour effectuer cette vente`,
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    try {
      // Simuler une transaction
      await new Promise((resolve) => setTimeout(resolve, 1500))

      toast({
        title: "Vente réussie",
        description: `Vous avez vendu ${sellAmount} ${tokenSymbol} avec succès`,
      })

      setSellAmount("")
      if (onTransactionComplete) {
        onTransactionComplete()
      }
    } catch (error) {
      console.error("Erreur de transaction:", error)
      toast({
        title: "Erreur de transaction",
        description: "Une erreur inattendue s'est produite",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Trader {tokenSymbol}</CardTitle>
        <CardDescription>Acheter et vendre des tokens {tokenName}</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="buy">
              <TrendingUp className="h-4 w-4 mr-2" />
              Acheter
            </TabsTrigger>
            <TabsTrigger value="sell">
              <TrendingDown className="h-4 w-4 mr-2" />
              Vendre
            </TabsTrigger>
            <TabsTrigger value="swap">
              <ArrowRightLeft className="h-4 w-4 mr-2" />
              Échanger
            </TabsTrigger>
          </TabsList>

          <TabsContent value="buy" className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Prix actuel:</span>
                <span className="font-medium">${tokenPrice.toFixed(6)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Solde SOL:</span>
                <span className="font-medium">{solBalance.toFixed(4)} SOL</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <label htmlFor="buyAmount" className="text-sm font-medium">
                  Montant à acheter
                </label>
                <button onClick={setMaxBuy} className="text-xs text-blue-500 hover:underline">
                  Max
                </button>
              </div>
              <div className="flex items-center space-x-2">
                <Input
                  id="buyAmount"
                  type="number"
                  placeholder="0.00"
                  value={buyAmount}
                  onChange={(e) => setBuyAmount(e.target.value)}
                  disabled={isProcessing}
                />
                <div className="bg-muted px-3 py-2 rounded text-sm font-medium">{tokenSymbol}</div>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="buyValue" className="text-sm font-medium">
                Valeur totale
              </label>
              <div className="flex items-center space-x-2">
                <Input id="buyValue" type="text" value={calculateBuyValue()} disabled />
                <div className="bg-muted px-3 py-2 rounded text-sm font-medium">SOL</div>
              </div>
            </div>

            <Button
              onClick={handleBuy}
              disabled={isProcessing || !buyAmount || Number.parseFloat(buyAmount) <= 0}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Traitement...
                </>
              ) : (
                `Acheter ${tokenSymbol}`
              )}
            </Button>
          </TabsContent>

          <TabsContent value="sell" className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Prix actuel:</span>
                <span className="font-medium">${tokenPrice.toFixed(6)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Solde {tokenSymbol}:</span>
                <span className="font-medium">
                  {tokenBalance.toFixed(4)} {tokenSymbol}
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <label htmlFor="sellAmount" className="text-sm font-medium">
                  Montant à vendre
                </label>
                <button onClick={setMaxSell} className="text-xs text-blue-500 hover:underline">
                  Max
                </button>
              </div>
              <div className="flex items-center space-x-2">
                <Input
                  id="sellAmount"
                  type="number"
                  placeholder="0.00"
                  value={sellAmount}
                  onChange={(e) => setSellAmount(e.target.value)}
                  disabled={isProcessing}
                />
                <div className="bg-muted px-3 py-2 rounded text-sm font-medium">{tokenSymbol}</div>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="sellValue" className="text-sm font-medium">
                Valeur totale
              </label>
              <div className="flex items-center space-x-2">
                <Input id="sellValue" type="text" value={calculateSellValue()} disabled />
                <div className="bg-muted px-3 py-2 rounded text-sm font-medium">SOL</div>
              </div>
            </div>

            <Button
              onClick={handleSell}
              disabled={isProcessing || !sellAmount || Number.parseFloat(sellAmount) <= 0}
              className="w-full bg-red-600 hover:bg-red-700"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Traitement...
                </>
              ) : (
                `Vendre ${tokenSymbol}`
              )}
            </Button>
          </TabsContent>

          <TabsContent value="swap" className="space-y-4">
            <div className="flex items-center justify-center py-10">
              <div className="text-center">
                <ArrowRightLeft className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Échange de tokens</h3>
                <p className="text-muted-foreground">La fonctionnalité d'échange sera bientôt disponible.</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2 items-start">
        <div className="text-xs text-muted-foreground">Frais de transaction: ~0.000005 SOL</div>
        <div className="text-xs text-muted-foreground">Prix mis à jour il y a 30 secondes</div>
      </CardFooter>
    </Card>
  )
}

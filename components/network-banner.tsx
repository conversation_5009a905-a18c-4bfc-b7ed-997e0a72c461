"use client"

import { useNetwork } from "@/contexts/network-context"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { NetworkSelector } from "@/components/network-selector"

export function NetworkBanner() {
  const { network } = useNetwork()

  // Ne pas afficher la bannière pour le réseau principal
  if (network === "mainnet") {
    return null
  }

  return (
    <Alert variant="warning" className="rounded-none border-x-0 border-t-0 px-4 py-2">
      <AlertDescription className="flex items-center justify-center text-sm">
        <span className="mr-2">
          Vous êtes connecté au réseau <strong>{network === "devnet" ? "Devnet" : "Testnet"}</strong>
        </span>
        <NetworkSelector variant="outline" size="sm" />
      </AlertDescription>
    </Alert>
  )
}

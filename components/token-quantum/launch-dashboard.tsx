"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Clock,
  Copy,
  ExternalLink,
  Info,
  Lock,
  MoreHorizontal,
  Rocket,
  Settings,
  Share2,
  Shield,
  Users,
  Wallet,
  AlertTriangle,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  Sparkles,
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import type { TokenLaunchStatus } from "@/lib/quantum-launch-service"

interface LaunchDashboardProps {
  launchId: string
}

export default function LaunchDashboard({ launchId }: LaunchDashboardProps) {
  const router = useRouter()
  const { connected, publicKey } = useWallet()
  const { toast } = useToast()

  const [launchStatus, setLaunchStatus] = useState<TokenLaunchStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const [contributionAmount, setContributionAmount] = useState("")
  const [isContributing, setIsContributing] = useState(false)
  const [isLaunching, setIsLaunching] = useState(false)
  const [showShareDialog, setShowShareDialog] = useState(false)
  const [showSettingsDialog, setShowSettingsDialog] = useState(false)

  // Simuler le chargement des données de lancement
  useEffect(() => {
    const fetchLaunchStatus = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Données simulées pour l'exemple
        const mockLaunchStatus: TokenLaunchStatus = {
          id: launchId,
          tokenAddress: "GFQUANTUMxyz123456789abcdef",
          ownerAddress: "8xj7...4Fgh",
          config: {
            name: "Global Finance Quantum",
            symbol: "GF",
            suffix: "QUANTUM",
            decimals: 9,
            totalSupply: 1000000000,
            description: "Token Quantum de nouvelle génération pour la finance décentralisée",
            website: "https://globalfinance.com",
            twitter: "https://twitter.com/globalfinance",
            telegram: "https://t.me/globalfinance",

            initialPrice: 0.00005,
            softCap: 50,
            hardCap: 200,
            minBuy: 0.1,
            maxBuy: 5,

            liquidityPercentage: 70,
            teamPercentage: 10,
            marketingPercentage: 10,
            reservePercentage: 10,

            liquidityLockPeriod: 180,
            teamLockPeriod: 90,

            phases: {
              presale: true,
              fairLaunch: true,
              initialDexOffering: false,
            },

            antiBot: true,
            antiDump: true,
            maxWalletPercentage: 2,
            maxTxPercentage: 1,

            buyTax: 5,
            sellTax: 7,
            transferTax: 3,

            teamWallet: "team_wallet_address",
            marketingWallet: "marketing_wallet_address",

            targetDex: "all",
            listingMultiplier: 1.5,
          },
          currentPhase: {
            id: "presale_123",
            name: "Presale",
            description: "Phase initiale de vente à prix réduit pour les premiers investisseurs",
            startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 jours avant
            endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 jours après
            targetAmount: 50,
            minContribution: 0.1,
            maxContribution: 5,
            price: 0.00005,
            status: "active",
            participants: 42,
            amountRaised: 28.5,
            percentageComplete: 57,
          },
          phases: [
            {
              id: "presale_123",
              name: "Presale",
              description: "Phase initiale de vente à prix réduit pour les premiers investisseurs",
              startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 jours avant
              endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 jours après
              targetAmount: 50,
              minContribution: 0.1,
              maxContribution: 5,
              price: 0.00005,
              status: "active",
              participants: 42,
              amountRaised: 28.5,
              percentageComplete: 57,
            },
            {
              id: "fairlaunch_456",
              name: "Fair Launch",
              description: "Lancement équitable avec prix fixe pour tous les participants",
              startDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 jours après
              endDate: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000), // 6 jours après
              targetAmount: 150,
              minContribution: 0.1,
              maxContribution: 5,
              price: 0.000055,
              status: "pending",
              participants: 0,
              amountRaised: 0,
              percentageComplete: 0,
            },
          ],
          launchDate: null,
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 jours avant
          updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 jour avant
          status: "fundraising",
          totalRaised: 28.5,
          participants: 42,
          marketCap: 0,
          liquidityValue: 0,
          liquidityPair: null,
          dexListings: [],
          transactions: [
            {
              type: "contribution",
              hash: "tx_abc123",
              date: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 heure avant
              amount: 0.5,
              status: "confirmed",
            },
            {
              type: "contribution",
              hash: "tx_def456",
              date: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 heures avant
              amount: 1.2,
              status: "confirmed",
            },
            {
              type: "contribution",
              hash: "tx_ghi789",
              date: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 heures avant
              amount: 0.8,
              status: "confirmed",
            },
          ],
        }

        setLaunchStatus(mockLaunchStatus)
      } catch (error) {
        console.error("Error fetching launch status:", error)
        toast({
          title: "Error",
          description: "Failed to load launch status. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchLaunchStatus()

    // Rafraîchir les données toutes les 30 secondes
    const interval = setInterval(fetchLaunchStatus, 30000)
    return () => clearInterval(interval)
  }, [launchId, toast])

  // Formater les dates
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Calculer le temps restant
  const calculateTimeRemaining = (endDate: Date) => {
    const now = new Date()
    const diff = new Date(endDate).getTime() - now.getTime()

    if (diff <= 0) return "Terminé"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    return `${days}j ${hours}h ${minutes}m`
  }

  // Gérer la contribution
  const handleContribute = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to contribute",
        variant: "destructive",
      })
      return
    }

    if (!contributionAmount || Number.parseFloat(contributionAmount) <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid contribution amount",
        variant: "destructive",
      })
      return
    }

    const amount = Number.parseFloat(contributionAmount)

    if (!launchStatus?.currentPhase) {
      toast({
        title: "No active phase",
        description: "There is no active phase to contribute to",
        variant: "destructive",
      })
      return
    }

    if (amount < launchStatus.currentPhase.minContribution) {
      toast({
        title: "Amount too low",
        description: `Minimum contribution is ${launchStatus.currentPhase.minContribution} SOL`,
        variant: "destructive",
      })
      return
    }

    if (amount > launchStatus.currentPhase.maxContribution) {
      toast({
        title: "Amount too high",
        description: `Maximum contribution is ${launchStatus.currentPhase.maxContribution} SOL`,
        variant: "destructive",
      })
      return
    }

    setIsContributing(true)

    try {
      // Simuler une contribution
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Contribution successful",
        description: `You have successfully contributed ${amount} SOL`,
      })

      // Mettre à jour les données localement
      if (launchStatus) {
        const updatedLaunchStatus = { ...launchStatus }

        // Mettre à jour la phase actuelle
        if (updatedLaunchStatus.currentPhase) {
          updatedLaunchStatus.currentPhase.amountRaised += amount
          updatedLaunchStatus.currentPhase.participants += 1
          updatedLaunchStatus.currentPhase.percentageComplete =
            (updatedLaunchStatus.currentPhase.amountRaised / updatedLaunchStatus.currentPhase.targetAmount) * 100
        }

        // Mettre à jour les phases
        updatedLaunchStatus.phases = updatedLaunchStatus.phases.map((phase) => {
          if (phase.id === updatedLaunchStatus.currentPhase?.id) {
            return { ...updatedLaunchStatus.currentPhase }
          }
          return phase
        })

        // Mettre à jour les totaux
        updatedLaunchStatus.totalRaised += amount
        updatedLaunchStatus.participants += 1

        // Ajouter la transaction
        updatedLaunchStatus.transactions.unshift({
          type: "contribution",
          hash: `tx_${Math.random().toString(36).substring(2, 15)}`,
          date: new Date(),
          amount,
          status: "confirmed",
        })

        setLaunchStatus(updatedLaunchStatus)
      }

      setContributionAmount("")
    } catch (error) {
      console.error("Error contributing:", error)
      toast({
        title: "Contribution failed",
        description: "Failed to process your contribution. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsContributing(false)
    }
  }

  // Gérer le lancement du token
  const handleLaunch = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to launch the token",
        variant: "destructive",
      })
      return
    }

    if (!launchStatus) {
      toast({
        title: "No launch data",
        description: "Launch data is not available",
        variant: "destructive",
      })
      return
    }

    // Vérifier si l'utilisateur est le propriétaire
    if (publicKey.toString() !== launchStatus.ownerAddress) {
      toast({
        title: "Not authorized",
        description: "Only the owner can launch this token",
        variant: "destructive",
      })
      return
    }

    setIsLaunching(true)

    try {
      // Simuler un lancement
      await new Promise((resolve) => setTimeout(resolve, 3000))

      toast({
        title: "Token launched",
        description: "Your token has been successfully launched on DEX",
      })

      // Mettre à jour les données localement
      if (launchStatus) {
        const updatedLaunchStatus = { ...launchStatus }

        // Mettre à jour le statut
        updatedLaunchStatus.status = "launched"
        updatedLaunchStatus.launchDate = new Date()

        // Ajouter le listing DEX
        updatedLaunchStatus.liquidityPair = `pair_${Math.random().toString(36).substring(2, 15)}`
        updatedLaunchStatus.liquidityValue = updatedLaunchStatus.totalRaised * 0.7 // 70% de la levée va en liquidité

        // Ajouter les listings DEX
        updatedLaunchStatus.dexListings = [
          {
            dex: "Raydium",
            pairAddress: `pair_raydium_${Math.random().toString(36).substring(2, 10)}`,
            listingDate: new Date(),
            initialPrice: launchStatus.config.initialPrice * launchStatus.config.listingMultiplier,
            currentPrice: launchStatus.config.initialPrice * launchStatus.config.listingMultiplier,
            volume24h: 0,
          },
          {
            dex: "Jupiter",
            pairAddress: `pair_jupiter_${Math.random().toString(36).substring(2, 10)}`,
            listingDate: new Date(),
            initialPrice: launchStatus.config.initialPrice * launchStatus.config.listingMultiplier,
            currentPrice: launchStatus.config.initialPrice * launchStatus.config.listingMultiplier,
            volume24h: 0,
          },
        ]

        // Calculer la capitalisation boursière initiale
        updatedLaunchStatus.marketCap =
          launchStatus.config.totalSupply * launchStatus.config.initialPrice * launchStatus.config.listingMultiplier

        // Ajouter la transaction de lancement
        updatedLaunchStatus.transactions.unshift({
          type: "launch",
          hash: `tx_${Math.random().toString(36).substring(2, 15)}`,
          date: new Date(),
          amount: updatedLaunchStatus.liquidityValue,
          status: "confirmed",
        })

        setLaunchStatus(updatedLaunchStatus)
      }
    } catch (error) {
      console.error("Error launching token:", error)
      toast({
        title: "Launch failed",
        description: "Failed to launch your token. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLaunching(false)
    }
  }

  // Copier l'adresse du token
  const copyTokenAddress = () => {
    if (launchStatus?.tokenAddress) {
      navigator.clipboard.writeText(launchStatus.tokenAddress)
      toast({
        title: "Address copied",
        description: "Token address copied to clipboard",
      })
    }
  }

  // Partager le lancement
  const shareTokenLaunch = (platform: string) => {
    if (!launchStatus) return

    const shareUrl = `https://yourplatform.com/token-quantum/launch/${launchId}`
    const shareText = `Check out ${launchStatus.config.name} (${launchStatus.config.symbol}${launchStatus.config.suffix}) token launch!`

    let url = ""
    switch (platform) {
      case "twitter":
        url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`
        break
      case "telegram":
        url = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`
        break
      default:
        navigator.clipboard.writeText(`${shareText} ${shareUrl}`)
        toast({
          title: "Link copied",
          description: "Share link copied to clipboard",
        })
        return
    }

    window.open(url, "_blank")
    setShowShareDialog(false)
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#D4AF37]"></div>
        <p className="mt-4 text-muted-foreground">Loading launch dashboard...</p>
      </div>
    )
  }

  if (!launchStatus) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-xl font-bold mb-2">Launch Not Found</h3>
        <p className="text-muted-foreground mb-4">The requested token launch could not be found.</p>
        <Button onClick={() => router.push("/token-quantum")}>Create New Launch</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête du lancement */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold">{launchStatus.config.name}</h1>
            <Badge className="bg-[#D4AF37] text-black">Quantum</Badge>
            {launchStatus.status === "launched" && <Badge className="bg-green-100 text-green-800">Launched</Badge>}
            {launchStatus.status === "fundraising" && <Badge className="bg-blue-100 text-blue-800">Fundraising</Badge>}
            {launchStatus.status === "setup" && <Badge className="bg-yellow-100 text-yellow-800">Setup</Badge>}
            {launchStatus.status === "failed" && <Badge className="bg-red-100 text-red-800">Failed</Badge>}
          </div>
          <p className="text-muted-foreground">
            {launchStatus.config.symbol}
            {launchStatus.config.suffix} • Created on {new Date(launchStatus.createdAt).toLocaleDateString()}
          </p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowShareDialog(true)}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>

          {publicKey?.toString() === launchStatus.ownerAddress && (
            <Button variant="outline" size="sm" onClick={() => setShowSettingsDialog(true)}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          )}

          {launchStatus.status === "fundraising" && launchStatus.currentPhase && (
            <Button
              className="bg-[#D4AF37] hover:bg-[#B8941F] text-black"
              onClick={() => document.getElementById("contribute-section")?.scrollIntoView({ behavior: "smooth" })}
            >
              <Wallet className="h-4 w-4 mr-2" />
              Contribute
            </Button>
          )}

          {launchStatus.status === "fundraising" &&
            publicKey?.toString() === launchStatus.ownerAddress &&
            launchStatus.totalRaised >= launchStatus.config.softCap && (
              <Button onClick={handleLaunch} disabled={isLaunching}>
                {isLaunching ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Launching...
                  </>
                ) : (
                  <>
                    <Rocket className="h-4 w-4 mr-2" />
                    Launch Token
                  </>
                )}
              </Button>
            )}
        </div>
      </div>

      {/* Informations principales */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Wallet className="h-5 w-5 text-[#D4AF37] mr-2" />
                <span className="text-sm font-medium">Total Raised</span>
              </div>
              <div className="text-2xl font-bold">{launchStatus.totalRaised.toFixed(2)} SOL</div>
            </div>
            <Progress value={(launchStatus.totalRaised / launchStatus.config.hardCap) * 100} className="h-2 mt-2" />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Soft Cap: {launchStatus.config.softCap} SOL</span>
              <span>Hard Cap: {launchStatus.config.hardCap} SOL</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-[#D4AF37] mr-2" />
                <span className="text-sm font-medium">Participants</span>
              </div>
              <div className="text-2xl font-bold">{launchStatus.participants}</div>
            </div>
            <div className="h-2 mt-2"></div>
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Min Buy: {launchStatus.config.minBuy} SOL</span>
              <span>Max Buy: {launchStatus.config.maxBuy} SOL</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-[#D4AF37] mr-2" />
                <span className="text-sm font-medium">Current Phase</span>
              </div>
              <Badge variant="outline">{launchStatus.currentPhase?.name || "None"}</Badge>
            </div>
            {launchStatus.currentPhase && (
              <>
                <div className="text-2xl font-bold mt-1">
                  {calculateTimeRemaining(launchStatus.currentPhase.endDate)}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Ends on {formatDate(launchStatus.currentPhase.endDate)}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Onglets d'information */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="overview">
            <Info className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="phases">
            <Calendar className="h-4 w-4 mr-2" />
            Phases
          </TabsTrigger>
          <TabsTrigger value="transactions">
            <BarChart className="h-4 w-4 mr-2" />
            Transactions
          </TabsTrigger>
          <TabsTrigger value="tokenomics">
            <TrendingUp className="h-4 w-4 mr-2" />
            Tokenomics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Token Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Token Address:</span>
                  <div className="flex items-center">
                    <span className="font-medium font-mono text-xs">
                      {launchStatus.tokenAddress.substring(0, 8)}...
                      {launchStatus.tokenAddress.substring(launchStatus.tokenAddress.length - 8)}
                    </span>
                    <Button variant="ghost" size="sm" onClick={copyTokenAddress}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <Separator />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Symbol:</span>
                  <span className="font-medium">
                    {launchStatus.config.symbol}
                    {launchStatus.config.suffix}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Decimals:</span>
                  <span className="font-medium">{launchStatus.config.decimals}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Total Supply:</span>
                  <span className="font-medium">
                    {launchStatus.config.totalSupply.toLocaleString()} {launchStatus.config.symbol}
                    {launchStatus.config.suffix}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Initial Price:</span>
                  <span className="font-medium">${launchStatus.config.initialPrice.toFixed(6)}</span>
                </div>
                {launchStatus.status === "launched" && (
                  <>
                    <Separator />
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Current Price:</span>
                      <span className="font-medium">
                        ${(launchStatus.config.initialPrice * launchStatus.config.listingMultiplier).toFixed(6)}
                      </span>
                    </div>
                    <Separator />
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Market Cap:</span>
                      <span className="font-medium">${launchStatus.marketCap.toLocaleString()}</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Launch Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  <Badge
                    className={`
                      ${launchStatus.status === "launched" ? "bg-green-100 text-green-800" : ""}
                      ${launchStatus.status === "fundraising" ? "bg-blue-100 text-blue-800" : ""}
                      ${launchStatus.status === "setup" ? "bg-yellow-100 text-yellow-800" : ""}
                      ${launchStatus.status === "failed" ? "bg-red-100 text-red-800" : ""}
                    `}
                  >
                    {launchStatus.status.charAt(0).toUpperCase() + launchStatus.status.slice(1)}
                  </Badge>
                </div>
                <Separator />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Soft Cap:</span>
                  <span className="font-medium">{launchStatus.config.softCap} SOL</span>
                </div>
                <Separator />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Hard Cap:</span>
                  <span className="font-medium">{launchStatus.config.hardCap} SOL</span>
                </div>
                <Separator />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Liquidity %:</span>
                  <span className="font-medium">{launchStatus.config.liquidityPercentage}%</span>
                </div>
                <Separator />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Liquidity Lock:</span>
                  <span className="font-medium">{launchStatus.config.liquidityLockPeriod} days</span>
                </div>
                {launchStatus.status === "launched" && launchStatus.launchDate && (
                  <>
                    <Separator />
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Launch Date:</span>
                      <span className="font-medium">{formatDate(launchStatus.launchDate)}</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Liquidity Value:</span>
                      <span className="font-medium">{launchStatus.liquidityValue.toFixed(2)} SOL</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>

          {launchStatus.config.description && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{launchStatus.config.description}</p>
              </CardContent>
            </Card>
          )}

          {launchStatus.status === "launched" && launchStatus.dexListings.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">DEX Listings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {launchStatus.dexListings.map((listing, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center mr-3">
                          {listing.dex.charAt(0)}
                        </div>
                        <div>
                          <div className="font-medium">{listing.dex}</div>
                          <div className="text-xs text-muted-foreground">
                            Listed on {formatDate(listing.listingDate)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">${listing.currentPrice.toFixed(6)}</div>
                        <div className="text-xs text-green-500 flex items-center justify-end">
                          <ArrowUpRight className="h-3 w-3 mr-1" />
                          {((listing.currentPrice / listing.initialPrice - 1) * 100).toFixed(2)}%
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <a href={`https://dex.com/${listing.pairAddress}`} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Trade
                        </a>
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Section de contribution */}
          {launchStatus.status === "fundraising" && launchStatus.currentPhase && (
            <Card id="contribute-section">
              <CardHeader>
                <CardTitle>Contribute to {launchStatus.config.name}</CardTitle>
                <CardDescription>
                  Current phase: {launchStatus.currentPhase.name} -{launchStatus.currentPhase.amountRaised.toFixed(2)} /{" "}
                  {launchStatus.currentPhase.targetAmount} SOL raised
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <div className="text-sm font-medium">Progress</div>
                      <div className="text-sm text-muted-foreground">
                        {launchStatus.currentPhase.percentageComplete.toFixed(0)}%
                      </div>
                    </div>
                    <Progress value={launchStatus.currentPhase.percentageComplete} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <div className="text-sm font-medium">Token Price</div>
                      <div className="text-lg">${launchStatus.currentPhase.price.toFixed(6)}</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">Time Remaining</div>
                      <div className="text-lg">{calculateTimeRemaining(launchStatus.currentPhase.endDate)}</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-sm font-medium">Your Contribution (SOL)</div>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        value={contributionAmount}
                        onChange={(e) => setContributionAmount(e.target.value)}
                        placeholder={`Min: ${launchStatus.currentPhase.minContribution} SOL`}
                        className="flex-1 px-3 py-2 border rounded-md"
                        min={launchStatus.currentPhase.minContribution}
                        max={launchStatus.currentPhase.maxContribution}
                        step="0.01"
                      />
                      <Button
                        variant="outline"
                        onClick={() =>
                          setContributionAmount(launchStatus.currentPhase?.maxContribution.toString() || "")
                        }
                      >
                        Max
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Min: {launchStatus.currentPhase.minContribution} SOL | Max:{" "}
                      {launchStatus.currentPhase.maxContribution} SOL
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-sm font-medium">You will receive</div>
                    <div className="text-lg">
                      {contributionAmount
                        ? (Number.parseFloat(contributionAmount) / launchStatus.currentPhase.price).toLocaleString(
                            undefined,
                            {
                              maximumFractionDigits: 0,
                            },
                          )
                        : "0"}{" "}
                      {launchStatus.config.symbol}
                      {launchStatus.config.suffix}
                    </div>
                  </div>

                  {!connected ? (
                    <div className="flex justify-center">
                      <WalletMultiButton className="bg-[#D4AF37] hover:bg-[#B8941F] text-black rounded-md px-4 py-2" />
                    </div>
                  ) : (
                    <Button
                      className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                      onClick={handleContribute}
                      disabled={isContributing}
                    >
                      {isContributing ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                          Processing...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Contribute
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="phases" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Launch Phases</CardTitle>
              <CardDescription>Timeline and progress of each phase</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {launchStatus.phases.map((phase, index) => (
                  <div key={phase.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 
                          ${
                            phase.status === "active"
                              ? "bg-blue-100 text-blue-800"
                              : phase.status === "completed"
                                ? "bg-green-100 text-green-800"
                                : "bg-muted text-muted-foreground"
                          }`}
                        >
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{phase.name}</div>
                          <div className="text-xs text-muted-foreground">{phase.description}</div>
                        </div>
                      </div>
                      <Badge
                        className={`
                          ${phase.status === "active" ? "bg-blue-100 text-blue-800" : ""}
                          ${phase.status === "completed" ? "bg-green-100 text-green-800" : ""}
                          ${phase.status === "pending" ? "bg-yellow-100 text-yellow-800" : ""}
                          ${phase.status === "failed" ? "bg-red-100 text-red-800" : ""}
                        `}
                      >
                        {phase.status.charAt(0).toUpperCase() + phase.status.slice(1)}
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>
                          Progress: {phase.amountRaised.toFixed(2)} / {phase.targetAmount} SOL
                        </span>
                        <span>{phase.percentageComplete.toFixed(0)}%</span>
                      </div>
                      <Progress value={phase.percentageComplete} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <div className="text-muted-foreground">Start Date</div>
                        <div>{formatDate(phase.startDate)}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">End Date</div>
                        <div>{formatDate(phase.endDate)}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Token Price</div>
                        <div>${phase.price.toFixed(6)}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Participants</div>
                        <div>{phase.participants}</div>
                      </div>
                    </div>

                    {index < launchStatus.phases.length - 1 && <Separator className="my-4" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>Recent transactions for this token launch</CardDescription>
            </CardHeader>
            <CardContent>
              {launchStatus.transactions.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Type</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Transaction</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {launchStatus.transactions.map((tx, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">
                          {tx.type.charAt(0).toUpperCase() + tx.type.slice(1)}
                        </TableCell>
                        <TableCell>{formatDate(tx.date)}</TableCell>
                        <TableCell>{tx.amount.toFixed(2)} SOL</TableCell>
                        <TableCell className="font-mono text-xs">
                          {tx.hash.substring(0, 8)}...{tx.hash.substring(tx.hash.length - 8)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={`
                              ${tx.status === "confirmed" ? "bg-green-100 text-green-800" : ""}
                              ${tx.status === "pending" ? "bg-yellow-100 text-yellow-800" : ""}
                              ${tx.status === "failed" ? "bg-red-100 text-red-800" : ""}
                            `}
                          >
                            {tx.status.charAt(0).toUpperCase() + tx.status.slice(1)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8 text-muted-foreground">No transactions yet</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokenomics" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Tokenomics</CardTitle>
              <CardDescription>Token distribution and tax information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Token Distribution</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">{launchStatus.config.liquidityPercentage}%</div>
                        <p className="text-sm text-muted-foreground">Liquidity Pool</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">{launchStatus.config.teamPercentage}%</div>
                        <p className="text-sm text-muted-foreground">Team</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">{launchStatus.config.marketingPercentage}%</div>
                        <p className="text-sm text-muted-foreground">Marketing</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">{launchStatus.config.reservePercentage}%</div>
                        <p className="text-sm text-muted-foreground">Reserve</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-medium mb-4">Transaction Taxes</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <ArrowUpRight className="h-5 w-5 text-green-500 mr-2" />
                            <span className="text-sm font-medium">Buy Tax</span>
                          </div>
                          <div className="text-2xl font-bold">{launchStatus.config.buyTax}%</div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <ArrowDownRight className="h-5 w-5 text-red-500 mr-2" />
                            <span className="text-sm font-medium">Sell Tax</span>
                          </div>
                          <div className="text-2xl font-bold">{launchStatus.config.sellTax}%</div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <ArrowUpRight className="h-5 w-5 text-blue-500 mr-2" />
                            <span className="text-sm font-medium">Transfer Tax</span>
                          </div>
                          <div className="text-2xl font-bold">{launchStatus.config.transferTax}%</div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-medium mb-4">Security Features</h3>
                  <div className="flex flex-wrap gap-2">
                    {launchStatus.config.antiBot && (
                      <Badge variant="outline" className="bg-blue-50">
                        <Shield className="h-3 w-3 mr-1" />
                        Anti-Bot
                      </Badge>
                    )}
                    {launchStatus.config.antiDump && (
                      <Badge variant="outline" className="bg-purple-50">
                        <Lock className="h-3 w-3 mr-1" />
                        Anti-Dump
                      </Badge>
                    )}
                    <Badge variant="outline" className="bg-green-50">
                      <DollarSign className="h-3 w-3 mr-1" />
                      Max Wallet: {launchStatus.config.maxWalletPercentage}%
                    </Badge>
                    <Badge variant="outline" className="bg-amber-50">
                      <DollarSign className="h-3 w-3 mr-1" />
                      Max Transaction: {launchStatus.config.maxTxPercentage}%
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialogue de partage */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share Token Launch</DialogTitle>
            <DialogDescription>Share this token launch with your community</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 border rounded-md">
              <span className="text-sm font-mono truncate">
                https://yourplatform.com/token-quantum/launch/{launchId}
              </span>
              <Button variant="ghost" size="sm" onClick={() => shareTokenLaunch("copy")}>
                <Copy className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex gap-2">
              <Button className="flex-1" variant="outline" onClick={() => shareTokenLaunch("twitter")}>
                <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                </svg>
                Twitter
              </Button>
              <Button className="flex-1" variant="outline" onClick={() => shareTokenLaunch("telegram")}>
                <svg className="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.562 8.248l-1.97 9.269c-.145.658-.537.818-1.084.508l-3-2.21-1.446 1.394c-.16.16-.295.295-.605.295l.213-3.053 5.56-5.023c.242-.213-.054-.333-.373-.121l-6.871 4.326-2.962-.924c-.643-.204-.657-.643.136-.953l11.57-4.461c.538-.196 1.006.128.832.953z" />
                </svg>
                Telegram
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowShareDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialogue de paramètres */}
      <Dialog open={showSettingsDialog} onOpenChange={setShowSettingsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Launch Settings</DialogTitle>
            <DialogDescription>Manage your token launch settings</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Settings can only be modified during the setup phase.
                {launchStatus.status !== "setup" && " This launch is already in progress."}
              </AlertDescription>
            </Alert>

            {/* Ici, vous pourriez ajouter des paramètres modifiables si le statut est "setup" */}

            {launchStatus.status === "fundraising" && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Launch Actions</h3>
                <div className="flex gap-2">
                  <Button
                    className="flex-1"
                    onClick={handleLaunch}
                    disabled={isLaunching || launchStatus.totalRaised < launchStatus.config.softCap}
                  >
                    {isLaunching ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Launching...
                      </>
                    ) : (
                      <>
                        <Rocket className="h-4 w-4 mr-2" />
                        Launch Token
                      </>
                    )}
                  </Button>

                  <Button className="flex-1" variant="outline">
                    <MoreHorizontal className="h-4 w-4 mr-2" />
                    More Options
                  </Button>
                </div>

                {launchStatus.totalRaised < launchStatus.config.softCap && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Soft cap not reached. You need to raise at least {launchStatus.config.softCap} SOL to launch.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSettingsDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

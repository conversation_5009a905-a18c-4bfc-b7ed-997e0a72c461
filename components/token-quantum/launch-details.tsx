"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import {
  Rocket,
  Clock,
  Users,
  DollarSign,
  TrendingUp,
  Lock,
  ExternalLink,
  Calendar,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Share2,
  Copy,
  Twitter,
  MessageCircle,
  Globe,
  Wallet,
} from "lucide-react"
import type { TokenLaunchStatus } from "@/lib/quantum-launch-service"

interface LaunchDetailsProps {
  launchId: string
}

export default function LaunchDetails({ launchId }: LaunchDetailsProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [launch, setLaunch] = useState<TokenLaunchStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const [contributionAmount, setContributionAmount] = useState("")
  const [isContributing, setIsContributing] = useState(false)

  useEffect(() => {
    fetchLaunchDetails()
  }, [launchId])

  const fetchLaunchDetails = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/quantum/launches/${launchId}`)
      const data = await response.json()

      if (data.success) {
        setLaunch(data.data)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch launch details",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching launch details:", error)
      toast({
        title: "Error",
        description: "Failed to fetch launch details. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleContribute = async () => {
    if (!launch?.currentPhase) return

    setIsContributing(true)
    try {
      // Simuler une contribution
      await new Promise((resolve) => setTimeout(resolve, 1500))

      toast({
        title: "Contribution Successful",
        description: `You have successfully contributed ${contributionAmount} SOL to ${launch.config.name}.`,
      })

      // Rafraîchir les détails du lancement
      fetchLaunchDetails()
      setContributionAmount("")
    } catch (error) {
      console.error("Error contributing:", error)
      toast({
        title: "Error",
        description: "Failed to process contribution. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsContributing(false)
    }
  }

  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied!",
      description: message,
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "setup":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Clock className="mr-1 h-3 w-3" />
            Upcoming
          </Badge>
        )
      case "fundraising":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <DollarSign className="mr-1 h-3 w-3" />
            Active
          </Badge>
        )
      case "preparing":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Preparing
          </Badge>
        )
      case "launched":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <Rocket className="mr-1 h-3 w-3" />
            Launched
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date))
  }

  const calculateTimeRemaining = (endDate: Date) => {
    const now = new Date()
    const end = new Date(endDate)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return "Ended"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    return `${days}d ${hours}h ${minutes}m`
  }

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center gap-4">
          <Skeleton className="h-16 w-16 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>

        <Skeleton className="h-10 w-full" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    )
  }

  if (!launch) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>Launch not found or has been removed.</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage
              src={`/abstract-geometric-shapes.png?height=64&width=64&query=${launch.config.name}`}
              alt={launch.config.name}
            />
            <AvatarFallback>{launch.config.symbol.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">{launch.config.name}</h1>
              {getStatusBadge(launch.status)}
            </div>
            <p className="text-muted-foreground">
              {launch.config.symbol}
              {launch.config.suffix}
            </p>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => copyToClipboard(window.location.href, "Launch URL copied to clipboard")}
          >
            <Share2 className="mr-1 h-4 w-4" />
            Share
          </Button>
          {launch.config.website && (
            <Button variant="outline" size="sm" asChild>
              <Link href={launch.config.website} target="_blank" rel="noopener noreferrer">
                <Globe className="mr-1 h-4 w-4" />
                Website
              </Link>
            </Button>
          )}
          {launch.config.twitter && (
            <Button variant="outline" size="sm" asChild>
              <Link href={launch.config.twitter} target="_blank" rel="noopener noreferrer">
                <Twitter className="mr-1 h-4 w-4" />
                Twitter
              </Link>
            </Button>
          )}
          {launch.config.telegram && (
            <Button variant="outline" size="sm" asChild>
              <Link href={launch.config.telegram} target="_blank" rel="noopener noreferrer">
                <MessageCircle className="mr-1 h-4 w-4" />
                Telegram
              </Link>
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tokenomics">Tokenomics</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Launch Information</CardTitle>
                <CardDescription>Current status and progress of the token launch</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Description</p>
                  <p>{launch.config.description}</p>
                </div>

                {launch.currentPhase && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{launch.currentPhase.name}</Badge>
                        <span className="text-sm text-muted-foreground">
                          Ends in {calculateTimeRemaining(launch.currentPhase.endDate)}
                        </span>
                      </div>
                      <span className="text-sm font-medium">{launch.currentPhase.percentageComplete}%</span>
                    </div>
                    <Progress value={launch.currentPhase.percentageComplete} className="h-2" />
                    <div className="flex justify-between text-sm">
                      <span>
                        {launch.currentPhase.amountRaised} / {launch.currentPhase.targetAmount} SOL
                      </span>
                      <span>{launch.participants} participants</span>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Token Price</p>
                    <p className="font-medium">${launch.currentPhase?.price || launch.config.initialPrice}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Listing Price</p>
                    <p className="font-medium">
                      ${(launch.config.initialPrice * launch.config.listingMultiplier).toFixed(6)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Soft Cap</p>
                    <p className="font-medium">{launch.config.softCap} SOL</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Hard Cap</p>
                    <p className="font-medium">{launch.config.hardCap} SOL</p>
                  </div>
                </div>

                {launch.status === "launched" && launch.dexListings && launch.dexListings.length > 0 && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">DEX Listings</p>
                    <div className="space-y-2">
                      {launch.dexListings.map((listing) => (
                        <div key={listing.id} className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <span>{listing.dex}</span>
                            <Badge variant="outline" className="text-xs">
                              ${listing.currentPrice.toFixed(6)}
                            </Badge>
                          </div>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`https://solscan.io/token/${launch.tokenAddress}`} target="_blank">
                              <ExternalLink className="h-3 w-3" />
                            </Link>
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {launch.status === "fundraising" && launch.currentPhase && (
              <Card>
                <CardHeader>
                  <CardTitle>Contribute</CardTitle>
                  <CardDescription>Participate in the {launch.currentPhase.name}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm">
                      Current price: ${launch.currentPhase.price} per {launch.config.symbol}
                      {launch.config.suffix}
                    </p>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-muted-foreground">Min Contribution</p>
                        <p className="font-medium">{launch.currentPhase.minContribution} SOL</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Max Contribution</p>
                        <p className="font-medium">{launch.currentPhase.maxContribution} SOL</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="amount" className="text-sm font-medium">
                      Amount (SOL)
                    </label>
                    <div className="flex gap-2">
                      <input
                        id="amount"
                        type="number"
                        value={contributionAmount}
                        onChange={(e) => setContributionAmount(e.target.value)}
                        min={launch.currentPhase.minContribution}
                        max={launch.currentPhase.maxContribution}
                        step="0.01"
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholder="Enter amount"
                      />
                      <Button
                        onClick={() => setContributionAmount(launch.currentPhase!.maxContribution.toString())}
                        variant="outline"
                      >
                        Max
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">You will receive approximately:</p>
                    <p className="font-medium">
                      {contributionAmount
                        ? (Number(contributionAmount) / launch.currentPhase.price).toLocaleString(undefined, {
                            maximumFractionDigits: 0,
                          })
                        : "0"}{" "}
                      {launch.config.symbol}
                      {launch.config.suffix}
                    </p>
                  </div>

                  <Button
                    onClick={handleContribute}
                    disabled={
                      isContributing ||
                      !contributionAmount ||
                      Number(contributionAmount) < launch.currentPhase.minContribution ||
                      Number(contributionAmount) > launch.currentPhase.maxContribution
                    }
                    className="w-full"
                  >
                    {isContributing ? (
                      <>
                        <Wallet className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Wallet className="mr-2 h-4 w-4" />
                        Contribute
                      </>
                    )}
                  </Button>
                </CardContent>
                <CardFooter className="flex flex-col items-start">
                  <p className="text-xs text-muted-foreground">
                    By contributing, you agree to the terms and conditions of this token launch.
                  </p>
                </CardFooter>
              </Card>
            )}

            {launch.status === "launched" && (
              <Card>
                <CardHeader>
                  <CardTitle>Market Information</CardTitle>
                  <CardDescription>Current market data for {launch.config.symbol}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Market Cap</p>
                      <p className="font-medium">${launch.marketCap.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Liquidity</p>
                      <p className="font-medium">{launch.liquidityValue.toFixed(2)} SOL</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Initial Price</p>
                      <p className="font-medium">${launch.config.initialPrice.toFixed(6)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Current Price</p>
                      <p className="font-medium">${launch.dexListings[0]?.currentPrice.toFixed(6) || "N/A"}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground mb-2">Token Address</p>
                    <div className="flex items-center gap-2 bg-muted p-2 rounded-md">
                      <code className="text-xs truncate flex-1">{launch.tokenAddress}</code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(launch.tokenAddress, "Token address copied to clipboard")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex flex-col gap-2">
                    <Button variant="outline" asChild>
                      <Link href={`https://solscan.io/token/${launch.tokenAddress}`} target="_blank">
                        <ExternalLink className="mr-2 h-4 w-4" />
                        View on Solscan
                      </Link>
                    </Button>
                    <Button variant="outline" asChild>
                      <Link
                        href={`https://jup.ag/swap/SOL-${launch.config.symbol}${launch.config.suffix}`}
                        target="_blank"
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Trade on Jupiter
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Launch Schedule</CardTitle>
              <CardDescription>Timeline of the token launch phases</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {launch.phases.map((phase, index) => (
                  <div key={phase.id} className="flex gap-4">
                    <div className="flex flex-col items-center">
                      <div
                        className={`rounded-full p-2 ${
                          phase.status === "completed"
                            ? "bg-green-100 text-green-700"
                            : phase.status === "active"
                              ? "bg-blue-100 text-blue-700"
                              : "bg-gray-100 text-gray-500"
                        }`}
                      >
                        {phase.status === "completed" ? (
                          <CheckCircle2 className="h-4 w-4" />
                        ) : phase.status === "active" ? (
                          <Clock className="h-4 w-4" />
                        ) : (
                          <Calendar className="h-4 w-4" />
                        )}
                      </div>
                      {index < launch.phases.length - 1 && <div className="w-0.5 bg-gray-200 h-full mt-2"></div>}
                    </div>
                    <div className="flex-1 pb-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{phase.name}</h4>
                        <Badge
                          variant={
                            phase.status === "completed"
                              ? "success"
                              : phase.status === "active"
                                ? "default"
                                : "secondary"
                          }
                        >
                          {phase.status === "completed"
                            ? "Completed"
                            : phase.status === "active"
                              ? "Active"
                              : "Pending"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{phase.description}</p>
                      <div className="flex flex-wrap gap-x-4 gap-y-2 mt-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Start:</span> {formatDate(phase.startDate)}
                        </div>
                        <div>
                          <span className="text-muted-foreground">End:</span> {formatDate(phase.endDate)}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Price:</span> ${phase.price}
                        </div>
                      </div>
                      {(phase.status === "completed" || phase.status === "active") && (
                        <div className="mt-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{phase.percentageComplete}%</span>
                          </div>
                          <Progress value={phase.percentageComplete} className="h-2 mt-1" />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>
                              {phase.amountRaised} / {phase.targetAmount} SOL
                            </span>
                            <span>{phase.participants} participants</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokenomics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Token Distribution</CardTitle>
              <CardDescription>Allocation of tokens across different categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Token Information</h4>
                    <div className="grid grid-cols-2 gap-y-2">
                      <div className="text-sm text-muted-foreground">Name</div>
                      <div className="text-sm font-medium">{launch.config.name}</div>
                      <div className="text-sm text-muted-foreground">Symbol</div>
                      <div className="text-sm font-medium">
                        {launch.config.symbol}
                        {launch.config.suffix}
                      </div>
                      <div className="text-sm text-muted-foreground">Decimals</div>
                      <div className="text-sm font-medium">{launch.config.decimals}</div>
                      <div className="text-sm text-muted-foreground">Total Supply</div>
                      <div className="text-sm font-medium">{launch.config.totalSupply.toLocaleString()}</div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Distribution</h4>
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <DollarSign className="h-4 w-4 mr-1 text-blue-500" />
                            Liquidity
                          </span>
                          <span>{launch.config.liquidityPercentage}%</span>
                        </div>
                        <Progress value={launch.config.liquidityPercentage} className="h-2 mt-1" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <Users className="h-4 w-4 mr-1 text-green-500" />
                            Team
                          </span>
                          <span>{launch.config.teamPercentage}%</span>
                        </div>
                        <Progress value={launch.config.teamPercentage} className="h-2 mt-1" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <TrendingUp className="h-4 w-4 mr-1 text-purple-500" />
                            Marketing
                          </span>
                          <span>{launch.config.marketingPercentage}%</span>
                        </div>
                        <Progress value={launch.config.marketingPercentage} className="h-2 mt-1" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm">
                          <span className="flex items-center">
                            <Lock className="h-4 w-4 mr-1 text-amber-500" />
                            Reserve
                          </span>
                          <span>{launch.config.reservePercentage}%</span>
                        </div>
                        <Progress value={launch.config.reservePercentage} className="h-2 mt-1" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Vesting & Locking</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Liquidity Lock</span>
                        <span className="text-sm font-medium">{launch.config.liquidityLockPeriod} days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Team Tokens Lock</span>
                        <span className="text-sm font-medium">{launch.config.teamLockPeriod} days</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Tokenomics</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Initial Market Cap</span>
                        <span className="text-sm font-medium">
                          $
                          {(
                            launch.config.totalSupply *
                            launch.config.initialPrice *
                            launch.config.listingMultiplier
                          ).toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Initial Liquidity</span>
                        <span className="text-sm font-medium">
                          ${((launch.config.hardCap * launch.config.liquidityPercentage) / 100).toLocaleString()} (
                          {launch.config.liquidityPercentage}% of raised)
                        </span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Tax Structure</h4>
                    <div className="grid grid-cols-2 gap-y-2">
                      <div className="text-sm text-muted-foreground">Buy Tax</div>
                      <div className="text-sm font-medium">{launch.config.buyTax}%</div>
                      <div className="text-sm text-muted-foreground">Sell Tax</div>
                      <div className="text-sm font-medium">{launch.config.sellTax}%</div>
                      <div className="text-sm text-muted-foreground">Transfer Tax</div>
                      <div className="text-sm font-medium">{launch.config.transferTax}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Features</CardTitle>
              <CardDescription>Protection mechanisms implemented for this token</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Anti-Bot Protection</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Status</span>
                      <Badge variant={launch.config.antiBot ? "success" : "outline"}>
                        {launch.config.antiBot ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Prevents bots from sniping tokens at launch by implementing transaction limits and timing
                      mechanisms.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Anti-Dump Protection</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Status</span>
                      <Badge variant={launch.config.antiDump ? "success" : "outline"}>
                        {launch.config.antiDump ? "Enabled" : "Disabled"}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Prevents large holders from dumping tokens all at once by implementing selling limits.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Transaction Limits</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Max Wallet Size</span>
                        <span className="text-sm font-medium">{launch.config.maxWalletPercentage}% of supply</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Max Transaction Size</span>
                        <span className="text-sm font-medium">{launch.config.maxTxPercentage}% of supply</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mt-2">
                      Limits the maximum amount of tokens that can be held or transferred in a single transaction to
                      prevent market manipulation.
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Liquidity Lock</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Duration</span>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        <Lock className="mr-1 h-3 w-3" />
                        {launch.config.liquidityLockPeriod} days
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Liquidity tokens are locked for {launch.config.liquidityLockPeriod} days to ensure trading can
                      continue and prevent rug pulls.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Team Token Lock</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Duration</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        <Lock className="mr-1 h-3 w-3" />
                        {launch.config.teamLockPeriod} days
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Team tokens are locked for {launch.config.teamLockPeriod} days to demonstrate long-term commitment
                      to the project.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Contract Verification</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Status</span>
                      <Badge variant="success">Verified</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      The token contract has been verified on Solscan, allowing anyone to inspect the code.
                    </p>
                    {launch.tokenAddress && (
                      <Button variant="outline" size="sm" className="mt-2" asChild>
                        <Link href={`https://solscan.io/token/${launch.tokenAddress}`} target="_blank">
                          <ExternalLink className="mr-2 h-4 w-4" />
                          View on Solscan
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>Recent transactions related to this token launch</CardDescription>
            </CardHeader>
            <CardContent>
              {launch.transactions && launch.transactions.length > 0 ? (
                <div className="space-y-4">
                  {launch.transactions.map((tx) => (
                    <div key={tx.id} className="flex items-center justify-between p-3 bg-muted rounded-md">
                      <div className="flex items-center gap-3">
                        <div
                          className={`rounded-full p-2 ${
                            tx.type === "contribution"
                              ? "bg-green-100 text-green-700"
                              : tx.type === "listing"
                                ? "bg-purple-100 text-purple-700"
                                : "bg-blue-100 text-blue-700"
                          }`}
                        >
                          {tx.type === "contribution" ? (
                            <Wallet className="h-4 w-4" />
                          ) : tx.type === "listing" ? (
                            <Rocket className="h-4 w-4" />
                          ) : (
                            <TrendingUp className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium capitalize">{tx.type}</p>
                          <p className="text-xs text-muted-foreground">{new Date(tx.date).toLocaleString()}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{tx.type === "contribution" ? `${tx.amount} SOL` : tx.amount}</p>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`https://solscan.io/tx/${tx.hash}`} target="_blank">
                            <ExternalLink className="h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8">
                  <Clock className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-1">No transactions yet</h3>
                  <p className="text-muted-foreground text-center">
                    Transactions will appear here once the token launch is active.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

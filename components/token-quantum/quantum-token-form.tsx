"use client"

import type React from "react"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Info, Upload } from "lucide-react"
import { sanitizeToBase58 } from "@/lib/base58-utils"

interface QuantumTokenFormProps {
  formData: any
  onChange: (field: string, value: any) => void
  availableSuffixes: string[]
  selectedSuffix: string
  onSuffixChange: (suffix: string) => void
}

export default function QuantumTokenForm({
  formData,
  onChange,
  availableSuffixes,
  selectedSuffix,
  onSuffixChange,
}: QuantumTokenFormProps) {
  const [logoPreview, setLogoPreview] = useState<string | null>(null)

  const handleSymbolChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Sanitize the symbol to ensure it's base58 compatible
    const sanitizedSymbol = sanitizeToBase58(e.target.value.toUpperCase())
    onChange("symbol", sanitizedSymbol)
  }

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      onChange("logo", file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setLogoPreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div className="space-y-6">
      <Alert className="bg-blue-50 text-blue-800 border-blue-200">
        <Info className="h-4 w-4 text-blue-600" />
        <AlertDescription>
          Les tokens Quantum utilisent un suffixe fixé par l'administrateur pour garantir leur authenticité et leur
          sécurité.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Nom du token</Label>
          <Input
            id="name"
            placeholder="Ex: Global Finance"
            value={formData.name}
            onChange={(e) => onChange("name", e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="symbol">Symbole du token</Label>
          <div className="flex items-center space-x-2">
            <Input
              id="symbol"
              placeholder="Ex: GF"
              value={formData.symbol}
              onChange={handleSymbolChange}
              className="uppercase"
              maxLength={5}
            />
            <Select value={selectedSuffix} onValueChange={onSuffixChange}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Suffixe" />
              </SelectTrigger>
              <SelectContent>
                {availableSuffixes.map((suffix) => (
                  <SelectItem key={suffix} value={suffix}>
                    {suffix}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Le symbole final sera: {formData.symbol}
            {selectedSuffix}
          </p>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="Décrivez votre token et son utilité..."
            value={formData.description}
            onChange={(e) => onChange("description", e.target.value)}
            rows={4}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="supply">Offre totale</Label>
            <Input
              id="supply"
              type="number"
              placeholder="1000000000"
              value={formData.supply}
              onChange={(e) => onChange("supply", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="decimals">Décimales</Label>
            <Select value={formData.decimals} onValueChange={(value) => onChange("decimals", value)}>
              <SelectTrigger id="decimals">
                <SelectValue placeholder="Sélectionner" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="6">6</SelectItem>
                <SelectItem value="9">9</SelectItem>
                <SelectItem value="12">12</SelectItem>
                <SelectItem value="18">18</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="logo">Logo du token</Label>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="border-2 border-dashed rounded-lg p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors">
                <input type="file" id="logo" className="hidden" accept="image/*" onChange={handleLogoChange} />
                <label htmlFor="logo" className="cursor-pointer flex flex-col items-center">
                  <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                  <span className="text-sm font-medium">Cliquez pour télécharger</span>
                  <span className="text-xs text-muted-foreground">PNG, JPG (max 2MB)</span>
                </label>
              </div>
            </div>

            {logoPreview && (
              <div className="w-16 h-16 rounded-full overflow-hidden border">
                <img
                  src={logoPreview || "/placeholder.svg"}
                  alt="Logo preview"
                  className="w-full h-full object-cover"
                />
              </div>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium">Liens sociaux</h3>

          <div>
            <Label htmlFor="website">Site web</Label>
            <Input
              id="website"
              placeholder="https://votre-site.com"
              value={formData.website}
              onChange={(e) => onChange("website", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="twitter">Twitter</Label>
            <Input
              id="twitter"
              placeholder="https://twitter.com/votre-compte"
              value={formData.twitter}
              onChange={(e) => onChange("twitter", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="telegram">Telegram</Label>
            <Input
              id="telegram"
              placeholder="https://t.me/votre-groupe"
              value={formData.telegram}
              onChange={(e) => onChange("telegram", e.target.value)}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

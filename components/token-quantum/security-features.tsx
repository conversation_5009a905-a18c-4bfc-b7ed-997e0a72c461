"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Shield, Lock, Ban, AlertTriangle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface SecurityData {
  antiBot: boolean
  antiDump: boolean
  maxWalletPercentage: number
  maxTransactionPercentage: number
  buyTax: number
  sellTax: number
  liquidityLockPeriod: number
  renounceOwnership: boolean
  auditEnabled: boolean
  kycEnabled: boolean
}

interface SecurityFeaturesProps {
  data: SecurityData
  onChange: (data: Partial<SecurityData>) => void
}

export function SecurityFeatures({ data, onChange }: SecurityFeaturesProps) {
  const [activeTab, setActiveTab] = useState("protection")

  // Gérer les changements de switch
  const handleSwitchChange = (name: keyof SecurityData, checked: boolean) => {
    onChange({ [name]: checked })
  }

  // Gérer les changements de slider
  const handleSliderChange = (name: keyof SecurityData, value: number[]) => {
    onChange({ [name]: value[0] })
  }

  // Gérer les changements d'input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    onChange({ [name as keyof SecurityData]: Number.parseFloat(value) || 0 })
  }

  // Gérer les changements de select
  const handleSelectChange = (name: keyof SecurityData, value: string) => {
    onChange({ [name]: Number.parseInt(value) || 0 })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fonctionnalités de sécurité</CardTitle>
        <CardDescription>Configurez les protections et les taxes de votre token</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="protection">Protection</TabsTrigger>
            <TabsTrigger value="taxes">Taxes</TabsTrigger>
            <TabsTrigger value="audit">Audit & KYC</TabsTrigger>
          </TabsList>

          <TabsContent value="protection" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-blue-500" />
                  <Label htmlFor="antiBot" className="cursor-pointer">
                    Protection Anti-Bot
                  </Label>
                </div>
                <Switch
                  id="antiBot"
                  checked={data.antiBot}
                  onCheckedChange={(checked) => handleSwitchChange("antiBot", checked)}
                />
              </div>
              {data.antiBot && (
                <p className="text-xs text-muted-foreground pl-6">
                  Empêche les bots de faire des transactions massives lors du lancement
                </p>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Ban className="h-4 w-4 text-blue-500" />
                  <Label htmlFor="antiDump" className="cursor-pointer">
                    Protection Anti-Dump
                  </Label>
                </div>
                <Switch
                  id="antiDump"
                  checked={data.antiDump}
                  onCheckedChange={(checked) => handleSwitchChange("antiDump", checked)}
                />
              </div>
              {data.antiDump && (
                <p className="text-xs text-muted-foreground pl-6">
                  Limite les ventes massives pour éviter les chutes brutales de prix
                </p>
              )}

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="maxWalletPercentage">Limite par wallet (% de l'offre totale)</Label>
                  <span className="text-sm text-muted-foreground">{data.maxWalletPercentage}%</span>
                </div>
                <Slider
                  id="maxWalletPercentage"
                  min={0.1}
                  max={10}
                  step={0.1}
                  value={[data.maxWalletPercentage]}
                  onValueChange={(value) => handleSliderChange("maxWalletPercentage", value)}
                />
                <p className="text-xs text-muted-foreground">
                  Limite la quantité de tokens qu'un seul wallet peut détenir
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="maxTransactionPercentage">Limite par transaction (% de l'offre totale)</Label>
                  <span className="text-sm text-muted-foreground">{data.maxTransactionPercentage}%</span>
                </div>
                <Slider
                  id="maxTransactionPercentage"
                  min={0.1}
                  max={5}
                  step={0.1}
                  value={[data.maxTransactionPercentage]}
                  onValueChange={(value) => handleSliderChange("maxTransactionPercentage", value)}
                />
                <p className="text-xs text-muted-foreground">
                  Limite la taille des transactions pour éviter les manipulations de prix
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="liquidityLockPeriod">Période de verrouillage de la liquidité</Label>
                <Select
                  value={data.liquidityLockPeriod.toString()}
                  onValueChange={(value) => handleSelectChange("liquidityLockPeriod", value)}
                >
                  <SelectTrigger id="liquidityLockPeriod">
                    <SelectValue placeholder="Sélectionnez une période" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 jours</SelectItem>
                    <SelectItem value="90">90 jours</SelectItem>
                    <SelectItem value="180">180 jours</SelectItem>
                    <SelectItem value="365">1 an</SelectItem>
                    <SelectItem value="730">2 ans</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Verrouille la liquidité pour une période définie pour rassurer les investisseurs
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-blue-500" />
                  <Label htmlFor="renounceOwnership" className="cursor-pointer">
                    Renoncer à la propriété
                  </Label>
                </div>
                <Switch
                  id="renounceOwnership"
                  checked={data.renounceOwnership}
                  onCheckedChange={(checked) => handleSwitchChange("renounceOwnership", checked)}
                />
              </div>
              {data.renounceOwnership && (
                <Alert className="mt-2">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Attention: Renoncer à la propriété signifie que vous ne pourrez plus modifier le contrat ou utiliser
                    les fonctions d'administration.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="taxes" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="buyTax">Taxe d'achat (%)</Label>
                  <span className="text-sm text-muted-foreground">{data.buyTax}%</span>
                </div>
                <Slider
                  id="buyTax"
                  min={0}
                  max={15}
                  step={0.5}
                  value={[data.buyTax]}
                  onValueChange={(value) => handleSliderChange("buyTax", value)}
                />
                <p className="text-xs text-muted-foreground">
                  Taxe appliquée lors de l'achat du token (recommandé: 2-5%)
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="sellTax">Taxe de vente (%)</Label>
                  <span className="text-sm text-muted-foreground">{data.sellTax}%</span>
                </div>
                <Slider
                  id="sellTax"
                  min={0}
                  max={15}
                  step={0.5}
                  value={[data.sellTax]}
                  onValueChange={(value) => handleSliderChange("sellTax", value)}
                />
                <p className="text-xs text-muted-foreground">
                  Taxe appliquée lors de la vente du token (recommandé: 2-7%)
                </p>
              </div>

              <div className="p-4 bg-blue-50 rounded-md mt-4">
                <h4 className="font-medium text-blue-800 mb-2">Répartition des taxes</h4>
                <p className="text-sm text-blue-700">
                  Les taxes collectées seront réparties automatiquement comme suit:
                </p>
                <ul className="text-sm text-blue-700 mt-2 space-y-1">
                  <li>• 50% pour la liquidité</li>
                  <li>• 30% pour le marketing</li>
                  <li>• 10% pour le développement</li>
                  <li>• 10% pour les récompenses aux détenteurs</li>
                </ul>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="audit" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="auditEnabled" className="cursor-pointer">
                  Audit de sécurité
                </Label>
                <Switch
                  id="auditEnabled"
                  checked={data.auditEnabled}
                  onCheckedChange={(checked) => handleSwitchChange("auditEnabled", checked)}
                />
              </div>
              {data.auditEnabled && (
                <div className="pl-6 border-l-2 border-gray-200 space-y-2">
                  <p className="text-sm">
                    Votre contrat sera audité par notre équipe de sécurité avant le déploiement pour garantir qu'il n'y
                    a pas de vulnérabilités.
                  </p>
                  <div className="p-3 bg-green-50 rounded-md">
                    <p className="text-sm text-green-800">
                      L'audit comprend: vérification du code, analyse des vulnérabilités, rapport détaillé et badge
                      d'audit pour votre site web.
                    </p>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label htmlFor="kycEnabled" className="cursor-pointer">
                  KYC (Know Your Customer)
                </Label>
                <Switch
                  id="kycEnabled"
                  checked={data.kycEnabled}
                  onCheckedChange={(checked) => handleSwitchChange("kycEnabled", checked)}
                />
              </div>
              {data.kycEnabled && (
                <div className="pl-6 border-l-2 border-gray-200 space-y-2">
                  <p className="text-sm">
                    Vous devrez compléter un processus de vérification d'identité pour renforcer la confiance des
                    investisseurs.
                  </p>
                  <div className="p-3 bg-green-50 rounded-md">
                    <p className="text-sm text-green-800">
                      Le KYC comprend: vérification d'identité, vérification d'adresse, badge KYC pour votre site web et
                      vos réseaux sociaux.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

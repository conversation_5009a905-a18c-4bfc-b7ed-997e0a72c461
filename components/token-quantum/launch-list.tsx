"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { Clock, Users, Rocket, ExternalLink } from "lucide-react"

interface LaunchListProps {
  showTabs?: boolean
  ownerAddress?: string
  limit?: number
}

export default function LaunchList({ showTabs = false, ownerAddress, limit = 10 }: LaunchListProps) {
  const router = useRouter()
  const { toast } = useToast()

  const [launches, setLaunches] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")

  useEffect(() => {
    fetchLaunches(activeTab === "all" ? undefined : activeTab)
  }, [activeTab, ownerAddress])

  const fetchLaunches = async (status?: string) => {
    setIsLoading(true)
    try {
      let url = `/api/quantum/launches?limit=${limit}`

      if (status) {
        url += `&status=${status}`
      }

      if (ownerAddress) {
        url += `&owner=${ownerAddress}`
      }

      const response = await fetch(url)
      const data = await response.json()

      if (data.success) {
        setLaunches(data.data)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch launches",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching launches:", error)
      toast({
        title: "Error",
        description: "Failed to fetch launches. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "setup":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Upcoming
          </Badge>
        )
      case "fundraising":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Active
          </Badge>
        )
      case "preparing":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Preparing
          </Badge>
        )
      case "launched":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            Launched
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const calculateTimeRemaining = (endDate: string) => {
    const now = new Date()
    const end = new Date(endDate)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return "Ended"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    return `${days}d ${hours}h ${minutes}m`
  }

  const renderLaunchCard = (launch: any) => (
    <Card key={launch.id} className="overflow-hidden">
      <CardContent className="p-0">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarImage
                  src={`/abstract-geometric-shapes.png?height=40&width=40&query=${launch.name}`}
                  alt={launch.name}
                />
                <AvatarFallback>{launch.symbol.substring(0, 2)}</AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold">{launch.name}</h3>
                  {getStatusBadge(launch.status)}
                </div>
                <p className="text-sm text-muted-foreground">
                  {launch.symbol}
                  {launch.suffix}
                </p>
              </div>
            </div>
          </div>

          {launch.current_phase_name && (
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span>{launch.current_phase_name}</span>
                <span>{launch.current_phase_percentage?.toFixed(0)}%</span>
              </div>
              <Progress value={launch.current_phase_percentage || 0} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Raised: {launch.current_phase_raised || 0} SOL</span>
                <span>Target: {launch.current_phase_target || 0} SOL</span>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{launch.participants} participants</span>
            </div>
            {launch.current_phase_status === "active" && launch.current_phase_end_date && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{calculateTimeRemaining(launch.current_phase_end_date)}</span>
              </div>
            )}
          </div>

          <div className="flex justify-between items-center">
            <div>
              <p className="text-xs text-muted-foreground">Price</p>
              <p className="font-medium">${launch.initial_price?.toFixed(6)}</p>
            </div>
            <Button asChild>
              <Link href={`/token-quantum/launch/${launch.id}`}>
                <Rocket className="mr-2 h-4 w-4" />
                View Launch
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderSkeletonCard = (index: number) => (
    <Card key={`skeleton-${index}`} className="overflow-hidden">
      <CardContent className="p-6 space-y-4">
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-12" />
          </div>
          <Skeleton className="h-2 w-full" />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-24" />
        </div>

        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <Skeleton className="h-3 w-12" />
            <Skeleton className="h-5 w-16" />
          </div>
          <Skeleton className="h-9 w-28" />
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {showTabs && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="fundraising">Active</TabsTrigger>
            <TabsTrigger value="preparing">Preparing</TabsTrigger>
            <TabsTrigger value="launched">Launched</TabsTrigger>
          </TabsList>
        </Tabs>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          Array.from({ length: limit }).map((_, index) => renderSkeletonCard(index))
        ) : launches.length > 0 ? (
          launches.map(renderLaunchCard)
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center py-12">
            <Rocket className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-1">No launches found</h3>
            <p className="text-muted-foreground mb-4">
              {activeTab === "all"
                ? "There are no token launches available at the moment."
                : `There are no ${activeTab} token launches available.`}
            </p>
            <Button asChild>
              <Link href="/token-quantum/launch/create">
                <Rocket className="mr-2 h-4 w-4" />
                Create Launch
              </Link>
            </Button>
          </div>
        )}
      </div>

      {!isLoading && launches.length > 0 && launches.length >= limit && (
        <div className="flex justify-center mt-6">
          <Button variant="outline" onClick={() => router.push("/token-quantum/launch")}>
            View All Launches
            <ExternalLink className="ml-2 h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}

"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface TokenConfigSectionProps {
  tokenName: string
  tokenSymbol: string
  tokenSupply: number
  setTokenName: (name: string) => void
  setTokenSymbol: (symbol: string) => void
  setTokenSupply: (supply: number) => void
}

export function TokenConfigSection({
  tokenName,
  tokenSymbol,
  tokenSupply,
  setTokenName,
  setTokenSymbol,
  setTokenSupply,
}: TokenConfigSectionProps) {
  return (
    <Card className="border-none shadow-none">
      <CardHeader className="px-0 pt-0">
        <CardTitle className="text-lg">Configuration de base du token</CardTitle>
        <CardDescription>Définissez les informations essentielles de votre token</CardDescription>
      </CardHeader>
      <CardContent className="px-0 space-y-4">
        <div className="space-y-2">
          <Label htmlFor="tokenName">Nom du token</Label>
          <Input
            id="tokenName"
            placeholder="Ex: Global Finance Token"
            value={tokenName}
            onChange={(e) => setTokenName(e.target.value)}
          />
          <p className="text-xs text-muted-foreground">
            Le nom complet de votre token, tel qu'il apparaîtra sur les exchanges et les wallets
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="tokenSymbol">Symbole du token</Label>
          <Input
            id="tokenSymbol"
            placeholder="Ex: GFT"
            value={tokenSymbol}
            onChange={(e) => setTokenSymbol(e.target.value)}
            maxLength={10}
          />
          <p className="text-xs text-muted-foreground">
            L'abréviation de votre token, généralement 3-5 caractères en majuscules
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="tokenSupply">Offre totale</Label>
          <Input
            id="tokenSupply"
            type="number"
            placeholder="Ex: 1000000000"
            value={tokenSupply}
            onChange={(e) => setTokenSupply(Number(e.target.value))}
            min={1}
          />
          <p className="text-xs text-muted-foreground">
            Le nombre total de tokens qui seront créés (ne peut pas être modifié après la création)
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

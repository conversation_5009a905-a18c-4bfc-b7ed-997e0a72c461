"use client"

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Shield, Lock, Ban, AlertTriangle, Check, X } from "lucide-react"

interface SecurityFeaturesDisplayProps {
  options: {
    antiBot?: boolean
    antiDump?: boolean
    antiWhale?: boolean
    lockLiquidity?: boolean
    renouncedOwnership?: boolean
  }
}

export function SecurityFeaturesDisplay({ options }: SecurityFeaturesDisplayProps) {
  // Créer un objet complet avec des valeurs par défaut pour les propriétés manquantes
  const data = {
    antiBot: options?.antiBot || false,
    antiDump: options?.antiDump || false,
    maxWalletPercentage: 5, // Valeur par défaut
    maxTransactionPercentage: 2, // Valeur par défaut
    buyTax: 5, // Valeur par défaut
    sellTax: 5, // Valeur par défaut
    liquidityLockPeriod: 180, // Valeur par défaut
    renounceOwnership: options?.renouncedOwnership || false,
    auditEnabled: false, // Valeur par défaut
    kycEnabled: false, // Valeur par défaut
  }

  // Calculer le score de sécurité (sur 100)
  const calculateSecurityScore = () => {
    let score = 0

    // Protections de base
    if (data.antiBot) score += 15
    if (data.antiDump) score += 15
    if (data.maxWalletPercentage <= 5) score += 10
    if (data.maxTransactionPercentage <= 2) score += 10

    // Taxes raisonnables
    if (data.buyTax <= 10 && data.sellTax <= 10) score += 10

    // Verrouillage de liquidité
    if (data.liquidityLockPeriod >= 365) score += 15
    else if (data.liquidityLockPeriod >= 180) score += 10
    else if (data.liquidityLockPeriod >= 90) score += 5

    // Audit et KYC
    if (data.auditEnabled) score += 15
    if (data.kycEnabled) score += 10

    return Math.min(score, 100)
  }

  const securityScore = calculateSecurityScore()

  // Déterminer la couleur du score
  const getScoreColor = () => {
    if (securityScore >= 80) return "text-green-600"
    if (securityScore >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Aperçu de la sécurité</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Score de sécurité</h3>
            <span className={`text-2xl font-bold ${getScoreColor()}`}>{securityScore}/100</span>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-blue-500" />
                <span>Protection Anti-Bot</span>
              </div>
              {data.antiBot ? <Check className="h-5 w-5 text-green-500" /> : <X className="h-5 w-5 text-red-500" />}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Ban className="h-4 w-4 text-blue-500" />
                <span>Protection Anti-Dump</span>
              </div>
              {data.antiDump ? <Check className="h-5 w-5 text-green-500" /> : <X className="h-5 w-5 text-red-500" />}
            </div>

            <div className="flex items-center justify-between">
              <span>Limite par wallet</span>
              <Badge variant="outline">{data.maxWalletPercentage}% max</Badge>
            </div>

            <div className="flex items-center justify-between">
              <span>Limite par transaction</span>
              <Badge variant="outline">{data.maxTransactionPercentage}% max</Badge>
            </div>

            <div className="flex items-center justify-between">
              <span>Taxes (achat/vente)</span>
              <Badge variant="outline">
                {data.buyTax}% / {data.sellTax}%
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Lock className="h-4 w-4 text-blue-500" />
                <span>Verrouillage de liquidité</span>
              </div>
              <Badge variant="outline">
                {data.liquidityLockPeriod === 30
                  ? "30 jours"
                  : data.liquidityLockPeriod === 90
                    ? "90 jours"
                    : data.liquidityLockPeriod === 180
                      ? "180 jours"
                      : data.liquidityLockPeriod === 365
                        ? "1 an"
                        : data.liquidityLockPeriod === 730
                          ? "2 ans"
                          : `${data.liquidityLockPeriod} jours`}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <span>Propriété renoncée</span>
              </div>
              {data.renounceOwnership ? (
                <Check className="h-5 w-5 text-green-500" />
              ) : (
                <X className="h-5 w-5 text-red-500" />
              )}
            </div>

            <div className="flex items-center justify-between">
              <span>Audit de sécurité</span>
              {data.auditEnabled ? (
                <Check className="h-5 w-5 text-green-500" />
              ) : (
                <X className="h-5 w-5 text-red-500" />
              )}
            </div>

            <div className="flex items-center justify-between">
              <span>KYC</span>
              {data.kycEnabled ? <Check className="h-5 w-5 text-green-500" /> : <X className="h-5 w-5 text-red-500" />}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface TokenPreviewProps {
  tokenName: string
  tokenSymbol: string
  tokenSupply: number
  securityOptions: {
    antiBot?: boolean
    antiDump?: boolean
    antiWhale?: boolean
    lockLiquidity?: boolean
    renouncedOwnership?: boolean
  }
  distribution: Array<{ address: string; percentage: number; label: string }>
  launchOptions: {
    launchType: string
    softCap: number
    hardCap: number
    presaleRate: number
    listingRate: number
    liquidityPercentage: number
    lockDuration: number
    startDate: Date
    endDate: Date
    launchPromotion: boolean
    telegramAnnouncement: boolean
    twitterPromotion: boolean
  }
}

export function TokenPreview({
  tokenName,
  tokenSymbol,
  tokenSupply,
  securityOptions,
  distribution,
  launchOptions,
}: TokenPreviewProps) {
  // Convertir les options de sécurité au format attendu par la fonction de calcul du score
  const securityData = {
    antiBot: securityOptions?.antiBot || false,
    antiDump: securityOptions?.antiDump || false,
    maxWalletPercentage: 5, // Valeur par défaut
    maxTransactionPercentage: 2, // Valeur par défaut
    buyTax: 5, // Valeur par défaut
    sellTax: 5, // Valeur par défaut
    liquidityLockPeriod: launchOptions?.lockDuration || 180, // Valeur par défaut basée sur lockDuration
    renounceOwnership: securityOptions?.renouncedOwnership || false,
    auditEnabled: false, // Valeur par défaut
    kycEnabled: false, // Valeur par défaut
  }

  // Calculer le score de sécurité (sur 100)
  const calculateSecurityScore = () => {
    let score = 0

    // Protections de base
    if (securityData.antiBot) score += 15
    if (securityData.antiDump) score += 15
    if (securityData.maxWalletPercentage <= 5) score += 10
    if (securityData.maxTransactionPercentage <= 2) score += 10

    // Taxes raisonnables
    if (securityData.buyTax <= 10 && securityData.sellTax <= 10) score += 10

    // Verrouillage de liquidité
    if (securityData.liquidityLockPeriod >= 365) score += 15
    else if (securityData.liquidityLockPeriod >= 180) score += 10
    else if (securityData.liquidityLockPeriod >= 90) score += 5

    // Audit et KYC
    if (securityData.auditEnabled) score += 15
    if (securityData.kycEnabled) score += 10

    return Math.min(score, 100)
  }

  const securityScore = calculateSecurityScore()

  // Déterminer la couleur du score
  const getScoreColor = () => {
    if (securityScore >= 80) return "text-green-600"
    if (securityScore >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  // Formater le type de lancement
  const formatLaunchType = (type: string) => {
    switch (type) {
      case "fair":
        return "Lancement équitable"
      case "presale":
        return "Presale"
      case "ido":
        return "IDO"
      default:
        return type
    }
  }

  // Créer un objet avec les données de base pour l'aperçu
  const basicData = {
    name: tokenName || "Nom du token",
    symbol: tokenSymbol || "SYMBOLE",
    description: "Token de nouvelle génération avec des fonctionnalités avancées et une sécurité renforcée",
    website: "",
    twitter: "",
    telegram: "",
    blockchain: "solana",
    tokenType: "Quantum",
  }

  // Créer un objet avec les données de tokenomics pour l'aperçu
  const tokenomicsData = {
    initialSupply: tokenSupply,
    maxSupply: tokenSupply,
    distribution: {
      presale: 0,
      liquidity: distribution.find((d) => d.label === "Liquidity Pool")?.percentage || 70,
      team: distribution.find((d) => d.label === "Team")?.percentage || 10,
      marketing: distribution.find((d) => d.label === "Marketing")?.percentage || 15,
      development: distribution.find((d) => d.label === "Development")?.percentage || 5,
      reserve: 0,
    },
    vesting: {
      team: {
        enabled: false,
        duration: 0,
        cliff: 0,
      },
      marketing: {
        enabled: false,
        duration: 0,
        cliff: 0,
      },
      development: {
        enabled: false,
        duration: 0,
        cliff: 0,
      },
    },
  }

  // Créer un objet avec les données de lancement pour l'aperçu
  const launchData = {
    launchType: launchOptions.launchType || "direct",
    softCap: launchOptions.softCap,
    hardCap: launchOptions.hardCap,
    presaleRate: launchOptions.presaleRate,
    listingRate: launchOptions.listingRate,
    startDate: launchOptions.startDate.toString(),
    endDate: launchOptions.endDate.toString(),
    liquidityPercentage: launchOptions.liquidityPercentage,
    liquidityLockPeriod: launchOptions.lockDuration,
    minContribution: 0.1,
    maxContribution: 10,
  }

  return (
    <Card className="sticky top-6">
      <CardHeader>
        <CardTitle>Aperçu du token</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center text-gray-500">
            {basicData.symbol ? basicData.symbol.substring(0, 2).toUpperCase() : "?"}
          </div>
          <div>
            <h3 className="text-xl font-bold">{basicData.name || "Nom du token"}</h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline">{basicData.symbol || "SYMBOLE"}</Badge>
              <Badge variant="secondary">{basicData.blockchain === "solana" ? "Solana" : "BNB Chain"}</Badge>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Description</h4>
          <p className="text-sm text-muted-foreground">{basicData.description || "Aucune description fournie"}</p>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Tokenomics</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <p className="text-muted-foreground">Offre initiale</p>
              <p>{tokenomicsData.initialSupply.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Offre maximale</p>
              <p>{tokenomicsData.maxSupply.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Sécurité</h4>
          <div className="flex items-center justify-between">
            <span className="text-sm">Score de sécurité</span>
            <span className={`font-bold ${getScoreColor()}`}>{securityScore}/100</span>
          </div>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <p className="text-muted-foreground">Anti-Bot</p>
              <p>{securityData.antiBot ? "Activé" : "Désactivé"}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Anti-Dump</p>
              <p>{securityData.antiDump ? "Activé" : "Désactivé"}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Taxes (achat/vente)</p>
              <p>
                {securityData.buyTax}% / {securityData.sellTax}%
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Liquidité verrouillée</p>
              <p>
                {securityData.liquidityLockPeriod === 30
                  ? "30 jours"
                  : securityData.liquidityLockPeriod === 90
                    ? "90 jours"
                    : securityData.liquidityLockPeriod === 180
                      ? "180 jours"
                      : securityData.liquidityLockPeriod === 365
                        ? "1 an"
                        : securityData.liquidityLockPeriod === 730
                          ? "2 ans"
                          : `${securityData.liquidityLockPeriod} jours`}
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">Lancement</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <p className="text-muted-foreground">Type</p>
              <p>{formatLaunchType(launchData.launchType)}</p>
            </div>
            {(launchData.launchType === "presale" || launchData.launchType === "ido") && (
              <>
                <div>
                  <p className="text-muted-foreground">Hard Cap</p>
                  <p>{launchData.hardCap} SOL</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Taux Presale</p>
                  <p>{launchData.presaleRate.toLocaleString()} tokens/SOL</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Liquidité</p>
                  <p>{launchData.liquidityPercentage}%</p>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="pt-4 border-t">
          <p className="text-xs text-muted-foreground text-center">
            Ce token sera créé sur {basicData.blockchain === "solana" ? "Solana" : "BNB Chain"} avec les paramètres
            ci-dessus. Vérifiez bien tous les détails avant de créer le token.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

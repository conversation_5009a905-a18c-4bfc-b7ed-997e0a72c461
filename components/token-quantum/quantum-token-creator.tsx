"use client"

import type React from "react"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { TokenImageSelector } from "../token-factory/token-image-selector"
import { TokenomicsConfig } from "./tokenomics-config"
import { SecurityFeatures } from "./security-features"
import { TokenomicsDisplay } from "./tokenomics-display"
import { SecurityFeaturesDisplay } from "./security-features-display"
import { LaunchpadIntegration } from "./launchpad-integration"
import { LaunchOptionsDisplay } from "./launch-options-display"
import { TokenPreview } from "./token-preview"
import { InitialDistribution } from "./initial-distribution"

interface TokenomicsData {
  initialSupply: number
  maxSupply: number
  distribution: {
    presale: number
    liquidity: number
    team: number
    marketing: number
    development: number
    reserve: number
  }
  vesting: {
    team: {
      enabled: boolean
      duration: number
      cliff: number
    }
    marketing: {
      enabled: boolean
      duration: number
      cliff: number
    }
    development: {
      enabled: boolean
      duration: number
      cliff: number
    }
  }
}

interface SecurityData {
  antiBot: boolean
  antiDump: boolean
  maxWalletPercentage: number
  maxTransactionPercentage: number
  buyTax: number
  sellTax: number
  liquidityLockPeriod: number
  renounceOwnership: boolean
  auditEnabled: boolean
  kycEnabled: boolean
}

interface LaunchData {
  launchType: "fair" | "presale" | "ido"
  softCap: number
  hardCap: number
  presaleRate: number
  listingRate: number
  startDate: string
  endDate: string
  liquidityPercentage: number
  liquidityLockPeriod: number
  minContribution: number
  maxContribution: number
}

export function QuantumTokenCreator() {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [isCreating, setIsCreating] = useState(false)
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imageUrl, setImageUrl] = useState<string | null>(null)

  // État du formulaire de base
  const [basicData, setBasicData] = useState({
    name: "",
    symbol: "",
    description: "",
    website: "",
    twitter: "",
    telegram: "",
    blockchain: "solana",
    tokenType: "quantum",
  })

  // État des tokenomics
  const [tokenomicsData, setTokenomicsData] = useState<TokenomicsData>({
    initialSupply: 1000000000,
    maxSupply: 1000000000,
    distribution: {
      presale: 60,
      liquidity: 20,
      team: 10,
      marketing: 5,
      development: 3,
      reserve: 2,
    },
    vesting: {
      team: {
        enabled: true,
        duration: 12,
        cliff: 1,
      },
      marketing: {
        enabled: true,
        duration: 6,
        cliff: 0,
      },
      development: {
        enabled: true,
        duration: 12,
        cliff: 0,
      },
    },
  })

  // État des fonctionnalités de sécurité
  const [securityData, setSecurityData] = useState<SecurityData>({
    antiBot: true,
    antiDump: true,
    maxWalletPercentage: 2,
    maxTransactionPercentage: 1,
    buyTax: 2,
    sellTax: 2,
    liquidityLockPeriod: 365,
    renounceOwnership: false,
    auditEnabled: true,
    kycEnabled: false,
  })

  // État des options de lancement
  const [launchData, setLaunchData] = useState<LaunchData>({
    launchType: "presale",
    softCap: 50,
    hardCap: 100,
    presaleRate: 1000000,
    listingRate: 800000,
    startDate: "",
    endDate: "",
    liquidityPercentage: 70,
    liquidityLockPeriod: 365,
    minContribution: 0.1,
    maxContribution: 5,
  })

  // Gérer les changements de formulaire de base
  const handleBasicChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setBasicData((prev) => ({ ...prev, [name]: value }))
  }

  // Gérer les changements de sélection
  const handleSelectChange = (name: string, value: string) => {
    setBasicData((prev) => ({ ...prev, [name]: value }))
  }

  // Gérer la sélection d'image
  const handleImageSelect = (file: File | null, url: string | null) => {
    setSelectedImage(file)
    setImageUrl(url)
  }

  // Gérer les changements de tokenomics
  const handleTokenomicsChange = (data: Partial<TokenomicsData>) => {
    setTokenomicsData((prev) => ({ ...prev, ...data }))
  }

  // Gérer les changements de sécurité
  const handleSecurityChange = (data: Partial<SecurityData>) => {
    setSecurityData((prev) => ({ ...prev, ...data }))
  }

  // Gérer les changements d'options de lancement
  const handleLaunchChange = (data: Partial<LaunchData>) => {
    setLaunchData((prev) => ({ ...prev, ...data }))
  }

  // Valider le formulaire
  const validateForm = () => {
    if (!basicData.name) {
      toast({
        title: "Erreur de validation",
        description: "Le nom du token est requis",
        variant: "destructive",
      })
      return false
    }

    if (!basicData.symbol) {
      toast({
        title: "Erreur de validation",
        description: "Le symbole du token est requis",
        variant: "destructive",
      })
      return false
    }

    if (tokenomicsData.initialSupply <= 0) {
      toast({
        title: "Erreur de validation",
        description: "L'offre initiale doit être supérieure à 0",
        variant: "destructive",
      })
      return false
    }

    // Vérifier que la distribution totalise 100%
    const totalDistribution =
      tokenomicsData.distribution.presale +
      tokenomicsData.distribution.liquidity +
      tokenomicsData.distribution.team +
      tokenomicsData.distribution.marketing +
      tokenomicsData.distribution.development +
      tokenomicsData.distribution.reserve

    if (Math.abs(totalDistribution - 100) > 0.01) {
      toast({
        title: "Erreur de validation",
        description: "La distribution des tokens doit totaliser 100%",
        variant: "destructive",
      })
      return false
    }

    return true
  }

  // Créer le token
  const createToken = async () => {
    if (!validateForm()) return

    setIsCreating(true)

    try {
      // Simuler le processus de création du token
      await new Promise((resolve) => setTimeout(resolve, 5000))

      // Afficher un message de succès
      toast({
        title: "Token créé avec succès",
        description: `Votre token ${basicData.name} (${basicData.symbol}) a été créé avec succès.`,
      })

      // Rediriger vers la page du token
      router.push(
        `/token-quantum/success?name=${encodeURIComponent(basicData.name)}&symbol=${encodeURIComponent(basicData.symbol)}`,
      )
    } catch (error) {
      console.error("Erreur lors de la création du token:", error)

      // Afficher une erreur
      toast({
        title: "Erreur",
        description: "Une erreur s'est produite lors de la création du token. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Créer un token Quantum</CardTitle>
            <CardDescription>
              Créez un token avancé avec des fonctionnalités de sécurité renforcées et des options de lancement
              personnalisées.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="basic">Informations</TabsTrigger>
                <TabsTrigger value="tokenomics">Tokenomics</TabsTrigger>
                <TabsTrigger value="security">Sécurité</TabsTrigger>
                <TabsTrigger value="launch">Lancement</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nom du token</Label>
                    <Input
                      id="name"
                      name="name"
                      value={basicData.name}
                      onChange={handleBasicChange}
                      placeholder="ex: Global Finance Quantum"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="symbol">Symbole du token</Label>
                    <Input
                      id="symbol"
                      name="symbol"
                      value={basicData.symbol}
                      onChange={handleBasicChange}
                      placeholder="ex: GFQ"
                      maxLength={10}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={basicData.description}
                    onChange={handleBasicChange}
                    placeholder="Décrivez votre token et son utilité..."
                    rows={4}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="website">Site web</Label>
                    <Input
                      id="website"
                      name="website"
                      value={basicData.website}
                      onChange={handleBasicChange}
                      placeholder="https://votre-site.com"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="blockchain">Blockchain</Label>
                    <Select
                      value={basicData.blockchain}
                      onValueChange={(value) => handleSelectChange("blockchain", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionnez une blockchain" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="solana">Solana</SelectItem>
                        <SelectItem value="bnb">BNB Chain</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input
                      id="twitter"
                      name="twitter"
                      value={basicData.twitter}
                      onChange={handleBasicChange}
                      placeholder="https://twitter.com/votre-compte"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="telegram">Telegram</Label>
                    <Input
                      id="telegram"
                      name="telegram"
                      value={basicData.telegram}
                      onChange={handleBasicChange}
                      placeholder="https://t.me/votre-groupe"
                    />
                  </div>
                </div>

                <TokenImageSelector onImageSelect={handleImageSelect} />
              </TabsContent>

              <TabsContent value="tokenomics" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <TokenomicsConfig data={tokenomicsData} onChange={handleTokenomicsChange} />
                  </div>
                  <div>
                    <TokenomicsDisplay data={tokenomicsData} />
                    <div className="mt-6">
                      <InitialDistribution distribution={tokenomicsData.distribution} />
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="security" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <SecurityFeatures data={securityData} onChange={handleSecurityChange} />
                  </div>
                  <div>
                    <SecurityFeaturesDisplay data={securityData} />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="launch" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <LaunchpadIntegration data={launchData} onChange={handleLaunchChange} />
                  </div>
                  <div>
                    <LaunchOptionsDisplay data={launchData} />
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-between mt-6">
              <Button variant="outline" onClick={() => router.back()}>
                Annuler
              </Button>
              <Button onClick={createToken} disabled={isCreating}>
                {isCreating ? "Création en cours..." : "Créer le token"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <div>
        <TokenPreview
          basicData={basicData}
          tokenomicsData={tokenomicsData}
          securityData={securityData}
          launchData={launchData}
          imageUrl={imageUrl}
        />
      </div>
    </div>
  )
}

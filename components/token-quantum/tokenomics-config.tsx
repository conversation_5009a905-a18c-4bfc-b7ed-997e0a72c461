"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>lider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface TokenomicsData {
  initialSupply: number
  maxSupply: number
  distribution: {
    presale: number
    liquidity: number
    team: number
    marketing: number
    development: number
    reserve: number
  }
  vesting: {
    team: {
      enabled: boolean
      duration: number
      cliff: number
    }
    marketing: {
      enabled: boolean
      duration: number
      cliff: number
    }
    development: {
      enabled: boolean
      duration: number
      cliff: number
    }
  }
}

interface TokenomicsConfigProps {
  data: TokenomicsData
  onChange: (data: Partial<TokenomicsData>) => void
}

export function TokenomicsConfig({ data, onChange }: TokenomicsConfigProps) {
  const [activeTab, setActiveTab] = useState("supply")

  // Gérer les changements de supply
  const handleSupplyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    onChange({ [name]: Number.parseFloat(value) || 0 })
  }

  // Gérer les changements de distribution
  const handleDistributionChange = (category: keyof typeof data.distribution, value: number) => {
    onChange({
      distribution: {
        ...data.distribution,
        [category]: value,
      },
    })
  }

  // Gérer les changements de vesting
  const handleVestingChange = (
    category: keyof typeof data.vesting,
    field: keyof typeof data.vesting.team,
    value: boolean | number,
  ) => {
    onChange({
      vesting: {
        ...data.vesting,
        [category]: {
          ...data.vesting[category],
          [field]: value,
        },
      },
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuration des tokenomics</CardTitle>
        <CardDescription>Définissez l'offre et la distribution de votre token</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="supply">Offre</TabsTrigger>
            <TabsTrigger value="distribution">Distribution</TabsTrigger>
            <TabsTrigger value="vesting">Vesting</TabsTrigger>
          </TabsList>

          <TabsContent value="supply" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="initialSupply">Offre initiale</Label>
              <Input
                id="initialSupply"
                name="initialSupply"
                type="number"
                value={data.initialSupply}
                onChange={handleSupplyChange}
                min={1}
              />
              <p className="text-xs text-muted-foreground">Nombre total de tokens créés au lancement</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxSupply">Offre maximale</Label>
              <Input
                id="maxSupply"
                name="maxSupply"
                type="number"
                value={data.maxSupply}
                onChange={handleSupplyChange}
                min={data.initialSupply}
              />
              <p className="text-xs text-muted-foreground">
                Nombre maximum de tokens pouvant exister (identique à l'offre initiale pour un token non-inflationniste)
              </p>
            </div>
          </TabsContent>

          <TabsContent value="distribution" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="presale">Presale (%)</Label>
                  <span className="text-sm text-muted-foreground">{data.distribution.presale}%</span>
                </div>
                <Slider
                  id="presale"
                  min={0}
                  max={100}
                  step={1}
                  value={[data.distribution.presale]}
                  onValueChange={(value) => handleDistributionChange("presale", value[0])}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="liquidity">Liquidité (%)</Label>
                  <span className="text-sm text-muted-foreground">{data.distribution.liquidity}%</span>
                </div>
                <Slider
                  id="liquidity"
                  min={0}
                  max={100}
                  step={1}
                  value={[data.distribution.liquidity]}
                  onValueChange={(value) => handleDistributionChange("liquidity", value[0])}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="team">Équipe (%)</Label>
                  <span className="text-sm text-muted-foreground">{data.distribution.team}%</span>
                </div>
                <Slider
                  id="team"
                  min={0}
                  max={100}
                  step={1}
                  value={[data.distribution.team]}
                  onValueChange={(value) => handleDistributionChange("team", value[0])}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="marketing">Marketing (%)</Label>
                  <span className="text-sm text-muted-foreground">{data.distribution.marketing}%</span>
                </div>
                <Slider
                  id="marketing"
                  min={0}
                  max={100}
                  step={1}
                  value={[data.distribution.marketing]}
                  onValueChange={(value) => handleDistributionChange("marketing", value[0])}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="development">Développement (%)</Label>
                  <span className="text-sm text-muted-foreground">{data.distribution.development}%</span>
                </div>
                <Slider
                  id="development"
                  min={0}
                  max={100}
                  step={1}
                  value={[data.distribution.development]}
                  onValueChange={(value) => handleDistributionChange("development", value[0])}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="reserve">Réserve (%)</Label>
                  <span className="text-sm text-muted-foreground">{data.distribution.reserve}%</span>
                </div>
                <Slider
                  id="reserve"
                  min={0}
                  max={100}
                  step={1}
                  value={[data.distribution.reserve]}
                  onValueChange={(value) => handleDistributionChange("reserve", value[0])}
                />
              </div>

              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  Total: {Object.values(data.distribution).reduce((a, b) => a + b, 0)}%
                  {Math.abs(Object.values(data.distribution).reduce((a, b) => a + b, 0) - 100) > 0.01 && (
                    <span className="font-bold"> (doit être égal à 100%)</span>
                  )}
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="vesting" className="space-y-4">
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="team-vesting">Vesting pour l'équipe</Label>
                  <Switch
                    id="team-vesting"
                    checked={data.vesting.team.enabled}
                    onCheckedChange={(checked) => handleVestingChange("team", "enabled", checked)}
                  />
                </div>

                {data.vesting.team.enabled && (
                  <div className="grid grid-cols-2 gap-4 pl-4 border-l-2 border-gray-200">
                    <div className="space-y-2">
                      <Label htmlFor="team-duration">Durée (mois)</Label>
                      <Input
                        id="team-duration"
                        type="number"
                        value={data.vesting.team.duration}
                        onChange={(e) => handleVestingChange("team", "duration", Number.parseInt(e.target.value) || 0)}
                        min={1}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="team-cliff">Cliff (mois)</Label>
                      <Input
                        id="team-cliff"
                        type="number"
                        value={data.vesting.team.cliff}
                        onChange={(e) => handleVestingChange("team", "cliff", Number.parseInt(e.target.value) || 0)}
                        min={0}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="marketing-vesting">Vesting pour le marketing</Label>
                  <Switch
                    id="marketing-vesting"
                    checked={data.vesting.marketing.enabled}
                    onCheckedChange={(checked) => handleVestingChange("marketing", "enabled", checked)}
                  />
                </div>

                {data.vesting.marketing.enabled && (
                  <div className="grid grid-cols-2 gap-4 pl-4 border-l-2 border-gray-200">
                    <div className="space-y-2">
                      <Label htmlFor="marketing-duration">Durée (mois)</Label>
                      <Input
                        id="marketing-duration"
                        type="number"
                        value={data.vesting.marketing.duration}
                        onChange={(e) =>
                          handleVestingChange("marketing", "duration", Number.parseInt(e.target.value) || 0)
                        }
                        min={1}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="marketing-cliff">Cliff (mois)</Label>
                      <Input
                        id="marketing-cliff"
                        type="number"
                        value={data.vesting.marketing.cliff}
                        onChange={(e) =>
                          handleVestingChange("marketing", "cliff", Number.parseInt(e.target.value) || 0)
                        }
                        min={0}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="development-vesting">Vesting pour le développement</Label>
                  <Switch
                    id="development-vesting"
                    checked={data.vesting.development.enabled}
                    onCheckedChange={(checked) => handleVestingChange("development", "enabled", checked)}
                  />
                </div>

                {data.vesting.development.enabled && (
                  <div className="grid grid-cols-2 gap-4 pl-4 border-l-2 border-gray-200">
                    <div className="space-y-2">
                      <Label htmlFor="development-duration">Durée (mois)</Label>
                      <Input
                        id="development-duration"
                        type="number"
                        value={data.vesting.development.duration}
                        onChange={(e) =>
                          handleVestingChange("development", "duration", Number.parseInt(e.target.value) || 0)
                        }
                        min={1}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="development-cliff">Cliff (mois)</Label>
                      <Input
                        id="development-cliff"
                        type="number"
                        value={data.vesting.development.cliff}
                        onChange={(e) =>
                          handleVestingChange("development", "cliff", Number.parseInt(e.target.value) || 0)
                        }
                        min={0}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import {
  CheckCircle2,
  AlertCircle,
  Loader2,
  <PERSON><PERSON>,
  Copy,
  ExternalLink,
  Rocket,
  Sparkles,
  TrendingUp,
  Shield,
  Lock,
  Upload,
  RefreshCw,
  Share2,
  Twitter,
  MessageCircle,
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useNetwork } from "@/contexts/network-context"

export default function AdvancedTokenCreator() {
  // Wallet connection
  const { publicKey, signTransaction, signAllTransactions, connected } = useWallet()
  const { activeNetwork } = useNetwork()
  const { toast } = useToast()

  // Form state
  const [tokenName, setTokenName] = useState("")
  const [tokenSymbol, setTokenSymbol] = useState("")
  const [tokenDecimals, setTokenDecimals] = useState("9")
  const [tokenSupply, setTokenSupply] = useState("1000000000")
  const [tokenDescription, setTokenDescription] = useState("")
  const [tokenWebsite, setTokenWebsite] = useState("")
  const [tokenTwitter, setTokenTwitter] = useState("")
  const [tokenTelegram, setTokenTelegram] = useState("")
  const [activeTab, setActiveTab] = useState("basic")
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [selectedSuffix, setSelectedSuffix] = useState("QUANTUM")

  // Advanced options
  const [antiBot, setAntiBot] = useState(true)
  const [antiBotDuration, setAntiBotDuration] = useState("30")
  const [antiDump, setAntiDump] = useState(true)
  const [cooldownPeriod, setCooldownPeriod] = useState("15")
  const [autoLiquidity, setAutoLiquidity] = useState(true)
  const [liquidityLockPeriod, setLiquidityLockPeriod] = useState("180")
  const [marketingFee, setMarketingFee] = useState(2)
  const [liquidityFee, setLiquidityFee] = useState(3)
  const [maxWallet, setMaxWallet] = useState(2)
  const [maxTransaction, setMaxTransaction] = useState(1)
  const [blacklist, setBlacklist] = useState(false)
  const [pauseTrading, setPauseTrading] = useState(false)

  // Launch options
  const [fairLaunch, setFairLaunch] = useState(true)
  const [presale, setPresale] = useState(false)
  const [softCap, setSoftCap] = useState("10")
  const [hardCap, setHardCap] = useState("50")
  const [presaleRate, setPresaleRate] = useState("1000000")
  const [presaleStart, setPresaleStart] = useState<Date | null>(null)
  const [presaleDuration, setPresaleDuration] = useState("7")
  const [initialLiquidity, setInitialLiquidity] = useState("10")
  const [listingRate, setListingRate] = useState("900000")
  const [launchPromotion, setLaunchPromotion] = useState(false)
  const [telegramAnnouncement, setTelegramAnnouncement] = useState(false)
  const [twitterPromotion, setTwitterPromotion] = useState(false)

  // Creation state
  const [isCreating, setIsCreating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [createdToken, setCreatedToken] = useState<any>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({})
  const [logs, setLogs] = useState<string[]>([])
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false)

  // Available suffixes
  const availableSuffixes = ["QUANTUM", "GF", "SOL", "MEME", "AI", "BONK", "PUMP"]

  // Simple function to sanitize input for token symbols
  // Only allow alphanumeric characters
  const sanitizeSymbol = (input: string): string => {
    return input.replace(/[^A-Za-z0-9]/g, "")
  }

  // Add log function
  const addLog = (message: string) => {
    console.log(message)
    setLogs((prev) => [...prev, `${new Date().toISOString().split("T")[1].split(".")[0]} - ${message}`])
  }

  // Check wallet balance
  useEffect(() => {
    const checkBalance = async () => {
      if (publicKey) {
        try {
          // Simulation pour l'exemple
          const balance = Math.random() * 10
          setWalletBalance(balance)
        } catch (err: any) {
          console.error("Error checking wallet balance:", err)
          setWalletBalance(null)
        }
      } else {
        setWalletBalance(null)
      }
    }

    checkBalance()
  }, [publicKey])

  // Handle logo change
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]

      // Create preview
      const reader = new FileReader()
      reader.onload = (event) => {
        setLogoPreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Handle symbol change
  const handleSymbolChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Sanitize the symbol to ensure it's compatible
    const sanitizedSymbol = sanitizeSymbol(e.target.value.toUpperCase())
    setTokenSymbol(sanitizedSymbol)
  }

  // Validate form
  const validateForm = () => {
    const errors: { [key: string]: string } = {}

    // Validate token name
    if (!tokenName.trim()) {
      errors.name = "Token name is required"
    }

    // Validate token symbol
    if (!tokenSymbol.trim()) {
      errors.symbol = "Token symbol is required"
    } else {
      // Check if symbol contains only alphanumeric characters
      const symbolRegex = /^[A-Za-z0-9]+$/
      if (!symbolRegex.test(tokenSymbol)) {
        errors.symbol = "Symbol must contain only letters and numbers"
      }
    }

    // Validate decimals
    const decimals = Number(tokenDecimals)
    if (isNaN(decimals) || decimals < 0 || decimals > 9) {
      errors.decimals = "Decimals must be between 0 and 9"
    }

    // Validate supply
    const supply = Number(tokenSupply)
    if (isNaN(supply) || supply <= 0) {
      errors.supply = "Supply must be a positive number"
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Run diagnostics
  const runDiagnostics = async () => {
    setIsRunningDiagnostics(true)
    setLogs([])

    addLog("Starting diagnostics...")

    try {
      // Test RPC connection
      addLog("Testing RPC connection...")
      await new Promise((resolve) => setTimeout(resolve, 500))
      addLog("✅ RPC connection successful")

      // Test symbol for validity
      addLog("Testing token symbol for validity...")
      await new Promise((resolve) => setTimeout(resolve, 300))
      addLog("✅ Token symbol is valid")

      // Test wallet connection
      addLog("Testing wallet connection...")
      const walletConnected = connected && publicKey
      addLog(walletConnected ? "✅ Wallet connected" : "❌ Wallet not connected")

      addLog("Diagnostics completed")
    } catch (error: any) {
      addLog(`❌ Error during diagnostics: ${error.message}`)
    } finally {
      setIsRunningDiagnostics(false)
    }
  }

  // Create token
  const createToken = async () => {
    if (!connected || !publicKey || !signTransaction) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to create a token",
        variant: "destructive",
      })
      return
    }

    // Validate form
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      })
      return
    }

    setIsCreating(true)
    setProgress(0)
    setError(null)
    setCurrentStep("Initializing...")
    setLogs([])

    try {
      addLog("Starting token creation process")
      addLog(
        `Token details: Name=${tokenName}, Symbol=${tokenSymbol}${selectedSuffix}, Decimals=${tokenDecimals}, Supply=${tokenSupply}`,
      )

      // Update progress
      setProgress(10)
      setCurrentStep("Preparing token creation...")
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Update progress
      setProgress(30)
      setCurrentStep("Creating token on blockchain...")
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Update progress
      setProgress(50)
      setCurrentStep("Configuring token features...")
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Update progress
      setProgress(70)
      setCurrentStep("Setting up security features...")
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Update progress
      setProgress(90)
      setCurrentStep("Finalizing token creation...")
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Simulate token creation
      const tokenAddress = "Gxyz" + Math.random().toString(36).substring(2, 10) + "..."
      const tokenAccountAddress = "Axyz" + Math.random().toString(36).substring(2, 10) + "..."
      const transactionId = "tx" + Math.random().toString(36).substring(2, 15) + "..."

      addLog(`Token created with address: ${tokenAddress}`)
      addLog(`Token account: ${tokenAccountAddress}`)
      addLog(`Transaction ID: ${transactionId}`)

      // Update progress
      setProgress(100)
      setCurrentStep("Token created successfully!")

      // Set created token
      setCreatedToken({
        name: tokenName,
        symbol: `${tokenSymbol}${selectedSuffix}`,
        decimals: Number(tokenDecimals),
        supply: tokenSupply,
        mintAddress: tokenAddress,
        tokenAccount: tokenAccountAddress,
        transactionId: transactionId,
        features: {
          antiBot,
          antiDump,
          autoLiquidity,
          marketingFee,
          liquidityFee,
          maxWallet,
          maxTransaction,
        },
      })

      toast({
        title: "Token Created Successfully",
        description: `Your token ${tokenName} (${tokenSymbol}${selectedSuffix}) has been created`,
      })
    } catch (error: any) {
      console.error("Error creating token:", error)
      setError(error.message || "An error occurred while creating the token")
      addLog(`ERROR: ${error.message || "Unknown error"}`)

      toast({
        title: "Token Creation Failed",
        description: error.message || "An error occurred while creating the token",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "The text has been copied to your clipboard",
    })
  }

  // View on explorer
  const viewOnExplorer = (address: string) => {
    const explorerUrl = `https://explorer.solana.com/address/${address}?cluster=devnet`
    window.open(explorerUrl, "_blank")
  }

  // Launch token (simulated)
  const launchToken = () => {
    toast({
      title: "Token Launch Initiated",
      description: "Your token launch process has started",
    })
    // In a real implementation, this would start the token launch process
  }

  // Share on social media
  const shareOnSocial = (platform: string) => {
    const message = `Check out my new token ${tokenName} (${tokenSymbol}${selectedSuffix}) on Global Finance!`
    let url = ""

    switch (platform) {
      case "twitter":
        url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`
        break
      case "telegram":
        url = `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(
          message,
        )}`
        break
      default:
        url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`
    }

    window.open(url, "_blank")
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Advanced Token Creator</CardTitle>
            <CardDescription>Create your own token with advanced features and security</CardDescription>
          </div>
          <Badge className="bg-[#D4AF37] text-black">Quantum</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={runDiagnostics}
              disabled={isRunningDiagnostics}
            >
              {isRunningDiagnostics ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running Diagnostics...
                </>
              ) : (
                "Run Diagnostics"
              )}
            </Button>
          </Alert>
        )}

        {createdToken ? (
          <div className="space-y-4">
            <Alert variant="default" className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle>Token Created Successfully</AlertTitle>
              <AlertDescription>Your token has been created on the blockchain.</AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Token Details</h3>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => viewOnExplorer(createdToken.mintAddress)}>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View on Explorer
                  </Button>
                  <Button variant="default" size="sm" onClick={launchToken}>
                    <Rocket className="h-4 w-4 mr-2" />
                    Launch Token
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Token Name</Label>
                  <div className="font-medium">{createdToken.name}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Token Symbol</Label>
                  <div className="font-medium">{createdToken.symbol}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Decimals</Label>
                  <div className="font-medium">{createdToken.decimals}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground">Initial Supply</Label>
                  <div className="font-medium">{Number(createdToken.supply).toLocaleString()}</div>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Token Address</Label>
                <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                  <code className="text-xs break-all">{createdToken.mintAddress}</code>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.mintAddress)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Token Account</Label>
                <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                  <code className="text-xs break-all">{createdToken.tokenAccount}</code>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.tokenAccount)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Transaction ID</Label>
                <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                  <code className="text-xs break-all">{createdToken.transactionId}</code>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(createdToken.transactionId)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Features</Label>
                <div className="flex flex-wrap gap-2">
                  {createdToken.features.antiBot && (
                    <Badge variant="outline" className="bg-blue-50">
                      <Shield className="h-3 w-3 mr-1" />
                      Anti-Bot
                    </Badge>
                  )}
                  {createdToken.features.antiDump && (
                    <Badge variant="outline" className="bg-purple-50">
                      <Lock className="h-3 w-3 mr-1" />
                      Anti-Dump
                    </Badge>
                  )}
                  {createdToken.features.autoLiquidity && (
                    <Badge variant="outline" className="bg-green-50">
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Auto-Liquidity
                    </Badge>
                  )}
                  <Badge variant="outline" className="bg-amber-50">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    {createdToken.features.marketingFee}% Marketing
                  </Badge>
                  <Badge variant="outline" className="bg-amber-50">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    {createdToken.features.liquidityFee}% Liquidity
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-muted-foreground">Share Your Token</Label>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => shareOnSocial("twitter")}>
                    <Twitter className="h-4 w-4 mr-2" />
                    Twitter
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => shareOnSocial("telegram")}>
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Telegram
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => copyToClipboard(window.location.href)}>
                    <Share2 className="h-4 w-4 mr-2" />
                    Copy Link
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button onClick={() => setCreatedToken(null)}>Create Another Token</Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {isCreating ? (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-medium">{currentStep}</h3>
                  <p className="text-sm text-muted-foreground">Please wait while we create your token...</p>
                </div>
                <Progress value={progress} className="h-2" />

                {/* Logs display */}
                <div className="mt-4">
                  <Label>Creation Logs</Label>
                  <div className="mt-2 p-2 bg-black text-green-400 font-mono text-xs h-40 overflow-y-auto rounded">
                    {logs.map((log, index) => (
                      <div key={index}>{log}</div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <>
                <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">
                      <Info className="h-4 w-4 mr-2" />
                      Basic
                    </TabsTrigger>
                    <TabsTrigger value="tokenomics">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Tokenomics
                    </TabsTrigger>
                    <TabsTrigger value="security">
                      <Shield className="h-4 w-4 mr-2" />
                      Security
                    </TabsTrigger>
                    <TabsTrigger value="launch">
                      <Rocket className="h-4 w-4 mr-2" />
                      Launch
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4 mt-4">
                    <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                      <Info className="h-4 w-4 text-blue-600" />
                      <AlertDescription>
                        Quantum tokens use a fixed suffix set by the administrator to ensure their authenticity and
                        security.
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-2">
                      <Label htmlFor="tokenName">Token Name</Label>
                      <Input
                        id="tokenName"
                        value={tokenName}
                        onChange={(e) => setTokenName(e.target.value)}
                        placeholder="Ex: Global Finance"
                        required
                        className={validationErrors.name ? "border-red-500" : ""}
                      />
                      {validationErrors.name && <p className="text-xs text-red-500 mt-1">{validationErrors.name}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenSymbol">Token Symbol</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="tokenSymbol"
                          value={tokenSymbol}
                          onChange={handleSymbolChange}
                          placeholder="Ex: GF"
                          required
                          maxLength={5}
                          className={`uppercase ${validationErrors.symbol ? "border-red-500" : ""}`}
                        />
                        <Select value={selectedSuffix} onValueChange={setSelectedSuffix}>
                          <SelectTrigger className="w-[120px]">
                            <SelectValue placeholder="Suffix" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableSuffixes.map((suffix) => (
                              <SelectItem key={suffix} value={suffix}>
                                {suffix}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      {validationErrors.symbol && (
                        <p className="text-xs text-red-500 mt-1">{validationErrors.symbol}</p>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        Final symbol will be: {tokenSymbol}
                        {selectedSuffix}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tokenDescription">Description</Label>
                      <Textarea
                        id="tokenDescription"
                        value={tokenDescription}
                        onChange={(e) => setTokenDescription(e.target.value)}
                        placeholder="Describe your token and its utility..."
                        rows={4}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="tokenSupply">Total Supply</Label>
                        <Input
                          id="tokenSupply"
                          type="number"
                          placeholder="1000000000"
                          value={tokenSupply}
                          onChange={(e) => setTokenSupply(e.target.value)}
                          required
                          className={validationErrors.supply ? "border-red-500" : ""}
                        />
                        {validationErrors.supply && (
                          <p className="text-xs text-red-500 mt-1">{validationErrors.supply}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="tokenDecimals">Decimals</Label>
                        <Select value={tokenDecimals} onValueChange={(value) => setTokenDecimals(value)}>
                          <SelectTrigger id="tokenDecimals">
                            <SelectValue placeholder="Select" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="6">6</SelectItem>
                            <SelectItem value="9">9</SelectItem>
                            <SelectItem value="12">12</SelectItem>
                            <SelectItem value="18">18</SelectItem>
                          </SelectContent>
                        </Select>
                        {validationErrors.decimals && (
                          <p className="text-xs text-red-500 mt-1">{validationErrors.decimals}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="logo">Token Logo</Label>
                      <div className="flex items-center space-x-4">
                        <div className="flex-1">
                          <div className="border-2 border-dashed rounded-lg p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors">
                            <input
                              type="file"
                              id="logo"
                              className="hidden"
                              accept="image/*"
                              onChange={handleLogoChange}
                            />
                            <label htmlFor="logo" className="cursor-pointer flex flex-col items-center">
                              <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                              <span className="text-sm font-medium">Click to upload</span>
                              <span className="text-xs text-muted-foreground">PNG, JPG (max 2MB)</span>
                            </label>
                          </div>
                        </div>

                        {logoPreview && (
                          <div className="w-16 h-16 rounded-full overflow-hidden border">
                            <img
                              src={logoPreview || "/placeholder.svg"}
                              alt="Logo preview"
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="font-medium">Social Links</h3>

                      <div className="space-y-2">
                        <Label htmlFor="tokenWebsite">Website</Label>
                        <Input
                          id="tokenWebsite"
                          placeholder="https://your-site.com"
                          value={tokenWebsite}
                          onChange={(e) => setTokenWebsite(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="tokenTwitter">Twitter</Label>
                        <Input
                          id="tokenTwitter"
                          placeholder="https://twitter.com/your-account"
                          value={tokenTwitter}
                          onChange={(e) => setTokenTwitter(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="tokenTelegram">Telegram</Label>
                        <Input
                          id="tokenTelegram"
                          placeholder="https://t.me/your-group"
                          value={tokenTelegram}
                          onChange={(e) => setTokenTelegram(e.target.value)}
                        />
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="tokenomics" className="space-y-4 mt-4">
                    <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                      <Info className="h-4 w-4 text-blue-600" />
                      <AlertDescription>
                        Configure the economic mechanisms of your token to optimize its distribution and value.
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-6">
                      <div className="space-y-4">
                        <h3 className="font-medium">Transaction Fees</h3>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="marketingFee">Marketing Fee (%)</Label>
                            <span className="text-sm font-medium">{marketingFee}%</span>
                          </div>
                          <Slider
                            id="marketingFee"
                            min={0}
                            max={10}
                            step={0.5}
                            value={[marketingFee]}
                            onValueChange={(value) => setMarketingFee(value[0])}
                          />
                          <p className="text-xs text-muted-foreground">
                            These fees are used to fund marketing and promotional activities for the token.
                          </p>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="liquidityFee">Liquidity Fee (%)</Label>
                            <span className="text-sm font-medium">{liquidityFee}%</span>
                          </div>
                          <Slider
                            id="liquidityFee"
                            min={0}
                            max={10}
                            step={0.5}
                            value={[liquidityFee]}
                            onValueChange={(value) => setLiquidityFee(value[0])}
                          />
                          <p className="text-xs text-muted-foreground">
                            These fees are automatically added to the liquidity pool to stabilize the token price.
                          </p>
                        </div>

                        {marketingFee + liquidityFee > 10 && (
                          <Alert variant="destructive">
                            <AlertDescription>
                              Warning: Total fees greater than 10% may discourage transactions and token adoption.
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-medium">Transaction Limits</h3>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="maxWallet">Maximum per wallet (%)</Label>
                            <span className="text-sm font-medium">{maxWallet}%</span>
                          </div>
                          <Slider
                            id="maxWallet"
                            min={0}
                            max={10}
                            step={0.5}
                            value={[maxWallet]}
                            onValueChange={(value) => setMaxWallet(value[0])}
                          />
                          <p className="text-xs text-muted-foreground">
                            Limits the maximum amount of tokens a wallet can hold to prevent whales.
                          </p>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="maxTransaction">Maximum per transaction (%)</Label>
                            <span className="text-sm font-medium">{maxTransaction}%</span>
                          </div>
                          <Slider
                            id="maxTransaction"
                            min={0}
                            max={5}
                            step={0.5}
                            value={[maxTransaction]}
                            onValueChange={(value) => setMaxTransaction(value[0])}
                          />
                          <p className="text-xs text-muted-foreground">
                            Limits the maximum amount of tokens that can be bought or sold in a single transaction.
                          </p>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="security" className="space-y-4 mt-4">
                    <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                      <Shield className="h-4 w-4 text-blue-600" />
                      <AlertDescription>
                        Security features protect your token and investors against attacks and manipulations.
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-6">
                      <div className="space-y-4">
                        <h3 className="font-medium">Bot Protection</h3>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="antiBot" className="cursor-pointer">
                            Anti-bot protection
                            <p className="text-xs text-muted-foreground font-normal">
                              Prevents bots from buying your token at launch
                            </p>
                          </Label>
                          <Switch id="antiBot" checked={antiBot} onCheckedChange={setAntiBot} />
                        </div>

                        {antiBot && (
                          <div className="space-y-2 pl-6 border-l-2 border-[#D4AF37]/30">
                            <div>
                              <Label htmlFor="antiBotDuration">Protection duration (minutes)</Label>
                              <Select value={antiBotDuration} onValueChange={setAntiBotDuration}>
                                <SelectTrigger id="antiBotDuration">
                                  <SelectValue placeholder="Select" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="15">15 minutes</SelectItem>
                                  <SelectItem value="30">30 minutes</SelectItem>
                                  <SelectItem value="60">1 hour</SelectItem>
                                  <SelectItem value="120">2 hours</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-medium">Dump Protection</h3>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="antiDump" className="cursor-pointer">
                            Anti-dump protection
                            <p className="text-xs text-muted-foreground font-normal">
                              Prevents massive sales that crash the price
                            </p>
                          </Label>
                          <Switch id="antiDump" checked={antiDump} onCheckedChange={setAntiDump} />
                        </div>

                        {antiDump && (
                          <div className="space-y-2 pl-6 border-l-2 border-[#D4AF37]/30">
                            <div>
                              <Label htmlFor="cooldownPeriod">Cooldown period (minutes)</Label>
                              <Input
                                id="cooldownPeriod"
                                type="number"
                                placeholder="15"
                                min="1"
                                max="60"
                                value={cooldownPeriod}
                                onChange={(e) => setCooldownPeriod(e.target.value)}
                              />
                              <p className="text-xs text-muted-foreground mt-1">
                                Waiting time between two sales for the same wallet
                              </p>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-medium">Automatic Liquidity</h3>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="autoLiquidity" className="cursor-pointer">
                            Auto-liquidity
                            <p className="text-xs text-muted-foreground font-normal">
                              Automatically adds liquidity to the pool to stabilize the price
                            </p>
                          </Label>
                          <Switch id="autoLiquidity" checked={autoLiquidity} onCheckedChange={setAutoLiquidity} />
                        </div>

                        {autoLiquidity && (
                          <div className="space-y-2 pl-6 border-l-2 border-[#D4AF37]/30">
                            <div>
                              <Label htmlFor="liquidityLockPeriod">Lock period (days)</Label>
                              <Select value={liquidityLockPeriod} onValueChange={setLiquidityLockPeriod}>
                                <SelectTrigger id="liquidityLockPeriod">
                                  <SelectValue placeholder="Select" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="30">30 days</SelectItem>
                                  <SelectItem value="90">90 days</SelectItem>
                                  <SelectItem value="180">180 days</SelectItem>
                                  <SelectItem value="365">1 year</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-medium">Other Protections</h3>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="blacklist" className="cursor-pointer">
                            Blacklist function
                            <p className="text-xs text-muted-foreground font-normal">
                              Allows blocking malicious addresses
                            </p>
                          </Label>
                          <Switch id="blacklist" checked={blacklist} onCheckedChange={setBlacklist} />
                        </div>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="pauseTrading" className="cursor-pointer">
                            Transaction pause
                            <p className="text-xs text-muted-foreground font-normal">
                              Allows temporarily suspending transactions in case of emergency
                            </p>
                          </Label>
                          <Switch id="pauseTrading" checked={pauseTrading} onCheckedChange={setPauseTrading} />
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="launch" className="space-y-4 mt-4">
                    <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                      <Rocket className="h-4 w-4 text-blue-600" />
                      <AlertDescription>
                        Configure your token launch to maximize its visibility and initial adoption.
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-6">
                      <div className="space-y-4">
                        <h3 className="font-medium">Launch Options</h3>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="fairLaunch" className="cursor-pointer">
                            Fair Launch
                            <p className="text-xs text-muted-foreground font-normal">
                              Fair launch without presale or privileged allocation
                            </p>
                          </Label>
                          <Switch
                            id="fairLaunch"
                            checked={fairLaunch}
                            onCheckedChange={(checked) => {
                              setFairLaunch(checked)
                              if (checked) {
                                setPresale(false)
                              }
                            }}
                          />
                        </div>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="presale" className="cursor-pointer">
                            Presale
                            <p className="text-xs text-muted-foreground font-normal">
                              Organize a presale before public launch
                            </p>
                          </Label>
                          <Switch
                            id="presale"
                            checked={presale}
                            onCheckedChange={(checked) => {
                              setPresale(checked)
                              if (checked) {
                                setFairLaunch(false)
                              }
                            }}
                          />
                        </div>

                        {presale && (
                          <div className="space-y-4 pl-6 border-l-2 border-[#D4AF37]/30">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="softCap">Soft Cap (SOL)</Label>
                                <Input
                                  id="softCap"
                                  type="number"
                                  placeholder="10"
                                  min="1"
                                  value={softCap}
                                  onChange={(e) => setSoftCap(e.target.value)}
                                />
                              </div>

                              <div>
                                <Label htmlFor="hardCap">Hard Cap (SOL)</Label>
                                <Input
                                  id="hardCap"
                                  type="number"
                                  placeholder="50"
                                  min="1"
                                  value={hardCap}
                                  onChange={(e) => setHardCap(e.target.value)}
                                />
                              </div>
                            </div>

                            <div>
                              <Label htmlFor="presaleRate">Presale rate (tokens per SOL)</Label>
                              <Input
                                id="presaleRate"
                                type="number"
                                placeholder="1000000"
                                min="1"
                                value={presaleRate}
                                onChange={(e) => setPresaleRate(e.target.value)}
                              />
                            </div>

                            <div>
                              <Label htmlFor="presaleDuration">Duration (days)</Label>
                              <Select value={presaleDuration} onValueChange={setPresaleDuration}>
                                <SelectTrigger id="presaleDuration">
                                  <SelectValue placeholder="Select" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="3">3 days</SelectItem>
                                  <SelectItem value="7">7 days</SelectItem>
                                  <SelectItem value="14">14 days</SelectItem>
                                  <SelectItem value="30">30 days</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-medium">Initial Liquidity</h3>

                        <div>
                          <Label htmlFor="initialLiquidity">Initial liquidity (SOL)</Label>
                          <Input
                            id="initialLiquidity"
                            type="number"
                            placeholder="10"
                            min="1"
                            value={initialLiquidity}
                            onChange={(e) => setInitialLiquidity(e.target.value)}
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Amount of SOL to add to the liquidity pool at launch
                          </p>
                        </div>

                        <div>
                          <Label htmlFor="listingRate">Listing rate (tokens per SOL)</Label>
                          <Input
                            id="listingRate"
                            type="number"
                            placeholder="900000"
                            min="1"
                            value={listingRate}
                            onChange={(e) => setListingRate(e.target.value)}
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Number of tokens per SOL at initial listing (launch price)
                          </p>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-medium">Launch Marketing</h3>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="launchPromotion" className="cursor-pointer">
                            Launch promotion
                            <p className="text-xs text-muted-foreground font-normal">
                              Promote your token on our platform at launch
                            </p>
                          </Label>
                          <Switch id="launchPromotion" checked={launchPromotion} onCheckedChange={setLaunchPromotion} />
                        </div>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="telegramAnnouncement" className="cursor-pointer">
                            Telegram announcement
                            <p className="text-xs text-muted-foreground font-normal">
                              Announce your token on our Telegram channel
                            </p>
                          </Label>
                          <Switch
                            id="telegramAnnouncement"
                            checked={telegramAnnouncement}
                            onCheckedChange={setTelegramAnnouncement}
                          />
                        </div>

                        <div className="flex items-center justify-between space-x-2">
                          <Label htmlFor="twitterPromotion" className="cursor-pointer">
                            Twitter promotion
                            <p className="text-xs text-muted-foreground font-normal">
                              Promote your token on our Twitter account
                            </p>
                          </Label>
                          <Switch
                            id="twitterPromotion"
                            checked={twitterPromotion}
                            onCheckedChange={setTwitterPromotion}
                          />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Token Creation Fee</AlertTitle>
                  <AlertDescription>
                    Creating a token requires a small amount of SOL to cover network fees.
                    {walletBalance !== null && (
                      <div className="mt-2">
                        Your wallet balance: <span className="font-medium">{walletBalance.toFixed(6)} SOL</span>
                      </div>
                    )}
                  </AlertDescription>
                </Alert>

                <div className="flex gap-2">
                  <Button
                    type="button"
                    onClick={runDiagnostics}
                    variant="outline"
                    disabled={isRunningDiagnostics}
                    className="flex-1"
                  >
                    {isRunningDiagnostics ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Running Diagnostics...
                      </>
                    ) : (
                      "Run Diagnostics"
                    )}
                  </Button>

                  <Button
                    type="button"
                    onClick={createToken}
                    disabled={isCreating || !connected}
                    className="flex-1 bg-[#D4AF37] hover:bg-[#B8941F] text-black"
                  >
                    {isCreating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Token...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        Create Quantum Token
                      </>
                    )}
                  </Button>
                </div>
              </>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {!isCreating && !createdToken && (
          <div className="text-sm text-muted-foreground">
            {connected ? (
              <>
                Connected: {publicKey?.toString().slice(0, 6)}...{publicKey?.toString().slice(-4)}
                {walletBalance !== null && <> | Balance: {walletBalance.toFixed(4)} SOL</>}
              </>
            ) : (
              "Please connect your wallet to create a token"
            )}
          </div>
        )}
      </CardFooter>
    </Card>
  )
}

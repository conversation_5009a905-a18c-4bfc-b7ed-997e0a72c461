"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/hooks/use-toast"
import {
  Clock,
  Users,
  Check,
  AlertCircle,
  Copy,
  Twitter,
  MessageCircle,
  Globe,
  Sparkles,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON> as RechartsTooltip } from "recharts"
import { <PERSON><PERSON><PERSON><PERSON>, Chart<PERSON>ooltipContent } from "@/components/ui/chart"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface LaunchPageProps {
  tokenAddress: string
}

export default function LaunchPage({ tokenAddress }: LaunchPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("overview")
  const [isLoading, setIsLoading] = useState(true)
  const [tokenData, setTokenData] = useState<any>(null)
  const [contributionAmount, setContributionAmount] = useState("")
  const [isContributing, setIsContributing] = useState(false)

  useEffect(() => {
    const fetchTokenData = async () => {
      setIsLoading(true)
      try {
        // In a real implementation, this would fetch data from an API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Mock data for demonstration
        setTokenData({
          name: "Global Finance Quantum",
          symbol: "GFQ",
          description:
            "Global Finance Quantum est un token innovant sur la blockchain Solana avec des fonctionnalités avancées et une sécurité renforcée.",
          status: "live", // upcoming, live, completed, failed
          imageUrl: "/placeholder.svg?height=100&width=100&text=GFQ",
          launchDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
          hardCap: 500000, // in USD
          softCap: 100000, // in USD
          raised: 275000, // in USD
          participants: 342,
          price: 0.00005, // in USD
          listingPrice: 0.00008, // in USD
          totalSupply: 1000000000,
          minContribution: 50, // in USD
          maxContribution: 5000, // in USD
          isVerified: true,
          liquidity: 70, // percentage
          lockPeriod: 180, // days
          social: {
            website: "https://example.com",
            twitter: "https://twitter.com/example",
            telegram: "https://t.me/example",
          },
          features: {
            antiBot: true,
            antiDump: true,
            autoLiquidity: true,
            burnable: true,
            deflationary: false,
          },
          distribution: {
            public: 70,
            team: 10,
            marketing: 10,
            development: 5,
            reserve: 5,
          },
          transactions: Array(5)
            .fill(0)
            .map((_, i) => ({
              id: `tx_${i}`,
              type: i % 2 === 0 ? "contribution" : "allocation",
              amount: Math.random() * 1000 + 100,
              address: `${Math.random().toString(36).substring(2, 10)}...`,
              timestamp: Date.now() - i * 3600000,
            })),
        })
      } catch (error) {
        console.error("Error fetching token data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch token data. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchTokenData()
  }, [toast, tokenAddress])

  const handleContribute = async () => {
    if (!contributionAmount) return

    setIsContributing(true)
    try {
      // In a real implementation, this would call a smart contract
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Contribution successful",
        description: `You have successfully contributed $${contributionAmount} to ${tokenData.name}.`,
      })

      // Update token data
      setTokenData((prev) => ({
        ...prev,
        raised: prev.raised + Number(contributionAmount),
        participants: prev.participants + 1,
      }))

      setContributionAmount("")
    } catch (error) {
      console.error("Error contributing:", error)
      toast({
        title: "Error",
        description: "Failed to process contribution. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsContributing(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "The text has been copied to your clipboard",
    })
  }

  const getProgressPercentage = () => {
    if (!tokenData) return 0
    return Math.min(100, Math.round((tokenData.raised / tokenData.hardCap) * 100))
  }

  const getTimeRemaining = () => {
    if (!tokenData || !tokenData.endDate) return ""

    const now = new Date()
    const end = new Date(tokenData.endDate)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return "Ended"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
      return `${days}d ${hours}h remaining`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m remaining`
    } else {
      return `${minutes}m remaining`
    }
  }

  const formatCurrency = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    } else {
      return `$${value.toFixed(2)}`
    }
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)} minutes ago`
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)} hours ago`
    } else {
      return formatDate(date)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-8 animate-pulse">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 rounded-full bg-gray-300"></div>
          <div className="space-y-2">
            <div className="h-6 bg-gray-300 rounded w-48"></div>
            <div className="h-4 bg-gray-200 rounded w-32"></div>
          </div>
        </div>

        <div className="h-10 bg-gray-300 rounded"></div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!tokenData) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>Failed to load token data. Please try again later.</AlertDescription>
      </Alert>
    )
  }

  // Prepare distribution data for pie chart
  const distributionData = Object.entries(tokenData.distribution).map(([key, value]) => ({
    name: key.charAt(0).toUpperCase() + key.slice(1),
    value: Number(value),
  }))

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={tokenData.imageUrl || "/placeholder.svg"} alt={tokenData.name} />
            <AvatarFallback>{tokenData.symbol.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">{tokenData.name}</h1>
              <Badge className="bg-[#D4AF37] hover:bg-[#B8941F] text-black">
                <Sparkles className="mr-1 h-3 w-3" />
                Quantum
              </Badge>
              {tokenData.isVerified && (
                <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                  <Check className="mr-1 h-3 w-3" />
                  Verified
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground">{tokenData.symbol}</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {tokenData.social.website && (
            <Button variant="outline" size="sm" asChild>
              <a href={tokenData.social.website} target="_blank" rel="noopener noreferrer">
                <Globe className="mr-1 h-4 w-4" />
                Website
              </a>
            </Button>
          )}
          {tokenData.social.twitter && (
            <Button variant="outline" size="sm" asChild>
              <a href={tokenData.social.twitter} target="_blank" rel="noopener noreferrer">
                <Twitter className="mr-1 h-4 w-4" />
                Twitter
              </a>
            </Button>
          )}
          {tokenData.social.telegram && (
            <Button variant="outline" size="sm" asChild>
              <a href={tokenData.social.telegram} target="_blank" rel="noopener noreferrer">
                <MessageCircle className="mr-1 h-4 w-4" />
                Telegram
              </a>
            </Button>
          )}
          <Button variant="outline" size="sm" onClick={() => copyToClipboard(tokenAddress)}>
            <Copy className="mr-1 h-4 w-4" />
            Copy Address
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tokenomics">Tokenomics</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Launch Information</CardTitle>
                <CardDescription>Current status and progress of the token launch</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Description</p>
                  <p>{tokenData.description}</p>
                </div>

                <div className="flex items-center text-sm text-amber-500 mb-2">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{getTimeRemaining()}</span>
                </div>

                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-muted-foreground">Progress</span>
                    <span>{getProgressPercentage()}%</span>
                  </div>
                  <Progress value={getProgressPercentage()} className="h-2" />
                  <div className="flex justify-between text-xs mt-1">
                    <span className="text-muted-foreground">
                      {formatCurrency(tokenData.raised)} / {formatCurrency(tokenData.hardCap)}
                    </span>
                    <div className="flex items-center">
                      <Users className="h-3 w-3 mr-1 text-muted-foreground" />
                      <span className="text-muted-foreground">{tokenData.participants}</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Token Price</p>
                    <p className="font-medium">${tokenData.price}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Listing Price</p>
                    <p className="font-medium">${tokenData.listingPrice}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Soft Cap</p>
                    <p className="font-medium">{formatCurrency(tokenData.softCap)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Hard Cap</p>
                    <p className="font-medium">{formatCurrency(tokenData.hardCap)}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Start Date</p>
                    <p className="font-medium">{formatDate(tokenData.launchDate)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">End Date</p>
                    <p className="font-medium">{formatDate(tokenData.endDate)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contribute</CardTitle>
                <CardDescription>Participate in the token launch</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm">
                    Current price: ${tokenData.price} per {tokenData.symbol}
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Min. Contribution</p>
                      <p>${tokenData.minContribution}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Max. Contribution</p>
                      <p>${tokenData.maxContribution}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (USD)</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={contributionAmount}
                      onChange={(e) => setContributionAmount(e.target.value)}
                      min={tokenData.minContribution}
                      max={tokenData.maxContribution}
                    />
                    {contributionAmount && (
                      <p className="text-sm text-muted-foreground">
                        You will receive approximately {(Number(contributionAmount) / tokenData.price).toLocaleString()}{" "}
                        {tokenData.symbol}
                      </p>
                    )}
                  </div>

                  <Button
                    className="w-full"
                    onClick={handleContribute}
                    disabled={
                      isContributing ||
                      !contributionAmount ||
                      Number(contributionAmount) < tokenData.minContribution ||
                      Number(contributionAmount) > tokenData.maxContribution
                    }
                  >
                    {isContributing ? "Processing..." : "Contribute"}
                  </Button>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="text-sm font-medium mb-2">Security Features</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex items-center">
                      <div
                        className={`w-2 h-2 rounded-full mr-2 ${tokenData.features.antiBot ? "bg-green-500" : "bg-gray-300"}`}
                      ></div>
                      <span>Anti-Bot</span>
                    </div>
                    <div className="flex items-center">
                      <div
                        className={`w-2 h-2 rounded-full mr-2 ${tokenData.features.antiDump ? "bg-green-500" : "bg-gray-300"}`}
                      ></div>
                      <span>Anti-Dump</span>
                    </div>
                    <div className="flex items-center">
                      <div
                        className={`w-2 h-2 rounded-full mr-2 ${tokenData.features.autoLiquidity ? "bg-green-500" : "bg-gray-300"}`}
                      ></div>
                      <span>Auto Liquidity</span>
                    </div>
                    <div className="flex items-center">
                      <div
                        className={`w-2 h-2 rounded-full mr-2 ${tokenData.features.burnable ? "bg-green-500" : "bg-gray-300"}`}
                      ></div>
                      <span>Burnable</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tokenomics" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Token Distribution</CardTitle>
                <CardDescription>Allocation of tokens across different categories</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ChartContainer
                  config={{
                    distribution: {
                      label: "Distribution",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={distributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {distributionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip
                        formatter={(value: number) => [`${value}%`, "Allocation"]}
                        content={<ChartTooltipContent />}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Token Details</CardTitle>
                <CardDescription>Key metrics and information about the token</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Supply</p>
                    <p className="font-medium">
                      {tokenData.totalSupply.toLocaleString()} {tokenData.symbol}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Initial Market Cap</p>
                    <p className="font-medium">{formatCurrency(tokenData.totalSupply * tokenData.listingPrice)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Liquidity %</p>
                    <p className="font-medium">{tokenData.liquidity}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Lock Period</p>
                    <p className="font-medium">{tokenData.lockPeriod} days</p>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="text-sm font-medium mb-2">Token Distribution Breakdown</h4>
                  <div className="space-y-2">
                    {Object.entries(tokenData.distribution).map(([key, value]) => (
                      <div key={key} className="flex justify-between items-center">
                        <span className="text-sm capitalize">{key}</span>
                        <div className="flex items-center">
                          <span className="text-sm font-medium">{value}%</span>
                          <div
                            className="w-16 h-2 ml-2 rounded-full"
                            style={{
                              background: `linear-gradient(90deg, ${
                                COLORS[Object.keys(tokenData.distribution).indexOf(key) % COLORS.length]
                              } ${Number(value)}%, transparent ${Number(value)}%)`,
                            }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="features" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Quantum Token Features</CardTitle>
              <CardDescription>Advanced security and functionality features</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Security Features</h3>

                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div
                        className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${tokenData.features.antiBot ? "bg-green-500" : "bg-gray-300"}`}
                      >
                        {tokenData.features.antiBot && <Check className="h-3 w-3 text-white" />}
                      </div>
                      <div>
                        <h4 className="font-medium">Anti-Bot Protection</h4>
                        <p className="text-sm text-muted-foreground">
                          Prevents automated trading bots from manipulating the market during launch.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div
                        className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${tokenData.features.antiDump ? "bg-green-500" : "bg-gray-300"}`}
                      >
                        {tokenData.features.antiDump && <Check className="h-3 w-3 text-white" />}
                      </div>
                      <div>
                        <h4 className="font-medium">Anti-Dump Mechanism</h4>
                        <p className="text-sm text-muted-foreground">
                          Limits the amount of tokens that can be sold at once to prevent price crashes.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div
                        className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${tokenData.features.autoLiquidity ? "bg-green-500" : "bg-gray-300"}`}
                      >
                        {tokenData.features.autoLiquidity && <Check className="h-3 w-3 text-white" />}
                      </div>
                      <div>
                        <h4 className="font-medium">Auto-Liquidity Generation</h4>
                        <p className="text-sm text-muted-foreground">
                          Automatically adds to liquidity pool with each transaction to ensure stable trading.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Tokenomic Features</h3>

                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div
                        className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${tokenData.features.burnable ? "bg-green-500" : "bg-gray-300"}`}
                      >
                        {tokenData.features.burnable && <Check className="h-3 w-3 text-white" />}
                      </div>
                      <div>
                        <h4 className="font-medium">Token Burning</h4>
                        <p className="text-sm text-muted-foreground">
                          Allows for permanent removal of tokens from circulation, potentially increasing value over
                          time.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div
                        className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${tokenData.features.deflationary ? "bg-green-500" : "bg-gray-300"}`}
                      >
                        {tokenData.features.deflationary && <Check className="h-3 w-3 text-white" />}
                      </div>
                      <div>
                        <h4 className="font-medium">Deflationary Mechanism</h4>
                        <p className="text-sm text-muted-foreground">
                          Automatically burns a percentage of tokens with each transaction to reduce supply over time.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className={`w-5 h-5 rounded-full mr-3 flex items-center justify-center bg-green-500`}>
                        <Check className="h-3 w-3 text-white" />
                      </div>
                      <div>
                        <h4 className="font-medium">Liquidity Locking</h4>
                        <p className="text-sm text-muted-foreground">
                          Liquidity is locked for {tokenData.lockPeriod} days to ensure long-term stability and prevent
                          rug pulls.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-6 border-t">
                <h3 className="text-lg font-medium mb-4">Advanced Quantum Features</h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2">Trading Delay</h4>
                      <p className="text-sm">
                        Initial trading is delayed by 24 hours after liquidity is added to prevent front-running.
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2">Max Transaction Limit</h4>
                      <p className="text-sm">
                        Maximum transaction size is limited to 1% of total supply to prevent market manipulation.
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2">Max Wallet Size</h4>
                      <p className="text-sm">
                        Maximum wallet holdings are limited to 2% of total supply to prevent whale dominance.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>Latest activity related to this token launch</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Address</TableHead>
                    <TableHead>Time</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tokenData.transactions.map((tx) => (
                    <TableRow key={tx.id}>
                      <TableCell>
                        <div className="flex items-center">
                          {tx.type === "contribution" ? (
                            <ArrowUpRight className="mr-2 h-4 w-4 text-green-500" />
                          ) : (
                            <ArrowDownRight className="mr-2 h-4 w-4 text-blue-500" />
                          )}
                          <span className="capitalize">{tx.type}</span>
                        </div>
                      </TableCell>
                      <TableCell>${tx.amount.toFixed(2)}</TableCell>
                      <TableCell>{tx.address}</TableCell>
                      <TableCell>{formatTimestamp(tx.timestamp)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from "recharts"
import { Plus, Trash2 } from "lucide-react"

interface DistributionItem {
  address: string
  percentage: number
  label: string
}

interface InitialDistributionProps {
  tokenSupply: string
  distribution?: DistributionItem[]
  onDistributionChange?: (distribution: DistributionItem[]) => void
}

export function InitialDistribution({
  tokenSupply,
  distribution: initialDistribution,
  onDistributionChange,
}: InitialDistributionProps) {
  const [distribution, setDistribution] = useState<DistributionItem[]>(
    initialDistribution || [
      { address: "", percentage: 70, label: "Liquidity Pool" },
      { address: "", percentage: 15, label: "Marketing" },
      { address: "", percentage: 10, label: "Team" },
      { address: "", percentage: 5, label: "Development" },
    ],
  )

  // Fonction pour mettre à jour un élément de distribution
  const updateDistribution = (index: number, field: keyof DistributionItem, value: string | number) => {
    const newDistribution = [...distribution]
    newDistribution[index] = {
      ...newDistribution[index],
      [field]: field === "percentage" ? Number(value) : value,
    }
    setDistribution(newDistribution)
    if (onDistributionChange) {
      onDistributionChange(newDistribution)
    }
  }

  // Fonction pour ajouter un nouvel élément de distribution
  const addDistributionItem = () => {
    const newDistribution = [
      ...distribution,
      { address: "", percentage: 0, label: `Allocation ${distribution.length + 1}` },
    ]
    setDistribution(newDistribution)
    if (onDistributionChange) {
      onDistributionChange(newDistribution)
    }
  }

  // Fonction pour supprimer un élément de distribution
  const removeDistributionItem = (index: number) => {
    const newDistribution = distribution.filter((_, i) => i !== index)
    setDistribution(newDistribution)
    if (onDistributionChange) {
      onDistributionChange(newDistribution)
    }
  }

  // Calculer le total des pourcentages
  const totalPercentage = distribution.reduce((sum, item) => sum + item.percentage, 0)

  // Préparer les données pour le graphique
  const chartData = distribution.map((item) => ({
    name: item.label,
    value: item.percentage,
  }))

  // Couleurs pour le graphique
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8", "#82ca9d"]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Distribution initiale des tokens</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value}%`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>

          <div className="space-y-4">
            {distribution.map((item, index) => (
              <div key={index} className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-4">
                  <Label htmlFor={`label-${index}`}>Allocation</Label>
                  <Input
                    id={`label-${index}`}
                    value={item.label}
                    onChange={(e) => updateDistribution(index, "label", e.target.value)}
                  />
                </div>
                <div className="col-span-3">
                  <Label htmlFor={`percentage-${index}`}>Pourcentage (%)</Label>
                  <Input
                    id={`percentage-${index}`}
                    type="number"
                    min="0"
                    max="100"
                    value={item.percentage}
                    onChange={(e) => updateDistribution(index, "percentage", e.target.value)}
                  />
                </div>
                <div className="col-span-4">
                  <Label htmlFor={`address-${index}`}>Adresse (optionnel)</Label>
                  <Input
                    id={`address-${index}`}
                    value={item.address}
                    onChange={(e) => updateDistribution(index, "address", e.target.value)}
                    placeholder="Adresse du portefeuille"
                  />
                </div>
                <div className="col-span-1 flex items-end">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeDistributionItem(index)}
                    disabled={distribution.length <= 1}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            <Button variant="outline" onClick={addDistributionItem} className="w-full mt-2">
              <Plus className="h-4 w-4 mr-2" />
              Ajouter une allocation
            </Button>

            <div
              className={`p-3 rounded-md ${totalPercentage === 100 ? "bg-green-50 text-green-800" : "bg-yellow-50 text-yellow-800"}`}
            >
              <p className="text-sm font-medium">
                Total: {totalPercentage}%{totalPercentage !== 100 && <span> (doit être égal à 100%)</span>}
              </p>
              <p className="text-xs mt-1">
                Nombre de tokens: {tokenSupply ? Number.parseInt(tokenSupply).toLocaleString() : "0"}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

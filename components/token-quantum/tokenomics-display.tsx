"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from "recharts"

interface DistributionItem {
  name?: string
  label?: string
  value?: number
  percentage?: number
  address?: string
}

interface TokenomicsDisplayProps {
  tokenName?: string
  tokenSymbol?: string
  tokenSupply?: number
  distribution?: DistributionItem[]
}

export function TokenomicsDisplay({
  tokenName = "",
  tokenSymbol = "",
  tokenSupply = 0,
  distribution = [],
}: TokenomicsDisplayProps) {
  // Préparer les données pour le graphique
  const distributionData = distribution.map((item) => ({
    name: item.label || item.name || "Non défini",
    value: item.percentage || item.value || 0,
  }))

  // Couleurs pour le graphique
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8", "#82ca9d", "#a4de6c", "#d0ed57"]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Aperçu des tokenomics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Nom du token</p>
              <p className="text-2xl font-bold">{tokenName || "Non défini"}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Symbole</p>
              <p className="text-2xl font-bold">{tokenSymbol || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Offre totale</p>
              <p className="text-2xl font-bold">{tokenSupply.toLocaleString()}</p>
            </div>
          </div>

          {distributionData.length > 0 ? (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={distributionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {distributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value}%`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-md">
              <p className="text-muted-foreground">Aucune donnée de distribution disponible</p>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="font-medium">Distribution détaillée</h4>
            <div className="space-y-1">
              {distributionData.length > 0 ? (
                distributionData.map((item, index) => (
                  <div key={index} className="flex justify-between">
                    <span>{item.name}</span>
                    <span className="font-medium">{item.value}%</span>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground">Aucune distribution définie</p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

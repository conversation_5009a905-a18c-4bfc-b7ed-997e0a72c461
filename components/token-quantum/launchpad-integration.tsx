"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

interface LaunchOptions {
  launchType: "fair" | "presale" | "ido" | "direct"
  softCap?: number
  hardCap?: number
  presaleRate?: number
  listingRate?: number
  liquidityPercentage?: number
  lockDuration?: number
  startDate?: Date | string
  endDate?: Date | string
  minContribution?: number
  maxContribution?: number
  launchPromotion?: boolean
  telegramAnnouncement?: boolean
  twitterPromotion?: boolean
}

interface LaunchpadIntegrationProps {
  launchOptions: LaunchOptions
  setLaunchOptions?: (options: LaunchOptions) => void
  tokenName?: string
  tokenSymbol?: string
}

export function LaunchpadIntegration({
  launchOptions,
  setLaunchOptions,
  tokenName,
  tokenSymbol,
}: LaunchpadIntegrationProps) {
  const [activeTab, setActiveTab] = useState("type")

  // Initialiser les dates avec des valeurs par défaut si elles ne sont pas définies
  const [startDate, setStartDate] = useState<Date | undefined>(
    launchOptions?.startDate
      ? typeof launchOptions.startDate === "string"
        ? new Date(launchOptions.startDate)
        : launchOptions.startDate
      : new Date(),
  )

  const [endDate, setEndDate] = useState<Date | undefined>(
    launchOptions?.endDate
      ? typeof launchOptions.endDate === "string"
        ? new Date(launchOptions.endDate)
        : launchOptions.endDate
      : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  )

  // Valeurs par défaut pour les options de lancement
  const defaultLaunchOptions: LaunchOptions = {
    launchType: "fair",
    softCap: 50,
    hardCap: 100,
    presaleRate: 1000000,
    listingRate: 900000,
    liquidityPercentage: 70,
    lockDuration: 180,
    minContribution: 0.1,
    maxContribution: 5,
  }

  // Fusionner les options par défaut avec les options fournies
  const options = { ...defaultLaunchOptions, ...launchOptions }

  // Gérer les changements de type de lancement
  const handleLaunchTypeChange = (value: "fair" | "presale" | "ido") => {
    if (setLaunchOptions) {
      setLaunchOptions({ ...options, launchType: value })
    }
  }

  // Gérer les changements d'input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    if (setLaunchOptions) {
      setLaunchOptions({ ...options, [name]: Number.parseFloat(value) || 0 })
    }
  }

  // Gérer les changements de slider
  const handleSliderChange = (name: keyof LaunchOptions, value: number[]) => {
    if (setLaunchOptions) {
      setLaunchOptions({ ...options, [name]: value[0] })
    }
  }

  // Gérer les changements de date
  const handleStartDateChange = (date: Date | undefined) => {
    setStartDate(date)
    if (date && setLaunchOptions) {
      setLaunchOptions({ ...options, startDate: date })
    }
  }

  const handleEndDateChange = (date: Date | undefined) => {
    setEndDate(date)
    if (date && setLaunchOptions) {
      setLaunchOptions({ ...options, endDate: date })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Options de lancement</CardTitle>
        <CardDescription>Configurez comment votre token sera lancé</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="type">Type</TabsTrigger>
            <TabsTrigger value="params">Paramètres</TabsTrigger>
            <TabsTrigger value="dates">Dates</TabsTrigger>
          </TabsList>

          <TabsContent value="type" className="space-y-4">
            <RadioGroup
              value={options.launchType}
              onValueChange={(value: "fair" | "presale" | "ido") => handleLaunchTypeChange(value)}
              className="space-y-4"
            >
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="fair" id="fair" className="mt-1" />
                <div>
                  <Label htmlFor="fair" className="font-medium">
                    Lancement équitable (Fair Launch)
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Le token est lancé directement sur un DEX sans presale. Tous les participants ont les mêmes
                    conditions d'achat.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <RadioGroupItem value="presale" id="presale" className="mt-1" />
                <div>
                  <Label htmlFor="presale" className="font-medium">
                    Presale
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Les investisseurs peuvent acheter des tokens avant le lancement à un prix préférentiel. Une partie
                    des fonds est utilisée pour créer de la liquidité.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <RadioGroupItem value="ido" id="ido" className="mt-1" />
                <div>
                  <Label htmlFor="ido" className="font-medium">
                    IDO (Initial DEX Offering)
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Lancement sur une plateforme IDO dédiée avec vérification KYC et processus d'allocation. Idéal pour
                    les projets plus importants.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <RadioGroupItem value="direct" id="direct" className="mt-1" />
                <div>
                  <Label htmlFor="direct" className="font-medium">
                    Lancement direct
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Le token est créé et disponible immédiatement. Option la plus simple et la plus rapide.
                  </p>
                </div>
              </div>
            </RadioGroup>
          </TabsContent>

          <TabsContent value="params" className="space-y-4">
            {/* Afficher les paramètres pour tous les types de lancement */}
            <div className="space-y-4">
              {/* Paramètres communs à tous les types de lancement */}
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="liquidityPercentage">Pourcentage de Liquidité (%)</Label>
                  <span className="text-sm text-muted-foreground">{options.liquidityPercentage}%</span>
                </div>
                <Slider
                  id="liquidityPercentage"
                  min={50}
                  max={100}
                  step={1}
                  value={[options.liquidityPercentage || 70]}
                  onValueChange={(value) => handleSliderChange("liquidityPercentage", value)}
                />
                <p className="text-xs text-muted-foreground">
                  Pourcentage des fonds collectés qui seront ajoutés à la liquidité (recommandé: 60-80%)
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="lockDuration">Durée de verrouillage (jours)</Label>
                  <span className="text-sm text-muted-foreground">{options.lockDuration} jours</span>
                </div>
                <Slider
                  id="lockDuration"
                  min={30}
                  max={365}
                  step={30}
                  value={[options.lockDuration || 180]}
                  onValueChange={(value) => handleSliderChange("lockDuration", value)}
                />
                <p className="text-xs text-muted-foreground">
                  Durée pendant laquelle la liquidité sera verrouillée (recommandé: 180+ jours)
                </p>
              </div>

              {/* Paramètres spécifiques pour presale et IDO */}
              {(options.launchType === "presale" || options.launchType === "ido") && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="softCap">Soft Cap (SOL)</Label>
                      <Input
                        id="softCap"
                        name="softCap"
                        type="number"
                        value={options.softCap}
                        onChange={handleInputChange}
                        min={1}
                      />
                      <p className="text-xs text-muted-foreground">Montant minimum à atteindre</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="hardCap">Hard Cap (SOL)</Label>
                      <Input
                        id="hardCap"
                        name="hardCap"
                        type="number"
                        value={options.hardCap}
                        onChange={handleInputChange}
                        min={options.softCap}
                      />
                      <p className="text-xs text-muted-foreground">Montant maximum à collecter</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="presaleRate">Taux de Presale (tokens par SOL)</Label>
                      <Input
                        id="presaleRate"
                        name="presaleRate"
                        type="number"
                        value={options.presaleRate}
                        onChange={handleInputChange}
                        min={1}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="listingRate">Taux de Listing (tokens par SOL)</Label>
                      <Input
                        id="listingRate"
                        name="listingRate"
                        type="number"
                        value={options.listingRate}
                        onChange={handleInputChange}
                        min={1}
                      />
                      <p className="text-xs text-muted-foreground">
                        Généralement inférieur au taux de presale pour récompenser les premiers investisseurs
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="minContribution">Contribution Min (SOL)</Label>
                      <Input
                        id="minContribution"
                        name="minContribution"
                        type="number"
                        value={options.minContribution}
                        onChange={handleInputChange}
                        min={0.01}
                        step={0.01}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxContribution">Contribution Max (SOL)</Label>
                      <Input
                        id="maxContribution"
                        name="maxContribution"
                        type="number"
                        value={options.maxContribution}
                        onChange={handleInputChange}
                        min={options.minContribution}
                      />
                    </div>
                  </div>
                </>
              )}

              {/* Paramètres spécifiques pour le lancement direct */}
              {options.launchType === "direct" && (
                <div className="p-4 bg-blue-50 rounded-md">
                  <p className="text-sm text-blue-800">
                    Le lancement direct ne nécessite pas de paramètres supplémentaires. Votre token sera créé et
                    disponible immédiatement après sa création.
                  </p>
                </div>
              )}

              {/* Paramètres spécifiques pour le fair launch */}
              {options.launchType === "fair" && (
                <div className="p-4 bg-blue-50 rounded-md">
                  <p className="text-sm text-blue-800">
                    Le lancement équitable ne nécessite pas de paramètres de presale. Votre token sera directement listé
                    sur les DEX avec la liquidité initiale que vous fournirez.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="dates" className="space-y-4">
            {(options.launchType === "presale" || options.launchType === "ido") && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Date de début</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !startDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, "PPP") : "Sélectionner une date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={handleStartDateChange}
                        initialFocus
                        disabled={(date) => date < new Date()}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>Date de fin</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !endDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, "PPP") : "Sélectionner une date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={handleEndDateChange}
                        initialFocus
                        disabled={(date) => (startDate ? date <= startDate : date <= new Date())}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            )}

            {options.launchType === "fair" && (
              <div className="p-4 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  Le lancement équitable sera effectué immédiatement après la création du token. Aucune date de début ou
                  de fin n'est nécessaire.
                </p>
              </div>
            )}

            {options.launchType === "direct" && (
              <div className="p-4 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  Le lancement direct sera effectué immédiatement après la création du token. Aucune date de début ou de
                  fin n'est nécessaire.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

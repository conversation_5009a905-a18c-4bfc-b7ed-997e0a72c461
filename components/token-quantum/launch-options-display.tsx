"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CalendarI<PERSON>, Rocket } from "lucide-react"
import { format } from "date-fns"

interface LaunchData {
  launchType: "fair" | "presale" | "ido"
  softCap: number
  hardCap: number
  presaleRate: number
  listingRate: number
  startDate: string
  endDate: string
  liquidityPercentage: number
  liquidityLockPeriod: number
  minContribution: number
  maxContribution: number
}

interface LaunchOptionsDisplayProps {
  data: LaunchData
}

export function LaunchOptionsDisplay({ data }: LaunchOptionsDisplayProps) {
  // Formater le type de lancement
  const formatLaunchType = (type: string) => {
    switch (type) {
      case "fair":
        return "Lancement équitable (Fair Launch)"
      case "presale":
        return "Presale"
      case "ido":
        return "IDO (Initial DEX Offering)"
      default:
        return type
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Aperçu du lancement</CardTitle>
      </<PERSON>H<PERSON>er>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Rocket className="h-4 w-4 text-blue-500" />
              <span className="font-medium">Type de lancement</span>
            </div>
            <Badge variant="secondary">{formatLaunchType(data.launchType)}</Badge>
          </div>

          {(data.launchType === "presale" || data.launchType === "ido") && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Soft Cap</p>
                  <p className="font-medium">{data.softCap} SOL</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Hard Cap</p>
                  <p className="font-medium">{data.hardCap} SOL</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Taux Presale</p>
                  <p className="font-medium">{data.presaleRate.toLocaleString()} tokens/SOL</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Taux Listing</p>
                  <p className="font-medium">{data.listingRate.toLocaleString()} tokens/SOL</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Liquidité</p>
                <p className="font-medium">{data.liquidityPercentage}%</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Contribution Min</p>
                  <p className="font-medium">{data.minContribution} SOL</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Contribution Max</p>
                  <p className="font-medium">{data.maxContribution} SOL</p>
                </div>
              </div>

              {data.startDate && data.endDate && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4 text-blue-500" />
                    <span className="font-medium">Calendrier</span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 pl-6">
                    <div>
                      <p className="text-sm text-muted-foreground">Début</p>
                      <p className="font-medium">{format(new Date(data.startDate), "dd/MM/yyyy")}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Fin</p>
                      <p className="font-medium">{format(new Date(data.endDate), "dd/MM/yyyy")}</p>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {data.launchType === "fair" && (
            <div className="p-4 bg-blue-50 rounded-md">
              <p className="text-sm text-blue-800">
                Votre token sera lancé directement sur les DEX sans période de presale. Les investisseurs pourront
                acheter immédiatement après le déploiement.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

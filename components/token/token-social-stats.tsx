"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { But<PERSON> } from "@/components/ui/button"
import { Twitter, MessageCircle, Globe, TrendingUp, Users, ExternalLink } from "lucide-react"
import { formatNumber } from "@/lib/utils"

interface TokenSocialStatsProps {
  tokenAddress: string
  tokenName: string
  tokenSymbol: string
}

export function TokenSocialStats({ tokenAddress, tokenName, tokenSymbol }: TokenSocialStatsProps) {
  const [socialData, setSocialData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchSocialData = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 800))

        // Générer des données aléatoires
        setSocialData({
          twitter: {
            followers: Math.floor(Math.random() * 50000) + 100,
            engagement: Math.random() * 5 + 0.5,
            url: Math.random() > 0.3 ? `https://twitter.com/${tokenSymbol.toLowerCase()}` : null,
          },
          telegram: {
            members: Math.floor(Math.random() * 20000) + 50,
            activeUsers: Math.floor(Math.random() * 5000) + 20,
            url: Math.random() > 0.3 ? `https://t.me/${tokenSymbol.toLowerCase()}` : null,
          },
          website: {
            visits: Math.floor(Math.random() * 100000) + 500,
            bounceRate: Math.random() * 50 + 30,
            url: Math.random() > 0.2 ? `https://${tokenSymbol.toLowerCase()}.io` : null,
          },
          community: {
            sentiment: Math.random() * 100,
            mentions: Math.floor(Math.random() * 1000) + 10,
          },
        })
      } catch (error) {
        console.error("Error fetching social data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSocialData()
  }, [tokenAddress, tokenSymbol])

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Présence sociale</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center gap-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const getSentimentColor = (sentiment: number) => {
    if (sentiment >= 70) return "text-green-500"
    if (sentiment >= 40) return "text-yellow-500"
    return "text-red-500"
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Présence sociale</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {socialData.twitter.url && (
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <Twitter className="h-5 w-5 text-blue-500" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div className="font-medium">Twitter</div>
                <a
                  href={socialData.twitter.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-600"
                >
                  <ExternalLink className="h-4 w-4" />
                </a>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatNumber(socialData.twitter.followers)} abonnés • {socialData.twitter.engagement.toFixed(1)}%
                d'engagement
              </div>
            </div>
          </div>
        )}

        {socialData.telegram.url && (
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <MessageCircle className="h-5 w-5 text-blue-500" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div className="font-medium">Telegram</div>
                <a
                  href={socialData.telegram.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-600"
                >
                  <ExternalLink className="h-4 w-4" />
                </a>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatNumber(socialData.telegram.members)} membres • {formatNumber(socialData.telegram.activeUsers)}{" "}
                actifs
              </div>
            </div>
          </div>
        )}

        {socialData.website.url && (
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <Globe className="h-5 w-5 text-blue-500" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div className="font-medium">Site web</div>
                <a
                  href={socialData.website.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-600"
                >
                  <ExternalLink className="h-4 w-4" />
                </a>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatNumber(socialData.website.visits)} visites • {socialData.website.bounceRate.toFixed(1)}% taux de
                rebond
              </div>
            </div>
          </div>
        )}

        <div className="flex items-center gap-4">
          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
            <TrendingUp className="h-5 w-5 text-blue-500" />
          </div>
          <div className="flex-1">
            <div className="font-medium">Sentiment communautaire</div>
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-muted rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    socialData.community.sentiment >= 70
                      ? "bg-green-500"
                      : socialData.community.sentiment >= 40
                        ? "bg-yellow-500"
                        : "bg-red-500"
                  }`}
                  style={{ width: `${socialData.community.sentiment}%` }}
                ></div>
              </div>
              <div className={`text-sm font-medium ${getSentimentColor(socialData.community.sentiment)}`}>
                {socialData.community.sentiment.toFixed(0)}%
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
            <Users className="h-5 w-5 text-blue-500" />
          </div>
          <div className="flex-1">
            <div className="font-medium">Mentions (7j)</div>
            <div className="text-sm text-muted-foreground">
              {formatNumber(socialData.community.mentions)} mentions sur les réseaux sociaux
            </div>
          </div>
        </div>

        <Button variant="outline" className="w-full">
          Rejoindre la communauté
        </Button>
      </CardContent>
    </Card>
  )
}

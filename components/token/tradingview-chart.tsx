"use client"

import type React from "react"
import { useEffect, useRef } from "react"

interface TradingViewChartProps {
  symbol: string
}

const TradingViewChart: React.FC<TradingViewChartProps> = ({ symbol }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const script = document.createElement("script")
    script.src = "https://s3.tradingview.com/tv.js"
    script.async = true
    script.onload = () => {
      if (typeof TradingView !== "undefined") {
        // Declare TradingView as a global variable
        ;(window as any).TradingView = TradingView

        new TradingView.widget({
          autosize: true,
          symbol: symbol,
          interval: "D",
          timezone: "Etc/UTC",
          theme: "dark",
          style: "1",
          locale: "fr",
          toolbar_bg: "#f1f3f6",
          enable_publishing: false,
          hide_top_toolbar: true,
          hide_legend: true,
          save_image: false,
          container_id: "tradingview_chart",
        })
      } else {
        console.error("TradingView library failed to load.")
      }
    }

    document.head.appendChild(script)

    return () => {
      // Cleanup function to remove the script when the component unmounts
      document.head.removeChild(script)
    }
  }, [symbol])

  return (
    <div className="tradingview-widget-container">
      <div id="tradingview_chart" />
      <div className="tradingview-widget-copyright">
        <a href="https://www.tradingview.com/" rel="noreferrer noopener nofollow" target="_blank">
          <span className="blue-text">Regardez</span> les marchés sur TradingView
        </a>
      </div>
    </div>
  )
}

export default TradingViewChart

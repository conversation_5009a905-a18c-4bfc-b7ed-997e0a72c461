"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ArrowDownUp, ArrowUpRight, ArrowRight, ExternalLink } from "lucide-react"
import { formatCurrency, formatNumber, formatTimeAgo, truncateAddress } from "@/lib/utils"

interface TokenTransactionsDetailProps {
  tokenAddress: string
}

interface Transaction {
  id: string
  type: "buy" | "sell" | "transfer"
  amount: number
  tokenAmount: number
  price: number
  timestamp: string
  from: string
  to: string
  txHash: string
}

export function TokenTransactionsDetail({ tokenAddress }: TokenTransactionsDetailProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [filter, setFilter] = useState<string | null>(null)

  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 800))

        // Générer des transactions aléatoires
        const transactionTypes = ["buy", "sell", "transfer"]
        const now = Date.now()

        const randomTransactions = Array.from({ length: 10 }, (_, i) => {
          const type = transactionTypes[Math.floor(Math.random() * transactionTypes.length)] as Transaction["type"]
          const minutesAgo = Math.floor(Math.random() * 60 * 24) // Jusqu'à 24 heures
          const timestamp = new Date(now - minutesAgo * 60 * 1000).toISOString()
          const tokenAmount = Math.random() * 100000 + 1000
          const price = Math.random() * 0.1
          const amount = tokenAmount * price

          return {
            id: `tx-${page}-${i}`,
            type,
            amount,
            tokenAmount,
            price,
            timestamp,
            from: `${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 6)}`,
            to: `${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 6)}`,
            txHash: `${Math.random().toString(36).substring(2, 15)}...${Math.random().toString(36).substring(2, 6)}`,
          }
        })

        // Trier par timestamp (plus récent en premier)
        randomTransactions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

        setTransactions(randomTransactions)
        setTotalPages(5) // Simuler 5 pages de résultats
      } catch (error) {
        console.error("Error fetching token transactions:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTransactions()
  }, [tokenAddress, page])

  const filteredTransactions = filter ? transactions.filter((tx) => tx.type === filter) : transactions

  const getTransactionIcon = (type: Transaction["type"]) => {
    switch (type) {
      case "buy":
        return <ArrowUpRight className="h-4 w-4 text-green-500" />
      case "sell":
        return <ArrowDownUp className="h-4 w-4 text-red-500" />
      case "transfer":
        return <ArrowRight className="h-4 w-4 text-blue-500" />
    }
  }

  const getTransactionBadge = (type: Transaction["type"]) => {
    switch (type) {
      case "buy":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Achat</Badge>
      case "sell":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Vente</Badge>
      case "transfer":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Transfert</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Transactions</CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilter(null)}
              className={filter === null ? "bg-muted" : ""}
            >
              Tout
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilter("buy")}
              className={filter === "buy" ? "bg-green-100 text-green-800 border-green-200" : ""}
            >
              Achats
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilter("sell")}
              className={filter === "sell" ? "bg-red-100 text-red-800 border-red-200" : ""}
            >
              Ventes
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilter("transfer")}
              className={filter === "transfer" ? "bg-blue-100 text-blue-800 border-blue-200" : ""}
            >
              Transferts
            </Button>
          </div>
        </div>
        <CardDescription>Historique des transactions récentes</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            {Array.from({ length: 5 }).map((_, index) => (
              <Skeleton key={index} className="h-12 w-full" />
            ))}
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">Aucune transaction trouvée pour ce token.</div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Prix</TableHead>
                    <TableHead>Montant</TableHead>
                    <TableHead>Valeur</TableHead>
                    <TableHead>De</TableHead>
                    <TableHead>À</TableHead>
                    <TableHead>Quand</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.map((tx) => (
                    <TableRow key={tx.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTransactionIcon(tx.type)}
                          {getTransactionBadge(tx.type)}
                        </div>
                      </TableCell>
                      <TableCell>{formatCurrency(tx.price)}</TableCell>
                      <TableCell>{formatNumber(tx.tokenAmount)}</TableCell>
                      <TableCell>{formatCurrency(tx.amount)}</TableCell>
                      <TableCell>
                        <span className="text-muted-foreground">{truncateAddress(tx.from)}</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-muted-foreground">{truncateAddress(tx.to)}</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-muted-foreground">{formatTimeAgo(tx.timestamp)}</span>
                      </TableCell>
                      <TableCell>
                        <a
                          href={`https://explorer.solana.com/tx/${tx.txHash}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:text-blue-600"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex justify-between items-center mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                Précédent
              </Button>
              <div className="text-sm text-muted-foreground">
                Page {page} sur {totalPages}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
              >
                Suivant
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

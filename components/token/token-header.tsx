"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Star, StarOff, Share2, Bell, BellOff } from "lucide-react"
import { formatCurrency, formatPercentage } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { useTokenStore } from "@/lib/token-store"

interface TokenHeaderProps {
  tokenName: string
  tokenSymbol: string
  tokenPrice: number
  priceChange24h: number
  isVerified?: boolean
  isQuantum?: boolean
  isNew?: boolean
}

export function TokenHeader({
  tokenName,
  tokenSymbol,
  tokenPrice,
  priceChange24h,
  isVerified = false,
  isQuantum = false,
  isNew = false,
}: TokenHeaderProps) {
  const { toast } = useToast()
  const [isNotificationsEnabled, setIsNotificationsEnabled] = useState(false)

  const favorites = useTokenStore((state) => state.favorites)
  const addFavorite = useTokenStore((state) => state.addFavorite)
  const removeFavorite = useTokenStore((state) => state.removeFavorite)

  // Vérifier si le token est dans les favoris
  const isFavorite = favorites.includes(tokenSymbol)

  // Gérer l'ajout/suppression des favoris
  const toggleFavorite = () => {
    if (isFavorite) {
      removeFavorite(tokenSymbol)
      toast({
        title: "Retiré des favoris",
        description: `${tokenName} a été retiré de vos favoris`,
      })
    } else {
      addFavorite(tokenSymbol)
      toast({
        title: "Ajouté aux favoris",
        description: `${tokenName} a été ajouté à vos favoris`,
      })
    }
  }

  // Gérer les notifications
  const toggleNotifications = () => {
    setIsNotificationsEnabled(!isNotificationsEnabled)
    toast({
      title: isNotificationsEnabled ? "Notifications désactivées" : "Notifications activées",
      description: isNotificationsEnabled
        ? `Vous ne recevrez plus de notifications pour ${tokenName}`
        : `Vous recevrez des notifications pour ${tokenName}`,
    })
  }

  // Partager le token
  const shareToken = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${tokenName} (${tokenSymbol})`,
          text: `Découvrez ${tokenName} sur Global Finance`,
          url: window.location.href,
        })
      } catch (error) {
        console.error("Erreur lors du partage:", error)
      }
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Lien copié",
        description: "L'URL du token a été copiée dans le presse-papier",
      })
    }
  }

  return (
    <div className="py-6">
      <div className="flex items-center gap-2 mb-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/market">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Retour au marché
          </Link>
        </Button>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#D4AF37] to-[#B8941F] flex items-center justify-center text-black font-bold">
            {tokenSymbol.substring(0, 2)}
          </div>

          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold">{tokenName}</h1>
              <div className="flex items-center gap-1">
                {isVerified && <Badge className="bg-blue-500 hover:bg-blue-600">Vérifié</Badge>}
                {isQuantum && <Badge className="bg-[#D4AF37] hover:bg-[#B8941F] text-black">Quantum</Badge>}
                {isNew && <Badge className="bg-green-500 hover:bg-green-600">Nouveau</Badge>}
              </div>
            </div>
            <div className="text-muted-foreground">{tokenSymbol}</div>
          </div>
        </div>

        <div className="flex flex-col items-end">
          <div className="text-2xl font-bold">{formatCurrency(tokenPrice)}</div>
          <div className={priceChange24h >= 0 ? "text-green-500 flex items-center" : "text-red-500 flex items-center"}>
            {formatPercentage(priceChange24h)} (24h)
          </div>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mt-4">
        <Button variant="outline" size="sm" onClick={toggleFavorite}>
          {isFavorite ? (
            <>
              <StarOff className="h-4 w-4 mr-2" />
              Retirer des favoris
            </>
          ) : (
            <>
              <Star className="h-4 w-4 mr-2" />
              Ajouter aux favoris
            </>
          )}
        </Button>

        <Button variant="outline" size="sm" onClick={toggleNotifications}>
          {isNotificationsEnabled ? (
            <>
              <BellOff className="h-4 w-4 mr-2" />
              Désactiver les alertes
            </>
          ) : (
            <>
              <Bell className="h-4 w-4 mr-2" />
              Activer les alertes
            </>
          )}
        </Button>

        <Button variant="outline" size="sm" onClick={shareToken}>
          <Share2 className="h-4 w-4 mr-2" />
          Partager
        </Button>
      </div>
    </div>
  )
}

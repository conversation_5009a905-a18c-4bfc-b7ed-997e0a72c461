"use client"

import { useState } from "react"
import { useGetTokenByAddress } from "@/hooks/use-get-token-by-address"
import { useGetTokenOnChainData } from "@/hooks/use-get-token-on-chain-data"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Check, AlertCircle, Globe, Twitter, MessageCircle, Github } from "lucide-react"
import { formatCurrency, formatNumber, formatPercentage, shortenAddress } from "@/lib/utils"

interface TokenDetailsProps {
  address: string
}

export function TokenDetails({ address }: TokenDetailsProps) {
  const { data: token, isLoading: isLoadingToken } = useGetTokenByAddress(address)
  const { data: onChainData, isLoading: isLoadingOnChain } = useGetTokenOnChainData(address)
  const [activeTab, setActiveTab] = useState("overview")

  if (isLoadingToken || isLoadingOnChain) {
    return (
      <Card className="w-full">
        <CardHeader>
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-1/2 mt-2" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!token) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Token non trouvé</CardTitle>
          <CardDescription>Impossible de trouver les détails du token avec l'adresse {address}</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-2xl">{token.name}</CardTitle>
            <Badge variant="outline" className="text-xs font-normal">
              {token.symbol}
            </Badge>
            {token.verified && (
              <Badge variant="secondary" className="bg-green-100 text-green-800 hover:bg-green-100">
                <Check className="h-3 w-3 mr-1" />
                Vérifié
              </Badge>
            )}
            {token.isQuantum && (
              <Badge variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-100">
                Quantum
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <ExternalLink className="h-3 w-3" />
              {shortenAddress(address)}
            </Badge>
          </div>
        </div>
        <CardDescription className="flex items-center gap-4 mt-2">
          <div className="flex items-center gap-2">
            <span className="text-2xl font-bold">{formatCurrency(token.price)}</span>
            <Badge
              variant={token.priceChange24h >= 0 ? "default" : "destructive"}
              className={token.priceChange24h >= 0 ? "bg-green-100 text-green-800 hover:bg-green-100" : ""}
            >
              {formatPercentage(token.priceChange24h)}
            </Badge>
          </div>
          <div className="flex gap-4 text-sm text-muted-foreground">
            {token.website && (
              <a
                href={token.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 hover:text-primary"
              >
                <Globe className="h-4 w-4" />
                Website
              </a>
            )}
            {token.twitter && (
              <a
                href={token.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 hover:text-primary"
              >
                <Twitter className="h-4 w-4" />
                Twitter
              </a>
            )}
            {token.telegram && (
              <a
                href={token.telegram}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 hover:text-primary"
              >
                <MessageCircle className="h-4 w-4" />
                Telegram
              </a>
            )}
            {token.github && (
              <a
                href={token.github}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 hover:text-primary"
              >
                <Github className="h-4 w-4" />
                Github
              </a>
            )}
          </div>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="on-chain">Données On-Chain</TabsTrigger>
            <TabsTrigger value="about">À propos</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Market Cap</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-lg font-bold">{formatCurrency(token.marketCap, true)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Volume 24h</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-lg font-bold">{formatCurrency(token.volume24h, true)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Liquidité</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-lg font-bold">{formatCurrency(token.liquidity, true)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Détenteurs</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-lg font-bold">{formatNumber(token.holders, true)}</div>
                </CardContent>
              </Card>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Offre en circulation</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-lg font-bold">{formatNumber(token.circulatingSupply, true)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Offre totale</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="text-lg font-bold">{formatNumber(token.totalSupply, true)}</div>
                </CardContent>
              </Card>
            </div>
            <Card>
              <CardHeader className="py-2">
                <CardTitle className="text-sm text-muted-foreground">Seuils de listing DEX</CardTitle>
              </CardHeader>
              <CardContent className="py-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {token.dexListingThresholds &&
                    Object.entries(token.dexListingThresholds).map(([dex, threshold]) => (
                      <div key={dex} className="flex items-center justify-between">
                        <span className="capitalize">{dex}</span>
                        <span className="font-medium">{formatCurrency(threshold)}</span>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="on-chain" className="space-y-4">
            {onChainData ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="py-2">
                      <CardTitle className="text-sm text-muted-foreground">Décimales</CardTitle>
                    </CardHeader>
                    <CardContent className="py-0">
                      <div className="text-lg font-bold">{onChainData.decimals}</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="py-2">
                      <CardTitle className="text-sm text-muted-foreground">Créateur</CardTitle>
                    </CardHeader>
                    <CardContent className="py-0">
                      <div className="text-lg font-bold">{onChainData.creator || "Inconnu"}</div>
                    </CardContent>
                  </Card>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="py-2">
                      <CardTitle className="text-sm text-muted-foreground">Autorité de mint</CardTitle>
                    </CardHeader>
                    <CardContent className="py-0">
                      <div className="text-lg font-bold">{onChainData.mintAuthority}</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="py-2">
                      <CardTitle className="text-sm text-muted-foreground">Autorité de gel</CardTitle>
                    </CardHeader>
                    <CardContent className="py-0">
                      <div className="text-lg font-bold">{onChainData.freezeAuthority}</div>
                    </CardContent>
                  </Card>
                </div>
                {onChainData.metadata && (
                  <Card>
                    <CardHeader className="py-2">
                      <CardTitle className="text-sm text-muted-foreground">Métadonnées</CardTitle>
                    </CardHeader>
                    <CardContent className="py-0">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <span className="text-muted-foreground">Nom:</span> {onChainData.metadata.name}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Symbole:</span> {onChainData.metadata.symbol}
                        </div>
                        <div className="col-span-2">
                          <span className="text-muted-foreground">URI:</span>{" "}
                          <a
                            href={onChainData.metadata.uri}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline"
                          >
                            {onChainData.metadata.uri}
                          </a>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            ) : (
              <Card>
                <CardContent className="py-8 flex flex-col items-center justify-center">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-center text-muted-foreground">
                    Impossible de charger les données on-chain pour ce token.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
          <TabsContent value="about" className="space-y-4">
            <Card>
              <CardHeader className="py-2">
                <CardTitle className="text-sm text-muted-foreground">Description</CardTitle>
              </CardHeader>
              <CardContent className="py-0">
                <p>{token.description || "Aucune description disponible pour ce token."}</p>
              </CardContent>
            </Card>
            {token.team && token.team.length > 0 && (
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Équipe</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {token.team.map((member, index) => (
                      <div key={index} className="flex items-center gap-2">
                        {member.avatar ? (
                          <img
                            src={member.avatar || "/placeholder.svg"}
                            alt={member.name}
                            className="h-8 w-8 rounded-full"
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                            {member.name.charAt(0)}
                          </div>
                        )}
                        <div>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-xs text-muted-foreground">{member.role}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
            {token.roadmap && token.roadmap.length > 0 && (
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Feuille de route</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="space-y-4">
                    {token.roadmap.map((item, index) => (
                      <div key={index} className="flex gap-4">
                        <div
                          className={`h-6 w-6 rounded-full flex items-center justify-center ${item.completed ? "bg-green-100 text-green-800" : "bg-muted text-muted-foreground"}`}
                        >
                          {item.completed ? <Check className="h-4 w-4" /> : index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{item.title}</div>
                          <div className="text-sm text-muted-foreground">{item.date}</div>
                          <div className="text-sm mt-1">{item.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
            {token.tokenomics && (
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-sm text-muted-foreground">Tokenomics</CardTitle>
                </CardHeader>
                <CardContent className="py-0">
                  <div className="space-y-4">
                    {(token.tokenomics.taxBuy !== undefined || token.tokenomics.taxSell !== undefined) && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {token.tokenomics.taxBuy !== undefined && (
                          <div>
                            <div className="text-muted-foreground">Taxe d'achat</div>
                            <div className="font-medium">{formatPercentage(token.tokenomics.taxBuy)}</div>
                            {token.tokenomics.taxBuyDetails && (
                              <div className="text-xs text-muted-foreground mt-1">{token.tokenomics.taxBuyDetails}</div>
                            )}
                          </div>
                        )}
                        {token.tokenomics.taxSell !== undefined && (
                          <div>
                            <div className="text-muted-foreground">Taxe de vente</div>
                            <div className="font-medium">{formatPercentage(token.tokenomics.taxSell)}</div>
                            {token.tokenomics.taxSellDetails && (
                              <div className="text-xs text-muted-foreground mt-1">
                                {token.tokenomics.taxSellDetails}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                    {token.tokenomics.distribution && (
                      <div>
                        <div className="text-muted-foreground mb-2">Distribution</div>
                        <div className="space-y-2">
                          {Object.entries(token.tokenomics.distribution).map(([category, percentage]) => (
                            <div key={category} className="flex items-center justify-between">
                              <span className="capitalize">{category}</span>
                              <span>{formatPercentage(percentage)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

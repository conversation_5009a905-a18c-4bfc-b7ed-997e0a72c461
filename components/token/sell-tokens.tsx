"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { Transaction } from "@solana/web3.js"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, AlertCircle, CheckCircle, Info } from "lucide-react"

interface SellTokensProps {
  tokenAddress: string
  tokenSymbol: string
  tokenName?: string
  currentSupply: number
  maxSupply: number
  solPriceUsd: number
  dexListingThresholdUsd: number
  walletBalance?: number // The user's token balance
}

interface SellSimulation {
  solToReceive: number
  tokenAmount: number
  pricePerToken: number
  newSupply: number
  newPrice: number
  marketCapUsd: number
  dexListingProgress: number
}

export default function SellTokens({
  tokenAddress,
  tokenSymbol,
  tokenName,
  currentSupply,
  maxSupply,
  solPriceUsd,
  dexListingThresholdUsd,
  walletBalance = 0,
}: SellTokensProps) {
  const { publicKey, signTransaction, connected } = useWallet()
  const { toast } = useToast()

  const [amount, setAmount] = useState(0) // Default amount: 0 tokens
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [simulation, setSimulation] = useState<SellSimulation | null>(null)
  const [isPreparing, setIsPreparing] = useState(false)

  // Minimum sell amount is 1 token
  const minSellAmount = 1
  const maxSellAmount = walletBalance

  // Update simulation when amount changes
  useEffect(() => {
    const fetchSimulation = async () => {
      if (amount < minSellAmount || amount > maxSellAmount) return

      try {
        setIsPreparing(true)

        const response = await fetch("/api/token/simulate-sell", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            tokenAddress,
            currentSupply,
            tokenAmount: amount,
          }),
        })

        const data = await response.json()

        if (data.success && data.simulation) {
          setSimulation(data.simulation)
        } else {
          console.error("Error simulating sell:", data.error)
        }
      } catch (error) {
        console.error("Error fetching simulation:", error)
      } finally {
        setIsPreparing(false)
      }
    }

    if (amount > 0) {
      fetchSimulation()
    } else {
      setSimulation(null)
    }
  }, [amount, tokenAddress, currentSupply, minSellAmount, maxSellAmount])

  // Handle sell
  const handleSell = async () => {
    if (!connected || !publicKey || !signTransaction) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to sell tokens.",
        variant: "destructive",
      })
      return
    }

    if (amount < minSellAmount || amount > maxSellAmount) {
      toast({
        title: "Invalid amount",
        description: `You can sell between ${minSellAmount} and ${maxSellAmount} tokens.`,
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Step 1: Get a transaction to sign
      const prepareResponse = await fetch("/api/token/sell", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tokenAddress,
          sellerAddress: publicKey.toString(),
          tokenAmount: amount,
          currentSupply,
        }),
      })

      const prepareData = await prepareResponse.json()

      if (!prepareData.success || !prepareData.transaction) {
        throw new Error(prepareData.error || "Failed to prepare transaction")
      }

      // Step 2: Deserialize and sign the transaction
      const transaction = Transaction.from(Buffer.from(prepareData.transaction, "base64"))
      const signedTransaction = await signTransaction(transaction)

      // Step 3: Send the signed transaction back to the server
      const completeResponse = await fetch("/api/token/sell", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tokenAddress,
          sellerAddress: publicKey.toString(),
          tokenAmount: amount,
          currentSupply,
          signedTransaction: Buffer.from(signedTransaction.serialize()).toString("base64"),
        }),
      })

      const completeData = await completeResponse.json()

      if (!completeData.success) {
        throw new Error(completeData.error || "Failed to complete sell")
      }

      // Sell successful
      setSuccess(true)
      toast({
        title: "Sell successful!",
        description: `You have sold ${amount} ${tokenSymbol} for ${prepareData.solToReceive.toFixed(8)} SOL.`,
      })

      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 3000)
    } catch (error: any) {
      console.error("Error selling tokens:", error)
      setError(error.message || "An error occurred while selling tokens")
      toast({
        title: "Error",
        description: error.message || "An error occurred while selling tokens",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Success view
  if (success) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <CardTitle>Sell successful!</CardTitle>
          </div>
          <CardDescription>Your sale of {tokenName || tokenSymbol} has been completed successfully.</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="bg-green-50 border-green-200">
            <AlertDescription>
              You have sold {amount} {tokenSymbol} for{" "}
              {simulation?.solToReceive.toLocaleString(undefined, { maximumFractionDigits: 8 })} SOL. The SOL has been
              sent to your wallet.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter>
          <Button className="w-full" onClick={() => window.location.reload()}>
            Continue
          </Button>
        </CardFooter>
      </Card>
    )
  }

  // Sell view
  return (
    <Card>
      <CardHeader>
        <CardTitle>Sell {tokenSymbol}</CardTitle>
        <CardDescription>
          Sell {tokenName || tokenSymbol} tokens and receive SOL based on the bonding curve pricing.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!connected && (
          <div className="bg-blue-50 p-4 rounded-md mb-4">
            <div className="flex flex-col items-center justify-center gap-4">
              <p className="text-center text-blue-800">Connect your wallet to sell tokens.</p>
              <WalletMultiButton className="!bg-primary hover:!bg-primary/90" />
            </div>
          </div>
        )}

        {connected && walletBalance <= 0 && (
          <Alert className="bg-yellow-50 border-yellow-200">
            <Info className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-yellow-800">
              You don't have any {tokenSymbol} tokens in your wallet to sell.
            </AlertDescription>
          </Alert>
        )}

        {connected && walletBalance > 0 && (
          <>
            <div className="space-y-2">
              <Label htmlFor="amount">
                Amount ({tokenSymbol}) - Maximum: {walletBalance.toLocaleString()}
              </Label>
              <div className="flex items-center space-x-2">
                <Slider
                  id="amount-slider"
                  min={0}
                  max={maxSellAmount}
                  step={1}
                  value={[amount]}
                  onValueChange={(values) => setAmount(values[0])}
                  disabled={isLoading || isPreparing}
                  className="flex-1"
                />
                <Input
                  id="amount"
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(Number(e.target.value) || 0)}
                  min={0}
                  max={maxSellAmount}
                  step={1}
                  className="w-24"
                  disabled={isLoading || isPreparing}
                />
              </div>
            </div>

            {simulation && amount > 0 && (
              <div className="rounded-md bg-muted p-3">
                <div className="text-sm text-muted-foreground mb-1">You will receive</div>
                <div className="text-2xl font-bold">
                  {simulation.solToReceive.toLocaleString(undefined, { maximumFractionDigits: 8 })} SOL
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Price per token: {simulation.pricePerToken.toFixed(8)} SOL
                </div>

                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>DEX listing progress (after sell):</span>
                    <span>{simulation.dexListingProgress.toFixed(1)}%</span>
                  </div>
                  <Progress value={simulation.dexListingProgress} className="h-1.5" />
                  <div className="text-xs text-right text-muted-foreground">
                    Threshold: {dexListingThresholdUsd.toLocaleString()} USD
                  </div>
                </div>

                <div className="mt-2 text-xs text-muted-foreground">
                  Estimated market cap after sell: $
                  {simulation.marketCapUsd.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                </div>
              </div>
            )}

            {isPreparing && !simulation && amount > 0 && (
              <div className="flex justify-center items-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </>
        )}
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          onClick={handleSell}
          disabled={
            isLoading ||
            isPreparing ||
            amount < minSellAmount ||
            amount > maxSellAmount ||
            !connected ||
            walletBalance <= 0
          }
        >
          {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          {isLoading ? "Processing transaction..." : `Sell ${tokenSymbol} tokens`}
        </Button>
      </CardFooter>
    </Card>
  )
}

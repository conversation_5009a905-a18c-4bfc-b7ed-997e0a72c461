"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>lider } from "@/components/ui/slider"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle, ArrowRight, Info, Loader2 } from "lucide-react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL, clusterApiUrl } from "@solana/web3.js"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface TokenBondingCurveProps {
  tokenAddress: string
  tokenSymbol: string
  tokenName?: string
  network?: "devnet" | "mainnet-beta"
  onPurchaseComplete?: () => void
  isInitialPurchase?: boolean
}

// Fonction pour calculer le prix selon la courbe de liaison
function calculatePrice(currentSupply: number, amount: number, reserveRatio = 0.2): number {
  // Formule simplifiée de la courbe de liaison: price = basePrice * (1 + (amount / currentSupply) * reserveRatio)
  const basePrice = 0.0001 // Prix de base en SOL
  return basePrice * (1 + (amount / currentSupply) * reserveRatio)
}

export default function TokenBondingCurve({
  tokenAddress,
  tokenSymbol,
  tokenName,
  network = "devnet",
  onPurchaseComplete,
  isInitialPurchase = false,
}: TokenBondingCurveProps) {
  const { publicKey, signTransaction, connected } = useWallet()
  const { toast } = useToast()

  const [amount, setAmount] = useState(0.1) // Montant en SOL
  const [tokenAmount, setTokenAmount] = useState(0) // Montant de tokens estimé
  const [isLoading, setIsLoading] = useState(false)
  const [isPurchaseComplete, setIsPurchaseComplete] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [walletBalance, setWalletBalance] = useState<number | null>(null)
  const [currentSupply, setCurrentSupply] = useState(1000000) // Supply actuelle (à titre d'exemple)
  const [currentPrice, setCurrentPrice] = useState(0) // Prix actuel du token
  const [marketCap, setMarketCap] = useState(0) // Capitalisation boursière estimée après achat

  // Définir un montant minimum pour l'achat initial
  const minInitialAmount = isInitialPurchase ? 0.05 : 0.01 // 0.05 SOL minimum pour l'achat initial

  // Vérifier le solde du wallet et les informations du token
  useEffect(() => {
    const fetchData = async () => {
      if (publicKey) {
        try {
          const endpoint = clusterApiUrl(network)
          const connection = new Connection(endpoint, "confirmed")

          // Récupérer le solde du wallet
          const balance = await connection.getBalance(publicKey)
          setWalletBalance(balance / LAMPORTS_PER_SOL)

          // Vérifier si le token existe
          try {
            const mintPublicKey = new PublicKey(tokenAddress)
            const accountInfo = await connection.getAccountInfo(mintPublicKey)

            if (!accountInfo) {
              setError("Token non trouvé")
            }

            // Simuler une récupération de l'offre actuelle
            // Dans une implémentation réelle, vous récupéreriez ces informations depuis la blockchain
            const seed = tokenAddress.slice(0, 8)
            const seedValue = Number.parseInt(seed, 16) / 0xffffffff
            const simulatedSupply = 1000000 + Math.floor(seedValue * 9000000)
            setCurrentSupply(simulatedSupply)

            // Calculer le prix actuel
            const price = calculatePrice(simulatedSupply, 1)
            setCurrentPrice(price)

            // Mettre à jour l'estimation des tokens
            updateTokenEstimation(amount, simulatedSupply)
          } catch (err) {
            console.error("Erreur lors de la vérification du token:", err)
            setError("Adresse de token invalide")
          }
        } catch (err) {
          console.error("Erreur lors de la récupération des données:", err)
        }
      }
    }

    fetchData()
  }, [publicKey, tokenAddress, network, amount])

  // Mettre à jour l'estimation des tokens à chaque changement de montant
  const updateTokenEstimation = (solAmount: number, supply: number = currentSupply) => {
    const price = calculatePrice(supply, solAmount)
    const tokens = solAmount / price
    setTokenAmount(tokens)

    // Calculer la capitalisation boursière estimée
    // Dans une implémentation réelle, vous utiliseriez le prix réel du SOL
    const solPrice = 50 // Prix du SOL en USD (fixé à 50 USD pour cet exemple)
    const estimatedMarketCap = tokens * price * solPrice
    setMarketCap(estimatedMarketCap)
  }

  const handleAmountChange = (value: number) => {
    // S'assurer que le montant est au moins le minimum requis pour l'achat initial
    const validAmount = isInitialPurchase ? Math.max(value, minInitialAmount) : value
    setAmount(validAmount)
    updateTokenEstimation(validAmount)
  }

  const handlePurchase = async () => {
    if (!publicKey || !signTransaction) {
      toast({
        title: "Portefeuille non connecté",
        description: "Veuillez connecter votre portefeuille pour acheter des tokens.",
        variant: "destructive",
      })
      return
    }

    if (amount < minInitialAmount) {
      toast({
        title: "Montant insuffisant",
        description: `Le montant minimum pour ${isInitialPurchase ? "l'achat initial" : "un achat"} est de ${minInitialAmount} SOL.`,
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const endpoint = clusterApiUrl(network)
      const connection = new Connection(endpoint, "confirmed")

      // Vérifier le solde du wallet
      const balance = await connection.getBalance(publicKey)
      if (balance < amount * LAMPORTS_PER_SOL) {
        throw new Error(
          `Solde insuffisant. Vous avez ${balance / LAMPORTS_PER_SOL} SOL, mais ${amount} SOL sont nécessaires.`,
        )
      }

      // Récupérer l'adresse du portefeuille de la plateforme (en dur pour cet exemple)
      const platformWallet = new PublicKey("7b8hK6apNE2dRgBXHqptZ6aXXAcrWmNPgnY6SCPcJn3f")

      // Créer une transaction pour envoyer les SOL au portefeuille de la plateforme
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: publicKey,
          toPubkey: platformWallet,
          lamports: Math.floor(amount * LAMPORTS_PER_SOL),
        }),
      )

      // Récupérer le blockhash récent
      const { blockhash } = await connection.getLatestBlockhash()
      transaction.recentBlockhash = blockhash
      transaction.feePayer = publicKey

      // Faire signer la transaction par le wallet
      const signedTransaction = await signTransaction(transaction)

      // Envoyer la transaction
      const signature = await connection.sendRawTransaction(signedTransaction.serialize())

      // Attendre la confirmation
      await connection.confirmTransaction(signature, "confirmed")

      console.log("Transaction confirmée:", signature)

      setIsPurchaseComplete(true)

      // Vérifier si le token a atteint le seuil de 50 000 USD pour le listing DEX
      const solPrice = 50 // Prix du SOL en USD (fixé à 50 USD pour cet exemple)
      const estimatedMarketCap = tokenAmount * currentPrice * solPrice

      let successMessage = `Vous avez acheté ${tokenAmount.toLocaleString()} ${tokenSymbol} pour ${amount} SOL.`

      if (isInitialPurchase) {
        successMessage = `Achat initial réussi! Votre token est maintenant lancé sur la plateforme.`
      }

      if (estimatedMarketCap >= 50000) {
        // Le token a atteint le seuil pour le listing DEX
        successMessage += " Votre token a atteint le seuil de 50 000 USD et est éligible pour être listé sur un DEX!"

        // Dans une implémentation réelle, vous appelleriez ici un service pour initier le processus de listing DEX
        try {
          // Simuler un appel API pour le listing DEX
          console.log("Initialisation du processus de listing DEX pour le token:", tokenAddress)

          // Notification supplémentaire pour le listing DEX
          setTimeout(() => {
            toast({
              title: "Éligible pour le listing DEX!",
              description:
                "Votre token a atteint le seuil de 50 000 USD et est en cours de préparation pour être listé sur un DEX.",
            })
          }, 1000)
        } catch (error) {
          console.error("Erreur lors de l'initialisation du listing DEX:", error)
        }
      }

      toast({
        title: "Achat réussi",
        description: successMessage,
      })

      if (onPurchaseComplete) {
        onPurchaseComplete()
      }
    } catch (error: any) {
      console.error("Erreur lors de l'achat:", error)
      setError(error.message || "Une erreur s'est produite lors de l'achat des tokens")

      toast({
        title: "Échec de l'achat",
        description: error.message || "Une erreur s'est produite lors de l'achat des tokens",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Afficher le résultat de l'achat
  if (isPurchaseComplete) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-green-600">Achat réussi</CardTitle>
          <CardDescription>
            {isInitialPurchase
              ? "Félicitations ! Votre token est maintenant lancé sur la plateforme."
              : `Félicitations ! Vous avez acheté des tokens ${tokenSymbol}.`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <p className="text-center font-medium">
              {tokenAmount.toLocaleString()} {tokenSymbol} ont été ajoutés à votre portefeuille
            </p>
          </div>

          {marketCap >= 50000 && (
            <Alert className="bg-blue-50 border-blue-200">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                Votre token a atteint le seuil de 50 000 USD et est éligible pour être listé sur un DEX!
              </AlertDescription>
            </Alert>
          )}

          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={() => {
                window.location.href = `/token-dashboard/${tokenAddress}`
              }}
            >
              Voir les détails du token
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Afficher le formulaire d'achat
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {isInitialPurchase ? "Achat initial requis" : `Acheter des tokens ${tokenName || tokenSymbol}`}
        </CardTitle>
        <CardDescription>
          {isInitialPurchase
            ? "Vous devez effectuer l'achat initial pour lancer votre token sur la plateforme."
            : "Achetez des tokens et bénéficiez du meilleur prix grâce à la bonding curve."}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isInitialPurchase && (
          <Alert className="bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              L'achat initial est une étape obligatoire qui permet d'initialiser la liquidité de votre token et de le
              rendre disponible sur la plateforme.
            </AlertDescription>
          </Alert>
        )}

        {!connected ? (
          <div className="flex flex-col items-center justify-center py-6">
            <p className="text-center mb-4">
              Connectez votre portefeuille pour {isInitialPurchase ? "lancer" : "acheter"} des tokens {tokenSymbol}
            </p>
            <WalletMultiButton className="bg-primary hover:bg-primary/90 text-white rounded-md px-4 py-2" />
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <div className="flex justify-between">
                <label className="text-sm font-medium">Montant à investir (SOL)</label>
                <span className="text-sm text-muted-foreground">
                  Prix: {currentPrice.toFixed(8)} SOL/{tokenSymbol}
                </span>
              </div>
              <Input
                type="number"
                value={amount}
                onChange={(e) => handleAmountChange(Number.parseFloat(e.target.value) || 0)}
                min={minInitialAmount}
                step={0.01}
                className="text-right"
              />
              <Slider
                value={[amount]}
                min={minInitialAmount}
                max={5}
                step={0.01}
                onValueChange={(values) => handleAmountChange(values[0])}
                className="mt-2"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{minInitialAmount} SOL</span>
                <span>5 SOL</span>
              </div>
            </div>

            <div className="p-4 bg-muted rounded-lg">
              <div className="flex justify-between mb-2">
                <span className="text-sm">Vous recevrez environ:</span>
                <span className="font-medium">
                  {tokenAmount.toLocaleString()} {tokenSymbol}
                </span>
              </div>

              {/* Afficher la capitalisation boursière estimée */}
              <div className="flex justify-between mb-2">
                <span className="text-sm">Capitalisation estimée:</span>
                <span className="font-medium">{marketCap.toLocaleString()} USD</span>
              </div>

              {/* Afficher la progression vers le seuil DEX */}
              <div className="mt-2 space-y-1">
                <div className="flex justify-between text-xs">
                  <span>Progression vers le listing DEX:</span>
                  <span>{Math.min(100, (marketCap / 50000) * 100).toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1.5">
                  <div
                    className="bg-primary h-1.5 rounded-full"
                    style={{ width: `${Math.min(100, (marketCap / 50000) * 100)}%` }}
                  ></div>
                </div>
                <div className="text-xs text-right text-muted-foreground">Seuil: 50 000 USD</div>
              </div>

              <div className="flex items-start gap-2 mt-4 text-xs text-muted-foreground">
                <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <p>
                  {isInitialPurchase
                    ? "L'achat initial est nécessaire pour lancer votre token. Une fois lancé, d'autres utilisateurs pourront l'acheter et le vendre."
                    : "Le prix du token augmente à chaque achat grâce à la bonding curve. Plus vous achetez tôt, plus vous bénéficiez d'un prix avantageux."}
                </p>
              </div>
            </div>

            {walletBalance !== null && (
              <div className="text-sm text-muted-foreground">
                <div className="flex justify-between">
                  <span>Solde du wallet:</span>
                  <span>{walletBalance.toFixed(4)} SOL</span>
                </div>
                {walletBalance < amount && (
                  <div className="mt-1 text-red-500">
                    Solde insuffisant. Vous avez besoin de {amount} SOL pour cette transaction.
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          disabled={
            !connected || isLoading || amount < minInitialAmount || (walletBalance !== null && walletBalance < amount)
          }
          onClick={handlePurchase}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Transaction en cours...
            </>
          ) : isInitialPurchase ? (
            `Lancer le token avec ${amount} SOL`
          ) : (
            `Acheter des tokens ${tokenSymbol}`
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle, CheckCircle, ExternalLink, Info, Loader2 } from "lucide-react"
import { useWallet } from "@solana/wallet-adapter-react"
import { getTokenListingStatus, listTokenOnDex } from "@/lib/dex-listing-service"

interface DexListingStatusProps {
  tokenAddress: string
  tokenSymbol: string
  network?: "devnet" | "mainnet-beta"
}

export default function DexListingStatus({ tokenAddress, tokenSymbol, network = "devnet" }: DexListingStatusProps) {
  const { publicKey, signTransaction, connected } = useWallet()
  const { toast } = useToast()

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [listingStatuses, setListingStatuses] = useState<{
    [dex: string]: {
      status: "listed" | "eligible" | "ineligible"
      marketCap?: number
      requiredMarketCap?: number
      progress?: number
    }
  }>({})
  const [selectedDex, setSelectedDex] = useState<string | null>(null)
  const [isListing, setIsListing] = useState(false)

  // Récupérer le statut de listing
  useEffect(() => {
    const fetchListingStatus = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const statuses = await getTokenListingStatus(tokenAddress)
        setListingStatuses(statuses)

        // Sélectionner automatiquement le premier DEX éligible
        const eligibleDex = Object.entries(statuses).find(([_, status]) => status.status === "eligible")
        if (eligibleDex) {
          setSelectedDex(eligibleDex[0])
        }
      } catch (error: any) {
        console.error("Erreur lors de la récupération du statut de listing:", error)
        setError(error.message || "Erreur lors de la récupération du statut de listing")
      } finally {
        setIsLoading(false)
      }
    }

    fetchListingStatus()
  }, [tokenAddress])

  // Gérer le listing sur un DEX
  const handleListOnDex = async () => {
    if (!publicKey || !signTransaction || !selectedDex) {
      toast({
        title: "Erreur",
        description: "Portefeuille non connecté ou DEX non sélectionné",
        variant: "destructive",
      })
      return
    }

    setIsListing(true)

    try {
      const result = await listTokenOnDex({
        tokenAddress,
        dex: selectedDex as any,
        walletPublicKey: publicKey,
        signTransaction,
      })

      if (result.success) {
        toast({
          title: "Listing réussi",
          description: `Le token ${tokenSymbol} a été listé avec succès sur ${selectedDex}`,
        })

        // Mettre à jour le statut
        setListingStatuses((prev) => ({
          ...prev,
          [selectedDex]: {
            ...prev[selectedDex],
            status: "listed",
          },
        }))
      } else {
        throw new Error(result.error || "Échec du listing")
      }
    } catch (error: any) {
      console.error("Erreur lors du listing:", error)
      toast({
        title: "Échec du listing",
        description: error.message || "Une erreur s'est produite lors du listing",
        variant: "destructive",
      })
    } finally {
      setIsListing(false)
    }
  }

  // Formater la capitalisation boursière
  const formatMarketCap = (value?: number) => {
    if (value === undefined) return "N/A"
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(value)
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Statut de listing DEX</CardTitle>
          <CardDescription>Vérification de l'éligibilité pour les DEX...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Statut de listing DEX</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  // Vérifier si le token est listé sur au moins un DEX
  const isListedAnywhere = Object.values(listingStatuses).some((status) => status.status === "listed")

  // Vérifier si le token est éligible pour au moins un DEX
  const isEligibleAnywhere = Object.values(listingStatuses).some((status) => status.status === "eligible")

  // Trouver le DEX avec la progression la plus élevée
  const highestProgressDex = Object.entries(listingStatuses)
    .filter(([_, status]) => status.status === "ineligible" && status.progress !== undefined)
    .sort(([_, a], [__, b]) => (b.progress || 0) - (a.progress || 0))[0]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Statut de listing DEX</CardTitle>
        <CardDescription>
          {isListedAnywhere
            ? `Le token ${tokenSymbol} est listé sur un ou plusieurs DEX`
            : isEligibleAnywhere
              ? `Le token ${tokenSymbol} est éligible pour être listé sur un DEX`
              : `Le token ${tokenSymbol} n'est pas encore éligible pour être listé sur un DEX`}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Statut global */}
        {isListedAnywhere ? (
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Votre token est listé sur un ou plusieurs DEX et peut être échangé par les utilisateurs.
            </AlertDescription>
          </Alert>
        ) : isEligibleAnywhere ? (
          <Alert className="bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              Votre token a atteint le seuil de capitalisation boursière requis et est éligible pour être listé sur un
              DEX.
            </AlertDescription>
          </Alert>
        ) : (
          <Alert className="bg-amber-50 border-amber-200">
            <Info className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              Votre token doit atteindre une capitalisation boursière de 50 000 USD pour être éligible au listing sur un
              DEX.
              {highestProgressDex && (
                <div className="mt-2">
                  <div className="text-sm font-medium">Progression vers l'éligibilité ({highestProgressDex[0]})</div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                    <div
                      className="bg-primary h-2.5 rounded-full"
                      style={{ width: `${highestProgressDex[1].progress || 0}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs mt-1">
                    <span>Actuel: {formatMarketCap(highestProgressDex[1].marketCap)}</span>
                    <span>Requis: {formatMarketCap(highestProgressDex[1].requiredMarketCap)}</span>
                  </div>
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Liste des DEX */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">Statut par DEX</h3>

          {Object.entries(listingStatuses).map(([dex, status]) => (
            <div key={dex} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium capitalize">{dex}</span>
                  {status.status === "listed" ? (
                    <Badge className="bg-green-100 text-green-800">Listé</Badge>
                  ) : status.status === "eligible" ? (
                    <Badge className="bg-blue-100 text-blue-800">Éligible</Badge>
                  ) : (
                    <Badge variant="outline">Non éligible</Badge>
                  )}
                </div>

                {status.status === "eligible" && (
                  <Button
                    size="sm"
                    onClick={() => setSelectedDex(dex)}
                    variant={selectedDex === dex ? "default" : "outline"}
                  >
                    Sélectionner
                  </Button>
                )}

                {status.status === "listed" && (
                  <Button size="sm" variant="outline" asChild>
                    <a href="#" target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Voir sur {dex}
                    </a>
                  </Button>
                )}
              </div>

              {status.status !== "listed" &&
                status.marketCap !== undefined &&
                status.requiredMarketCap !== undefined && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span>Progression:</span>
                      <span>{Math.min(100, (status.marketCap / status.requiredMarketCap) * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-primary h-1.5 rounded-full"
                        style={{ width: `${Math.min(100, (status.marketCap / status.requiredMarketCap) * 100)}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs mt-1">
                      <span>Actuel: {formatMarketCap(status.marketCap)}</span>
                      <span>Requis: {formatMarketCap(status.requiredMarketCap)}</span>
                    </div>
                  </div>
                )}
            </div>
          ))}
        </div>

        {/* Action de listing */}
        {isEligibleAnywhere && selectedDex && (
          <div className="mt-4">
            <Alert className="bg-blue-50 border-blue-200 mb-4">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                Vous pouvez maintenant lister votre token sur {selectedDex}. Cette action permettra aux utilisateurs
                d'échanger votre token sur ce DEX.
              </AlertDescription>
            </Alert>

            <Button className="w-full" disabled={!connected || isListing} onClick={handleListOnDex}>
              {isListing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Listing en cours...
                </>
              ) : (
                `Lister sur ${selectedDex}`
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowUpRight, ArrowDownRight, ArrowLeftRight, ExternalLink } from "lucide-react"
import { getTokenTransactions } from "@/lib/token-transaction-service"

interface TokenTransactionListProps {
  address: string
  limit?: number
}

export default function TokenTransactionList({ address, limit = 10 }: TokenTransactionListProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [transactions, setTransactions] = useState<any[]>([])
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true)
      try {
        const { transactions: txs, totalPages: pages } = await getTokenTransactions(address, { limit, page })
        setTransactions(txs)
        setTotalPages(pages)
      } catch (error) {
        console.error("Error fetching transactions:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTransactions()
  }, [address, limit, page])

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  const formatAmount = (amount: number) => {
    return amount.toLocaleString(undefined, { maximumFractionDigits: 2 })
  }

  const formatPrice = (price: number) => {
    return price.toLocaleString(undefined, { style: "currency", currency: "USD" })
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Type</TableHead>
            <TableHead>Prix</TableHead>
            <TableHead>Montant</TableHead>
            <TableHead>Valeur</TableHead>
            <TableHead>De</TableHead>
            <TableHead>À</TableHead>
            <TableHead>Heure</TableHead>
            <TableHead className="text-right">Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((tx) => (
            <TableRow key={tx.signature}>
              <TableCell>
                <Badge
                  variant={tx.type === "buy" ? "default" : tx.type === "sell" ? "destructive" : "outline"}
                  className="flex items-center gap-1 w-fit"
                >
                  {tx.type === "buy" && <ArrowUpRight className="h-3 w-3" />}
                  {tx.type === "sell" && <ArrowDownRight className="h-3 w-3" />}
                  {tx.type === "transfer" && <ArrowLeftRight className="h-3 w-3" />}
                  {tx.type === "buy" ? "Achat" : tx.type === "sell" ? "Vente" : "Transfert"}
                </Badge>
              </TableCell>
              <TableCell>{formatPrice(tx.price)}</TableCell>
              <TableCell>{formatAmount(tx.amount)}</TableCell>
              <TableCell>{formatPrice(tx.value)}</TableCell>
              <TableCell className="font-mono text-xs">{tx.from}</TableCell>
              <TableCell className="font-mono text-xs">{tx.to}</TableCell>
              <TableCell>{formatDate(tx.timestamp)}</TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="sm" asChild>
                  <a
                    href={`https://explorer.solana.com/tx/${tx.signature}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center"
                  >
                    <ExternalLink className="h-3.5 w-3.5 mr-1" />
                    Explorer
                  </a>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {totalPages > 1 && (
        <div className="flex justify-center gap-2 mt-4">
          <Button variant="outline" size="sm" onClick={() => setPage((p) => Math.max(1, p - 1))} disabled={page === 1}>
            Précédent
          </Button>
          <span className="flex items-center px-2">
            Page {page} sur {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
          >
            Suivant
          </Button>
        </div>
      )}
    </div>
  )
}

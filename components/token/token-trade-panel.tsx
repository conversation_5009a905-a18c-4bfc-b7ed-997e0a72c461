"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@solana/wallet-adapter-react"
import { Loader2, AlertCircle, TrendingUp, TrendingDown, RefreshCw } from "lucide-react"
import bondingCurveService from "@/lib/bonding-curve-service"

interface TokenTradePanelProps {
  tokenAddress: string
  tokenSymbol: string
}

export default function TokenTradePanel({ tokenAddress, tokenSymbol }: TokenTradePanelProps) {
  const { public<PERSON><PERSON>, connected, signTransaction } = useWallet()
  const { toast } = useToast()

  const [activeTab, setActiveTab] = useState<"buy" | "sell">("buy")
  const [amount, setAmount] = useState<number>(100)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [estimatedCost, setEstimatedCost] = useState<number>(0)
  const [estimatedPrice, setEstimatedPrice] = useState<number>(0)
  const [currentTokenPrice, setCurrentTokenPrice] = useState<number>(0)

  // Initialiser la bonding curve lors du chargement
  useEffect(() => {
    // Si la bonding curve n'est pas encore initialisée pour ce token
    // initialiser avec des valeurs par défaut
    const curves = bondingCurveService.simulatePriceCurve(tokenAddress, 100)
    if (curves.length === 0) {
      bondingCurveService.initializeCurve({
        initialPrice: 0.00001,
        reserveRatio: 0.2,
        currentSupply: 1000000,
        totalSupply: 1000000000,
        tokenAddress,
      })
    }

    // Récupérer le prix actuel
    const price = bondingCurveService.calculateCurrentPrice(tokenAddress)
    setCurrentTokenPrice(price)
  }, [tokenAddress])

  // Mettre à jour l'estimation de coût/récompense
  useEffect(() => {
    if (amount <= 0) {
      setEstimatedCost(0)
      setEstimatedPrice(0)
      return
    }

    if (activeTab === "buy") {
      const cost = bondingCurveService.calculateBuyPrice(tokenAddress, amount)
      setEstimatedCost(cost)
      setEstimatedPrice(cost / amount)
    } else {
      const reward = bondingCurveService.calculateSellReward(tokenAddress, amount)
      setEstimatedCost(reward)
      setEstimatedPrice(reward / amount)
    }
  }, [amount, activeTab, tokenAddress])

  // Fonction pour gérer l'achat/vente
  const handleTrade = async () => {
    setLoading(true)
    setError(null)

    try {
      if (!connected || !publicKey || !signTransaction) {
        throw new Error("Veuillez connecter votre wallet pour effectuer cette opération.")
      }

      if (amount <= 0) {
        throw new Error("Veuillez entrer un montant valide.")
      }

      const tradeParams = {
        tokenAddress,
        amount,
        userAddress: publicKey.toString(),
        isBuy: activeTab === "buy",
      }

      // Créer la transaction d'achat ou de vente
      const result =
        activeTab === "buy"
          ? await bondingCurveService.createBuyTransaction(tradeParams)
          : await bondingCurveService.createSellTransaction(tradeParams)

      if (!result.success || !result.transaction) {
        throw new Error(
          result.error || `Échec de la création de la transaction de ${activeTab === "buy" ? "achat" : "vente"}`,
        )
      }

      // Signer la transaction
      const signedTransaction = await signTransaction(result.transaction)

      // En production, il faudrait envoyer la transaction signée à la blockchain
      // et mettre à jour l'offre en circulation dans la bonding curve

      // Simuler le succès de la transaction
      toast({
        title: `${activeTab === "buy" ? "Achat" : "Vente"} réussi`,
        description: `Vous avez ${activeTab === "buy" ? "acheté" : "vendu"} ${amount} ${tokenSymbol} pour ${result.cost?.toFixed(6)} SOL.`,
      })

      // Mettre à jour l'offre en circulation
      if (activeTab === "buy") {
        // Augmenter l'offre en circulation
        const curve = bondingCurveService.simulatePriceCurve(tokenAddress, 1)[0]
        if (curve) {
          bondingCurveService.updateSupply(tokenAddress, curve.supply + amount)
        }
      } else {
        // Diminuer l'offre en circulation
        const curve = bondingCurveService.simulatePriceCurve(tokenAddress, 1)[0]
        if (curve) {
          bondingCurveService.updateSupply(tokenAddress, Math.max(0, curve.supply - amount))
        }
      }

      // Mettre à jour le prix actuel
      const newPrice = bondingCurveService.calculateCurrentPrice(tokenAddress)
      setCurrentTokenPrice(newPrice)
    } catch (err: any) {
      console.error(`Error during ${activeTab}:`, err)
      setError(
        err.message || `Une erreur s'est produite lors de l'${activeTab === "buy" ? "achat" : "vente"} des tokens`,
      )
      toast({
        title: "Erreur",
        description:
          err.message || `Une erreur s'est produite lors de l'${activeTab === "buy" ? "achat" : "vente"} des tokens`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Trading de {tokenSymbol}</CardTitle>
        <CardDescription>
          Achetez ou vendez des tokens {tokenSymbol} via notre système de bonding curve.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <div>
            <span className="text-sm text-muted-foreground">Prix actuel</span>
            <div className="text-xl font-bold">{currentTokenPrice.toFixed(8)} SOL</div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Rafraîchir le prix actuel
              const price = bondingCurveService.calculateCurrentPrice(tokenAddress)
              setCurrentTokenPrice(price)
            }}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Rafraîchir
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as "buy" | "sell")}>
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="buy" className="flex items-center">
              <TrendingUp className="h-4 w-4 mr-1" />
              Acheter
            </TabsTrigger>
            <TabsTrigger value="sell" className="flex items-center">
              <TrendingDown className="h-4 w-4 mr-1" />
              Vendre
            </TabsTrigger>
          </TabsList>

          <TabsContent value="buy">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="buy-amount">Montant de tokens à acheter</Label>
                <Input
                  id="buy-amount"
                  type="number"
                  min="1"
                  value={amount}
                  onChange={(e) => setAmount(Number(e.target.value))}
                  disabled={loading}
                />
              </div>

              <div className="p-4 bg-muted rounded-md">
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-muted-foreground">Coût estimé:</span>
                  <span className="font-medium">{estimatedCost.toFixed(6)} SOL</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-muted-foreground">Prix unitaire estimé:</span>
                  <span className="font-medium">
                    {estimatedPrice.toFixed(8)} SOL/{tokenSymbol}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Vous recevrez:</span>
                  <span className="font-medium">
                    {amount} {tokenSymbol}
                  </span>
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                Note: Le prix augmente avec chaque achat selon la formule de la bonding curve.
              </div>
            </div>
          </TabsContent>

          <TabsContent value="sell">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sell-amount">Montant de tokens à vendre</Label>
                <Input
                  id="sell-amount"
                  type="number"
                  min="1"
                  value={amount}
                  onChange={(e) => setAmount(Number(e.target.value))}
                  disabled={loading}
                />
              </div>

              <div className="p-4 bg-muted rounded-md">
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-muted-foreground">Vous recevrez:</span>
                  <span className="font-medium">{estimatedCost.toFixed(6)} SOL</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-muted-foreground">Prix unitaire estimé:</span>
                  <span className="font-medium">
                    {estimatedPrice.toFixed(8)} SOL/{tokenSymbol}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Vous vendez:</span>
                  <span className="font-medium">
                    {amount} {tokenSymbol}
                  </span>
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                Note: Le prix diminue avec chaque vente selon la formule de la bonding curve.
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter>
        <Button className="w-full" onClick={handleTrade} disabled={loading || !connected || amount <= 0}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Traitement en cours...
            </>
          ) : activeTab === "buy" ? (
            `Acheter ${amount} ${tokenSymbol}`
          ) : (
            `Vendre ${amount} ${tokenSymbol}`
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}

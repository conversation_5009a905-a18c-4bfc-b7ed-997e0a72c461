"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { getSimilarTokens } from "@/lib/token-service"
import { formatCurrency, formatPercentage, shortenAddress } from "@/lib/utils"
import Link from "next/link"

interface SimilarTokensProps {
  address: string
  limit?: number
}

export function SimilarTokens({ address, limit = 5 }: SimilarTokensProps) {
  const [tokens, setTokens] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      if (!address) {
        setTokens([])
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const similarTokens = await getSimilarTokens(address, limit)
        setTokens(similarTokens)
      } catch (err: any) {
        console.error("Erreur lors de la récupération des tokens similaires:", err)
        setError(err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [address, limit])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Tokens similaires</CardTitle>
        <CardDescription>Tokens avec des caractéristiques similaires</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: limit }).map((_, index) => (
              <Skeleton key={index} className="h-12 w-full" />
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-4 text-muted-foreground">
            Une erreur s'est produite lors du chargement des tokens similaires.
          </div>
        ) : tokens.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">Aucun token similaire trouvé.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Token</TableHead>
                <TableHead className="text-right">Prix</TableHead>
                <TableHead className="text-right">Variation 24h</TableHead>
                <TableHead className="text-right">Market Cap</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tokens.map((token) => (
                <TableRow key={token.address}>
                  <TableCell>
                    <Link href={`/token/${token.address}`} className="flex items-center gap-2 hover:underline">
                      <div className="font-medium">{token.symbol}</div>
                      <div className="text-muted-foreground text-xs">{shortenAddress(token.address)}</div>
                    </Link>
                  </TableCell>
                  <TableCell className="text-right">{formatCurrency(token.price)}</TableCell>
                  <TableCell className="text-right">
                    <Badge
                      variant={token.priceChange24h >= 0 ? "outline" : "destructive"}
                      className={
                        token.priceChange24h >= 0
                          ? "bg-green-100 text-green-800 hover:bg-green-100"
                          : "bg-red-100 text-red-800 hover:bg-red-100"
                      }
                    >
                      {formatPercentage(token.priceChange24h)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">{formatCurrency(token.marketCap, true)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

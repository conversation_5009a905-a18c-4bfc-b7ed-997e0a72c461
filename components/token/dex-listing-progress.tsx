"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import bondingCurveService from "@/lib/bonding-curve-service"

interface DexListingProgressProps {
  tokenAddress: string
  tokenSymbol: string
}

export default function DexListingProgress({ tokenAddress, tokenSymbol }: DexListingProgressProps) {
  const [progress, setProgress] = useState(0)
  const [threshold, setThreshold] = useState(50000) // Valeur par défaut: 50,000 USD
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchProgress = async () => {
      setIsLoading(true)
      try {
        // Récupérer les paramètres de la bonding curve
        const curveParams = bondingCurveService.getCurveParams(tokenAddress)
        if (curveParams) {
          setThreshold(curveParams.dexListingThreshold)
        }

        // Calculer la progression
        const progress = bondingCurveService.calculateDexListingProgress(tokenAddress)
        setProgress(progress)
      } catch (error) {
        console.error("Error fetching DEX listing progress:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchProgress()

    // Mettre à jour la progression toutes les 30 secondes
    const interval = setInterval(fetchProgress, 30000)
    return () => clearInterval(interval)
  }, [tokenAddress])

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">Progression vers le listing DEX</CardTitle>
        <CardDescription>
          {progress < 100
            ? `Le token ${tokenSymbol} sera automatiquement listé sur un DEX lorsque la capitalisation boursière atteindra $${threshold.toLocaleString()}`
            : `Le token ${tokenSymbol} est éligible pour le listing DEX!`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>0%</span>
            <span>{progress.toFixed(2)}%</span>
            <span>100%</span>
          </div>
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>$0</span>
            <span>${Math.floor((threshold * progress) / 100).toLocaleString()}</span>
            <span>${threshold.toLocaleString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

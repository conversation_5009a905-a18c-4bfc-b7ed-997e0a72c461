"use client"

import { useState, useEffect } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { Progress } from "@/components/ui/progress"
import { Wallet } from "lucide-react"

interface TokenHolder {
  id: string
  address: string
  balance: number
  percentage: number
  isContract: boolean
}

interface TokenHoldersListProps {
  tokenAddress: string
}

export default function TokenHoldersList({ tokenAddress }: TokenHoldersListProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [holders, setHolders] = useState<TokenHolder[]>([])

  useEffect(() => {
    const fetchHolders = async () => {
      try {
        // Dans une implémentation réelle, vous feriez un appel API ici
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Données simulées
        const totalSupply = 1000000000
        let remainingPercentage = 100

        const mockHolders: TokenHolder[] = Array.from({ length: 10 }).map((_, index) => {
          // Le dernier détenteur prend le reste du pourcentage
          const percentage =
            index === 9
              ? remainingPercentage
              : Math.min(remainingPercentage, Math.random() * 20 + (index === 0 ? 15 : 1))
          remainingPercentage -= percentage

          const balance = (totalSupply * percentage) / 100

          return {
            id: `holder-${index}`,
            address: `${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 10)}`,
            balance,
            percentage,
            isContract: Math.random() > 0.8,
          }
        })

        // Trier par pourcentage décroissant
        mockHolders.sort((a, b) => b.percentage - a.percentage)

        setHolders(mockHolders)
      } catch (error) {
        console.error("Erreur lors du chargement des détenteurs:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchHolders()
  }, [tokenAddress])

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("fr-FR").format(num)
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Rang</TableHead>
            <TableHead>Adresse</TableHead>
            <TableHead>Solde</TableHead>
            <TableHead>Pourcentage</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {holders.map((holder, index) => (
            <TableRow key={holder.id}>
              <TableCell className="font-medium">{index + 1}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Wallet className="h-4 w-4 text-muted-foreground" />
                  <span className="font-mono text-xs">{holder.address}</span>
                  {holder.isContract && (
                    <span className="text-xs bg-muted text-muted-foreground px-1.5 py-0.5 rounded">Contrat</span>
                  )}
                </div>
              </TableCell>
              <TableCell>{formatNumber(holder.balance)} SOLP</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <span className="w-12 text-right">{holder.percentage.toFixed(2)}%</span>
                  <Progress value={holder.percentage} className="h-2 w-24" />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Calendar, Check, Clock, ExternalLink, Rocket } from "lucide-react"
import { formatDate } from "@/lib/utils"

interface TokenUpcomingEventsProps {
  tokenAddress: string
}

interface Event {
  id: string
  title: string
  description: string
  date: string
  type: "listing" | "airdrop" | "burn" | "partnership" | "update"
  completed: boolean
  url?: string
}

export function TokenUpcomingEvents({ tokenAddress }: TokenUpcomingEventsProps) {
  const [events, setEvents] = useState<Event[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchEvents = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Générer des événements aléatoires
        const now = new Date()
        const eventTypes: Event["type"][] = ["listing", "airdrop", "burn", "partnership", "update"]
        const eventTitles = {
          listing: ["Listing sur Raydium", "Listing sur Jupiter", "Listing sur Orca", "Listing sur Binance"],
          airdrop: ["Airdrop communautaire", "Distribution de récompenses", "Airdrop pour les early adopters"],
          burn: ["Burn programmé", "Destruction de tokens", "Réduction de l'offre"],
          partnership: ["Partenariat stratégique", "Collaboration avec un projet DeFi", "Intégration cross-chain"],
          update: ["Mise à jour majeure", "Lancement de la V2", "Nouvelle fonctionnalité"],
        }

        const eventDescriptions = {
          listing: [
            "Ajout de liquidité et ouverture des échanges sur cette plateforme majeure.",
            "Lancement officiel sur cet exchange décentralisé.",
          ],
          airdrop: ["Distribution de tokens aux détenteurs actuels.", "Récompenses pour la communauté active."],
          burn: ["Réduction de l'offre totale pour augmenter la valeur.", "Destruction programmée de tokens."],
          partnership: [
            "Collaboration stratégique pour développer l'écosystème.",
            "Alliance avec un projet complémentaire.",
          ],
          update: ["Déploiement de nouvelles fonctionnalités.", "Amélioration de la sécurité et des performances."],
        }

        // Créer entre 2 et 5 événements
        const numEvents = Math.floor(Math.random() * 4) + 2
        const randomEvents: Event[] = []

        for (let i = 0; i < numEvents; i++) {
          const type = eventTypes[Math.floor(Math.random() * eventTypes.length)]
          const titles = eventTitles[type]
          const descriptions = eventDescriptions[type]

          // Date aléatoire entre -5 jours et +30 jours
          const eventDate = new Date(now)
          eventDate.setDate(now.getDate() + Math.floor(Math.random() * 36) - 5)

          randomEvents.push({
            id: `event-${i}`,
            title: titles[Math.floor(Math.random() * titles.length)],
            description: descriptions[Math.floor(Math.random() * descriptions.length)],
            date: eventDate.toISOString(),
            type,
            completed: eventDate < now,
            url: Math.random() > 0.5 ? `https://example.com/event-${i}` : undefined,
          })
        }

        // Trier par date
        randomEvents.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

        setEvents(randomEvents)
      } catch (error) {
        console.error("Error fetching token events:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchEvents()
  }, [tokenAddress])

  const getEventBadge = (type: Event["type"]) => {
    switch (type) {
      case "listing":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Listing</Badge>
      case "airdrop":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Airdrop</Badge>
      case "burn":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Burn</Badge>
      case "partnership":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Partenariat</Badge>
      case "update":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Mise à jour</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Événements à venir</CardTitle>
        <CardDescription>Roadmap et événements importants</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex gap-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : events.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">Aucun événement à venir pour ce token.</div>
        ) : (
          <div className="relative">
            <div className="absolute left-6 top-0 bottom-0 w-[1px] bg-muted" />
            <div className="space-y-8">
              {events.map((event) => (
                <div key={event.id} className="flex gap-4 relative">
                  <div
                    className={`h-12 w-12 rounded-full flex items-center justify-center z-10 ${event.completed ? "bg-green-100" : "bg-muted/50"}`}
                  >
                    {event.completed ? (
                      <Check className="h-6 w-6 text-green-600" />
                    ) : (
                      <Calendar className="h-6 w-6 text-muted-foreground" />
                    )}
                  </div>
                  <div className="space-y-1 pt-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{event.title}</h4>
                      {getEventBadge(event.type)}
                    </div>
                    <p className="text-sm text-muted-foreground">{event.description}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatDate(new Date(event.date), "full")}</span>
                      </div>
                      {event.url && (
                        <a
                          href={event.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-1 text-blue-500 hover:text-blue-600"
                        >
                          <ExternalLink className="h-3 w-3" />
                          <span>Plus d'infos</span>
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div className="flex gap-4 relative">
                <div className="h-12 w-12 rounded-full flex items-center justify-center z-10 bg-[#D4AF37]/20">
                  <Rocket className="h-6 w-6 text-[#D4AF37]" />
                </div>
                <div className="space-y-1 pt-3">
                  <h4 className="font-medium">Et plus encore à venir...</h4>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

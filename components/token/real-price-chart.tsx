"use client"

import { useEffect, useRef, useState } from "react"
import { create<PERSON>hart, ColorType, type IChartApi, type ISeriesApi, type UTCTimestamp } from "lightweight-charts"
import tokenPriceService from "@/lib/token-price-service"

interface RealPriceChartProps {
  tokenAddress: string
  tokenSymbol: string
  height?: number
}

export default function RealPriceChart({ tokenAddress, tokenSymbol, height = 400 }: RealPriceChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const [period, setPeriod] = useState<"24h" | "7d" | "30d" | "90d">("7d")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Références pour le chart et les séries
  const chartRef = useRef<IChartApi | null>(null)
  const candlestickSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null)
  const volumeSeriesRef = useRef<ISeriesApi<"Histogram"> | null>(null)

  useEffect(() => {
    // Fonction pour initialiser le graphique
    const initChart = async () => {
      if (!chartContainerRef.current) return

      setIsLoading(true)
      setError(null)

      try {
        // Récupérer les données OHLC
        const ohlcData = await tokenPriceService.getTokenOHLCData(tokenAddress, period)

        if (ohlcData.length === 0) {
          throw new Error("Aucune donnée disponible pour ce token")
        }

        // Nettoyer le conteneur
        chartContainerRef.current.innerHTML = ""

        // Créer le graphique
        const chart = createChart(chartContainerRef.current, {
          layout: {
            background: { type: ColorType.Solid, color: "#1a1a1a" },
            textColor: "rgba(255, 255, 255, 0.9)",
          },
          grid: {
            vertLines: { color: "#334158" },
            horzLines: { color: "#334158" },
          },
          width: chartContainerRef.current.clientWidth,
          height,
          timeScale: {
            timeVisible: true,
            secondsVisible: false,
            borderColor: "#485c7b",
          },
          rightPriceScale: {
            borderColor: "#485c7b",
          },
        })

        // Ajouter la série de chandeliers
        const candlestickSeries = chart.addCandlestickSeries({
          upColor: "#26a69a",
          downColor: "#ef5350",
          borderVisible: false,
          wickUpColor: "#26a69a",
          wickDownColor: "#ef5350",
        })

        // Préparer les données pour le graphique
        const candleData = ohlcData.map((item) => ({
          time: (item.time / 1000) as UTCTimestamp,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
        }))

        candlestickSeries.setData(candleData)

        // Ajouter la série de volume
        const volumeSeries = chart.addHistogramSeries({
          color: "#26a69a",
          priceFormat: {
            type: "volume",
          },
          priceScaleId: "",
          scaleMargins: {
            top: 0.8,
            bottom: 0,
          },
        })

        // Préparer les données de volume
        const volumeData = ohlcData.map((item) => ({
          time: (item.time / 1000) as UTCTimestamp,
          value: item.volume,
          color: item.close >= item.open ? "#26a69a" : "#ef5350",
        }))

        volumeSeries.setData(volumeData)

        // Sauvegarder les références
        chartRef.current = chart
        candlestickSeriesRef.current = candlestickSeries
        volumeSeriesRef.current = volumeSeries

        // Ajuster la taille du graphique lors du redimensionnement de la fenêtre
        const handleResize = () => {
          if (chartContainerRef.current && chart) {
            chart.applyOptions({ width: chartContainerRef.current.clientWidth })
          }
        }

        window.addEventListener("resize", handleResize)

        // Nettoyer
        return () => {
          window.removeEventListener("resize", handleResize)
          if (chart) {
            chart.remove()
          }
        }
      } catch (err: any) {
        console.error("Error initializing chart:", err)
        setError(err.message || "Une erreur s'est produite lors de l'initialisation du graphique")
      } finally {
        setIsLoading(false)
      }
    }

    initChart()
  }, [tokenAddress, period, height])

  // Mettre à jour les données lorsque la période change
  const handlePeriodChange = (newPeriod: "24h" | "7d" | "30d" | "90d") => {
    setPeriod(newPeriod)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Prix de {tokenSymbol}</h3>
        <div className="flex space-x-2">
          <button
            className={`px-2 py-1 text-xs rounded ${
              period === "24h" ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
            }`}
            onClick={() => handlePeriodChange("24h")}
          >
            24h
          </button>
          <button
            className={`px-2 py-1 text-xs rounded ${
              period === "7d" ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
            }`}
            onClick={() => handlePeriodChange("7d")}
          >
            7j
          </button>
          <button
            className={`px-2 py-1 text-xs rounded ${
              period === "30d" ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
            }`}
            onClick={() => handlePeriodChange("30d")}
          >
            30j
          </button>
          <button
            className={`px-2 py-1 text-xs rounded ${
              period === "90d" ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
            }`}
            onClick={() => handlePeriodChange("90d")}
          >
            90j
          </button>
        </div>
      </div>

      {isLoading && (
        <div className="flex items-center justify-center h-[400px] bg-muted/20 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Chargement du graphique...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="flex items-center justify-center h-[400px] bg-muted/20 rounded-lg">
          <div className="text-center text-red-500">
            <p>{error}</p>
          </div>
        </div>
      )}

      <div
        ref={chartContainerRef}
        className={`w-full ${isLoading || error ? "hidden" : ""}`}
        style={{ height: `${height}px` }}
      />
    </div>
  )
}

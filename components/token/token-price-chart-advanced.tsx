"use client"

import { useEffect, useRef, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { ArrowUp, ArrowDown, Clock, DollarSign } from "lucide-react"
import tokenChartService, { type PriceData, type TradeData } from "@/lib/token-chart-service"

// Importation dynamique de lightweight-charts
import dynamic from "next/dynamic"
const LightweightCharts = dynamic(() => import("lightweight-charts"), { ssr: false })

interface TokenPriceChartAdvancedProps {
  tokenAddress: string
  tokenSymbol: string
  tokenName?: string
}

export default function TokenPriceChartAdvanced({
  tokenAddress,
  tokenSymbol,
  tokenName,
}: TokenPriceChartAdvancedProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [timeframe, setTimeframe] = useState<"1h" | "24h" | "7d" | "30d">("24h")
  const [chartData, setChartData] = useState<PriceData[]>([])
  const [trades, setTrades] = useState<TradeData[]>([])
  const [stats, setStats] = useState({
    marketCap: 0,
    volume24h: 0,
    liquidity: 0,
    holders: 0,
  })

  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartInstanceRef = useRef<any>(null)
  const seriesRef = useRef<any>(null)

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        const data = await tokenChartService.getTokenChartData(tokenAddress, timeframe)
        setChartData(data.prices)
        setTrades(data.trades)
        setStats({
          marketCap: data.marketCap,
          volume24h: data.volume24h,
          liquidity: data.liquidity,
          holders: data.holders,
        })
      } catch (error) {
        console.error("Error fetching chart data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [tokenAddress, timeframe])

  useEffect(() => {
    if (!chartContainerRef.current || chartData.length === 0 || isLoading) return

    // Nettoyer le graphique précédent s'il existe
    if (chartInstanceRef.current) {
      chartInstanceRef.current.remove()
      chartInstanceRef.current = null
      seriesRef.current = null
    }

    // Créer un nouveau graphique
    const chart = LightweightCharts.createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: 400,
      layout: {
        background: { color: "#1E1E1E" },
        textColor: "#D9D9D9",
      },
      grid: {
        vertLines: { color: "#2B2B43" },
        horzLines: { color: "#2B2B43" },
      },
      timeScale: {
        borderColor: "#2B2B43",
        timeVisible: true,
      },
      crosshair: {
        mode: LightweightCharts.CrosshairMode.Normal,
      },
      rightPriceScale: {
        borderColor: "#2B2B43",
      },
    })

    // Ajouter une série de prix
    const areaSeries = chart.addAreaSeries({
      topColor: "rgba(76, 175, 80, 0.56)",
      bottomColor: "rgba(76, 175, 80, 0.04)",
      lineColor: "rgba(76, 175, 80, 1)",
      lineWidth: 2,
    })

    // Formater les données pour le graphique
    const formattedData = chartData.map((item) => ({
      time: item.time,
      value: item.close,
    }))

    areaSeries.setData(formattedData)

    // Ajuster l'échelle de temps
    chart.timeScale().fitContent()

    // Stocker les références
    chartInstanceRef.current = chart
    seriesRef.current = areaSeries

    // Gérer le redimensionnement
    const handleResize = () => {
      if (chartContainerRef.current && chartInstanceRef.current) {
        chartInstanceRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        })
      }
    }

    window.addEventListener("resize", handleResize)

    // S'abonner aux mises à jour en temps réel
    let unsubscribe: (() => void) | null = null

    const setupRealTimeUpdates = async () => {
      unsubscribe = await tokenChartService.subscribeToTokenUpdates(tokenAddress, (newData) => {
        if (seriesRef.current) {
          seriesRef.current.update({
            time: newData.time,
            value: newData.close,
          })
        }
      })
    }

    setupRealTimeUpdates()

    return () => {
      window.removeEventListener("resize", handleResize)
      if (unsubscribe) unsubscribe()
      if (chartInstanceRef.current) {
        chartInstanceRef.current.remove()
        chartInstanceRef.current = null
        seriesRef.current = null
      }
    }
  }, [chartData, isLoading, tokenAddress])

  if (isLoading) {
    return <Skeleton className="h-[500px] w-full" />
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">{tokenName || tokenSymbol}</h2>
          <div className="flex items-center gap-2 text-muted-foreground">
            <span>{tokenSymbol}</span>
            <Badge variant="outline" className="text-xs">
              {tokenAddress.substring(0, 4)}...{tokenAddress.substring(tokenAddress.length - 4)}
            </Badge>
          </div>
        </div>

        <Tabs value={timeframe} onValueChange={(value) => setTimeframe(value as any)} className="w-full md:w-auto">
          <TabsList className="grid grid-cols-4 w-full md:w-[300px]">
            <TabsTrigger value="1h">1H</TabsTrigger>
            <TabsTrigger value="24h">24H</TabsTrigger>
            <TabsTrigger value="7d">7J</TabsTrigger>
            <TabsTrigger value="30d">30J</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Prix</span>
              <span className="text-xl font-bold">
                ${chartData.length > 0 ? chartData[chartData.length - 1].close.toFixed(8) : "0.00"}
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Cap. Marché</span>
              <span className="text-xl font-bold">${stats.marketCap.toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Volume (24h)</span>
              <span className="text-xl font-bold">${stats.volume24h.toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">Détenteurs</span>
              <span className="text-xl font-bold">{stats.holders.toLocaleString()}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Graphique des prix</CardTitle>
            </CardHeader>
            <CardContent>
              <div ref={chartContainerRef} className="w-full h-[400px]" />
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="h-[500px] overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle>Transactions récentes</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-[440px] overflow-y-auto">
                {trades.length > 0 ? (
                  <div className="divide-y">
                    {trades.map((trade, index) => (
                      <div key={index} className="p-3 hover:bg-muted/50 transition-colors">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            {trade.type === "buy" ? (
                              <ArrowUp className="h-4 w-4 text-green-500" />
                            ) : (
                              <ArrowDown className="h-4 w-4 text-red-500" />
                            )}
                            <span className={trade.type === "buy" ? "text-green-500" : "text-red-500"}>
                              {trade.type === "buy" ? "Achat" : "Vente"}
                            </span>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {formatDistanceToNow(new Date(trade.time * 1000), { addSuffix: true, locale: fr })}
                          </span>
                        </div>
                        <div className="mt-1 flex justify-between">
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                            <span>${trade.price.toFixed(8)}</span>
                          </div>
                          <span>
                            {trade.amount.toLocaleString()} {tokenSymbol}
                          </span>
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground truncate">{trade.account}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-[400px] text-muted-foreground">
                    <Clock className="h-12 w-12 mb-2 opacity-20" />
                    <p>Aucune transaction récente</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from "recharts"
import { Info, TrendingUp, TrendingDown, RefreshCw, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Avatar } from "@/components/ui/avatar"
import BuyTokens from "./buy-tokens"
import SellTokens from "./sell-tokens"

interface TokenDashboardProps {
  tokenAddress: string
  tokenSymbol: string
  tokenName?: string
  tokenImageUrl?: string
  initialMetrics: {
    currentSupply: number
    maxSupply: number
    currentPrice: number
    marketCapUsd: number
    solPriceUsd: number
    dexListingThresholdUsd: number
    dexListingProgress: number
    holders: number
    transactions: number
  }
  userBalance?: number
}

interface TokenTransaction {
  id: string
  type: "buy" | "sell"
  amount: number
  price: number
  total: number
  timestamp: string
  address: string
  txSignature: string
}

export default function TokenDashboard({
  tokenAddress,
  tokenSymbol,
  tokenName,
  tokenImageUrl,
  initialMetrics,
  userBalance = 0,
}: TokenDashboardProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("overview")
  const [isLoading, setIsLoading] = useState(false)
  const [metrics, setMetrics] = useState(initialMetrics)
  const [transactions, setTransactions] = useState<TokenTransaction[]>([])
  const [priceHistory, setPriceHistory] = useState<{ timestamp: string; price: number }[]>([])

  // Fetch token data
  const fetchTokenData = async () => {
    setIsLoading(true)

    try {
      // Fetch token metrics
      const metricsResponse = await fetch(`/api/token/${tokenAddress}/metrics`)
      const metricsData = await metricsResponse.json()

      if (metricsData.success) {
        setMetrics(metricsData.metrics)
      }

      // Fetch token transactions
      const transactionsResponse = await fetch(`/api/token/${tokenAddress}/transactions`)
      const transactionsData = await transactionsResponse.json()

      if (transactionsData.success) {
        setTransactions(transactionsData.transactions)
      }

      // Fetch token price history
      const priceHistoryResponse = await fetch(`/api/token/${tokenAddress}/price-history`)
      const priceHistoryData = await priceHistoryResponse.json()

      if (priceHistoryData.success) {
        setPriceHistory(priceHistoryData.priceHistory)
      }
    } catch (error) {
      console.error("Error fetching token data:", error)
      toast({
        title: "Error",
        description: "Failed to fetch token data. Please try again later.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data on initial load
  useEffect(() => {
    fetchTokenData()
  }, [tokenAddress])

  // Format currency
  const formatCurrency = (value: number, currency = "USD", maximumFractionDigits = 2) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
      maximumFractionDigits,
    }).format(value)
  }

  // Format large numbers
  const formatNumber = (value: number, maximumFractionDigits = 2) => {
    return new Intl.NumberFormat("en-US", {
      maximumFractionDigits,
    }).format(value)
  }

  // Format SOL
  const formatSol = (value: number) => {
    return `${formatNumber(value, 8)} SOL`
  }

  // Format address
  const formatAddress = (address: string) => {
    return `${address.substring(0, 4)}...${address.substring(address.length - 4)}`
  }

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleString()
  }

  // Format time ago
  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const seconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (seconds < 60) return `${seconds}s ago`
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`
    const days = Math.floor(hours / 24)
    return `${days}d ago`
  }

  return (
    <div className="space-y-6">
      {/* Token Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <Avatar className="h-12 w-12">
                {tokenImageUrl ? (
                  <img src={tokenImageUrl || "/placeholder.svg"} alt={tokenSymbol} className="object-cover" />
                ) : (
                  <div className="bg-primary text-white h-full w-full flex items-center justify-center text-lg font-bold">
                    {tokenSymbol.substring(0, 2)}
                  </div>
                )}
              </Avatar>
              <div>
                <h1 className="text-2xl font-bold">
                  {tokenName || tokenSymbol} ({tokenSymbol})
                </h1>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>Price: {formatSol(metrics.currentPrice)}</span>
                  <span>•</span>
                  <span>Market Cap: {formatCurrency(metrics.marketCapUsd)}</span>
                </div>
              </div>
            </div>

            <Button size="sm" onClick={fetchTokenData} disabled={isLoading} variant="outline">
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Token Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="buy">Buy</TabsTrigger>
          <TabsTrigger value="sell">Sell</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* DEX Listing Progress */}
          <Card>
            <CardHeader>
              <CardTitle>DEX Listing Progress</CardTitle>
              <CardDescription>
                When the market cap reaches {formatCurrency(metrics.dexListingThresholdUsd)}, this token will be
                automatically listed on a DEX.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>
                    Current: {formatCurrency(metrics.marketCapUsd)} ({formatNumber(metrics.dexListingProgress, 1)}%)
                  </span>
                  <span>Goal: {formatCurrency(metrics.dexListingThresholdUsd)}</span>
                </div>
                <Progress value={metrics.dexListingProgress} className="h-2" />
              </div>

              {metrics.dexListingProgress >= 75 && (
                <Alert className="mt-4 bg-blue-50 border-blue-200">
                  <Info className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800">
                    This token is close to reaching the DEX listing threshold! Once the threshold is reached, it will be
                    automatically listed on a DEX.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Token Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Current Price</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatSol(metrics.currentPrice)}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(metrics.currentPrice * metrics.solPriceUsd)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Market Cap</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(metrics.marketCapUsd)}</div>
                <p className="text-xs text-muted-foreground">{formatSol(metrics.marketCapUsd / metrics.solPriceUsd)}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Supply</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(metrics.currentSupply)}</div>
                <p className="text-xs text-muted-foreground">
                  {formatNumber((metrics.currentSupply / metrics.maxSupply) * 100, 1)}% of max supply
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Price Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Price History</CardTitle>
              <CardDescription>Token price over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {priceHistory.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={priceHistory}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                      <YAxis tickFormatter={(value) => formatSol(value)} />
                      <Tooltip
                        formatter={(value: number) => [formatSol(value), "Price"]}
                        labelFormatter={(label) => new Date(label).toLocaleString()}
                      />
                      <Line type="monotone" dataKey="price" stroke="#8884d8" activeDot={{ r: 8 }} strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <p className="text-muted-foreground">No price history data available yet.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>Latest token transactions</CardDescription>
            </CardHeader>
            <CardContent>
              {transactions.length > 0 ? (
                <div className="space-y-4">
                  {transactions.map((tx) => (
                    <div key={tx.id} className="flex items-center justify-between py-2 border-b last:border-0">
                      <div className="flex items-center gap-3">
                        {tx.type === "buy" ? (
                          <TrendingUp className="h-5 w-5 text-green-500" />
                        ) : (
                          <TrendingDown className="h-5 w-5 text-red-500" />
                        )}
                        <div>
                          <div className="font-medium">
                            {tx.type === "buy" ? "Buy" : "Sell"} {formatNumber(tx.amount)} {tokenSymbol}
                          </div>
                          <div className="text-sm text-muted-foreground flex items-center gap-1">
                            <span>{formatTimeAgo(tx.timestamp)}</span>
                            <span>•</span>
                            <span>{formatAddress(tx.address)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatSol(tx.total)}</div>
                        <div className="text-sm text-muted-foreground">
                          {formatSol(tx.price)} per {tokenSymbol}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">No transactions yet.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Buy Tab */}
        <TabsContent value="buy">
          <BuyTokens
            tokenAddress={tokenAddress}
            tokenSymbol={tokenSymbol}
            tokenName={tokenName}
            currentSupply={metrics.currentSupply}
            maxSupply={metrics.maxSupply}
            solPriceUsd={metrics.solPriceUsd}
            dexListingThresholdUsd={metrics.dexListingThresholdUsd}
          />
        </TabsContent>

        {/* Sell Tab */}
        <TabsContent value="sell">
          <SellTokens
            tokenAddress={tokenAddress}
            tokenSymbol={tokenSymbol}
            tokenName={tokenName}
            currentSupply={metrics.currentSupply}
            maxSupply={metrics.maxSupply}
            solPriceUsd={metrics.solPriceUsd}
            dexListingThresholdUsd={metrics.dexListingThresholdUsd}
            walletBalance={userBalance}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

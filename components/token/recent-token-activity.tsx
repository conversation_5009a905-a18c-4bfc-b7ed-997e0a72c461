"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { ArrowUpRight, ArrowDownLeft, ArrowLeftRight, ExternalLink } from "lucide-react"
import { formatCurrency, formatNumber, formatTimeAgo, shortenAddress } from "@/lib/utils"
import { getTokenTransactions } from "@/lib/token-transaction-service"

interface RecentTokenActivityProps {
  tokenAddress: string | string[]
  limit?: number
}

export function RecentTokenActivity({ tokenAddress, limit = 5 }: RecentTokenActivityProps) {
  const [transactions, setTransactions] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const result = await getTokenTransactions(tokenAddress, { limit })
        setTransactions(result.transactions)
      } catch (err: any) {
        setError(err)
        console.error("Erreur lors de la récupération des transactions:", err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTransactions()
  }, [tokenAddress, limit])

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "buy":
        return <ArrowDownLeft className="h-4 w-4 text-green-500" />
      case "sell":
        return <ArrowUpRight className="h-4 w-4 text-red-500" />
      case "transfer":
        return <ArrowLeftRight className="h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  const getTransactionBadge = (type: string) => {
    switch (type) {
      case "buy":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            Achat
          </Badge>
        )
      case "sell":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
            Vente
          </Badge>
        )
      case "transfer":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            Transfert
          </Badge>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Activité récente</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: limit }).map((_, i) => (
              <Skeleton key={i} className="h-14 w-full" />
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-4 text-muted-foreground">
            Une erreur s'est produite lors du chargement des transactions.
          </div>
        ) : transactions.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">Aucune transaction récente.</div>
        ) : (
          <div className="space-y-4">
            {transactions.map((tx) => (
              <div key={tx.signature} className="flex items-center gap-3 border-b pb-3 last:border-0 last:pb-0">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    tx.type === "buy"
                      ? "bg-green-100 text-green-800"
                      : tx.type === "sell"
                        ? "bg-red-100 text-red-800"
                        : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {getTransactionIcon(tx.type)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm truncate">{shortenAddress(tx.from)}</span>
                    {getTransactionBadge(tx.type)}
                  </div>

                  <div className="flex items-center justify-between mt-1">
                    <div className="text-xs text-muted-foreground">
                      {tx.type === "transfer" ? (
                        <span>→ {shortenAddress(tx.to)}</span>
                      ) : (
                        <span>
                          {formatNumber(tx.amount)} tokens à {formatCurrency(tx.price)}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">{formatTimeAgo(tx.timestamp)}</span>
                      <a
                        href={`https://explorer.solana.com/tx/${tx.signature}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:text-blue-600"
                      >
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

"use client"

import { useState } from "react"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, CheckCircle, ExternalLink, HelpCircle, Loader2, Rocket } from "lucide-react"
import { formatCurrency } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { useWallet } from "@solana/wallet-adapter-react"

interface MarketCapProgressBarProps {
  tokenAddress: string | string[]
  marketCap: number
  dexListingThresholds: {
    raydium?: number
    orca?: number
    jupiter?: number
    [key: string]: number | undefined
  }
}

export default function MarketCapProgressBar({
  tokenAddress,
  marketCap,
  dexListingThresholds,
}: MarketCapProgressBarProps) {
  const { toast } = useToast()
  const { connected, publicKey, signTransaction } = useWallet()
  const [isLoading, setIsLoading] = useState(false)
  const [listingDex, setListingDex] = useState<string | null>(null)
  const [listingProgress, setListingProgress] = useState(0)
  const [listingStep, setListingStep] = useState("")
  const [listingError, setListingError] = useState<string | null>(null)
  const [dexStatus, setDexStatus] = useState<{ [key: string]: "pending" | "listed" | "eligible" | "ineligible" }>({
    raydium: marketCap >= (dexListingThresholds.raydium || 0) ? "eligible" : "ineligible",
    orca: marketCap >= (dexListingThresholds.orca || 0) ? "eligible" : "ineligible",
    jupiter: marketCap >= (dexListingThresholds.jupiter || 0) ? "eligible" : "ineligible",
  })
  const [showDetails, setShowDetails] = useState(false)

  // Calculer le seuil maximum pour la barre de progression
  const maxThreshold = Math.max(...(Object.values(dexListingThresholds).filter(Boolean) as number[]))

  // Calculer le pourcentage de progression
  const progressPercentage = Math.min((marketCap / maxThreshold) * 100, 100)

  // Lister le token sur un DEX spécifique
  const listOnDex = async (dex: string) => {
    if (!connected || !publicKey || !signTransaction) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to continue.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setListingDex(dex)
    setListingProgress(0)
    setListingStep("")
    setListingError(null)

    try {
      // Étape 1: Préparation de la transaction
      setListingProgress(20)
      setListingStep("Preparing transaction...")
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Étape 2: Signature de la transaction
      setListingProgress(40)
      setListingStep("Signing transaction...")
      await new Promise((resolve) => setTimeout(resolve, 800))

      // Étape 3: Envoi de la transaction
      setListingProgress(60)
      setListingStep("Sending transaction...")
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Étape 4: Confirmation de la transaction
      setListingProgress(80)
      setListingStep("Confirming transaction...")
      await new Promise((resolve) => setTimeout(resolve, 1200))

      // Étape 5: Finalisation
      setListingProgress(100)
      setListingStep("Listing successful!")

      // Simuler un succès
      toast({
        title: "Listing successful",
        description: `The token has been successfully listed on ${dex.charAt(0).toUpperCase() + dex.slice(1)}`,
      })

      // Mettre à jour le statut
      setDexStatus((prev) => ({
        ...prev,
        [dex]: "listed",
      }))

      // Réinitialiser après 3 secondes
      setTimeout(() => {
        setListingDex(null)
      }, 3000)
    } catch (error: any) {
      console.error(`Error listing on ${dex}:`, error)
      setListingError(error.message || `An error occurred while listing on ${dex}`)

      toast({
        title: "Listing failed",
        description: error.message || `An error occurred while listing on ${dex}`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Obtenir la couleur de la barre de progression en fonction du pourcentage
  const getProgressColor = () => {
    if (progressPercentage >= 100) return "bg-green-500"
    if (progressPercentage >= 75) return "bg-blue-500"
    if (progressPercentage >= 50) return "bg-yellow-500"
    if (progressPercentage >= 25) return "bg-orange-500"
    return "bg-red-500"
  }

  // Obtenir l'icône de statut pour chaque DEX
  const getStatusIcon = (status: "pending" | "listed" | "eligible" | "ineligible") => {
    switch (status) {
      case "listed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "eligible":
        return <Rocket className="h-4 w-4 text-blue-500" />
      case "pending":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case "ineligible":
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  // Obtenir le texte de statut pour chaque DEX
  const getStatusText = (status: "pending" | "listed" | "eligible" | "ineligible") => {
    switch (status) {
      case "listed":
        return "Listed"
      case "eligible":
        return "Eligible"
      case "pending":
        return "Pending"
      case "ineligible":
        return "Not eligible"
    }
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Market Cap</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full p-0">
                    <HelpCircle className="h-3 w-3" />
                    <span className="sr-only">Info</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs text-xs">
                    Market Cap is calculated by multiplying the current token price by its circulating supply. When the
                    Market Cap reaches certain thresholds, the token becomes eligible for listing on different DEXes.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <span className="text-sm font-bold">{formatCurrency(marketCap)}</span>
        </div>

        <div className="relative">
          <Progress value={progressPercentage} max={100} className={`h-2 ${getProgressColor()}`} />

          {/* Threshold markers */}
          {Object.entries(dexListingThresholds).map(([dex, threshold]) => {
            if (!threshold) return null

            const position = (threshold / maxThreshold) * 100
            if (position > 100) return null

            return (
              <TooltipProvider key={dex}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className="absolute top-0 w-0.5 h-4 bg-white border-l border-r border-foreground/20"
                      style={{ left: `${position}%`, transform: "translateX(-50%)" }}
                    ></div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">
                      {dex.charAt(0).toUpperCase() + dex.slice(1)}: {formatCurrency(threshold)}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )
          })}
        </div>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="link" size="sm" className="text-xs p-0 h-auto" onClick={() => setShowDetails(!showDetails)}>
          {showDetails ? "Hide details" : "Show details"}
        </Button>

        <div className="text-xs text-muted-foreground">
          {progressPercentage >= 100
            ? "All thresholds reached"
            : `${progressPercentage.toFixed(0)}% of the way to full listing`}
        </div>
      </div>

      {showDetails && (
        <Card className="mt-2">
          <CardContent className="p-3">
            {listingDex && (
              <div className="mb-4 space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Listing on {listingDex.charAt(0).toUpperCase() + listingDex.slice(1)}...
                  </span>
                  <span className="text-xs">{listingProgress}%</span>
                </div>
                <Progress value={listingProgress} max={100} className="h-2" />
                <div className="text-xs text-center">{listingStep}</div>

                {listingError && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{listingError}</AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            <div className="space-y-3">
              {Object.entries(dexListingThresholds).map(([dex, threshold]) => {
                if (!threshold) return null

                const status = dexStatus[dex] || "ineligible"
                const isEligibleForListing = status === "eligible"
                const isCurrentlyListing = listingDex === dex && isLoading

                return (
                  <div key={dex} className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(status)}
                      <span className="text-sm capitalize">{dex}</span>
                      <span className="text-xs text-muted-foreground">{formatCurrency(threshold)}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-xs">{getStatusText(status)}</span>

                      {status === "listed" ? (
                        <Button variant="outline" size="sm" className="h-7 text-xs" asChild>
                          <a
                            href={`https://${dex}.io/swap?inputCurrency=SOL&outputCurrency=${tokenAddress}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View
                          </a>
                        </Button>
                      ) : isEligibleForListing ? (
                        <Button
                          variant="default"
                          size="sm"
                          className="h-7 text-xs"
                          onClick={() => listOnDex(dex)}
                          disabled={isLoading || !connected}
                        >
                          {isCurrentlyListing ? (
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          ) : (
                            <Rocket className="h-3 w-3 mr-1" />
                          )}
                          {isCurrentlyListing ? "Listing..." : "List"}
                        </Button>
                      ) : (
                        <Button variant="outline" size="sm" className="h-7 text-xs" disabled>
                          Not available
                        </Button>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

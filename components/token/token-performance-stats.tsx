"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { LineChart, Line, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip, ReferenceLine } from "recharts"
import { Download, TrendingUp, TrendingDown, Minus } from "lucide-react"
import { formatCurrency, formatNumber, formatPercentage } from "@/lib/utils"

interface TokenPerformanceStatsProps {
  tokenAddress: string
  tokenData: any
}

export function TokenPerformanceStats({ tokenAddress, tokenData }: TokenPerformanceStatsProps) {
  const [timeframe, setTimeframe] = useState<"24h" | "7d" | "30d" | "90d" | "1y" | "all">("30d")
  const [chartType, setChartType] = useState<"price" | "volume" | "marketCap" | "holders">("price")

  // Générer des données historiques en fonction de la période sélectionnée
  const generateHistoricalData = () => {
    const now = Date.now()
    const data = []
    let points: number
    let interval: number
    const basePrice = tokenData.price * 0.8 // Prix de base légèrement inférieur au prix actuel
    const baseVolume = tokenData.volume24h * 0.7
    const baseMarketCap = tokenData.marketCap * 0.8
    const baseHolders = tokenData.holders * 0.6

    // Déterminer le nombre de points et l'intervalle en fonction de la période
    switch (timeframe) {
      case "24h":
        points = 24
        interval = 60 * 60 * 1000 // 1 heure
        break
      case "7d":
        points = 7
        interval = 24 * 60 * 60 * 1000 // 1 jour
        break
      case "30d":
        points = 30
        interval = 24 * 60 * 60 * 1000 // 1 jour
        break
      case "90d":
        points = 90
        interval = 24 * 60 * 60 * 1000 // 1 jour
        break
      case "1y":
        points = 12
        interval = 30 * 24 * 60 * 60 * 1000 // 1 mois
        break
      case "all":
        points = 24
        interval = 30 * 24 * 60 * 60 * 1000 // 1 mois
        break
      default:
        points = 30
        interval = 24 * 60 * 60 * 1000 // 1 jour
    }

    // Tendance générale (croissance, déclin ou stable)
    const trend = Math.random() > 0.6 ? "growth" : Math.random() > 0.5 ? "decline" : "stable"
    const trendFactor =
      trend === "growth"
        ? (points / 100) * (1 + Math.random())
        : trend === "decline"
          ? -(points / 100) * (1 + Math.random())
          : 0

    // Générer les données historiques
    for (let i = 0; i < points; i++) {
      // Variation aléatoire autour de la tendance
      const dayVariation = Math.random() * 0.05 - 0.025
      const trendProgress = trendFactor * (i / points)

      // Appliquer la tendance et la variation
      const priceMultiplier = 1 + trendProgress + dayVariation
      const volumeMultiplier = 1 + trendProgress + (Math.random() * 0.1 - 0.05)
      const marketCapMultiplier = priceMultiplier // Corrélé au prix
      const holdersMultiplier = 1 + trendProgress / 2 + (Math.random() * 0.03 - 0.01) // Moins volatile

      // Ajouter des événements spéciaux pour certains points
      const hasEvent = Math.random() > 0.9
      const eventMultiplier = hasEvent ? (Math.random() > 0.5 ? 1.2 : 0.8) : 1

      data.push({
        timestamp: now - (points - i) * interval,
        price: basePrice * priceMultiplier * eventMultiplier,
        volume: baseVolume * volumeMultiplier * (hasEvent ? 1.5 : 1),
        marketCap: baseMarketCap * marketCapMultiplier * eventMultiplier,
        holders: Math.floor(baseHolders * holdersMultiplier * (hasEvent ? 1.1 : 1)),
        hasEvent,
        eventType: hasEvent
          ? Math.random() > 0.5
            ? "Annonce"
            : Math.random() > 0.5
              ? "Listing"
              : "Partenariat"
          : null,
      })
    }

    return data
  }

  const historicalData = generateHistoricalData()

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp)
    if (timeframe === "24h") {
      return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    } else if (timeframe === "7d" || timeframe === "30d" || timeframe === "90d") {
      return date.toLocaleDateString([], { day: "numeric", month: "short" })
    } else {
      return date.toLocaleDateString([], { month: "short", year: "numeric" })
    }
  }

  const getChartColor = () => {
    switch (chartType) {
      case "price":
        return "#8b5cf6" // Violet
      case "volume":
        return "#10b981" // Vert
      case "marketCap":
        return "#f97316" // Orange
      case "holders":
        return "#3b82f6" // Bleu
      default:
        return "#8b5cf6"
    }
  }

  const getChartTitle = () => {
    switch (chartType) {
      case "price":
        return "Évolution du prix"
      case "volume":
        return "Volume d'échanges"
      case "marketCap":
        return "Capitalisation boursière"
      case "holders":
        return "Nombre de détenteurs"
      default:
        return "Performance"
    }
  }

  const getChartDescription = () => {
    switch (chartType) {
      case "price":
        return "Évolution du prix du token au fil du temps"
      case "volume":
        return "Volume d'échanges quotidien"
      case "marketCap":
        return "Évolution de la capitalisation boursière"
      case "holders":
        return "Évolution du nombre de détenteurs"
      default:
        return "Données de performance"
    }
  }

  const getYAxisFormatter = () => {
    switch (chartType) {
      case "price":
        return (value: number) => formatCurrency(value, true)
      case "volume":
        return (value: number) => formatCurrency(value, true)
      case "marketCap":
        return (value: number) => formatCurrency(value, true)
      case "holders":
        return (value: number) => formatNumber(value)
      default:
        return (value: number) => value.toString()
    }
  }

  const getTooltipFormatter = (value: number) => {
    switch (chartType) {
      case "price":
        return formatCurrency(value)
      case "volume":
        return formatCurrency(value)
      case "marketCap":
        return formatCurrency(value)
      case "holders":
        return formatNumber(value)
      default:
        return value.toString()
    }
  }

  // Calculer les statistiques de performance
  const calculatePerformanceStats = () => {
    if (!historicalData.length) return null

    const firstPoint = historicalData[0]
    const lastPoint = historicalData[historicalData.length - 1]
    const change = lastPoint[chartType] - firstPoint[chartType]
    const percentChange = (change / firstPoint[chartType]) * 100

    const min = Math.min(...historicalData.map((item) => item[chartType]))
    const max = Math.max(...historicalData.map((item) => item[chartType]))
    const avg = historicalData.reduce((sum, item) => sum + item[chartType], 0) / historicalData.length

    return {
      change,
      percentChange,
      min,
      max,
      avg,
    }
  }

  const performanceStats = calculatePerformanceStats()

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle>{getChartTitle()}</CardTitle>
            <CardDescription>{getChartDescription()}</CardDescription>
          </div>
          <div className="flex flex-wrap gap-2">
            <Tabs value={chartType} onValueChange={(value) => setChartType(value as any)}>
              <TabsList>
                <TabsTrigger value="price">Prix</TabsTrigger>
                <TabsTrigger value="volume">Volume</TabsTrigger>
                <TabsTrigger value="marketCap">Cap. Marché</TabsTrigger>
                <TabsTrigger value="holders">Détenteurs</TabsTrigger>
              </TabsList>
            </Tabs>
            <Tabs value={timeframe} onValueChange={(value) => setTimeframe(value as any)}>
              <TabsList>
                <TabsTrigger value="24h">24h</TabsTrigger>
                <TabsTrigger value="7d">7j</TabsTrigger>
                <TabsTrigger value="30d">30j</TabsTrigger>
                <TabsTrigger value="90d">90j</TabsTrigger>
                <TabsTrigger value="1y">1a</TabsTrigger>
                <TabsTrigger value="all">Tout</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] mb-6">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={historicalData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
              <XAxis dataKey="timestamp" tickFormatter={formatTimestamp} />
              <YAxis tickFormatter={getYAxisFormatter()} />
              <Tooltip
                labelFormatter={(timestamp) => formatTimestamp(timestamp as number)}
                formatter={(value) => [getTooltipFormatter(value as number), getChartTitle()]}
                contentStyle={{ backgroundColor: "rgba(0, 0, 0, 0.8)", border: "none", borderRadius: "4px" }}
                itemStyle={{ color: getChartColor() }}
              />
              <Line
                type="monotone"
                dataKey={chartType}
                stroke={getChartColor()}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
              {historicalData
                .filter((item) => item.hasEvent)
                .map((item, index) => (
                  <ReferenceLine
                    key={index}
                    x={item.timestamp}
                    stroke="#D4AF37"
                    strokeDasharray="3 3"
                    label={{
                      value: item.eventType,
                      position: "insideTopRight",
                      fill: "#D4AF37",
                      fontSize: 10,
                    }}
                  />
                ))}
            </LineChart>
          </ResponsiveContainer>
        </div>

        {performanceStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Actuel</p>
                  <p className="text-lg font-bold">
                    {getTooltipFormatter(historicalData[historicalData.length - 1][chartType])}
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Variation</p>
                  <div className="flex items-center">
                    <p className="text-lg font-bold">{getTooltipFormatter(performanceStats.change)}</p>
                    <span
                      className={`ml-2 text-sm ${
                        performanceStats.percentChange > 0
                          ? "text-green-500"
                          : performanceStats.percentChange < 0
                            ? "text-red-500"
                            : "text-muted-foreground"
                      }`}
                    >
                      {performanceStats.percentChange > 0 ? (
                        <TrendingUp className="h-4 w-4 inline mr-1" />
                      ) : performanceStats.percentChange < 0 ? (
                        <TrendingDown className="h-4 w-4 inline mr-1" />
                      ) : (
                        <Minus className="h-4 w-4 inline mr-1" />
                      )}
                      {formatPercentage(performanceStats.percentChange)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Minimum</p>
                  <p className="text-lg font-bold">{getTooltipFormatter(performanceStats.min)}</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Maximum</p>
                  <p className="text-lg font-bold">{getTooltipFormatter(performanceStats.max)}</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Moyenne</p>
                  <p className="text-lg font-bold">{getTooltipFormatter(performanceStats.avg)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <div className="flex justify-end mt-4">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exporter les données
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

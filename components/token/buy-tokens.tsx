"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { Transaction } from "@solana/web3.js"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, AlertCircle, CheckCircle, Info } from "lucide-react"

interface BuyTokensProps {
  tokenAddress: string
  tokenSymbol: string
  tokenName?: string
  currentSupply: number
  maxSupply: number
  solPriceUsd: number
  dexListingThresholdUsd: number
  isInitialPurchase?: boolean
}

interface PurchaseSimulation {
  tokensToReceive: number
  costInSol: number
  pricePerToken: number
  newSupply: number
  newPrice: number
  marketCapUsd: number
  dexListingProgress: number
}

export default function BuyTokens({
  tokenAddress,
  tokenSymbol,
  tokenName,
  currentSupply,
  maxSupply,
  solPriceUsd,
  dexListingThresholdUsd,
  isInitialPurchase = false,
}: BuyTokensProps) {
  const { publicKey, signTransaction, connected } = useWallet()
  const { toast } = useToast()

  const [amount, setAmount] = useState(0.1) // Default amount: 0.1 SOL
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [simulation, setSimulation] = useState<PurchaseSimulation | null>(null)
  const [isPreparing, setIsPreparing] = useState(false)

  // Minimum purchase amount
  const minPurchaseAmount = isInitialPurchase ? 0.05 : 0.01

  // Update simulation when amount changes
  useEffect(() => {
    const fetchSimulation = async () => {
      if (amount < minPurchaseAmount) return

      try {
        setIsPreparing(true)

        const response = await fetch("/api/token/simulate-purchase", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            tokenAddress,
            currentSupply,
            amountInSol: amount,
          }),
        })

        const data = await response.json()

        if (data.success && data.simulation) {
          setSimulation(data.simulation)
        } else {
          console.error("Error simulating purchase:", data.error)
        }
      } catch (error) {
        console.error("Error fetching simulation:", error)
      } finally {
        setIsPreparing(false)
      }
    }

    fetchSimulation()
  }, [amount, tokenAddress, currentSupply, minPurchaseAmount])

  // Handle purchase
  const handlePurchase = async () => {
    if (!connected || !publicKey || !signTransaction) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to buy tokens.",
        variant: "destructive",
      })
      return
    }

    if (amount < minPurchaseAmount) {
      toast({
        title: "Amount too low",
        description: `Minimum purchase amount is ${minPurchaseAmount} SOL.`,
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Step 1: Get a transaction to sign
      const prepareResponse = await fetch("/api/token/purchase", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tokenAddress,
          buyerAddress: publicKey.toString(),
          amountInSol: amount,
          currentSupply,
        }),
      })

      const prepareData = await prepareResponse.json()

      if (!prepareData.success || !prepareData.transaction) {
        throw new Error(prepareData.error || "Failed to prepare transaction")
      }

      // Step 2: Deserialize and sign the transaction
      const transaction = Transaction.from(Buffer.from(prepareData.transaction, "base64"))
      const signedTransaction = await signTransaction(transaction)

      // Step 3: Send the signed transaction back to the server
      const completeResponse = await fetch("/api/token/purchase", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tokenAddress,
          buyerAddress: publicKey.toString(),
          amountInSol: amount,
          currentSupply,
          signedTransaction: Buffer.from(signedTransaction.serialize()).toString("base64"),
        }),
      })

      const completeData = await completeResponse.json()

      if (!completeData.success) {
        throw new Error(completeData.error || "Failed to complete purchase")
      }

      // Purchase successful
      setSuccess(true)
      toast({
        title: "Purchase successful!",
        description: `You have purchased ${simulation?.tokensToReceive.toLocaleString()} ${tokenSymbol} for ${amount} SOL.`,
      })

      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 3000)
    } catch (error: any) {
      console.error("Error purchasing tokens:", error)
      setError(error.message || "An error occurred while purchasing tokens")
      toast({
        title: "Error",
        description: error.message || "An error occurred while purchasing tokens",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Success view
  if (success) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <CardTitle>Purchase successful!</CardTitle>
          </div>
          <CardDescription>
            Your purchase of {tokenName || tokenSymbol} has been completed successfully.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="bg-green-50 border-green-200">
            <AlertDescription>
              You have purchased {simulation?.tokensToReceive.toLocaleString()} {tokenSymbol} for {amount} SOL. The
              tokens have been added to your wallet.
            </AlertDescription>
          </Alert>

          {simulation && simulation.dexListingProgress > 50 && (
            <Alert className="mt-4 bg-blue-50 border-blue-200">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                This token is {simulation.dexListingProgress.toFixed(1)}% of the way to DEX listing threshold!
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter>
          <Button className="w-full" onClick={() => window.location.reload()}>
            Continue
          </Button>
        </CardFooter>
      </Card>
    )
  }

  // Purchase view
  return (
    <Card>
      <CardHeader>
        <CardTitle>{isInitialPurchase ? "Initial Purchase Required" : `Buy ${tokenSymbol}`}</CardTitle>
        <CardDescription>
          {isInitialPurchase
            ? "You need to make an initial purchase to launch your token on the platform."
            : `Buy ${tokenName || tokenSymbol} tokens and benefit from the bonding curve pricing.`}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!connected && (
          <div className="bg-blue-50 p-4 rounded-md mb-4">
            <div className="flex flex-col items-center justify-center gap-4">
              <p className="text-center text-blue-800">Connect your wallet to make a purchase.</p>
              <WalletMultiButton className="!bg-primary hover:!bg-primary/90" />
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="amount">Amount (SOL)</Label>
          <div className="flex items-center space-x-2">
            <Slider
              id="amount-slider"
              min={minPurchaseAmount}
              max={1}
              step={0.01}
              value={[amount]}
              onValueChange={(values) => setAmount(values[0])}
              disabled={isLoading || isPreparing}
              className="flex-1"
            />
            <Input
              id="amount"
              type="number"
              value={amount}
              onChange={(e) => setAmount(Number(e.target.value) || 0)}
              min={minPurchaseAmount}
              step={0.01}
              className="w-24"
              disabled={isLoading || isPreparing}
            />
          </div>
        </div>

        {simulation && (
          <div className="rounded-md bg-muted p-3">
            <div className="text-sm text-muted-foreground mb-1">You will receive</div>
            <div className="text-2xl font-bold">
              {simulation.tokensToReceive.toLocaleString(undefined, { maximumFractionDigits: 2 })} {tokenSymbol}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Price per token: {simulation.pricePerToken.toFixed(8)} SOL
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-xs">
                <span>DEX listing progress:</span>
                <span>{simulation.dexListingProgress.toFixed(1)}%</span>
              </div>
              <Progress value={simulation.dexListingProgress} className="h-1.5" />
              <div className="text-xs text-right text-muted-foreground">
                Threshold: {dexListingThresholdUsd.toLocaleString()} USD
              </div>
            </div>

            <div className="mt-2 text-xs text-muted-foreground">
              Estimated market cap after purchase: $
              {simulation.marketCapUsd.toLocaleString(undefined, { maximumFractionDigits: 2 })}
            </div>
          </div>
        )}

        {isPreparing && !simulation && (
          <div className="flex justify-center items-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isInitialPurchase && (
          <Alert className="bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              The initial purchase is required to launch your token. This establishes the starting price and makes your
              token available on the platform.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          onClick={handlePurchase}
          disabled={isLoading || isPreparing || amount < minPurchaseAmount || !connected}
        >
          {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          {isLoading
            ? "Processing transaction..."
            : isInitialPurchase
              ? `Launch token with ${amount} SOL`
              : `Buy ${tokenSymbol} tokens`}
        </Button>
      </CardFooter>
    </Card>
  )
}

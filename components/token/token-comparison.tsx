"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  LineChart,
  Line,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  Legend,
} from "recharts"
import { Skeleton } from "@/components/ui/skeleton"
import { Search, X, BarChart3, TrendingUp, PieChartIcon as ChartPieIcon } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { formatCurrency, formatNumber, formatPercentage } from "@/lib/utils"

interface TokenComparisonProps {
  primaryTokenAddress: string
  initialCompareTokens?: string[]
}

export function TokenComparison({ primaryTokenAddress, initialCompareTokens = [] }: TokenComparisonProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [comparisonTokens, setComparisonTokens] = useState<string[]>(initialCompareTokens)
  const [tokensData, setTokensData] = useState<Record<string, any>>({})
  const [comparisonMetric, setComparisonMetric] = useState<"price" | "volume" | "marketCap" | "holders">("price")
  const [timeframe, setTimeframe] = useState<"7d" | "30d" | "90d">("30d")
  const [view, setView] = useState<"chart" | "table" | "stats">("chart")

  // Simuler la recherche de tokens
  useEffect(() => {
    if (!searchQuery || searchQuery.length < 2) {
      setSearchResults([])
      return
    }

    const simulateSearch = async () => {
      // Simuler un délai d'API
      await new Promise((resolve) => setTimeout(resolve, 500))

      const generateRandomToken = (index: number) => {
        const name = `${searchQuery.toUpperCase()} ${String.fromCharCode(65 + index)} Token`
        const symbol = `${searchQuery.substring(0, 2).toUpperCase()}${index}`
        return {
          address: `${index}${Math.random().toString(36).substring(2, 10)}`,
          name,
          symbol,
          price: Math.random() * 10,
          marketCap: Math.random() * 10000000,
        }
      }

      const results = Array.from({ length: 5 }, (_, i) => generateRandomToken(i))
      setSearchResults(results)
    }

    simulateSearch()
  }, [searchQuery])

  // Charger les données des tokens pour la comparaison
  useEffect(() => {
    const fetchTokensData = async () => {
      setIsLoading(true)
      try {
        const allTokens = [primaryTokenAddress, ...comparisonTokens]
        const tokenData: Record<string, any> = {}

        for (const address of allTokens) {
          // Simuler un appel API
          await new Promise((resolve) => setTimeout(resolve, 500))

          const isPrimary = address === primaryTokenAddress

          // Générer des données aléatoires
          const now = Date.now()
          const data = {
            address,
            name: isPrimary ? "Token Principal" : `Token Comparatif ${comparisonTokens.indexOf(address) + 1}`,
            symbol: isPrimary ? "MAIN" : `CMP${comparisonTokens.indexOf(address) + 1}`,
            price: {
              current: Math.random() * (isPrimary ? 1 : 0.5),
              change: {
                "7d": Math.random() * 40 - 20,
                "30d": Math.random() * 60 - 30,
                "90d": Math.random() * 80 - 40,
              },
            },
            volume: {
              current: Math.random() * (isPrimary ? 1000000 : 500000),
              change: {
                "7d": Math.random() * 40 - 20,
                "30d": Math.random() * 60 - 30,
                "90d": Math.random() * 80 - 40,
              },
            },
            marketCap: {
              current: Math.random() * (isPrimary ? 10000000 : 5000000),
              change: {
                "7d": Math.random() * 40 - 20,
                "30d": Math.random() * 60 - 30,
                "90d": Math.random() * 80 - 40,
              },
            },
            holders: {
              current: Math.floor(Math.random() * (isPrimary ? 10000 : 5000)),
              change: {
                "7d": Math.random() * 30,
                "30d": Math.random() * 50,
                "90d": Math.random() * 70,
              },
            },
            history: generateHistoricalData(timeframe, isPrimary),
          }

          tokenData[address] = data
        }

        setTokensData(tokenData)
      } catch (error) {
        console.error("Error fetching token comparison data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (primaryTokenAddress) {
      fetchTokensData()
    }
  }, [primaryTokenAddress, comparisonTokens, timeframe])

  // Générer des données historiques
  const generateHistoricalData = (period: string, isPrimary: boolean) => {
    const now = Date.now()
    const data = []
    let points: number
    let interval: number

    switch (period) {
      case "7d":
        points = 7
        interval = 24 * 60 * 60 * 1000 // 1 jour
        break
      case "30d":
        points = 30
        interval = 24 * 60 * 60 * 1000 // 1 jour
        break
      case "90d":
        points = 90
        interval = 24 * 60 * 60 * 1000 // 1 jour
        break
    }

    const basePrice = Math.random() * (isPrimary ? 0.5 : 0.2)
    const baseVolume = Math.random() * (isPrimary ? 500000 : 200000)
    const baseMarketCap = Math.random() * (isPrimary ? 5000000 : 2000000)
    const baseHolders = Math.floor(Math.random() * (isPrimary ? 5000 : 2000))

    // Tendance générale (croissance, déclin ou stable)
    const trend = Math.random() > 0.5 ? "growth" : Math.random() > 0.5 ? "decline" : "stable"

    const trendFactor =
      trend === "growth"
        ? (points / 100) * (1 + Math.random())
        : trend === "decline"
          ? -(points / 100) * (1 + Math.random())
          : 0

    for (let i = 0; i < points; i++) {
      // Variation aléatoire autour de la tendance
      const dayVariation = Math.random() * 0.05 - 0.025
      const trendProgress = trendFactor * (i / points)

      // Appliquer la tendance et la variation
      const priceMultiplier = 1 + trendProgress + dayVariation
      const volumeMultiplier = 1 + trendProgress + (Math.random() * 0.1 - 0.05)
      const marketCapMultiplier = priceMultiplier // Corrélé au prix
      const holdersMultiplier = 1 + trendProgress / 2 + (Math.random() * 0.03 - 0.01) // Moins volatile

      data.push({
        timestamp: now - (points - i) * interval,
        price: basePrice * priceMultiplier,
        volume: baseVolume * volumeMultiplier,
        marketCap: baseMarketCap * marketCapMultiplier,
        holders: Math.floor(baseHolders * holdersMultiplier),
      })
    }

    return data
  }

  const addComparisonToken = (address: string) => {
    if (comparisonTokens.length >= 3) {
      // Limite à 3 tokens pour la comparaison
      return
    }
    if (!comparisonTokens.includes(address)) {
      setComparisonTokens([...comparisonTokens, address])
    }
    setSearchQuery("")
    setSearchResults([])
  }

  const removeComparisonToken = (address: string) => {
    setComparisonTokens(comparisonTokens.filter((token) => token !== address))
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString([], { day: "numeric", month: "short" })
  }

  const prepareChartData = () => {
    if (!tokensData[primaryTokenAddress]) return []

    const primaryData = tokensData[primaryTokenAddress].history
    const chartData = primaryData.map((item: any, index: number) => {
      const dataPoint: any = {
        timestamp: item.timestamp,
        [`${tokensData[primaryTokenAddress].symbol}`]: item[comparisonMetric],
      }

      // Ajouter les données des tokens comparatifs
      comparisonTokens.forEach((address) => {
        if (tokensData[address] && tokensData[address].history[index]) {
          dataPoint[tokensData[address].symbol] = tokensData[address].history[index][comparisonMetric]
        }
      })

      return dataPoint
    })

    return chartData
  }

  const prepareBarChartData = () => {
    if (!tokensData[primaryTokenAddress]) return []

    const metrics = ["price", "volume", "marketCap", "holders"]
    const allTokens = [primaryTokenAddress, ...comparisonTokens].filter((address) => tokensData[address])

    return metrics.map((metric) => {
      const item: any = { name: getMetricLabel(metric) }

      allTokens.forEach((address) => {
        const token = tokensData[address]
        // Normaliser les données pour une meilleure visualisation
        let value = token[metric]?.current
        if (metric === "price") value *= 1000 // Multiplier le prix pour une meilleure visualisation
        item[token.symbol] = value
      })

      return item
    })
  }

  const getMetricLabel = (metric: string) => {
    switch (metric) {
      case "price":
        return "Prix"
      case "volume":
        return "Volume"
      case "marketCap":
        return "Cap. Marché"
      case "holders":
        return "Détenteurs"
      default:
        return metric
    }
  }

  const getMetricFormatFunction = (metric: string) => {
    switch (metric) {
      case "price":
        return (value: number) => formatCurrency(value)
      case "volume":
        return (value: number) => formatCurrency(value)
      case "marketCap":
        return (value: number) => formatCurrency(value)
      case "holders":
        return (value: number) => formatNumber(value)
      default:
        return (value: number) => value.toString()
    }
  }

  const getColorForIndex = (index: number) => {
    const colors = ["#D4AF37", "#8b5cf6", "#10b981", "#f97316", "#ef4444"]
    return colors[index % colors.length]
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Comparaison de tokens</CardTitle>
        <CardDescription>Comparez les performances avec d'autres tokens</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un token à comparer..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchResults.length > 0 && (
            <div className="absolute z-10 mt-1 w-full bg-background border rounded-md shadow-md">
              {searchResults.map((result) => (
                <div
                  key={result.address}
                  className="p-2 hover:bg-muted cursor-pointer flex justify-between items-center"
                  onClick={() => addComparisonToken(result.address)}
                >
                  <div>
                    <div className="font-medium">{result.name}</div>
                    <div className="text-sm text-muted-foreground">{result.symbol}</div>
                  </div>
                  <div className="text-sm">{formatCurrency(result.price)}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex flex-wrap gap-2 items-center">
          <div className="text-sm text-muted-foreground mr-2">Tokens comparés:</div>
          <Badge className="bg-[#D4AF37] text-black hover:bg-[#C4A030]">
            {tokensData[primaryTokenAddress]?.symbol || "TOKEN"}
            <span className="ml-1 opacity-70">(Principal)</span>
          </Badge>

          {comparisonTokens.map(
            (address, index) =>
              tokensData[address] && (
                <Badge key={address} variant="outline" className="flex items-center gap-1">
                  {tokensData[address].symbol}
                  <button onClick={() => removeComparisonToken(address)}>
                    <X className="h-3 w-3 ml-1" />
                  </button>
                </Badge>
              ),
          )}

          {comparisonTokens.length === 0 && (
            <div className="text-sm text-muted-foreground italic">Aucun token sélectionné pour comparaison</div>
          )}
        </div>

        <div className="flex flex-wrap justify-between items-center gap-4">
          <div className="flex gap-2">
            <Select value={comparisonMetric} onValueChange={(value) => setComparisonMetric(value as any)}>
              <SelectTrigger className="w-[150px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="price">Prix</SelectItem>
                <SelectItem value="volume">Volume</SelectItem>
                <SelectItem value="marketCap">Cap. Marché</SelectItem>
                <SelectItem value="holders">Détenteurs</SelectItem>
              </SelectContent>
            </Select>

            <Tabs value={timeframe} onValueChange={(value) => setTimeframe(value as any)}>
              <TabsList>
                <TabsTrigger value="7d">7j</TabsTrigger>
                <TabsTrigger value="30d">30j</TabsTrigger>
                <TabsTrigger value="90d">90j</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="flex gap-2">
            <Button variant={view === "chart" ? "default" : "outline"} size="sm" onClick={() => setView("chart")}>
              <TrendingUp className="h-4 w-4 mr-1" /> Courbe
            </Button>
            <Button variant={view === "stats" ? "default" : "outline"} size="sm" onClick={() => setView("stats")}>
              <BarChart3 className="h-4 w-4 mr-1" /> Statistiques
            </Button>
            <Button variant={view === "table" ? "default" : "outline"} size="sm" onClick={() => setView("table")}>
              <ChartPieIcon className="h-4 w-4 mr-1" /> Tableau
            </Button>
          </div>
        </div>

        {isLoading ? (
          <Skeleton className="h-[300px] w-full" />
        ) : (
          <>
            {view === "chart" && (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={prepareChartData()} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                    <XAxis dataKey="timestamp" tickFormatter={formatTimestamp} />
                    <YAxis
                      tickFormatter={(value) => {
                        return getMetricFormatFunction(comparisonMetric)(value, true)
                      }}
                    />
                    <Tooltip
                      labelFormatter={(timestamp) => formatTimestamp(timestamp as number)}
                      formatter={(value, name) => {
                        return [getMetricFormatFunction(comparisonMetric)(value as number), name]
                      }}
                    />
                    <Legend />

                    {/* Ligne pour le token principal */}
                    <Line
                      type="monotone"
                      dataKey={tokensData[primaryTokenAddress]?.symbol}
                      stroke={getColorForIndex(0)}
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 6 }}
                    />

                    {/* Lignes pour les tokens de comparaison */}
                    {comparisonTokens.map(
                      (address, index) =>
                        tokensData[address] && (
                          <Line
                            key={address}
                            type="monotone"
                            dataKey={tokensData[address].symbol}
                            stroke={getColorForIndex(index + 1)}
                            strokeWidth={2}
                            dot={false}
                            activeDot={{ r: 6 }}
                          />
                        ),
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}

            {view === "stats" && (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={prepareBarChartData()} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => formatNumber(value, true)} />
                    <Tooltip
                      formatter={(value, name, props) => {
                        const metric = props.payload.name
                        if (metric === "Prix") return [formatCurrency(value as number), name]
                        if (metric === "Volume") return [formatCurrency(value as number), name]
                        if (metric === "Cap. Marché") return [formatCurrency(value as number), name]
                        return [formatNumber(value as number), name]
                      }}
                    />
                    <Legend />

                    {/* Barre pour le token principal */}
                    <Bar dataKey={tokensData[primaryTokenAddress]?.symbol} fill={getColorForIndex(0)} />

                    {/* Barres pour les tokens de comparaison */}
                    {comparisonTokens.map(
                      (address, index) =>
                        tokensData[address] && (
                          <Bar key={address} dataKey={tokensData[address].symbol} fill={getColorForIndex(index + 1)} />
                        ),
                    )}
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}

            {view === "table" && (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Métrique</TableHead>
                      <TableHead>{tokensData[primaryTokenAddress]?.symbol || "Principal"}</TableHead>
                      {comparisonTokens.map(
                        (address) =>
                          tokensData[address] && <TableHead key={address}>{tokensData[address].symbol}</TableHead>,
                      )}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Prix</TableCell>
                      <TableCell>
                        {formatCurrency(tokensData[primaryTokenAddress]?.price.current)}
                        <div
                          className={
                            tokensData[primaryTokenAddress]?.price.change[timeframe] >= 0
                              ? "text-green-500 text-xs"
                              : "text-red-500 text-xs"
                          }
                        >
                          {formatPercentage(tokensData[primaryTokenAddress]?.price.change[timeframe])}
                        </div>
                      </TableCell>
                      {comparisonTokens.map(
                        (address) =>
                          tokensData[address] && (
                            <TableCell key={address}>
                              {formatCurrency(tokensData[address].price.current)}
                              <div
                                className={
                                  tokensData[address].price.change[timeframe] >= 0
                                    ? "text-green-500 text-xs"
                                    : "text-red-500 text-xs"
                                }
                              >
                                {formatPercentage(tokensData[address].price.change[timeframe])}
                              </div>
                            </TableCell>
                          ),
                      )}
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Volume</TableCell>
                      <TableCell>
                        {formatCurrency(tokensData[primaryTokenAddress]?.volume.current)}
                        <div
                          className={
                            tokensData[primaryTokenAddress]?.volume.change[timeframe] >= 0
                              ? "text-green-500 text-xs"
                              : "text-red-500 text-xs"
                          }
                        >
                          {formatPercentage(tokensData[primaryTokenAddress]?.volume.change[timeframe])}
                        </div>
                      </TableCell>
                      {comparisonTokens.map(
                        (address) =>
                          tokensData[address] && (
                            <TableCell key={address}>
                              {formatCurrency(tokensData[address].volume.current)}
                              <div
                                className={
                                  tokensData[address].volume.change[timeframe] >= 0
                                    ? "text-green-500 text-xs"
                                    : "text-red-500 text-xs"
                                }
                              >
                                {formatPercentage(tokensData[address].volume.change[timeframe])}
                              </div>
                            </TableCell>
                          ),
                      )}
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Cap. Marché</TableCell>
                      <TableCell>
                        {formatCurrency(tokensData[primaryTokenAddress]?.marketCap.current)}
                        <div
                          className={
                            tokensData[primaryTokenAddress]?.marketCap.change[timeframe] >= 0
                              ? "text-green-500 text-xs"
                              : "text-red-500 text-xs"
                          }
                        >
                          {formatPercentage(tokensData[primaryTokenAddress]?.marketCap.change[timeframe])}
                        </div>
                      </TableCell>
                      {comparisonTokens.map(
                        (address) =>
                          tokensData[address] && (
                            <TableCell key={address}>
                              {formatCurrency(tokensData[address].marketCap.current)}
                              <div
                                className={
                                  tokensData[address].marketCap.change[timeframe] >= 0
                                    ? "text-green-500 text-xs"
                                    : "text-red-500 text-xs"
                                }
                              >
                                {formatPercentage(tokensData[address].marketCap.change[timeframe])}
                              </div>
                            </TableCell>
                          ),
                      )}
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Détenteurs</TableCell>
                      <TableCell>
                        {formatNumber(tokensData[primaryTokenAddress]?.holders.current)}
                        <div
                          className={
                            tokensData[primaryTokenAddress]?.holders.change[timeframe] >= 0
                              ? "text-green-500 text-xs"
                              : "text-red-500 text-xs"
                          }
                        >
                          {formatPercentage(tokensData[primaryTokenAddress]?.holders.change[timeframe])}
                        </div>
                      </TableCell>
                      {comparisonTokens.map(
                        (address) =>
                          tokensData[address] && (
                            <TableCell key={address}>
                              {formatNumber(tokensData[address].holders.current)}
                              <div
                                className={
                                  tokensData[address].holders.change[timeframe] >= 0
                                    ? "text-green-500 text-xs"
                                    : "text-red-500 text-xs"
                                }
                              >
                                {formatPercentage(tokensData[address].holders.change[timeframe])}
                              </div>
                            </TableCell>
                          ),
                      )}
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}

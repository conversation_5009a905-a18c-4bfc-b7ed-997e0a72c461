"use client"

import { useState, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, AlertCircle, CheckCircle } from "lucide-react"
import tokenInitialPurchaseService from "@/lib/token-initial-purchase-service"

interface InitialTokenPurchaseProps {
  tokenAddress: string
  tokenSymbol: string
  tokenName?: string
}

export default function InitialTokenPurchase({ tokenAddress, tokenSymbol, tokenName }: InitialTokenPurchaseProps) {
  const { publicKey, signTransaction, connected } = useWallet()
  const { toast } = useToast()
  const [amount, setAmount] = useState(0.1) // Montant par défaut: 0.1 SOL
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [tokensToReceive, setTokensToReceive] = useState(amount * 1000) // 1000 tokens par SOL

  // Mettre à jour le nombre de tokens à recevoir lorsque le montant change
  useEffect(() => {
    setTokensToReceive(amount * 1000)
  }, [amount])

  // Gérer l'achat de tokens
  const handlePurchase = async () => {
    if (!connected || !publicKey) {
      toast({
        title: "Wallet non connecté",
        description: "Veuillez connecter votre wallet pour effectuer un achat.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)
    setSuccess(false)

    try {
      // Étape 1: Créer la transaction d'achat
      const purchaseResult = await tokenInitialPurchaseService.createInitialPurchaseTransaction({
        tokenAddress,
        buyerAddress: publicKey.toString(),
        amountInSol: amount,
      })

      if (!purchaseResult.success || !purchaseResult.transaction) {
        throw new Error(purchaseResult.error || "Échec de la création de la transaction d'achat")
      }

      // Étape 2: Faire signer la transaction par l'utilisateur
      const signedTransaction = await signTransaction(purchaseResult.transaction)

      // Étape 3: Envoyer la transaction signée
      const sendResult = await tokenInitialPurchaseService.sendTransaction(signedTransaction)

      if (!sendResult.success) {
        throw new Error(sendResult.error || "Échec de l'envoi de la transaction")
      }

      // Étape 4: Créer et envoyer la transaction de transfert de tokens
      // Cette étape est gérée côté serveur car elle nécessite la clé privée de la plateforme
      const completeResult = await tokenInitialPurchaseService.completeInitialPurchase({
        tokenAddress,
        buyerAddress: publicKey.toString(),
        amountInSol: amount,
      })

      if (!completeResult.success) {
        throw new Error(completeResult.error || "Échec de la finalisation de l'achat")
      }

      // Achat réussi
      setSuccess(true)
      toast({
        title: "Achat réussi!",
        description: `Vous avez acheté ${tokensToReceive.toLocaleString()} ${tokenSymbol} pour ${amount} SOL.`,
      })

      // Recharger la page après un court délai
      setTimeout(() => {
        window.location.reload()
      }, 3000)
    } catch (error: any) {
      console.error("Erreur lors de l'achat de tokens:", error)
      setError(error.message || "Une erreur s'est produite lors de l'achat de tokens")
      toast({
        title: "Erreur",
        description: error.message || "Une erreur s'est produite lors de l'achat de tokens",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <CardTitle>Achat réussi!</CardTitle>
          </div>
          <CardDescription>
            Votre achat initial de {tokenName || tokenSymbol} a été effectué avec succès.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="bg-green-50 border-green-200">
            <AlertDescription>
              Vous avez acheté {tokensToReceive.toLocaleString()} {tokenSymbol} pour {amount} SOL. Les tokens ont été
              ajoutés à votre wallet.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter>
          <Button className="w-full" onClick={() => window.location.reload()}>
            Continuer
          </Button>
        </CardFooter>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Acheter {tokenSymbol}</CardTitle>
        <CardDescription>
          Soyez parmi les premiers à acheter {tokenName || tokenSymbol} et bénéficiez du meilleur prix!
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!connected && (
          <div className="bg-blue-50 p-4 rounded-md mb-4">
            <div className="flex flex-col items-center justify-center gap-4">
              <p className="text-center text-blue-800">Connectez votre wallet pour effectuer un achat initial.</p>
              <WalletMultiButton className="!bg-primary hover:!bg-primary/90" />
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="amount">Montant (SOL)</Label>
          <div className="flex items-center space-x-2">
            <Slider
              id="amount-slider"
              min={0.01}
              max={1}
              step={0.01}
              value={[amount]}
              onValueChange={(values) => setAmount(values[0])}
              disabled={isLoading}
              className="flex-1"
            />
            <Input
              id="amount"
              type="number"
              value={amount}
              onChange={(e) => setAmount(Number(e.target.value) || 0)}
              min={0.01}
              step={0.01}
              className="w-24"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="rounded-md bg-muted p-3">
          <div className="text-sm text-muted-foreground mb-1">Vous recevrez</div>
          <div className="text-2xl font-bold">
            {tokensToReceive.toLocaleString(undefined, { maximumFractionDigits: 2 })} {tokenSymbol}
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            Prix unitaire: {(amount / tokensToReceive).toFixed(8)} SOL
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter>
        <Button className="w-full" onClick={handlePurchase} disabled={isLoading || amount < 0.01 || !connected}>
          {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          {isLoading ? "Transaction en cours..." : `Acheter avec ${amount} SOL`}
        </Button>
      </CardFooter>
    </Card>
  )
}

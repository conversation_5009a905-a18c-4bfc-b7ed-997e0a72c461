"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowDownUp, ArrowRight, ArrowUpRight, Clock, Coins, RefreshCcw, Wallet } from "lucide-react"
import { formatTimeAgo, formatCurrency, formatNumber } from "@/lib/utils"

interface TokenActivityFeedProps {
  tokenAddress: string
}

interface Activity {
  id: string
  type: "buy" | "sell" | "transfer" | "liquidity" | "mint" | "burn"
  amount: number
  tokenAmount: number
  timestamp: string
  from: string
  to: string
  txHash: string
}

export function TokenActivityFeed({ tokenAddress }: TokenActivityFeedProps) {
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<string | null>(null)

  useEffect(() => {
    const fetchActivities = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 800))

        // Générer des activités aléatoires
        const activityTypes = ["buy", "sell", "transfer", "liquidity", "mint", "burn"]
        const now = Date.now()

        const randomActivities = Array.from({ length: 20 }, (_, i) => {
          const type = activityTypes[Math.floor(Math.random() * activityTypes.length)] as Activity["type"]
          const minutesAgo = Math.floor(Math.random() * 60 * 24) // Jusqu'à 24 heures
          const timestamp = new Date(now - minutesAgo * 60 * 1000).toISOString()
          const tokenAmount = Math.random() * 100000 + 1000
          const price = Math.random() * 0.1
          const amount = tokenAmount * price

          return {
            id: `activity-${i}`,
            type,
            amount,
            tokenAmount,
            timestamp,
            from: `${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 6)}`,
            to: `${Math.random().toString(36).substring(2, 10)}...${Math.random().toString(36).substring(2, 6)}`,
            txHash: `${Math.random().toString(36).substring(2, 15)}...${Math.random().toString(36).substring(2, 6)}`,
          }
        })

        // Trier par timestamp (plus récent en premier)
        randomActivities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

        setActivities(randomActivities)
      } catch (error) {
        console.error("Error fetching token activities:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchActivities()
  }, [tokenAddress])

  const filteredActivities = filter ? activities.filter((activity) => activity.type === filter) : activities

  const getActivityIcon = (type: Activity["type"]) => {
    switch (type) {
      case "buy":
        return <ArrowUpRight className="h-4 w-4 text-green-500" />
      case "sell":
        return <ArrowDownUp className="h-4 w-4 text-red-500" />
      case "transfer":
        return <ArrowRight className="h-4 w-4 text-blue-500" />
      case "liquidity":
        return <RefreshCcw className="h-4 w-4 text-purple-500" />
      case "mint":
        return <Coins className="h-4 w-4 text-amber-500" />
      case "burn":
        return <Coins className="h-4 w-4 text-orange-500" />
    }
  }

  const getActivityBadge = (type: Activity["type"]) => {
    switch (type) {
      case "buy":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Achat</Badge>
      case "sell":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Vente</Badge>
      case "transfer":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Transfert</Badge>
      case "liquidity":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Liquidité</Badge>
      case "mint":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Création</Badge>
      case "burn":
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">Destruction</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Activité du token</CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilter(null)}
              className={filter === null ? "bg-muted" : ""}
            >
              Tout
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilter("buy")}
              className={filter === "buy" ? "bg-green-100 text-green-800 border-green-200" : ""}
            >
              Achats
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilter("sell")}
              className={filter === "sell" ? "bg-red-100 text-red-800 border-red-200" : ""}
            >
              Ventes
            </Button>
          </div>
        </div>
        <CardDescription>Transactions et activités récentes</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex gap-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                </div>
              </div>
            ))}
          </div>
        ) : filteredActivities.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">Aucune activité trouvée pour ce token.</div>
        ) : (
          <div className="space-y-4">
            {filteredActivities.slice(0, 10).map((activity) => (
              <div key={activity.id} className="flex items-start gap-4 pb-4 border-b last:border-0">
                <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getActivityBadge(activity.type)}
                      <span className="text-sm font-medium">
                        {formatNumber(activity.tokenAmount)} {activity.type === "burn" ? "brûlés" : ""}
                      </span>
                    </div>
                    <div className="text-sm font-medium">{formatCurrency(activity.amount)}</div>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Wallet className="h-3 w-3" />
                    <span>
                      {activity.type === "buy" || activity.type === "transfer"
                        ? `De ${activity.from} à ${activity.to}`
                        : activity.type === "sell"
                          ? `De ${activity.to} à ${activity.from}`
                          : activity.type === "mint"
                            ? `Créé par ${activity.from}`
                            : activity.type === "burn"
                              ? `Brûlé par ${activity.from}`
                              : `${activity.from} ↔ ${activity.to}`}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <a
                      href={`https://explorer.solana.com/tx/${activity.txHash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:text-blue-600"
                    >
                      {activity.txHash}
                    </a>
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>{formatTimeAgo(activity.timestamp)}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {filteredActivities.length > 10 && (
              <Button variant="outline" className="w-full">
                Voir plus d'activités
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip } from "recharts"
import { formatCurrency } from "@/lib/utils"

interface TokenLiquidityStatsProps {
  tokenAddress: string
}

interface LiquidityPool {
  name: string
  value: number
  color: string
  locked: boolean
  lockPeriod?: number
}

export function TokenLiquidityStats({ tokenAddress }: TokenLiquidityStatsProps) {
  const [liquidityData, setLiquidityData] = useState<{
    totalLiquidity: number
    pools: LiquidityPool[]
    liquidityRatio: number
    lockedPercentage: number
    averageLockTime: number
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchLiquidityData = async () => {
      setIsLoading(true)
      try {
        // Simuler un appel API
        await new Promise((resolve) => setTimeout(resolve, 800))

        // Générer des données aléatoires
        const totalLiquidity = Math.random() * 1000000 + 50000

        const pools: LiquidityPool[] = [
          {
            name: "Raydium",
            value: totalLiquidity * (Math.random() * 0.5 + 0.2), // 20-70% du total
            color: "#3b82f6",
            locked: Math.random() > 0.3,
            lockPeriod: Math.random() > 0.5 ? Math.floor(Math.random() * 12) + 1 : undefined,
          },
          {
            name: "Orca",
            value: totalLiquidity * (Math.random() * 0.3 + 0.1), // 10-40% du total
            color: "#8b5cf6",
            locked: Math.random() > 0.5,
            lockPeriod: Math.random() > 0.5 ? Math.floor(Math.random() * 12) + 1 : undefined,
          },
          {
            name: "Jupiter",
            value: totalLiquidity * (Math.random() * 0.2 + 0.05), // 5-25% du total
            color: "#ec4899",
            locked: Math.random() > 0.7,
            lockPeriod: Math.random() > 0.5 ? Math.floor(Math.random() * 12) + 1 : undefined,
          },
        ]

        // Calculer le pourcentage de liquidité verrouillée
        const lockedLiquidity = pools.reduce((sum, pool) => sum + (pool.locked ? pool.value : 0), 0)
        const lockedPercentage = (lockedLiquidity / totalLiquidity) * 100

        // Calculer la durée moyenne de verrouillage
        const lockedPools = pools.filter((pool) => pool.locked && pool.lockPeriod)
        const averageLockTime =
          lockedPools.length > 0
            ? lockedPools.reduce((sum, pool) => sum + (pool.lockPeriod || 0), 0) / lockedPools.length
            : 0

        setLiquidityData({
          totalLiquidity,
          pools,
          liquidityRatio: Math.random() * 30 + 10, // 10-40% ratio de liquidité par rapport à la market cap
          lockedPercentage,
          averageLockTime,
        })
      } catch (error) {
        console.error("Error fetching liquidity data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchLiquidityData()
  }, [tokenAddress])

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Liquidité</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!liquidityData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Liquidité</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">
            Aucune donnée de liquidité disponible pour ce token.
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Liquidité</CardTitle>
        <CardDescription>Répartition et statistiques de liquidité</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row items-center gap-6">
          <div className="w-full md:w-1/2 h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={liquidityData.pools}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={2}
                  dataKey="value"
                >
                  {liquidityData.pools.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [formatCurrency(value), "Liquidité"]}
                  labelFormatter={(index) => liquidityData.pools[index].name}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>

          <div className="w-full md:w-1/2 space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Liquidité totale</span>
                <span className="text-sm font-bold">{formatCurrency(liquidityData.totalLiquidity)}</span>
              </div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Ratio de liquidité</span>
                <span className="text-sm">{liquidityData.liquidityRatio.toFixed(2)}%</span>
              </div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Liquidité verrouillée</span>
                <span className="text-sm">{liquidityData.lockedPercentage.toFixed(2)}%</span>
              </div>
              {liquidityData.averageLockTime > 0 && (
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Durée moyenne de verrouillage</span>
                  <span className="text-sm">{liquidityData.averageLockTime.toFixed(1)} mois</span>
                </div>
              )}
            </div>

            <div className="space-y-2">
              {liquidityData.pools.map((pool, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: pool.color }}></div>
                      <span className="text-sm">{pool.name}</span>
                      {pool.locked && (
                        <Badge variant="outline" className="text-xs">
                          {pool.lockPeriod ? `Verrouillé ${pool.lockPeriod} mois` : "Verrouillé"}
                        </Badge>
                      )}
                    </div>
                    <span className="text-sm">{formatCurrency(pool.value)}</span>
                  </div>
                  <Progress value={(pool.value / liquidityData.totalLiquidity) * 100} className="h-1" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

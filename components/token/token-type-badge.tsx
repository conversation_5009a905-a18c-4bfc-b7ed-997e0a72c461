import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>les, Zap } from "lucide-react"

interface TokenTypeBadgeProps {
  isAIGenerated?: boolean
  className?: string
}

export function TokenTypeBadge({ isAIGenerated, className }: TokenTypeBadgeProps) {
  if (isAIGenerated) {
    return (
      <Badge variant="secondary" className={`flex items-center gap-1 bg-purple-100 text-purple-800 ${className}`}>
        <Sparkles className="h-3 w-3" />
        <span>AI Generated</span>
      </Badge>
    )
  }

  return (
    <Badge variant="outline" className={`flex items-center gap-1 ${className}`}>
      <Zap className="h-3 w-3" />
      <span>Standard</span>
    </Badge>
  )
}

"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Connection, PublicKey, clusterApiUrl } from "@solana/web3.js"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

interface PriceData {
  timestamp: number
  price: number
}

interface TokenPriceChartProps {
  tokenAddress: string
  network?: "devnet" | "mainnet-beta"
}

export default function TokenPriceChart({ tokenAddress, network = "devnet" }: TokenPriceChartProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [priceData, setPriceData] = useState<PriceData[]>([])
  const [timeframe, setTimeframe] = useState<"24h" | "7d" | "30d" | "90d">("7d")
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPriceData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Connexion à Solana
        const endpoint = clusterApiUrl(network)
        const connection = new Connection(endpoint, "confirmed")

        // Vérifier si le token existe
        try {
          const mintPublicKey = new PublicKey(tokenAddress)
          const accountInfo = await connection.getAccountInfo(mintPublicKey)

          if (!accountInfo) {
            throw new Error("Token non trouvé")
          }
        } catch (err) {
          console.error("Erreur lors de la vérification du token:", err)
          throw new Error("Adresse de token invalide")
        }

        // Récupérer les données de prix réelles
        // Note: Dans une implémentation réelle, vous utiliseriez une API comme CoinGecko ou Jupiter
        // Pour cette démonstration, nous allons générer des données basées sur l'adresse du token

        const now = Date.now()
        const data: PriceData[] = []

        // Utiliser l'adresse du token comme seed pour générer des prix cohérents
        const seed = tokenAddress.slice(0, 8)
        const seedValue = Number.parseInt(seed, 16) / 0xffffffff // Normaliser entre 0 et 1

        // Prix de base basé sur l'adresse du token (pour être cohérent)
        const basePrice = 0.01 + seedValue * 10

        // Nombre de points de données selon le timeframe
        let points = 0
        let interval = 0

        switch (timeframe) {
          case "24h":
            points = 24
            interval = 60 * 60 * 1000 // 1 heure
            break
          case "7d":
            points = 7 * 24
            interval = 60 * 60 * 1000 // 1 heure
            break
          case "30d":
            points = 30
            interval = 24 * 60 * 60 * 1000 // 1 jour
            break
          case "90d":
            points = 90
            interval = 24 * 60 * 60 * 1000 // 1 jour
            break
        }

        // Générer les données de prix
        let currentPrice = basePrice
        for (let i = points; i >= 0; i--) {
          const timestamp = now - i * interval

          // Variation aléatoire mais déterministe basée sur l'adresse et le timestamp
          const hash = tokenAddress.slice(0, 8) + timestamp.toString().slice(-8)
          const hashValue = Number.parseInt(hash, 16) / 0xffffffff // Normaliser entre 0 et 1
          const change = (hashValue - 0.5) * 0.05 // Variation de -2.5% à +2.5%

          currentPrice = currentPrice * (1 + change)

          data.push({
            timestamp,
            price: currentPrice,
          })
        }

        setPriceData(data)
      } catch (error: any) {
        console.error("Erreur lors du chargement des données de prix:", error)
        setError(error.message || "Erreur lors du chargement des données de prix")
      } finally {
        setIsLoading(false)
      }
    }

    fetchPriceData()
  }, [tokenAddress, timeframe, network])

  const formatXAxis = (timestamp: number) => {
    switch (timeframe) {
      case "24h":
        return format(new Date(timestamp), "HH:mm", { locale: fr })
      case "7d":
        return format(new Date(timestamp), "EEE", { locale: fr })
      case "30d":
        return format(new Date(timestamp), "dd MMM", { locale: fr })
      case "90d":
        return format(new Date(timestamp), "MMM", { locale: fr })
      default:
        return format(new Date(timestamp), "dd/MM", { locale: fr })
    }
  }

  const formatTooltipDate = (timestamp: number) => {
    switch (timeframe) {
      case "24h":
        return format(new Date(timestamp), "dd MMM HH:mm", { locale: fr })
      default:
        return format(new Date(timestamp), "dd MMM yyyy", { locale: fr })
    }
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload as PriceData
      return (
        <Card className="p-3 border shadow-sm bg-background">
          <p className="text-sm font-medium">{formatTooltipDate(data.timestamp)}</p>
          <p className="text-sm text-muted-foreground mt-1">
            Prix:{" "}
            <span className="font-medium text-foreground">
              {new Intl.NumberFormat("fr-FR", { style: "currency", currency: "USD" }).format(data.price)}
            </span>
          </p>
        </Card>
      )
    }
    return null
  }

  if (isLoading) {
    return <Skeleton className="h-[400px] w-full" />
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  // Calculer le min et max pour l'axe Y avec une marge
  const minPrice = Math.min(...priceData.map((d) => d.price)) * 0.95
  const maxPrice = Math.max(...priceData.map((d) => d.price)) * 1.05

  return (
    <div className="space-y-4">
      <Tabs
        value={timeframe}
        onValueChange={(value) => setTimeframe(value as "24h" | "7d" | "30d" | "90d")}
        className="w-full"
      >
        <TabsList className="grid grid-cols-4 w-[300px]">
          <TabsTrigger value="24h">24h</TabsTrigger>
          <TabsTrigger value="7d">7j</TabsTrigger>
          <TabsTrigger value="30d">30j</TabsTrigger>
          <TabsTrigger value="90d">90j</TabsTrigger>
        </TabsList>
      </Tabs>

      <ResponsiveContainer width="100%" height={350}>
        <LineChart data={priceData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.2} />
          <XAxis dataKey="timestamp" tickFormatter={formatXAxis} tick={{ fontSize: 12 }} stroke="#6B7280" />
          <YAxis
            domain={[minPrice, maxPrice]}
            tickFormatter={(value) => `$${value.toFixed(6)}`}
            tick={{ fontSize: 12 }}
            stroke="#6B7280"
            width={80}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line type="monotone" dataKey="price" stroke="#8884d8" strokeWidth={2} dot={false} activeDot={{ r: 6 }} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}

// Exporter à la fois par défaut et nommé pour résoudre l'erreur d'importation
export { TokenPriceChart }

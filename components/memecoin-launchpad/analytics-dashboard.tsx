"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Line, Pie, LineChart, Pie<PERSON>hart, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { ArrowUp, ArrowDown, TrendingUp, Users, DollarSign, Activity, Calendar, Zap, ExternalLink } from "lucide-react"
import { useMemeStore } from "@/lib/meme-store"
import { formatNumber, formatCurrency, formatPercentage } from "@/lib/utils"

export default function MemeAnalyticsDashboard() {
  const [timeframe, setTimeframe] = useState<"day" | "week" | "month" | "year">("month")
  const [topMemecoins, setTopMemecoins] = useState<any[]>([])
  const [trendingMemecoins, setTrendingMemecoins] = useState<any[]>([])
  const [overallStats, setOverallStats] = useState<any>({
    totalCoins: 0,
    totalMarketCap: 0,
    totalVolume: 0,
    averagePrice: 0,
    priceChange: 0,
    totalHolders: 0,
  })
  const [isLoading, setIsLoading] = useState(true)
  const memecoins = useMemeStore((state) => state.memecoins)

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)

      try {
        // In a real application, we would fetch data from an API
        // For now, we'll use the data from the meme store

        // Sort memecoins by market cap to get top memecoins
        const sortedByMarketCap = [...memecoins].sort((a, b) => b.marketCap - a.marketCap).slice(0, 5)
        setTopMemecoins(sortedByMarketCap)

        // Get trending memecoins (randomly for demo)
        const shuffled = [...memecoins].sort(() => 0.5 - Math.random())
        setTrendingMemecoins(shuffled.slice(0, 5))

        // Calculate overall stats
        const totalCoins = memecoins.length
        const totalMarketCap = memecoins.reduce((sum, coin) => sum + coin.marketCap, 0)
        const totalVolume = memecoins.reduce((sum, coin) => sum + coin.price * coin.supply * Math.random() * 0.1, 0)
        const averagePrice = memecoins.reduce((sum, coin) => sum + coin.price, 0) / totalCoins
        const priceChange = Math.random() * 20 - 10 // Random value between -10% and +10%
        const totalHolders = memecoins.reduce((sum, coin) => sum + coin.holders, 0)

        setOverallStats({
          totalCoins,
          totalMarketCap,
          totalVolume,
          averagePrice,
          priceChange,
          totalHolders,
        })

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1000))
      } catch (error) {
        console.error("Error fetching memecoin analytics:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [memecoins])

  // Generate chart data based on timeframe
  const generateChartData = (timeframe: "day" | "week" | "month" | "year") => {
    const data = []
    let points: number
    let interval: string

    switch (timeframe) {
      case "day":
        points = 24
        interval = "h"
        break
      case "week":
        points = 7
        interval = "d"
        break
      case "month":
        points = 30
        interval = "d"
        break
      case "year":
        points = 12
        interval = "m"
        break
    }

    let baseValue = 100000

    for (let i = 0; i < points; i++) {
      // Add some randomness to create realistic looking data
      const randomFactor = 1 + (Math.random() * 30 - 15) / 100 // -15% to +15%
      baseValue = baseValue * randomFactor

      data.push({
        name: timeframe === "day" ? `${i}${interval}` : `${i + 1}${interval}`,
        marketCap: Math.round(baseValue),
        volume: Math.round(baseValue * (Math.random() * 0.3)),
        coins: Math.floor(3 + (i / points) * 5),
      })
    }

    return data
  }

  const chartData = generateChartData(timeframe)

  // Generate distribution data for pie chart
  const distributionData = [
    { name: "Top 5", value: 45, fill: "#3B82F6" },
    { name: "Top 6-20", value: 30, fill: "#10B981" },
    { name: "Autres", value: 25, fill: "#6366F1" },
  ]

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array(8)
          .fill(0)
          .map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-5 w-1/2 bg-muted rounded-md"></div>
              </CardHeader>
              <CardContent>
                <div className="h-10 w-2/3 bg-muted rounded-md"></div>
              </CardContent>
            </Card>
          ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Tableau de bord analytique</h2>
          <p className="text-muted-foreground">
            Vue d'ensemble des statistiques et performances du marché des memecoins
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Tabs value={timeframe} onValueChange={(value) => setTimeframe(value as any)}>
            <TabsList>
              <TabsTrigger value="day">1J</TabsTrigger>
              <TabsTrigger value="week">1S</TabsTrigger>
              <TabsTrigger value="month">1M</TabsTrigger>
              <TabsTrigger value="year">1A</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Stats overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Nombre de memecoins</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(overallStats.totalCoins)}</div>
            <p className="text-xs text-muted-foreground">+{Math.floor(Math.random() * 5) + 1} nouveaux cette semaine</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cap. marché totale</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(overallStats.totalMarketCap, true)}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {overallStats.priceChange >= 0 ? (
                <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
              ) : (
                <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
              )}
              <span className={overallStats.priceChange >= 0 ? "text-green-500" : "text-red-500"}>
                {formatPercentage(Math.abs(overallStats.priceChange))}
              </span>
              <span className="text-muted-foreground ml-1">ce mois-ci</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Volume 24h</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(overallStats.totalVolume, true)}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {Math.random() > 0.5 ? (
                <>
                  <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-green-500">{formatPercentage(Math.random() * 20)}</span>
                </>
              ) : (
                <>
                  <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-red-500">{formatPercentage(Math.random() * 20)}</span>
                </>
              )}
              <span className="text-muted-foreground ml-1">depuis hier</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Détenteurs uniques</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(overallStats.totalHolders)}</div>
            <p className="text-xs text-muted-foreground">
              +{formatNumber(Math.floor(Math.random() * 100) + 50)} cette semaine
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Évolution du marché</CardTitle>
            <CardDescription>Cap. marché et volume dans le temps</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                marketCap: {
                  label: "Cap. Marché",
                  color: "hsl(var(--chart-1))",
                },
                volume: {
                  label: "Volume",
                  color: "hsl(var(--chart-2))",
                },
              }}
              className="h-80"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{
                    top: 5,
                    right: 10,
                    left: 10,
                    bottom: 0,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis dataKey="name" className="fill-muted-foreground text-xs" />
                  <YAxis
                    className="fill-muted-foreground text-xs"
                    tickFormatter={(value) => `$${value >= 1000 ? `${(value / 1000).toFixed(0)}K` : value}`}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Line
                    type="monotone"
                    dataKey="marketCap"
                    stroke="var(--color-marketCap)"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="volume"
                    stroke="var(--color-volume)"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Distribution du marché</CardTitle>
            <CardDescription>Répartition de la capitalisation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80 flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={distributionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  />
                  <Tooltip formatter={(value) => [`${value}%`, "Part de marché"]} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Memecoins */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Top Memecoins par Capitalisation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topMemecoins.map((coin, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold text-xs mr-4">
                    {index + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center">
                        <span className="font-medium truncate">{coin.name}</span>
                        <span className="ml-2 text-xs text-muted-foreground">{coin.ticker}</span>
                        {coin.isVerified && (
                          <Badge
                            variant="outline"
                            className="ml-2 bg-green-500/10 text-green-500 hover:bg-green-500/20"
                          >
                            Vérifié
                          </Badge>
                        )}
                      </div>
                      <span className="font-medium text-right">{formatCurrency(coin.price)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">Cap. {formatCurrency(coin.marketCap, true)}</span>
                      <span className="text-xs text-green-500">+{formatPercentage(Math.random() * 50)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Memecoins Tendances</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {trendingMemecoins.map((coin, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-amber-500 to-red-500 flex items-center justify-center text-white font-bold text-xs mr-4">
                    <Zap className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center">
                        <span className="font-medium truncate">{coin.name}</span>
                        <span className="ml-2 text-xs text-muted-foreground">{coin.ticker}</span>
                        <Badge className="ml-2 bg-amber-500/10 text-amber-500 hover:bg-amber-500/20">Tendance</Badge>
                      </div>
                      <span className="font-medium text-right">{formatCurrency(coin.price)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">{formatNumber(coin.holders)} détenteurs</span>
                      <span className="text-xs text-green-500">+{formatPercentage(Math.random() * 100 + 20)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Activité récente</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array(5)
              .fill(0)
              .map((_, i) => {
                const isCreation = Math.random() > 0.7
                const isPurchase = !isCreation && Math.random() > 0.5
                const timestamp = new Date(Date.now() - Math.random() * 86400000 * 2).toLocaleString()
                const randomCoin = memecoins[Math.floor(Math.random() * memecoins.length)]

                return (
                  <div key={i} className="flex items-start">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
                        isCreation
                          ? "bg-blue-500/10 text-blue-500"
                          : isPurchase
                            ? "bg-green-500/10 text-green-500"
                            : "bg-amber-500/10 text-amber-500"
                      }`}
                    >
                      {isCreation ? (
                        <Calendar className="h-4 w-4" />
                      ) : isPurchase ? (
                        <ArrowUp className="h-4 w-4" />
                      ) : (
                        <ArrowDown className="h-4 w-4" />
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm">
                        {isCreation ? (
                          <>
                            Nouveau memecoin <span className="font-medium">{randomCoin.name}</span> créé
                          </>
                        ) : isPurchase ? (
                          <>
                            Achat de{" "}
                            <span className="font-medium">
                              {formatNumber(Math.floor(Math.random() * 10000) + 1000)}
                            </span>{" "}
                            <span className="font-medium">{randomCoin.ticker}</span>
                          </>
                        ) : (
                          <>
                            Vente de{" "}
                            <span className="font-medium">
                              {formatNumber(Math.floor(Math.random() * 10000) + 1000)}
                            </span>{" "}
                            <span className="font-medium">{randomCoin.ticker}</span>
                          </>
                        )}
                      </p>
                      <p className="text-xs text-muted-foreground">{timestamp}</p>
                    </div>
                    <Button variant="ghost" size="sm" className="ml-auto">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                )
              })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

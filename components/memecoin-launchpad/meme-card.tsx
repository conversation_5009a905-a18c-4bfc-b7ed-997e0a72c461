import Link from "next/link"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"

interface MemeCardProps {
  coin: {
    id: string
    name: string
    ticker: string
    imageUrl: string
    marketCap: number
    price: number
    creator: string
    createdAt: number
    replies: number
    isVerified?: boolean
  }
}

export default function MemeCard({ coin }: MemeCardProps) {
  return (
    <Link href={`/memecoin-launchpad/coin/${coin.id}`}>
      <div className="bg-[#111] border border-[#333] rounded-lg overflow-hidden hover:border-[#D4AF37]/50 transition-colors cursor-pointer">
        <div className="relative aspect-video w-full">
          <Image
            src={coin.imageUrl || "/placeholder.svg?height=200&width=400"}
            alt={coin.name}
            fill
            className="object-cover"
          />
        </div>
        <div className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h3 className="font-bold">{coin.name}</h3>
              <div className="flex items-center text-sm text-gray-400">
                <span>({coin.ticker})</span>
                {coin.isVerified && <Badge className="ml-2 bg-[#D4AF37]/20 text-[#D4AF37] text-xs">Verified</Badge>}
              </div>
            </div>
            <div className="text-right">
              <div className="font-bold">${coin.marketCap.toLocaleString()}</div>
              <div className="text-xs text-gray-400">market cap</div>
            </div>
          </div>
          <div className="flex justify-between text-xs text-gray-400">
            <div>created by {coin.creator.substring(0, 6)}...</div>
            <div>replies: {coin.replies}</div>
          </div>
        </div>
      </div>
    </Link>
  )
}

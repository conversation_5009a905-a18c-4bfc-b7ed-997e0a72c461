"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import Link from "next/link"
import {
  Clock,
  Rocket,
  Users,
  DollarSign,
  Calendar,
  Lock,
  ArrowRight,
  TrendingUp,
  Check,
  AlertCircle,
} from "lucide-react"
import { useMemeStore } from "@/lib/meme-store"

interface LaunchData {
  id: string
  name: string
  ticker: string
  imageUrl: string
  status: "upcoming" | "live" | "completed" | "failed"
  launchDate: Date
  endDate?: Date
  hardCap: number
  softCap: number
  raised: number
  participants: number
  price: number
  totalSupply: number
  minContribution: number
  maxContribution: number
  isVerified: boolean
  liquidity: number
  lockPeriod: number
  social: {
    website?: string
    twitter?: string
    telegram?: string
    discord?: string
  }
  description: string
}

export default function LaunchTracker() {
  const [activeTab, setActiveTab] = useState("live")
  const [launches, setLaunches] = useState<{
    upcoming: LaunchData[]
    live: LaunchData[]
    completed: LaunchData[]
  }>({
    upcoming: [],
    live: [],
    completed: [],
  })
  const [isLoading, setIsLoading] = useState(true)

  const memecoins = useMemeStore((state) => state.memecoins)

  useEffect(() => {
    const fetchLaunches = async () => {
      setIsLoading(true)

      // In a real app, this would be an API call
      // For demo purposes, we'll generate some data based on the memecoins

      await new Promise((resolve) => setTimeout(resolve, 1000))

      const now = new Date()
      const upcoming: LaunchData[] = []
      const live: LaunchData[] = []
      const completed: LaunchData[] = []

      // Generate random launches based on existing memecoins
      memecoins.forEach((coin, index) => {
        // For demo purposes, we'll add a bit of randomness
        const random = Math.random()

        const launchData: LaunchData = {
          id: `launch-${coin.id}`,
          name: coin.name,
          ticker: coin.ticker,
          imageUrl: coin.imageUrl,
          launchDate: new Date(now.getTime() + (random > 0.7 ? 1 : random > 0.3 ? -1 : 0) * Math.random() * 864000000), // +/- 10 days
          hardCap: Math.floor(coin.marketCap * 0.5),
          softCap: Math.floor(coin.marketCap * 0.2),
          raised: Math.floor(coin.marketCap * Math.random() * 0.7),
          participants: Math.floor(Math.random() * 1000) + 50,
          price: coin.price,
          totalSupply: coin.supply,
          minContribution: 0.01,
          maxContribution: 5,
          isVerified: coin.isVerified,
          liquidity: Math.floor(Math.random() * 50) + 30, // 30-80%
          lockPeriod: Math.floor(Math.random() * 12) + 1, // 1-12 months
          social: {
            website: coin.website,
            twitter: coin.twitter,
            telegram: coin.telegram,
          },
          description: `${coin.name} est un memecoin innovant sur la blockchain Solana avec des fonctionnalités uniques et un focus sur la communauté.`,
        }

        // Random status assignment for demo
        if (random > 0.7) {
          launchData.status = "upcoming"
          launchData.launchDate = new Date(now.getTime() + Math.random() * 864000000) // Up to 10 days in future
          upcoming.push(launchData)
        } else if (random > 0.4) {
          launchData.status = "live"
          launchData.launchDate = new Date(now.getTime() - Math.random() * 86400000) // Up to 1 day ago
          launchData.endDate = new Date(now.getTime() + Math.random() * 172800000) // Up to 2 days in future
          live.push(launchData)
        } else {
          launchData.status = "completed"
          launchData.launchDate = new Date(now.getTime() - Math.random() * 8640000 * 10) // Up to 100 days ago
          launchData.endDate = new Date(launchData.launchDate.getTime() + Math.random() * 86400000 * 3) // 1-3 days after launch
          launchData.raised =
            random > 0.2 ? launchData.hardCap : Math.floor(launchData.softCap * (0.8 + Math.random() * 0.5))
          completed.push(launchData)
        }
      })

      setLaunches({
        upcoming: upcoming.slice(0, 5),
        live: live.slice(0, 5),
        completed: completed.slice(0, 5),
      })

      setIsLoading(false)
    }

    fetchLaunches()
  }, [memecoins])

  const getStatusBadge = (status: LaunchData["status"]) => {
    switch (status) {
      case "upcoming":
        return <Badge className="bg-blue-500 hover:bg-blue-600">À venir</Badge>
      case "live":
        return <Badge className="bg-[#D4AF37] hover:bg-[#B8941F] text-black">En cours</Badge>
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
            Terminé
          </Badge>
        )
      case "failed":
        return <Badge variant="destructive">Échoué</Badge>
    }
  }

  const getTimeRemaining = (date: Date) => {
    const now = new Date()
    const diff = date.getTime() - now.getTime()

    if (diff <= 0) return "Terminé"

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
      return `${days}j ${hours}h restants`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m restants`
    } else {
      return `${minutes}m restants`
    }
  }

  const getProgressPercentage = (raised: number, hardCap: number) => {
    return Math.min(100, Math.round((raised / hardCap) * 100))
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatCurrency = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    } else {
      return `$${value.toFixed(2)}`
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4 animate-pulse">
        <Tabs defaultValue="live">
          <TabsList>
            <TabsTrigger value="live" disabled>
              En cours
            </TabsTrigger>
            <TabsTrigger value="upcoming" disabled>
              À venir
            </TabsTrigger>
            <TabsTrigger value="completed" disabled>
              Terminés
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-gray-300"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-3"></div>
                <div className="h-2 bg-gray-200 rounded w-full mb-4"></div>
                <div className="grid grid-cols-2 gap-2 mb-4">
                  <div className="h-10 bg-gray-200 rounded"></div>
                  <div className="h-10 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
              <CardFooter>
                <div className="h-8 bg-gray-300 rounded w-full"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="live">
            <Rocket className="mr-2 h-4 w-4" />
            En cours ({launches.live.length})
          </TabsTrigger>
          <TabsTrigger value="upcoming">
            <Calendar className="mr-2 h-4 w-4" />À venir ({launches.upcoming.length})
          </TabsTrigger>
          <TabsTrigger value="completed">
            <Check className="mr-2 h-4 w-4" />
            Terminés ({launches.completed.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="live" className="pt-4">
          {launches.live.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {launches.live.map((launch) => (
                <Card key={launch.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage
                          src={launch.imageUrl || `/placeholder.svg?height=40&width=40&text=${launch.ticker}`}
                          alt={launch.name}
                        />
                        <AvatarFallback>{launch.ticker.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <CardTitle className="text-lg mr-2">{launch.name}</CardTitle>
                            {getStatusBadge(launch.status)}
                          </div>
                          {launch.isVerified && (
                            <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                              Vérifié
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{launch.ticker}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <p className="text-sm mb-4 line-clamp-2">{launch.description}</p>

                    {launch.endDate && (
                      <div className="flex items-center text-sm text-amber-500 mb-2">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{getTimeRemaining(launch.endDate)}</span>
                      </div>
                    )}

                    <div className="mb-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-muted-foreground">Progression</span>
                        <span>{getProgressPercentage(launch.raised, launch.hardCap)}%</span>
                      </div>
                      <Progress value={getProgressPercentage(launch.raised, launch.hardCap)} className="h-2" />
                      <div className="flex justify-between text-xs mt-1">
                        <span className="text-muted-foreground">
                          {formatCurrency(launch.raised)} / {formatCurrency(launch.hardCap)}
                        </span>
                        <div className="flex items-center">
                          <Users className="h-3 w-3 mr-1 text-muted-foreground" />
                          <span className="text-muted-foreground">{launch.participants}</span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-[#111] p-2 rounded-md text-center">
                        <p className="text-xs text-muted-foreground">Prix</p>
                        <p className="font-medium">${launch.price.toFixed(6)}</p>
                      </div>
                      <div className="bg-[#111] p-2 rounded-md text-center">
                        <p className="text-xs text-muted-foreground">Liquidité</p>
                        <p className="font-medium">
                          {launch.liquidity}% <Lock className="h-3 w-3 inline" />
                        </p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full bg-[#D4AF37] hover:bg-[#B8941F] text-black" asChild>
                      <Link href={`/memecoin-launchpad/launch/${launch.id}`}>Rejoindre le lancement</Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8">
              <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Aucun lancement en cours</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Il n'y a actuellement aucun lancement de memecoin en cours.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="upcoming" className="pt-4">
          {launches.upcoming.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {launches.upcoming.map((launch) => (
                <Card key={launch.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage
                          src={launch.imageUrl || `/placeholder.svg?height=40&width=40&text=${launch.ticker}`}
                          alt={launch.name}
                        />
                        <AvatarFallback>{launch.ticker.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <CardTitle className="text-lg mr-2">{launch.name}</CardTitle>
                            {getStatusBadge(launch.status)}
                          </div>
                          {launch.isVerified && (
                            <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                              Vérifié
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{launch.ticker}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <p className="text-sm mb-4 line-clamp-2">{launch.description}</p>

                    <div className="flex items-center text-sm text-blue-500 mb-4">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span>Début: {formatDate(launch.launchDate)}</span>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-[#111] p-2 rounded-md text-center">
                        <p className="text-xs text-muted-foreground">Prix initial</p>
                        <p className="font-medium">${launch.price.toFixed(6)}</p>
                      </div>
                      <div className="bg-[#111] p-2 rounded-md text-center">
                        <p className="text-xs text-muted-foreground">Hard Cap</p>
                        <p className="font-medium">{formatCurrency(launch.hardCap)}</p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full" variant="outline" asChild>
                      <Link href={`/memecoin-launchpad/launch/${launch.id}`}>
                        <TrendingUp className="mr-2 h-4 w-4" />
                        Configurer une alerte
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8">
              <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Aucun lancement à venir</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Il n'y a actuellement aucun lancement de memecoin planifié.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="completed" className="pt-4">
          {launches.completed.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {launches.completed.map((launch) => (
                <Card key={launch.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage
                          src={launch.imageUrl || `/placeholder.svg?height=40&width=40&text=${launch.ticker}`}
                          alt={launch.name}
                        />
                        <AvatarFallback>{launch.ticker.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <CardTitle className="text-lg mr-2">{launch.name}</CardTitle>
                            {getStatusBadge(launch.status)}
                          </div>
                          {launch.isVerified && (
                            <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-500/20">
                              Vérifié
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{launch.ticker}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-1">
                        <div className="flex items-center text-sm">
                          <DollarSign className="h-4 w-4 mr-1 text-green-500" />
                          <span className="text-green-500">
                            Objectif {launch.raised >= launch.hardCap ? "atteint" : "partiel"}
                          </span>
                        </div>
                        <span className="text-sm">{getProgressPercentage(launch.raised, launch.hardCap)}%</span>
                      </div>
                      <Progress value={getProgressPercentage(launch.raised, launch.hardCap)} className="h-2" />
                      <div className="flex justify-between text-xs mt-1">
                        <span className="text-muted-foreground">
                          {formatCurrency(launch.raised)} / {formatCurrency(launch.hardCap)}
                        </span>
                        <div className="flex items-center">
                          <Users className="h-3 w-3 mr-1 text-muted-foreground" />
                          <span className="text-muted-foreground">{launch.participants}</span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-[#111] p-2 rounded-md text-center">
                        <p className="text-xs text-muted-foreground">Début</p>
                        <p className="text-xs font-medium">{formatDate(launch.launchDate)}</p>
                      </div>
                      <div className="bg-[#111] p-2 rounded-md text-center">
                        <p className="text-xs text-muted-foreground">Fin</p>
                        <p className="text-xs font-medium">{formatDate(launch.endDate || launch.launchDate)}</p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full" asChild>
                      <Link href={`/token/${launch.ticker}`}>
                        <ArrowRight className="mr-2 h-4 w-4" />
                        Voir le token
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8">
              <Check className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Aucun lancement terminé</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Il n'y a pas encore eu de lancements de memecoin terminés.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

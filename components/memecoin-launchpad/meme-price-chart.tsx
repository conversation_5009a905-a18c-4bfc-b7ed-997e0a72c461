"use client"

import { useState, useEffect } from "react"
import { Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js"

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler)

interface MemePriceChartProps {
  coinId: string
}

export default function MemePriceChart({ coinId }: MemePriceChartProps) {
  const [chartData, setChartData] = useState<any>(null)
  const [timeframe, setTimeframe] = useState("24h")

  useEffect(() => {
    // In a real implementation, this would fetch historical price data
    // For this demo, we'll generate random data

    const generateRandomData = () => {
      const labels = []
      const data = []
      const pointCount = timeframe === "24h" ? 24 : timeframe === "7d" ? 7 : 30
      const basePrice = 0.00001 + Math.random() * 0.0001

      for (let i = 0; i < pointCount; i++) {
        if (timeframe === "24h") {
          labels.push(`${i}:00`)
        } else {
          labels.push(`Day ${i + 1}`)
        }

        // Generate a random price with an upward trend
        const randomFactor = 0.8 + Math.random() * 0.4
        data.push(basePrice * (1 + i / pointCount) * randomFactor)
      }

      return {
        labels,
        datasets: [
          {
            label: "Price (SOL)",
            data,
            borderColor: "#D4AF37",
            backgroundColor: "rgba(212, 175, 55, 0.1)",
            tension: 0.4,
            fill: true,
            pointRadius: 0,
            borderWidth: 2,
          },
        ],
      }
    }

    setChartData(generateRandomData())
  }, [coinId, timeframe])

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: "index" as const,
        intersect: false,
        backgroundColor: "#111",
        titleColor: "#fff",
        bodyColor: "#aaa",
        borderColor: "#333",
        borderWidth: 1,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
          drawBorder: false,
        },
        ticks: {
          color: "#666",
        },
      },
      y: {
        grid: {
          color: "#222",
          drawBorder: false,
        },
        ticks: {
          color: "#666",
          callback: (value: number) => `$${value.toFixed(7)}`,
        },
      },
    },
    interaction: {
      mode: "nearest" as const,
      axis: "x" as const,
      intersect: false,
    },
  }

  return (
    <div className="h-full">
      <div className="flex justify-end mb-4">
        <div className="flex space-x-2">
          <button
            className={`px-2 py-1 text-xs rounded ${timeframe === "24h" ? "bg-[#D4AF37] text-black" : "bg-[#222] text-gray-400"}`}
            onClick={() => setTimeframe("24h")}
          >
            24H
          </button>
          <button
            className={`px-2 py-1 text-xs rounded ${timeframe === "7d" ? "bg-[#D4AF37] text-black" : "bg-[#222] text-gray-400"}`}
            onClick={() => setTimeframe("7d")}
          >
            7D
          </button>
          <button
            className={`px-2 py-1 text-xs rounded ${timeframe === "30d" ? "bg-[#D4AF37] text-black" : "bg-[#222] text-gray-400"}`}
            onClick={() => setTimeframe("30d")}
          >
            30D
          </button>
        </div>
      </div>

      {chartData ? (
        <div className="h-[250px]">
          <Line data={chartData} options={options} />
        </div>
      ) : (
        <div className="h-[250px] flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#D4AF37]"></div>
        </div>
      )}
    </div>
  )
}

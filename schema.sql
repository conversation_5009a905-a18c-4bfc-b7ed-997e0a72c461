-- Table pour stocker les informations des tokens
CREATE TABLE tokens (
    id SERIAL PRIMARY KEY,
    mint_address VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    symbol VARCHAR(255) NOT NULL,
    decimals INTEGER NOT NULL,
    total_supply BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    creator_wallet VARCHAR(255) NOT NULL,
    -- Ajoutez d'autres champs pertinents ici
);

-- Table pour stocker les paramètres de la courbe de liaison
CREATE TABLE bonding_curve_params (
    token_id INTEGER REFERENCES tokens(id),
    k BIGINT NOT NULL,
    initial_supply BIGINT NOT NULL,
    initial_price DECIMAL NOT NULL,
    hard_cap DECIMAL NOT NULL,
    platform_fee_percent DECIMAL NOT NULL,
    PRIMARY KEY (token_id)
);

-- Table pour stocker les informations des transactions
CREATE TABLE transactions (
    id SERIAL PRIMARY KEY,
    token_id INTEGER REFERENCES tokens(id),
    tx_hash VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- buy ou sell
    amount DECIMAL NOT NULL,
    price DECIMAL NOT NULL,
    wallet_address VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT NOW()
);

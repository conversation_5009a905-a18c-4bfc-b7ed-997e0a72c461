import { type NextRequest, NextResponse } from "next/server"
import { isAdmin } from "@/lib/admin-service"

export function adminAuthMiddleware(request: NextRequest) {
  // Exclure la page de connexion admin de la vérification
  if (request.nextUrl.pathname === "/admin/connect") {
    return NextResponse.next()
  }

  // Récupérer l'adresse du wallet depuis les cookies
  const walletAddress = request.cookies.get("walletAddress")?.value
  const adminAuthToken = request.cookies.get("adminAuthToken")?.value

  // Si pas de wallet connecté, rediriger vers la page de connexion admin
  if (!walletAddress || !adminAuthToken) {
    console.log("Admin Auth Middleware: No wallet connected, redirecting to connect page")
    return NextResponse.redirect(new URL("/admin/connect", request.url))
  }

  // Vérifier si le wallet est un admin et si le token est valide
  // Cette vérification devrait idéalement être faite côté serveur avec une validation du token
  if (!isAdmin(walletAddress)) {
    console.log(`Admin Auth Middleware: Wallet ${walletAddress} is not an admin, redirecting`)

    // Supprimer les cookies incorrects
    const response = NextResponse.redirect(new URL("/admin/connect", request.url))
    response.cookies.delete("adminAuthToken")

    return response
  }

  // Si c'est un admin, autoriser l'accès
  console.log(`Admin Auth Middleware: Wallet ${walletAddress} is an admin, access granted`)
  return NextResponse.next()
}

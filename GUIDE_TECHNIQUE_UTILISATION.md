# Guide Technique et d'Utilisation - Plateforme Global Finance (GF-beta)

## Installation et Configuration

### Prérequis Système
```bash
# Versions requises
Node.js >= 18.0.0
npm >= 9.0.0 ou pnpm >= 8.0.0
Git >= 2.30.0

# Outils recommandés
VS Code avec extensions TypeScript/React
Phantom Wallet ou Solflare pour tests
Solana CLI pour développement avancé
```

### Installation du Projet
```bash
# Cloner le repository
git clone https://github.com/your-org/solana-platform.git
cd solana-platform

# Installer les dépendances
pnpm install

# Configurer les variables d'environnement
cp .env.example .env.local

# Lancer en mode développement
pnpm dev
```

### Configuration des Variables d'Environnement
```bash
# .env.local
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.devnet.solana.com
NEXT_PUBLIC_BNB_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545
ADMIN_WALLET=AeDCnGRAEghvLTbZG3XJkfvi65qaqpgJvNTGNWWhBPP5
COINGECKO_API_KEY=your_coingecko_api_key
MARKET_API_ENDPOINT=https://api.solscan.io
```

## Guide d'Utilisation

### 1. Connexion Wallet

#### Étapes de Connexion
1. **Installer un Wallet Solana** (Phantom, Solflare, Torus)
2. **Créer ou importer un compte** avec des SOL de test
3. **Connecter le wallet** via le bouton "Connect Wallet"
4. **Approuver la connexion** dans l'extension wallet

#### Obtenir des SOL de Test
```bash
# Via Solana CLI
solana airdrop 2 YOUR_WALLET_ADDRESS --url devnet

# Via faucet web
https://faucet.solana.com/
```

### 2. Création de Tokens

#### Token Factory Standard
1. **Accéder à Token Factory** depuis le menu principal
2. **Remplir les informations** :
   - Nom du token (ex: "My Token")
   - Symbole (ex: "MTK")
   - Décimales (généralement 9)
   - Supply initiale (ex: 1,000,000)
   - Description et métadonnées
3. **Configurer la sécurité** :
   - Limites de transaction
   - Blacklist si nécessaire
   - Autorités (mint, freeze)
4. **Choisir le suffixe** (GFMM, GFAI, personnalisé)
5. **Confirmer et payer** les frais de création
6. **Signer la transaction** dans votre wallet

#### Token Quantum (Avancé)
1. **Accéder à Token Quantum** pour fonctionnalités IA
2. **Sélectionner le type** :
   - Token généré par IA
   - Token avec analyse de marché
   - Token avec gouvernance DAO
3. **Configurer les paramètres avancés** :
   - Bonding curve
   - Vesting schedule
   - Mécanismes anti-dump
4. **Lancement automatisé** sur DEX

### 3. Memecoin Launchpad

#### Création de Memecoin
1. **Accéder au Launchpad** depuis le menu
2. **Utiliser l'interface simplifiée** :
   - Upload d'image/logo
   - Nom et ticker accrocheurs
   - Description humoristique
   - Liens réseaux sociaux
3. **Configurer le lancement** :
   - Prix initial
   - Supply totale
   - Mécanisme de fair launch
4. **Publier et promouvoir** sur les réseaux

#### Suivi des Lancements
- **Dashboard en temps réel** des performances
- **Métriques communautaires** (holders, volume)
- **Intégration réseaux sociaux** pour promotion
- **Analytics détaillées** post-lancement

### 4. Trading et Marché

#### Interface de Trading
1. **Explorer le marché** via l'onglet Market
2. **Rechercher des tokens** par nom/adresse
3. **Analyser les graphiques** de prix
4. **Consulter les métriques** :
   - Market cap
   - Volume 24h
   - Nombre de holders
   - Historique des prix

#### Fonctionnalités Avancées
- **Alertes de prix** personnalisables
- **Analyse technique** intégrée
- **Carnet d'ordres** en temps réel
- **Historique des transactions** détaillé

### 5. Staking et DeFi

#### Créer un Pool de Staking
1. **Accéder à la section Staking**
2. **Sélectionner votre token** à staker
3. **Configurer le pool** :
   - APR (taux de récompense annuel)
   - Période de verrouillage
   - Récompenses en token natif ou autre
4. **Déployer le pool** et commencer à staker

#### Participer au Staking
1. **Choisir un pool** existant
2. **Connecter votre wallet** avec les tokens
3. **Entrer le montant** à staker
4. **Confirmer la transaction** de staking
5. **Suivre les récompenses** en temps réel

### 6. NFT Gallery

#### Créer des NFTs
1. **Accéder à NFT Gallery**
2. **Upload des fichiers** (image, vidéo, audio)
3. **Remplir les métadonnées** :
   - Nom et description
   - Attributs et rareté
   - Royalties pour le créateur
4. **Mint le NFT** sur Solana
5. **Lister sur le marketplace** si souhaité

#### Trading de NFTs
- **Explorer les collections** disponibles
- **Filtrer par prix/rareté** 
- **Acheter/vendre** directement
- **Suivre les tendances** du marché NFT

## Guide Administrateur

### Accès Administration
```typescript
// Vérification automatique du rôle admin
const isAdmin = publicKey?.toString() === ADMIN_WALLET_ADDRESS

// Accès au dashboard admin
/admin - Interface principale
/admin/user-management - Gestion utilisateurs
/admin/token-management - Gestion tokens
/admin/platform-settings - Configuration plateforme
```

### Fonctionnalités Admin

#### Gestion des Utilisateurs
- **Voir tous les utilisateurs** connectés
- **Assigner des rôles** (user, creator, moderator, admin)
- **Blacklister des adresses** malveillantes
- **Générer des rapports** d'activité

#### Gestion des Tokens
- **Monitorer tous les tokens** créés
- **Approuver/rejeter** les nouveaux tokens
- **Configurer les frais** de création
- **Gérer les suffixes** disponibles

#### Configuration Plateforme
- **Paramètres réseau** (Devnet/Mainnet)
- **Frais de la plateforme** 
- **Limites de création** par utilisateur
- **Maintenance mode** si nécessaire

## Développement et Personnalisation

### Structure du Code

#### Ajouter un Nouveau Service
```typescript
// lib/my-custom-service.ts
export class MyCustomService {
  constructor(private connection: Connection) {}
  
  async customFunction(params: any): Promise<any> {
    // Logique personnalisée
    return result
  }
}

// Utilisation dans un composant
import { MyCustomService } from '@/lib/my-custom-service'

const service = new MyCustomService(connection)
const result = await service.customFunction(params)
```

#### Créer un Nouveau Composant
```typescript
// components/my-component.tsx
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface MyComponentProps {
  title: string
  onAction: () => void
}

export function MyComponent({ title, onAction }: MyComponentProps) {
  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <Button onClick={onAction}>Action</Button>
    </Card>
  )
}
```

#### Ajouter une Nouvelle Page
```typescript
// app/my-page/page.tsx
import { MyComponent } from '@/components/my-component'

export default function MyPage() {
  const handleAction = () => {
    console.log('Action triggered')
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Ma Nouvelle Page</h1>
      <MyComponent title="Mon Composant" onAction={handleAction} />
    </div>
  )
}
```

### Tests et Débogage

#### Tests Unitaires
```bash
# Installer les dépendances de test
pnpm add -D jest @testing-library/react @testing-library/jest-dom

# Lancer les tests
pnpm test

# Tests avec couverture
pnpm test:coverage
```

#### Débogage Blockchain
```typescript
// Activer les logs détaillés
const connection = new Connection(RPC_URL, {
  commitment: 'confirmed',
  confirmTransactionInitialTimeout: 60000,
  disableRetryOnRateLimit: false
})

// Logger les transactions
console.log('Transaction signature:', signature)
console.log('Transaction details:', await connection.getTransaction(signature))
```

### Déploiement

#### Build de Production
```bash
# Build optimisé
pnpm build

# Vérifier le build
pnpm start

# Analyser le bundle
pnpm analyze
```

#### Variables d'Environnement Production
```bash
# .env.production
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
NEXT_PUBLIC_ENVIRONMENT=production
ADMIN_WALLET=PRODUCTION_ADMIN_WALLET_ADDRESS
```

## Dépannage

### Problèmes Courants

#### Erreur de Connexion Wallet
```typescript
// Vérifier la connexion
if (!connected || !publicKey) {
  throw new Error('Wallet not connected')
}

// Vérifier le réseau
if (network !== 'devnet') {
  throw new Error('Please switch to Devnet')
}
```

#### Erreur de Transaction
```typescript
// Gestion d'erreur robuste
try {
  const signature = await sendTransaction(transaction, connection)
  await connection.confirmTransaction(signature)
} catch (error) {
  console.error('Transaction failed:', error)
  // Retry logic ou fallback
}
```

#### Performance Lente
- **Vérifier la connexion RPC** (utiliser un endpoint rapide)
- **Optimiser les requêtes** (batch requests)
- **Mettre en cache** les données fréquemment utilisées
- **Utiliser le lazy loading** pour les composants lourds

Cette documentation couvre tous les aspects techniques et d'utilisation de la plateforme Global Finance.

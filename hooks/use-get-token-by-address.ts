"use client"

import { useState, useEffect } from "react"
import { getTokenDetails } from "@/lib/token-service"

export function useGetTokenByAddress(address: string) {
  const [data, setData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      if (!address) {
        setData(null)
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const tokenData = await getTokenDetails(address)
        setData(tokenData)
      } catch (err: any) {
        console.error("Erreur lors de la récupération des détails du token:", err)
        setError(err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [address])

  return { data, isLoading, error }
}

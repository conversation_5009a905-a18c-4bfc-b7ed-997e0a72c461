"use client"

import { useState, useEffect } from "react"
import { getTokenPriceHistory } from "@/lib/token-service"

export function useTokenPriceHistory(address: string, timeframe: "24h" | "7d" | "30d" | "all" = "24h") {
  const [data, setData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      if (!address) {
        setData([])
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const priceHistory = await getTokenPriceHistory(address, timeframe)
        setData(priceHistory)
      } catch (err: any) {
        console.error("Erreur lors de la récupération de l'historique des prix:", err)
        setError(err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [address, timeframe])

  return { data, isLoading, error }
}

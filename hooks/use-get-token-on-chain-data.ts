"use client"

import { useState, useEffect } from "react"
import { getTokenOnChainData } from "@/lib/token-service"

export function useGetTokenOnChainData(address: string) {
  const [data, setData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      if (!address) {
        setData(null)
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const onChainData = await getTokenOnChainData(address)
        setData(onChainData)
      } catch (err: any) {
        console.error("Erreur lors de la récupération des données on-chain du token:", err)
        setError(err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [address])

  return { data, isLoading, error }
}

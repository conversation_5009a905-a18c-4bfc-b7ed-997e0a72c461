"use client"

import type React from "react"

import { useState, useEffect, createContext } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { useToast } from "@/components/ui/use-toast"

interface Permission {
  id: string
  name: string
  description: string
}

interface AdminContextType {
  isAdmin: boolean
  isSuperAdmin: boolean
  isLoading: boolean
  permissions: string[]
  availablePermissions: Permission[]
  checkPermission: (permission: string) => boolean
  refreshAdminStatus: () => Promise<void>
}

const AdminContext = createContext<AdminContextType | undefined>(undefined)

export function AdminProvider({ children }: { children: React.ReactNode }) {
  const { publicKey, connected } = useWallet()
  const { toast } = useToast()
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const [isSuperAdmin, setIsSuperAdmin] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [permissions, setPermissions] = useState<string[]>([])
  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([])

  const checkAdminStatus = async (): Promise<void> => {
    if (!connected || !publicKey) {
      setIsAdmin(false)
      setIsSuperAdmin(false)
      setPermissions([])
      setIsLoading(false)
      return
    }

    try {
      // Vérifier le statut admin
      const response = await fetch(`/api/admin/check-auth?wallet=${publicKey.toString()}`)

      if (!response.ok) {
        throw new Error("Failed to verify admin status")
      }

      const data = await response.json()
      setIsAdmin(data.isAdmin)
      setIsSuperAdmin(data.isSuperAdmin || false)
      setPermissions(data.permissions || [])

      // Charger les permissions disponibles
      if (data.isAdmin) {
        const permissionsResponse = await fetch("/api/admin/permissions")
        if (permissionsResponse.ok) {
          const permissionsData = await permissionsResponse.json()
          setAvailablePermissions(permissionsData.permissions || [])
        }
      }

      setIsLoading(false)
    } catch (error) {
      console.error("Error checking admin status:", error)
      setIsAdmin(false)
      setIsSuperAdmin(false)
      setPermissions([])
      setIsLoading(false)
      toast({
        title: "Error",
        description: "Failed to verify admin status. Please try again.",
        variant: "destructive",
      })
    }
  }

  const checkPermission = (permission: string): boolean => {
    if (isSuperAdmin || permissions.includes("all")) {
      return true
    }
    return permissions.includes(permission)
  }

  const refreshAdminStatus = async (): Promise<void> => {
    setIsLoading(true)
    await checkAdminStatus()
  }

  useEffect(() => {
    if (connected && publicKey) {
      checkAdminStatus()
    } else {
      setIsAdmin(false)
      setIsSuperAdmin(false)
      setPermissions([])
      setIsLoading(false)
    }
  }, [connected, publicKey])

  return (
    <AdminContext.Provider
      value={{
        isAdmin,
        isSuperAdmin,
        isLoading,
        permissions,
        availablePermissions,
        checkPermission,
        refreshAdminStatus,
      }}
    >
      {children}
    </AdminContext.Provider>
  )
}

export function useAdmin() {
  const { publicKey, connected } = useWallet()
  const [isAdmin, setIsAdmin] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkAdminStatus = async () => {
      setIsLoading(true)

      if (connected && publicKey) {
        const walletAddress = publicKey.toString()

        try {
          // Vérifier directement avec la fonction isAdmin
          const adminStatus = isAdmin(walletAddress)

          // Stocker l'adresse du wallet dans un cookie pour le middleware
          document.cookie = `walletAddress=${walletAddress}; path=/; max-age=3600; SameSite=Strict`

          setIsAdmin(adminStatus)
        } catch (error) {
          console.error("Erreur lors de la vérification du statut admin:", error)
          setIsAdmin(false)
        }
      } else {
        setIsAdmin(false)
      }

      setIsLoading(false)
    }

    checkAdminStatus()
  }, [connected, publicKey])

  return { isAdmin, isLoading }
}

"use client"

import { useState, useEffect } from "react"
import { useNetwork } from "@/contexts/network-context"
import TokenSuffixService from "@/lib/token-suffix-service"

export function useTokenSuffix() {
  const { activeNetwork } = useNetwork()
  const [suffix, setSuffix] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadSuffix = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const networkSuffix = await TokenSuffixService.getSuffixForNetwork(activeNetwork.id)
        setSuffix(networkSuffix)
      } catch (err) {
        console.error("Erreur lors du chargement du suffixe:", err)
        setError("Impossible de charger le suffixe configuré")
        setSuffix("BETAGF") // Suffixe par défaut en cas d'erreur
      } finally {
        setIsLoading(false)
      }
    }

    loadSuffix()
  }, [activeNetwork])

  return { suffix, isLoading, error }
}

"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { useToast } from "@/components/ui/use-toast"
import { getNetworkById, type NetworkConfig } from "@/lib/network-config"

interface NetworkContextType {
  network: string
  switchNetwork: (network: string) => Promise<void>
  isLoading: boolean
  activeNetwork: NetworkConfig | null
}

const NetworkContext = createContext<NetworkContextType | undefined>(undefined)

export function NetworkProvider({ children }: { children: ReactNode }) {
  const [network, setNetwork] = useState<string>("solana-devnet")
  const [isLoading, setIsLoading] = useState(false)
  const [activeNetwork, setActiveNetwork] = useState<NetworkConfig | null>(null)
  const { toast } = useToast()

  // Charger le réseau depuis le localStorage au démarrage
  useEffect(() => {
    const savedNetwork = localStorage.getItem("network") as string
    if (savedNetwork && ["solana-devnet", "solana-mainnet"].includes(savedNetwork)) {
      setNetwork(savedNetwork)
    }
  }, [])

  // Mettre à jour activeNetwork quand network change
  useEffect(() => {
    const networkConfig = getNetworkById(network)
    setActiveNetwork(networkConfig || null)
  }, [network])

  const switchNetwork = async (newNetwork: string) => {
    if (network === newNetwork) return

    setIsLoading(true)

    try {
      // Vérifier que le réseau est valide
      const networkConfig = getNetworkById(newNetwork)
      if (!networkConfig) {
        throw new Error(`Réseau invalide: ${newNetwork}`)
      }

      // Enregistrer le réseau dans le localStorage
      localStorage.setItem("network", newNetwork)

      // Mettre à jour l'état
      setNetwork(newNetwork)
      setActiveNetwork(networkConfig)

      toast({
        title: "Réseau changé",
        description: `Vous êtes maintenant connecté au réseau ${networkConfig.name}`,
      })
    } catch (error) {
      console.error("Erreur lors du changement de réseau:", error)
      toast({
        title: "Erreur",
        description: "Impossible de changer de réseau. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <NetworkContext.Provider value={{ network, switchNetwork, isLoading, activeNetwork }}>
      {children}
    </NetworkContext.Provider>
  )
}

export function useNetwork() {
  const context = useContext(NetworkContext)
  if (context === undefined) {
    throw new Error("useNetwork must be used within a NetworkProvider")
  }
  return context
}

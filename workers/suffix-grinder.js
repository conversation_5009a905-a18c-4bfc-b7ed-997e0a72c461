const { parentPort, workerData } = require("worker_threads")
const { Keypair } = require("@solana/web3.js")

const { suffix, maxAttempts, workerId } = workerData

console.log(`Worker ${workerId} démar<PERSON> pour rechercher le suffixe: ${suffix}`)

let attempts = 0
const startTime = Date.now()

while (attempts < maxAttempts) {
  attempts++

  // Générer un nouveau keypair
  const keypair = Keypair.generate()
  const publicKey = keypair.publicKey.toString()

  // Vérifier si l'adresse se termine par le suffixe
  if (publicKey.endsWith(suffix)) {
    const timeElapsed = (Date.now() - startTime) / 1000
    console.log(
      `Worker ${workerId}: Keypair trouvé après ${attempts} tentatives en ${timeElapsed.toFixed(2)} secondes: ${publicKey}`,
    )

    // Convertir la clé secrète en tableau de nombres
    const secretKey = Array.from(keypair.secretKey)

    // Envoyer le résultat au thread principal
    parentPort.postMessage({
      success: true,
      keypair: {
        public: publicKey,
        secret: secretKey,
      },
      attempts,
      timeElapsed,
    })

    // Terminer le worker
    process.exit(0)
  }

  // Afficher la progression toutes les 10000 tentatives
  if (attempts % 10000 === 0) {
    console.log(`Worker ${workerId}: ${attempts} tentatives effectuées...`)
  }
}

// Si on arrive ici, c'est qu'on n'a pas trouvé de correspondance
const timeElapsed = (Date.now() - startTime) / 1000
console.log(`Worker ${workerId}: Échec après ${maxAttempts} tentatives (${timeElapsed.toFixed(2)} secondes)`)

parentPort.postMessage({
  success: false,
  attempts,
  timeElapsed,
  error: `Worker ${workerId}: Échec après ${maxAttempts} tentatives (${timeElapsed.toFixed(2)} secondes)`,
})

process.exit(0)

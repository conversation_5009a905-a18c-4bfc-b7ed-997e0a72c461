-- Table pour stocker les configurations de lancement de tokens
CREATE TABLE quantum_launch_configs (
  id SERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  suffix VARCHAR(20) NOT NULL,
  decimals INTEGER NOT NULL DEFAULT 9,
  total_supply BIGINT NOT NULL,
  description TEXT,
  website VARCHAR(255),
  twitter VARCHAR(255),
  telegram VARCHAR(255),
  initial_price DECIMAL(18, 12) NOT NULL,
  soft_cap DECIMAL(18, 6) NOT NULL,
  hard_cap DECIMAL(18, 6) NOT NULL,
  min_buy DECIMAL(18, 6) NOT NULL,
  max_buy DECIMAL(18, 6) NOT NULL,
  liquidity_percentage INTEGER NOT NULL,
  team_percentage INTEGER NOT NULL,
  marketing_percentage INTEGER NOT NULL,
  reserve_percentage INTEGER NOT NULL,
  liquidity_lock_period INTEGER NOT NULL,
  team_lock_period INTEGER NOT NULL,
  presale_enabled BOOLEAN NOT NULL DEFAULT FALSE,
  fair_launch_enabled BOOLEAN NOT NULL DEFAULT FALSE,
  ido_enabled BOOLEAN NOT NULL DEFAULT FALSE,
  anti_bot BOOLEAN NOT NULL DEFAULT FALSE,
  anti_dump BOOLEAN NOT NULL DEFAULT FALSE,
  max_wallet_percentage DECIMAL(5, 2) NOT NULL,
  max_tx_percentage DECIMAL(5, 2) NOT NULL,
  buy_tax DECIMAL(5, 2) NOT NULL,
  sell_tax DECIMAL(5, 2) NOT NULL,
  transfer_tax DECIMAL(5, 2) NOT NULL,
  team_wallet VARCHAR(44) NOT NULL,
  marketing_wallet VARCHAR(44) NOT NULL,
  target_dex VARCHAR(20) NOT NULL,
  listing_multiplier DECIMAL(5, 2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table pour stocker les lancements de tokens
CREATE TABLE quantum_launches (
  id VARCHAR(50) PRIMARY KEY,
  token_address VARCHAR(44) NOT NULL,
  owner_address VARCHAR(44) NOT NULL,
  config_id INTEGER NOT NULL REFERENCES quantum_launch_configs(id),
  current_phase_id VARCHAR(50),
  launch_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  status VARCHAR(20) NOT NULL DEFAULT 'setup',
  total_raised DECIMAL(18, 6) NOT NULL DEFAULT 0,
  participants INTEGER NOT NULL DEFAULT 0,
  market_cap DECIMAL(18, 6) NOT NULL DEFAULT 0,
  liquidity_value DECIMAL(18, 6) NOT NULL DEFAULT 0,
  liquidity_pair VARCHAR(100)
);

-- Table pour stocker les phases de lancement
CREATE TABLE quantum_launch_phases (
  id VARCHAR(50) PRIMARY KEY,
  launch_id VARCHAR(50) NOT NULL REFERENCES quantum_launches(id),
  name VARCHAR(50) NOT NULL,
  description TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  target_amount DECIMAL(18, 6) NOT NULL,
  min_contribution DECIMAL(18, 6) NOT NULL,
  max_contribution DECIMAL(18, 6) NOT NULL,
  price DECIMAL(18, 12) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  participants INTEGER NOT NULL DEFAULT 0,
  amount_raised DECIMAL(18, 6) NOT NULL DEFAULT 0,
  percentage_complete DECIMAL(5, 2) NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table pour stocker les listings DEX
CREATE TABLE quantum_dex_listings (
  id SERIAL PRIMARY KEY,
  launch_id VARCHAR(50) NOT NULL REFERENCES quantum_launches(id),
  dex VARCHAR(50) NOT NULL,
  pair_address VARCHAR(100) NOT NULL,
  listing_date TIMESTAMP WITH TIME ZONE NOT NULL,
  initial_price DECIMAL(18, 12) NOT NULL,
  current_price DECIMAL(18, 12) NOT NULL,
  volume_24h DECIMAL(18, 6) NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table pour stocker les transactions
CREATE TABLE quantum_transactions (
  id VARCHAR(50) PRIMARY KEY,
  launch_id VARCHAR(50) NOT NULL REFERENCES quantum_launches(id),
  type VARCHAR(20) NOT NULL,
  hash VARCHAR(100) NOT NULL,
  date TIMESTAMP WITH TIME ZONE NOT NULL,
  amount DECIMAL(18, 6) NOT NULL,
  status VARCHAR(20) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table pour stocker les contributions
CREATE TABLE quantum_contributions (
  id VARCHAR(50) PRIMARY KEY,
  launch_id VARCHAR(50) NOT NULL REFERENCES quantum_launches(id),
  phase_id VARCHAR(50) NOT NULL REFERENCES quantum_launch_phases(id),
  contributor_address VARCHAR(44) NOT NULL,
  amount DECIMAL(18, 6) NOT NULL,
  transaction_id VARCHAR(100) NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index pour améliorer les performances des requêtes
CREATE INDEX idx_quantum_launches_owner ON quantum_launches(owner_address);
CREATE INDEX idx_quantum_launches_status ON quantum_launches(status);
CREATE INDEX idx_quantum_launch_phases_launch_id ON quantum_launch_phases(launch_id);
CREATE INDEX idx_quantum_launch_phases_status ON quantum_launch_phases(status);
CREATE INDEX idx_quantum_transactions_launch_id ON quantum_transactions(launch_id);
CREATE INDEX idx_quantum_contributions_contributor ON quantum_contributions(contributor_address);
CREATE INDEX idx_quantum_contributions_launch_phase ON quantum_contributions(launch_id, phase_id);

-- Fonction pour mettre à jour le timestamp updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_quantum_launch_configs_updated_at
BEFORE UPDATE ON quantum_launch_configs
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_quantum_launches_updated_at
BEFORE UPDATE ON quantum_launches
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_quantum_launch_phases_updated_at
BEFORE UPDATE ON quantum_launch_phases
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_quantum_dex_listings_updated_at
BEFORE UPDATE ON quantum_dex_listings
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
